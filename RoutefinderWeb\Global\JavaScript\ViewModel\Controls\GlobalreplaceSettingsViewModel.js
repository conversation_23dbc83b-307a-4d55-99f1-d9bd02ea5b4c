
(function()
{
	createNamespace("TF.Control").GlobalReplaceSettingsViewModel = GlobalReplaceSettingsViewModel;

	function GlobalReplaceSettingsViewModel(selectedCount, gridType)
	{
		var self = this;
		self.gridType = gridType;
		self.obSpecifyRecords = ko.observableArray([
			{ value: 'allInFilter', text: 'All records in filter' },
			{ value: 'selected', disable: selectedCount == 0, text: 'Current ' + selectedCount + ' selected ' + TF.getSingularOrPluralTitle("record", selectedCount) }
		]);

		self.ShowInSourceFieldIgnoreList = {
			"vehicle": ["vehiclename"]
		}
		self.ShowInSourceFields = [];

		self.obReplaceType = ko.observable("Standard");
		self.obStandardInputGroupDisable = ko.observable(false);
		self.obExtendedsEnable = ko.observable(false);
		self.obDisplayUpdateRequirement = ko.observable(false);
		self.obDisplayAddorRemoveDisabilityCodes = ko.observable(false);
		self.obDisplayAddorRemoveEthnicCodes = ko.observable(false);
		self.allDisabilityCodes = [];
		self.allEthnicCodes = [];
		self.allNoTransportationTypes = ko.observableArray([]);
		self.obselectedDC = ko.observable([]);
		self.obselectedEC = ko.observable([]);
		self.obSelectedDisabilityCodes = ko.observable('');
		self.obSelectedEthnicCodes = ko.observable('');
		self.obSelectedNoTransportationType = ko.observable();
		let supportedTagsGridType = tf.authManager.hasTags() ? tf.helper.tagHelper.supportedGridType : [];
		self.replaceTypeDisable = ![...new Set([...["student", "street", "georegion", "staff", "vehicle"], ...supportedTagsGridType])].includes(gridType);
		self.standardTypeDisable = ['mapincident'].includes(gridType);
		self.obReplaceType.subscribe(function()
		{
			self.obStandardInputGroupDisable(self.obReplaceType() == "Extended");
			self.obExtendedsEnable(self.obReplaceType() != "Standard" && !self.replaceTypeDisable);
		});
		if (self.standardTypeDisable)
		{
			self.obReplaceType("Extended");
		}
		self.extendeds = {
			georegion: [
				{ display: "Add Event Rule", key: "addGeoRegionEventRule" },
				{ display: "Remove All Event Rules", key: "removeAllEventRules" }
			],
			staff: [
				{ display: "Deactivate Current Cards", key: "deactivateCurrentCards" },
			],
			street: [
				{ display: "Edit Line Style", key: "editLineStyle" }
			],
			student: [
				{ display: "Add Additional Requirement", key: "addAdditionalRequirement" },
				{ display: "Add Disability Code(s)", key: "addDisabilityCodes", disable: !tf.authManager.isAuthorizedFor("studentDisability", "add") },
				{ display: "Add Ethnic Code(s)", key: "addEthnicCodes" },
				{ display: "Deactivate Current Cards", key: "deactivateCurrentCards" },
				{ display: "Remove All Additional Requirements", key: "removeAllAdditionalRequirements" },
				{ display: "Remove All Disability Codes", key: "removeAllDisabilityCodes", disable: !tf.authManager.isAuthorizedFor("studentDisability", "delete") },
				{ display: "Remove All Ethnic Codes", key: "removeAllEthnicCodes" },
				{ display: "Remove All Exceptions", key: "removeAllExceptions" },
				{ display: "Remove All Schedule", key: "removeAllSchedule" },
				{ display: "Remove All Default Requirements", key: "removeAllDefaultRequirements" },
				{ display: "Remove Disability Code(s)", key: "removeDisabilityCodes", disable: !tf.authManager.isAuthorizedFor("studentDisability", "delete") },
				{ display: "Remove Ethnic Code(s)", key: "removeEthnicCodes" },
				{ display: "Remove To School Default Requirements", key: "removeToSchoolDefaultRequirements" },
				{ display: "Remove From School Default Requirements", key: "removeFromSchoolDefaultRequirements" },
				{ display: "Reset All Default Requirements", key: "resetAllDefaultRequirements" },
				{ display: "Reset To School Default Requirements", key: "resetToSchoolDefaultRequirements" },
				{ display: "Reset From School Default Requirements", key: "resetFromSchoolDefaultRequirements" },
				{ display: "Update All Default Requirements to None", key: "updateAllDefaultRequirementsToNone" },
				{ display: "Update To School Default Requirements to None", key: "updateToSchoolDefaultRequirementsToNone" },
				{ display: "Update From School Default Requirements to None", key: "updateFromSchoolDefaultRequirementsToNone" }
			],
			vehicle: [
				{ display: "Deactivate Current Cards", key: "deactivateCurrentCards" },
			]
		};
		supportedTagsGridType.forEach(type =>
		{
			self.extendeds[type] = _.sortBy((self.extendeds[type] || [])
				.concat([
					{ display: "Associate Tags", key: "associateTags" },
					{ display: "Disassociate Tags", key: "disassociateTags" }
				]), 'display');
		});
		self.obSelectedExtended = ko.observable("");

		self.obSelectedExtended.subscribe(function()
		{
			self.obDisplayUpdateRequirement(self.isUpdateDefaultRequirementsToNone(self.obSelectedExtended()));
			self.obDisplayAddorRemoveDisabilityCodes(self.isAddRemoveDisabilityCodes(self.obSelectedExtended()));
			self.obDisplayAddorRemoveEthnicCodes(self.isAddRemoveEthnicCodes(self.obSelectedExtended()));
		});

		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint('disabilitycode')))
			.then(function(result)
			{
				let disabilityCodes = result.Items;
				Array.sortBy(disabilityCodes, "Code");
				self.allDisabilityCodes = disabilityCodes.map(function(item)
				{
					return { id: item.Id, text: item.Code, value: item.Code }
				});
				;
			});
		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint('ethniccode')))
			.then(function(result)
			{
				let ethnicCodes = result.Items;
				Array.sortBy(ethnicCodes, "Code");
				self.allEthnicCodes = ethnicCodes.map(function(item)
				{
					return { id: item.Id, text: item.Code, value: item.Code }
				});
				;
			});
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "notransportationtypes"))
			.then(function(result)
			{
				var noTransportationTypes = result.Items;
				Array.sortBy(noTransportationTypes, "Type");
				self.allNoTransportationTypes(noTransportationTypes);
			});

		var selectedSpecifyRecord = selectedCount ? self.obSpecifyRecords()[1] : self.obSpecifyRecords()[0];
		self.obSelectedSpecifyRecords = ko.observable(selectedSpecifyRecord.value);
		self.obNewValue = ko.observable("");
		self.obSelectType = ko.observable("Disabled");
		self.lastSelectedField = "";
		self.gridOption = {};
		self.obSelectDataList = ko.observableArray();
		self.inputEnable = ko.observable(false);
		self.obTitle = ko.observable("");
		self.obMaxLength = ko.observable("50");
		self.allFields = ko.observableArray([]);
		self.targetFieldName = ko.observable();
		self.targetField = ko.computed(() =>
		{
			return self.allFields().find(i => i.field == self.targetFieldName());
		});
		self.generateInputType = ko.computed(() =>
		{
			return self.obSelectType() === "Number" ? "Decimal" : self.obSelectType();
		});
		self.itemTypes = ko.observable();
		self.getFields().then(function(items)
		{
			var data = items.filter((item => self.filterIgnoreList(item, self.ShowInSourceFieldIgnoreList)));
			self.ShowInSourceFields = items.filter((item => !self.filterIgnoreList(item, self.ShowInSourceFieldIgnoreList)));
			self.allFields(data);
			self.itemTypes(self.createItemTypes(data));
		});
		self.obIsValueRequired = ko.observable(false);
		self.$container = null;
		self.copyFrom = ko.observable(false);
		self.sourceFieldName = ko.observable();
		var supportZipCity = ['student', 'altsite', 'georegion', 'school'];
		self.sourceFields = ko.computed(() =>
		{
			let currentTarge = self.targetField();
			if (!currentTarge)
			{
				return [];
			}

			if (supportZipCity.indexOf(self.gridType) > -1)
			{
				if (currentTarge.field == 'MailZip')
				{
					return self.allFields().filter(i => i.field == "GeoZip");
				}

				if (currentTarge.field == 'MailCity')
				{
					return self.allFields().filter(i => i.field == "GeoCity");
				}
			}

			let types = self.itemTypes();
			if (!types)
			{
				return [];
			}

			let fields = (currentTarge.type == "String" ? Array.sortBy(self.allFields().concat(self.ShowInSourceFields), "title") : types[currentTarge.type]) || [];
			if (supportZipCity.indexOf(self.gridType) > -1)
			{
				if (currentTarge.field == 'GeoZip')
				{
					return fields.filter(i => i.field == "MailZip");
				}

				if (currentTarge.field == 'GeoCity')
				{
					return fields.filter(i => i.field == "MailCity");
				}
			}

			return fields.filter(i => self.checkSupportCopy(i));
		});

		self.supportCopyFrom = ko.computed(() => !!self.sourceFields().length);
		self.targetField.subscribe(function()
		{
			var selectedField = self.targetField();
			if (selectedField && selectedField.editType && selectedField.editType.allowInput !== undefined)
			{
				self.inputEnable(typeof selectedField.editType.allowInput == "function" ? selectedField.editType.allowInput() : selectedField.editType.allowInput);
			}

			if (selectedField && selectedField.field != self.lastSelectedField)
			{
				self.lastSelectedField = selectedField.field;
				self.destroyValidator();
				self.updateNewValue().then(function(result)
				{
					const validators = selectedField.editType.validators || {};

					if (selectedField.editType.maxLength)
					{
						self.obMaxLength(selectedField.editType.maxLength.toString());
					}

					if (result.item)
					{
						let data = result.item.slice();
						data.sort((a, b) =>
						{
							let aText = a[result.textField || "text"]?.toLowerCase(),
								bText = b[result.textField || "text"]?.toLowerCase();
							if (aText < bText) return -1;
							if (aText > bText) return 1;
							return 0;
						});
						self.obSelectDataList(data);
					}
					if (result.title)
					{
						self.obTitle(result.title);
					}
					if (result.type)
					{
						self.obSelectType(result.type);

						if (result.type === "Number")
						{
							if (result.format != "Money")
							{
								self.updateNumberformtValidation(result.format);
							}
						}

						if (result.type === "DateTime")
						{
							self.obNewValue(moment().format("MM/DD/YYYY hh:mm A"));
						}
						if (result.type === "Date")
						{
							self.obNewValue(moment().format("MM/DD/YYYY"));
						}
					}
					self.updateValidator(result.type, result.format, validators);
				});
			}

			if (!self.supportCopyFrom())
			{
				self.copyFrom(false);
				self.sourceFieldName(null);
			}
			else
			{
				let sourceField = self.sourceFieldName();
				if (sourceField && !self.sourceFields().some(i => i.field == sourceField))
				{
					self.sourceFieldName(null);
				}
			}
		});

		self.sourceField = ko.computed(() => this.sourceFields().find(i => i.field === this.sourceFieldName()));
	}

	GlobalReplaceSettingsViewModel.prototype.checkSupportCopy = function(from, to)
	{
		to = to || this.targetField();
		if (!to || to.field == from.field)
		{
			return false;
		}

		if (to.type != from.type && to.type != "String")
		{
			return false;
		}

		if (to.type == 'Number' && to.editType.format == 'Integer' && from.type == 'Number' && from.editType.format != 'Integer')
		{
			return false;
		}

		if (to.editType && (to.editType.format == "DropDown" || to.editType.format == "ListMover" || to.editType.format == "TextGrid"))
		{
			if (to.foreignTable && to.foreignTable == from.foreignTable && to.foreignTableKey == from.foreignTableKey)
			{
				return true;
			}

			return false;
		}

		if (from.editType && (from.editType.format == "DropDown" || from.editType.format == "ListMover" || from.editType.format == "TextGrid") && from.editType.entityKey)
		{
			return false;
		}

		if (to.format == "Phone" && from.format != to.format)
		{
			return false;
		}

		return true;
	};

	GlobalReplaceSettingsViewModel.prototype.createItemTypes = function(items)
	{
		let types = {};
		items.forEach(i =>
		{
			var fieldType = i.type;
			let type = types[fieldType] || [];
			types[fieldType] = type;
			type.push(i);
		});

		return types;
	};

	GlobalReplaceSettingsViewModel.prototype.customInputInit = function(element)
	{
		var self = this;
		self.targetField.subscribe(function(field)
		{
			var inputControl = ko.utils.domData.get(this, "input");
			if (inputControl)
			{
				var maxLength = field && field.editType && field.editType.maxLength ? field.editType.maxLength : "50";
				inputControl.$element.attr("maxLength", maxLength);
			}
		}.bind(element));
	}

	GlobalReplaceSettingsViewModel.prototype.destroyValidator = function()
	{
		var self = this;
		if (self.$container && self.$container.data("bootstrapValidator"))
		{
			self.$container.data("bootstrapValidator").destroy()
		}
		$('.mass-update-value').find(".enableHotkey").off("keyup.numbermass");
	};

	GlobalReplaceSettingsViewModel.prototype.updateValidator = function(type, format, validators)
	{
		var self = this, validatorFields = null;
		if ("String" == type || "Phone" == type || "Email" == type || "Date" == type || "Time" == type || "Number" == type || "DateTime" == type || "DataList" == type || "Integer" == type || "ListMover" == type)
		{
			validatorFields = self.generateValidatorFields(type, format, validators);
			if (validatorFields)
			{
				if (!self.$container)
				{
					self.$container = $('.mass-update-value');
				}

				self.$container.bootstrapValidator({
					excluded: [":disabled"],
					live: 'enabled',
					message: 'This value is not valid',
					fields: validatorFields
				});
			}
		}
	};

	GlobalReplaceSettingsViewModel.prototype.generateValidatorFields = function(type, format, defaultValidators)
	{
		const self = this;
		const valueValidators = defaultValidators || {};
		const validator = {
			sourceField: {
				trigger: "blur change",
				validators: { notEmpty: { message: 'required' } }
			},
		};

		if (self.obIsValueRequired())
		{
			valueValidators["notEmpty"] = { message: 'required' }
		}

		if ("String" === type && self.obIsValueRequired())
		{
			valueValidators["stringLength"] = {
				max: self.obMaxLength() ? self.obMaxLength() : 200,
				message: 'The input should be less than' + self.obMaxLength() ? self.obMaxLength() : 200 + 'characters',
				specialInputSupport: true
			};
		}
		else if ("Phone" === type)
		{
			valueValidators["phoneinplus"] = {
				message: 'The input is not a valid phone number',
				country: tfRegion,
				specialInputSupport: true
			};
		}
		else if ("Date" === type)
		{
			valueValidators["date"] = {
				format: 'MM/DD/YYYY',
				message: 'The input is not a valid date(MM/DD/YYYY)',
				specialInputSupport: true
			};
		}
		else if ("DateTime" === type)
		{
			valueValidators["date"] = {
				format: 'MM/DD/YYYY hh:mm A',
				message: 'The input is not a valid datetime(MM/DD/YYYY hh:mm A)',
				specialInputSupport: true
			};
		}
		else if ("Time" === type)
		{
			valueValidators["callback"] = {
				message: 'The input is not a time(hh:mm A)',
				callback: function(value, validator)
				{
					var m = new moment(value, 'h:mm A', true);
					return value == "" || m.isValid();
				}
			};
		}
		else if ("Number" === type)
		{
			if (format == "Money")
			{
				return null;
			}

			valueValidators["numeric"] = {
				message: 'The input is not a valid number',
				specialInputSupport: true,
			}
		}
		else if ("Email" === type)
		{
			valueValidators["emailAddress"] = {
				message: 'The input is not a valid email address',
				specialInputSupport: true
			}
		}

		if ("DataList" === type && self.obIsValueRequired())
		{
			valueValidators["callback"] = {
				message: 'The input is not a valid value',
				callback: v => v === "" || self.obSelectDataList().some(i => i.text == v)
			}
		}


		// initialize validators for value field
		validator[type] = {
			trigger: "blur change",
			validators: valueValidators,
		};

		return validator;
	};

	GlobalReplaceSettingsViewModel.prototype.updateNewValue = function()
	{
		var self = this, selectedField = self.targetField(),
			editType = selectedField.editType,
			type = editType.format;

		if (tf.authManager.hasNationalMaps() && type === "DropDown" && selectedField.field === "GeoZip")
		{
			type = "String";
			self.obMaxLength(10);
		}

		self.obNewValue("");
		self.obSelectDataList([]);
		self.obSelectType("String");
		self.obIsValueRequired(false);
		if (editType.validators && editType.validators.notEmpty)
		{
			self.obIsValueRequired(true);
		}

		if (type === "BooleanDropDown")
		{
			type = "DataList";
			if (!selectedField.UDFType)
			{
				self.obIsValueRequired(true);
			}
			var dataList = self.getBooleanDataList(selectedField);
			return Promise.resolve({ type: type, item: dataList });
		}

		if (type === "DropDown")
		{
			type = "DataList";
			return editType.getSource().then(function(item)
			{
				if (editType.allowNullValue)
				{
					let isGender = (self.gridType === 'student' || self.gridType === 'staff') && editType.entityKey === 'GenderId';
					if (!isGender)
					{
						item = item.filter(x => !!x.text && !!x.value);
					}
					//For Trip.DriverId null and '' has different meanings.
					if (self.gridType === 'trip' && editType.entityKey === 'DriverId' || isGender)
					{
						item.unshift({ text: "(none)", value: null })
					}
					else
					{
						item.unshift({ text: "(none)", value: "" })
					}
				}
				return Promise.resolve({ type: type, item: item });
			})
		}

		if (type === "Note")
		{
			type = "String";
			return Promise.resolve({ type: type });
		}

		if (type === "Phone")
		{
			self.obMaxLength("18");
			return Promise.resolve({ type: type });
		}

		if (type === "Fax")
		{
			type = "Phone"
			self.obMaxLength("18");
			return Promise.resolve({ type: type });
		}

		if (type === "ListMover")
		{
			return editType.getSource().then(function(item)
			{
				return Promise.resolve({ type: type, item: item, title: selectedField.title });
			})
		}

		if (type === "TextGrid")
		{
			return Promise.all([
				editType.getSource(),
				typeof editType.columns === "function" ? editType.columns() : editType.columns
			]).then(function([item, columns])
			{
				self.gridOption = Object.assign({}, editType, {
					availableSource: item,
					columns
				});

				return { ...editType, type: "Grid", item: item, title: selectedField.title, columns };
			});
		}

		if (type === "LineStyle")
		{
			return Promise.resolve({ type: type });
		}

		return Promise.resolve({ type: type, format: selectedField.format });
	};

	GlobalReplaceSettingsViewModel.prototype.updateNumberformtValidation = function(format)
	{
		var self = this, $container = $('.mass-update-value');
		$container.find(".enableHotkey").attr("numberFormat", format);
		$container.find(".enableHotkey").off("keyup.numbermass").on("keyup.numbermass", (e) =>
		{
			const $massValueInput = $(e.currentTarget);
			var value = $massValueInput.val(),
				floatValue = parseFloat(value),
				decimalPlaces = null,
				index = value.indexOf('.');
			if (format)
			{
				const formatArray = format.split('.');
				if (formatArray.length > 1)
				{
					decimalPlaces = formatArray[1].length;
				}
			}

			if (isNaN(Number(value)))
			{
				$massValueInput.val("");
				return;
			}

			if (decimalPlaces == null || index === -1) return;

			// cut the float with decimal places.
			if (index === 0) return;
			var count = value.length - index - 1;
			if (count > decimalPlaces)
			{
				const kappa = Math.pow(10, decimalPlaces);
				var floorValue = Math.floor(floatValue * kappa) / kappa
				$massValueInput.val(floorValue.toFixed(decimalPlaces));
				self.obNewValue(floorValue.toFixed(decimalPlaces));
			}
			if (value.length - index - 1 > decimalPlaces)
			{
				$massValueInput.val(floorValue.toFixed(decimalPlaces));
				self.obNewValue(floorValue.toFixed(decimalPlaces));
			}
		});
	};

	GlobalReplaceSettingsViewModel.prototype.getBooleanDataList = function(selectedField)
	{
		var booleanDataList = [];
		booleanDataList.push({ text: " ", value: null });
		if (selectedField.positiveLabel)
		{
			booleanDataList.push({ text: selectedField.positiveLabel, value: true });
		}

		if (selectedField.negativeLabel)
		{
			booleanDataList.push({ text: selectedField.negativeLabel, value: false });
		}

		return booleanDataList;
	};

	GlobalReplaceSettingsViewModel.prototype.checkSpeedTypeChangeForTrip = function()
	{
		const fieldName = this.targetField().field;
		if (this.obReplaceType() === "Standard" && this.gridType === "trip" && (fieldName === "SpeedTypeName" || fieldName === "SpeedType"))
		{
			return tf.promiseBootbox.yesNo("You have chosen to change travel speed for time calculation on the selected trip(s). Do you want to overwrite manually adjusted travel speeds between trip stops?", "Confirmation Message");
		}

		return false;
	}

	GlobalReplaceSettingsViewModel.prototype.apply = async function()
	{
		var field = this.targetField();
		var overwriteCustomChange = await this.checkSpeedTypeChangeForTrip();
		if (overwriteCustomChange === null)
		{
			return false;
		}
		if (this.obReplaceType() == "Extended" && !this.obSelectedExtended())
		{
			tf.promiseBootbox.alert("Extended is a required field, please select a valid value.");
			return false;
		}

		if (this.obReplaceType() == "Extended" && this.isUpdateDefaultRequirementsToNone(this.obSelectedExtended()) && !this.obSelectedNoTransportationType())
		{
			tf.promiseBootbox.alert("No Transportation Type is a required field, please select a valid value.");
			return false;
		}
		if (this.obReplaceType() == "Extended" && this.isAddRemoveDisabilityCodes(this.obSelectedExtended()) && !this.obSelectedDisabilityCodes())
		{
			tf.promiseBootbox.alert("Disability Code is required.");
			return false;
		}
		if (this.obReplaceType() == "Extended" && this.isAddRemoveEthnicCodes(this.obSelectedExtended()) && !this.obSelectedEthnicCodes())
		{
			tf.promiseBootbox.alert("Ethnic Code is required.");
			return false;
		}
		if (this.obReplaceType() == "Standard" && !field)
		{
			tf.promiseBootbox.alert("You must select a field before apply.");
			return false;
		}

		let validator = this.$container && this.$container.data("bootstrapValidator");
		if (this.obReplaceType() == "Standard" && validator)
		{
			validator.validate();
			if (!validator.isValid())
			{
				if (!this.copyFrom() && this.obIsValueRequired())
				{
					tf.promiseBootbox.alert("This is a required field, You must select or input a valid value.");
					return false;
				}

				if (this.copyFrom() && !this.sourceFieldName())
				{
					tf.promiseBootbox.alert("You must select a New Value field.");
					return false;
				}

				tf.promiseBootbox.alert("You must input a valid value.");
				return false;
			}
		}

		var relationshipKey,
			fieldName = "",
			newValue = this.getNewValue(),
			targetUdf,
			sourceUdf,
			sourceField,
			needConvertBooleanDisplayName,
			sourceTrueDisplayName,
			sourceFalseDisplayName;
		if (field)
		{
			relationshipKey = field.editType.relationshipKey;
			fieldName = field.editType.entityKey ? field.editType.entityKey : field.field;

			if (field.UDFId)
			{
				if (this.copyFrom())
				{
					fieldName = null;
					targetUdf = field.UDFId;
				}
				else
				{
					relationshipKey = "UDF";
					fieldName = "UserDefinedFields";
				}
			}
		}

		if (this.obSelectedExtended() === "editLineStyle" && this.obExtendedsEnable())
		{
			const appliedStyle = await tf.modalManager.showModal(new TF.RoutingMap.LineStyleSelectorModalViewModel());
			if (!appliedStyle)
			{
				return false;
			}

			newValue = JSON.stringify([{
				Pen: {
					Width: appliedStyle.lineWidth?.toString(),
					Pattern: appliedStyle.pattern?.toString(),
					Color: TF.Color.toLongColorFromHTMLColor(appliedStyle.color).toString(),
					Opacity: appliedStyle.opacity?.toString()
				}
			}]);
			fieldName = "Style";
		}

		if (this.copyFrom())
		{
			let src = this.sourceField();
			if (src.UDFId)
			{
				sourceUdf = src.UDFId;
			} else
			{
				sourceField = src.editType.entityKey ? src.editType.entityKey : src.field;
			}

			if (src.type == "Boolean" && field && field.editType && field.type == "String")
			{
				let editFormat = field.editType.format;
				if (editFormat == "String" || editFormat == "Email" || editFormat == "Note")
				{
					needConvertBooleanDisplayName = true;
					sourceTrueDisplayName = src.positiveLabel || "True";
					sourceFalseDisplayName = src.negativeLabel || "False";
				}
			}
		}

		return {
			targetField: fieldName,
			targetUdfId: targetUdf,
			sourceField: sourceField,
			sourceUdfId: sourceUdf,
			newValue: newValue,
			editType: field && field.editType,
			format: field ? field.format : null,
			specifyRecords: this.obSelectedSpecifyRecords(),
			replaceType: this.obReplaceType(),
			extended: this.obSelectedExtended(),
			type: field ? field.type : null,
			relationshipKey: relationshipKey,
			noTransportationId: this.obSelectedNoTransportationType(),
			DisabilityCodeIds: this.obselectedDC().map(item => item.id),
			EthnicCodeIds: this.obselectedEC().map(item => item.id),
			needConvertBooleanDisplayName: needConvertBooleanDisplayName,
			sourceTrueDisplayName: sourceTrueDisplayName,
			sourceFalseDisplayName: sourceFalseDisplayName,
			overwriteCustomChange: overwriteCustomChange,
		};
	};

	GlobalReplaceSettingsViewModel.prototype.getNewValue = function()
	{
		if (this.copyFrom())
		{
			return null;
		}

		let self = this,
			newValue = self.obNewValue(),
			field = self.targetField(),
			selectPickListOptionIDs;

		if (field && field.UDFId && field.UDFType == "List")
		{
			selectPickListOptionIDs = [];
			newValue.split(",").forEach(function(value)
			{
				if (value != null)
				{
					value = value.trim();
				}

				var valueItem = self.obSelectDataList().find(function(item)
				{
					return item.text == value;
				});

				if (valueItem && valueItem.value)
				{
					selectPickListOptionIDs.push(valueItem.value);
				}
			});
		}
		else
		{
			const selectedType = self.obSelectType();
			switch (selectedType)
			{
				case "DataList":
				case "Grid":
					var found = false;
					const textField = field.editType.textField instanceof Function ? field.editType.textField() : field.editType.textField || "text";
					const valueField = field.editType.valueField instanceof Function ? field.editType.valueField() : field.editType.valueField || "value";
					self.obSelectDataList().every(function(data)
					{
						if (data[textField] == self.obNewValue())
						{
							newValue = data[valueField];
							found = true;
							return false;
						}

						return true;
					});

					if (!self.inputEnable() && !found)
					{
						newValue = null;
					}
					break;
				case "Time":
					newValue = ["", null].includes(self.obNewValue()) ? "" : moment(self.obNewValue()).format('HH:mm');
					break;
				case "ListMover":
					newValue = self.obSelectDataList().map(function(item)
					{
						var result = null;
						self.obNewValue().split(",").forEach(function(value)
						{
							if (item.text == value.trim()) result = item.value;
						})
						return result;
						// allow 0 specifically for generic staff type whose id is 0.
					}).filter(d => Boolean(d) || d === 0).toString();
					break;
				default:
					newValue = self.obNewValue();
					if (field && field.UnitOfMeasureSupported && tf.measurementUnitConverter.isNeedConversion(field.UnitInDatabase))
					{
						newValue = tf.measurementUnitConverter.convert({
							originalUnit: tf.measurementUnitConverter.getCurrentUnitOfMeasure(),
							targetUnit: field.UnitInDatabase || tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
							isReverse: !!field.UnitOfMeasureReverse,
							precision: 5,
							value: newValue,
							unitType: field.UnitTypeOfMeasureSupported
						});
					}
					break;
			}
		}

		if (field && field.UDFId)
		{
			newValue = [{
				TypeId: field.UDFTypeId,
				Id: field.UDFId,
				RecordValue: newValue === "" ? null : newValue,
				DataTypeId: tf.dataTypeHelper.getId(self.gridType),
				SelectPickListOptionIDs: selectPickListOptionIDs
			}];
		}

		return newValue;
	};

	GlobalReplaceSettingsViewModel.prototype.filterIgnoreList = function(item, ignoreList)
	{
		return item.editType && tf.helpers.detailViewHelper.editableThroughEditType(item.editType)
			&& (ignoreList[this.gridType] == null || (ignoreList[this.gridType] && ignoreList[this.gridType].indexOf(item.field.toLowerCase()) === -1));
	}

	GlobalReplaceSettingsViewModel.prototype.getFields = function()
	{
		let self = this;

		return tf.dataPointHelper.updateDataPointUDF(self.gridType).then(function()
		{
			let categories, key,
				dataPointObj = $.extend({}, dataPointsJSON[self.gridType] || {}),
				fieldSources = [],
				ignoreList = {
					"school": ["schoolcode"],
					"tripstop": ["sequence", "tripname"],
					"district": ["idstring"],
					"trip": ["name", "schools", "triptypename"],
					"fieldtrip": ["DeptActivity", "destination", "billingclassificationname", "template"].concat(tf.authManager.hasTripfinderRouting() ? "" : "travelscenarioname"),
					"route": ["name", "names"]
				},
				requiredFields = tf.helpers.detailViewHelper.requiredFields[self.gridType].map(function(item) { return item.name }),
				filter = function(item)
				{
					if (self.filterIgnoreList(item, ignoreList))
					{
						var fieldName = item.editType.entityKey || item.field;
						if (requiredFields.indexOf(fieldName) > -1)
						{
							item.editType.validators = item.editType.validators || {};
							item.editType.validators["notEmpty"] = { message: 'required' };
						}
						else if (item.editType.validators)
						{
							delete item.editType.validators["notEmpty"];
						}

						return true;
					}

					return false;
				};

			categories = Object.keys(dataPointObj);
			for (var i = 0, len = categories.length; i < len; i++)
			{
				key = categories[i];
				if (key == "User Defined") continue;
				fieldSources = fieldSources.concat(dataPointObj[key].filter(filter));
			}

			fieldSources.forEach(i => i.type = i.type == "Note" ? "String" : i.type);

			return tf.UDFDefinition.udfHelper.get(self.gridType).then(function(items)
			{
				var hasDBID = tf.dataTypeHelper.getDataTypeConfig(self.gridType).hasDBID !== false,
					definition = tf.UDFDefinition.get(self.gridType) || {},
					allFields = definition.userDefinedFields || [],
					validFields = allFields.filter(v => v.UDFType != "image").filter(function(f) { return !hasDBID || f.UDFDataSources.some(function(d) { return d.DBID === tf.datasourceManager.databaseId }) }).map(function(f) { return f.OriginalName });
				items = (items || []).filter(function(i) { return validFields.indexOf(i.field) > -1 });
				fieldSources = fieldSources.concat(items.filter(filter));
				fieldSources = Array.sortBy(fieldSources, "title");
				return fieldSources;
			});
		})
	};

	GlobalReplaceSettingsViewModel.prototype.isUpdateDefaultRequirementsToNone = function(extended)
	{
		return /^update\w+DefaultRequirementsToNone$/i.test(extended);
	}

	GlobalReplaceSettingsViewModel.prototype.isAddRemoveDisabilityCodes = function(extended)
	{
		return /^(remove|add)DisabilityCodes$/i.test(extended);
	}
	GlobalReplaceSettingsViewModel.prototype.isAddRemoveEthnicCodes = function(extended)
	{
		return /^(remove|add)EthnicCodes$/i.test(extended);
	}
})();
