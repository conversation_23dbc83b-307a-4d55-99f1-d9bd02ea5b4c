﻿(function()
{
	createNamespace('TF.Control').ResetLoadTimesViewModel = ResetLoadTimesViewModel;

	function ResetLoadTimesViewModel(affected)
	{
		this.affected = affected;
		this.obDescription = ko.observable("The Load Time has been adjusted for " + affected.affects.length + " grades (" +
			affected.affects.map(function(item)
			{
				return item.Category;
			}).join(",") + ").  " +
			affected.ManualStudentCount + " of the " +
			affected.TotalStudentCount + " students associated with these grades have a manually adjusted Load Time.  " +
			affected.ManualStopsCount + " of the " +
			affected.TotalStopsCount + " trip stops assigned to these students have a manually adjusted Total Stop Time.  You may reset the Load Time for these students and the Total Stop Time for the trip stops associated with these students that have been manually adjusted Total Stop Time.");
		this.obLoadTimes = ko.observable(null);
		this.obStopTimes = ko.observable(null);

		//this.obLoadTimes(false);// load time check box by default is unchecked
		//this.obStopTimes(false);// stop time check box by default is unchecked
	}

	ResetLoadTimesViewModel.prototype.apply = function()
	{
		var obj = {
			LoadTimes: this.obLoadTimes(),
			StopTimes: this.obStopTimes()
		};

		var message = "";
		if (obj.LoadTimes && (!obj.StopTimes))
		{//to reset load time
			var strStudent = this.affected.ManualStudentCount > 0 ? "these students?" : "this student?";
			message = "Resetting the manually adjusted Load Time for a student will reset the automatic calculation of the Load Time for that student.  Are you sure you want to reset the Load Times for " + strStudent;
		}
		else if ((!obj.LoadTimes) && obj.StopTimes)
		{//to reset stop time
			message = "Resetting the manually adjusted Total Stop Time for a trip stop will reset the automatic calculation of this value.  Are you sure you want to reset these Total Stop Times?";
		}
		else if (obj.LoadTimes && obj.StopTimes)
		{//to reset load time and stop time
			var strStudent = this.affected.ManualStudentCount > 0 ? "these students" : "this student";
			message = "Resetting the manually adjusted Load Time for a student and the Total Stop Time for a trip stop will reset the automatic calculation of these values.  Are you sure you want to reset the Load Time for " + strStudent + " and the associated trip stop Total Stop Times?";
		}
		else if ((!obj.LoadTimes) && (!obj.StopTimes))
		{
			message = "Student Load Times and the associated trip stop Total Stop Times that have not been manually adjusted will be recalculated.  Are you sure you do not want to reset manually adjusted values?";
		}

		return tf.promiseBootbox.confirm({ message: message, title: "Confirmation Message" })
		.then(function(result)
		{
			if (result)
			{//....
				return obj;
			}
			return result;
		});
	};

	ResetLoadTimesViewModel.prototype.dispose = function()
	{

	};

})();

