(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.MailingStateDeletion = MailingStateDeletion;

	function MailingStateDeletion()
	{
		this.type = 'mailingstate';
		this.deleteType = 'Mailing State/Province';
		namespace.BaseDeletion.call(this, true);
	}

	MailingStateDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	MailingStateDeletion.prototype.constructor = MailingStateDeletion;

	MailingStateDeletion.prototype.getAssociatedData = function(ids)
	{
		var self = this, associatedDatas = [];
		return tf.promiseAjax.get(pathCombine(self.apiPrefix, tf.dataTypeHelper.getEndpoint(self.type), `${ids[0]}/status`))
			.then(function(response)
			{
				if (response && response.InUse)
				{
					var rejectMessage = `<pre class='titleNotify'>This ${self.deleteType.toLowerCase()} is associated with one or more records.  It cannot be deleted. </pre>`;
					var rejectTitle = `${self.deleteType} Cannot be Deleted`;
					return tf.promiseBootbox.alert(rejectMessage, rejectTitle)
						.then(function()
						{
							self.deleteIds = [];
							return Promise.resolve(false);
						}.bind(self));
				}
				return associatedDatas;
			});
	};

	MailingStateDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();