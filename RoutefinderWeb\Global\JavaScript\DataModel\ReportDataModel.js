﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.ReportDataModel = function(reportEntity)
	{
		namespace.BaseDataModel.call(this, reportEntity);
	}

	namespace.ReportDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ReportDataModel.prototype.constructor = namespace.ReportDataModel;

	namespace.ReportDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "DataTypeId", default: null },
		{ from: "ReportDataSchemaID", default: null },
		{ from: "Type", default: null },
		{ from: "Path", default: null },
		{ from: "CreatedOn", default: "1970-01-01T00:00:00" },
		{ from: "CreatedBy", default: 0 },
		{ from: "IsSystem", default: 0 }
	];
})();
