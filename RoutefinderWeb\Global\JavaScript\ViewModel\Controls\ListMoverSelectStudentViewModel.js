(function()
{
	createNamespace('TF.Control').ListMoverSelectStudentViewModel = ListMoverSelectStudentViewModel;
	function ListMoverSelectStudentViewModel(selectedData, options, needLoadTimeColumn, needFilterCheckBox)
	{
		options.getUrl = function(gridType)
		{
			return pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(gridType));
		};
		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
		this._needFilterCheckBox = needFilterCheckBox;
		if (needLoadTimeColumn)
		{
			this.columnSources.student.pop();
			this.columnSources.student.push({
				FieldName: 'LoadTime',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('LoadTime'),
				Width: '90px',
				type: 'integer',
				isSortItem: true
			});
		}

		if (this.options.rightGridFilter)
		{
			this._getRightColumns = this.getRightColumnsSupportFilter;
		}
		if (this.options.columnSources)
		{
			this.columnSources = { student: this.options.columnSources }
		}
	}

	ListMoverSelectStudentViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectStudentViewModel.prototype.constructor = ListMoverSelectStudentViewModel;

	ListMoverSelectStudentViewModel.prototype.columnSources = {
		student: [
			// {
			// 	FieldName: 'RawImage',
			// 	DisplayName: ' ',
			// 	Width: '35px',
			// 	sortable: false,
			// 	locked: false,
			// 	type: 'nofilter',
			// 	isSortItem: false,
			// 	template: function(arg)
			// 	{
			// 		var url = "data:image/jpeg;base64," + arg.RawImage;
			// 		return '<img style="width:20px; height:20px;" src="' + url + '" class="img-circle"/>';
			// 		//return '<img style="width:20px; height:20px;" src="' + url + '" onerror="this.src=\'default.JPG\'" class="img-circle"/>';
			// 		//return '<img style="width:20px; height:20px;" src="' + url + '" onerror="this.src=\'\e135\'" class="img-circle"/>';
			// 	},
			// 	filterable: false,
			// 	DBName: "RawImage"
			// },
			{
				FieldName: 'FullName',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('Name'),
				Width: '120px',
				type: 'string',
				isSortItem: true
			}, {
				FieldName: 'School',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('School'),
				Width: '120px',
				type: 'string',
				isSortItem: true
			},
			// {
			// 	FieldName: 'Geo',
			// 	DisplayName: tf.applicationTerm.getApplicationTermSingularByName('Geo'),
			// 	type: 'string',
			// 	hidden: true
			// },
			{
				FieldName: 'Grade',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('Grade'),
				Width: '60px',
				type: 'string',
				isSortItem: true,
				filterable: {
					cell: {
						showOperators: false
					}
				}
			}
		]
	};

	ListMoverSelectStudentViewModel.prototype.onLeftDataBound = function()
	{
		TF.Control.KendoListMoverWithSearchControlViewModel.prototype.onLeftDataBound.apply(this, arguments);
		$('.grid-filter-clear-all').removeClass('grid-filter-clear-all');
	};

	ListMoverSelectStudentViewModel.prototype.getFields = function()
	{
		return this.columns.map(function(item) { return item.FieldName; }).concat(['Id']);
	};

	ListMoverSelectStudentViewModel.prototype.rightKendoGridOption = function()
	{
		var options = TF.Control.KendoListMoverWithSearchControlViewModel.prototype.rightKendoGridOption.call(this);

		// make the right selected grid filterable
		if (this.options.rightGridFilter)
		{
			options.filterable = {
				extra: false,
				mode: "row"
			};
			options.dataSource.serverFiltering = true;
		}
		return options;
	};

	/**
	 * override getRightColumns to support filter
	 */
	ListMoverSelectStudentViewModel.prototype.getRightColumnsSupportFilter = function()
	{
		var self = this;
		var currentColumns = this._gridDefinition.Columns;
		var columns = currentColumns.map(function(definition)
		{
			var column = definition;
			column.field = definition.FieldName;
			column.title = definition.DisplayName;
			column.width = definition.Width || TF.Control.KendoListMoverWithSearchControlViewModel.defaults.columnWidth;
			column.hidden = definition.hidden;
			self.setColumnFilterableCell(column, definition, "listmover");
			if (column.filterable &&
				column.filterable.cell)
			{
				column.filterable.cell.showOperators = false;
			}
			if (definition.AllowSorting === false)
			{
				column.sortable = false;
			}
			if (definition.template !== undefined)
			{
				column.template = definition.template;
			}

			return column;
		});
		return columns;
	};

	ListMoverSelectStudentViewModel.prototype.apply = function()
	{
		// Todo: input data struct (studentDataModel) different with output data struct (subClass),  need convert data
		return new Promise(function(resolve, reject)
		{
			if (this.options.mustSelect && this.selectedData.length === 0)
			{
				reject();
			} else
			{
				var requestOption = this.setLeftRequestOption(
					{
						data: {},
						paramData: {}
					});
				requestOption.data.idFilter = { IncludeOnly: this.selectedData };
				var sortColumns = TF.FilterHelper.getSortColumns(this.columns);
				TF.FilterHelper.setSortItems(requestOption, sortColumns);
				return tf.ajax.post(this.setLeftGridRequestURL(this.options.getUrl(this.options.type, this.options)), requestOption).then(function(response)
				{
					var data = $.isArray(response.Items[0]) ? response.Items[0] : response.Items;
					resolve(data);
				}.bind(this));
			}
		}.bind(this));
	};

	ListMoverSelectStudentViewModel.prototype.cancel = function()
	{
		if (this.options.ignoreChangeConfirmation)
		{
			return Promise.resolve(true);
		}

		return new Promise(function(resolve, reject)
		{
			if (!isArraySame(this.oldData, this.selectedData))
			{
				return tf.promiseBootbox.yesNo("You have unsaved changes.  Are you sure you want to cancel?", "Confirmation Message").then(function(result)
				{
					if (result)
					{
						resolve(true);
					}
					else
					{
						reject();
					}
				});
			} else
			{
				resolve(true);
			}
		}.bind(this));
	};

	ListMoverSelectStudentViewModel.prototype.afterInit = function()
	{
		if (this._needFilterCheckBox) return;
		this.$form.find(".checkbox.list-mover-grid-right-label").hide();
		this.$form.find(".checkbox.list-mover-grid-right-label").hide();
	}

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var diffData1 = Enumerable.From(newData).Where(function(x)
		{
			return !Array.contain(oldData, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldData).Where(function(x)
		{
			return !Array.contain(newData, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();
