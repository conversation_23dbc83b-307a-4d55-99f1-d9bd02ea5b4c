(function()
{
	createNamespace("TF.Modal").ModalManager = ModalManager;

	function ModalManager()
	{
		var self = this;

		self._$modalContainer = $('.tfmodal-container');
		self._showing = false;
		self.obBaseModalViewModels = ko.observableArray([]);
		self.obShowing = ko.observable(false);
		self.modalAdd = self.modalAdd.bind(self);
		// ko.applyBindings(self, self._$modalContainer[0]);
		self._handlers = {};

		if (tf.isViewfinder)
		{
			$("body").delegate("input,textarea,body,button,a", "focus.modal", function(e)
			{
				var focusList = ['<input class="k-color-value form-control">'];
				// if user click tab key in the keyboard and the focus is not in modal which is opened, the first input will be focused in the modal
				if (tf.modalManager.obBaseModalViewModels().length > 0)
				{
					var $target = tf.modalManager.obBaseModalViewModels()[tf.modalManager.obBaseModalViewModels().length - 1].$target;
					var $modalBody = $target ? $target.find(".modal-body") : undefined;
					if ($modalBody && $modalBody[0] && !$modalBody[0].contains(e.target) && !$(e.target).closest(".typeahead").length > 0 && focusList.indexOf(e.target.outerHTML) == -1)
					{
						// find the first input or textarea in modal which is opened
						var $inputs = $modalBody.find("input[tabindex!=-1]:visible:enabled:first,textarea[tabindex!=-1]:visible:enabled:first");
						if ($inputs.length > 0)
						{
							$($inputs[0]).focus();
						}
						else 
						{
							var focusHtml = "<input class='used-for-focus' />";
							$modalBody.append(focusHtml);
							$modalBody.find(".used-for-focus").focus();
							$modalBody.find(".used-for-focus").remove();
						}
						e.preventDefault();
					}
				}
			});
		}

		self._$modalContainer.on('shown.bs.modal', function(e)
		{
			var $modal = $(e.target);
			var baseModalViewModel = ko.dataFor($modal[0]);
			if (!baseModalViewModel.containerLoaded) return;
			baseModalViewModel.containerLoaded(true);
			baseModalViewModel.$target = $modal;
			if (!TF.isMobileDevice)
			{
				$(document.activeElement).blur();
				if (baseModalViewModel.focusInFirstElement)
				{
					$modal.find(':text,select,textarea,:checkbox,:password,:radio,:file').not(":disabled").eq(0).focus();
				}

				if (baseModalViewModel.obResizable())
				{
					self.makeModalResizable($modal);
				}
			}
			else
			{
				if (!TF.isPhoneDevice)
				{
					var scrollElements = baseModalViewModel.$target.find(':visible').filter(function(index, element)
					{
						var overflow = $(element).css("overflow");
						return overflow == 'auto' || overflow == 'scroll';
					});

					for (var i = 0; i < scrollElements.length; i++)
					{
						new TF.TapHelper(scrollElements[i], {
							swipingUp: function(e)
							{
								if (isSpecificDomInMobileDevice(gridMapPopupSubContentSelector, e.target))
								{
									return;
								}

								e.stopPropagation();
								var target = e.currentTarget;
								if (target && target.scrollHeight - target.scrollTop <= target.clientHeight)
								{
									if (e.cancelable && !(TF.isMobileDevice && self._$modalContainer.find(".form-container").length > 0))
									{
										e.preventDefault();
									}
								}
							},
							swipingDown: function(e)
							{
								if (isSpecificDomInMobileDevice(gridMapPopupSubContentSelector, e.target))
								{
									return;
								}

								e.stopPropagation();
								var target = e.currentTarget;
								if (target && target.scrollTop <= 0)
								{
									if (e.cancelable && !(TF.isMobileDevice && self._$modalContainer.find(".form-container").length > 0))
									{
										e.preventDefault();
									}
								}
							}
						});
					}
				}
			}

			// these input is not focused when user click tab key in the keyboard
			var $inputs = $modal.find(".modal-body").find("input:button,input:image,input:file,input.notinput-required-message,input[data-tf-input-type='Select']");
			$inputs.attr("tabIndex", -1);

		});

		self._$modalContainer.on('hidden.bs.modal', function(e)
		{
			var $modal = $(e.target);
			var baseModalViewModel = ko.dataFor($modal[0]);
			// remove child hash key
			tf.shortCutKeys.removeChildKey(baseModalViewModel.shortCutKeyHashMapKeyName, baseModalViewModel.inheritChildrenShortCutKey);

			baseModalViewModel.dispose();
			$modal.data("bs.modal", null);
			self.obBaseModalViewModels.remove(baseModalViewModel);

			if (self._$modalContainer.find(".modal-fullscreen").length === 0)
			{
				$("body").removeClass('modal-fullscreen-open');
			}
		});

		$(window).resize(function(e)
		{
			if (TF.isAndroid || (TF.isMobileDevice && self._$modalContainer.find(".form-container").length > 0))
			{
				return;
			}
			setTimeout(function()
			{
				self.obBaseModalViewModels().map(function(modal)
				{
					if (!modal.$target)
					{
						return;
					}

					var $dialog = modal.$target.find(".modal-dialog"),
						offset = $dialog.offset(),
						deltaY = ($(window).height() - $dialog.outerHeight(true)) / 2,
						deltaX = ($(window).width() - $dialog.outerWidth(true)) / 2,
						top, left;

					if (!$dialog.hasClass("modal-fullscreen"))
					{
						$dialog.find('.modal-body').css("max-height", window.innerHeight - 80);
					}

					if (offset.top < 0)
					{
						$dialog.css("top", "-" + deltaY + "px");
					}
					else if (offset.top > deltaY * 2)
					{
						$dialog.css("top", deltaY + "px");
					}

					if (offset.left < 0)
					{
						$dialog.css("left", "-" + deltaX + "px");
					}
					else if (offset.left > deltaX * 2)
					{
						$dialog.css("left", deltaX + "px");
					}
				});
			});
		});
	}

	var gridMapPopupSubContentSelector = ".mobile-modal-content-body .sub-content";
	function isSpecificDomInMobileDevice(selector, target)
	{
		if (!TF.isMobileDevice)
		{
			return false;
		}

		return $(target).closest(selector).length;
	}

	ModalManager.prototype = {
		hideModal: function(baseModalViewModel)
		{
			var $modal = null,
				$modals = this._$modalContainer.find(".tfmodal"),
				length = $modals.length;

			if (length === 0) { return; }

			for (var i = 0; i < $modals.length; i++)
			{
				if (ko.dataFor($modals[i]) == baseModalViewModel)
				{
					$modal = $($modals[i]);
				}
			}
			if ($modal)
			{
				$modal.find(".modal-header").off("click.inputblur");
				$modal.modal('hide');
			}

			// remove child hash key
			tf.shortCutKeys.removeChildKey(baseModalViewModel.shortCutKeyHashMapKeyName, baseModalViewModel.inheritChildrenShortCutKey);
			delete tf.shortCutKeys._triggerMap[baseModalViewModel.shortCutKeyHashMapKeyName]
		},

		showModal: function(baseModalViewModel, draggable)
		{
			if (!(baseModalViewModel instanceof TF.Modal.BaseModalViewModel))
			{
				throw "require a subclass of BaseModalViewModel";
			}
			var self = this;
			self.currentBaseModalViewModel = baseModalViewModel;
			baseModalViewModel.draggable = draggable == false ? false : true;

			// add the child hash key
			tf.shortCutKeys.addChildKey(baseModalViewModel.shortCutKeyHashMapKeyName);

			self.obBaseModalViewModels.push(baseModalViewModel);

			tf.shortCutKeys.bind(["tab", "shift+tab"], self.TabClicked.bind(self), baseModalViewModel.shortCutKeyHashMapKeyName, "keydown");

			// Bind hot keys
			self.bindHotKeys(baseModalViewModel);
			return baseModalViewModel.getPromise();
		},

		hideAll: function()
		{
			var self = this;
			var openModals = this.obBaseModalViewModels();

			var cnt = openModals.length;
			for (var i = 0; i < cnt; i++)
			{
				var openModal = openModals.pop();
				self.hideModal(openModal);
			}
		}
	};

	ModalManager.prototype.TabClicked = function(e)
	{
		var self = this,
			$currentModal = _.last(self.obBaseModalViewModels()).$target,
			$modalBody = $currentModal.find(".modal-body"),
			isModalHidden = !$modalBody || $modalBody.length == 0 || $modalBody.is(":hidden"),
			isTabDisabled = $modalBody.find('.enable-tab').length === 0;

		if (isModalHidden || isTabDisabled)
		{
			return;
		}

		var potentialElements = $modalBody.find(":tabbable"),
			sequence = $.map(potentialElements, function(item)
			{
				return item;
			}),
			groups = _.groupBy(potentialElements, function(el)
			{
				var $parent = $(el).parents(".input-group");
				if ($parent.length != 0)
				{
					var rect = $parent[0].getBoundingClientRect();
					return rect.x * rect.y + 1;
				}
			}),
			keys = Object.keys(groups),
			focusableElements = [];

		for (var i = 0; i < keys.length; i++)
		{
			focusableElements = isNaN(Number(keys[i])) ?
				focusableElements.concat(groups[keys[i]]) :
				focusableElements.concat(groups[keys[i]].filter(function(el)
				{
					return $(el).attr("data-tf-input-type");
				}));
		}

		focusableElements = focusableElements.filter(function(el)
		{
			return $(el).parents(".k-grid").length == 0;
		});

		focusableElements = sequence.filter(function(item)
		{
			return focusableElements.some(function(el)
			{
				return el == item;
			});
		});

		if (focusableElements.length == 0) return;

		if ($modalBody.find(".respect-tab-index"))
		{
			focusableElements.sort(function(a, b)
			{
				return +a.attributes.tabindex.value > +b.attributes.tabindex.value ? 1 : -1;
			});
		}

		var isShift = e.shiftKey,
			count = focusableElements.length,
			curIndex = _.findIndex(focusableElements, function(el) { return $(el).is(":focus"); }),
			targetIndex = curIndex + (isShift ? -1 : 1);

		if (targetIndex < 0)
		{
			targetIndex = count - 1;
		}
		else if (targetIndex > count - 1)
		{
			targetIndex %= count;
		}

		$(focusableElements[targetIndex]).focus();
		e.preventDefault();
		e.stopPropagation();
	};

	/**
	 * Bind hot keys to modal;
	 * @param {object} baseModalViewModel the object of modal .
	 * @returns {void}
	 */
	ModalManager.prototype.bindHotKeys = function(baseModalViewModel)
	{
		var self = this;
		// If this modal has buttons.
		var buttonTemplateLocation = baseModalViewModel.buttonTemplate() ? baseModalViewModel.buttonTemplate().toLowerCase() : "";
		if (buttonTemplateLocation)
		{
			// If there are 'save' button and 'save & new' button both on this modal, only bind the 'save' button event with the hot key 's'.
			var usedFirstletter = [], underlineButtons = [];

			/**
			 * Get the first letter from the button's label, then bind the button event with the first letter and alt-firstletter.
			 * @param {string} label the button's label.
			 * @param {string} buttonClass find the button by button's class.
			 * @returns {boolean} Binding success or faild
			 */
			function bind(label, buttonClass)
			{
				var firstletter = label.length > 0 ? label.substr(0, 1).toLowerCase() : null;
				if (firstletter && usedFirstletter.indexOf(firstletter) === -1)
				{
					// The alt-firstletter was binded, when the focus in input, textarea or select, this hot key always is available.
					bindExtend("alt+" + firstletter, buttonClass, { permission: ["INPUT", "TEXTAREA", "SELECT"] });
					usedFirstletter.push(firstletter);
					return true;
				}
				return false;
			}

			/**
			 * Bind the hot keys.
			 * @param {string} bindKeysName the keys' name.
			 * @param {string} buttonClass find the button by button's class.
			 * @param {array} permission which element is focused, the hot key is still available.
			 * @returns {void}
			 */
			function bindExtend(bindKeysName, buttonClass, permission)
			{
				function buttonTrigger()
				{
					var $positive = baseModalViewModel.$target.find(buttonClass);
					if (!$positive.attr("disabled"))
					{
						$(document.activeElement).blur();
						// TODO: distinguish click and mousedown event.
						const eventArg = { key: bindKeysName };
						$positive.trigger("mousedown", eventArg).trigger("click", eventArg);
					}
					clearUnderline(underlineButtons);
				}

				tf.shortCutKeys.bind(bindKeysName, function()
				{
					baseModalViewModel.notDelayTrigger ? buttonTrigger() : setTimeout(buttonTrigger);
					return false;
				}, baseModalViewModel.shortCutKeyHashMapKeyName, undefined, permission);
			}

			/**
			 * Clear the buttons' underline.
			 * @param {array} buttons the array of button style's observable object.
			 * @returns {void}
			 */
			function clearUnderline(buttons)
			{
				buttons.forEach(function(item)
				{
					item.underlineClass("");
				});
			}

			/**
			 * Set the buttons' underline.
			 * @param {array} buttons the array of button style's observable object.
			 * @returns {void}
			 */
			function setUnderline(buttons)
			{
				buttons.forEach(function(item)
				{
					var $button = baseModalViewModel.$target.find(item.buttonClass);
					if (!$button.attr("disabled"))
					{
						item.underlineClass("underline");
					}
				});
			}

			// All modals have the positive button.
			var positiveLabel = baseModalViewModel.obPositiveButtonLabel().trim();
			if (bind(positiveLabel, ".modal-footer button.positive"))
			{
				underlineButtons.push({ underlineClass: baseModalViewModel.obPositiveUnderlineClass, buttonClass: ".modal-footer button.positive" });
			}

			if (baseModalViewModel.obEnableEsc())
			{
				// The "esc" should act like the close button.
				bindExtend("esc", ".modal-header button.close", { permission: ["INPUT", "TEXTAREA", "SELECT"] });
			}

			if (baseModalViewModel.obEnableEnter())
			{
				// The "enter" should act like the positive button.
				bindExtend("enter", ".modal-footer button.positive", { permission: ["INPUT", "SELECT"] });
			}

			// Not include the modal which only has one button.
			if (buttonTemplateLocation !== "modal/positive")
			{
				var negativeLabel = baseModalViewModel.obNegativeButtonLabel().trim();
				if (bind(negativeLabel, ".modal-footer button.negative"))
				{
					underlineButtons.push({ underlineClass: baseModalViewModel.obNegativeUnderlineClass, buttonClass: ".modal-footer button.negative" });
				}
				// The modal has three buttons.
				if (buttonTemplateLocation === "modal/positivenegativeextend")
				{
					var otherLabel = baseModalViewModel.obSaveAndNewButtonLabel().trim();
					if (bind(otherLabel, ".modal-footer button.other"))
					{
						underlineButtons.push({ underlineClass: baseModalViewModel.obSaveAndNewUnderlineClass, buttonClass: ".modal-footer button.other" });
					}
				}
				else if (buttonTemplateLocation === "modal/positivenegativeother")
				{
					var otherLabel = baseModalViewModel.obOtherButtonLabel().trim();
					if (bind(otherLabel, ".modal-footer button.other"))
					{
						underlineButtons.push({ underlineClass: baseModalViewModel.obOtherUnderlineClass, buttonClass: ".modal-footer button.other" });
					}
				}
			}

			var timeOut = null, time = 500;
			if (navigator.userAgent.indexOf('Firefox') >= 0)
			{
				tf.shortCutKeys.bind("alt", function()
				{
					if (!timeOut)
					{
						setUnderline(underlineButtons);
					}
					else
					{
						clearTimeout(timeOut);
					}
					timeOut = setTimeout(function()
					{
						clearUnderline(underlineButtons);
						timeOut = null;
						time = 500;
					}, time);
					time = 100;
					return false;
				}, baseModalViewModel.shortCutKeyHashMapKeyName, "keydown", { permission: ["INPUT", "TEXTAREA", "SELECT"] });
			}
			else
			{
				// VIEW-2408, Add the underlined letter corresponding to its hotkey when the 'Alt' key was pressed.
				tf.shortCutKeys.bind("alt", function()
				{
					setUnderline(underlineButtons);
					return false;
				}, baseModalViewModel.shortCutKeyHashMapKeyName, "keydown", { permission: ["INPUT", "TEXTAREA", "SELECT"] });
				tf.shortCutKeys.bind("alt", function()
				{
					clearUnderline(underlineButtons);
					return false;
				}, baseModalViewModel.shortCutKeyHashMapKeyName, "keyup", { permission: ["INPUT", "TEXTAREA", "SELECT"] });
			}
		}
	};

	ModalManager.prototype.modalAdd = function(el, modalViewModel)
	{
		var options = { backdrop: (this.currentBaseModalViewModel.obCloseable() ? true : "static"), keyboard: false },
			$el = $(el),
			tfModal = $el.closest('.tfmodal'),
			dialog = $el.closest('.modal-dialog');
		if (this.currentBaseModalViewModel.obBackdrop() !== "")
		{
			options.backdrop = this.currentBaseModalViewModel.obBackdrop();
		}
		if (dialog.attr("class").indexOf("modal-fullscreen") === -1)
		{
			dialog.find('.modal-body').css("max-height", window.innerHeight - 80);
		}
		if (modalViewModel.draggable && !dialog.hasClass("modal-fullscreen"))
		{
			const parentContainerId = TF.productName === 'Viewfinder' ? '#pageContent' : '#main';
			tf.promiseBootbox.modalDraggable(tfModal, dialog, null, parentContainerId);
			tfModal.find(".modal-header").on("click.inputblur", function(e)
			{
				$(document.activeElement).blur();
			});
		}

		if (dialog.hasClass("modal-fullscreen") && dialog.find("#FullPageCalloutContainer") === 0 && TF.isPhoneDevice)
		{
			$("body").addClass('modal-fullscreen-open');
			dialog.find(".mobile-modal-grid-head").on("touchmove", function(e)
			{
				e.stopPropagation();
				e.preventDefault();
			});
			new TF.TapHelper(dialog[0], {
				swipingUp: function(e)
				{
					if (isSpecificDomInMobileDevice(gridMapPopupSubContentSelector, e.target))
					{
						return;
					}

					var modalBody = $(e.target).closest(".mobile-modal-content-body")[0];
					if (modalBody && modalBody.scrollHeight - modalBody.scrollTop <= $(modalBody).height())
					{
						e.stopPropagation();
						e.preventDefault();
					}
				},
				swipingDown: function(e)
				{
					if (isSpecificDomInMobileDevice(gridMapPopupSubContentSelector, e.target))
					{
						return;
					}

					var modalBody = $(e.target).closest(".mobile-modal-content-body")[0];
					if (modalBody && modalBody.scrollTop <= 0)
					{
						e.stopPropagation();
						e.preventDefault();
					}
				}
			});
		}
		else
		{
			// var scrollElements = tfModal.find(':visible').filter(function(index, element)
			// {
			//	var overflow = $(element).css("overflow");
			//	return overflow == 'auto' || overflow == 'scroll';
			// })

			// tfModal.on("touchmove", function(e)
			// {
			//	//e.stopPropagation();
			//	//e.preventDefault();
			// });

			// for (var i in scrollElements)
			// {
			//	var x = scrollElements[i]
			//	new TF.TapHelper(dialog[0], {
			//		swipingUp: function(e)
			//		{
			//			var modalBody = $(e.target).closest(".modal-body")[0];
			//			if (modalBody && modalBody.scrollHeight - modalBody.scrollTop <= modalBody.clientHeight)
			//			{
			//				e.stopPropagation();
			//				e.preventDefault();
			//			}
			//		},
			//		swipingDown: function(e)
			//		{
			//			var modalBody = $(e.target).closest(".modal-body")[0];
			//			if (modalBody && modalBody.scrollTop <= 0)
			//			{
			//				e.stopPropagation();
			//				e.preventDefault();
			//			}
			//		}
			//	});
			// }
		}
		tfModal.modal(options);
		this.currentBaseModalViewModel.obPageElement(tfModal);

		if (modalViewModel.afterRender)
		{
			modalViewModel.afterRender(el);
		}
	};

	ModalManager.prototype.makeModalResizable = function($modal)
	{
		var modalContent = $modal.find(".modal-content");
		modalContent.resizable({
			start: function()
			{
				modalContent.find(".kendo-grid,.k-grid-content,.content-resizable").each(function(i, item)
				{
					$(item).data("originalHeight", $(item).height());
				});
				$modal.find(".modal-body").css("max-height", "inherit");
			},
			resize: function(event, ui)
			{
				var height = ui.size.height;
				var changeHeight = ui.size.height - ui.originalSize.height;
				var bodyHeight = height - 42 - 46 - 30;
				ui.element.find(".modal-body").height(bodyHeight);
				var kendoGridResizable = modalContent.find(".content-resizable");
				if (kendoGridResizable.length > 0)
				{
					kendoGridResizable.each(function(i, item)
					{
						resizeGrid($(item));
					});
				} else
				{
					var kendoGrid = modalContent.find(".kendo-grid:not(.height-fixed)");
					if (kendoGrid.length > 0)
					{
						resizeGrid(kendoGrid.eq(kendoGrid.length - 1));
					}
				}

				function resizeGrid(kendoGrid)
				{
					var gridContent = kendoGrid.find(".k-grid-content");
					var newHeight = kendoGrid.data("originalHeight") + changeHeight;
					kendoGrid.height(newHeight);
					if (gridContent.length > 0)
					{
						gridContent.height(gridContent.data("originalHeight") + changeHeight);
					}
					if (kendoGrid.closest(".box-line-container").length > 0)
					{
						kendoGrid.closest(".box-line-container").height(newHeight + 30);
					}
				}
			}
		});
	};
})();
