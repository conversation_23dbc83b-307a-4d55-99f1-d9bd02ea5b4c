(function()
{
	createNamespace('TF.Control').EditFieldTripTemplateRecordViewModel = EditFieldTripTemplateRecordViewModel;

	const NONE_VALUE = "(None)";
	const SupportedUdfTypes = TF.createEnum(
		["Boolean", "Date", "DateTime", "Memo", "Number", "Phone", "Text", "Time", "List", "Email", "Currency", "Hyperlink", "Image"],
		[1, 2, 3, 4, 5, 6, 7, 8, 10, 22, 12, 23, 24]
	);

	function EditFieldTripTemplateRecordViewModel(configType, recordEntity)
	{
		let self = this;
		TF.Control.EditFieldTripConfigRecordViewModelBase.call(self, configType, recordEntity);
		let entity = recordEntity || {};

		self.isStrictDestination = tf.fieldTripConfigsDataHelper.fieldTripConfigs.StrictDest;
		self.isStrictAccountCodes = tf.fieldTripConfigsDataHelper.fieldTripConfigs.StrictAcctCodes;

		self.entity = entity;

		self.obTemplateName = ko.observable(entity.Name);
		self.obInactive = ko.observable(Boolean(entity.TemplateStatus));
		//#region Main

		self.obFieldTripName = ko.observable(entity.FieldTripName);
		self.obSchoolList = ko.observableArray();
		self.obSelectedSchool = ko.observable();
		self.obSelectedSchoolText = ko.computed(function()
		{
			return (self.obSelectedSchool() || {}).value;
		});

		self.obDepartmentList = ko.observableArray();
		self.obSelectedDepartment = ko.observable();
		self.obSelectedDepartmentText = ko.computed(function()
		{
			return (self.obSelectedDepartment() || {}).value;
		});

		self.obActivityList = ko.observableArray();
		self.obSelectedActivity = ko.observable();
		self.obSelectedActivityText = ko.computed(function()
		{
			return (self.obSelectedActivity() || {}).value;
		});

		self.obTravelScenarioList = ko.observableArray();
		self.obSelectedTravelScenario = ko.observable();
		self.obSelectedTravelScenarioText = ko.computed(function ()
		{
			return (self.obSelectedTravelScenario() || {}).value;
		});

		self.obFieldTripContact = ko.observable(entity.FieldTripContact);
		self.obContactPhone = ko.observable(entity.ContactPhone);
		self.obContactPhoneExt = ko.observable(entity.ContactPhoneExt);
		self.obContactEmail = ko.observable(entity.ContactEmail);

		self.obEquipmentList = ko.observableArray();
		self.obSelectedEquipment = ko.observable();
		self.obSelectedEquipmentText = ko.computed(function()
		{
			return (self.obSelectedEquipment() || {}).value;
		});

		self.obClassificationList = ko.observableArray();
		self.obSelectedClassification = ko.observable();
		self.obSelectedClassificationText = ko.computed(function()
		{
			return (self.obSelectedClassification() || {}).value;
		});

		self.obNumofStudents = ko.observable(entity.NumberOfStudents);
		self.obNumofAdults = ko.observable(entity.NumberOfAdults);
		self.obNumofWheelChairs = ko.observable(entity.NumberOfWheelChairs);
		self.obNumofVehicles = ko.observable(entity.NumberOfVehicles);
		self.obEstimatedDistance = ko.observable(tf.measurementUnitConverter.convert({
			originalUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
			targetUnit: tf.measurementUnitConverter.getCurrentUnitOfMeasure(),
			value: entity.EstimatedDistance,
		}));
		self.obEstimatedCost = ko.observable(entity.EstimatedCost);
		self.obEstimatedHours = ko.observable(entity.EstimatedHours);

		self.obFieldTripAccounts = ko.observableArray([]);
		self.obSelectedAccount = ko.observable();
		self.obSelectedAccountText = ko.computed(function()
		{
			return (self.obSelectedAccount() || {}).value;
		});
		//#endregion

		//#region Destination

		self.obDepartureList = ko.observableArray();
		self.obSelectedDeparture = ko.observable();
		self.obSelectedDepartureText = ko.computed(function()
		{
			return (self.obSelectedDeparture() || {}).value;
		});

		self.obDepartureNote = ko.observable(entity.DepartureNotes);

		self.obDestinationList = ko.observableArray();
		self.obSelectedDestination = ko.observable();
		self.obSelectedDestinationText = ko.computed(function()
		{
			return (self.obSelectedDestination() || {}).value;
		});

		self.obDestinationStreet = ko.observable(entity.DestinationStreet);
		self.obDestinationCityList = ko.observableArray();
		self.obDestinationCity = ko.observable(entity.DestinationCity);
		self.obDestinationState = ko.observable(entity.DestinationState);
		self.obDestinationZip = ko.observable(entity.DestinationZip);
		self.obDestinationZipList = ko.observableArray();
		self.obDestinationContact = ko.observable(entity.DestinationContact);
		self.obDestinationTitle = ko.observable(entity.DestinationContactTitle);
		self.obDestinationPhone = ko.observable(entity.DestinationContactPhone);
		self.obDestinationPhoneExt = ko.observable(entity.DestinationPhoneExt);
		self.obDestinationFax = ko.observable(entity.DestinationFax);
		self.obDestinationEmail = ko.observable(entity.DestinationEmail);
		self.obDestinationNote = ko.observable(entity.DestinationNotes);
		self.obDirections = ko.observable(entity.DirectionNotes);

		//#endregion

		//#region Billing

		self.obBillingClassificationList = ko.observableArray();
		self.obSelectedBillingClassification = ko.observable();
		self.obSelectedBillingClassificationText = ko.computed(function()
		{
			const selectedValue = (self.obSelectedBillingClassification() || {}).value;
			return selectedValue ?? NONE_VALUE;
		});

		self.obFuelConsumptionRate = ko.observable(tf.measurementUnitConverter.convert({
			originalUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
			targetUnit: tf.measurementUnitConverter.getCurrentUnitOfMeasure(),
			value: entity.FuelConsumptionRate,
			isReverse: true,
		}),);
		self.obAideFixedCost = ko.observable(entity.AideFixedCost);
		self.obTripFixedCost = ko.observable(entity.FixedCost);
		self.obDriverFixedCost = ko.observable(entity.DriverFixedCost);
		self.obVehFixedCost = ko.observable(entity.VehFixedCost);
		self.obMinimumCost = ko.observable(entity.MinimumCost);
		self.obDriverRate = ko.observable(entity.DriverRate);
		self.obDriverOTRate = ko.observable(entity.DriverOTRate);
		self.obAideRate = ko.observable(entity.AideRate);
		self.obAideOTRate = ko.observable(entity.AideOTRate);
		self.obBillingNotes = ko.observable(entity.BillingNotes);

		self.obInvoiceCount = ko.observable();

		self.obSelectedInvoices = ko.observableArray([]);

		self.obDeletingInvoices = ko.observableArray([]);

		self.obUpdateingInvoices = ko.observableArray([]);

		//#endregion

		self.obNotes = ko.observable(entity.Notes);

		//#region Documents

		self.obDocumentCount = ko.observable();

		self.obSelectedDocuments = ko.observableArray([]);

		self.obDeletingDocuments = ko.observableArray([]);

		self.obUpdatingDocuments = ko.observableArray([]);

		//#endregion

		//#region User Defined Fields
		self.udfHelper = new TF.DetailView.UserDefinedFieldHelper();
		self.udfValueMap = {};
		//#endregion
	}

	EditFieldTripTemplateRecordViewModel.prototype = Object.create(TF.Control.EditFieldTripConfigRecordViewModelBase.prototype);

	EditFieldTripTemplateRecordViewModel.prototype.constructor = EditFieldTripTemplateRecordViewModel;

	EditFieldTripTemplateRecordViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		TF.Control.EditFieldTripConfigRecordViewModelBase.prototype.init.call(self, viewModel, el);

		self.obSelectedSchool.subscribe(function(data)
		{
			self.handleDepartmentActivityCombinations((data || {}).key);
		});


		self.$element.find(".tab .tab-header > div").on("click", function(e)
		{
			if (!$(e.currentTarget).hasClass("active"))
			{
				$(e.currentTarget).siblings().removeClass("active");
				$(e.currentTarget).addClass("active");
				let index = $(e.currentTarget).parent(".tab-header").find(">div").index($(e.currentTarget));
				self.$element.find(".tab .tab-panel > div").hide();
				self.$element.find(".tab .tab-panel > div").eq(index).show();
			}
		});

		self.documentClassificationPromise = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "DocumentClassifications")).then(r => (r && r.Items || []).map(({ Id, Name }) => ({ key: Id, value: Name })));

		Promise.all([
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"), { paramData: { "@fields": "Name,SchoolCode" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "DistrictDepartments"), { paramData: { "@fields": "Name,Id" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripActivities"), { paramData: { "@fields": "Name,Id" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "FieldTripTravelScenarios"), { paramData: { "@fields": "Name,Id" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripClassifications"), { paramData: { "@fields": "Code,Id" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripEquipments"), { paramData: { "@fields": "EquipmentName,Id" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripBillingClassifications")),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripDestinations")),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "MailingCities"), { paramData: { "@fields": "Id,Name" } }),
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "MailingPostalCodes"), { paramData: { "@fields": "Id,Postal" } }),
			self.isEdit ? tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "documents"), {
				paramData: {
					"@relationships": "FieldTripTemplate,DocumentClassification,LastUpdatedName,DocumentRelationship",
					"@fields": "Id,FileName,DocumentClassificationName,Attached,FileSizeKB,LastUpdated,LastUpdatedName,DocumentRelationships,DocumentClassificationID,Description",
					attachedToTypeID: self.getDataTypeIdOfFieldTripTemplate(),
					attachedToID: self.entity.Id
				}
			}) : Promise.resolve({ Items: [] }),
			self.isEdit ? tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripInvoiceTemplates"), {
				paramData: {
					"@relationships": "FieldTripAccount",
					"@filter": `eq(FieldTripTemplateId,${self.entity.Id})`
				}
			}) : Promise.resolve({ Items: [] }),
			self.isStrictAccountCodes ? tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "fieldtripaccounts"), {
				paramData: {
					"@relationships": "Activity,Department",
				}
			}) : Promise.resolve({ Items: [] })
		]).then(values => values.map(v => v.Items)).then(function(
			[schools, districtDepartments, activities, travelScenarios, classifications, equipments, billingClassifications, destinations, cities, postalCodes, documents, invocies, fieldTripAccounts])
		{
			self.fieldTripAccounts = fieldTripAccounts;

			self.obSchoolList(schools.map(({ Name, SchoolCode }) => ({ key: SchoolCode, value: Name })));
			self.obSelectedSchool(self.obSchoolList().find(x => x.key === self.entity.School));

			self.obDepartmentList(districtDepartments.map(({ Name, Id }) => ({ key: Id, value: Name })));
			self.obSelectedDepartment(self.obDepartmentList().find(x => x.key === self.entity.DistrictDepartmentId));

			self.obActivityList(activities.map(({ Name, Id }) => ({ key: Id, value: Name })));
			self.obSelectedActivity(self.obActivityList().find(x => x.key === self.entity.FieldTripActivityId));

			// Walking is built-in Field Trip Travel Scenario, and it cannot rename.
			travelScenarios = travelScenarios.filter(x => x.Name != "Walking");
			self.obTravelScenarioList(travelScenarios.map(({ Name, Id }) => ({ key: Id, value: Name })));
			self.obSelectedTravelScenario(self.obTravelScenarioList().find(x => x.key === self.entity.TravelScenarioId));

			self.obEquipmentList(equipments.map(({ EquipmentName, Id }) => ({ key: Id, value: EquipmentName })));
			self.obSelectedEquipment(self.obEquipmentList().find(x => x.key === self.entity.FieldTripEquipmentId));

			self.obClassificationList(classifications.map(({ Code, Id }) => ({ key: Id, value: Code })));
			self.obSelectedClassification(self.obClassificationList().find(x => x.key === self.entity.FieldTripClassificationId));

			self.obDepartureList(schools.map(({ Name, SchoolCode }) => ({ key: SchoolCode, value: Name })));
			self.obSelectedDeparture(self.obDepartureList().find(x => x.key === self.entity.DepartFromSchool));

			self.obDestinationList(destinations.map(({ Name, Id, ...others }) => ({ key: Id, value: Name, ...others })));
			self.obSelectedDestination(self.obDestinationList().find(x => x.key === self.entity.FieldTripDestinationId));
			self.editedDestinationCollection = {};
			self.obSelectedDestination.subscribe(function(v)
			{
				if (v)
				{
					self.editedDestinationCollection[v.key] = {
						Street: self.obDestinationStreet(),
						City: self.obDestinationCity(),
						State: self.obDestinationState(),
						Zip: self.obDestinationZip(),
						Contact: self.obDestinationContact(),
						ContactTitle: self.obDestinationTitle(),
						Phone: self.obDestinationPhone(),
						PhoneExt: self.obDestinationPhoneExt(),
						Fax: self.obDestinationFax(),
						Email: self.obDestinationEmail(),
						Notes: self.obDestinationNote(),
					};
				}
			}, null, "beforeChange");
			self.obSelectedDestination.subscribe(function(v)
			{
				if (v)
				{
					v = self.editedDestinationCollection[v.key] || v;
					self.obDestinationStreet(v.Street);
					self.obDestinationCity(v.City);
					self.obDestinationState(v.State);
					self.obDestinationZip(v.Zip);
					self.obDestinationContact(v.Contact);
					self.obDestinationTitle(v.ContactTitle);
					self.obDestinationPhone(v.Phone);
					self.obDestinationPhoneExt(v.PhoneExt);
					self.obDestinationFax(v.Fax);
					self.obDestinationEmail(v.Email);
					self.obDestinationNote(v.Notes);
				}
			});
			self.obSelectedDestination.extend({ rateLimit: 200 });

			self.obDestinationCityList(cities.map(({ Name, Id }) => ({ key: Id, value: Name })));
			self.obDestinationZipList(postalCodes.map(({ Postal, Id }) => ({ key: Id, value: Postal })));

			self.obBillingClassificationList(Array.extend([{ key: null, value: NONE_VALUE }], billingClassifications.map(({ Classification, Id, ...others }) =>
			{
				others.FuelConsumptionRate && (others.FuelConsumptionRate = tf.measurementUnitConverter.convert({
					originalUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
					targetUnit: tf.measurementUnitConverter.getCurrentUnitOfMeasure(),
					value: others.FuelConsumptionRate,
					isReverse: true,
				}));
				return { key: Id, value: Classification, ...others };
			})));
			self.obSelectedBillingClassification(self.obBillingClassificationList().find(x => x.key === self.entity.BillingClassificationId));
			self.editedBillingClassificationCollection = {};
			self.obSelectedBillingClassification.subscribe(function(v)
			{
				if (v?.key != null)
				{
					self.editedBillingClassificationCollection[v.key] = {
						FuelConsumptionRate: self.obFuelConsumptionRate(),
						AideFixedCost: self.obAideFixedCost(),
						FixedCost: self.obTripFixedCost(),
						DriverFixedCost: self.obDriverFixedCost(),
						VehFixedCost: self.obVehFixedCost(),
						MinimumCost: self.obMinimumCost(),
						DriverRate: self.obDriverRate(),
						DriverOTRate: self.obDriverOTRate(),
						AideRate: self.obAideRate(),
						AideOTRate: self.obAideOTRate(),
					};
				}
			}, null, "beforeChange");
			self.obSelectedBillingClassification.subscribe(function(v)
			{
				if (v?.key != null)
				{
					v = self.editedBillingClassificationCollection[v.key] || v;
					self.obFuelConsumptionRate(v.FuelConsumptionRate);
					self.obAideFixedCost(v.AideFixedCost);
					self.obTripFixedCost(v.FixedCost);
					self.obDriverFixedCost(v.DriverFixedCost);
					self.obVehFixedCost(v.VehFixedCost);
					self.obMinimumCost(v.MinimumCost);
					self.obDriverRate(v.DriverRate);
					self.obDriverOTRate(v.DriverOTRate);
					self.obAideRate(v.AideRate);
					self.obAideOTRate(v.AideOTRate);
				}
				else
				{
					self.obFuelConsumptionRate(null);
					self.obAideFixedCost(null);
					self.obTripFixedCost(null);
					self.obDriverFixedCost(null);
					self.obVehFixedCost(null);
					self.obMinimumCost(null);
					self.obDriverRate(null);
					self.obDriverOTRate(null);
					self.obAideRate(null);
					self.obAideOTRate(null);
				}
			});
			self.obSelectedBillingClassification.extend({ rateLimit: 200 });

			if ([0, 1].includes(self.entity.InvoiceAmountType))
			{
				self.$element.find(`[name="amountbasis"][value=${self.entity.InvoiceAmountType}]`).prop("checked", true);
			}

			self.obDocumentCount(documents.length);

			self.$element.find(".document .kendo-grid.grid-container").kendoGrid({
				columns: [
					{ field: "FileName", title: `File Name`, width: "200px" },
					{ field: "Description", title: "Description", width: "150px" },
					{ field: "DocumentClassificationName", title: "Classification", width: "150px" },
					{ field: "Attached", title: "Attached", width: "100px" },
					{ field: "FileSizeKB", title: "Size(KB)", width: "100px" },
					{ field: "LastUpdated", title: "Last Updated", width: "250px" },
					{ field: "LastUpdatedName", title: "Last Updated By", width: "150px" },
				],
				height: 400,
				dataSource: documents.map(x => ({ ...x, Attached: 1, LastUpdated: moment(x.LastUpdated).format("MM/DD/YYYY") })),
				scrollable: true,
				sortable: false,
				selectable: "multiple",
				change: function(e)
				{
					var selectedRows = this.select();
					self.obSelectedDocuments(Array.from(selectedRows).map(row => this.dataItem(row)))
				}
			});

			self.obInvoiceCount(invocies.length);
			self.$element.find(".billing .kendo-grid.grid-container").kendoGrid({
				columns: [
					{ field: "AccountName", title: `Account Name`, width: "60%" },
					{
						field: "Amount", title: "Amount", width: "40%", template: '#= Amount ? kendo.toString(kendo.parseFloat(Amount), "n") : "" #'
					},
				],
				height: 300,
				dataSource: invocies,
				scrollable: true,
				sortable: false,
				selectable: "multiple",
				change: function(e)
				{
					var selectedRows = this.select();
					self.obSelectedInvoices(Array.from(selectedRows).map(row => this.dataItem(row)))
				}
			});
		});

		self.initFieldTripUdfList().then(udfItems =>
		{
			// create a clean copy of exist record entity
			let recordEntity = JSON.parse(JSON.stringify(self.entity));

			udfItems.forEach(udfItem =>
			{
				// get current udf value if exists
				let existUdf = recordEntity.UserDefinedFields?.find(udf => udf.Guid == udfItem.Guid);

				// 1. prepare UDF definition, additional option and observable value
				self.udfValueMap[udfItem.Guid] = {
					'definition': udfItem,
					'options': self.getUdfOptions(udfItem, existUdf),
					'obValue': self.getUdfObValue(udfItem, existUdf),
				};

				// 2. dynamically generate UDF html and apply value binding
				var udfHtmlContent = self.getUdfHtmlContent(udfItem, self.udfValueMap);

				udfHtmlContent = $(udfHtmlContent)[0];
				self.$element.find(".udf .main").append(udfHtmlContent);

				ko.cleanNode(udfHtmlContent);
				ko.applyBindings(ko.observable(self.udfValueMap[udfItem.Guid]), udfHtmlContent);
			});
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.handleDepartmentActivityCombinations = function(schoolCode)
	{
		let self = this;

		if (!schoolCode)
		{
			self.obFieldTripAccounts([]);
			self.obSelectedAccount(null);
		}
		else
		{
			let accounts = self.fieldTripAccounts.filter(({ School, DepartmentId }) => schoolCode === School && !DepartmentId);

			let groups = _.groupBy(accounts, function(account)
			{
				return `${account.School}_____****_____${account.DepartmentId || -1}_____$$$_____${account.FieldTripActivityId || -1}`;
			});

			self.obFieldTripAccounts(Object.values(groups).reduce(function(acc, [{ Id, DepartmentId, DepartmentName, FieldTripActivityId, FieldTripActivityName }])
			{
				return acc.concat({ key: Id, value: `${DepartmentId ? DepartmentName : "[Any]"} / ${FieldTripActivityId ? FieldTripActivityName : "[Any]"}`, DepartmentId, FieldTripActivityId })
			}, []));

			let selectedAccount = self.obFieldTripAccounts().find(function({ DepartmentId, FieldTripActivityId })
			{
				return DepartmentId && FieldTripActivityId && self.entity.FieldTripDepartment && self.entity.FieldTripDepartment.Id === DepartmentId && self.entity.FieldTripActivityId === FieldTripActivityId;
			});

			if (selectedAccount)
			{
				self.obSelectedAccount(selectedAccount);
			}
		}
	};

	EditFieldTripTemplateRecordViewModel.prototype.updateValidatorFields = function(validatorFields)
	{
		function initialFieldValidators(name)
		{
			if (!validatorFields[name] || !validatorFields[name].validators)
			{
				validatorFields[name] = {
					trigger: "blur change",
					validators: {}
				};
			}
		}

		function addNumericValidator(name, isCurrency)
		{
			let minValue = isCurrency ? -**********.999999 : -**********999.999999,
				maxValue = isCurrency ? **********.999999 : **********999.999999,
				minText = isCurrency ? "-**********" : "-**********999",
				maxText = isCurrency ? "**********" : "**********999",
				message = `${isCurrency ? "currency" : "numeric"} value should be between ${minText} and ${maxText}.`;

			validatorFields[name].validators.callback = {
				message: message,
				callback: function(value, validator, $field)
				{
					var numberValue = Number(value.replaceAll(',', ''));

					if (isNaN(numberValue))
					{
						return false;
					}

					if (numberValue >= minValue && numberValue <= maxValue)
					{
						return true;
					}

					return false;
				}
			}
		}

		let self = this;
		self.$element.find("input[data-tf-input-type=Email]").each(function(n, field)
		{
			var name = $(field).attr("name");
			initialFieldValidators(name);

			validatorFields[name].validators.emailAddress = {
				message: " invalid email"
			};
		});

		self.$element.find("input[data-tf-input-type=Phone]").each(function(n, field)
		{
			var name = $(field).attr("name");
			initialFieldValidators(name);

			validatorFields[name].validators.callback = {
				message: 'Invalid phone number.',
				callback: function(value)
				{
					return value === '' || tf.dataFormatHelper.isValidPhoneNumber(value);
				}
			};
		});

		self.$element.find("input[data-tf-input-type=Time]").each(function(n, field)
		{
			var name = $(field).attr("name");
			initialFieldValidators(name);

			validatorFields[name].validators.callback = {
				message: " invalid time",
				callback: function(value, validator, $field)
				{
					if (value)
					{
						var mmtFrom = moment(value, ["h:mm A", "hh:mm A"], true);
						if (!mmtFrom.isValid())
						{
							return { valid: false, message: " invalid time" };
						}
					}

					return { valid: true, message: "" };
				}
			};
		});

		self.$element.find(".field-trip-template-udf-currency").each(function(n, field)
		{
			var name = $(field).attr("name");
			initialFieldValidators(name);
			addNumericValidator(name, true);
		});

		self.$element.find(".field-trip-template-udf-number").each(function(n, field)
		{
			var name = $(field).attr("name");
			initialFieldValidators(name);
			addNumericValidator(name, false);
		});

		validatorFields["Name"] = {
			trigger: "none",
			validators: {
				callback: {
					callback: function(value)
					{
						if (!(value || "").trim())
						{
							return Promise.resolve({ valid: false, message: "required" });
						}

						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "FieldTripTemplates"), { paramData: { "@fields": "Id,Name" } }).then(function(response)
						{
							let existingItems = response.Items.filter(x => x.Id !== self.entity.Id);

							let isInvalid = existingItems.some(function(item) { return item.Name === value.trim() });

							return { valid: !isInvalid, message: "already exists" };
						});
					}
				}
			}
		};

		return validatorFields;
	};

	EditFieldTripTemplateRecordViewModel.prototype.getDataTypeIdOfFieldTripTemplate = function()
	{
		return tf.dataTypeHelper._getObjectByType("fieldtriptemplate").id;
	};

	EditFieldTripTemplateRecordViewModel.prototype.initValidation = function()
	{
		let self = this, isValidating = false,
			updateValidationMessage = function($field)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate($field);
					isValidating = false;
				}
			};

		TF.Control.EditFieldTripConfigRecordViewModelBase.prototype.initValidation.call(this);
		this.$element.off('error.validator.bv').on('error.validator.bv', function(e, data)
		{
			data.element
				.data('bv.messages')
				.find('.help-block[data-bv-for="' + data.field + '"]').hide()
				.filter('[data-bv-validator="' + data.validator + '"]').show();
		}).off('success.field.bv').on('success.field.bv', function(e, data)
		{
			updateValidationMessage(data.element);
			var $parent = data.element.closest('.form-group');
			$parent.removeClass('has-success');
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.onAddInvoice = function()
	{
		let self = this;
		tf.modalManager.showModal(new TF.Control.EditFieldTripTemplateInvoiceModalViewModel()).then(function(invoices)
		{
			if (!!invoices)
			{
				let invoiceGrid = self.$element.find(".billing .kendo-grid.grid-container").data("kendoGrid");

				let latestDatasource = (invoiceGrid._data || []).concat(invoices);
				invoiceGrid.setDataSource(latestDatasource);
				self.obInvoiceCount(latestDatasource.length);
			}
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.onEditInvoice = function(viewmodel, e)
	{
		let self = this;
		if ($(e.currentTarget).hasClass("enable"))
		{
			var tobeEditedItem = self.obSelectedInvoices()[0];
			tf.modalManager.showModal(new TF.Control.EditFieldTripTemplateInvoiceModalViewModel(tobeEditedItem)).then(function(editedItem)
			{
				if (editedItem)
				{
					tobeEditedItem.set("AccountName", editedItem.AccountName);
					tobeEditedItem.set("Amount", editedItem.Amount);
					tobeEditedItem.set("FieldTripAccountId", editedItem.FieldTripAccountId);

					self.obSelectedInvoices([]);
					self.obUpdateingInvoices([...new Set(self.obUpdateingInvoices().concat(tobeEditedItem))]);
				}
			});
		}
	};

	EditFieldTripTemplateRecordViewModel.prototype.onDeleteInvoice = function(viewmodel, e)
	{
		let self = this;
		if ($(e.currentTarget).hasClass("enable"))
		{
			let invoiceGrid = self.$element.find(".billing .kendo-grid.grid-container").data("kendoGrid");
			let latestDatasource = (invoiceGrid._data || []).filter(x => !self.obSelectedInvoices().includes(x));
			self.obInvoiceCount(latestDatasource.length);
			invoiceGrid.setDataSource(latestDatasource);

			self.obDeletingInvoices(self.obDeletingInvoices().concat(self.obSelectedInvoices().filter(x => !!x.Id)));
			self.obSelectedInvoices([]);
		}
	};

	EditFieldTripTemplateRecordViewModel.prototype.onAddDocument = function(viewmodel, e)
	{
		let self = this,
			documentGrid = self.$element.find(".document .kendo-grid.grid-container").data("kendoGrid");

		function getCurrentUserName(user)
		{
			if (!user.FirstName) return user.LastName;

			if (!user.LastName) return user.FirstName;

			return `${user.FirstName}, ${user.LastName}`;
		}

		self.documentClassificationPromise.then(documentClassifications =>
		{
			tf.modalManager.showModal(new TF.Control.EditFieldTripTemplateDocumentModalViewModel(undefined, documentClassifications)).then(function(documents)
			{
				if (documents && documents.length > 0)
				{
					let latestDatasource = (documentGrid._data || []).concat(documents.map(({ name, size, description, classification, Attached, ...others }) =>
					({
						FileName: name,
						FileSizeKB: (size / 1024).toFixed(2),
						DocumentClassificationName: classification.value,
						Description: description,
						LastUpdated: moment().format("MM/DD/YYYY"),
						LastUpdatedName: getCurrentUserName(tf.userEntity),
						Attached: Attached || 0,
						classification,
						...others
					})));
					documentGrid.setDataSource(latestDatasource);
					self.obDocumentCount(latestDatasource.length);

					self.obSelectedDocuments([]);
				}
			});
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.onEditDocument = function(viewmodel, e)
	{
		let self = this;
		if ($(e.currentTarget).hasClass("enable"))
		{
			self.documentClassificationPromise.then(documentClassifications =>
			{
				var tobeEditedItem = self.obSelectedDocuments()[0];
				tf.modalManager.showModal(new TF.Control.EditFieldTripTemplateDocumentModalViewModel(tobeEditedItem, documentClassifications)).then(function(editedItem)
				{
					if (editedItem)
					{
						tobeEditedItem.set("classification", editedItem.classification);
						tobeEditedItem.set("DocumentClassificationName", editedItem.classification.value);
						tobeEditedItem.set("Description", editedItem.description);

						self.obSelectedDocuments([]);
						self.obUpdatingDocuments([...new Set(self.obUpdatingDocuments().concat(tobeEditedItem))]);
					}
				});
			});
		}
	};

	EditFieldTripTemplateRecordViewModel.prototype.onDeleteDocument = function(viewmodel, e)
	{
		let self = this;
		if ($(e.currentTarget).hasClass("enable"))
		{
			let documentGrid = self.$element.find(".document .kendo-grid.grid-container").data("kendoGrid");
			let latestDatasource = (documentGrid._data || []).filter(x => !self.obSelectedDocuments().includes(x));
			self.obDocumentCount(latestDatasource.length);
			documentGrid.setDataSource(latestDatasource);

			self.obDeletingDocuments(self.obDeletingDocuments().concat(self.obSelectedDocuments().filter(x => x.Attached)));
			self.obSelectedDocuments([]);
		}
	};

	EditFieldTripTemplateRecordViewModel.prototype.getFileNameWithoutExtension = function(fullName)
	{
		var pattern = /\.{1}[a-z]{1,}$/;
		if (pattern.exec(fullName) !== null)
		{
			return (fullName.slice(0, pattern.exec(fullName).index));
		}
		else
		{
			return fullName;
		}
	};

	EditFieldTripTemplateRecordViewModel.prototype.getRecordEntity = function()
	{
		var self = this;

		var invoiceAmountType = self.$element.find(`[name="amountbasis"]:checked`).attr("value");

		return {
			Id: self.entity.Id,
			DBID: self.entity.DBID || tf.datasourceManager.databaseId,
			Name: self.obTemplateName().trim(),
			TemplateStatus: Number(self.obInactive()),
			// main
			FieldTripName: self.obFieldTripName(),
			School: self.obSelectedSchool() && self.obSelectedSchool().key,
			DistrictDepartmentId: self.isStrictAccountCodes ? self.obSelectedAccount() && self.obSelectedAccount().DepartmentId : self.obSelectedDepartment() && self.obSelectedDepartment().key,
			FieldTripActivityId: self.isStrictAccountCodes ? self.obSelectedAccount() && self.obSelectedAccount().FieldTripActivityId : self.obSelectedActivity() && self.obSelectedActivity().key,
			TravelScenarioId: self.obSelectedTravelScenario()?.key || self.entity.TravelScenarioId,

			FieldTripContact: self.obFieldTripContact(),
			ContactPhone: self.obContactPhone(),
			ContactPhoneExt: self.obContactPhoneExt(),
			ContactEmail: self.obContactEmail(),
			FieldTripEquipmentId: self.obSelectedEquipment() && self.obSelectedEquipment().key,
			FieldTripClassificationId: self.obSelectedClassification() && self.obSelectedClassification().key,
			NumberOfStudents: self.obNumofStudents(),
			NumberOfAdults: self.obNumofAdults(),
			NumberOfVehicles: self.obNumofVehicles(),
			NumberOfWheelChairs: self.obNumofWheelChairs(),
			EstimatedDistance: tf.measurementUnitConverter.convert({
				originalUnit: tf.measurementUnitConverter.getCurrentUnitOfMeasure(),
				targetUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
				value: self.obEstimatedDistance(),
			}),
			EstimatedHours: self.obEstimatedHours(),
			EstimatedCost: self.obEstimatedCost(),

			//destination
			DepartFromSchool: self.obSelectedDeparture() && self.obSelectedDeparture().key,
			DepartureNotes: self.obDepartureNote(),

			FieldTripDestinationId: self.obSelectedDestination() && self.obSelectedDestination().key,
			Destination: self.obSelectedDestination() && self.obSelectedDestination().value,
			DestinationStreet: self.obDestinationStreet(),
			DestinationCity: self.obDestinationCity(),
			DestinationState: self.obDestinationState(),
			DestinationZip: self.obDestinationZip(),

			DestinationContact: self.obDestinationContact(),
			DestinationContactTitle: self.obDestinationTitle(),
			DestinationContactPhone: self.obDestinationPhone(),
			DestinationPhoneExt: self.obDestinationPhoneExt(),
			DestinationFax: self.obDestinationFax(),
			DestinationEmail: self.obDestinationEmail(),
			DestinationNotes: self.obDestinationNote(),
			DirectionNotes: self.obDirections(),

			//#endregion

			//billing
			BillingClassificationId: self.obSelectedBillingClassification() && self.obSelectedBillingClassification().key,
			FuelConsumptionRate: tf.measurementUnitConverter.convert({
				originalUnit: tf.measurementUnitConverter.getCurrentUnitOfMeasure(),
				targetUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
				value: self.obFuelConsumptionRate(),
				isReverse: true,
			}),
			AideFixedCost: self.obAideFixedCost(),
			FixedCost: self.obTripFixedCost(),
			DriverFixedCost: self.obDriverFixedCost(),
			VehFixedCost: self.obVehFixedCost(),
			MinimumCost: self.obMinimumCost(),
			DriverRate: self.obDriverRate(),
			DriverOTRate: self.obDriverOTRate(),
			AideRate: self.obAideRate(),
			AideOTRate: self.obAideOTRate(),
			BillingNotes: self.obBillingNotes(),
			InvoiceAmountType: invoiceAmountType ? Number(invoiceAmountType) : null,

			//notes
			Notes: self.obNotes(),

			//UDF
			UserDefinedFields: self.buildUdfRecordValues()
		};
	};

	EditFieldTripTemplateRecordViewModel.prototype.save = function()
	{
		var self = this;

		return self.pageLevelViewModel.saveValidate()
			.then(function(result)
			{
				var configType = self.configType,
					isNew = self.obRecordId() === -1;

				return Promise.resolve(self.getRecordEntity()).then(function(recordEntity)
				{
					if (result && recordEntity)
					{
						var saveActionPromise = (isNew ? tf.fieldTripConfigsDataHelper.addNewConfigRecordByType(configType, recordEntity) : tf.fieldTripConfigsDataHelper.updateConfigRecordByType(configType, recordEntity));
						return saveActionPromise;
					}

					return null;
				});
			}).then(function(fieldtriptemplate)
			{
				if (!fieldtriptemplate) return false;

				return Promise.all([
					self.attachDocument(fieldtriptemplate),
					self.detachDocument(fieldtriptemplate),
					self.updateDocument(),
					self.addInvoice(fieldtriptemplate),
					self.deleteInvoice(),
					self.updateInvoice()
				]).then(() => true);
			});
	};

	EditFieldTripTemplateRecordViewModel.prototype.updateDocument = function()
	{
		let self = this;

		// new added documents will be handled in attachDocument flow
		let tobeUpdated = self.obUpdatingDocuments().filter(x => !!x.Attached);

		if (!tobeUpdated.length) return Promise.resolve();

		return Promise.all(tobeUpdated.map(x =>
		{
			return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), "Documents"), {
				paramData: {
					Id: x.Id
				},
				data: [
					{ "op": "replace", "path": "/Description", "value": x.Description },
					{ "op": "replace", "path": "/DocumentClassificationID", "value": x.classification.key }
				]
			});
		}));
	};

	EditFieldTripTemplateRecordViewModel.prototype.attachDocument = function(fieldtriptemplate)
	{
		let self = this;
		let documentGrid = self.$element.find(".document .kendo-grid.grid-container").data("kendoGrid"),
			documents = documentGrid._data.filter(x => !x.Attached);

		if (!documents.length) return Promise.resolve();

		return Promise.all(documents.map(document =>
		{
			let helper = new TF.UploadHelper({
				maxFileByteSize: TF.DetailView.UploadDocumentHelper.maxFileByteSize,
				acceptFileExtensions: TF.DetailView.UploadDocumentHelper.acceptFileExtensions,
			});

			var $fileSelector = helper.createFileSelector("document-file-selector");
			helper.init($fileSelector);

			helper.selectFile(document.originalFile);
			return helper.upload().then(value => ({ tempFileName: value, ...document }));
		})).then(function(values)
		{
			return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("document")), {
				data: values.map(d =>
				{
					return {
						MimeType: d.originalFile.type,
						FileName: d.originalFile.name,
						FileSizeKB: (d.originalFile.size / 1024).toFixed(2),
						Description: d.Description,
						TempFileName: d.tempFileName,
						Name: self.getFileNameWithoutExtension(d.originalFile.name),
						DocumentClassificationID: d.classification.key
					}
				})
			}).then(function(response)
			{
				let documentIds = response && response.Items && response.Items.map(x => x.Id) || [];

				return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "DocumentRelationships"), {
					data: documentIds.map(function(id)
					{
						return {
							DocumentID: id,
							DBID: fieldtriptemplate.DBID,
							AttachedToType: self.getDataTypeIdOfFieldTripTemplate(),
							AttachedToID: fieldtriptemplate.Id
						}
					})
				})
			});
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.detachDocument = function(fieldtriptemplate)
	{
		let self = this;

		let documentRelationshipIds = self.obDeletingDocuments().map(x => (x.DocumentRelationships.filter(y => y.AttachedToID === fieldtriptemplate.Id)[0] || {}).DocumentRelationshipID).filter(Boolean);

		if (!documentRelationshipIds.length) return Promise.resolve();

		return tf.promiseAjax.delete(pathCombine(tf.api.apiPrefixWithoutDatabase(), "DocumentRelationships"), {
			data: [... new Set(documentRelationshipIds)]
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.addInvoice = function(fieldtriptemplate)
	{
		let self = this,
			invoiceGrid = self.$element.find(".billing .kendo-grid.grid-container").data("kendoGrid"),
			newInvoices = invoiceGrid._data.filter(x => !x.Id);

		if (!newInvoices.length) return Promise.resolve();

		let dataItems = newInvoices.map(({ FieldTripAccountId, Amount }) => ({ FieldTripAccountId, Amount, FieldTripTemplateId: fieldtriptemplate.Id }));

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtripinvoicetemplates"), {
			data: dataItems
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.updateInvoice = function()
	{
		let self = this;

		// new added invoice will be handled in addInvoice flow
		let tobeUpdated = self.obUpdateingInvoices().filter(x => !!x.Id);

		if (!tobeUpdated.length) return Promise.resolve();

		return Promise.all(tobeUpdated.map(x =>
		{
			return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), "fieldtripinvoicetemplates"), {
				paramData: {
					Id: x.Id
				},
				data: [
					{ "op": "replace", "path": "/FieldTripAccountId", "value": x.FieldTripAccountId },
					{ "op": "replace", "path": "/Amount", "value": x.Amount }
				]
			});
		}));
	};

	EditFieldTripTemplateRecordViewModel.prototype.deleteInvoice = function()
	{
		let self = this,
			ids = self.obDeletingInvoices().map(({ Id }) => Id);

		if (!ids.length) return Promise.resolve();

		return tf.promiseAjax.delete(pathCombine(tf.api.apiPrefix(), "FieldTripInvoiceTemplates"), {
			paramData: {
				"@filter": `in(Id, ${ids.join()})`
			}
		});
	};

	EditFieldTripTemplateRecordViewModel.prototype.buildUdfRecordValues = function()
	{
		let self = this;
		const FIELD_TRIP_TEMPLATE_TYPE_ID = tf.dataTypeHelper.getId("fieldtriptemplate");

		return Object.keys(self.udfValueMap).map(guid =>
		{
			let udfValue = {
				"Id": self.udfValueMap[guid].definition.Id,
				"TypeId": self.udfValueMap[guid].definition.TypeId,
				"RecordValue": self.udfValueMap[guid].obValue(),
				"DataTypeId": FIELD_TRIP_TEMPLATE_TYPE_ID
			}

			let definition = self.udfValueMap[guid].definition;
			let options = self.udfValueMap[guid].options;

			if (definition.TypeId == SupportedUdfTypes.List)
			{
				let selectPickListOptionIds = [];
				if (definition.PickListMultiSelect && self.udfValueMap[guid].options.selectPickListOptionIDs)
				{
					selectPickListOptionIds = self.udfValueMap[guid].options.selectPickListOptionIDs;
				}
				else if (!definition.PickListMultiSelect && options.obSingleSelectedOption()?.ID)
				{
					selectPickListOptionIds.push(options.obSingleSelectedOption()?.ID);
				}

				selectPickListOptionIds = selectPickListOptionIds.filter(listOptionId => listOptionId != -1); // filter out NONE_VALUE
				if (selectPickListOptionIds.length == 0) // no item selected
				{
					udfValue["RecordValue"] = "";
				}
				udfValue["SelectPickListOptionIDs"] = selectPickListOptionIds;
			}
			else if (definition.TypeId == SupportedUdfTypes.Time)
			{
				if (udfValue['RecordValue'])
				{
					let momentObj = moment(udfValue['RecordValue']);

					if (!momentObj.isValid())
					{
						momentObj = moment("12/30/1899 " + udfValue['RecordValue']);
					}

					udfValue['RecordValue'] = momentObj.isValid() ? momentObj.format("HH:mm:ss") : null;
				}
			}
			else if (definition.TypeId == SupportedUdfTypes.Boolean)
			{
				let recordValue = udfValue['RecordValue'];

				if (recordValue !== NONE_VALUE)
				{
					recordValue = recordValue === (definition.TrueDisplayName || "True"); // convert to boolean value
				}
				else
				{
					recordValue = null;
				}

				udfValue['RecordValue'] = recordValue;
			}
			else if (definition.TypeId == SupportedUdfTypes.Number || definition.TypeId == SupportedUdfTypes.Currency)
			{
				let recordValue = udfValue['RecordValue'];

				if (recordValue && isNaN(recordValue)) // contains commas?
				{
					recordValue = Number(recordValue.replaceAll(',', ''));
				}

				udfValue['RecordValue'] = recordValue;
			}
			else if (definition.TypeId == SupportedUdfTypes.Image)
			{
				const options = self.udfValueMap[guid].options;
				const fileName = options.fileName ?? definition.DefaultImage;
				const imageBase64 = udfValue['RecordValue']?.replace(/^data:image\/\w+;base64,/, '');
				const imageType = TF.DetailView.DataBlockComponent.UDFImageBlock.getImageType(udfValue['RecordValue']);
				const captionValue = options.captionValue;

				// follow the logic from UDFImageBlock
				let recordValue = [
									imageBase64 ? fileName : "",
									captionValue, 
									imageType, 
									imageBase64 || "",
									imageBase64 ? "true" : "false"
								 ];

				udfValue['RecordValue'] = JSON.stringify(recordValue);
			}

			return udfValue;
		});
	}

	EditFieldTripTemplateRecordViewModel.prototype.initFieldTripUdfList = function(type)
	{
		let self = this;

		return tf.UDFDefinition.RetrieveByType("fieldtrip").then(async (response) =>
		{
			let udfs = response.Items || [];

			// cache and get DefaultImage
			udfs = await this.initImageUDFs(udfs);

			const supportedUdfTypeIds = SupportedUdfTypes.all.map(supportType => supportType.id);
			return udfs.filter(item =>
						{
							return self.udfHelper.isShowInCurrentDataSource(item) && supportedUdfTypeIds.includes(item.TypeId);
						})
				       .sort((a, b) => a.DisplayName.localeCompare(b.DisplayName));
		}
		);
	};

	EditFieldTripTemplateRecordViewModel.prototype.initImageUDFs = async function(udfs)
	{
		// cache the DefaultImage
		await this.udfHelper.getImageUDFs(udfs);

		udfs.forEach(udf => {
			if (udf.TypeId !== SupportedUdfTypes.Image)
			{
				return;
			}

			udf.DefaultImageBase64 = TF.DetailView.UserDefinedFieldHelper.CacheImage["ImageUDFCache-" + udf.DefaultImage];
		});

		return udfs;
	}

	EditFieldTripTemplateRecordViewModel.prototype.getUdfObValue = function(udfDefinition, udfValue)
	{
		let existValue = udfValue?.RecordValue ?? null;

		// get current value for existing record
		if (this.isEdit)
		{
			switch (udfDefinition.TypeId)
			{
				case SupportedUdfTypes.Boolean:
					if (existValue) // if exist record has valid value
					{
						let trueDisplayValue = udfDefinition.TrueDisplayName || "True";
						let falseDisplayValue = udfDefinition.FalseDisplayName || "False";

						existValue = (existValue.toLowerCase() == 'true') ? trueDisplayValue : falseDisplayValue;
					}
					else
					{
						existValue = NONE_VALUE; // set to None if boolean value is not selected
					}
					break;

				case SupportedUdfTypes.List:
					let displayValues = (udfValue?.SelectPickListOptionIDs || []).map(selectedId =>
					{
						const selectedPickListOption = udfDefinition.UDFPickListOptions.find(pickListOption => pickListOption.ID == selectedId);
						return selectedPickListOption?.PickList;
					});

					// update the Text value based on UDFPickListOptions
					existValue = displayValues.filter(displayValue => !!displayValue).join(',');

					if (!udfDefinition.PickListMultiSelect)
					{
						existValue = existValue || NONE_VALUE; // set to None if no item is selected
					}
					break;

				case SupportedUdfTypes.Number:
				case SupportedUdfTypes.Currency:
					if (existValue)
					{
						existValue = this.formatNumberContent(existValue, udfDefinition);
					}
					break;
				case SupportedUdfTypes.Image:
					const values = JSON.parse(existValue);
					const base64Image = values?.at(3);

					if (values && base64Image)
					{
						const metaType = values?.at(2) || `image/${udfDefinition.DefaultImage.split('.').pop()}`;
						existValue = `data:${metaType};base64,${base64Image}`;
					}
					else
					{
						existValue = null;
					}
					break;
			}
			return ko.observable(existValue);
		}

		// get default value for new record
		switch (udfDefinition.TypeId)
		{
			case SupportedUdfTypes.Boolean:
				if (udfDefinition.DefaultBoolean !== null)
				{
					// format boolean value
					existValue = udfDefinition.DefaultBoolean ? (udfDefinition.TrueDisplayName || "True") : (udfDefinition.FalseDisplayName || "False");
				}
				else
				{
					existValue = NONE_VALUE; // set to None if boolean value is not selected
				}
				break;

			case SupportedUdfTypes.Date:
				existValue = udfDefinition.DefaultDate;
				break;

			case SupportedUdfTypes.DateTime:
				existValue = udfDefinition.DefaultDatetime;
				break;

			case SupportedUdfTypes.Memo:
				existValue = udfDefinition.DefaultMemo;
				break;

			case SupportedUdfTypes.Phone:
				existValue = udfDefinition.DefaultPhoneNumber;
				break;

			case SupportedUdfTypes.Text:
				existValue = udfDefinition.DefaultText;
				break;

			case SupportedUdfTypes.Time:
				existValue = udfDefinition.DefaultTime;
				break;

			case SupportedUdfTypes.List:
				let defaultOptions = udfDefinition.UDFPickListOptions.filter(option => option.IsDefaultItem);
				existValue = defaultOptions.map(function(item) { return item.PickList; }).join(", ");

				if (!udfDefinition.PickListMultiSelect)
				{
					existValue = existValue || NONE_VALUE; // set to None if no item is selected
				}
				break;

			case SupportedUdfTypes.Email:
				existValue = udfDefinition.DefaultEmail;
				break;

			case SupportedUdfTypes.Number:
			case SupportedUdfTypes.Currency:
				existValue = udfDefinition.TypeId == SupportedUdfTypes.Number ? udfDefinition.DefaultNumeric : udfDefinition.DefaultText;

				if (existValue !== null)
				{
					existValue = this.formatNumberContent(existValue, udfDefinition);
				}

				break;
			case SupportedUdfTypes.Image:
				const metaType = udfDefinition.DefaultImage ? "image/" + udfDefinition.DefaultImage.split('.').pop() : '';
				if (udfDefinition.DefaultImageBase64)
				{
					existValue = `data:${metaType};base64,${udfDefinition.DefaultImageBase64}`;
				}
				break;
			case SupportedUdfTypes.Hyperlink:
				existValue = udfDefinition.DefaultHyperlink;
				break;
		}

		return ko.observable(existValue)
	}

	EditFieldTripTemplateRecordViewModel.prototype.getUdfOptions = function(udfDefinition, udfValue)
	{
		let options = {};
		let self = this;
		if (udfDefinition.TypeId == SupportedUdfTypes.Boolean)
		{
			options['source'] = [
				{ key: -1, value: NONE_VALUE }, // None option
				{ key: 1, value: udfDefinition.TrueDisplayName || "True" },
				{ key: 0, value: udfDefinition.FalseDisplayName || "False" },
			];
		}
		else if (udfDefinition.TypeId == SupportedUdfTypes.List)
		{
			// to store single selected option item object
			let obSingleSelectedOption = ko.observable(null);
			// to store multiple selected option item id
			let selectPickListOptionIDs = udfDefinition.SelectPickListOptionIDs;

			// if editing existing UDF value
			if (self.isEdit)
			{
				if (!udfValue)
				{
					selectPickListOptionIDs = [];
				}
				else
				{
					selectPickListOptionIDs = udfValue.SelectPickListOptionIDs;

					// init value based on exist udf(single select)
					if (!udfDefinition.PickListMultiSelect)
					{
						let [currentSelectedOption] = udfValue.UDFPickListOptions.filter(pickListOption => selectPickListOptionIDs.includes(pickListOption.ID));
						obSingleSelectedOption = ko.observable(currentSelectedOption);
					}
				}
			}
			else
			{
				let defaultOptions = udfDefinition.UDFPickListOptions.filter(option => option.IsDefaultItem);
				// init default value (single select)
				if (!udfDefinition.PickListMultiSelect)
				{
					obSingleSelectedOption = ko.observable(defaultOptions[0]);
				}

				selectPickListOptionIDs = defaultOptions.map(option => option.ID);
			}

			// compose required objects for List UDF so custom input can access
			options = {
				source: [
					{ ID: -1, IsDefaultItem: false, PickList: NONE_VALUE, UDFID: udfDefinition.Id }, // None option
					...udfDefinition.UDFPickListOptions
				],
				obSingleSelectedOption: obSingleSelectedOption,
				selectPickListOptionIDs: selectPickListOptionIDs,
				// callback method when apply value from List Mover
				updateListItems: function(selectedOptions)
				{
					var text = selectedOptions.map(function(item) { return item.PickList; }).join(", ");

					selectPickListOPtionsIDs = selectedOptions.map(function(item) { return item.ID; });

					self.udfValueMap[udfDefinition.Guid].obValue(text);
					self.udfValueMap[udfDefinition.Guid].options.selectPickListOptionIDs = selectPickListOPtionsIDs || [];
				}
			};

			options['openListMover'] = self.openListMover.bind(this, options);
		}
		else if (udfDefinition.TypeId === SupportedUdfTypes.Number || udfDefinition.TypeId === SupportedUdfTypes.Currency)
		{
			const isCommaForNumber = udfDefinition.TypeId == SupportedUdfTypes.Number && udfDefinition.IncludeCommas;
			const isCommaForCurrency = udfDefinition.TypeId == SupportedUdfTypes.Currency;

			options = {
				decimalPlaces: this.getPrecisionValue(udfDefinition),
				showCommas: isCommaForNumber || isCommaForCurrency
			};
		}
		else if (udfDefinition.TypeId === SupportedUdfTypes.Image)
		{
			const captionMaxLength = TF.DetailView.DataBlockComponent.UDFImageBlock.getDefaultCaptionMaxLength();
			const existValue = udfValue?.RecordValue && JSON.parse(udfValue.RecordValue); 
			const captionValue = existValue === undefined ? udfDefinition.DefaultCaption : existValue?.at(1);

			options = {
				editPhotoClick: self.editPhotoClick.bind(this, udfDefinition),
				removeImage: self.removeImage.bind(this, udfDefinition),
				captionValue: captionValue,
				captionChanged: (options, event) => {
					let value = $(event.target).val();
					if (value.length > captionMaxLength) {
						value = value.substring(0, captionMaxLength);
						$(event.target).val(value);
					}

					options.options.captionValue = value;

					const captionCounterLabel = `${value.length}/${captionMaxLength}`;

					const captionCounter = $(event.target).siblings('.udf-caption-counter');
					captionCounter.text(captionCounterLabel); 
				}
			}
		}

		return options;
	}

	EditFieldTripTemplateRecordViewModel.prototype.formatNumberContent = function(value, udfItem)
	{
		var decimalPlaces = this.getPrecisionValue(udfItem);
		var format = `0.${Array(decimalPlaces).join('0')}`; //e.g., '0.00'

		const isCommaForNumber = udfItem.TypeId == SupportedUdfTypes.Number && udfItem.IncludeCommas;

		// format the value if Type is Currency or IncludeCommas of Number is checked
		if (isCommaForNumber || udfItem.TypeId == SupportedUdfTypes.Currency)
		{
			var formatValue = tf.helpers.detailViewHelper.formatNumberContent(value, format, udfItem)

			if (formatValue == 'None')
			{
				return '';
			}

			return formatValue;
		}

		return Number(value).toFixed(decimalPlaces); // format number with trailing zero
	}

	EditFieldTripTemplateRecordViewModel.prototype.getPrecisionValue = function(udfDefinition)
	{
		var precisionValue = 0;

		if (udfDefinition.TypeId == SupportedUdfTypes.Number)
		{
			precisionValue = udfDefinition.NumberPrecision || 0;
		}
		else if (udfDefinition.TypeId == SupportedUdfTypes.Currency)
		{
			precisionValue = udfDefinition.MaxLength;
		}

		return precisionValue;
	}

	EditFieldTripTemplateRecordViewModel.prototype.openListMover = function(options)
	{
		var availableList = [],
			selectedList = [],
			pickListOptions = options.source.filter(pickListOption => pickListOption.ID != -1) // exclude None option

		pickListOptions
			.forEach(function(item, index)
			{
				const isItemSelected = options.selectPickListOptionIDs.includes(item.ID);

				(isItemSelected ? selectedList : availableList).push({
					text: item.PickList,
					value: index,
				});
			});

		tf.modalManager.showModal(new TF.DetailView.ListMoverFieldEditorModalViewModel({
			title: "Item",
			availableSource: availableList,
			selectedSource: selectedList,
			allowNullValue: true
		}))
			.then(function(result)
			{
				if (Array.isArray(result))
				{
					const selectedListOptions = pickListOptions.filter(function(item, index)
					{
						return (result.indexOf(index) > -1);
					});
					options.updateListItems(selectedListOptions);
				}
			});
	};

	EditFieldTripTemplateRecordViewModel.prototype.editPhotoClick = async function(udfDefinition, event)
	{
		const udfValue = this.udfValueMap[udfDefinition.Guid];
		const acceptFileExtensions = TF.DetailView.DataBlockComponent.UDFImageBlock.getAcceptFileExtensions();

		let file = this.getCurrentFile(udfDefinition);
		if (file)
		{
			await this.updateImage(file, udfValue);
			return;
		}

		var $imageInput = $('<input>', {
			type: 'file',
			name: 'imageInput',
			accept: acceptFileExtensions.map(ext => '.' + ext).join(', '),
		}).css({
			'display': 'none',
		});

		$imageInput.on('change', async (event) =>
		{
			var files = event.target.files;
			if (files.length > 0)
			{
				const file = files[0];
				await this.updateImage(file, udfValue);
			}

			$imageInput.remove();
			$imageInput = null;
		});

		$imageInput.click();
	}

	EditFieldTripTemplateRecordViewModel.prototype.onPickImage = async function(file)
	{
		if (!file)
		{
			return;
		}

		let imageBase64 = null;

		try
		{
			let cropResult = await TF.DetailView.DataBlockComponent.UDFImageBlock.cropFile(file);
			if (cropResult != null && typeof cropResult === 'object')
			{
				this.udfImageValue = cropResult;
				var currentImgUrl = cropResult.fileData;
				imageBase64 = currentImgUrl;
			}
		}
		catch (err)
		{
			console.log(err);
		}

		return imageBase64;
	}

	EditFieldTripTemplateRecordViewModel.prototype.updateImage = async function(file, udfValue)
	{
		const imageBase64 = await this.onPickImage(file);
		if (imageBase64 && imageBase64 !== udfValue.obValue())
		{
			udfValue.options.fileName = file.name;
			udfValue.obValue(imageBase64);
		}
	}

	EditFieldTripTemplateRecordViewModel.prototype.getCurrentFile = function(udfDefinition)
	{
		const options = this.udfValueMap[udfDefinition.Guid];
		const base64 = options.obValue();
		if (!base64 || !base64.startsWith('data:'))
		{
			return;
		}

		return TF.DetailView.DataBlockComponent.UDFImageBlock.convertBase64ToFile(base64);
	}	

	EditFieldTripTemplateRecordViewModel.prototype.removeImage = function(udfDefinition, options, event)
	{
		event.stopPropagation();

		var alterMessage = "Are you sure you want to delete the picture for this record?";

		return tf.promiseBootbox.yesNo(alterMessage, "Delete Picture").then((result) =>
		{
			if (result === true)
			{
				options.obValue(null);
			}
		});
	}

	EditFieldTripTemplateRecordViewModel.prototype.getUdfHtmlContent = function(udfItem)
	{
		let content = "";
		let extendCss = "";

		switch (udfItem.TypeId)
		{
			case SupportedUdfTypes.Boolean:
				content = `
				<div class="input-group">
					<div data-bind="typeahead:{source:options.source, format:function(obj){return obj.value;}, drowDownShow:true, notSort:true}">
						<!-- ko customInput:{type:"Select", value:obValue, attributes:{name: definition.DisplayName, class:"form-control"}} -->
						<!-- /ko -->
					</div>
					<div class="input-group-btn">
						<button type="button" class="btn btn-default btn-sharp">
							<span class="caret"></span>
						</button>
					</div>
				</div>			
				`;
				break;

			case SupportedUdfTypes.Date:
				content = `
					<!-- ko customInput:{type:"Date", value:obValue, attributes:{name: definition.DisplayName, class:"form-control"}} -->
					<!-- /ko -->
				`;
				break;

			case SupportedUdfTypes.DateTime:
				content = `
					<!-- ko customInput:{
						type:'DateTime',
						value:obValue,
						attributes:{name: definition.DisplayName, class:'form-control',format:'MM/DD/YYYY hh:mm A',tabindex:"4"}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.Memo:
				extendCss = 'tf-textarea tf-udf-memo';
				content = `<textarea class='form-control' data-bind='value:obValue, name: definition.DisplayName' tabindex='4'/>`;
				break;

			case SupportedUdfTypes.Number:
				content = `
					<!-- ko customInput:{
						type:'Decimal',
						value:obValue,
						attributes:{
								name: definition.DisplayName, 
								class:'form-control field-trip-template-udf-number',
								tabindex:'4',
								showCommas: options.showCommas,
								decimalPlaces: options.decimalPlaces								
						}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.Phone:
				content = `
					<!-- ko customInput:{
							type:'Phone',
							value:obValue,
							attributes:{name: definition.DisplayName, class:'form-control',maxlength:'18',tabindex:'4'}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.Text:
				content = `
					<!-- ko customInput:{
						type:'String',
						value:obValue,
						attributes:{name: definition.DisplayName, class:'form-control',maxlength:'${udfItem.MaxLength || 200}',tabindex:'4'}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.Time:
				content = `
					<!-- ko customInput:{
						type:'Time', 
						value:obValue, 
						attributes:{name: definition.DisplayName, class:'form-control',tabindex:'4'}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.List:
				if (udfItem.PickListMultiSelect)
				{
					content = `
					<div class="input-group listmover">
						<!-- ko customInput:{
							type:"String",
							value:obValue,
							attributes:{name: definition.DisplayName, class:"form-control",maxlength:"100", readonly:"true",tabindex:"4"}} -->
						<!-- /ko -->
						<button class="list-default-value-button" data-bind="click:options.openListMover"></button>
					</div>
					`;
				}
				else
				{
					content = `
					<div class="input-group dropdown">
						<div data-bind="typeahead:{source:options.source,format:function(obj){return obj.PickList;},drowDownShow:true,notSort:true,isSelec:true, selectedValue:options.obSingleSelectedOption}">
							<!-- ko customInput:{
								type:"Select",
								value:obValue,
								attributes:{name: definition.DisplayName, class:"form-control" }
							} -->
							<!-- /ko -->
						</div>
						<div class="input-group-btn">
							<button type="button" class="btn btn-default btn-sharp">
								<span class="caret"></span>
							</button>
						</div>
					</div>`
				}

				break;

			case SupportedUdfTypes.Email:
				content = `
					<!-- ko customInput:{
						type:'Email',
						value:obValue,
						attributes:{name: definition.DisplayName, class:'form-control',maxlength:'200',tabindex:'4'}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.Currency:
				content = `
				<!-- ko customInput:{
					type:'Decimal',
					value:obValue,
					attributes:{
						name: definition.DisplayName,
						class:'form-control field-trip-template-udf-currency',
						maxlength:'50',
						tabindex:'4',
						showCommas: options.showCommas,
						decimalPlaces: options.decimalPlaces
					}
				} -->
				<!-- /ko -->
				`;
				break;
			case SupportedUdfTypes.Hyperlink:
				content = `
					<!-- ko customInput:{
						type:'Url',
						value:obValue,
						attributes:{name: definition.DisplayName, class:'form-control',maxlength:'512'}
					} -->
					<!-- /ko -->`;
				break;

			case SupportedUdfTypes.Image:
				extendCss = 'image-default';
				content = `
				<div class="thumb" data-bind="click: options.editPhotoClick">
					<!--ko if:obValue-->
						<img alt="" class="normal" data-bind="attr:{src:obValue}" />
						<div class="image-remove" data-bind="click: options.removeImage"></div>
					<!--/ko-->
					<!--ko ifnot:obValue-->
						<img alt="" class="normal" src="../../global/Img/detail-screen/udf-default-image.png" />
					<!--/ko-->
					<div class="thumb-text">
						Select Image
					</div>
				</div>
				<div class="udf-caption-area">
					<input data-bind="value: options.captionValue, event:{
						input: options.captionChanged,
						focus: options.captionChanged
					}" class="udf-caption" type="text"/>
					<div class="udf-caption-counter" data-bind="text: options.captionCounterLabel"></div>
				</div>
				`;
				break;
		}

		let container = `<div class="row">
							<div class="col-xs-24">
								<div class="form-group tf-validate ${extendCss}">
									<label data-bind="text:definition.DisplayName"></label>
									${content}
								</div>
							</div>
						</div>`;
		return container;
	};
})();