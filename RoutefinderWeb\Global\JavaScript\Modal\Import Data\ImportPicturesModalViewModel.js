(function()
{
	createNamespace('TF.Modal').ImportPicturesModalViewModel = ImportPicturesModalViewModel;

	function ImportPicturesModalViewModel()
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-sm";
		self.contentTemplate('modal/import data/ImportPicturesView');
		self.buttonTemplate('modal/positivenegative');
		self.title("Picture Import");
		self.ImportPicturesViewModel = new TF.Control.ImportPicturesViewModel();
		self.data(self.ImportPicturesViewModel);
		//Disabled the Submit button in default
		self.obDisableControl(true);
		self.obPositiveButtonLabel('OK');
		self.ImportPicturesViewModel.onFileReadComplete.subscribe(function(e, files)
		{
			if (files != null)
			{
				self.obDisableControl(false);
			} else
			{
				self.obDisableControl(true);
			}
		});
	}

	ImportPicturesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ImportPicturesModalViewModel.prototype.constructor = ImportPicturesModalViewModel;

	ImportPicturesModalViewModel.prototype.positiveClick = function(e)
	{
		var self = this;
		this.ImportPicturesViewModel.uploadFile().then(function(res)
		{
			if (res)
				self.negativeClick();
		});
	}
})()