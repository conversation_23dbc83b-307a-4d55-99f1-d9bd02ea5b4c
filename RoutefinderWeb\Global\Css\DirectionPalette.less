﻿@import "RoutePanelStyle";

#setting-panel-icon {
	#icon;

	&.destination-opacity {
		background-image: url('../img/direction-setting-panel/drop-destination-opacity.png');
	}

	&.destination {
		background-image: url('../img/direction-setting-panel/drop-destination-dark.png');
	}

	&.print {
		background-image: url('../img/direction-setting-panel/print.png');

		&.disable {
			opacity: 0.3;
			cursor: default;
		}
	}

	&.print-setting {
		background-image: url('../img/direction-setting-panel/print-settings.png');
	}

	&.uturn-setting {
		background-image: url('../img/direction-setting-panel/uturn-settings.png');
	}

	&.zoom-map-to-layers {
		background-image: url('../img/Routing Map/menuicon/ZoomToBounds_Black.svg');
		background-size: 25px;
	}

	&.clear-all {
		background-image: url('../img/direction-setting-panel/Omit-Black.png');
	}

	&.open-direction-widget {
		background-image: url('../img/direction-setting-panel/Directions-Off.png');
	}

	&.settings {
		background-image: url('../img/direction-setting-panel/settings.png');
	}

	&.delete {
		background-image: url('../img/menu/Delete-Black.svg');
	}
}

.directions-tool {
	#routing-panel-tool;
	position: relative;

	.icon {
		#setting-panel-icon;

		&.destination-opacity {
			z-index: 1;
			position: relative;

			&.disable {
				cursor: default;
			}
		}
	}

	.destination-opacity-background {
		background: #000;
		position: absolute;
		height: 16px;
		width: 14px;
		top: 7px;
		left: 10px;

		&.drop-mode-open {
			background-color: #4A77EC;
		}

		&.disable {
			opacity: 0.3;
			cursor: default;
		}
	}

	.vertical-line {
		#vertical-line;
	}

	.print-setting-group {
		position: relative;
		#Popover-Menu;

		.menu {
			.menu-title {
				margin-top: 0;
				margin-left: 30px;
			}
		}
	}

	.uturn-setting-group {
		position: relative;
		#Popover-Menu;

		.menu {
			width: 320px;

			.menu-title {
				margin-top: 0;
				margin-left: 30px;
			}
		}
	}

	.active {
		.icon {
			background-color: #4b4b4b;

			&.print-setting {
				background-image: url('../img/direction-setting-panel/print-settings-white.png');
			}

			&.uturn-setting {
				background-image: url('../img/direction-setting-panel/uturn-settings-white.png');
			}

			&.settings {
				background-image: url('../img/direction-setting-panel/Settings-White.png');
			}
		}

		.menu {
			display: block;
		}
	}
}

#direction-circle {
	height: 20px;
	width: 20px;
	min-width: 20px;
	line-height: 20px;
	border-radius: 12px;
	vertical-align: middle;
	text-align: center;
	background-color: #4A77EC;
	color: #fff;
}

.directions-destination-circle {
	#direction-circle;
}

.directions-destination-circle-add-button {
	#direction-circle;
	margin-left: 14px;
	font-size: 20px;
	cursor: pointer;
}

#roundtrip-circle {
	color: transparent;
	background-color: white;
	background-image: url('../img/direction-setting-panel/Directions-Bullseye.png');
}

.directions-roundtrip-circle {
	#roundtrip-circle;
	background-size: 20px 20px;
	max-height: 20px;
	max-width: 20px;
}


#through-point-circle {
	width: 9px;
	min-width: 9px;
	height: 9px;
	border-radius: 4.5px;
}

.through-point-circle {
	#through-point-circle;
	background-color: #D4E5FF;
	border-style: solid;
	border-width: 1px;
	border-color: #599BFF;
}

.directions-destination-set-as-through-point {
	#through-point-circle;
	background-color: #BFBFBF;
	display: none;
	cursor: pointer;
}

.directions-content {
	padding: 0;
	overflow: hidden;
}

.directions-stops {
	overflow-y: auto;
	min-width: 350px;
	padding-top: 15px;
	padding-left: 15px;
	padding-right: 15px;

	.directions-items {
		.directions-item .directions-destination-item input {
			font-family: Arial, Helvetica, sans-serif;
			font-size: 12px;
			height: 32px;
			box-sizing: border-box;
			border: 1px solid;
		}
	}
}

.directions-item .required-message {
	margin-left: 39px;
	margin-top: -5px;
	color: red;
}

.directions-item.ui-draggable.clone {
	position: fixed !important;
	padding-top: 5px;
}

.directions-stops.minDisplay {
	.directions-items {
		.directions-item {
			display: none;

			.direction-through-point-items {
				display: none;
			}

			&:first-child,
			&:last-child {
				display: block;
				pointer-events: none;

				input {
					background-color: #ebebe4;
					border: 1px solid #ccc;
				}
			}

			.directions-destination-sort {
				visibility: hidden;
			}
		}
	}

	.directions-check-box-group,
	.directions-item-footer-wrapper {
		display: none;
	}

	.collapseExpandController .icon {
		background-image: url('../img/direction-setting-panel/arrow_down_black.png');
	}
}

.collapseExpandController {
	height: 20px;

	.icon {
		margin: 0 auto;
		width: 50px;
		height: 100%;
		cursor: pointer;
		opacity: 0.3;
		text-align: center;
		background-position: 0 -4px;
		background-size: 50px 30px;
		background-repeat: no-repeat;
		background-image: url('../img/direction-setting-panel/arrow_up_black.png');
	}
}

.directions-check-box-group {
	float: left;
	margin-left: 35px;
	display: flex;
	align-items: center;
	height: 24px;
	line-height: 24px;

	>input[type="checkbox"] {
		margin: 0;
		margin-right: 5px;
		margin-left: 5px;
	}
}

.directions-item-active-template(@before-margin-top: -12px;
	@after-margin-top: -12px;

	@top: 5px) {
	border-top: 1px #666 solid;

	&:before {
		margin-top: @before-margin-top;
		top: @top;
		content: " ";
		height: 0;
		width: 0;
		border: 7px solid #000;
		border-color: transparent;
		position: absolute;
		border-left-color: #666;
	}

	&:after {
		margin-top: @after-margin-top;
		top: @top;
		content: " ";
		height: 0;
		width: 0;
		border: 7px solid #000;
		border-color: transparent;
		position: absolute;
		border-right-color: #666;
		margin-left: -13px;
		right: 0;
	}
}

@direction-item-padding-top: 5px;

.padding-top-5 {
	padding-top: @direction-item-padding-top;
}

.directions-item-footer.active {
	.directions-item-active-template;
	position: relative;
}

.directions-items {
	@direction-item-inner-margin-right: 4px;
	@direction-item-margin-bottom: 10px;

	.directions-item {
		margin-bottom: @direction-item-margin-bottom;

		.directions-destination-item.active {
			.directions-item-active-template(-17px;
				-17px;
				9px);
			position: relative;
		}
	}

	.directions-destination-item {
		display: flex;
		align-items: center;
		margin-bottom: @direction-item-margin-bottom;
		padding-top: @direction-item-padding-top;

		&>*:not(:last-child) {
			margin-right: @direction-item-inner-margin-right;
		}
	}

	.directions-destination-item.ui-state-hover {
		border-top: 10px blue solid;
	}

	.direction-through-point-content {
		padding-left: 40px;
	}

	.direction-through-point-item {
		display: flex;
		align-items: baseline;
		margin-bottom: @direction-item-margin-bottom;

		&>* {
			margin-right: @direction-item-inner-margin-right;
		}

		;
	}
}

.directions-destination-sort {
	height: 24px;
	width: 10px;
	min-width: 10px;
	background-repeat: no-repeat;
	background-position-y: center;
	background-image: url('../img/direction-setting-panel/directions-drag-handle.png');
	cursor: pointer;
	opacity: .3;
}

.directions-destination-remove {
	background-repeat: no-repeat;
	height: 24px;
	width: 24px;
	background-position: 2px 4px;
	background-image: url('../img/direction-setting-panel/directions-remove.png');
	cursor: pointer;
	opacity: .3;
	display: none;
}

.directions-destination-item:hover {

	.directions-destination-set-as-through-point,
	.directions-destination-remove {
		display: initial;
	}
}

.through-point-address {
	line-height: 17px;
	color: #666666;
	font-size: 9px;
}

.through-point-btn-group {
	min-width: 150px;
}

.through-point-link-btn {
	font-size: 9px;
	color: #4A77EC;
}

// .middle-destination-color {
// 	background-color: #4A77EC;
// }
.first-destination-color {
	background-color: #009900;
}

.last-destination-color {
	background-color: #CC0000;
}

.directions-item .directions-destination-search-wrapper {
	display: flex; // flex: 1;
	// width: 200px;
}

.directions-destination-button-group {
	width: 32px;
	display: flex;
	align-items: center;
}

.directions-destination-address-input {
	width: calc(~'100% - 25px - 5px - 32px');

	&>input {
		width: 100%;
		padding: 0 12px;
	}
}

.directions-item {
	.directions-destination-search-wrapper {
		.arcgis-search-without-btn-template(~'calc(100% - 15px - 25px - 5px - 32px)');
	}
}

.directions-info {
	width: 100%;
	height: auto;
	background-color: #fff;
	opacity: 1;
	font-family: Arial;
	box-sizing: border-box;

	.summary {
		position: relative;
		display: block;
		height: 90px;
		border-top: 1px solid #ddd;
		background-color: #f2f2f2;

		&>div {
			display: inline-block;
			float: left;
			width: 50%;
			padding-top: 15px;
			text-align: center;

			.content {
				height: 35px;

				.section,
				.section>div {
					display: inline-block;

					.value,
					.unit {
						color: #f60;
						float: left;
						margin-right: 3px;
					}

					.value {
						line-height: 35px;
						font-size: 18pt;
					}

					.unit {
						line-height: 40px;
						font-size: 11pt;
					}
				}
			}

			.description {
				height: 50%;
				color: #666;
				font-weight: bold;
				font-size: 11pt;
			}
		}
	}

	.directions-details {
		position: relative;
		width: 100%;
		height: calc(~'100% - 100px');
		padding-top: 20px;
		padding-left: 28px;
		padding-right: 5px;

		.directions-elementList {
			.directions-element-stop {

				.distance,
				.time {
					display: none;
				}
			}

			.directions-element:first-child {
				.symbol.esriDMTDepart {
					background-color: #090;
				}

				.symbol.directions-roundtrip-detail {
					#roundtrip-circle;
					background-size: 24px 24px;
					top: 0px;
					background-position-x: 0px;
				}

				.vertical-line {
					top: 12px;
				}
			}

			.directions-element:last-child {
				.symbol.esriDMTStop {
					background-color: #c00;
				}

				.symbol.directions-roundtrip-detail {
					#roundtrip-circle;
					background-size: 24px 24px;
					top: 0px;
					background-position-x: 0px;
				}

				.vertical-line {
					bottom: calc(~'100% - 12px');
				}
			}

			.directions-element.throughPoint {
				padding-top: 0;
				padding-bottom: 0;

				.symbol.throughPoint {
					top: 3px;
					left: 38px;
				}

				.text {
					margin-left: 45px;

					.instruction {
						font-size: 10pt;
					}
				}
			}

			.directions-element {
				position: relative;
				display: block;
				min-height: 30px;
				padding: 8px;

				.symbol {
					position: absolute;
					top: 6px;
					z-index: 2;
					width: 24px;
					height: 24px;
					line-height: 24px;
					border-radius: 12px;
					background-repeat: no-repeat;
					background-size: 15px;
					background-position: 5px;
					background-color: #ccc;

					&.esriDMTStop,
					&.esriDMTDepart {
						background-color: #4a77ec;
						color: #fff;
						font-size: 15px;
						text-align: center;
						font-weight: bold;
					}

					&.esriDMTUnknown {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTUnknown.png');
					}

					&.esriDMTStraight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTStraight.png');
					}

					&.esriDMTBearLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTBearLeft.png');
					}

					&.esriDMTBearRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTBearRight.png');
					}

					&.esriDMTTurnLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTurnLeft.png');
					}

					&.esriDMTTurnRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTurnRight.png');
					}

					&.esriDMTSharpLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTSharpLeft.png');
					}

					&.esriDMTSharpRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTSharpRight.png');
					}

					&.esriDMTRampLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTRampLeft.png');
					}

					&.esriDMTRampRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTRampRight.png');
					}

					&.esriDMTTurnRightLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTurnRightLeft.png');
					}

					&.esriDMTTurnLeftLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTurnLeftLeft.png');
					}

					&.esriDMTTurnLeftRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTurnLeftRight.png');
					}

					&.esriDMTTurnRightRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTurnRightRight.png');
					}

					&.esriDMTUTurn {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTUTurn.png');
					}

					&.esriDMTFerry {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTFerry.png');
					}

					&.esriDMTRoundabout {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTRoundabout.png');
					}

					&.esriDMTHighwayMerge {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTHighwayMerge.png');
					}

					&.esriDMTHighwayExit {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTHighwayExit.png');
					}

					&.esriDMTHighwayChange {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTHighwayChange.png');
					}

					&.esriDMTForkCenter {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTForkCenter.png');
					}

					&.esriDMTForkLeft {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTForkLeft.png');
					}

					&.esriDMTForkRight {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTForkRight.png');
					}

					&.esriDMTTripItem {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTTripItem.png');
					}

					&.esriDMTEndOfFerry {
						background-image: url('../Img/Routing Map/esriDirectionTypes/esriDMTEndOfFerry.png');
					}

					&.railroadStop {
						background-image: url('../Img/Routing Map/esriDirectionTypes/railroadStop.png');
					}

					&.throughPoint {
						background-color: #D5E5FE;
						border: 2px solid #6EA3FD;
						width: 12px;
						height: 12px;
						top: 12px;
						left: 14px;
					}
				}

				.text {
					margin-left: 30px;
					max-width: calc(~'100% - 30px');

					.instruction {
						width: calc(~'100% - 85px');
						line-height: 20px;
						color: #666;
						font-size: 13px;
					}

					.distance,
					.time {
						font-size: 12px;
						color: #999;
					}

					.time {
						position: absolute;
						top: 11px;
						right: 10px;
						width: 80px;
						text-align: right;
					}
				}

				.vertical-line {
					position: absolute;
					left: 19px;
					border-right: 2px solid #ccc;
					width: 1px;
					top: 0;
					bottom: 0;
				}

				.edit-trip {
					position: absolute;
					width: 23px;
					height: 23px;
					left: -28px;
					top: 8px;
					background-image: url('../Img/Routing Map/edit-property.svg');
					background-repeat: no-repeat;
					cursor: pointer;
					background-size: contain;

					&.disable {
						opacity: 0.3;

						cursor: default;
					}
				}

				.edit-area.edit-direction {
					display: none;
					position: relative;
					left: 31px;
					width: 97%;
					line-height: 30px;
					padding: 10px;
					margin: 15px 0 0 0;
					font-size: 13px;
					border: 1px solid #ccc;
					white-space: pre-line;
					z-index: 3;
					outline: none;
					overflow: hidden;

					&.edit-show {
						display: block;
					}
				}

				.custom-text {
					width: 97%;
					line-height: 30px;
					margin-top: -8px;
					margin-bottom: -8px;
					font-size: 13px;
					padding: 0px;
					color: transparent;
					text-shadow: 0 0 0 #666;
					white-space: pre-line;
					border: 0px;
					outline: none;
					overflow: hidden;

					&:disabled {
						background: transparent;
					}
				}

			}
		}

		.warning {
			display: none;
			color: #666;
			font-size: 13px;
			text-align: center;
			height: 45px;
		}
	}
}

.directions-items {
	.esri-search {
		width: 100%;
	}

	.esri-search__submit-button,
	.esri-search__clear-button {
		display: none;
	}

}