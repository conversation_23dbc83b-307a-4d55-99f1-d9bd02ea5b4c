﻿(function()
{
	createNamespace('TF.Modal').ModifyGeoregionTypeModalViewModel = ModifyGeoregionTypeModalViewModel;

	function ModifyGeoregionTypeModalViewModel(georegionTypeId, onSave)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.onSave = onSave;
		this.sizeCss = "modal-dialog-lg";
		this.contentTemplate('modal/modifygeoregiontypecontrol');
		this.buttonTemplate('modal/positivenegative');
		this.modifyGeoregionTypeViewModel = new TF.Control.ModifyGeoregionTypeViewModel(georegionTypeId);
		this.data(this.modifyGeoregionTypeViewModel);
		// this.containerLoaded.subscribe(this.placeInCenter, this);

		var viewTitle;

		if (georegionTypeId)
		{
			viewTitle = "Edit " + tf.applicationTerm.getApplicationTermSingularByName("Geo Region") + " Type";
		}
		else
		{
			viewTitle = "Add " + tf.applicationTerm.getApplicationTermSingularByName("Geo Region") + " Type";
			this.buttonTemplate('modal/positivenegativeextend');
		}

		this.title(viewTitle);
	}
	ModifyGeoregionTypeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ModifyGeoregionTypeModalViewModel.prototype.constructor = ModifyGeoregionTypeModalViewModel;

	ModifyGeoregionTypeModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.modifyGeoregionTypeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ModifyGeoregionTypeModalViewModel.prototype.saveAndNewClick = function()
	{
		this.modifyGeoregionTypeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.modifyGeoregionTypeViewModel.obEntityDataModel(new TF.DataModel.GeoregionTypeDataModel());
				this.modifyGeoregionTypeViewModel.obEntityDataModel().apiIsDirty(false);
				this.newDataList.push(result);
				if ($("input[name=name]") && $("input[name=name]").length > 0)
				{
					$("input[name=name]").focus();
				}
				this.onSave && this.onSave();
			}
		}.bind(this));
	};

	ModifyGeoregionTypeModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.modifyGeoregionTypeViewModel.tryClose().then(function(result)
		{
			if (result != null)
			{
				this.negativeClose();
			}
		}.bind(this));
	};

	ModifyGeoregionTypeModalViewModel.prototype.dispose = function()
	{
		this.modifyGeoregionTypeViewModel.dispose();
	};

	// ModifyGeoregionTypeModalViewModel.prototype.placeInCenter = function()
	// {
	// 	this.modifyGeoregionTypeViewModel.placeInCenter();
	// };

})();
