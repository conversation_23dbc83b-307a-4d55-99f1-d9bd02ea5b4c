﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.MailingPostalCodeDataModel = function(mailingPostalCodeEntity)
	{
		namespace.BaseDataModel.call(this, mailingPostalCodeEntity);
	}

	namespace.MailingPostalCodeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.MailingPostalCodeDataModel.prototype.constructor = namespace.MailingPostalCodeDataModel;

	namespace.MailingPostalCodeDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Postal", default: "" }
	];
})();