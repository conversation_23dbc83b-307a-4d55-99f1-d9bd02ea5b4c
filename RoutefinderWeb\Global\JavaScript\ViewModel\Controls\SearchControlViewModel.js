﻿(function()
{
	createNamespace("TF.Control").SearchControlViewModel = SearchControlViewModel;

	/**
	 * Constructor
	 * @returns {void}
	 */
	function SearchControlViewModel()
	{
		var self = this;

		self.$element = null;
		self.$searchBtn = null;
		self.$searchToolContainer = null;
		self.$searchText = null;
		self.obIsActive = ko.observable(false);
		self.obInputTextHint = ko.observable("");
		self.obIsLoading = ko.observable(false);
		self.searchTextFocused = false;
		self.clearFocusTimeout = null;
		self.deferSearchTimeout = null;
		self.obHasSearchBeenConducted = ko.observable(false);
		self.elementContentHelper = new TF.Helper.ElementContentHelper();
		self.isMouseDownInSearchZone = false;
		self.requireScrollTopReset = false;
		self.pendingSearch = { searchText: "", dataType: "" };
		self.searchResult = [];
		self.allResultsCount = 0;
		self.obSearchKeywords = ko.computed(function()
		{
			var searchText = self.obInputTextHint().replace(/\\/g, "\\\\");

			return self.splitKeywords(searchText);
		}, self);

		self.maxHeight = 0;
		self.topDelta = 0;
		self.singleCardHeight = 61;
		self.sectionHeight = 38;
		self.resultHeaderHeight = 25;
		self.$virtualContainer = null;
		self.$virtualContent = null;
		self.currentItems = [];

		//Types
		self.allTypes = null;
		self.updateAllDataTypes();
		self.obAllTypes = ko.observable(self.allTypes);
		self.obSelectType = ko.observable(self.allTypes[0]);

		self.defaultCardStyle = {};
		self.obSuggestedResult = ko.observable([]);
		self.recentSearches = ["Recent Search 1", "Recent Search 2", "Recent Search 3"];
		self.obRecentSearches = ko.observableArray(self.recentSearches);
		self.obRecentlyViewed = ko.observableArray([]);
		self.obAllResultsCount = ko.observable(0);
		self.obSingleResultCount = ko.observable(0);
		self.obChangeDataSourceShow = ko.observable(false);
		self.obNotShowRecentSearch = ko.observable(self.getRecentSearchesCount() === 0);

		//Events
		self.toggleSearchControl = self.toggleSearchControl.bind(self);
		self.openTypesMenuClick = self.openTypesMenuClick.bind(self);
		self.clearSearchClick = self.clearSearchClick.bind(self);
		self.selectTypeClick = self.selectTypeClick.bind(self);
		self.inputSearchText = self.inputSearchText.bind(self);
		self.viewInGridClick = self.viewInGridClick.bind(self);
		self.suggestedResultClick = self.suggestedResultClick.bind(self);
		self.recentSearchRecordClick = self.recentSearchRecordClick.bind(self);
		self.inputKeyDown = self.inputKeyDown.bind(self);
		self.mouseOnSection = self.mouseOnSection.bind(self);
		self.showAllClick = self.showAllClick.bind(self);

		self.onSearchButtonClickEvent = new TF.Events.Event();
		self.onSearchStatusToggle = new TF.Events.Event();
		self.onNavComplete = new TF.Events.Event();
		self.onClearRecentSearchEvent = new TF.Events.Event();
		self.onClearRecentSearchEvent.subscribe(function()
		{
			self.obRecentSearches([]);
			self.obRecentlyViewed([]);
		});
		self.onSearchSettingsChangedEvent = new TF.Events.Event();
		self.onSearchSettingsChangedEvent.subscribe(self.searchSettingsChanged.bind(self));

		self.maxFontSizeForSearchText = 16;
		self.minFontSizeForSearchText = 14;
		function updatePlaceHolder()
		{
			var dataSourceName = tf.datasourceManager.databaseName || "",
				text = "Search " + dataSourceName + "...";
			if (self.searchBoxPlaceHoderText)
			{
				self.searchBoxPlaceHoderText(text);

			} else
			{
				self.searchBoxPlaceHoderText = ko.observable(text);
			}
		}

		updatePlaceHolder();
		tf.datasourceManager.datasourceChanged.subscribe(function()
		{
			updatePlaceHolder();
		});

		self.obIsActive.subscribe(function(data)
		{
			self.onSearchStatusToggle.notify(data);
		});

		self.lastSearchText = null;
		self.map = null;

		self.RecentViewedDefault = 5;
		self.inputKeyDownSearching = false;
		self.inputTextSearching = false;
	}

	SearchControlViewModel.prototype = Object.create(TF.Control.SearchControlViewModel.prototype);
	SearchControlViewModel.prototype.constructor = SearchControlViewModel;

	/**
	 * Initialize.
	 * @param {object} current view model
	 * @param {dom} element
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.init = function(model, element)
	{
		//return;
		var self = this;
		self.initElement(element);
		self.reloadRecentSearches();
		tf.map.usingArcGISComplete.subscribe(() =>
		{
			self.createMap();
		});

		self.bindCurrentDocumentChangeEvent();
	};

	SearchControlViewModel.prototype.createMap = function()
	{
		var map = new tf.map.ArcGIS.Map();
		var view = new tf.map.ArcGIS.MapView({
			container: $("<div></div>")[0],
			map: map,
			spatialReference: {
				wkid: 102100
			}
		});
		map.mapView = view;
		var ext = TF.createDefaultMapExtent();
		view.when().then(function()
		{
			view.extent = ext;
		});
		this.map = map;
	};

	/**
	 * Initialize the search elements.
	 * @param {type} element
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.initElement = function(element)
	{
		var self = this;
		self.$element = $(element);
		self.$searchBtn = self.$element.find(".search-btn");
		self.$searchHeader = self.$element.find(".search-header");
		self.$searchText = self.$searchHeader.find(".search-text");
		self.$searchControlRow = self.$element.find(".search-control-row");
		self.$searchTypeSelector = self.$searchControlRow.find(".type-selector");
		self.$searchTypesMenu = self.$searchTypeSelector.find(".dropdown-menu");
		self.$searchContent = self.$element.find(".search-content");
		self.$searchResult = self.$searchContent.find(".search-result");

		self.$virtualContainer = self.$searchResult.find(".virtual-container");
		self.$virtualContent = self.$searchResult.find(".virtual-content");

		if (TF.isPhoneDevice)
		{
			self.$searchText.addClass("mobile");
		}
		self.$searchResult.on("scroll", function()
		{
			if (self.currentItems.length > 0 && self.currentItems[0].cards.length > 0)
			{
				var scrollHeight = self.$searchResult[0].scrollHeight, height = self.$searchResult.height(),
					scrollTop = self.$searchResult.scrollTop();
				if (scrollHeight > (height + scrollTop))
				{
					self.setCurrentSearchItem();
					self.userInputChanged();
				}
			}
			var sections = self.$searchResult.find(".section");
			if (sections.length === 1)
			{
				if (!sections.hasClass("fixed"))
				{
					sections.addClass("fixed");
				}
			}
			else if (sections.length > 1)
			{
				sections.removeClass("fixed");
				sections.css({ top: 0 });
				var $section, cardsHeight, sectionheight,
					cardsSupplementHeight = 10, startDistance = 10,
					scrollTop = self.$searchResult.scrollTop();
				for (var i = 0; i < sections.length; i++)
				{
					$section = $(sections[i]);
					cardsHeight = $(sections[i]).next().height() + cardsSupplementHeight;
					if (scrollTop > cardsHeight)
					{
						scrollTop -= cardsHeight;
						sectionheight = $section.outerHeight();
						if (scrollTop > sectionheight)
						{
							scrollTop -= sectionheight;
						}
						else
						{
							if (scrollTop > startDistance && i !== sections.length - 1)
							{
								$(sections[i + 1]).addClass("fixed");
								$(sections[i + 1]).css({ top: sectionheight - scrollTop });
								$section.css({ top: startDistance - scrollTop });
							}
							$section.addClass("fixed");
							break;
						}
					}
					else
					{
						$section.addClass("fixed");
						break;
					}
				}
			}
		});

		$("body").on("mousedown", function(e)
		{
			if (self.keepSearchActive || !self.isNavPanelExpand())
			{
				return;
			}
			var $target = $(e.target);
			if (self.$searchTypeSelector.find(e.target).length === 0)
			{
				self.$searchTypesMenu.hide();
			}

			if ($target.closest(".navigation-quick-search.active, .toggle-button, .navigation-header .item-logo").length <= 0
				&& !$target.hasClass("search-text"))
			{
				self.isMouseDownInSearchZone = false;
				if (self.$searchText.val().trim() === "" && self.obIsActive())
				{
					self.toggleSearchControl();
				}
				else
				{
					self.$searchText.blur();
				}
			}
			else
			{
				self.isMouseDownInSearchZone = true;
			}
		});

		$("body").on("mouseup", function(e)
		{
			self.isMouseDownInSearchZone = false;
		});

		$(window).on("resize", function()
		{
			if (self.currentItems.length > 0 && self.currentItems[0].cards.length > 0)
			{
				self.maxHeight = parseInt(self.$searchResult.css("maxHeight")) - parseInt(self.$searchResult.css("paddingTop")) - parseInt(self.$searchResult.css("paddingBottom"));
				self.setCurrentSearchItem();
				self.userInputChanged();
			}
		});

		self.$element.on("click contextmenu", function(e)
		{
			if (!self.isNavPanelExpand()) { return; }

			var $e = $(e.target);
			if ($e.closest(".type-selector").length <= 0)
			{
				if ($e.closest(".search-btn").length <= 0 &&
					($e.closest(".clear-btn").length <= 0 || self.$searchText.val().trim() !== "") &&
					($e.closest(".navigation-menu").length <= 0))
				{
					self.$searchText.focus();
				}
				if ($(e.target).closest(".search-text").length <= 0)
				{
					self.setSearchInputCursor();
				}
				self.searchTextFocused = true;
			}
			else
			{
				self.$searchText.blur();
				self.searchTextFocused = false;
			}

			clearTimeout(self.clearFocusTimeout);
		});

		self.$searchText.on("focus", function(e)
		{
			self.searchTextFocused = true;
			if ($(e.target).closest(".navigation-quick-search.focus").length <= 0)
			{
				self.$element.addClass("active");
				self.obIsActive(true);
			}
			self.checkIfToResetScrollTop();
			self.userInputChanged();
			self.getRecentlyViewed();
		});

		self.$searchText.on("blur", function(evt)
		{
			if (self.keepSearchActive)
			{
				return;
			}
			if (!self.isMouseDownInSearchZone && !self.loseFocusOnWindow)
			{
				self.clearFocusTimeout = setTimeout(function()
				{
					if (self.obIsActive() && self.$searchControlRow.css("display") === "none")
					{
						self.toggleSearchControl();
					}
					self.searchTextFocused = false;

					self.loseFocusOnWindow = true;
					self.$searchText.blur();
					self.loseFocusOnWindow = false;
				}, 200);
			}
			evt.stopPropagation();
		});
		self.$searchText.on("keydown", self.inputKeyDown.bind(self));
		self.$searchText.on("keyup", self.inputKeyUp.bind(self));
		self.$searchText.on("input", self.inputSearchText.bind(self));
	};

	SearchControlViewModel.prototype.isNavPanelExpand = function()
	{
		return $(".navigation-menu").hasClass("expand");
	};

	SearchControlViewModel.prototype.setSearchInputCursor = function()
	{
		var self = this;
		if (self.$searchText[0].selectionStart !== undefined &&
			self.$searchText[0].selectionStart === self.$searchText[0].selectionEnd)
		{
			self.$searchText[0].selectionStart = self.$searchText.val().length;
			self.$searchText[0].selectionEnd = self.$searchText.val().length;
		}
	};

	SearchControlViewModel.prototype.onSearchIconClick = function(model, e)
	{
		e.stopPropagation();

		var self = this;
		self.onSearchButtonClickEvent.notify(e);
		clearTimeout(self.clearFocusTimeout);
	};

	SearchControlViewModel.prototype.getRecentSearchesCount = function()
	{
		var self = this, searchSettings = tf.storageManager.get("search.configurations");

		if (!searchSettings || !searchSettings.hasOwnProperty("RecentSearchNumber") || searchSettings.RecentSearchNumber == null)
		{
			return 5;
		}

		return searchSettings.RecentSearchNumber;
	};

	SearchControlViewModel.prototype.getRecentSearchViewedCount = function()
	{
		var searchSettings = tf.storageManager.get("search.configurations");
		if (!searchSettings || !searchSettings.hasOwnProperty("RecentSearchResultNumber") || searchSettings.RecentSearchResultNumber == null)
		{
			return this.RecentViewedDefault;
		}

		return searchSettings.RecentSearchResultNumber;
	};

	/**
	 * Get all recent search history.
	 * @returns {type}
	 */
	SearchControlViewModel.prototype.reloadRecentSearches = function()
	{
		var self = this;
		return self.getUserRecentSearches().then(function(response)
		{
			if (response)
			{
				response = response.filter(function(record)
				{
					return self.allTypes.some(function(type)
					{
						var recordType = record.type == 18 ? "all" : self.getDataTypeKeyById(record.type);
						return type.value === recordType && type.permission;
					});
				});
				self.obRecentSearches(response.slice(0, self.getRecentSearchesCount()));
			}
			return Promise.resolve();
		});
	};

	/**
	 * Show/Hide the search toolbar.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.toggleSearchControl = function(e)
	{
		var self = this;

		if (!self.obIsActive())
		{
			self.obIsActive(true);
			self.expandSearch();
		}
		else
		{
			self.collapseSearch();
		}
	};

	/**
	 * Expand the Search widget.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.expandSearch = function()
	{
		var self = this;
		self.$element.addClass("active");

		// Set the width and then remove it to solve a chrome display issue.
		// Without it, the icon's position would have a slight movement when search is activated.
		self.$element.css("width", "64px");
		self.$searchText.focus();
		self.$element.css("width", "");

		self.setSearchInputCursor();
		self.checkIfToResetScrollTop();
		self.getRecentlyViewed();
	};

	/**
	 * Collapse the Search widget.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.collapseSearch = function()
	{
		var self = this;
		self.$element.removeClass("active");
		self.obIsActive(false);
	};

	/**
	 * Open Types menu.
	 * @param {object} model the knockout model.
	 * @param {object} e the dom element.
	 */
	SearchControlViewModel.prototype.openTypesMenuClick = function(model, e)
	{
		var self = this, menu = $(e.currentTarget).find(".dropdown-menu");

		if (self.searchTextFocused)
		{
			clearTimeout(self.clearFocusTimeout);
			self.setSearchInputCursor();
		}
		//ga('send', 'event', 'Action', 'Search Columns');
		menu.show();
	};

	/**
	 * The click event handler for clearSearch button.
	 * @param {object} model the knockout model.
	 * @param {event} e the dom element.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.clearSearchClick = function(model, e)
	{
		if ($(".menu-opened").length === 0)
		{
			this.clearText();
			e.stopPropagation();
		}
	};

	/**
	 * Clear the text when there is any, collapse the search when there is no text.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.clearText = function()
	{
		var self = this, currentText = self.$searchText.val().trim();
		if (currentText === "")
		{
			self.obInputTextHint(currentText);
			self.collapseSearch();
		}
		else
		{
			self.clearTextWithValue();
		}
	};

	SearchControlViewModel.prototype.clearTextWithValue = function()
	{
		var self = this;
		self.obIsLoading(false);
		clearTimeout(self.deferSearchTimeout);
		self.$searchText.val("");
		self.obInputTextHint("");
		self.obHasSearchBeenConducted(false);
		self.obSuggestedResult([]);
		self.resetVirtualContainer();
		self.$searchText.focus();
		self.setSearchInputCursor();
		clearTimeout(self.clearFocusTimeout);
	};

	/**
	 * Change type for search.
	 * @param {object} model the knockout model.
	 * @param {object} e the dom element.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.selectTypeClick = function(model, e)
	{
		var self = this,
			text = self.$searchText.val().trim(),
			typeChanged = self.obSelectType().value !== model.value;

		self.switchDataType(model);
		self.$searchTypesMenu.hide();
		if (typeChanged && text !== "")
		{
			self.search(text);
		}

		if (self.searchTextFocused)
		{
			clearTimeout(self.clearFocusTimeout);
		}
		self.$searchText.focus();
		self.setSearchInputCursor();

		if (e)
		{
			e.stopPropagation();
		}
	};

	/**
	 * Switch to specified data type and change the sequence in the dropdown menu.
	 * @param {object} selectedType The datat type that the user selects.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.switchDataType = function(selectedType)
	{
		var self = this,
			types = $.grep(self.allTypes, function(type)
			{
				return type.value !== selectedType.value;
			});

		types.unshift(selectedType);
		self.obSelectType(selectedType);
		self.obAllTypes(types);
	};

	/**
	 * Find the data type by its value.
	 * @param {string} value The data type value.
	 * @returns {object} value The data type that matches the value.
	 */
	SearchControlViewModel.prototype.findDataTypeByValue = function(value)
	{
		var idx, selectedType, typeList = this.obAllTypes();

		for (idx = 0; idx < typeList.length; idx++)
		{
			selectedType = typeList[idx];
			if (selectedType.value === value)
			{
				return selectedType;
			}
		}
	};

	/**
	 * Fired after inputing some characters.
	 * @param {object} model the knockout model.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.inputSearchText = function()
	{
		var self = this,
			text = self.$searchText.val(),
			exp = /[^0-9a-zA-Z\.\,\:\-\+\@\[\]\*\'\&\/\#\(\)\_ ]/g;

		if (exp.test(text))
		{
			text = text.replace(exp, "");
			self.$searchText.val(text);
			if (self.obInputTextHint() === text)
			{
				return;
			}
		}

		if (self.placeholderFontSize)
		{
			self.$searchText.css("fontSize", text.length === 0 ? self.placeholderFontSize : "");
		}

		if (self.deferSearchTimeout)
		{
			clearTimeout(self.deferSearchTimeout);
		}

		text = text.trim();
		if (text.length <= 2)
		{
			self.obIsLoading(false);
			self.obHasSearchBeenConducted(false);
			self.obSuggestedResult([]);
			self.resetVirtualContainer();
		}
		else
		{
			// Prevents conflicts with carriage return searches
			if (!self.inputKeyDownSearching)
			{
				self.inputTextSearching = true;
				self.deferSearchTimeout = setTimeout(function()
				{
					//search
					self.saveUserSearch(text);
					self.search(text);
					self.inputKeyDownSearching = false;
				}, 1000);
			}
		}
	};

	/**
	 * In virtual scroll mode, set the result item according to scroll top of the search result div.
	 * @returns {promise} after image loaded.
	 */
	SearchControlViewModel.prototype.setCurrentSearchItem = function()
	{
		var self = this, scrollTop, begin, end, itemCountPerPage, cardsCount, newItems = [], marginTop;
		if (self.currentItems.length <= 0)
		{
			self.resetVirtualContainer();
			self.obSuggestedResult([]);
			return Promise.resolve();
		}
		else
		{
			cardsCount = self.currentItems[0].cards.length;
			if (cardsCount <= 0)
			{
				return Promise.resolve();
			}
		}

		self.topDelta = self.resultHeaderHeight + self.sectionHeight;
		self.$virtualContainer.height(cardsCount * self.singleCardHeight + self.topDelta);
		scrollTop = self.$searchResult.scrollTop();
		begin = Math.floor((scrollTop - self.topDelta) / self.singleCardHeight);
		begin = begin > 0 ? begin : 0;

		if (!self.maxHeight)
		{
			self.maxHeight = parseInt(self.$searchResult.css("maxHeight")) - parseInt(self.$searchResult.css("paddingTop")) - parseInt(self.$searchResult.css("paddingBottom"));
		}
		itemCountPerPage = Math.ceil((self.maxHeight - self.topDelta) / self.singleCardHeight);
		end = begin + itemCountPerPage + 1;
		if (end > cardsCount)
		{
			end = cardsCount;
		}

		if (end - begin < itemCountPerPage && begin != 0)
		{
			begin = end - itemCountPerPage;
			if (begin < 0)
			{
				begin = 0;
			}
		}

		newItems = $.extend(true, [], self.currentItems);
		newItems[0].cards = self.currentItems[0].cards.slice(begin, end);
		self.obSuggestedResult(newItems);
		self.includesPhoto(newItems);
		if (begin >= 1)
		{
			self.$virtualContainer.find(".result-head").hide();
			marginTop = begin * self.singleCardHeight;
		}
		else
		{
			self.$virtualContainer.find(".result-head").show();
			marginTop = 0;
		}
		self.$virtualContent.css("marginTop", marginTop);
	};

	SearchControlViewModel.prototype.checkIfIncludesPhoto = function()
	{
		var self = this, searchSettings = tf.storageManager.get("search.configurations");

		if (!searchSettings || !searchSettings.hasOwnProperty("ShowImageIcon") || searchSettings.ShowImageIcon == null)
		{
			return true;
		}

		return searchSettings.ShowImageIcon;
	};

	SearchControlViewModel.prototype.includesPhoto = function(items)
	{
		var self = this, dataType;

		if (!self.checkIfIncludesPhoto())
		{
			return;
		}

		items.forEach(function(item)
		{
			dataType = item.type;
			if (dataType !== "student" && dataType !== "staff" && dataType !== "vehicle")
			{
				return;
			}
			if (!(item && item.cards && Array.isArray(item.cards)))
			{
				return;
			}
			item.cards.forEach(function(card)
			{
				if (card.imageSrc !== undefined)
				{
					return;
				}

				tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "recordpictures"), {
					paramData: {
						DBID: tf.datasourceManager.databaseId,
						DataTypeID: TF.Helper.SearchControlHelper.getDataTypeId(dataType),
						RecordID: card.Id,
						"@fields": "FileContent"
					}
				}).then(function(result)
				{
					if (result && result.Items && result.Items[0] && result.Items[0].FileContent)
					{
						card.imageSrc = "url(data:image/jpeg;base64," + result.Items[0].FileContent + ")";
						var $card = self.$searchResult.find("[data-index=" + card.Id + "]");
						if ($card.length > 0)
						{
							$card.prepend("<div class='photo' style='background-image: " + card.imageSrc + "'></div>");
							self.autoAdjustFontSize($card);
							self.showSearchTextInElementContent($card);
						}
					}
				}).catch(function() { });
			});
		});
	};

	/**
	 * Reset the status of Virtual Container
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.resetVirtualContainer = function()
	{
		var self = this;
		self.currentItems.length = 0;
		self.$virtualContainer.find(".result-head").show();
		self.$virtualContainer.css("height", "auto");
		self.$virtualContent.css("marginTop", 0);
	};

	/**
	 * Process the search text before search.
	 * @param {string} searchText The user input search text.
	 * @returns {string} The processed text.
	 */
	SearchControlViewModel.prototype.processSearchText = function(searchText)
	{
		var commaSplitArray = searchText.split(","), spaceSplitArray, commaResultArray = [], spaceResultArray;
		$.each(commaSplitArray, function(index, item)
		{
			item = item.trim();
			spaceSplitArray = item.split(" ");
			spaceResultArray = [];
			$.each(spaceSplitArray, function(index2, item2)
			{
				item2 = item2.trim();
				if (item2.length > 0)
				{
					spaceResultArray.push(item2);
				}
			});
			if (spaceResultArray.length > 0)
			{
				commaResultArray.push(spaceResultArray.join(" "));
			}
		});

		return commaResultArray.join(",");
	};

	SearchControlViewModel.prototype.searchSettingsChanged = function(autoSearch)
	{
		var self = this, lastSelectedType = self.obSelectType(), currentSearchText = self.$searchText.val().trim(),
			suggestedResult = self.obSuggestedResult();
		autoSearch = autoSearch == false ? false : true;
		self.updateAllDataTypes();
		self.obAllTypes(self.allTypes);
		if (autoSearch)
		{
			self.reloadRecentSearches();
			self.getRecentlyViewed();
		}
		self.obNotShowRecentSearch(self.getRecentSearchesCount() === 0);

		if ($.grep(self.allTypes, function(type) { return type.value === lastSelectedType.value; }).length === 0)
		{
			self.obSelectType(self.allTypes[0]);
		}
		else
		{
			self.switchDataType(lastSelectedType);
		}

		// apply new search settings only if user already searched something.
		if (suggestedResult && suggestedResult.length === 0) return;
		if (self.lastSearchText == null || self.lastSearchText.length === 0 || self.lastSearchText != currentSearchText) return;

		if (autoSearch)
		{
			self.search(self.lastSearchText);
		}
	};

	/**
	 * Search for result.
	 * @param {string} text Search text.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.search = function(text)
	{
		var self = this,
			searchText = text, type = self.obSelectType().value;

		if (self.compareToPendingSearchCondition(searchText, type)) { return; }
		self.updatePendingSearchCondition(searchText, type);

		self.lastSearchText = text;

		self.obIsLoading(true);
		//ga('send', 'event', 'Action', 'Searched', searchText);
		self.getAllSuggestedResult(searchText, type).then(function(response)
		{
			self.updatePendingSearchCondition("", "");

			if (response)
			{
				var result = response.searchResult,
					searchText = response.searchText,
					searchType = response.searchType;

				// If the search condition of this response does not match current condition. skip it.
				if (!(searchText === self.$searchText.val().trim() && searchType === self.obSelectType().value)) { return; }

				self.obInputTextHint(searchText);
				self.obHasSearchBeenConducted(true);

				if (searchType === "all" && result.length !== 1)
				{
					self.resetVirtualContainer();
					self.obSuggestedResult(result);
					self.includesPhoto(result);
				}
				else
				{
					self.currentItems = result;
					self.setCurrentSearchItem();
				}

				if (self.$searchContent.css("display") === "none")
				{
					self.requireScrollTopReset = true;
				}
				else
				{
					self.$searchResult.scrollTop(0);
				}

				self.userInputChanged();
			}

			self.reloadRecentSearches();
			self.obIsLoading(false);
		});
	};

	/**
	 * Fired after key down.
	 * @param {object} e Key down argument.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.inputKeyDown = function(e)
	{
		var self = this,
			text = self.$searchText.val().trim();

		switch (e.keyCode)
		{
			case $.ui.keyCode.ENTER:
				if (self.processSearchText(text) !== "")
				{
					self.saveUserSearch(text);
					self.search(text);

					self.setKeyDownSearchFlag();
					// If the input text is not completed, clear the input text search.
					// Preventing multiple searches
					if (self.inputTextSearching && self.deferSearchTimeout)
					{
						clearTimeout(self.deferSearchTimeout);
					}
				}
				break;
			case $.ui.keyCode.ESCAPE:
				e.preventDefault();
				e.stopPropagation();
				self.clearText();
				break;
			default:
				break;
		}
	};

	SearchControlViewModel.prototype.inputKeyUp = function(e)
	{
		var text = this.$searchText.val().trim();
		if (text === "")
		{
			e.preventDefault();
			e.stopPropagation();
			this.clearTextWithValue();
		}
	};

	SearchControlViewModel.prototype.setKeyDownSearchFlag = function()
	{
		// Set a flag to prevent the input text search from being triggered immediately.
		// Then clear the flag after a delay of 50 milliseconds to not prevent the next carriage return search.
		this.inputKeyDownSearching = true;
		setTimeout(() =>
		{
			this.inputKeyDownSearching = false;
		}, 50);
	}

	SearchControlViewModel.prototype.updateAllDataTypes = function()
	{
		var self = this,
			searchSettings = tf.storageManager.get("search.configurations"),
			allTypes = TF.Helper.SearchControlHelper.allSearchTypes().filter(function(item)
			{
				return item.permission && (!item.mapCanvas || self.isCurrentTabMapCanvas());
			});

		if (!searchSettings || !searchSettings.hasOwnProperty("DataTypeConfig") || searchSettings.DataTypeConfig == null)
		{
			self.allTypes = allTypes;
			return;
		}

		self.allTypes = allTypes.filter(function(item)
		{
			const configItem = searchSettings.DataTypeConfig.find(configItem => item.value == configItem.name);
			// configItem == null: it's not stored in the config, the item is either "all" or a new type which is added after the previous saving
			// configItem.selected == false: the item is unselected in the settings
			return configItem == null || configItem.selected;
		});
	};

	SearchControlViewModel.prototype.getResultCountPerDataType = function()
	{
		var self = this, searchSettings = tf.storageManager.get("search.configurations");

		if (!searchSettings || !searchSettings.hasOwnProperty("ResultPerDataType") || searchSettings.ResultPerDataType == null)
		{
			return 3;
		}

		return searchSettings.ResultPerDataType;
	};

	SearchControlViewModel.prototype.getAllSuggestedResult = function(value, type)
	{
		var self = this, Deferred = $.Deferred(), allTypeTexts,
			processedText = self.processSearchText(value),
			createResponseObj = function(value, type, result)
			{
				return {
					searchText: value,
					searchType: type,
					searchResult: result
				};
			};

		self.obChangeDataSourceShow(false);
		if (processedText === "")
		{
			Deferred.resolve(false);
		}
		else if (type !== "all")
		{
			self.getSuggestedResultByType(type, processedText).then(function(result)
			{
				var singleResultCount = 0;
				if (result && result.count)
				{
					singleResultCount = result.count;
				}

				var primise = Promise.resolve();
				if (!result)
				{
					primise = self.checkAllDBs();
				}

				primise.then(function()
				{
					self.obAllResultsCount(singleResultCount);
					self.obSingleResultCount(singleResultCount);
					Deferred.resolve(createResponseObj(value, type, result ? [result] : []));
				});
			});
		}
		else
		{
			var count = self.getResultCountPerDataType(), PromiseAll = [], curType;
			for (var i = 0, len = self.allTypes.length; i < len; i++)
			{
				curType = self.allTypes[i];
				if (curType.value !== "all" && curType.permission && curType.value !== "mystreets")
				{
					PromiseAll.push(self.getSuggestedResultByType(curType.value, processedText, count));
				}
			}

			Promise.all(PromiseAll).then(function(result)
			{
				var allResultsCount = 0,
					searchResult = $.grep(result, function(item)
					{
						if (item && item.count)
						{
							allResultsCount += item.count;
						}
						return item;
					});
				var primise = Promise.resolve();
				if (searchResult.length === 0)
				{
					primise = self.checkAllDBs();
				}
				primise.then(function()
				{
					self.obAllResultsCount(allResultsCount);
					self.obSingleResultCount(allResultsCount);
					if (searchResult.length === 1)
					{
						self.getSuggestedResultByType(searchResult[0].type, processedText).then(function(result)
						{
							Deferred.resolve(createResponseObj(value, type, result ? [result] : []));
						});
					}
					else
					{
						allTypeTexts = self.allTypes.map(function(item) { return item.value; });
						var dataTypeConfigurations= tf.userPreferenceManager.getUserPreferenceValue("rfweb.search.configurations")?.DataTypeConfig || [];
						const dataTypeOrder = dataTypeConfigurations.map(item => item.name);
						searchResult.sort((a, b) => {
							return dataTypeOrder.indexOf(a.type) - dataTypeOrder.indexOf(b.type);
						});
						
						Deferred.resolve(createResponseObj(value, type, searchResult));
					}
				});
			});
		}

		return Deferred;
	};

	/**
	 * If the length of dbs greater than one, the change data source link will be shown.
	 * @returns {promise} after check if it has more than one db.
	 */
	SearchControlViewModel.prototype.checkAllDBs = function()
	{
		var self = this;
		return tf.datasourceManager.validateAllDBs().then(function(result)
		{
			if (result && result.Items && result.Items[0] && result.Items[0].AnyDatabasePass && result.Items[0].Count > 1)
			{
				self.obChangeDataSourceShow(true);
			}
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultByType = function(type, value, count)
	{

		var self = this, getSuggest = Promise.resolve();
		var searchType = TF.Helper.SearchControlHelper.allSearchTypes().filter(function(c) { return c.value == type; })[0];
		var isMapCanvas = searchType.mapCanvas;
		if (isMapCanvas)
		{
			if (this.isCurrentTabMapCanvas())
			{
				if (type == "unassignedStudentsRouting")
				{
					getSuggest = this.getSuggestedResultByUnassignedStudentRouting(type, value, count);
				}
				else if (type == "assignedStudentsRouting")
				{
					getSuggest = this.getSuggestedResultByAssignedStudentRouting(type, value, count);
				}
				else if (type == "stopPoolRouting")
				{
					getSuggest = this.getSuggestedResultByStopPoolRouting(type, value, count);
				}
				else if (type == "tripStopRouting")
				{
					getSuggest = this.getSuggestedResultByTripStopRouting(type, value, count);
				}
			} else
			{
				getSuggest = Promise.resolve({
					count: 0,
					cards: [],
					whereQuery: "",
					showInName: "Map"
				});
			}
		}
		else if (type == "address")
		{
			getSuggest = this.getSuggestedResultByAddressPoint(type, value, count);
		}
		else if (type == "street")
		{
			getSuggest = this.getSuggestedResultByMapAddress(type, value, count);
		} else if (type == "schoolLocation")
		{
			getSuggest = this.getSuggestedResultBySchoolLocation(type, value, count);
		} else
		{
			var endPoint = type;
			var paramData = {
				text: value,
				count: count
			};
			if (type == "tripRouting")
			{
				endPoint = "trip";
				delete paramData.count;
			}
			let apiPrefix = tf.api.apiPrefix()
			if (type === "mapincident")
			{
				apiPrefix = tf.api.apiPrefixWithoutDatabase();
			}
			getSuggest = tf.promiseAjax.get(pathCombine(apiPrefix, "search", tf.dataTypeHelper.getEndpoint(endPoint), "simple"),
				{
					paramData: paramData
				}, { overlay: false })
				.then(function(data)
				{
					if (data.Items[0] && data.Items[0].SimpleEntities && data.Items[0].SimpleEntities.length > 0)
					{
						var entities = data.Items[0].SimpleEntities,
							cards = entities.map(function(item)
							{
								var entity = {
									Id: item.Id,
									title: item.Title,
									subtitle: item.SubTitle,
									type: type,
									searchText: value,
									whereQuery: data.Items[0].WhereQuery,
									imageSrc: undefined
								};
								return entity;
							});
						return {
							type: type,
							count: data.TotalRecordCount,
							searchText: value,
							cards: cards.slice(0, count),
							allCards: cards,
							whereQuery: data.Items[0].WhereQuery,
							showInName: searchType.showIn || "Grid"
						};
					}
				}).catch(function() { });
		}

		return getSuggest.then(function(suggestion)
		{
			if (!suggestion || suggestion.cards.length == 0)
			{
				return;
			}
			var style = TF.Helper.SearchControlHelper.cardStyle()[type];
			suggestion = suggestion || {
				count: 0,
				cards: [],
				whereQuery: ""
			};
			suggestion.type = type;
			suggestion.title = style.title;
			suggestion.color = style.color;
			suggestion.searchText = value;
			suggestion.showInName = suggestion.showInName || "Grid";
			suggestion.cards.forEach(function(c)
			{
				c.showInName = suggestion.showInName;
			});
			return suggestion;
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultByAddressPoint = function(type, value, count)
	{
		var geoSearch = new TF.RoutingMap.RoutingPalette.GeoSearch(tf.map.ArcGIS, this.map, false);
		return geoSearch.suggestAddressPoint(value).then(function(data)
		{
			var allData = (data || []),
				allCards = allData.map(function(item)
				{
					var geometry = new tf.map.ArcGIS.Point({
						x: item.location.x,
						y: item.location.y,
						spatialReference: tf.map.ArcGIS.SpatialReference.WebMercator
					});
					return {
						Id: 0,
						title: item.address,
						subtitle: "",
						type: type,
						whereQuery: "",
						imageSrc: undefined,
						address: item.address,
						x: item.location.x,
						y: item.location.y,
						geometry: geometry
					};
				});
			return {
				count: allData.length,
				cards: allCards.slice(0, count),
				allCards: allCards,
				whereQuery: "",
				showInName: "Map"
			};
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultByMapAddress = function(type, value, count)
	{
		var geoSearch = new TF.RoutingMap.RoutingPalette.GeoSearch(tf.map.ArcGIS, this.map, false);
		return geoSearch.suggest(value).then(function(data)
		{
			var allData = (data || []),
				allCards = allData.map(function(item)
				{
					var geometry = new tf.map.ArcGIS.Point({
						x: item.location.x,
						y: item.location.y,
						spatialReference: tf.map.ArcGIS.SpatialReference.WebMercator
					});
					return {
						Id: 0,
						title: item.address,
						subtitle: "",
						type: type,
						whereQuery: "",
						imageSrc: undefined,
						address: item.address,
						x: item.location.x,
						y: item.location.y,
						geometry: geometry,
						Addr_type: item.attributes.Addr_type
					};
				});
			return {
				count: allData.length,
				cards: allCards.slice(0, count),
				allCards: allCards,
				whereQuery: "",
				showInName: "Map"
			};
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultsCountByType = function(type, value)
	{
		const queryString = "?text=" + encodeURIComponent(value), apiPrefix = type === "mapincident" ? tf.api.apiPrefixWithoutDatabase() : tf.api.apiPrefix();
		return tf.promiseAjax.get(pathCombine(apiPrefix, "search", tf.dataTypeHelper.getEndpoint(type), "simple", "ids", queryString));
	};

	/**
	 * The event handler when user clicks on View In Grid.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.viewInGridClick = function(model, e)
	{
		var self = this;
		if (model.showInName == "Grid")
		{
			self.goToGrid(model.type, model.searchText, model.whereQuery);
		}
		else
		{
			self.gotoMap(model);
		}
		self.reloadRecentSearches();
		self.onNavComplete.notify();
	};

	/**
	 * The event handler when user clicks on any suggested result.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.suggestedResultClick = function(model, e)
	{
		var self = this;

		if (self.suggestedResultClicked)
		{
			return;
		}
		else
		{
			self.suggestedResultClicked = true;
		}

		if (model.showInName == "Map")
		{
			if (model.geometry)
			{
				if (model.type === "mystreets")
				{
					let geometry = new tf.map.ArcGIS.Polyline({
						paths: model.geometry.paths,
						spatialReference: model.geometry.spatialReference,
						extent: model.extent
					});
					model.geometry = geometry;
				}
				else
				{
					let geometry = new tf.map.ArcGIS.Point({
						x: model.geometry.x,
						y: model.geometry.y,
						spatialReference: model.geometry.spatialReference
					});
					model.geometry = geometry;
				}
			}
			self.gotoMap(model);
		} else
		{
			self.goToGrid(model.type, model.searchText, model.whereQuery, model.Id);
		}
		TF.Helper.SearchControlHelper.saveUserSearchResult(model).then(function()
		{
			self.getRecentlyViewed();
		}).finally(() =>
		{
			self.suggestedResultClicked = false;
		});
		self.reloadRecentSearches();
	};

	/**
	* draw street or parcel point location on focus doc map,or create a new map canvas to display
	*/
	SearchControlViewModel.prototype.gotoMap = function(model)
	{
		var self = this;
		self.collapseSearch();
		// clear quick filter
		tf.storageManager.delete(tf.storageManager.gridCurrentQuickFilter(model.type));

		var records;
		if (model.allCards)
		{
			records = model.allCards;
		} else
		{
			records = [model];
		}

		if (model.type == "tripRouting")
		{
			self.openTrip(records.map((record) => { return record.Id; }));
		} else
		{
			// if show in map, find the focus document with map,else open a new map
			var hashObjs = tf.documentManagerViewModel.obHashArray(), map, _mapCanvasPage;
			for (var hash in hashObjs)
			{
				var hashObj = hashObjs[hash];
				if (hashObj.visible)
				{
					// find document
					var doc = tf.documentManagerViewModel.allDocuments().find(function(i)
					{
						return i.routeState == hash;
					});
					if (doc && hashObj.DocumentData.data)
					{
						// find map
						if (hashObj.DocumentData.data.type == "RoutingMap")
						{
							map = doc.mapCanvasPage._map;
							_mapCanvasPage = doc.mapCanvasPage;
						} else if (hashObj.DocumentData.data.gridType && model.type !== "mystreets")
						{
							if (doc.obRightContentType() == doc.contentTypes.splitMap)
							{
								map = doc.gridMap._map;
							} else if (doc.obRightContentType() == doc.contentTypes.detailView)
							{
								var mapBlock = Enumerable.From(doc.detailView.rootGridStack.dataBlocks).FirstOrDefault(null, function(c) { return c.mapManager; });
								if (mapBlock)
								{
									map = mapBlock.mapManager._map;
								}
								else
								{
									var mapDataBlock;
									doc.detailView.rootGridStack.dataBlocks.forEach((item) =>
									{
										if (item.nestedGridStacks)
										{
											item.nestedGridStacks.forEach((gridStack) =>
											{
												if (gridStack && gridStack.dataBlocks && gridStack.dataBlocks.find(x => x.mapManager))
												{
													mapDataBlock = gridStack.dataBlocks.find(x => x.mapManager);
												}
											});
										}
									})
									map = mapDataBlock ? mapDataBlock.mapManager._map : null;
								}
							}
						}
					}
				}
			}
			if (!map)
			{
				// open new map canvas to display
				tf.documentManagerViewModel.add(TF.Document.DocumentData.RoutingMapInfo.create(), false, false, "", true, false);
				clearInterval(self.findRoutingMapInterval);
				self.findRoutingMapInterval = setInterval(function()
				{
					const routingMap = tf.documentManagerViewModel.allDocuments()[tf.documentManagerViewModel.allDocuments().length - 1];
					if (routingMap.mapCanvasPage && routingMap.mapCanvasPage._map)
					{
						clearInterval(self.findRoutingMapInterval);
						routingMap.mapCanvasPage._map.mapView.when(function()
						{
							// make sure map is loaded
							setTimeout(() =>
							{
								if (model.type === "mystreets")
								{
									if (!routingMap.mapCanvasPage.mapEditingPaletteViewModel.obShow())
									{
										routingMap.mapCanvasPage.togglePalettePanel(routingMap.mapCanvasPage.mapEditingPaletteViewModel);
										TF.RoutingMap.EsriTool.centerSingleItem(routingMap.mapCanvasPage._map, model);
										setTimeout(() =>
										{
											routingMap.mapCanvasPage.mapEditingPaletteViewModel.myStreetsViewModel.drawTool.displayGraphicInExtent().then(function()
											{
												routingMap.mapCanvasPage.mapEditingPaletteViewModel.myStreetsViewModel.dataModel.setHighlighted([model.Id]);
											});
										}, 3000);

									}
								}
								else
								{
									drawSymbolOnMap(routingMap.mapCanvasPage._map);
								}
							}, 1000);
						});
					}
				}, 500);
			} else
			{
				if (model.type === "mystreets")
				{
					TF.RoutingMap.EsriTool.centerSingleItem(_mapCanvasPage._map, model);
					setTimeout(() =>
					{
						_mapCanvasPage.mapEditingPaletteViewModel.myStreetsViewModel.drawTool.displayGraphicInExtent().then(function()
						{
							_mapCanvasPage.mapEditingPaletteViewModel.myStreetsViewModel.dataModel.setHighlighted([model.Id]);
						});
					}, 2000);
				}
				else
				{
					drawSymbolOnMap(map);
				}
			}
		}

		self.$searchText.blur();

		function drawSymbolOnMap(map)
		{
			var points = records,
				graphics = [],
				url = /(.*)\/.+\/html/g.exec(location.href)[1],
				symbol = {
					type: "picture-marker",
					url: url + "/global/img/map/pin.png",
					width: "20px",
					height: "26px",
					yoffset: window.yoffset || 10
				};

			graphics = points.map(function(point)
			{
				return new tf.map.ArcGIS.Graphic({
					geometry: point.geometry,
					symbol: symbol
				});
			});

			var layerId = "searchPointLayer";
			var layer = map.findLayerById(layerId);
			if (!layer)
			{
				layer = new tf.map.ArcGIS.GraphicsLayer({ id: layerId });
				map.add(layer);
			}
			layer.removeAll();
			layer.addMany(graphics);
			// zoom to view
			if (graphics.length == 1)
			{
				TF.RoutingMap.EsriTool.centerSingleItem(map, graphics[0]);
			} else
			{
				TF.RoutingMap.EsriTool.centerMultipleItem(map, graphics);
			}
		}
	};

	SearchControlViewModel.prototype.openTrip = function(ids)
	{
		var self = this;
		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search/trips"),
			{
				data: { idFilter: { IncludeOnly: ids } },
				paramData: { "@relationships": "TripStops" }
			}).then(function(responses)
			{
				var trips = responses.Items;
				// open in current map canvas
				if (self.isCurrentTabMapCanvas())
				{
					var routingMapDocumentViewModel = tf.documentManagerViewModel.obCurrentDocument().mapCanvasPage;
					var routingPaletteViewModel = routingMapDocumentViewModel.routingPaletteViewModel;
					var tripDataModel = routingPaletteViewModel.tripViewModel.dataModel;
					let docData = routingMapDocumentViewModel.DocumentData || {};
					let innerData = docData.data || {};
					innerData.trips = trips;
					innerData.tryOpenTrip = true;
					docData.data = innerData;
					routingMapDocumentViewModel.DocumentData = docData;
					if (!routingPaletteViewModel.obShow())
					{
						var initSubscriber = tripDataModel.onInit.subscribe(function()
						{
							tryOpenTrip(tripDataModel, trips);
							initSubscriber.dispose();
						});
						routingMapDocumentViewModel.togglePalettePanel(routingPaletteViewModel);
					} else
					{
						tryOpenTrip(tripDataModel, trips);
					}
				} else
				{
					// open in new map canvas
					var docData = TF.Document.DocumentData.RoutingMapInfo.create();
					docData.data = docData.data || {};
					docData.data.trips = trips;
					docData.data.tryOpenTrip = true;
					tf.documentManagerViewModel.add(docData, false, false, "", true, false);
				}
			});

		function tryOpenTrip(tripDataModel, trips)
		{
			var tripsDic = _.keyBy(trips, function(trip)
			{
				return trip.Id;
			});
			return tripDataModel.tryOpenTrip(trips).then(function()
			{
				tripDataModel.viewModel.eventsManager.zoomClick(tripDataModel.trips.filter(function(trip) { return tripsDic[trip.id]; }));
			});
		}
	};

	/**
	 * Go to grid URL
	 * @param {string} type The data type for this search.
	 * @param {object} options The options for grid.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.goToGrid = function(type, searchText, whereClause, id)
	{
		var self = this;
		tf.loadingIndicator.showImmediately();
		self.collapseSearch();

		// clear quick filter
		tf.storageManager.delete(tf.storageManager.gridCurrentQuickFilter(type));

		var findDoc = function()
		{
			var hashObjs = tf.documentManagerViewModel.obHashArray(), existingHash;

			for (var hash in hashObjs)
			{

				var hashObj = hashObjs[hash], hashData = hashObj.DocumentData.data, gridState = (hashData || {}).gridState || {};
				if (gridState.isQuickSearch && hashData.gridType == type && hashData.filterClause == whereClause)
				{
					existingHash = hash;
					break;
				}
			}

			if (!existingHash)
			{
				return null;
			}

			return tf.documentManagerViewModel.allDocuments().find(function(i)
			{
				return i.routeState == existingHash;
			});
		};

		var existingDoc = findDoc();
		self.getSuggestedResultsCountByType(type, searchText).then(function(ids)
		{
			if (!existingDoc)
			{
				var options = { gridType: type, gridState: { isQuickSearch: true, filteredIds: ids }, layoutAndFilterOperation: true, fromSearch: true, filterClause: whereClause };
				var documentData = new TF.Document.DocumentData(TF.Document.DocumentData.Grid, options);
				tf.documentManagerViewModel.add(documentData, false, true);
			}
			else
			{
				if (existingDoc.DocumentData?.data?.gridState)
				{
					existingDoc.DocumentData.data.gridState.filterClause = "";
					existingDoc.DocumentData.data.gridState.filteredIds = ids;
					const kendoGrid = existingDoc.getSearchGrid();
					if (kendoGrid && kendoGrid.kendoGrid)
					{
						kendoGrid.kendoGrid.dataSource.data([]);
						kendoGrid.kendoGrid.dataSource.sync();

						kendoGrid.options.fromSearch = true;
						kendoGrid.obCallOutFilterName(undefined);
						kendoGrid.obSelectedGridFilterId(null);
						kendoGrid._gridState.filteredIds = ids;
						kendoGrid.kendoGrid.dataSource.originalFilter({});
					}
				}

				tf.documentManagerViewModel._showDocument(existingDoc);
			}

			if (id)
			{
				var openDetailView = function()
				{
					let scrollAndSelected = false;
					if (!existingDoc)
					{
						existingDoc = findDoc();
						if (!existingDoc)
						{
							return;
						}
					}

					var grid = existingDoc.getSearchGrid();
					if (!grid)
					{
						return;
					}

					var allIds = grid.obAllIds();
					if (!allIds || !allIds.length)
					{
						return;
					}

					scrollAndSelected = existingDoc.gridViewModel.scrollToRowById(id);
					scrollAndSelected = scrollAndSelected && existingDoc.gridViewModel.selectedGridRowById(id);
					scrollAndSelected && existingDoc.gridViewModel.openDetailView(id);
					return scrollAndSelected;
				};

				var timer = setInterval(function()
				{
					if (openDetailView())
					{
						clearInterval(timer);
					}
				}, 100);
			}

			self.$searchText.blur();
			tf.loadingIndicator.tryHide();
		});
	};

	/**
	 * To change the select type to 'all', then search.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.showAllClick = function()
	{
		var self = this,
			model = $.grep(self.allTypes, function(type) { return type.value === "all"; })[0];
		self.selectTypeClick(model);
	};

	SearchControlViewModel.prototype.openDataSourceModal = function()
	{
		var self = this;
		self.keepSearchActive = true;
		tf.datasourceManager.open().then(function()
		{
			self.keepSearchActive = false;
		});
	};

	/**
	 * Get the type display label by the type value.
	 * @param {string} value The type value.
	 * @returns {void} The type display label.
	 */
	SearchControlViewModel.prototype.recentSearchSubtitleFormat = function(value)
	{
		var self = this, type = self.getDataTypeKeyById(value);
		for (var i = 0; i < self.allTypes.length; i++)
		{
			if (self.allTypes[i].value === type)
			{
				return self.allTypes[i].text;
			}
		}

		return "";
	};

	/**
	 * The event handler when user clicks on any recent search record.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.recentSearchRecordClick = function(searchRecord)
	{
		var self = this,
			selectedDataType = self.findDataTypeByValue(self.getDataTypeKeyById(searchRecord.type)),
			searchText = searchRecord.text;

		if (!selectedDataType)
		{
			selectedDataType = self.findDataTypeByValue("all");
		}

		self.switchDataType(selectedDataType);
		self.$searchText.val(searchText);
		self.$searchText.focus();
		self.setSearchInputCursor();
		clearTimeout(self.deferSearchTimeout);
		clearTimeout(self.clearFocusTimeout);
		self.searchTextFocused = true;
		self.search(searchText);
	};

	/**
	 * Request to save this user search record.
	 * @param {type} searchText The text user input for the search.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.saveUserSearch = function(searchText)
	{
		searchText = searchText.trim();

		var self = this,
			type = self.obSelectType().value,
			dataTypeId = TF.Helper.SearchControlHelper.getDataTypeId(type);

		if (self.compareToPendingSearchCondition(searchText, type))
		{
			return Promise.resolve(true);
		}

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "UserSearchRecords"), {
			data: [{
				DBID: tf.datasourceManager.databaseId,
				DataType: dataTypeId,
				SearchText: searchText,
				ApplicationId: TF.applicationId
			}]
		}, { overlay: false });
	};

	SearchControlViewModel.prototype.getDataTypeKeyById = function(dataTypeId)
	{
		var type = TF.Helper.SearchControlHelper.allSearchTypes().filter(function(c) { return c.dataTypeId == dataTypeId; });

		if (type && type.length > 0)
		{
			return type[0].value;
		}

		if (type == 18)
		{
			return "all";
		}

		return tf.dataTypeHelper.getKeyById(dataTypeId);
	};

	SearchControlViewModel.prototype.getUserRecentSearches = function()
	{
		var self = this;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "UserSearchRecords"), {
			paramData: {
				DBID: tf.datasourceManager.databaseId,
				ApplicationId: TF.applicationId
			}
		}, { overlay: false })
			.then(function(response)
			{
				if (!response) { return; }

				var results = self.userSearchEntitiesFormation(response.Items);

				return Promise.resolve(results);
			});
	};

	/**
	 * Format the UserSearch entities.
	 * @param {type} entityList
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.userSearchEntitiesFormation = function(entityList)
	{
		if (!Array.isArray(entityList)) { return []; }

		entityList = Array.sortBy(entityList, "UserSearchID", true);
		return entityList.map(function(entity)
		{
			return {
				text: entity.SearchText,
				type: entity.DataType
			};
		});
	};

	/**
	 * Triggered when user's input has been changed.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.userInputChanged = function()
	{
		var self = this;
		self.highlightKeywordsInResult();
		self.autoAdjustFontSize();
		self.showSearchTextInElementContent();
	};

	/**
	 * Split the user input search text into keywords.
	 * @param {string} searchInputText The user input search text.
	 * @returns {object} The array that contains keywords.
	 */
	SearchControlViewModel.prototype.splitKeywords = function(searchInputText)
	{
		var keyword, result = [],
			candidateList = searchInputText.split(","),
			subCandidateList;

		$.each(candidateList, function(index, item)
		{
			item = item.trim();
			subCandidateList = item.split(" ");

			$.each(subCandidateList, function(index2, item2)
			{
				keyword = item2.trim();
				if (keyword)
				{
					result.push(item2);
				}
			});

			if (subCandidateList.length > 1)
			{
				result.push(item);
			}
		});

		return result;
	};

	/**
	 * Bolden the keyword with the title and subtitle.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.highlightKeywordsInResult = function()
	{
		var isCaseSensitive = false,
			keywords = this.obSearchKeywords(),
			$textList = $(".card-title, .card-subtitle");

		this.elementContentHelper.boldenKeywordsInText($textList, keywords, isCaseSensitive);
	};

	/**
	 * Adjust the font-size to fix its element's width.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.autoAdjustFontSize = function($cards)
	{
		var self = this,
			maxFontSizeForCardTitle = 15,
			minFontSizeForCardTitle = 15,
			$cards = $cards ? $cards : self.$searchResult.find(".card"),
			$generalCards = $cards.filter(function(index, card)
			{
				return $(card).children(".photo").length <= 0;
			}),
			$photoCards = $cards.filter(function(index, card)
			{
				return $(card).children(".photo").length > 0;
			});

		function reduceFontSizeUntil($baseCards)
		{
			if ($baseCards.length > 0)
			{
				var $cardLeft = $baseCards.find(".card-left"),
					$cardTitles = $cardLeft.find(".card-title"),
					maxCardTitleWidth = parseInt($cardLeft.css("max-width"));
				$cardTitles.each((index, el) =>
				{
					var $el = $(el);
					var schoolGradeWidth = $el.parent().find(".school-grade").width() + 5;
					var rightInfoWidth = $el.parent().find(".card-right-info").width();
					var extraWidth = schoolGradeWidth + rightInfoWidth;
					// For title in the data card.
					self.elementContentHelper.reduceFontSizeUntil($el, maxFontSizeForCardTitle, minFontSizeForCardTitle, maxCardTitleWidth - extraWidth, extraWidth);
				});
			}
		}
		reduceFontSizeUntil($generalCards);
		reduceFontSizeUntil($photoCards);
		// For result header.
		self.autoAdjustResultHeaderFontSize();
	};

	/**
	 * Make sure the bolden search text is shown (not in the truncated part).
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.showSearchTextInElementContent = function($cards)
	{
		var self = this,
			keywords = self.obSearchKeywords(),
			$cards = $cards ? $cards : self.$searchResult.find(".card"),
			$generalCards = $cards.filter(function(index, card)
			{
				return $(card).children(".photo").length <= 0;
			}),
			$photoCards = $cards.filter(function(index, card)
			{
				return $(card).children(".photo").length > 0;
			});

		function adjustTextWithKeywordForDisplay($baseCards)
		{
			if ($baseCards.length > 0)
			{
				var $cardLeft = $baseCards.find(".card-left"),
					$cardTitles = $cardLeft.find(".card-title"),
					$cardSubTitles = $cardLeft.find(".card-subtitle"),
					maxCardTitleWidth = parseInt($cardLeft.css("max-width"));
				self.elementContentHelper.adjustTextWithKeywordForDisplay($cardSubTitles, keywords, maxCardTitleWidth);
				$cardTitles.each((i, e) =>
				{
					var $e = $(e);
					var schoolGradeWidth = $e.parent().find(".school-grade").width() + 5;
					var rightInfoWidth = $e.parent().find(".card-right-info").width();
					var extraWidth = schoolGradeWidth + rightInfoWidth;
					self.elementContentHelper.adjustTextWithKeywordForDisplay($e, keywords, maxCardTitleWidth - extraWidth);
				});
			}
		}
		adjustTextWithKeywordForDisplay($generalCards);
		adjustTextWithKeywordForDisplay($photoCards);
	};

	/**
	 * Update the saved pending search condition so only matched response would be applied.
	 * @param {string} text The search text.
	 * @param {string} type The data type.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.updatePendingSearchCondition = function(text, type)
	{
		this.pendingSearch["searchText"] = text;
		this.pendingSearch["searchType"] = type;
	};

	/**
	 * Whether the text and type are identical with the pending search condition.
	 * @param {string} text The search text.
	 * @param {string} type The data type.
	 * @returns {bool} Whether the given information matches current search condition.
	 */
	SearchControlViewModel.prototype.compareToPendingSearchCondition = function(text, type)
	{
		return (text === this.pendingSearch["searchText"] && type === this.pendingSearch["searchType"]);
	};

	/**
	 * Check if the scrollTop for search result should be set to 0.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.checkIfToResetScrollTop = function()
	{
		var self = this;

		if (self.requireScrollTopReset && self.$searchResult.is(":visible"))
		{
			self.$searchResult.scrollTop(0);
			self.requireScrollTopReset = false;
		}
	};

	/**
	 * Auto adjust the font-size for the result header.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.autoAdjustResultHeaderFontSize = function()
	{
		var self = this, maxWidth,
			maxFontSize = 15, minFontSize = 12,
			fontSize = maxFontSize,
			$resultHeader = self.$searchResult.find(".result-head"),
			$headerLabel = $resultHeader.find(".head-label"),
			$searchText = $resultHeader.find(".search-text"),
			totalWidth = $resultHeader.width();

		$searchText.css({ "display": "inline", "width": "auto", "padding-left": 0, "padding-right": 0 });

		do
		{
			$resultHeader.css("font-size", fontSize);
			maxWidth = totalWidth - $headerLabel.width() - 2;

		} while ($searchText.width() >= maxWidth && --fontSize > minFontSize);

		// Set style back to original.
		$searchText.css({ "display": "", "width": "", "padding-left": "", "padding-right": "" });
		$searchText.css("width", maxWidth);
	};

	/**
	 * The event handler when user hover on Section In Grid.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.mouseOnSection = function(model, e)
	{
		var $sectionTitle = $(e.currentTarget.children[0]), $viewInGrid = $(e.currentTarget.children[1].children[1]);
		if (e.handleObj.type === "mouseover")
		{
			$sectionTitle.addClass("hover");
			$viewInGrid.addClass("hover");
		}
		else
		{
			$sectionTitle.removeClass("hover");
			$viewInGrid.removeClass("hover");
		}
	};

	/**
	 * Open Search settings modal.
	 * @param {Object} model
	 * @param {Event} e
	 */
	SearchControlViewModel.prototype.openSearchSettingsModal = function()
	{
		var self = this,
			manageModal = new TF.Modal.SearchSettingsModalViewModel({
				onClearRecentSearchEvent: self.onClearRecentSearchEvent,
				onSearchSettingsChangedEvent: self.onSearchSettingsChangedEvent
			});

		self.keepSearchActive = true;
		//ga('send', 'event', 'Area', 'Search Settings');
		tf.modalManager.showModal(manageModal).then(function()
		{
			self.keepSearchActive = false;
			self.$searchText.focus();
		});
	};

	SearchControlViewModel.prototype.bindCurrentDocumentChangeEvent = function()
	{
		var self = this;
		tf.documentManagerViewModel.obCurrentDocument.subscribe(function()
		{
			self.searchSettingsChanged(false);
			var currentDocument = tf.documentManagerViewModel.obCurrentDocument();
			if (self.isCurrentTabMapCanvas())
			{
				self.refreshByMapCanvas();
				if (!currentDocument.bindSearchControlEvent)
				{
					currentDocument.routingPaletteViewModel.tripViewModel.dataModel.onTripsChangeEvent.subscribe(function()
					{
						self.refreshByMapCanvas();
					});
					currentDocument.routingPaletteViewModel.tripViewModel.dataModel.onTripStopsChangeEvent.subscribe(function()
					{
						self.refreshByMapCanvas();
					});
					currentDocument.routingPaletteViewModel.tripViewModel.dataModel.onAssignStudentsChangeToMapEvent.subscribe(function()
					{
						self.refreshByMapCanvas();
					});
					currentDocument.routingPaletteViewModel.tripViewModel.dataModel.onCandidatesStudentsChangeToMapEvent.subscribe(function()
					{
						self.refreshByMapCanvas();
					});
					currentDocument.routingPaletteViewModel.stopPoolViewModel.dataModel.onAllChangeEvent.subscribe(function()
					{
						self.refreshByMapCanvas();
					});
					currentDocument.bindSearchControlEvent = true;
				}
			} else
			{
				// remove map canvas results
				var mapCanvasTypes = TF.Helper.SearchControlHelper.allSearchTypes().filter(function(c) { return c.mapCanvas; }).map(function(c) { return c.value; });
				self.obSuggestedResult(self.obSuggestedResult().filter(function(c)
				{
					return mapCanvasTypes.indexOf(c.type) < 0;
				}));
				var count = Enumerable.From(self.obSuggestedResult()).Sum(function(c) { return c.count; });
				self.obAllResultsCount(count);
				self.obSingleResultCount(count);
			}
			self.lastDocumentType = (currentDocument || {}).documentType;
		});
	};

	SearchControlViewModel.prototype.refreshByMapCanvas = function()
	{
		var self = this,
			type = self.obSelectType().value,
			promises = [];
		if (!self.lastSearchText)
		{
			return;
		}
		var mapCanvasTypes = self.allTypes.filter(function(c) { return c.mapCanvas; }).map(function(c) { return c.value; });
		mapCanvasTypes.forEach(function(mapCanvasType)
		{
			if (type == mapCanvasType || type == "all")
			{
				promises.push(self.getSuggestedResultByType(mapCanvasType, self.lastSearchText));
			}
		});

		Promise.all(promises).then(function(data)
		{
			data = data.filter(function(c) { return c; });
			var suggestedResult = self.obSuggestedResult().filter(function(c)
			{
				return mapCanvasTypes.indexOf(c.type) < 0;
			});
			var types = self.allTypes.map(function(c) { return c.value; });
			suggestedResult = suggestedResult.concat(data);
			suggestedResult = Enumerable.From(suggestedResult).OrderBy(function(c) { return types.indexOf(c.type); }).ToArray();

			var resultCountPerDataType;
			if (suggestedResult.length > 1)
			{
				resultCountPerDataType = self.getResultCountPerDataType();
			}
			suggestedResult.forEach(function(d)
			{
				if (!d.allCards)
				{
					d.allCards = d.cards;
				}
				d.cards = d.allCards.slice(0, resultCountPerDataType);
			});

			self.resetVirtualContainer();
			self.obSuggestedResult([]);
			self.obSuggestedResult(suggestedResult);
			var count = Enumerable.From(suggestedResult).Sum(function(c) { return c.count; });
			self.obAllResultsCount(count);
			self.obSingleResultCount(count);
			self.highlightKeywordsInResult();
			self.autoAdjustFontSize();
			self.showSearchTextInElementContent();
		});
	};

	SearchControlViewModel.prototype.isCurrentTabMapCanvas = function()
	{
		var document = tf.documentManagerViewModel.obCurrentDocument();
		return document && document.documentType == "Routing Map";
	};

	SearchControlViewModel.prototype.createStudentRightInfo = function(student)
	{
		if (student.RequirementType == 1)
		{
			var weeks = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];
			var weekRange = "";
			student.RequirementDays.forEach(function(day, index)
			{
				var week = "<b>" + weeks[index] + "</b>";
				if (!day)
				{
					week = "<del>" + week + "</del>";

				}
				if (index != weeks.length - 1)
				{
					week += "&nbsp;";
				}
				weekRange += week;
			});
			var dateRange = moment(student.RequirementStartDate).format("MM/DD/YYYY") + " - " + moment(student.RequirementEndDate).format("MM/DD/YYYY");
			return "<div style='font-size:12px;margin-top:5px;'><div>" + weekRange + "</div><div>" + dateRange + "</div></div>";
		}
	};

	SearchControlViewModel.prototype.getSuggestedResultByUnassignedStudentRouting = function(type, value, count)
	{
		return this.getSuggestedStudents(value).then(students =>
		{
			let document = tf.documentManagerViewModel.obCurrentDocument(),
				allData = Object.values(document.routingPaletteViewModel.tripViewModel.dataModel.routingStudentManager.students)
					.filter(c => c.geometry && c.isShowOnCandidateMap && c.isCandidate && students.some(x => x.Id === c.id)),
				allCards = allData.map(item =>
				{
					var schoolGrade = item.SchoolCode || "";
					if (item.Grade != null)
					{
						if (schoolGrade)
						{
							schoolGrade += " | ";
						}

						schoolGrade += item.Grade;
					}

					return {
						Id: 0,
						title: item.FirstName + " " + item.LastName,
						subtitle: item.Address,
						type: type,
						whereQuery: "",
						imageSrc: undefined,
						x: item.geometry.x,
						y: item.geometry.y,
						geometry: item.geometry,
						schoolGrade: schoolGrade,
						rightInfo: this.createStudentRightInfo(item)
					};
				});

			return {
				count: allData.length,
				cards: allCards.slice(0, count),
				allCards: allCards,
				whereQuery: "",
				showInName: "Map"
			};
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultByAssignedStudentRouting = function(type, value, count)
	{
		return this.getSuggestedStudents(value).then(students =>
		{
			let document = tf.documentManagerViewModel.obCurrentDocument(),
				trips = document.routingPaletteViewModel.tripViewModel.dataModel.trips,
				allData = _.flatMapDeep(trips, trip => trip.TripStops?.map(stop => stop.Students) || [])
					.filter(c => students.some(x => x.Id === c.id) && c.geometry?.x),
				allCards = allData.map(item =>
				{
					var schoolGrade = item.SchoolCode || "";
					if (item.Grade != null)
					{
						if (schoolGrade)
						{
							schoolGrade += " | ";
						}

						schoolGrade += item.Grade;
					}

					return {
						Id: 0,
						title: item.FirstName + " " + item.LastName,
						subtitle: item.Address,
						type: type,
						whereQuery: "",
						imageSrc: undefined,
						x: item.geometry.x,
						y: item.geometry.y,
						geometry: item.geometry,
						schoolGrade: schoolGrade,
						rightInfo: this.createStudentRightInfo(item)
					};
				});
			return {
				count: allData.length,
				cards: allCards.slice(0, count),
				allCards: allCards,
				whereQuery: "",
				showInName: "Map"
			};
		});
	};

	SearchControlViewModel.prototype.getSuggestedStudents = function(value)
	{
		if (this.suggestedStudents && this.searchText === value)
		{
			return Promise.resolve(this.suggestedStudents);
		}
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "search/students/simple"),
			{
				paramData: {
					text: value,
				}
			}, { overlay: false }).then(res => 
			{
				this.suggestedStudents = res.Items[0]?.SimpleEntities;
				this.searchText = value;
				return this.suggestedStudents;
			});
	}

	SearchControlViewModel.prototype.getSuggestedResultByStopPoolRouting = function(type, value, count)
	{
		value = value.toLowerCase();
		var document = tf.documentManagerViewModel.obCurrentDocument(),
			allData = document.routingPaletteViewModel.stopPoolViewModel.dataModel.all
				.filter(c => c.Street && c.Street.toLowerCase().indexOf(value) >= 0),
			allCards = allData.map(item =>
			{
				return {
					Id: 0,
					title: item.Street,
					subtitle: "",
					type: type,
					whereQuery: "",
					imageSrc: undefined,
					x: item.geometry.x,
					y: item.geometry.y,
					geometry: item.geometry
				};
			});

		return Promise.resolve({
			count: allData.length,
			cards: allCards.slice(0, count),
			allCards: allCards,
			whereQuery: "",
			showInName: "Map"
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultByTripStopRouting = function(type, value, count)
	{
		var document = tf.documentManagerViewModel.obCurrentDocument();
		var tripStops = [];
		document.routingPaletteViewModel.tripViewModel.dataModel.trips.forEach(function(trip)
		{
			tripStops = tripStops.concat(trip.TripStops);
		});
		value = value.toLowerCase();
		var allData = Enumerable.From(tripStops)
			.Where(c => c.Street && c.Street.toLowerCase().indexOf(value) >= 0)
			.Distinct(c => c.Street + ":" + c.geometry.x + ":" + c.geometry.y)
			.ToArray(),
			allCards = allData.map(item =>
			{
				return {
					Id: 0,
					title: item.Street,
					subtitle: "",
					type: type,
					whereQuery: "",
					imageSrc: undefined,
					x: item.geometry.x,
					y: item.geometry.y,
					geometry: item.geometry
				};
			});
		return Promise.resolve({
			count: allData.length,
			cards: allCards.slice(0, count),
			allCards: allCards,
			whereQuery: "",
			showInName: "Map"
		});
	};

	SearchControlViewModel.prototype.getSuggestedResultBySchoolLocation = async function(type, value, count)
	{
		try
		{
			const schoolResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "search", "schools", "simple"),
				{
					paramData: {
						text: value,
					}
				}, { overlay: false });

			if (!schoolResponse.Items[0]?.SimpleEntities || schoolResponse.Items[0]?.SimpleEntities.length === 0) { return; }

			const schools = schoolResponse.Items[0].SimpleEntities,
				allCards = [],
				schoolCodes = schools.map(x => x.SchoolCode),
				schoolLocations = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schoollocations"),
					{
						paramData: {
							"@fields": "Id,Xcoord,Ycoord,SchoolCode,Name",
							"@filter": `in(SchoolCode,${schoolCodes.join(",")})&eq(DBID,${tf.datasourceManager.databaseId})`
						}
					}, { overlay: false });

			function pushToAllCards(school, location)
			{
				var x = location?.Xcoord || school.Xcoord, y = location?.Ycoord || school.Ycoord;
				x && allCards.push({
					Id: location?.Id || school.Id,
					title: school.Title + (location ? (" - " + location.Name) : ""),
					subtitle: school.SubTitle,
					type: type,
					whereQuery: "",
					searchText: value,
					imageSrc: undefined,
					x: x,
					y: y,
					geometry: TF.xyToGeometry(x, y),
				});
			}

			schools.forEach((school) =>
			{
				pushToAllCards(school);
				schoolLocations.Items.filter(x => x.SchoolCode === school.SchoolCode).forEach((location) =>
				{
					pushToAllCards(school, location);
				});
			});

			return {
				type: type,
				count: allCards.length,
				cards: allCards.slice(0, count),
				allCards: allCards,
				searchText: value,
				whereQuery: "",
				showInName: "Map"
			};
		} catch (e)
		{
			console.error(e);
		}
	};

	/**
	 * The dispose function.
	 * @returns {void}
	 */
	SearchControlViewModel.prototype.dispose = function()
	{
		var self = this;
		self.onSearchButtonClickEvent.unsubscribeAll();
		self.onSearchStatusToggle.unsubscribeAll();
		self.onClearRecentSearchEvent.unsubscribeAll();
		self.onSearchSettingsChangedEvent.unsubscribeAll();

		self.inputKeyDownSearching = false;
		self.inputTextSearching = false;
	};

	SearchControlViewModel.prototype.getRecentlyViewed = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'UserSearchResults'), {
			paramData: {
				"DBID": tf.datasourceManager.databaseId,
				"ApplicationId": TF.applicationId
			}
		}).then(response =>
		{
			var items = Array.sortBy(response.Items, "ID", true).map(function(item)
			{
				return JSON.parse(item.SearchResult);
			});

			items = items.filter(item => tf.authManager.isAuthorizedForDataType(item.type));
			items = items.slice(0, this.getRecentSearchViewedCount());
			items.forEach(item =>
			{
				var style = TF.Helper.SearchControlHelper.cardStyle()[item.type];
				item.dataType = style.title;
				item.color = style.color;
				item.searchText = item.title;
			});

			this.obRecentlyViewed(items);
			this.autoAdjustFontSize();
		});
	};
})();
