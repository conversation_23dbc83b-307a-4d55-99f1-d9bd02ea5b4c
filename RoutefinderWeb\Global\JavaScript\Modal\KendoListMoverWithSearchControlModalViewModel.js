﻿(function()
{
	createNamespace('TF.Modal').KendoListMoverWithSearchControlModalViewModel = KendoListMoverWithSearchControlModalViewModel;

	function KendoListMoverWithSearchControlModalViewModel(selectedData, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = 'modal-dialog-lg listmover';
		this.title(options.title);
		this.description = options.description;
		let isDashboard = window.location.pathname.indexOf('dashboard.html') >= 0;
		let template = options.contentTemplate || "modal/KendoListMoverWithSearchControl";
		template = (isDashboard && !tf.isViewfinder) ? ('en-US/Html/' + template) : template;
		this.contentTemplate(template);
		this.buttonTemplate(((isDashboard && !tf.isViewfinder) ? 'en-US/Html/' : '') + 'modal/positivenegative');
		this.obPositiveButtonLabel("Apply");
		this.obResizable(false);
		this.obEnableEnter(false);
		//this.kendoListMoverWithSearchControlViewModel = new TF.Control.KendoListMoverWithSearchControlViewModel(selectedData, options);
		//this.data(this.kendoListMoverWithSearchControlViewModel);
	}

	KendoListMoverWithSearchControlModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	KendoListMoverWithSearchControlModalViewModel.prototype.constructor = KendoListMoverWithSearchControlModalViewModel;


	KendoListMoverWithSearchControlModalViewModel.prototype.dispose = function()
	{
		this.data().dispose();
	};
})();
