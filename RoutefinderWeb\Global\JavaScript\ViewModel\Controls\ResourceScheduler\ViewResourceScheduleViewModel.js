﻿(function()
{
	createNamespace('TF.Control.ResourceScheduler').ViewResourceScheduleViewModel = ViewResourceScheduleViewModel;

	function ViewResourceScheduleViewModel(resourceEntity, resourceType, trips, fieldtrips, restrictions)
	{
		this.trips = trips;
		this.fieldtrips = fieldtrips;
		this.restrictions = restrictions;

		this.obResourceType = ko.observable(resourceType);
		this.obName = ko.observable("");
		this.obDescription1Title = ko.observable("");
		this.obDescription2Title = ko.observable("");
		this.obScheduleItems = ko.observableArray([]);
		this.startTimeOrder = 0; // 0: normal, -1: desc,  1: asc
		this.endTimeOrder = 0;
		this.momentHelper = new TF.Document.MomentHelper();

		switch (resourceType)
		{
			case "drivers":
				this.obName(resourceEntity.DriverName);
				break;
			case "aides":
				this.obName(resourceEntity.AideName);
				break;
			case "vehicles":
				this.obName(resourceEntity.VehicleName);
				this.obCapacity = ko.observable(resourceEntity.Capacity);
				this.obWC_Capacity = ko.observable(resourceEntity.WC_Capacity);
				this.obYearMade = ko.observable(resourceEntity.YearMade);
				this.obMakeBody = ko.observable(resourceEntity.MakeBody);
				this.obModel = ko.observable(resourceEntity.Model);
				break;
		}

		this.initScheduleItems();
	}

	ViewResourceScheduleViewModel.prototype.initScheduleItems = function()
	{
		var scheduleItems, scheduleItemsSource = this.trips.concat(this.fieldtrips).concat(this.restrictions),
			description1FieldName, description2FieldName, typeName;

		switch (this.obResourceType())
		{
			case "drivers":
				description1FieldName = "VehicleName";
				description2FieldName = "AideName";
				this.obDescription1Title("Vehicle");
				this.obDescription2Title("Aide Name");
				break;
			case "aides":
				description1FieldName = "DriverName";
				description2FieldName = "VehicleName";
				this.obDescription1Title("Driver Name");
				this.obDescription2Title("Vehicle");
				break;
			case "vehicles":
				description1FieldName = "DriverName";
				description2FieldName = "AideName";
				this.obDescription1Title("Driver Name");
				this.obDescription2Title("Aide Name");
				break;
		}

		scheduleItems = scheduleItemsSource.map(item =>
		{
			switch (item.Type.toLowerCase())
			{
				case "trip":
					typeName = "Trip";
					break;
				case "fieldtrip":
					typeName = "Field Trip";
					break;
				case "restriction":
					typeName = "Restriction";
					break;
			}

			return {
				Type: typeName,
				Name: item.Name,
				StartTime: this.momentHelper.toString(item.StartTime, "hh:mm A"),
				EndTime: item.EndTime ? this.momentHelper.toString(item.EndTime, "hh:mm A") : 'None',
				Description1: item.Type !== "restriction" ? item[description1FieldName] : "",
				Description2: item.Type !== "restriction" ? item[description2FieldName] : ""
			};
		}).sort(function(a, b)
		{
			if (a.StartTime === b.StartTime)
			{
				return a.Name.localeCompare(b.Name, 'en');
			}

			var timeA = moment(a.StartTime, 'hh:mm A'),
				timeB = moment(b.StartTime, 'hh:mm A');
			return new Date(timeA) - new Date(timeB);
		});

		this.obScheduleItems(scheduleItems);
	};

	ViewResourceScheduleViewModel.prototype.sortTime = function(isStart)
	{
		let self = this, scheduleItems = self.obScheduleItems(), sortedScheduleItems,
			currentTime = isStart ? 'StartTime' : 'EndTime', currentOrder = 0;

		currentOrder = self.getTimeOrder(isStart);

		sortedScheduleItems = scheduleItems.sort(function(a, b)
		{
			if (a[currentTime] === b[currentTime])
			{
				return a.Name.localeCompare(b.Name, 'en');
			}
			var timeA = moment(a[currentTime], 'hh:mm A'),
				timeB = moment(b[currentTime], 'hh:mm A');

			if (currentOrder === 0 || currentOrder === 1)
			{
				return new Date(timeA) - new Date(timeB);
			} else
			{
				return new Date(timeB) - new Date(timeA);
			}

		});
		self.obScheduleItems(sortedScheduleItems);
	};


	ViewResourceScheduleViewModel.prototype.getTimeOrder = function(isStart)
	{
		let self = this, currentTimeOrder = isStart ? 'startTimeOrder' : 'endTimeOrder',
			startOrderIconEle = $(".schedule-items-container .starttime .k-icon"),
			endOrderIconEle = $(".schedule-items-container .endtime .k-icon"),
			currentTimeEle = isStart ? startOrderIconEle : endOrderIconEle,
			otherTimeEle = isStart ? endOrderIconEle : startOrderIconEle,
			nextOrder = 0;
		currentTimeEle.removeClass().addClass("k-icon");
		otherTimeEle.removeClass().addClass("k-icon");
		switch (self[currentTimeOrder])
		{
			case 0:
				nextOrder = 1;
				currentTimeEle.addClass("k-icon k-i-sort-asc-sm");
				break;
			case 1:
				currentTimeEle.addClass("k-icon k-i-sort-desc-sm");
				nextOrder = -1;
				break;
			case -1:
				nextOrder = 0;
				break;
		}
		self[currentTimeOrder] = nextOrder;
		return nextOrder;
	};
})();