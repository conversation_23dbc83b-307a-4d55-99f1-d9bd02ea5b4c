﻿(function()
{
	createNamespace('TF.Control').AdjustTripStopTimeViewModel = AdjustTripStopTimeViewModel;

	function AdjustTripStopTimeViewModel()
	{
		this.obHandler = ko.observable(0);
		this.obAmount = ko.observable(0);
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	AdjustTripStopTimeViewModel.prototype.init = function(viewModel, el)
	{
		if (!this.$form)
		{
			this.$form = $(el);

			var validatorFields = {}, isValidating = false, self = this;

			validatorFields.amount = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					}
				}
			}

			$(el).bootstrapValidator({
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

			this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
			this.$form.find("select[name=amount]").focus();

			this.load();
		}
	};

	AdjustTripStopTimeViewModel.prototype.load = function()
	{

	}

	AdjustTripStopTimeViewModel.prototype.apply = function()
	{
		return this.save()
		.then(function(data)
		{
			return data;

		}, function()
		{
		});
	};

	AdjustTripStopTimeViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate()
		.then(function(result)
		{
			if (result)
			{
				return { "handle": this.obHandler(), "amount": this.obAmount() };
			}
		}.bind(this));
	};

	AdjustTripStopTimeViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();