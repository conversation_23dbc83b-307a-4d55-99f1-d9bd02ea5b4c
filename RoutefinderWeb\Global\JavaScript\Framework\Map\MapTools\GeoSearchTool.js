﻿(function()
{
	const ACTION_BUTTON_ENUM = {
		Apply: 'Apply',
		Cancel: 'Cancel',
		Clear: 'Clear',
		Exit: 'Exit',
	}

	createNamespace("TF.Map").GeoSearchTool = GeoSearchTool;

	function GeoSearchTool(gridMap, routingMapTool)
	{
		var self = this;
		self.routingMapTool = routingMapTool;
		self._drawTool = null;
		self._drawPolygonLayer = null;
		self._drawDeleteMarkerLayer = null;
		self._startNodeMarker = null;
		self.map = gridMap._map;
		self.isMoveOnDeleteMark = false;
		self.$container = $(self.map.mapView.container);
		self.gridMap = gridMap;
		if (self.gridMap._grid)
		{
			self.grid = self.gridMap._grid;
		}
		else
		{
			self.grid = (gridMap.parentDocument && gridMap.parentDocument.gridViewModel.searchGrid) || gridMap.searchGrid;
		}
		self._idx = 0;
		self._geoSearchPolygons = {};
		self._appliedPolygonIndices = [];
		self._changes = [];

		self.onGeoSearchStart = new TF.Events.Event();
		self.onGeoSearchEnd = new TF.Events.Event();
		self.drawCompleted = new TF.Events.Event();
		self.clearGeoSearch = new TF.Events.Event();

		self.initToolbar();

		if (self.grid)
		{
			self.onFilterWithGeoSearchSubKey && self.grid.onFilterWithGeoSearch.unsubscribe(self.onFilterWithGeoSearchSubKey);
			self.onFilterWithGeoSearchSubKey = self.grid.onFilterWithGeoSearch.subscribe((e, data) =>
			{
				var viewModel = tf.isViewfinder ? self.gridMap : (self.gridMap.splitMapViewModel || self.gridMap);
				if (viewModel && viewModel.loadDataWithGeoSearchPolygon)
				{
					var polygons = self.getPolygonsToBeApplied().map(function(item)
					{
						return item.geometry;
					});

					viewModel.loadDataWithGeoSearchPolygon(data.dataIds, polygons, data.callback);
				}
			}).key;
		}
	}

	GeoSearchTool.prototype = Object.create(TF.Map.GeoSearchTool.prototype);
	GeoSearchTool.prototype.constructor = TF.Map.GeoSearchTool;

	/**
	* Initialize this toolbar.
	* @return {None}
	*/
	GeoSearchTool.prototype.initToolbar = function()
	{
		var self = this;
		self._initElements();
		self._initHelper();
		self._initLayers();
		self._initDrawTool();
	};

	/**
	* Initialize the layers to be used.
	* @return {None}
	*/
	GeoSearchTool.prototype._initLayers = function()
	{
		var self = this;
		self.initDrawPolygonLayer();
		self.initDeleteMarkerLayer();
	};

	/**
	* Initialize the helpers.
	* @return {None}
	*/
	GeoSearchTool.prototype._initHelper = function()
	{
		var self = this,
			arcgis = tf.map.ArcGIS;

		self._arcgis = arcgis;

		self._geoSearchPolygonSymbol = {
			type: "simple-fill",
			color: [18, 89, 208, 0.3],
			outline: {
				color: [18, 89, 208, 0.6],
				width: 1,
			}
		};

		self._geoSearchPolylineSymbol = {
			type: "simple-line",
			color: [18, 89, 208, 0.6],
			width: 0.75
		};

		self._highlightPolygonSymbol = {
			type: "simple-fill",
			color: [18, 89, 208, 0.3],
			outline: {
				color: [18, 89, 208, 0.6],
				width: 3,
			}
		};

		self._geoSearchDeleteMarkerSymbol = {
			type: "picture-marker",
			url: (tf && (tf.isViewfinder || tf.dashboardViewMode)) ? "global/img/map/view/delete-polygon.png" : "../../global/img/map/view/delete-polygon.png",
			width: "16px",
			height: "16px"
		};
	};

	/**
	* Initialize the required elements for the Geo-Search toolbar.
	* @return {None}
	*/
	GeoSearchTool.prototype._initElements = function()
	{
		var self = this;

		self.toolbarBottom = $("<div></div>", { class: "esri-map-toolbar-bottom" });

		self._applyBtn = $("<div></div>", { class: "toolbar-btn white apply-btn inactive", text: ACTION_BUTTON_ENUM.Apply });
		self._cancelBtn = $("<div></div>", { class: "toolbar-btn black close-btn inactive", text: ACTION_BUTTON_ENUM.Cancel });
		self._exitBtn = $("<div></div>", { class: "toolbar-btn black exit-btn", text: ACTION_BUTTON_ENUM.Exit });

		self._applyBtn.bind("click", self._applyBtnClick.bind(self));
		self._cancelBtn.bind("click", self._cancelBtnClick.bind(self));
		self._exitBtn.bind("click", self._exitBtnClick.bind(self));

		self.toolbarBottom.append(self._applyBtn);
		self.toolbarBottom.append(self._cancelBtn);
		self.toolbarBottom.append(self._exitBtn);
		let grid = self.$container.closest(".grid");
		if (window.isMobileDevice() && grid.length > 0)
		{
			grid.append(self.toolbarBottom);
		}
		else
		{
			self.$container.append(self.toolbarBottom);
		}
	};

	/**
	* The handler for Clear-All button.
	* @return {None}
	*/
	GeoSearchTool.prototype.clearAllBtnClick = function()
	{
		var self = this;
		if (self.gridMap.applyGeoSearchResult && self.isPolygonOnMap())
		{
			tf.promiseBootbox.yesNo("Are you sure you want to clear your shapes", "Clear Shapes")
				.then(function(response)
				{
					if (response)
					{
						self.clearSearchResults();
					}
				});
		}
	};

	/**
	* The handler for Apply button.
	* @return {None}
	*/
	GeoSearchTool.prototype._applyBtnClick = function()
	{
		var self = this;
		if (isMobileDevice())
		{
			if (self.isPolygonOnMap())
			{
				self.drawCompleted.notify();
				self.applySearch();
			}
			else
			{
				tf.promiseBootbox.alert("You have not drawn any shapes.", "Warning");
			}
		}
		else
		{
			if (!self._applyBtn.hasClass("inactive"))
			{
				self.applySearch();
				self._inactivateBtn(self._applyBtn);
				self._cancelBtn.text(ACTION_BUTTON_ENUM.Clear);
				if (self.grid)
				{
					self.grid.disable(false);
				}
			}
		}
	};

	/**
	* The handler for Cancel button.
	* @return {None}
	*/
	GeoSearchTool.prototype._cancelBtnClick = function()
	{
		var self = this;

		if (!isMobileDevice())
		{
			if (self._cancelBtn.hasClass("inactive"))
			{
				return;
			}

			if (self._cancelBtn.text() === ACTION_BUTTON_ENUM.Clear)
			{
				self.clearSearchResults();
				self.clearGeoSearch.notify();
				self._cancelBtn.text(ACTION_BUTTON_ENUM.Cancel);
				self._inactivateBtn(self._cancelBtn);
				self._activateDrawTool();
				return;
			}
		}

		if (self.isUnsavedChange())
		{
			tf.promiseBootbox.yesNo("Are you sure you want to cancel changes?", "Confirmation Message")
				.then(function(response)
				{
					if (response)
					{
						self.drawCompleted.notify();
						self.cancelGeoSearchChanges();
					}
				});
		}
		else
		{
			self.drawCompleted.notify();
			self.applySearch();
		}
	};

	GeoSearchTool.prototype.clearSearchResults = function()
	{
		var self = this;
		self._clearAllAction();
		self._cancelBtn.text(ACTION_BUTTON_ENUM.Cancel);
		self._inactivateBtn(self._cancelBtn);
		if (self.isGeoSearching())
		{
			self._changes = [];
		}
		else
		{
			self.clearGeoSearch.notify();
		}
	};

	/**
	 * Cancel unsaved GeoSearch changes.
	 * @return {void}
	 */
	GeoSearchTool.prototype.cancelGeoSearchChanges = function()
	{
		var self = this;
		self.routingMapTool.stopSketch("geoSearchTool");
		self.cancelUnsavedChange();
		self.applySearch();
		if (!isMobileDevice())
		{
			self._inactivateBtn(self._applyBtn);
			if (Object.keys(self._geoSearchPolygons).length > 0)
			{
				self._cancelBtn.text(ACTION_BUTTON_ENUM.Clear);
			}
			else
			{
				self._cancelBtn.text(ACTION_BUTTON_ENUM.Cancel);
				self._inactivateBtn(self._cancelBtn);
			}
		}
	};

	/**
	* The click handler for Exit button.
	* @return {None}
	*/
	GeoSearchTool.prototype._exitBtnClick = function()
	{
		var self = this, confirmMsg = "";

		if (self.isPolygonOnMap())
		{
			confirmMsg = "One or more areas are drawn on the Map. If you exit, these shapes will be removed and the Geo Search Filter will be removed from the map. Are you sure you want to exit?";
		}
		else if (self.isUnsavedChange())
		{
			confirmMsg = "Are you sure you want to exit?";
		}

		if (confirmMsg)
		{
			tf.promiseBootbox.yesNo(confirmMsg, "Confirmation Message")
				.then(function(response)
				{
					if (response)
					{
						self.endGeoSearch(true);
						self.clearSearchResults();
					}
				});
		}
		else
		{
			self.endGeoSearch(true);
		}
	};

	/**
	* Start the Geo-Search.
	* @return {None}
	*/
	GeoSearchTool.prototype.startGeoSearch = function()
	{
		var self = this;
		self.routingMapTool.startSketch("geoSearchTool");
		if (self.grid)
		{
			self.grid.isGeoSearching = true;
		}
		if (self.isGeoSearching())
		{
			if (isMobileDevice())
			{
				self._activateDrawTool();
			}
			else
			{
				self._cancelBtnClick();
			}
		}
		else
		{
			self._changes = [];

			self.displayGeoSearchButton();
			if (self.grid)
			{
				self.grid.disableShortCutKey = true;
			}

			self._activateDrawTool();
			self._drawDeleteMarkerLayer.visible = true;

			if (self.grid)
			{
				self.grid.disable(true);
			}

			self._bindMapMouseEvent();
			self.onGeoSearchStart.notify();
		}
	};

	/**
	* Activate the draw tool.
	* @return {None}
	*/
	GeoSearchTool.prototype.activateDrawTool = function()
	{
		// Used For ViewFinder
		this._activateDrawTool();
	}

	/**
	* Cancel the draw tool.
	* @return {None}
	*/
	GeoSearchTool.prototype.cancelDrawTool = function()
	{
		// Used For ViewFinder
		if (this._sketchVM) this._sketchVM.cancel();
	}

	/**
	* Activate the draw tool.
	* @return {None}
	*/
	GeoSearchTool.prototype._activateDrawTool = function()
	{
		if (isMobileDevice())
		{
			this._sketchVM.create("polygon", { mode: "freehand" });
		}
		else
		{
			this._sketchVM.create("polygon", { mode: "click" });
		}
	};

	/**
	* Display extra Geo-Search buttons.
	* @return {None}
	*/
	GeoSearchTool.prototype.displayGeoSearchButton = function()
	{
		this.toolbarBottom.addClass("active");
		$(this.map.mapView.container).find(".esri-ui .esri-attribution").css("bottom", 60);
		this.displayMobileGeoSearchButton();
	};

	/**
	* Hide extra Geo-Search buttons.
	* @return {None}
	*/
	GeoSearchTool.prototype.hideGeoSearchButton = function()
	{
		this.toolbarBottom.removeClass("active");
		$(this.map.mapView.container).find(".esri-ui .esri-attribution").css("bottom", 0);
		this.hideMobileGeoSearchButton();
	};

	/**
	* Display extra Geo-Search buttons in mobile.
	* @return {None}
	*/
	GeoSearchTool.prototype.displayMobileGeoSearchButton = function()
	{
		// Used For ViewFinder
	};

	/**
	* Hide extra Geo-Search buttons in mobile.
	* @return {None}
	*/
	GeoSearchTool.prototype.hideMobileGeoSearchButton = function()
	{
		// Used For ViewFinder
	};

	/**
	* End the Geo-Search.
	* @return {None}
	*/
	GeoSearchTool.prototype.endGeoSearch = function(flag, notDeactive)
	{
		var self = this;
		if (self.grid)
		{
			self.grid.disable(false);
		}
		self._unBindMapMouseEvent();
		if (self._drawDeleteMarkerLayer)
		{
			self._drawDeleteMarkerLayer.visible = false;
		}
		self.hideGeoSearchButton();
		self._cancelBtn.text(ACTION_BUTTON_ENUM.Cancel);
		TF.Helper.MapHelper.setMapCursor(self.map, "default");
		if (self.gridMap.enableMouseEvent)
		{
			self.gridMap.enableMouseEvent();
		}
		if (self.grid)
		{
			self.grid.disableShortCutKey = false;
			self.grid.isGeoSearching = false;
		}

		if (flag)
		{
			self._drawPolygonLayer.removeAll();
			if (self._drawDeleteMarkerLayer)
			{
				self._drawDeleteMarkerLayer.removeAll();
			}
			self._inactivateBtn(self._applyBtn);
			self._inactivateBtn(self._cancelBtn);
			self._appliedPolygonIndices = [];
			self._geoSearchPolygons = {};
		}

		if (self._sketchVM && !notDeactive) { self._sketchVM.cancel(); }

		self.onGeoSearchEnd.notify();
	};

	/**
	* Apply the Geo-Search.
	* @return {None}
	*/
	GeoSearchTool.prototype.applySearch = function()
	{
		var self = this,
			indices = Object.keys(self._geoSearchPolygons);
		if (self.gridMap.applyGeoSearchResult && indices.length > 0)
		{
			var polygons = self.getPolygonsToBeApplied().map(function(item)
			{
				return item.geometry;
			});

			if (isMobileDevice() && self._sketchVM) { self._sketchVM.cancel(); }

			self.gridMap.applyGeoSearchResult(polygons).then(function()
			{
				self._appliedPolygonIndices = indices;
				if (isMobileDevice())
				{
					self.endGeoSearch(false, true);
				} else
				{
					self._changes = [];
				}
			});
			self._sketchVM && (self._sketchVM.cancel());
		}
		else
		{
			self.endGeoSearch(false);
		}
	};

	GeoSearchTool.prototype.forceExit = function()
	{
		const needClearResult = this.needClearResult();
		this.endGeoSearch(true);
		needClearResult && this.clearSearchResults();
	}

	GeoSearchTool.prototype.needClearResult = function()
	{
		return this.isPolygonOnMap() || this.isUnsavedChange();
	}

	/**
	* Clear all polygons and relevant features.
	* @return {None}
	*/
	GeoSearchTool.prototype._clearAllAction = function()
	{
		var self = this,
			indices = Object.keys(self._geoSearchPolygons);
		self._deletePolygonByIndices(indices);
	};

	/**
	* Initialize the map tool.
	* @return {None}
	*/
	GeoSearchTool.prototype._initDrawTool = function()
	{
		var self = this;
		self._sketchVM = new self._arcgis.SketchViewModel({
			view: self.map.mapView,
			layer: self._drawPolygonLayer,
			updateOnGraphicClick: false,
			defaultUpdateOptions: { // set the default options for the update operations
				toggleToolOnClick: false // only reshape operation will be enabled
			},
			polygonSymbol: self._geoSearchPolygonSymbol,
			polylineSymbol: self._geoSearchPolylineSymbol
		});

		self._sketchVM.on("create", function(e)
		{
			if (e.state == "start")
			{
				if (TF.isMobileDevice)
				{
					var symbol = e.graphic.symbol;
					symbol.color = [18, 89, 208, 0.3];
					symbol.outline.color = [18, 89, 208, 0.6];
				}
				if (self._drawDeleteMarkerLayer && !self.isMoveOnDeleteMark)
				{
					self._drawDeleteMarkerLayer.visible = false;
				}
			}
			if (e.state == "complete")
			{
				if (e.graphic)
				{
					self._drawDeleteMask(e.graphic);
					if (!isMobileDevice())
					{
						self._activateDrawTool();
					}
					else
					{
						self.drawCompleted.notify();
					}
				}

				if (self._drawDeleteMarkerLayer && self.isGeoSearching()) { self._drawDeleteMarkerLayer.visible = true; }
			}
		});
	};

	/**
	* bind mouse events to map
	*/
	GeoSearchTool.prototype._bindMapMouseEvent = function()
	{
		var self = this;

		if (isMobileDevice())
		{
			self._pointerDownEvent = self.map.mapView.on("pointer-up", function(event)
			{
				self._hitTestOnGeoSearchLayer(event).then(function(response)
				{
					self._hitDeleteMarkerTest(response);
					self._DeleteMarker(event);
				});
			});
		}
		else
		{
			self._pointerMoveEvent = self.map.mapView.on("pointer-move", function(event)
			{
				self._hitTestOnGeoSearchLayer(event).then(function(response)
				{
					self._hitDeleteMarkerTest(response);
				});
			});

			self._pointerDownEvent = self.map.mapView.on("pointer-up", function(event)
			{
				self._DeleteMarker(event);
			});
		}

		self._keyDownEvent = self.map.mapView.on("key-down", function(event)
		{
			if (event.key == "Escape")
			{
				// griddata/dashboardcavas is not able to continue drawing because it _activateDrawTool() before arcgis inbuild esc() event，inbuild esc() event supposed to  happen before ；
				// why use setTimeout : because dashboardcavas and griddata both call this function; the setTimeout change the order of event excuted;1.arcgis inbuild esc() event;2._activateDrawTool()
				// restore draw tool
				setTimeout(() =>
				{
					self._activateDrawTool();
				}, 200);
			}
		});
	};

	GeoSearchTool.prototype._hitTestOnGeoSearchLayer = function(event)
	{
		var mapPoint = this.map.mapView.toMap(event);
		return Promise.all(this.map.mapView.allLayerViews.items.filter(x => x.layer.id == this._drawDeleteMarkerLayer.id
			|| x.layer.id == this._drawPolygonLayer.id).reverse().map(x => x.hitTest(mapPoint, event))).then(function(response)
			{
				return {
					screenPoint: event,
					results: response.flat()
				}
			});
	};

	GeoSearchTool.prototype._hitDeleteMarkerTest = function(response)
	{
		var self = this;
		var isOnDrawPolygonLayer = false,
			isOnDeleteMaskLayer = false,
			graphic;
		if (response.results.length)
		{
			for (var i = 0; i < response.results.length; i++)
			{
				graphic = response.results[i].graphic;
				if (graphic.layer == self._drawDeleteMarkerLayer)
				{
					isOnDeleteMaskLayer = true;
					self._pointerOverDeleteMarkerLayer(graphic);
					break;
				}
				if (graphic.layer == self._drawPolygonLayer)
				{
					isOnDrawPolygonLayer = true;
					self._pointerOverDrawPolygonLayer(graphic);
					break;
				}
			}
		}
		if (!isOnDeleteMaskLayer)
		{
			self._pointerOutDeleteMarkerLayer();
			self.isMoveOnDeleteMark = false;
		}
		else
		{
			self.isMoveOnDeleteMark = true;
		}
		if (!isOnDrawPolygonLayer)
		{
			self._pointerOutDrawPolygonLayer();
		}
	};

	GeoSearchTool.prototype._DeleteMarker = function(event)
	{
		var self = this;
		// click delete mask to delete polygon
		if (self._prevPointerOverDeleteMarkerGraphic)
		{
			event.stopPropagation();
			// reactive sketch tool
			if (self._sketchVM.state == "active")
			{
				self._sketchVM.cancel();
				setTimeout(function()
				{
					self._activateDrawTool();
				}, 20);
			}

			if (self._deleteMarkerTimeout)
			{
				clearTimeout(self._deleteMarkerTimeout);
			}

			self._deleteMarkerTimeout = setTimeout(function()
			{
				if (!self._prevPointerOverDeleteMarkerGraphic ||
					!self._prevPointerOverDeleteMarkerGraphic.attributes ||
					!self._prevPointerOverDeleteMarkerGraphic.attributes.idx)
				{
					return;
				}
				self._deletePolygonByIndices([self._prevPointerOverDeleteMarkerGraphic.attributes.idx]);
			}, 100);
		}
	};

	GeoSearchTool.prototype._unBindMapMouseEvent = function()
	{
		this._pointerMoveEvent && this._pointerMoveEvent.remove();
		this._pointerDownEvent && this._pointerDownEvent.remove();
		this._keyDownEvent && this._keyDownEvent.remove();
		this._pointerMoveEvent = null;
		this._pointerDownEvent = null;
		this._keyDownEvent = null;
	};

	/**
	* Initialize the layer for the Polygons as well as events.
	* @return {None}
	*/
	GeoSearchTool.prototype.initDrawPolygonLayer = function()
	{
		var self = this,
			GraphicsLayer = self._arcgis.GraphicsLayer;

		self._drawPolygonLayer = new GraphicsLayer({ id: "geoSearchDrawPolygonLayer" });
		self.map.add(self._drawPolygonLayer, 1);
	};

	GeoSearchTool.prototype._pointerOverDrawPolygonLayer = function(graphic)
	{
		graphic.symbol = this._highlightPolygonSymbol;
		this._prevPointerOverDrawPolygonGraphic = graphic;
	};

	GeoSearchTool.prototype._pointerOutDrawPolygonLayer = function()
	{
		if (this._prevPointerOverDrawPolygonGraphic)
		{
			this._prevPointerOverDrawPolygonGraphic.symbol = this._geoSearchPolygonSymbol;
			this._prevPointerOverDrawPolygonGraphic = null;
		}
	};

	/**
	* Initialize the layer for the Delete-Markers on top-right of every polygon as well as events.
	* @return {None}
	*/
	GeoSearchTool.prototype.initDeleteMarkerLayer = function()
	{
		var self = this,
			GraphicsLayer = self._arcgis.GraphicsLayer;

		self._drawDeleteMarkerLayer = new GraphicsLayer({ id: "geoSearchDrawDeleteMarkerLayer" });
		self.map.add(self._drawDeleteMarkerLayer, 12);
	};

	GeoSearchTool.prototype._pointerOverDeleteMarkerLayer = function(graphic)
	{
		TF.Helper.MapHelper.setMapCursor(this.map, "pointer");
		this._prevPointerOverDeleteMarkerGraphic = graphic;
	};

	GeoSearchTool.prototype._pointerOutDeleteMarkerLayer = function()
	{
		if (this._sketchVM.state !== "active")
		{
			TF.Helper.MapHelper.setMapCursor(this.map, "default");
		}
		else
		{
			TF.Helper.MapHelper.setMapCursor(this.map, "crosshair");
		}
		this._prevPointerOverDeleteMarkerGraphic = null;
	};

	/**
	* Draw a polygon and related things with draw-end event arguments.
	* @param {object} geometry - drawed geometry.
	* @return {None}
	*/
	GeoSearchTool.prototype._drawDeleteMask = function(graphic)
	{
		if (!graphic) { return; }

		var self = this;
		self._idx++;

		var Graphic = self._arcgis.Graphic,
			northeasternPt = self._getNortheastNodePosition(graphic.geometry),
			markerSymbol = self._geoSearchDeleteMarkerSymbol,
			deleteMarker = new Graphic({ geometry: northeasternPt, symbol: markerSymbol, attributes: { idx: self._idx } }),
			action = { tasks: [{ idx: self._idx, polygon: graphic, marker: deleteMarker }], isAdd: true };

		self._conductAction(action);
	};

	/**
	* Conduct an action with specified tasks.
	* @param {object} action - a customized action format might include several tasks, a task should include the index, polygon and delete-marker.
	* @return {None}
	*/
	GeoSearchTool.prototype._conductAction = function(action)
	{
		var self = this;
		action.tasks.forEach(function(task)
		{
			if (action.isAdd)
			{
				self._geoSearchPolygons[task.idx] = { polygon: task.polygon, marker: task.marker };
				var inGraphics = self._drawPolygonLayer.graphics.items.filter(function(graphic) { return graphic == task.polygon; }).length > 0;
				if (!inGraphics)
				{
					self._drawPolygonLayer.add(task.polygon);
				}
				self._drawDeleteMarkerLayer.add(task.marker);
			}
			else if (task.idx)
			{
				delete self._geoSearchPolygons[task.idx];
				self._drawPolygonLayer.remove(task.polygon);
				self._drawDeleteMarkerLayer.remove(task.marker);
			}
		});

		if (Object.keys(self._geoSearchPolygons).length === 0)
		{
			self._inactivateBtn(self._applyBtn);
			if (!isMobileDevice())
			{
				self._cancelBtn.text(ACTION_BUTTON_ENUM.Clear);
			}
		}
		else
		{
			self._activateBtn(self._applyBtn);
			if (!isMobileDevice())
			{
				self._activateBtn(self._cancelBtn);
				self._cancelBtn.text(ACTION_BUTTON_ENUM.Cancel)
			}
		}

		self._changes.push(action);
	};

	/**
	* Get the northeastern vertex in a polygon.
	* @param {Polygon(arcgis)} polygon - given polygon
	* @return {Point(arcgis)} the northeastern vertex of the polygon
	*/
	GeoSearchTool.prototype._getNortheastNodePosition = function(polygon)
	{
		var self = this,
			Point = self._arcgis.Point,
			extent = polygon.extent,
			northeastBound = new Point(extent.center.x, extent.center.y, extent.spatialReference);
		return northeastBound;
	};

	/**
	* Activate the specified button.
	* @param {$} $btn - the jquery instance of the button element
	* @return {None}
	*/
	GeoSearchTool.prototype._activateBtn = function($btn)
	{
		$btn.removeClass("inactive");
	};

	/**
	* Deactivate the specified button.
	* @param {$} $btn - the jquery instance of the button element
	* @return {None}
	*/
	GeoSearchTool.prototype._inactivateBtn = function($btn)
	{
		if (!$btn.hasClass("inactive"))
		{
			$btn.addClass("inactive");
		}
	};

	/**
	* Delete the polygons with given indices.
	* @param {array<int>} indices - the array of polygon indices.
	* @return {None}
	*/
	GeoSearchTool.prototype._deletePolygonByIndices = function(indices)
	{
		var self = this,
			action,
			tasks = [];

		indices.forEach(function(idx)
		{
			var geoSearchPolygon = self._geoSearchPolygons[idx];
			if (!geoSearchPolygon)
			{
				return true;
			}

			var marker = geoSearchPolygon.marker;
			var polygon = geoSearchPolygon.polygon;
			tasks.push({ idx: idx, polygon: polygon, marker: marker });
			delete self._geoSearchPolygons[idx];
		});

		if (!tasks.length)
		{
			return;
		}

		action = { tasks: tasks, isAdd: false };

		self._conductAction(action);
	};

	/**
	* Check there is geo search result applied onto the map.
	* @return {boolean} whether any geo search result is applied.
	*/
	GeoSearchTool.prototype.hasGeoSearchPolygon = function()
	{
		var self = this;
		return self._appliedPolygonIndices && self._appliedPolygonIndices.length > 0;
	};

	/**
	* Indicate whether the geo search is taking place.
	*
	* @return {boolean} whether the geo searching is taking place.
	*/
	GeoSearchTool.prototype.isGeoSearching = function()
	{
		return this.toolbarBottom.hasClass("active");
	};

	/**
	* Get the polygons to be applied.
	*
	* @return {Polygon(arcgis)} the polygons that are currently in stack to be applied
	*/
	GeoSearchTool.prototype.getPolygonsToBeApplied = function()
	{
		var self = this, polygons = [];
		for (var idx in self._geoSearchPolygons)
		{
			polygons.push(self._geoSearchPolygons[idx].polygon);
		}
		return polygons;
	};

	/**
	* Cancel unsaved changes.
	*
	* @return {None}
	*/
	GeoSearchTool.prototype.cancelUnsavedChange = function()
	{
		var self = this, idx, action,
			unsavedChanges = self._changes;

		for (idx = unsavedChanges.length; idx > 0; idx--)
		{
			action = unsavedChanges[idx - 1];
			action.isAdd = !action.isAdd;
			self._conductAction(action);
		}

		self._changes = [];
	};

	/**
	* Indicate whether there is unsaved change
	*
	* @return {boolean} whether there is unsaved change.
	*/
	GeoSearchTool.prototype.isUnsavedChange = function()
	{
		return this._changes.length;
	};

	GeoSearchTool.prototype.isPolygonOnMap = function()
	{
		return this._drawPolygonLayer && this._drawPolygonLayer.graphics.length > 0;
	};

	/**
	* The method to dispose Geo-Search toolbar.
	*
	* @return {None}
	*/
	GeoSearchTool.prototype.dispose = function()
	{
		var self = this;
		self.onFilterWithGeoSearchSubKey && self.grid?.onFilterWithGeoSearch?.unsubscribe(self.onFilterWithGeoSearchSubKey);
		TF.Helper.MapHelper.setMapCursor(self.map, "default");
		self._sketchVM && self._sketchVM.cancel();
		self.grid = null;
		self._sketchVM = null;
		self._drawPolygonLayer = null;
		self._drawDeleteMarkerLayer = null;
		self._startNodeMarker = null;
		self._idx = null;
		self._geoSearchPolygons = null;
		self._appliedPolygonIndices = null;
		self._changes = null;
	};
})();
