(function()
{
	createNamespace("TF.Modal").FilterScheduleModalViewModel = FilterScheduleModalViewModel;

	function FilterScheduleModalViewModel(options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.sizeCss = "modal-dialog-sm";
		self.title("Filter " + options && options.title + " Schedules");
		self.helpKey = options.type;
		self.contentTemplate("Modal/FilterSchedule");
		this.buttonTemplate('modal/positivenegative');
		self.model = new TF.Fragment.FilterScheduleViewModel(options && options.type);
		self.data(self.model);
	}

	FilterScheduleModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	FilterScheduleModalViewModel.prototype.constructor = FilterScheduleModalViewModel;

	FilterScheduleModalViewModel.prototype.positiveClick = function()
	{
		this.model.apply().then(result =>
		{
			if (result)
			{
				this.positiveClose(result);
			}
		});
	};

	FilterScheduleModalViewModel.prototype.dispose = function()
	{
		this.model.dispose();
		if (this.tooltip)
		{
			this.tooltip.dispose();
			this.tooltip = null;
		}
	};
})();