(function()
{
	createNamespace('TF.Control').ImportStatusViewModel = ImportStatusViewModel;

	const PARTIAL_LOG_HINT = "This is a partial log. Download the file to see the full log.";

	function ImportStatusViewModel(importStatus)
	{
		var self = this;
		self.importStatus = importStatus;

		self.obSkippedCount = ko.observable(importStatus.SkippedCount);
		self.obUpdatedCount = ko.observable(importStatus.UpdatedCount);
		self.obInsertedCount = ko.observable(importStatus.InsertedCount);
		self.obRejectedCount = ko.observable(importStatus.RejectedCount);
		self.obNoChangeCount = ko.observable(importStatus.NoChangeCount);
		self.obLockedCount = ko.observable(importStatus.LockedCount);

		self.obFileName = ko.observable(importStatus.LogFilePath);
		self.obLogs = ko.observable();
	};

	/**
	 * Initialize the import result modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	ImportStatusViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.$el = $(el);

		tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "ImportedData"), {
			paramData: {
				logFileName: self.importStatus.LogFilePath
			}
		}).then(function(response)
		{
			if (response && response.Items && response.Items[0])
			{
				self.obLogs(response.Items[0]);
			}

			if (self.obLogs() && self.obLogs().includes(PARTIAL_LOG_HINT))
			{
				self.addDownloadButton(el);
			}
		});
	};


	/**
	 * Add the print html.
	 * @return {void}
	 */
	ImportStatusViewModel.prototype.BeforePrint = function()
	{
		var self = this;

		$("body").append("<div class='import-status import-status-print'>" + self.$el.html() + "</div>");

		var $sourceTextarea = self.$el.find("textarea"),
			$printTextarea = $(".import-status-print").find("textarea"),
			$sourceLogFile = self.$el.find(".file-group input"),
			$printLogFile = $(".import-status-print").find(".file-group input");

		$printLogFile.val($sourceLogFile.val());
		$printTextarea.val($sourceTextarea.val());

		$(".import-status-print").addClass("show");
		var height = $printTextarea[0].scrollHeight;
		$printTextarea.height(height);
		$(".import-status-print").removeClass("show");
	};

	/**
	 * remove the print html.
	 * @return {void}
	 */
	ImportStatusViewModel.prototype.AfterPrint = function()
	{
		$(".import-status-print").remove();
	};

	/**
	 * Download log file.
	 * @return {void}
	 */
	ImportStatusViewModel.prototype.downloadLogFile = function()
	{
		const downloadEle = document.createElement('a');
		downloadEle.href = pathCombine(tf.api.apiPrefixWithoutDatabase(), `logFiles?fileName=${this.importStatus.LogFilePath}`);
		downloadEle.download = this.importStatus.LogFilePath;
		document.body.appendChild(downloadEle);
		downloadEle.click();
		document.body.removeChild(downloadEle);
	};

	/**
	 * Add download button
	 * @return {void}
	 */
	ImportStatusViewModel.prototype.addDownloadButton = function(el)
	{
		var self = this;
		$("button.close").after(`
        <button type="button" class="close download-log logfile" aria-hidden="true" title="Download Log file"
            style="margin-right:15px;width:10px;margin-top:10px;background:url(../../global/img/download.svg) no-repeat bottom;">&nbsp;</button>`);

		$("button.close.download-log").on("click", function({ target }, eventArg)
		{
			if ($(target).prop("disabled") || (eventArg && eventArg.key === 'esc'))
			{
				return;
			}

			self.downloadLogFile();
		});
	}
})();

