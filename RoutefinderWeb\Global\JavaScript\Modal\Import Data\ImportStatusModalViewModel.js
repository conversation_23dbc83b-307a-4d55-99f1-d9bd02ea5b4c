(function()
{
	createNamespace('TF.Modal').ImportStatusModalViewModel = ImportStatusModalViewModel;

	function ImportStatusModalViewModel(importStatus)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.contentTemplate('modal/import data/importstatuscontrol');
		self.buttonTemplate('modal/positivenegative');
		self.importStatusViewModel = new TF.Control.ImportStatusViewModel(importStatus);
		self.data(self.importStatusViewModel);
		self.title("Import/Export Status");
		self.obPositiveButtonLabel("Print");
		self.obNegativeButtonLabel("Close");
		self.obResizable(true);
	};

	ImportStatusModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ImportStatusModalViewModel.prototype.constructor = ImportStatusModalViewModel;

	/**
	 * The event of Print button click.
	 * @return {void}
	 */
	ImportStatusModalViewModel.prototype.positiveClick = function()
	{
		var self = this;

		self.importStatusViewModel.BeforePrint();
		window.print();
		self.importStatusViewModel.AfterPrint();
	};

})();
