(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.CategoryDataModel = function(CategoryDataModel)
	{
		namespace.BaseDataModel.call(this, CategoryDataModel);
	};

	namespace.CategoryDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.CategoryDataModel.prototype.constructor = namespace.CategoryDataModel;

	namespace.CategoryDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "Color", default: "ff0000" },
		{ from: "MapDisplay", default: 2 },
		{ from: "Active", default: 1 },
		{ from: "TrailLength", default: 60 },
		{ from: "Symbol" }
	];
})();