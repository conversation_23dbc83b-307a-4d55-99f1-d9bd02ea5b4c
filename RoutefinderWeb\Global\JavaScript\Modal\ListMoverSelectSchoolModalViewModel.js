﻿(function()
{
	createNamespace('TF.Modal').ListMoverSelectSchoolModalViewModel = ListMoverSelectSchoolModalViewModel;

	function ListMoverSelectSchoolModalViewModel(selectedData, options)
	{
		options.displayCheckbox = false;
		options.showRemoveColumnButton = true;

		selectedData = selectedData.map(function(item) { return item; });
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverSelectSchoolViewModel = new TF.Control.ListMoverSelectSchoolViewModel(selectedData, options);
		this.data(this.ListMoverSelectSchoolViewModel);
	}

	ListMoverSelectSchoolModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectSchoolModalViewModel.prototype.constructor = ListMoverSelectSchoolModalViewModel;

	ListMoverSelectSchoolModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverSelectSchoolViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ListMoverSelectSchoolModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverSelectSchoolViewModel.cancel().then(function(result)
		{
			// if (result)
			// {
			this.negativeClose(false);
			// }
		}.bind(this));
	};

})();
