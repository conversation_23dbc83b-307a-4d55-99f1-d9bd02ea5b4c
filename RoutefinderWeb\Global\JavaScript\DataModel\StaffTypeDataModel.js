﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.StaffTypeDataModel = function(StaffTypeDataModel)
	{
		namespace.BaseDataModel.call(this, StaffTypeDataModel);
		this.validator = {
			maxLength: {
				description: 200,
				name: 50
			}
		};
	}

	namespace.StaffTypeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.StaffTypeDataModel.prototype.constructor = namespace.StaffTypeDataModel;

	namespace.StaffTypeDataModel.prototype.mapping = [
		{ from: "StaffTypeId", to: "id", default: 0 },
		{ from: "StaffTypeName", to: "name", default: "" },
		{ from: "StaffTypeDescription", to: "description", default: "" },
		{ from: "IsSystemDefined", default: false }
	];
})();