(function()
{
	Tool = TF.RoutingMap.Locate.Tool;

	/**
	 * Initiailze Locate Tool map events variables.
	 * @returns {void}
	 */
	Tool.prototype._initEvents = function()
	{
		var self = this;
		self._addressLayerClickEvent = self._addressLayer.on('click', self._addressLayerClickHandler.bind(self));
	};

	Tool.prototype._disposeEvents = function()
	{
		var self = this;
		if (self._addressLayerClickEvent)
		{
			self._addressLayerClickEvent.remove();
			self._addressLayerClickEvent = null;
		}
	};

	Tool.prototype._addressLayerClickHandler = function(e)
	{
		// TODO: highlight selection
	};
})();