﻿.map-body {

	.map-left-panel {
		height: 100%;

		.map-container {
			/*margin: 20px 0;*/
			height: 692px;
			background-color: #F2F2F2;

			.kendo-mini-calendar {
				width: 100%;
				border: 0px;

				.k-header {
					background-image: none;
					background-color: #6b6b6b;

					.k-link.k-nav-fast {
						color: #ffffff;
					}

					.k-hover {
						background-image: none;
						background-color: transparent;
					}

					.k-i-arrow-w {
						background-position: 0px -304px;
						background-image: url('../ThirdParty/Kendo/Styles/Black/sprite.png');
						cursor: pointer;
					}

					.k-i-arrow-e {
						background-position: 0px -272px;
						background-image: url('../ThirdParty/Kendo/Styles/Black/sprite.png');
						cursor: pointer;
					}
				}

				table.k-content {

					thead {
						display: none;
					}

					tbody {
						tr {
							td {
								border: 1px solid #F2F2F2;
								text-align: center;
								height: 35px;

								.k-link {
									color: #333;
									box-shadow: none;
									border: 0;
									outline-width: 0;
								}

								&.k-other-month {
									.k-link {
										color: #E6E6E6;
									}
								}

								&:first-child {
									border-left: 0px;
								}

								&:last-child {
									border-right: 0px;
								}

								&.k-hover {
									background-color: transparent;
								}

								&.k-selected {
									background-color: #E27B70;
									box-shadow: none;
									background-image: none;

									&:hover {
										background-color: #E27B70;
									}

									// box-shadow: inset 0 0 0 1px red;
									.k-link {
										color: white;
									}
								}
							}

							.k-today {
								background-color: #D74B3C;

								.k-link {
									color: white;
								}

								&:hover {
									background-color: #D74B3C;
								}
							}

							&:last-child {
								td {
									border-bottom: 0px;
								}
							}
						}
					}
				}

				.k-footer {
					display: none;
				}
			}

			.info-scroll {
				margin: 10px 5px 0px 5px;
				height: 400px;
				overflow: auto;

				.title {
					font-size: 14px;
					font-weight: bold;
					border-bottom: 1px solid #BDBDBD;
				}

				.content {
					background-color: white;
					margin-bottom: 20px;
					margin-top: 5px;

					.container-list {
						margin-bottom: 10px;
						overflow: hidden;

						.shuttles-color {
							background-color: #FFCC00;
							width: 8px;
							height: 20px;
							float: left;
						}

						.icon-description {
							float: left;
							width: 95%;

							span {
								margin-left: 10px;
							}
						}
					}

					.box-description {
						border-bottom: 1px solid #F2F2F2;
						height: 50px;
					}

					.sub-description-small {
						overflow: hidden;
						font-size: 11px;
						color: #B6B6B6;
					}

					.sub-description-normal {
						color: #B6B6B6;
					}

					.signal-text {
						margin: 0 10px;
						overflow: hidden;
						line-height: 18px;
					}

					.tf-horizontal {
						background-color: #F2F2F2;
						margin-bottom: 2px;
						width: 98%;
					}

					.span-left {
						float: left;
					}

					.span-right {
						float: right;
					}
				}
			}
		}
	}

	.map-right-panel {
		.tf-trip-stop-map-view-continer {
			margin: 0;
		}
	}
}

.geo-search-toolbar {
	width: 100%;
	pointer-events: none;
	cursor: auto;

	&.active {
		pointer-events: visiblePainted;
		background-color: rgba(20, 20, 20, 0.7);
	}

	.gm-style-mtc {
		pointer-events: visiblePainted;

		&.confirm {
			float: right;
			height: 31px;
			width: 70px;
			cursor: pointer;
			text-align: center;
			font-size: 11px;
			line-height: 31px;
			font-weight: bold;
			display: none;

			&.close {
				background-color: transparent;
				color: white;
				opacity: 1;
				text-shadow: none;
				width: auto;
				margin-left: 20px;
			}

			&.apply {
				background-color: white;
				color: #000000;

				&.active {
					display: block;
				}
			}
		}

		.geo-search-button,
		.geo-action-button {
			height: 31px;
			width: 31px;
			float: left;
			cursor: pointer;
			background-repeat: no-repeat;
			background-position: center;
			background-color: white;
			box-shadow: rgba(0, 0, 0, 0.298039) 0 1px 4px -1px;
			position: relative;
			left: 10px;
		}

		.geo-search-button.draw-polygon {
			background-image: url('../img/map/view/Geo-Search-b.png');

			&.active {
				background-image: url('../img/map/view/Geo-Search-Applied-b.png');
			}
		}

		.geo-search-button.center-map {
			background-image: url('../img/map/view/CenterInMap.png');

			&.active {
				background-image: url('../img/map/view/CenterInMapApplied.png');
			}
		}

		.geo-search-button,
		.geo-action-button {
			&:hover {
				background-color: #ebebeb;
			}
		}

		.geo-action-button.undo {
			background-image: url('../img/map/view/undo.png');
		}

		.geo-action-button.undo.disabled {
			background-image: url('../img/map/view/undo-gray.png');
			cursor: default;
		}

		.geo-action-button.redo {
			background-image: url('../img/map/view/redo.png');
		}

		.geo-action-button.redo.disabled {
			background-image: url('../img/map/view/redo-gray.png');
			cursor: default;
		}

		.geo-action-button.clear-all-polygons {
			background-image: url('../img/map/view/clear-all.png');
		}
	}
}

.data-entry-map-container {
	width: 100%;
	height: 100%;
	position: relative;
	@dockMaxHeight: 430px;

	&.map-page {
		position: relative;
	}

	.map_panel {
		width: 100%;
		height: 100%;
	}

	.dock {
		height: @dockMaxHeight;
	}

	.routingmap_panel {
		min-width: 400px;

		.item-container {
			margin-top: 0;

			.slider-list>.item-header:first-child {
				border-top: 1px solid #cc0012 !important;
			}
		}

		.item-content {
			display: block;
			overflow: inherit;
		}

		&.dock-left,
		&.dock-right {
			.list-container {
				max-height: @dockMaxHeight;
			}
		}

		.list-container {
			max-height: 500px;
		}
	}

	.off-map-tool {

		.esriBasemapGallery,
		.esri-basemap-gallery {
			width: 560px;
			max-width: 560px;
		}
	}
}

.right-doc {
	.off-map-tool.form-open {
		z-index: 99 !important;
	}
}

.data-entry-map {
	width: 100%;
	height: 100%;
}

.map-tool-container .map-tool-item {
	width: 300px;
	height: 50px;
	position: absolute;
	right: 0;
	cursor: pointer;

	&.disable {
		opacity: 0.5;
		cursor: inherit;

		label,
		.tool-icon {
			cursor: inherit;
		}
	}
}

.map-tool-container .map-tool-item label {
	display: none;
	cursor: pointer;

	&.disable {
		pointer-events: none;
	}
}

.off-map-tool.active .map-tool-container .map-tool-item label {
	display: initial;
	position: absolute;
	color: white;
	text-align: right;
	right: 80px;
	line-height: 50px;
	font-size: 17px;
	font-weight: normal;
}

.map_panel .map {
	width: 100%;
	height: 100%;
}