﻿(function()
{
	createNamespace("TF.Modal").NewTripStopModalViewModel = NewTripStopModalViewModel;

	function NewTripStopModalViewModel(geoCandidate, tripViewModels, successCallback, failCallback)
	{
		TF.Modal.BaseModalViewModel.call(this, successCallback, failCallback);
		this.title('New ' + tf.applicationTerm.getApplicationTermSingularByName("Stop"));
		this.sizeCss = "modal-dialog-lg";
		this.contentTemplate('controls/newstop');
		this.buttonTemplate('modal/positivenegative');
		this.data(new TF.Fragment.NewStopViewModel(geoCandidate, tripViewModels));
	};

	NewTripStopModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	NewTripStopModalViewModel.prototype.constructor = NewTripStopModalViewModel;
})()


