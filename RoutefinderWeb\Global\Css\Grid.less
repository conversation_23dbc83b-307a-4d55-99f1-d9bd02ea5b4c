@import 'z-index';
@summaryGridHeight: 83px;
@summaryGridMobileHeight: 65px;
@gridRowHeight: 33px;
@gridHeaderBackground: linear-gradient(#4b4b4b 0, #4b4b4b @gridRowHeight, #d6d6d6 @gridRowHeight, #d6d6d6 100%);
@listMoverGridHeaderBackground: linear-gradient(#4b4b4b 0, #4b4b4b 32px, #d6d6d6 32px, #d6d6d6 100%);
@gridRowDefaultBkgColor: #fff;
@gridRowSelectedBkgColor: #ffffce;
@gridRowAltBkgColor: #ecf2f9;
@gridRowBorderColor: #d5d5d5;
@gridRowSelectedBorderColor: #2e2e2e;

.sort-bar {
	height: 25px;
	padding: 3px;
	box-sizing: content-box;

	.sorted-item {
		display: inline-block;
		border-radius: 4px;
		background: #efefef;
		padding: 4px;
		line-height: 15px;
		margin: 1px 2px 1px 2px;
	}

	.sorted-item-placeholder {
		padding: 5px 2px 5px 2px;
		background: red;
	}
}

.setting-page-grid-icons,
.document-grid .grid-icons {
	min-height: 28px;
	float: left;
	width: 100%;
	padding: 0 16px;
}

.document-grid {
	.iconrow {
		margin: 0;
		padding-top: 3px;
		padding-bottom: 3px;
		float: left;
		height: 28px;
		white-space: nowrap;

		&:after {
			content: '';
			clear: both;
			display: block;
		}

		.iconbutton {
			width: 22px;
			float: left;
			margin: 0 1px;
			height: 22px;
			text-align: center;
			opacity: 0.8;

			&.disabled {
				opacity: 0.4;
			}
		}

		.divider {
			border-right: 1px solid #333333;
			height: 20px;
			margin: 1px 3px;
			float: left;
		}
	}

	.toppart {
		margin: 0 5px 0 5px;
		height: 84px;
	}

	.header {
		font-size: 20px;
		font-weight: bold;
		color: #D0503C;
	}

	.grid-staterow {
		color: #333;
		float: right;

		.title {
			font-weight: bold;
		}

		.modified {
			color: blue;
		}
	}
}

.list-mover-mover {
	.list-mover-iconrow-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 28px;

		span.datepickerinput span.k-picker-wrap.k-state-default {
			height: auto;
		}

		.date-range-title {
			display: flex;
			align-items: center;

			.warning {
				padding-left: 5px;
				color: red;
			}
		}
	}

	.iconbutton.filter.list-mover-filter-applied {
		background-image: url(../../global/img/grid/filter-applied-red.png);
	}

	.iconrow {
		display: flex;
		margin: 3px 0;

		&:after {
			content: '';
			clear: both;
			display: block;
		}

		.iconbutton {
			width: 22px;
			float: left;
			margin: 0 1px;
			height: 22px;
			text-align: center;
			opacity: 0.8;

			&.disabled {
				opacity: 0.4;
			}
		}

		.divider {
			border-right: 1px solid #333333;
			height: 20px;
			margin: 1px 3px;
			float: left;
		}
	}

	.active-trips-checkbox
	{
		input{
			outline: none;
		}
	}
}

.k-mobile {

	//set iconbutton ease to click on tablet
	@media (max-width: 1024px) and (min-width: 768px) {
		.document-grid .iconrow .iconbutton {
			width: 24px;
			height: 24px;
			margin-right: 2px;
		}
	}
}

button.iconbutton {
	width: 22px;
	margin: 0 1px;
	height: 22px;
	text-align: center;
	background-color: transparent;
	border-style: none;

	&:hover:not(:disabled):after {
		margin-left: -2px;
	}

	&:disabled {
		opacity: 0.4;

		&:hover:after {
			border: none;
		}
	}
}

.document-grid .iconrow button.iconbutton {
	&:hover:not(:disabled):after {
		margin-left: -3px;
	}

	&:disabled {
		opacity: 0.4;

		&:hover:after {
			border: none;
		}
	}
}

.iconbutton {
	&.gridview {
		background-image: url('../../global/img/grid/grid.svg');

		&.open {
			background-image: url('../../global/img/grid/grid-active.svg');
		}
	}

	&.detailview {
		background-image: url('../../global/img/grid/details.svg');

		&.open {
			background-image: url('../../global/img/grid/details-active.svg');
		}
	}

	&.mapview {
		background-image: url('../../global/img/grid/Map.png');

		&.open {
			background-image: url('../../global/img/grid/MapOpen.png');
		}
	}

	&.calendarview {
		background-image: url('../../global/img/grid/calendar.png');

		&.open {
			background-image: url('../../global/img/grid/calendar-active.svg');
			background-size: 22px;
		}
	}

	&.directory {
		background-image: url('../../global/img/grid/DirectoryClose.png');

		&.open {
			background-image: url('../../global/img/grid/DirectoryOpen.png');
		}
	}

	&.calendarview {
		background-image: url('../../global/img/grid/calendar.svg');
		background-size: 23px;
	}

	&.calendarprint {
		background-image: url(../img/print.png);
		opacity: 0.6;
	}

	&.directoryview {
		background-image: url('../../global/img/grid/Directory.png');
	}

	&.summarybar {
		background-image: url('../../global/img/grid/summary.png');

		&.open {
			background-image: url('../../global/img/grid/summaryBarOpen.png');
		}
	}

	&.addremovecolumn {
		background-image: url('../../global/img/grid/column.png');
	}

	&.managereports {
		background-image: url('../../global/img/grid/Manage-Reports.png');
	}

	&.newGrid {
		background-image: url('../../global/img/grid/OpenGrid.png');
		opacity: 0.6;
	}

	&.layout {
		background-image: url('../../global/img/grid/layout.png');

		&.contextmenu-open {
			.grid-contextmenu-open('../../global/img/grid/layoutOpen.png');
			background-position-x: 2px;
		}
	}

	&.filter {
		background-image: url('../../global/img/grid/filter.png');

		&.contextmenu-open {
			.grid-contextmenu-open('../../global/img/grid/filterOpen.png');
			background-position-x: 2px;
		}
	}

	&.thematic {
		background-image: url('../../global/img/map/view/thematics.svg');

		&.contextmenu-open {
			.grid-contextmenu-open('../../global/img/map/view/thematics-rev.svg');
			background-position-x: 2px;
		}
	}

	&.refreshi {
		background-image: url('../../global/img/grid/refreshi.png');
	}

	&.chartView {
		background-image: url('../../global/img/grid/Chart_View.svg');
	}

	&.new {
		background-image: url('../img/grid/New.png');
	}

	&.bulkmenu {
		background-image: url('../../global/img/grid/bulkmenu.png');
	}

	&.delete {
		background-image: url('../../global/img/menu/Delete-Black.svg');
	}

	&.view {
		background-image: url('../img/grid/view.png');
	}

	&.approve {
		background-image: url('../img/grid/approve.svg');
	}

	&.copytoclipboard {
		background-image: url('../img/grid/Copy-and-New-5.png');
	}

	&.binoculars {
		background-image: url('../img/grid/binoculars.svg');
	}

	&.substituteResources {
		background-image: url('../img/grid/SubstituteResources.svg');
	}

	&.copyexisting {
		background-image: url('../img/grid/Copy-and-New-5.png');
	}

	&.exportexisting {
		background-image: url('../img/grid/ExportReportDef.svg');
	}

	&.preview {
		background-image: url('../img/grid/search.svg');
	}

	&.remove {
		background-image: url('../img/grid/remove_download.svg');
	}

	&.importreport {
		background-image: url('../img/grid/ImportReport.svg');
	}

	&.runreport {
		background-image: url('../img/grid/runnow.png');
	}

	&.favorite {
		background-image: url('../img/grid/favorite.svg');

		&.not {
			background-image: url('../img/grid/not-favorite.svg');
		}
	}

	&.schedule-report {
		background-image: url('../img/grid/new.png');
	}

	&.geocoding {
		background-image: url('../img/grid/geocoding.png');
	}

	&.reverseGeocode {
		background-image: url('../img/grid/reverse-geocoded.svg');
		background-size: 19px;
	}

	&.replace {
		background-image: url('../img/grid/replace.png');
	}

	&.findSchedule {
		background-image: url('../img/grid/findscheule.png');
	}

	&.ungeocode {
		background-image: url('../img/grid/ungeocode.png');
	}

	&.template {
		background-image: url('../img/grid/template.svg');
		background-size: 18px;

		&.mergedocument {
			background-image: url('../img/detail-screen/settings.svg');
		}

		&.contextmenu-open {
			background-image: url('../img/grid/template-open.svg');
			background-color: #4B4B4B;
			opacity: 1;

			&.mergedocument {
				background-image: url('../img/detail-screen/settings_white.svg');
			}
		}
	}

	&.pencil {
		background-image: url('../../global/img/grid/editor_pencil_2.png');
	}

	&.more {
		background-image: url('../../global/img/search/more.png');
	}

	&.white-add {
		background-image: url('../../global/img/grid/button-add.png');
	}

	&.eye-show {
		background-image: url('../../global/img/Icons/eye.svg');
		filter: grayscale(1) brightness(0.3);
	}

	&.lock {
		background-image: url('../../global/img/Routing Map/menuicon/lock-gray.png');
	}

	&.lock-black {
		background-image: url('../../global/img/Routing Map/menuicon/lock-black.png');
		margin-left: 0;
	}

	&.crosser {
		background-image: url('../../global/img/Routing Map/cross_street_gray.png');
		background-size: auto;
	}

	&.crosser-black {
		background-image: url('../../global/img/Routing Map/cross_street.svg');
	}

	&.stop-crosser {
		background-image: url('../../global/img/Routing Map/stop_crosser.svg');
	}

	&.student-exception-gray {
		background-image: url('../../global/img/Routing Map/circled-e-gray.svg');
	}

	&.student-exception {
		background-image: url('../../global/img/Routing Map/circled-e.svg');
	}

	&.eye-hide {
		background-image: url('../../global/img/Icons/eye-slash.svg');
		filter: grayscale(1) brightness(0.3);
	}

	&.policies {
		background-image: url('../../global/img/grid/Policies.png');
	}

	&.repair {
		background-image: url('../../global/img/grid/Repair.png');
	}

	&.restore {
		background-image: url('../../global/img/grid/Restore.png');
	}

	&.archive {
		background-image: url('../../global/img/grid/Archive.png');
	}

	&.upload {
		background-image: url('../../global/img/grid/upload.png');
	}

	&.download {
		background-image: url('../../global/img/grid/download.svg');
	}

	&.linked {
		background-image: url('../../global/img/grid/refresh-linked.png');
	}

	&.dashboard-view {
		background-image: url('../../global/img/grid/dashboard-view.svg');
	}

	&.disabled {
		pointer-events: all;
		opacity: 0.4;
	}

	&.save {
		background-image: url('../../global/img/Routing Map/menuicon/save1.png');
		position: relative;

		.badge {
			position: absolute;
			top: -4px;
			right: -5px;
			background-color: #f33541;
			padding: 2px 5px;
			font-size: 11px;
			min-height: 14px;
			min-width: 14px;
		}
	}
}

.grid-contextmenu-open(@image-url) {
	margin-right: 0;
	width: 23px;
	border-left: solid #9b9b9b 1px;
	opacity: 1;
	background-color: #4B4B4B;
	background-image: url(@image-url);
}

.document-grid .filter.filter-applied {
	background-image: url('../../global/img/grid/filter_applied.png');
}

.tf-contextmenu {
	.menu-item {
		&.disabledToUse {
			opacity: 0.5;
		}

		&.dashboad {
			.menu-icon {
				background-image: url('../../global/img/grid/DrillDown.png');
			}
		}
	}

	.menu-item-checked {
		.menu-icon {
			background-image: url('../../global/img/grid/green_check.png');
		}

		.menu-label {
			font-weight: bold;
		}

		&.menu-item-broken .menu-label {
			font-weight: normal;
		}

		&.dashboad {
			.menu-icon {
				background-image: url('../../global/img/grid/DrillDownApplied.png');
			}
		}
	}

	.menu-item-dbspecific {
		.menu-icon {
			background-image: url('../../global/img/grid/db-specific.svg');
		}
	}

	.menu-item-broken {
		.menu-icon {
			background-image: url('../../global/img/grid/BrokenFilter.png');
		}

		&.menu-item-dbspecific {
			.menu-icon {
				background-image: url('../../global/img/grid/broken-db.png');
			}
		}
	}
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.refresh,
.iconbutton.refresh {
	background-image: url('../../global/img/grid/icon_refresh.svg');
}

.iconbutton.run,
.iconbutton.runform {
	background-image: url('../Img/grid/runnow.png');
}

.no-image-label {
	font-style: italic;
	font-size: 12px;
}

.grid-icon {
	display: block;
	height: 16px;
	width: 16px;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 16px;

	&.grid-icon-lock {
		background-image: url('../../global/img/grid/lock.png');
	}

	&.grid-icon-unlock {
		background-image: url('../../global/img/grid/unlock.png');
	}

	&.grid-icon-schoolshuttle {
		background-image: url('../../global/img/grid/schoolshuttle.png');
	}

	&.grid-icon-schoolshuttle_front {
		background-image: url('../../global/img/grid/schoolshuttle_front.png');
	}

	&.grid-icon-schoolshuttle_back {
		background-image: url('../../global/img/grid/schoolshuttle_back.png');
	}

	&.grid-icon-sunmoon {
		background-image: url('../../global/img/grid/sunmoon.png');
	}

	&.grid-icon-sun {
		background-image: url('../../global/img/grid/sun.png');
	}

	&.grid-icon-moon {
		background-image: url('../../global/img/grid/moon.png');
	}

	&.grid-icon-reddot {
		background-image: url('../../global/img/grid/reddot.png');
	}

	&.grid-icon-yellowdot {
		background-image: url('../../global/img/grid/yellowdot.png');
	}

	&.grid-icon-no-image {
		background-image: url(../../Global/img/grid/no-image.png);
	}

	&.grid-icon-editor_pencil {
		background-image: url('../../global/img/grid/editor_pencil.png');
	}

	&.eye-hide {
		background-image: url('../../global/img/Icons/eye-slash.svg');
		filter: grayscale(1) brightness(0.3);
	}

	&.grid-icon-DrillDownBlack {
		background-image: url('../../global/img/grid/DrillDownBlack.png');
	}

	&.grid-icon-BrokenFilterBlack {
		background-image: url('../../global/img/grid/BrokenFilterBlack.png');
	}
}

.hover-underline {
	&:after {
		content: '';
		width: 16px;
		height: 0;
		float: left;
		margin-left: 3px;
		margin-top: 20px;
		border: 1px solid red;
		-webkit-backface-visibility: hidden;
		backface-visibility: hidden;
	}
}

.grid-container {
	border: none;

	.k-grid-header {
		border-bottom: none;
	}

	.k-grid-content {
		border: #E1E1E1 1px solid;
		box-sizing: border-box;
	}
}

.without-filter-grid-bottom .k-grid-content-locked {
	border: #E1E1E1 1px solid;
	border-right: 0;
}

.list-mover-mover .without-filter-grid-bottom {
	.k-pager.k-grid-pager {
		border: #E1E1E1 1px solid;
	}
}

.document-grid {
	.tf-searchgrid-container {
		height: calc(~"100% - 22px");
		margin-bottom: -2px;
	}

	.tf-searchgrid-container.with-summarybar {
		height: calc(~"100% - 94px");
	}

	.summarygrid-container {
		height: 74px;
		margin-bottom: -2px;
	}

	.search-grid-paginator {
		height: 20px;
		margin-left: 10px;
	}

	.bulk-menu {
		position: absolute;
		z-index: 1;
	}
}

.GridHeader>input {
	background-color: #f3f3f3;
	border-color: #c4c4c4;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	border-style: solid;
	border-width: 1px;
}

.GridIcon {
	background-color: #a1a1a1;
	border-radius: 12px;
	display: block;
	height: auto;
	max-width: 24px;
	max-height: 24px;
	width: auto;
}

.hide-left-bar-top-border .tf-contextmenu:before {
	content: "";
	position: absolute;
	top: -1px;
	width: 22px;
	height: 2px;
	background-color: #4b4b4b;
}

.tf-contextmenu.grid-menu>.menu-divider:before {
	height: 7px;
}

.tf-contextmenu.grid-menu>.menu-item>.menu-label {
	padding-left: 30px;
}

.tf-contextmenu.grid-menu>.menu-item>.menu-label {
	min-width: 250px;
}

.tf-contextmenu.grid-menu .menu-subtitle:before {
	content: "";
	float: left;
	background: #4b4b4b;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 14px 14px;
	width: 22px;
	height: 24px;
}

.tf-contextmenu.grid-menu .menu-subtitle .menu-subtitle-text {
	line-height: 24px;
	margin-left: 22px;
	padding: 0 20px 0 15px;
	min-width: 100px;
	white-space: nowrap;
	font-size: 13px;
	font-weight: bold;
}

.tf-contextmenu.grid-menu>.menu-item.speciallast {
	background-color: #e0e0e0;
}

.tf-contextmenu.grid-menu>.menu-item.speciallast>.menu-icon {
	background-color: inherit;
}

.tf-contextmenu-wrap.popover-wrap {
	position: absolute;
	top: 20px;
	left: -30px;
}

.tf-contextmenu.grid-popover:before,
.tf-contextmenu.grid-popover:after {
	content: "";
	position: absolute;
	border-style: dashed dashed solid dashed;
	line-height: 1;
	display: inline-block;
}

.tf-contextmenu.grid-popover {
	width: 350px;
	background-color: #FFF;
	border: 1px solid #D7DADB;
	display: block;
	text-align: left;
	line-height: 18px;
	font-size: 11px;
	border-bottom: 5px solid #333;
}

.tf-contextmenu.grid-popover:after {
	left: 15px;
	border-color: transparent transparent #D7DADB;
	border-width: 0 21px 21px;
	top: -21px;
	z-index: 1080;
}

.tf-contextmenu.grid-popover:before {
	left: 16px;
	border-color: transparent transparent #FFF;
	border-width: 0 20px 21px;
	top: -20px;
	z-index: 1081;
}

.tf-contextmenu.grid-popover.largerWidth {
	width: 450px;
}

.tf-contextmenu.grid-popover .boldFont {
	font-weight: bold;
}

.tf-contextmenu.grid-popover .lightFont {
	color: #848484;
}

.tf-contextmenu.grid-popover .no-padding-right {
	padding-right: 0px;
}

.tf-contextmenu.grid-popover .padding-bottom-10 {
	padding-bottom: 10px;
}

.tf-contextmenu.grid-popover .border-bottom {
	padding: 10px;
	border-bottom: 1px solid #333;
}

.tf-contextmenu.grid-popover .padding-nobottom-10 {
	padding: 10px 10px 0px 10px;
}

.tf-contextmenu.grid-popover .padding-10 {
	padding: 10px;
	border-bottom: 1px solid #efefef;
}

.tf-contextmenu.grid-popover .borderleft {
	border-left: 20px solid #e78e10;
	padding-left: 0px;
}

.tf-contextmenu.grid-popover .borderleft>div {
	padding-left: 6px;
}

.tf-contextmenu.grid-popover .borderleft>div:last-child {
	padding-left: 12px;
}

.tf-contextmenu.grid-popover .detail-bottom-info {
	text-align: center;
	background: #D7DADB;
	line-height: 25px;
}

.tf-contextmenu.grid-popover .view-detail-grid {
	padding: 10px;
	cursor: pointer;
	border-bottom: 1px solid #E1E1E1;
}

.tf-contextmenu.grid-popover .view-detail-grid:nth-of-type(2n) {
	background-color: #f2f2f2;
}

.tf-contextmenu.grid-popover .view-detail-grid:hover {
	background-color: @gridRowAltBkgColor;
}

.tf-contextmenu.grid-popover .iconbutton {
	display: inline-block;
	cursor: pointer;
}

.tf-contextmenu.grid-popover .grayrow {
	color: #fff;
	background-color: #4b4b4b;
	height: 24px;
	line-height: 24px;
	padding-left: 10px;
	width: 100%;
}

.document-grid .bottompart {
	height: calc(~"100% - 84px");
}

.document-grid .bottompart>div {
	height: 100%;
	float: left;
	width: 100%;
	background-color: #F5F5F5;
}

.document-grid .search-grid.with-splitmap {
	width: 50%;
	float: left;
}

.document-grid .search-grid .grid-bottombar {
	background: linear-gradient(to bottom, #f8f8f8 0%, #d6d6d6 100%);
	height: 22px;
	position: relative;
	line-height: 22px;
}

.search-grid .grid-bottombar .search-grid-paginator .pagesize-setter {
	width: 50px;
}

.search-grid .grid-bottombar .search-grid-paginator .page-selector {
	margin-left: calc(~"45% - 200px");
}

.search-grid .grid-bottombar .search-grid-paginator .page-item {
	margin: 0 2px;
	color: #4b4b4b;
	cursor: pointer;
}

.search-grid .grid-bottombar .search-grid-paginator .page-item.bold {
	font-weight: bold;
	color: black;
}

.search-grid .grid-bottombar .search-grid-paginator .page-previous {
	display: inline-block;
	width: 15px;
	background-repeat: no-repeat;
	background-position: center;
	background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="8" width="8"><polygon points="8,0 8,8 4,4" style="fill:%23474747;" /></svg>');
}

.search-grid .grid-bottombar .search-grid-paginator .page-next {
	display: inline-block;
	width: 15px;
	background-repeat: no-repeat;
	background-position: center;
	background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="8" width="8"><polygon points="0,0 0,8 4,4" style="fill:%23474747;" /></svg>');
}

.search-grid .grid-overlay {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 999;
	display: none;
	background-image: url('../img/Spinner-White.gif');
	background-repeat: no-repeat;
	background-position: center;
	background-color: rgba(238, 238, 238, 0.4);
}

.tf-filter-dropdown {
	display: inline-block;
}

.tf-filter-dropdown ul {
	font-size: 9px;
	line-height: 22px;
}

.tf-filter-dropdown ul>li:hover {
	background-color: #ffffcc;
}

.tf-filter-dropdown ul>li>.tf-filter-symbol {
	display: inline-block;
	width: 26px;
	height: 26px;
	background-position: 50% 50%;
	background-size: 18px 18px;
	background-repeat: no-repeat;
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.contains {
	background-image: url('../../global/img/Filter/Contains.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.doesnotcontain {
	background-image: url('../../global/img/Filter/DoesNotContain.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.equalto {
	background-image: url('../../global/img/Filter/Equals.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.endswith {
	background-image: url('../../global/img/Filter/EndsWith.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.startswith {
	background-image: url('../../global/img/Filter/StartsWith.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.greaterthan,
.tf-filter-dropdown ul>li>.tf-filter-symbol.after {
	background-image: url('../../global/img/Filter/GreaterThan.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.lessthan,
.tf-filter-dropdown ul>li>.tf-filter-symbol.before {
	background-image: url('../../global/img/Filter/LessThan.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.notequalto {
	background-image: url('../../global/img/Filter/DoesNotEqual.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.isnull {
	background-image: url('../../global/img/Filter/Empty.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.isnotnull {
	background-image: url('../../global/img/Filter/NotEmpty.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.greaterthanorequalto,
.tf-filter-dropdown ul>li>.tf-filter-symbol.onorafter {
	background-image: url('../../global/img/Filter/GreaterThanEqual.png');
}

.tf-filter-dropdown ul>li>.tf-filter-symbol.lessthanorequalto,
.tf-filter-dropdown ul>li>.tf-filter-symbol.onorbefore {
	background-image: url('../../global/img/Filter/LessThanEqual.png');
}

.tf-filter-dropdown ul>li,
.tf-filter-dropdown ul>li>.tf-filter-symbol,
.tf-filter-dropdown ul>li>.tf-filter-text {
	vertical-align: middle;
	cursor: pointer;
}

.search-grid .tf-filter {
	margin-left: 2px;
	width: calc(~"100% - 6px");
}

@tf-filter-border-color: #c5c5c5;

.search-grid .tf-filter .form-control {
	padding-left: 2px;
	border-color: @tf-filter-border-color;
}

.search-grid .tf-filter.input-group .input-group-addon {
	border-color: @tf-filter-border-color;
}

.k-filtercell .input-group.tf-filter {
	outline-color: @tf-filter-border-color;

	.form-control.datepickerinput {
		&:focus {
			-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px @tf-filter-border-color;
			box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px @tf-filter-border-color;
			outline-color: @tf-filter-border-color;
			border-color: @tf-filter-border-color;
		}
	}
}

.search-grid .tf-filter.input-group .form-control:last-child {
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}

.search-grid .tf-filter .input-group-btn>.tf-filter-button {
	border: 1px solid #A9A9A9;
	border-right: none;
	background-color: #eeeeee;
	color: #262626;
	background-repeat: no-repeat;
	background-position: center;
	font-size: 8px;
	width: 30px;
	height: 24px;
}

.search-grid .tf-filter.input-group .input-group-addon.tf-icon-button {
	background: #fff;
	font-size: 0px;
	padding: 3px 4px;
}

.document-grid {

	.splitmap,
	.directoryview {
		position: relative;
		width: 50%;
		float: right;
	}
}

.list-mover-mover a.btn {
	border-radius: 0;
	width: 100%;
	padding: 5px 0;
	text-align: center;
	background-color: #D7D7D7;
	margin: 5px 0;
}

.list-mover-mover a.btn:first-child,
.list-mover-mover a.btn:last-child {
	margin-top: 20px;
	margin-bottom: 20px;
}

.list-mover-mover {
	.grid-container {
		box-sizing: border-box;
	}

	.move-button-column {
		padding: 100px 0 0 0;
		max-width: 38px;
		/*margin-top: 20px;
		display: flex;
		flex-direction: column;
		justify-content: center;*/
	}

	.move-button-column-2 {
		.move-button-column;
		margin-left: -10px;
	}

	.move-button-column-3 {
		.move-button-column;
	}
}

@media (max-width: 992px) {
	.list-mover-mover {
		.move-button-column {
			width: 20px;
			margin-right: -8px;
			margin-left: -8px;
		}
	}
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.contains {
	background-image: url('../../global/img/Filter/Contains.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.doesnotcontain {
	background-image: url('../../global/img/Filter/DoesNotContain.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.equalto {
	background-image: url('../../global/img/Filter/Equals.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.endswith {
	background-image: url('../../global/img/Filter/EndsWith.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.startswith {
	background-image: url('../../global/img/Filter/StartsWith.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.greaterthan,
.search-grid .tf-filter .input-group-btn .tf-filter-button.after {
	background-image: url('../../global/img/Filter/GreaterThan.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.lessthan,
.search-grid .tf-filter .input-group-btn .tf-filter-button.before {
	background-image: url('../../global/img/Filter/LessThan.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.notequalto {
	background-image: url('../../global/img/Filter/DoesNotEqual.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.isnull {
	background-image: url('../../global/img/Filter/Empty.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.isnotnull {
	background-image: url('../../global/img/Filter/NotEmpty.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.greaterthanorequalto,
.search-grid .tf-filter .input-group-btn .tf-filter-button.onorafter {
	background-image: url('../../global/img/Filter/GreaterThanEqual.png');
}

.search-grid .tf-filter .input-group-btn .tf-filter-button.lessthanorequalto,
.search-grid .tf-filter .input-group-btn .tf-filter-button.onorbefore {
	background-image: url('../../global/img/Filter/LessThanEqual.png');
}

.kendo-grid .k-filter-row {
	background-color: #D6D6D6 !important;
	height: 38px;
}

.kendo-grid {

	.k-focus {
		box-shadow: none !important;
	}

	.k-grid-content table.table-blank-fullfill {
		tr.k-alt {

			&,
			&.k-selected {
				background-color: @gridRowAltBkgColor;
			}

			>td {
				background-color: @gridRowAltBkgColor;
			}

			&.disable>td {
				color: grey;
			}
		}
	}

	.k-input {
		height: 20px;
		padding: 0px;
		color: #333;
		text-indent: 4px;
	}

	.k-grid-header {
		.k-filtercell {
			.k-input {
				height: 22px;
			}
		}
	}

	.k-loading-image {
		background-image: none;
	}

	.k-loading-color {
		opacity: 0;
	}

	.left-color-icon {
		height: 15px;
		width: 15px;
		margin-right: 0.5em;
		border: 1px solid rgb(213, 213, 213);
		float: left;
		border-radius: 50%;

		&.square {
			border-radius: 0;
		}
	}

	.favorite-report,
	.favorite-dashboard {
		background-image: url('../img/grid/favorite.svg');
		background-size: 16px 16px;

		&.not {
			background-image: url('../img/grid/not-favorite.svg');
		}
	}

	.k-autocomplete {
		.k-input {
			text-indent: 0.33em;
		}

		.k-clear-value {
			display: none !important;
		}
	}

	.k-i-loading {
		background-image: url(Default/loading.gif);
		background-position: 50% 50%;
		bottom: -1.5px;

		&.right-button {
			right: 25px;
		}
	}
}

.kendo-grid [role=columnheader] .k-icon {
	background-image: url('../../global/thirdparty/kendo/styles/Black/sprite.png');
}

.kendo-grid [role=columnheader] .k-grid-filter-menu.k-grid-header-menu {
	//display: none;
}

.k-header {

	.k-icon.k-add,
	.k-denied {
		/*background-image: url('../../global/thirdparty/kendo/styles/Black/sprite.png');*/
		background-image: none;
		width: 0;
	}
}

.k-drag-clue.dragIn {
	border: 2px solid red;
	height: 34px;

	.k-svg-icon {
		background-image: url('../../global/img/menu/Delete-Black.svg');
		background-position: center center;
		width: 16px;
		background-size: auto;

		svg {
			display: none;
		}
	}
}

.k-drag-clue.dragIn .k-svg-i-cancel {
	&:before {
		content: '';
	}
}


.k-column-menu .k-header,
.k-column-menu .k-selected>.k-link {
	color: @gridRowSelectedBorderColor;
}

.k-column-menu .k-header {
	background-color: #ffffff;
}

.k-grid-menu .k-selected:not(.disSelectable),
.k-grid-content .k-selected:not(.disSelectable),
.k-grid-content-locked .k-selected:not(.disSelectable) {
	color: #2e2e2e;

	td {
		color: #2e2e2e;
		background-color: @gridRowSelectedBkgColor !important;
	}
}

.k-grid-content .disSelectable {
	background: inherit;
	pointer-events: none;

	td {
		color: lightgray !important;
	}
}

.k-grid-content {
	tr:has(.disSelectable) {
		background: inherit;
		pointer-events: none;

		td {
			color: lightgray !important;
		}
	}
}

.k-grid-content-locked {
	tr:has(.disSelectable) {
		background: inherit;
		pointer-events: none;

		td {
			color: lightgray !important;
		}
	}
}

.k-grid table,
.k-grid table.k-table {
	border-collapse: collapse;
}

@media(max-width: 912px) {
	.open-datasource-grid-container .k-grid table {
		min-width: 863px;
	}
}

.k-grid tbody td:first-child {
	border-left: 1px solid @gridRowBorderColor;
}

.k-grid-content {

	.k-state-dragover,
	.k-dragover {
		background: #ddedfb;
		border: 2px solid #00008B !important;
		border-left: none !important;
		border-right: none !important;

		td {
			background: #ddedfb !important;
		}
	}
}

.k-grid-content-locked {
	table {
		tr {
			border-right: 1px solid rgb(213, 213, 213);
		}
	}

	.k-state-dragover,
	.k-dragover {
		background: #ddedfb;
		border: 2px solid #00008B !important;
		border-left: none;
		border-right: none;

		td {
			border-top: 2px solid #00008B;
			background: #ddedfb !important;
		}
	}
}

.k-column-menu .k-hover {
	background: #ddedfb;
}

.grid-with-splitmap {
	width: calc(~"50% - 2px");
	float: left;
}

.k-grid th:last-child,
.k-grid td:last-child {
	 position: relative;
	 &::after {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		border-right: 1px solid #d5d5d5;
		box-sizing: border-box;
		pointer-events: none;
	 }
}

.k-grid .k-grid-content-locked td:last-child {
	&::after {
		content: none;
	}
}

.kendo-grid {
	font-size: 14px;

	.k-table-md {
		font-size: 14px !important;
	}

	.k-loading-mask {
		display: none;
	}


	tr {
		height: @gridRowHeight;
		&.disable {
			opacity: 0.6;

			.k-grid-delete {
				cursor: not-allowed;
			}
		}

		td {
			&.has-link {
				cursor: pointer;
				padding: 0;
			}

			>div {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			white-space: nowrap !important;
			overflow: hidden !important;
			text-overflow: ellipsis !important;
			background-color: transparent;
		}

		td.k-detail-cell {
			>div {
				overflow: visible;
			}
		}
	}

	.grid-filter-clear-all {
		cursor: pointer;
		background: url(../../global/img/grid-filter-clear-all.png) no-repeat;
		height: 16px;
		width: 15px;
	}

	.summary-filter-clear-all {
		cursor: pointer;
		background: url(../../global/img/grid/SummaryBarClearAll.png) no-repeat;
		height: 16px;
		width: 23px;
	}

	.bulkmenu-button {
		cursor: pointer;
		background: url(../../global/img/grid/bulkmenu.png) no-repeat;
		height: 20px;
		width: 16px;
	}

	.k-grid-toolbar {
		background: #ffffff;
	}

	.k-grid-header {
		background: @gridHeaderBackground;

		thead th,
		thead td {
			.k-numeric-wrap {
				padding: 0 24px 0 0;

				.k-select {
					width: 24px;

					.k-link {
						.k-icon {
							float: none;
						}
					}
				}
			}

			&.k-header {
				vertical-align: middle;
				padding: 6px 7px 5px 7px;
				z-index: @grid-column-header-z-index;

				>.k-cell-inner>.k-link,
				>.k-link {
					height: 20px;
					margin: -6px -7px -5px -7px;
					padding: 6px 7px 5px 7px;
					display: flex;
					justify-content: space-between;
					text-decoration: none;

					.column-title {
						text-overflow: ellipsis;
						white-space: nowrap;
						overflow: hidden;
					}
				}

				>.k-cell-inner {
					margin: -6px -7px -5px -7px;
					padding: 6px 7px 5px 7px;
					display: flex;
					height: 33px;

					>.k-link {
						height: 20px;
						display: flex;
						justify-content: space-between;
						text-decoration: none;
						width: 100%;
						cursor: pointer;

						.column-title {
							text-overflow: ellipsis;
							white-space: nowrap;
							overflow: hidden;
						}
					}
				}
			}
		}

		.sort-icon-template() {

			table thead tr td {
				.k-filtercell {
					&>.k-filtercell-wrapper {
						padding-right: 1px;

						.k-autocomplete,
						.k-numerictextbox,
						.tf-filter,
						.k-header {
							float: right;
							width: calc(~"100% - 1.4em");
							z-index: 0;
							border-bottom-width: 1px;

							&::-ms-clear {
								display: none;
							}
						}

						.k-dropdown-operator {
							width: 22px;
							right: inherit;
							border-radius: 0;
							border-right-width: 1px;
						}

						.k-button {
							margin-right: 0;
							margin-left: 0;
						}

						.k-datepicker,
						.k-numerictextbox {
							&+span+.k-button {
								right: 2em;
								border-radius: 0;
							}
						}

						.tf-filter {
							&+span+.k-button {
								right: 23px; //2.3em;
								border-radius: 0;
								border-right: 0;
							}
						}
					}

					.k-operator-hidden {

						.k-header,
						.k-autocomplete,
						.k-numerictextbox,
						.tf-filter,
						.k-header {
							width: 100%;
						}

						.k-button {
							right: 2em;
							border-radius: 0;
						}

						.icon-select-item {
							height: 20px;
							background-repeat: no-repeat;
							margin-top: 5px;
							width: 100%;
							display: inline-block;
							background-size: 16px;
							background-position-y: center;
						}

						.k-dropdownlist {
							.k-input-button {
								border-left: 1px solid #c5c5c5;
								right: 0;
							}
						}
					}
				}

				.hide-cross-button {
					.k-button[title="Clear"] {
						display: none;

						&.clear-custom-filter-menu-btn {
							display: inline;
							vertical-align: top;
							pointer-events: auto;
							cursor: pointer;
							opacity: 1;

							>span {
								opacity: 1;
							}
						}
					}
				}

				.k-header {
					a.k-link {
						.k-icon {

							&.k-i-arrow-n,
							&.k-i-arrow-60-up {
								border-bottom: 9px solid #FFFFFF;
								border-right: 5px solid transparent;
								border-left: 5px solid transparent;
								background-image: none;
								width: 0;
								height: 0;
								margin: 3.5px;
							}

							&.k-i-arrow-s,
							&.k-i-sort-desc-sm,
							&.k-i-sort-asc-sm,
							&.k-svg-i-sort-asc-small,
							&.k-svg-i-sort-desc-small {
								border-top: 9px solid #FFFFFF;
								border-right: 5px solid transparent;
								border-left: 5px solid transparent;
								background-image: none;
								width: 0;
								height: 0;
								margin: 3.5px;
							}
						}
					}
				}
			}
		}

		.k-grid-header-wrap {
			border-right-width: 0;
			.sort-icon-template;
		}

		.k-grid-header-locked {
			.sort-icon-template;
		}
	}

	.k-header {
		background: #4B4B4B;
		color: #cccccc;

		>.k-link,
		>.k-cell-inner>.k-link {
			color: #cccccc;
		}
	}

	.k-pager-numbers {

		.k-selected {
			background-color: transparent;
			border-color: transparent;
			color: #222222;
			font-weight: bold;
		}
	}

	tbody>tr>td>.k-button,
	.on-demand-container .k-button {
		min-width: 16px;
		text-indent: -99999px;
		border: none;
		width: 16px;
		height: 16px;

		&:hover {
			opacity: 0.8;
		}
	}

	.k-button {
		@k-button-border-width: 1px;

		&:active:hover,
		&.k-active:hover {
			background-color: @gridRowAltBkgColor;
		}

		&:focus:not(.k-disabled):not([disabled]) {
			/* move border and fix offset*/
			border: none;
			top: @k-button-border-width;
			right: @k-button-border-width;
			box-shadow: none;
		}
	}

	.k-button.k-grid-edit,
	.k-button.k-grid-import,
	.k-grid-KML-download,
	.k-button.k-grid-copyandnew,
	.k-button.k-grid-delete {
		&:active:hover {
			background-color: transparent;
		}
	}

	.k-grid-edit,
	.k-grid-import,
	.k-grid-KML-download,
	.k-grid-copyandnew,
	.k-grid-delete,
	.k-grid-restore,
	.k-grid-setting,
	.k-grid-run,
	.unassign-student-icon,
	.open-trip-icon,
	.k-grid-approve {
		height: 16px !important;
		opacity: 0.6;
	}

	.k-grid-show-eye {
		background: url('../../global/img/Icons/eye.svg') no-repeat center center;
		filter: grayscale(1) brightness(0.3);
	}

	.k-grid-hide-eye {
		background: url('../../global/img/Icons/eye-slash.svg') no-repeat center center;
		filter: grayscale(1) brightness(0.3);
	}

	.k-button.k-button-disabled {
		pointer-events: none;
		opacity: .3;
	}

	.k-grid-export {
		background: url('../../global/img/grid/export.svg') no-repeat center center;
	}

	.unassign-student-icon {
		background: url('../../global/img/unassign-student.png') no-repeat center center;
	}

	.open-trip-icon {
		background: url('../img/Routing Map/menuicon/file.png') no-repeat center center;
	}

	.k-grid-view {
		background: url('../../global/img/grid/view.png') no-repeat center center;
		cursor: pointer;
		opacity: 0.6;

		&.disable {
			opacity: 0.4;
			cursor: default;

			&:hover {
				opacity: 0.4;
			}
		}
	}

	.k-grid-download {
		background: url('../../global/img/grid/download.png') no-repeat center center;
		cursor: pointer;
		opacity: 0.6;

		&.disable {
			opacity: 0.4;
			cursor: default;

			&:hover {
				opacity: 0.4;
			}
		}
	}

	.btn-new-grid-with-associated-records:hover {
		text-decoration: underline;
		cursor: pointer;
	}

	.btn-new-grid-with-associated-records {
		display: inline-block;
	}

	.k-grid-KML-download {
		background: url('../../global/img/grid/KML-download.svg') no-repeat center center;
	}

	.k-grid-import {
		background: url('../../global/img/grid/upload.png') no-repeat center center;
	}

	.k-grid-edit {
		background: url('../../global/img/grid/editor_pencil.png') no-repeat center center;
	}

	.k-grid-copyandnew {
		background: url('../../global/img/grid/Copy-and-New-5.png') no-repeat center center;
	}

	.k-grid-copyandnew2 {
		background: url('../../global/img/Routing Map/menuicon/Copy-black.png') no-repeat center center;
	}

	.k-grid-delete {
		background: url('../../global/img/menu/Delete-Black.svg') no-repeat center center;

		&.delete-relationship {
			background: url('../../Global/img/detail-screen/remove-association.png') no-repeat center center;
			cursor: pointer;
		}

		&.reportlib-remove {
			background-image: url('../../global/img/grid/reportlib-delete-black.png');
			cursor: pointer;
		}
	}

	.k-grid-restore {
		background: url('../../global/img/icons/restore.svg') no-repeat center center;
		background-size: 16px;
	}

	.k-grid-run {
		background: url(../../global/img/grid/runnow.png) no-repeat center center;
	}

	.k-grid-setting {
		background: url('../../global/img/navigation-menu/data_source_black.svg') no-repeat center center;
		margin: 0 8px;
	}

	.k-grid-approve {
		background: url('../../global/img/menu/Approve-Black.png') no-repeat center center;
		background-size: 16px 16px;
	}

	.k-button.disable,
	.k-grid-KML-download.disable,
	.k-grid-import.disable,
	.k-grid-edit.disable,
	.k-grid-copyandnew.disable,
	.k-grid-delete.disable,
	.k-grid-approve.disable {
		opacity: 0.3;

		&:hover {
			cursor: not-allowed;
			opacity: 0.3;
			background-color: transparent;
		}
	}

	&.kendo-grid-container {
		font-size: 0px;
		background-color: rgba(204, 204, 204, 0.19);
	}

	&.kendo-grid-container>div {
		font-size: 14px;

		.k-table-md {
			font-size: 14px;
			line-height: 18px;
		}
	}

	&.kendo-grid-container {
		table tr td span.hot-link {
			&:hover {
				text-decoration: underline;
				text-underline-offset: 3px;
				cursor: pointer;
			}
		}
	}

	&.kendo-grid-container .k-grid-header-wrap.k-auto-scrollable {
		background: linear-gradient(#4B4B4B 0, #4B4B4B 33, #D6D6D6 33, #D6D6D6 100%);
	}

	.k-grid-header-wrap td.k-header:last-child {
		border-right-width: 0;
	}

	&.summarybar-showing {

		.k-grid-content,
		.k-virtual-scrollable-wrap {
			overflow-x: hidden;
		}

		>.k-pager {
			height: 25px;
			position: absolute;
			bottom: -@summaryGridHeight;
			width: calc(~"100% - 2px");
		}

		&+.kendo-summarygrid-container table,
		table {
			padding-right: 18px;
		}
	}

	.k-grid-pager {
		text-align: center;
		height: 24px;
		overflow: hidden;
		padding: 4px;
		border-top: #c9c9c9 1px solid;
		box-sizing: content-box;
		border-color: #c9c9c9;
		background-color: #f2f2f2;
		color: #333;
		column-gap: 0;

		.k-pager-numbers {
			float: none;
		}

		>.k-pager-nav {
			float: none;
			width: 2em;
			display: inline-block;
		}

		.k-pager-sizes {
			float: left;
		}

		.k-pager-refresh {
			display: none;
		}
	}

	.k-pager>.k-link {
		border-radius: 0;
	}

	.k-pager-numbers .k-link {
		border-radius: 0;
	}

	.k-dropdown .k-input {
		background-color: #fff;

		&::before {
			content: '';
		}
	}

	.k-dropdownlist .k-input-inner {
		background-color: #fff;

		&::before {
			content: '';
		}
	}

	.k-pager-info {
		// kendo grod will set its footer info visible everytime when resizing the browser, force set it to hidden.
		display: none !important;
	}

	.input-group-addon {
		line-height: 12px;
	}

	.form-control {
		height: 26px;
	}
}

.mergeDocumentGrid.kendo-grid .k-grid-header thead th.k-header>.k-cell-inner>.k-link,
.mergeDocumentGrid.kendo-grid .k-grid-header thead td.k-header>.k-cell-inner>.k-link {
	cursor: default;

	span {
		cursor: default;
	}
}

.list-mover-mover {

	.kendo-grid .k-grid-header-wrap.k-auto-scrollable,
	.k-grid-header {
		background: @listMoverGridHeaderBackground;

		thead td {
			border-left-width: 1px;
		}
	}
}

.k-animation-container {
	.k-list-container {

		li.k-hover {
			border-color: transparent;
		}

		.k-list>li.k-focus {
			border-color: #007cc0;
		}
	}
}

.k-list-container {
	background-color: #ffffff;

	li {

		&:hover,
		&.k-hover {
			background: #FFFFCC;
			border-color: transparent;
		}

		&.k-selected {
			background-color: #FFFFCC;
		}

		&.k-focus.k-selected {
			background: #FFFFCC;
			color: #222222;
			border-color: transparent;
		}

		.k-list-item-text {
			width: 100%;
		}

		.icon-select-item {
			height: 16px;
			background-repeat: no-repeat;
			width: 100%;
			display: inline-block;
			background-size: 16px;
			background-position-y: center;
			margin-left: -20px;
			margin-top: 5px;
		}
	}
}

.k-list-md .k-list-optionlabel {
	border: none;
	box-shadow: none;
	padding: 0 4px;

	&.k-selected.k-focus {
		background: #ffffcc;
	}
}

.k-mobile {
	.kendo-summarygrid-container {
		.k-grid-content {
			min-height: @summaryGridMobileHeight;
		}
	}

	.kendo-grid.summarybar-showing {

		.k-pager {
			bottom: -65px;
		}

		table {
			padding-right: 0px;
		}

		.k-detail-row {
			.detail-grid {

				.k-pager {
					bottom: 0;
				}
			}
		}
	}
}

.kendo-summarygrid-container {
	border-top-width: 2px;
	margin-top: -39px;
	border-right: 0;

	.k-grid-header tr:first-child {
		display: none;
	}

	.k-dropdown-wrap {
		background: #ffffff;
		background-color: #ececec;
	}

	.k-grid-content {
		overflow-x: scroll;
		overflow-y: hidden;
		margin-top: -65px;
		min-height: @summaryGridHeight;
	}

	.k-grid-header {
		margin-top: 27px;
		position: relative;
		z-index: 999;
		background-image: none;
		background-color: #D6D6D6;

		.k-grid-header-wrap {
			border-right-width: 0px;
		}
	}

	.k-grid-content-locked {
		margin-top: -65px;
		background-color: #EEEEEE;

		tr {
			background-color: inherit;
		}
	}

	.k-grid-container {
		overflow: inherit;
	}
}

.kendo-grid .k-filtercell .k-filter,
.kendo-grid .k-filtercell .k-i-filter {
	display: block;
	width: 22px;
	height: 22px;
}

.k-popup ul.k-list li.filter {
	@item-horizon: 1px #eee solid;

	&.custom,
	&.list {
		border-top: @item-horizon;
	}
}

.k-popup ul.k-list li.filter,
.kendo-grid .k-filtercell .k-filter,
.kendo-grid .k-filtercell .k-i-filter {
	&.custom {
		background-image: url('../../global/img/Filter/Custom.png');
		margin-bottom: -2px;
	}

	&.all {
		background-image: url('../../global/img/Filter/all.svg');
		margin-bottom: -2px;
	}

	&.lastxdays {
		background-image: url('../../global/img/Filter/lastxdays.svg');
		margin-bottom: -2px;
	}

	&.lastxhours {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/lastxhours.svg');
	}

	&.lastxmonths {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/lastxmonths.svg');
	}

	&.lastxweeks {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/lastxweeks.svg');
	}

	&.lastxyears {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/lastxyears.svg');
	}

	&.nextxdays {
		background-image: url('../../global/img/Filter/nextxdays.svg');
		margin-bottom: -2px;
	}

	&.nextxhours {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/nextxhours.svg');
	}

	&.nextxmonths {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/nextxmonths.svg');
	}

	&.nextxweeks {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/nextxweeks.svg');
	}

	&.nextxyears {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/nextxyears.svg');
	}

	&.olderthanxdays {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/lastxyears.svg');
	}

	&.olderthanxmonths {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/olderthanxmonths.svg');
	}

	&.olderthanxyears {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/olderthanxyears.svg');
	}

	&.onyearx {
		margin-bottom: -2px;
		background-image: url('../../global/img/Filter/onyearx.svg');
	}

	&.lastmonth {
		background-image: url('../../global/img/Filter/lastmonth.svg');
		margin-bottom: -2px;
	}

	&.lastweek {
		background-image: url('../../global/img/Filter/lastweek.svg');
		margin-bottom: -2px;
	}

	&.lastyear {
		background-image: url('../../global/img/Filter/lastyear.svg');
		margin-bottom: -2px;
	}

	&.nextmonth {
		background-image: url('../../global/img/Filter/nextmonth.svg');
		margin-bottom: -2px;
	}

	&.nextweek {
		background-image: url('../../global/img/Filter/nextweek.svg');
		margin-bottom: -2px;
	}

	&.nextyear {
		background-image: url('../../global/img/Filter/nextyear.svg');
		margin-bottom: -2px;
	}

	&.onorafterx {
		background-image: url('../../global/img/Filter/onorafterx.svg');
		margin-bottom: -2px;
	}

	&.onorbeforex {
		background-image: url('../../global/img/Filter/onorbeforex.svg');
		margin-bottom: -2px;
	}

	&.onx {
		background-image: url('../../global/img/Filter/onx.svg');
		margin-bottom: -2px;
	}

	&.thismonth {
		background-image: url('../../global/img/Filter/thismonth.svg');
		margin-bottom: -2px;
	}

	&.thisweek {
		background-image: url('../../global/img/Filter/thisweek.svg');
		margin-bottom: -2px;
	}

	&.thisyear {
		background-image: url('../../global/img/Filter/thisyear.svg');
		margin-bottom: -2px;
	}

	&.today {
		background-image: url('../../global/img/Filter/today.svg');
		margin-bottom: -2px;
	}

	&.tomorrow {
		background-image: url('../../global/img/Filter/tomorrow.svg');
		margin-bottom: -2px;
	}

	&.yesterday {
		background-image: url('../../global/img/Filter/yesterday.svg');
		margin-bottom: -2px;
	}

	&.nextbusinessday {
		background-image: url('../../global/img/Filter/nextbusinessday.svg');
		margin-bottom: -2px;
	}

	&.list {
		background-image: url('../../global/img/Filter/List.png');
	}

	&.contains {
		background-image: url('../../global/img/Filter/Contains.png');
	}

	&.isequalto {
		background-image: url('../../global/img/Filter/Equals.png');
	}

	&.isnotequalto {
		background-image: url('../../global/img/Filter/DoesNotEqual.png');
	}

	&.startswith {
		background-image: url('../../global/img/Filter/StartsWith.png');
	}

	&.doesnotcontain {
		background-image: url('../../global/img/Filter/DoesNotContain.png');
	}

	&.endswith {
		background-image: url('../../global/img/Filter/EndsWith.png');
	}

	&.islessthanorequalto {
		background-image: url('../../global/img/Filter/LessThanEqual.png');
	}

	&.isgreaterthanorequalto {
		background-image: url('../../global/img/Filter/GreaterThanEqual.png');
	}

	&.isgreaterthan {
		background-image: url('../../global/img/Filter/GreaterThan.png');
	}

	&.isafter {
		background-image: url('../../global/img/Filter/GreaterThan.png');
	}

	&.islessthan {
		background-image: url('../../global/img/Filter/LessThan.png');
	}

	&.isbefore {
		background-image: url('../../global/img/Filter/LessThan.png');
	}

	&.isbeforeorequalto {
		background-image: url('../../global/img/Filter/LessThanEqual.png');
	}

	&.isafterorequalto {
		background-image: url('../../global/img/Filter/GreaterThanEqual.png');
	}

	&.isnull {
		background-image: none;
	}

	&.isnotnull {
		background-image: none;
	}

	&.isempty {
		background-image: url('../../global/img/Filter/Empty.png');
	}

	&.isnotempty {
		background-image: url('../../global/img/Filter/NotEmpty.png');
	}

	&.k-hover,
	&.k-focus {
		border-width: 1px 0;
	}
}

.k-popup ul.k-list {
	li.filter {
		padding: 0 4px;
		padding-left: 30px;
		background-position: left center;
		border-width: 1px 0;
		border-style: solid;
		line-height: 1.9em;
		min-height: 1.9em;
	}
}

.k-reorder-cue .k-icon {
	background: none;
	width: 17px;
	height: 8.5px;
	left: -8.5px;

	&.k-i-arrow-n {
		bottom: -9.5px;

		&:after {
			content: "";
			display: block;
			border-bottom: 8.5px solid #D74B3C;
			border-right: 8.5px solid transparent;
			border-left: 8.5px solid transparent;
		}
	}

	&.k-i-arrow-s,
	&.k-i-sort-desc-sm {
		top: -9.5px;

		&:after {
			content: "";
			display: block;
			border-top: 8.5px solid #D74B3C;
			border-right: 8.5px solid transparent;
			border-left: 8.5px solid transparent;
		}
	}
}

.kendo-grid {
	table {
		// width: 0;
		margin-bottom: 0;
	}

	&.vertical-size-medium,
	&.vertical-size-large,
	&.full-width {
		table {
			width: 100%;
		}
	}

	tr {
		@drag-target-cursor-color: #D74B3C;
		@drag-target-cusor-them: 2px solid @drag-target-cursor-color;

		&.drag-target-insert-after-cursor {
			td {
				border-bottom: @drag-target-cusor-them; //border-top-width: 3px;
			}
		}

		&.drag-target-insert-before-cursor {
			td {
				border-top: @drag-target-cusor-them; //border-top-width: 3px;
			}
		}

		& .drag-target-cursor-left-triangle,
		& .drag-target-cursor-right-triangle {
			height: 0px;
			width: 0px;
			border: 7px solid #000;
			border-color: transparent;
			position: absolute;
			margin-top: 0px;
		}

		& .drag-target-cursor-left-triangle {
			border-left-color: @drag-target-cursor-color;
			margin-left: 1px;
		}

		& .drag-target-cursor-right-triangle {
			border-right-color: @drag-target-cursor-color;
		}
	}

	.k-grid-pager .k-pager-numbers {
		position: relative;
		left: 0;
		z-index: 1000;
	}

	.k-pager-numbers+.k-link {
		margin-left: 0;
	}

	.k-pager-numbers .k-current-page .k-link {
		border-radius: 0;
	}

	.k-pager-numbers.k-state-expanded .k-current-page .k-link,
	.k-pager-numbers.k-expanded .k-current-page .k-link {
		border-radius: 0;
	}
}

col.k-sorted {
	background-color: unset;
}

.open-datasource-grid-container {
	.k-grid-content {

		.k-selected {
			background: #FFFFCC;
		}

		col.k-sorted {
			background-color: unset;
		}
	}
}

@k-header-locked-background-color: #3C3C3C;
@k-header-default-background-color: #4B4B4B;

.not-only-first-column-locked {

	.k-grid-header-locked,
	.k-grid-content-locked,
	.k-grid-footer-locked {
		&:after {
			content: '';
			position: absolute;
			right: 0;
			top: 0;
			width: 3px;
			background-color: #4B4B4B;
		}
	}

	.k-grid-content-locked {
		&:after {
			width: 3px;
			height: 2000%;
		}
	}

	.k-grid-header-locked {
		overflow: visible;

		&:after {
			width: 3px;
			height: calc(~'100% + 1px');
		}
	}

	.k-grid-header-locked {
		.k-header {
			background-color: @k-header-locked-background-color;
		}
	}

	.kendo-summarygrid-container {
		.k-grid-content-locked {
			overflow: visible;

			&:after {
				top: -20px;
				height: calc(~'100% + 20px');
			}
		}
	}

	.kendo-grid-container {
		.k-grid-header-locked {
			-webkit-border-image: -webkit-linear-gradient(@k-header-default-background-color 0, @k-header-default-background-color @gridRowHeight, #d6d6d6 @gridRowHeight, #d6d6d6 100%) 1 100%;
			-o-border-image: -o-linear-gradient(@k-header-default-background-color 0, @k-header-default-background-color @gridRowHeight, #d6d6d6 @gridRowHeight, #d6d6d6 100%) 1 100%;
			-moz-border-image: -moz-linear-gradient(@k-header-default-background-color 0, @k-header-default-background-color @gridRowHeight, #d6d6d6 @gridRowHeight, #d6d6d6 100%) 1 100%;
		}
	}

	.k-grid-content-locked {
		.k-alt {
			>td {
				background-color: #E5EAF1;
			}
		}

		.k-alt.k-selected {
			>td {
				background-color: @gridRowSelectedBkgColor;
			}
		}
	}

	.k-grid-header-locked {
		.k-header {
			background-color: @k-header-locked-background-color;
		}
	}

	.k-grid-header-locked,
	.k-grid-content-locked {
		background-color: #EEEEEE;
	}

	.k-grid-header-locked,
	.kendo-summarygrid-container .k-grid-content-locked {
		overflow: initial;
	}
}

.k-grid-header-locked,
.k-grid-content-locked,
.k-grid-footer-locked,
.kendo-summarygrid-container .k-grid-content-locked {
	&:after {
		height: 0;
	}
}

.k-grid-header-locked {
	.k-header {
		background-color: @k-header-default-background-color;
	}
}

.kendo-grid-container .k-grid-header-locked {
	-webkit-border-image: none;
	-o-border-image: none;
	-moz-border-image: none;
}

.k-grid-content,
.managelayoutgrid-container,
.k-grid-content-locked {
	table {
		border-bottom: 1px #E1E1E1 solid !important;
	}

	.k-selected {
		.k-link {
			color: #2e2e2e;
		}
	}
}

.whiteboardManageModal, .mapviewer-workspace-manage {
	.k-grid-content {
		height: 242px !important;
	}

	.k-grid-header {
		thead th.k-header>.k-cell-inner {

			.k-link,
			.k-column-title {
				cursor: default;

			}
		}
	}
}

.managelayoutgrid-container,
.managefiltergrid-container {
	.k-grid-header {
		thead th.k-header>.k-cell-inner {

			.k-link,
			.k-column-title {
				cursor: default;

			}
		}
	}

	.k-grid-content {
		overflow-y: auto;
		height: 267px !important;
	}

	.k-grid-footer {
		border: none;
		justify-content: flex-end;
	}
}

.associate-records .k-grid-header-wrap.k-auto-scrollable {
	margin-inline-end: 0px;
}

.k-virtual-scrollable-wrap,
.k-treelist .k-grid-content,
.tabstrip-customized-dashboards,
.tabstrip-userdefinedfields .k-grid-content {
	background-color: #fff;

	table.kendogrid-blank-fullfill {
		position: absolute;
		box-sizing: border-box;
		width: 100%;
		border-bottom: 1px #E1E1E1 solid;
		background: #fff;

		>div.fillItem {
			border-collapse: collapse;
		}

		.l {
			background-color: #fff;
			height: @gridRowHeight;
		}

		.l-alt {
			background-color: @gridRowAltBkgColor;
			height: @gridRowHeight;
		}

		.k-state-dragover,
		.k-dragover {
			background: #ddedfb;

			&:not(:first-child) {
				margin-top: -1px;
			}

			height: 32px !important;
			border: 2px solid #00008B;
		}
	}

	.table-blank-fullfill {
		position: relative;

		tr.k-master-row:nth-of-type(odd) {

			&,
			&.k-sate-selected {
				background-color: #FFFFFF;
			}
		}
	}
}

.managelayoutgrid-container .k-grid-header {
	table {
		border-bottom: 0;
	}
}

.k-grid td {
	cursor: default;
	padding: 4px 7px;
}

.k-grid tr.k-detail-row>td {
	padding: 7px;
}

.gridAlert {
	z-index: @ent-grid-alert-z-index;

	.title {
		//color: #31708f;
		font-weight: bold;
	}

	.close {
		font-size: 16px;
		right: 0; //color: #31708f;
		opacity: 1;
	}

	.message {
		//color: #5A93AD;
	}
}

.unassignedEntity.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-button {
	right: 0;
}

.unassignedEntity.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-numerictextbox+button,
.unassignedEntity.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-dropdown+button,
.unassignedEntity.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-datepicker+button {
	right: 21px;
}

.availablecolumngrid-container.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-numerictextbox+button,
.availablecolumngrid-container.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-dropdown+button,
.availablecolumngrid-container.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-datepicker+button {
	right: 21px;
}

.availablecolumngrid-container.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell .k-operator-hidden .k-numerictextbox {
	width: 100%;
}

a.tf-grid-mobile-inner-link {
	color: black;
	text-decoration: none;
	outline: none;
	display: block;
	height: @gridRowHeight;
	line-height: @gridRowHeight;
	width: 100%;
	padding: 0 .6em;

	&.link:hover {
		color: black;
	}
}

.kendo-grid {
	.eyecolumn {
		cursor: pointer;
		border: none;

		&:before {
			content: '\e106';
			display: inline-block;
			width: 20px;
			height: 20px;
			font-family: 'Glyphicons Halflings';
			background-color: #F6F6F6;
		}

		&:checked:before {
			content: "\e105";
		}
	}
}

@media only screen and (max-width: 1024px) {
	.k-mobile .kendo-grid {

		.k-grid-content.overflow-hidden,
		.k-grid-content.overflow-hidden .k-virtual-scrollable-wrap,
		&.kendo-summarygrid-container .k-grid-content {
			overflow: hidden;
		}
	}
}

.directoryview {
	position: relative;

	table {
		width: 100%;
		height: 100%;

		.top-tr {
			height: 32px;

			.top-buttons {
				margin-top: 10px;
				margin-right: 10px;
				height: 22px;

				.iconbutton {
					float: right;
					margin-right: 10px;
				}
			}
		}

		.content {
			text-align: center;

			.title {
				font-weight: bold;
				font-size: 16px;
				margin-top: 10px;
			}
		}
	}
}

.edit-kendo-columns.list-mover-mover {

	.k-pager.k-grid-pager {
		border: #E1E1E1 1px solid;
		border-top-width: 0;
	}

	&.disabled {
		pointer-events: none;
		opacity: 0.5;
	}
}

.k-filtercell .input-group.tf-filter+span+button {
	//customizedtimepicker clean button position fix
	margin-right: 1px !important;
}

.k-filtercell .input-group.tf-filter+button {
	//VIEW-674 ListMover  customizedtimepicker clean button position fix
	margin-right: 22px !important;
}

.no-matching-records,
.tip-message {
	margin-top: 15px;
	color: #848484;
}

.tip-message {
	margin-left: 15px;
}

.omit-container {
	max-height: 200px;
	overflow-y: auto;
	margin-bottom: 30px;
}

.omitRecord {
	line-height: 25px;
	border-color: #ACACAC;
	border-style: solid;
	border-width: 1px;
	border-radius: 0px;
	margin-right: 10px;
	text-align: left;
	overflow: hidden;
	margin-top: 5px;
	padding-right: 5px;
}

// .kendo-grid .k-grid-header{
.k-input,
span.k-numeric-wrap,
span.k-numerictextbox,
input.form-control.datepickerinput {
	height: 22px;
	box-sizing: border-box;
}

span.k-dropdown-operator {
	height: 22px;
	box-sizing: border-box;
}

span.k-dropdown-wrap,
span.k-dropdown-wrap.k-disabled {
	height: 22px;
	padding-right: 20px;
	box-sizing: border-box;
}

.k-picker-wrap {
	height: 22px;
	box-sizing: border-box;
	padding: 0px 22px 0px 0px;
}

form.k-filter-menu .k-filter-menu-container .k-datepicker .k-picker-wrap {
	margin-top: 7px !important;

	.k-input {
		height: 100% !important;
		border-right: 1px solid #BFBFBF;
	}

	.k-select {
		border-left: 0 !important;
		line-height: 18px;
	}
}

.k-datepicker.k-header {
	height: 22px;
	box-sizing: border-box;
}

.k-dropdown-wrap>.k-select,
.k-dropdown-wrap.k-disabled>.k-select {
	height: 22px;
	line-height: 22px;
	min-height: 22px;
	box-sizing: border-box;
	width: 22px;
}

.k-grid-header {
	.k-dropdownlist {
		.k-input-button {
			height: 22px;
			line-height: 22px;
			min-height: 22px;
			box-sizing: border-box;
			width: 22px;
			padding: 0;
			margin: 0;
		}
	}
}

.k-numerictextbox .k-link {
	height: 11px;
	line-height: 9px; // .k-icon.k-i-arrow-n,
	// .k-icon.k-i-arrow-s {
	// 	background-position-x: 1px !important;
	// }
}

.k-filtercell {
	height: 22px;
	line-height: 22px;
	min-height: 22px;
	box-sizing: border-box;
	overflow: hidden;

	&>span {
		height: 22px;
		line-height: 22px;
		min-height: 22px;
		box-sizing: border-box;
	}

	.k-filter {
		vertical-align: initial;
	}

	.k-button.k-icon-button {
		height: 22px;
		margin: 0px;

		.k-icon.k-i-close {
			left: -3.4px;
			top: -2px;
		}

		.k-icon.k-svg-i-filter-clear {
			&:before {
				content: "\e11b";
				font-size: 12px;
			}

			svg {
				display: none;
			}
		}
	}

	&[data-kendo-field] {
		.k-numerictextbox {
			.k-numeric-wrap::before {
				content: initial;
			}
		}
	}
}

.kendo-grid-container {
	&.has-locked-column .k-grid-content {
		border-left: #000000 1px solid;

		tbody td:first-child {
			border-left: none;
		}
	}

	&.thematic-applied {
		&.has-locked-column .k-grid-content-locked table tr {
			background-color: #ededed !important;

			td {
				color: #333 !important;
			}
		}

		.k-grid-content,
		.k-grid-content-locked {
			table {
				tr {
					background-color: #fff;

					&.k-selected {
						td {
							border-top: 1px solid @gridRowSelectedBorderColor !important;
							border-bottom: 1px solid @gridRowSelectedBorderColor !important;
						}
					}

					&:not(.k-selected) {
						td.thematic-light {
							color: #fff;
						}
					}

					td {
						border-top: 1px solid @gridRowBorderColor;
					}
				}
			}
		}

		table.kendogrid-blank-fullfill {
			tbody {
				>tr {
					border-top: 1px solid @gridRowBorderColor;
					box-sizing: border-box;

					&.k-selected {
						border-top: 1px solid @gridRowSelectedBorderColor !important;
						border-bottom: 1px solid @gridRowSelectedBorderColor !important;
					}
				}
			}
		}
	}

	.k-filtercell {
		.datepickerbutton {
			cursor: pointer;

			span {
				cursor: pointer;
			}
		}

		.k-button.k-icon-button {
			.k-icon.k-i-close {
				left: -3px;
				top: -3px;
			}
		}
	}
}

.k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper .k-dropdown-operator {
	background-position: 4px 3px;
}

.kendo-grid .k-grid-header {

	.k-grid-header-wrap,
	.k-grid-header-locked {

		table thead tr td .k-filtercell .k-operator-hidden {
			.k-button {
				right: 2px;
			}

			.k-datepicker {
				button.k-button {
					position: relative;;
				}
			}
			button.k-button {
				position: absolute;
				top: 0;

				&.k-spinner-decrease,
				&.k-spinner-increase {
					height: 11px;
					line-height: 9px;
					right: 0;
				}

				&.k-spinner-decrease {
					top: 10px
				}
			}
		}
	}
}

.k-dropdown-wrap.k-state-border-up,
.k-dropdown-wrap.k-state-border-up .k-input,
.k-dropdown-wrap.k-border-up,
.k-dropdown-wrap.k-border-up .k-input {
	border-radius: 0;
}

.k-popup ul.k-list li.filter,
.kendo-grid .k-filtercell .k-filter {
	background-repeat: no-repeat;
	background-size: 16px;
	background-position: 3px center;
}

.kendo-grid .k-grid-header {

	.k-dropdown-wrap>.k-select {
		width: 22px;
	}

	.k-grid-header-wrap,
	.k-grid-header-locked {

		table tr td .k-filtercell>.k-filtercell-wrapper .k-dropdown-operator {
			width: 22px;
		}
	}

	.k-dropdown-wrap>.k-select {
		width: 21px;
	}

	.k-numerictextbox .k-link {
		width: 21px;
	}

	.k-filtercell .k-filtercell-wrapper {
		position: relative;
	}

	.k-filtercell .k-button.k-icon-button {
		width: 22px;
	}
}

.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper .k-header {
	border-bottom: none;
}

.k-picker-wrap .k-select {
	width: 22px;
	height: 21px;
	line-height: 21px;
	min-height: 21px;
	box-sizing: border-box;
}

.kendo-grid .k-grid-header td .k-numeric-wrap {
	padding-right: 22px;

	.k-select {
		width: 22px;
		height: 21px;
		line-height: 21px;
		min-height: 21px;
		box-sizing: border-box;
	}
}

.k-dropdown-wrap .k-input,
.k-picker-wrap .k-input,
.k-numeric-wrap .k-input,
.k-dropdown-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-selected-color,
.k-numeric-wrap.k-state-border-down .k-input,
.k-autocomplete.k-state-border-down .k-input,
.k-dropdown-wrap.k-border-down .k-input,
.k-picker-wrap.k-border-down .k-input,
.k-picker-wrap.k-border-down .k-selected-color,
.k-numeric-wrap.k-border-down .k-input,
.k-autocomplete.k-border-down .k-input {
	border-radius: 0;
}

.k-filter-menu,
.k-dropdown {
	.k-dropdown-wrap {
		.k-input {
			height: 20px;
			line-height: 20px;
			padding: 0px;
		}

		.k-select {
			line-height: 18px;
		}
	}

	.k-textbox {
		height: 22px;
		line-height: 22px;
		margin-top: 0.5em;
	}
}

.merge-doc-toolbar .k-dropdown .k-dropdown-wrap .k-input {
	line-height: 26px;
	height: 26px;
}

.merge-doc-toolbar .k-colorpicker .k-tool-icon {
	top: -5px;
}

.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper {

	.k-datepicker+span+.k-button,
	.k-numerictextbox+span+.k-button,
	.tf-filter+span+.k-button {
		right: 22px;
		border-radius: 0;
	}
}

.k-numerictextbox .k-select .k-link span.k-i-arrow-s {
	background-position: 0 -36px;
}

.k-numerictextbox .k-select .k-link span.k-i-arrow-n {
	background-position: 0 -2px;
}

.k-numerictextbox .k-select .k-link span.k-i-arrow-60-up {
	background-position: 0 -2px;
}

.k-header {

	&>.k-filter-list-btn,
	&>.k-grid-filter.k-filter-custom-btn {
		height: 21px;
		z-index: 1;
		background-color: #eee;
		border-width: 0 0 1px 1px;
		border-style: solid;
		border-color: #c5c5c5;
		position: absolute;
		padding: 0;
		margin: 0;
		top: 42px;
		width: 22px;
		box-sizing: border-box;
		display: flex;
		justify-content: center;
		align-items: center;
		right: 9px;
		@parent-control-right-padding: .6em;

		&.z-left-btn {
			right: @parent-control-right-padding;
		}

		&.o-left-btn {
			right: calc(~"22px + "@parent-control-right-padding);
		}

		&.t-left-btn {
			right: calc(~"44px + "@parent-control-right-padding);
		}

		&:hover {
			cursor: default;
		}
	}

	&>.k-grid-filter.k-filter-custom-btn,
	&>.k-filter-list-btn {

		.k-icon.k-filter,
		.k-icon.k-i-filter {
			background-image: url('../../global/thirdparty/kendo/styles/default/sprite.png');
		}

		border-width: 0.01em;
	}
}

// for display filter menu btn in correct postion
.k-grid-header thead td.k-header {
	position: relative;
	overflow: visible;
}

.kendo-grid .k-command-cell .k-button:focus:not(.k-disabled):not([disabled]) {
	border: none !important;
}

.kendo-grid .k-button:focus:not(.k-disabled):not([disabled]) {
	height: 22px;
	margin: 0;
	border: 1px #bfbfbf solid !important;
}

.k-numerictextbox:hover {
	.k-select {
		&:hover {
			background-color: rgb(236, 236, 236);

			.k-link:hover {
				background-color: #D0C9C6;
			}
		}
	}
}

a.drill-down-links {
	cursor: pointer;
	text-decoration: none;
	color: #333333;

	&:hover {
		text-decoration: underline;
	}
}

.lg-filter-dropdownlist {
	width: 200px !important;
}

.k-filter-custom-input,
.k-filter-list-input {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.k-filter-custom-input {
	&.datepickerinput {
		border-right-width: 0;
	}

	&.text-ellipsis {
		float: left !important;
		height: 20px;

		&:not(.datepickerinput) {
			width: calc(~'100% - 42px') !important;
		}

		//&.datepickerinput,
		&[data-kendo-role='datepicker'] {
			width: calc(~'100% - 32px') !important;
		}
	}
}

.k-numeric-wrap .k-filter-custom-input.text-ellipsis {
	float: left !important;
	width: calc(~'100% - 21px') !important;
}

.k-grid .k-button.clear-custom-filter-menu-btn {
	margin-left: 0;
	margin-top: 0;
	border: none; //border-left: blue 1px solid;
	border-left: 1px #B6B6B6 solid;
	height: 20px;
}

.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper .tf-filter[data-kendo-role="customizedtimepicker"],
.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper .tf-filter[data-kendo-role="customizeddatetimepicker"] {
	.k-filter-custom-input.text-ellipsis {
		//width: calc(~'100% - 21px');
		margin-top: 0;
	}

	.clear-custom-filter-menu-btn {
		position: absolute;
		right: 21px;
		top: 1px;
		z-index: 100;
	}
}

.k-grid .k-datepicker .k-button.clear-custom-filter-menu-btn {
	position: relative;
}

.k-filter-list-input:not(.datepickerinput) {
	width: calc(~'100% - 20px') !important;
	padding-right: 2px;

	&[data-kendo-role="datepicker"] {
		width: 100% !important;
	}
}

.k-numeric-wrap .k-filter-list-input {
	width: 100% !important;
}

.k-datepicker.k-header {
	background-color: #cccccc;
}

form.k-filter-menu {
	box-shadow: none;

	.k-button.k-primary {
		background-color: #333333;
		border-color: #444444;
		outline: none !important;

		&:active:hover {
			border-color: #444444;
			background-color: #333333;
		}
	}

	.k-button.cancelButton {
		&:active {
			border-color: #bfbfbf;
			background-color: #eaeaea;
			color: #333;
		}
	}
}

.calendar-edit-table-parent {
	.input-group-addon.glyphicon.datepickerbutton {
		box-sizing: border-box;
	}
}

// .clearFilterMenu {
// 	position: absolute;
// 	right: 1.8em;
// 	z-index: 2;
// 	justify-content: center;
// 	align-items: center;
// 	cursor: pointer;
// 	&:focus {
// 		right: 21px !important;
// 	}
// }
.k-header {
	&>.k-grid-filter.k-filter-custom-btn {
		top: 43px;
		right: 10px;
		border: none; // border-left: blue 1px solid;
		border-left: 1px #B6B6B6 solid;
		height: 20px;
	}

	&>.k-filter-list-btn {
		height: 22px;
		top: 42px;
		right: 9px;
		width: 22px;
	}

	&:not(.k-datepicker) {
		.clearButtonPosition {
			top: -1px;
			right: 1.7em;
		}
	}
}

.kendo-grid.availablecolumngrid-container .k-grid-header .k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper .tf-filter {
	width: calc(100%) !important;
}

.k-animation-container .k-icon {
	background-image: url('../../Global/ThirdParty/Kendo/Styles/Default/sprite.png');
	opacity: 1;
}

.k-animation-container a.k-link .k-icon {
	background-image: url('../../Global/ThirdParty/Kendo/Styles/Default/sprite.png');
	opacity: 1;
}

.k-filter-menu.k-popup>div .input-group.tf-filter {
	.datepickerinput {
		border-radius: 0;
	}
}

.k-filtercell .k-numerictextbox .k-icon {
	height: 16px;
}

.k-filter-menu.k-popup.k-group.k-reset.k-state-border-up,
.k-filter-menu.k-popup.k-group.k-reset.k-border-up {
	background-color: #EEEEEE;

	.k-dropdown-wrap>.k-select,
	>.k-select {
		border-left-style: solid;
		border-left-width: 1px;
		border-right-color: rgb(204, 204, 204);
		border-left-color: rgb(204, 204, 204);
		min-height: 21px;
		height: 21px;
		border-bottom-width: 1px;
		border-bottom-style: solid;
		background-color: #EEEEEE;
		cursor: pointer;

		.k-icon {
			cursor: pointer;
		}
	}

	.k-hover,
	.k-focus,
	.k-textbox:focus,
	.k-textbox:hover {
		border-color: #B6B6B6;

		.k-select {
			border-color: #B6B6B6;
			background-color: #B6B6B6;
		}
	}

	.k-numerictextbox>.k-select {
		height: 100%;
		min-height: 1.65em;
		width: 21px;

		&:focus,
		:hover {
			border-color: #EEEEEE;
			background-color: #EEEEEE;
		}
	}

	.k-dropdown-wrap>.k-select {
		width: 22px;
	}
}

.textEllipsis {
	float: left !important;
}

.k-filter-menu.k-popup.k-group.k-reset.k-state-border-up .k-numerictextbox,
.k-filter-menu.k-popup.k-group.k-reset.k-border-up .k-numerictextbox {
	@background-color-thick: #b6b6b6;
	@background-color-light: #eee;

	.k-hover {
		.k-select:hover {
			background-color: @background-color-light;
		}
	}

	.k-select>.k-link {
		height: 10px;
		line-height: 10px;

		&:hover {
			background-color: @background-color-thick;
			height: 10px;
			line-height: 10px;
		}

		.k-icon.k-i-arrow-n,
		.k-icon.k-i-arrow-s,
		.k-icon.k-i-arrow-60-up,
		.k-icon.k-i-sort-desc-sm {
			&:hover {
				background-color: transparent;
			}

			background-color: transparent;
		}
	}
}

.k-autocomplete .k-loading {
	bottom: 2px;

	&.right-button {
		right: 25px;
	}
}

.popup-reduce-records-model-btn {
	//background-color:red;
	background-image: url('../../global/thirdparty/kendo/styles/Black/sprite.png');
	background-position: -32px -80px;
	background-repeat: no-repeat;
	position: absolute;
	height: 16px;
	width: 16px;
	top: 8px;
	right: 5px;
}

.list-mover-mover .grid-container {

	.k-grid-container {
		border-bottom: #C5C5C5 1px solid;
	}

	.k-grid-content {
		border-bottom-color: #C5C5C5;
	}
}

.modal-body .bold-Label {
	font-weight: bold;
}

.filtername-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 170px;
	display: block;
	float: left;
	text-align: left;
	padding-left: 2px;
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.summarybarMobile {
	background-image: url('../../global/img/grid/summary.png');

	&.open {
		background-image: url('../../global/img/grid/summaryBarOpen-purple.png');
	}
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.viewReport,
.iconbutton.viewReport {
	background-image: url('../../global/img/grid/ViewReport-Black.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.save,
.iconbutton.save {
	background-image: url('../../global/img/grid/Save-Black.png');
}

.iconbutton.save-publish {
	background-image: url('../../global/img/Routing Map/menuicon/save-publish.svg');
	background-size: 22px;
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.newCopy,
.iconbutton.newCopy {
	background-image: url('../img/menu/Copy.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.email,
.iconbutton.email {
	background-image: url('../../global/img/Icons/Email-Black.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.sendTo,
.iconbutton.sendTo {
	background-image: url('../../global/img/Icons/send.png');
}

.tf-contextmenu .menu-item .iconbutton.checkmark {
	background-image: url('../../global/img/grid/checkmark.png');
	float: right;
	margin-top: 3px;
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.omit,
.iconbutton.omit {
	background-image: url('../../global/img/menu/omit.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.new-grid-with-selected-records,
.iconbutton.new-grid-with-selected-records {
	background-image: url('../../global/img/grid/New_Grid_with_Selected_Records.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.invert,
.iconbutton.invert {
	background-image: url('../../global/img/grid/invert.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.select-all,
.iconbutton.select-all {
	background-image: url('../../global/img/Icons/select-all.png');
}

.tf-contextmenu .menu-item .menu-icon.iconbutton.clear-selection,
.iconbutton.clear-selection {
	background-image: url('../../global/img/Icons/clear-selection.png');
}

.document-grid .iconrow .iconbutton.more-ellipsis {
	background-image: url('../../global/img/grid/more-ellipsis.png');
}

.document-grid .iconrow .iconbutton.more-ellipsis-white {
	background-image: url('../../global/img/grid/more-ellipsis-white.png');
}

.tf-contextmenu .menu-divider.no-backGround-color {
	background-color: #FFFFFF;
}

.is-disabled-text-input,
.is-disabled-text-input:hover {
	background-color: #f3f3f3 !important;

	&.input.k-textbox {
		background-color: #f3f3f3 !important;
	}
}

.grid-container.availablecolumngrid-container .k-grid-header {
	border-width: 0;
}

.disabled-delete-btn {
	pointer-events: none;
	opacity: 0.4;
}

.cursor-style-hold {
	width: 100vw;
	height: 100vh;
	z-index: 10000;
	position: fixed;
	left: 0;
	top: 0;
}

a.link.is-mobile,
a.link.is-mobile:hover {
	color: #2e2e2e;
	text-decoration: none;
}

.menu-icon-mobile {
	display: none;
}

.tf-contextmenu .menu-item.menu-item-quick-filter .menu-label:not(:last-child)::after {
	display: none;
}

.grid-staterow-wrap {
	width: auto;
	display: block;
	height: 28px;
	align-items: center;
	justify-content: inherit;
	padding-top: 7px;
	font-size: 14px;

	.grid-staterow {
		float: none;
		text-align: right;
		display: flex;

		.grid-staterow-status {
			white-space: nowrap;
			text-align: right;
			display: flex;
			justify-content: flex-end;
			font-size: 12px;
		}

		.title {
			&~span.comma-split {
				padding-left: 0;
			}
		}
	}
}

.list-mover-mover {
	.kendo-grid.kendo-grid-container {
		background-color: #fff;
	}
}

.hide-cross-button .datepickerbutton {
	position: absolute;
}

.hide-cross-button span[data-kendo-role=customizeddatetimepicker] {
	input.text-ellipsis {
		width: ~'calc(100% - 20px)';
	}
}

.document-dataentry .k-filter-list-btn.btn.btn-default {
	opacity: 1;
}

.languages_manage_button {
	color: #D0503C;
	font-size: 13px;
	margin-top: 6px;
	float: right;
	cursor: pointer;
}

.clear-border-top {
	border-top: none;
}

.clear-border-bottom {
	border-bottom: none;
}

.border-top-for-gps-mobile-item-wrapper {
	border-top: 1px solid #d9d9d9;
}

.mobile-modal-grid-modal .last-item-has-bottom-border.mobile-modal-grid-select-option:last-child {
	border-bottom: 1px solid #d9d9d9;
}

.retry {
	background-image: url('../../global/img/icons/retry.png');
	background-repeat: no-repeat;
	height: 22px;
	width: 22px;
	opacity: 0.8;
	margin: 15px -2px 0 3px;
}

.mobile-button-text-center {
	text-align: center;
}

.list-mover-icon-container .iconrow .iconbutton {
	height: 22px;
	width: 22px;
}

span.k-autocomplete.k-header.k-disabled,
span.k-picker-wrap.k-disabled,
span.k-numeric-wrap.k-disabled {
	height: 20px;
	border-color: #B6B6B6; // border-color:black;
}

.kendo-grid .k-grid-header .k-grid-header-wrap table tr td .k-filtercell>.k-filtercell-wrapper .k-header {
	height: 22px;
	border-bottom: 1px #B6B6B6 solid;
	background-color: #F3F3F3;
}

form.k-filter-menu.k-popup.k-group.k-reset.k-state-border-up,
form.k-filter-menu.k-popup.k-group.k-reset.k-border-up {
	margin-left: 1px;
}

form.k-filter-menu .k-numeric-wrap.k-disabled,
form.k-filter-menu .k-datepicker.k-header .k-picker-wrap.k-disabled {
	opacity: 1;
}

.k-numeric-wrap .k-link,
.k-numeric-wrap .k-link+.k-link {
	border-radius: 0;
}

.form-container .k-numeric-wrap .k-select .k-link {
	background-color: transparent !important;
}

.k-dropdown-wrap .k-select,
.k-picker-wrap .k-select,
.k-numeric-wrap .k-select,
.k-datetimepicker .k-select+.k-select,
.k-list-container.k-state-border-right,
.k-list-container.k-border-right {
	border-radius: 0;
}

.k-grid .k-header .k-link {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	user-drag: none;
	-webkit-user-drag: none;
}

input.quick-add-layout-radio {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	border-radius: 50%;
	width: 16px !important;
	height: 16px !important;
	margin: 0 !important;
	border: 1px solid #333333;
	transition: 0.2s all linear;
	outline: none;
	position: relative !important;
	background-color: #DDDDDD;
	top: 3px;
}

input.quick-add-layout-radio:checked::after {
	content: '';
	width: 12px;
	height: 12px;
	background: #000000;
	position: absolute;
	top: 1px;
	left: 1px;
	border-radius: 100%;
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
}

.k-loading-mask>.k-loading-image {
	background-image: none;
}

.manageDocumentContact-container {
	border: #E1E1E1 1px solid;

	.k-grid-content {
		border: none !important;
	}
}

.k-status {
	background: white;
}

.kendo-grid.detail-grid {
	box-sizing: content-box;

	.no-matching-records {
		display: none;
	}

	&.hide-filter-row {
		>.k-grid-header .k-filter-row {
			display: none;
		}
	}
}

.gridcell {
	a.k-icon {
		cursor: pointer;
	}
}

.k-grid-footer-wrap:focus,
.k-grid-footer-wrap tbody:focus,
.k-grid-footer:focus {
	outline: none;
}

.DRTRSgrid {
	tr {
		td:not(:last-child) {
			text-align: center;
		}
	}
}

.k-animation-container .k-calendar-container {
	tbody td .k-link {
		text-align: center;
		padding-left: 0.27em;
		padding-right: 0.27em;
	}
}


/* fixed the 2021 kendo version style issue */
.k-grid thead tr td {
	border-left-color: #c9c9c9;
}

.k-grid tr td {
	border-left-color: @gridRowBorderColor;
}

/* override kendor.common.min.css(Version:2021.2.616) L:5859, */
.k-grid-content-locked,
.k-grid-footer-locked,
.k-grid-header-locked {
	border: none
}

.k-grid-container>:first-child,
.k-grid-header+.k-grid-content {
	td {
		&:first-child {
			border-width: 0 0 0 0 !important;
		}
	}
}

.k-filter-row>td:first-child,
.k-grid tbody td:first-child,
.k-grid tfoot td:first-child,
.k-grid .k-table-td:first-child,
.k-grid-header td.k-header:first-child {
	border-left-width: 1px;
	border-inline-start-width: 1px !important
}

.k-numeric-wrap {
	.k-input {
		padding: 0.177em 0;
		height: 1.65em;
		text-indent: 0.33em;
	}
}

.kendo-grid .k-filter-row td {
	border-color: transparent;
}

.k-button {
	&.k-button-icontext {
		font-size: 0px;
		margin-right: 3px;
	}
}


.k-grid tr:hover {
	background: none;
}

.kendo-grid .k-filtercell .k-i-filter:before {
	content: '';
}

.k-popup ul.k-list li.filter,
.kendo-grid .k-filtercell .k-filter,
.kendo-grid .k-filtercell .k-i-filter {
	background-repeat: no-repeat;
	background-size: 16px;
	cursor: pointer;
	background-position: 2px;
}

.kendo-grid {

	.k-i-sort-asc-sm,
	.k-i-sort-desc-sm,
	.k-svg-i-sort-desc-small,
	.k-svg-i-sort-asc-small {
		&:before {
			content: '';
		}
	}
}

.kendo-grid {

	.k-i-sort-desc-sm,
	.k-svg-i-sort-desc-small {
		background-image: url(../../global/thirdparty/kendo/styles/Black/sprite.png);
		background-position: 0px -32px;
		border-color: transparent;
	}
}

.k-animation-container .k-icon {
	background-image: none;
}

.k-filter-row .k-numerictextbox {
	.k-icon.k-i-arrow-60-up {
		background-image: url('../../global/thirdparty/kendo/styles/default/sprite.png');
		border-color: transparent;
		background-position: 0 -2px;

		&:before {
			content: none;
		}
	}

	.k-icon.k-i-arrow-60-down {
		background-image: url('../../global/thirdparty/kendo/styles/default/sprite.png');
		border-color: transparent;
		background-position: 0 -36px;

		&:before {
			content: none;
		}
	}
}

.k-autocomplete.k-hover,
.k-autocomplete.k-focus,
.k-picker-wrap.k-hover,
.k-picker-wrap.k-focus,
.k-numeric-wrap.k-hover,
.k-numeric-wrap.k-focus,
.k-dropdown-wrap.k-hover,
.k-dropdown-wrap.k-focus {
	border-color: @gridRowBorderColor;
}

.k-picker-wrap.k-state-border-down,
.k-picker-wrap.k-border-down {
	border-bottom-width: 1px;
}

.k-hover>.k-select,
.k-focus>.k-select {
	border-color: @gridRowBorderColor;
}

.k-datepicker>.k-select {
	background-color: #eee;
	border-bottom-width: 1px;
}

.k-dropdown .k-dropdown-wrap {
	background-color: #ececec;

	&.k-hover {
		background-color: #bdb4af;
	}
}

.k-animation-container {
	a.k-link .k-icon {
		background-image: none;
	}

	.k-list .k-item.k-hover {
		background-color: #eaeaea;
	}
}

.k-filter-custom-btn {
	.k-icon {
		&.k-i-filter {
			background-position: -32px -80px;

			&::before {
				content: '';
			}
		}
	}
}

.k-tabstrip .k-tabstrip-items {

	background-image: url(textures/highlight.png);
	background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, .6)), to(rgba(255, 255, 255, .0)));
	background-image: none, -webkit-linear-gradient(top, rgba(255, 255, 255, .6) 0%, rgba(255, 255, 255, .0) 100%);
	background-image: none, linear-gradient(to bottom, rgba(255, 255, 255, .6) 0%, rgba(255, 255, 255, .0) 100%);
	background-position: 50% 50%;
	background-color: #eae8e8;
	border-color: #c9c9c9;

	.k-active {
		border-color: #c9c9c9;
		border-bottom-color: #fff;
		background: none;

		.k-link {
			border-color: transparent;
			color: #333;
		}
	}

	.k-hover {
		background-image: url(textures/highlight.png);
		background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, .4)), to(rgba(255, 255, 255, .0)));
		background-image: none, -webkit-linear-gradient(top, rgba(255, 255, 255, .4) 0%, rgba(255, 255, 255, .0) 100%);
		background-image: none, linear-gradient(to bottom, rgba(255, 255, 255, .4) 0%, rgba(255, 255, 255, .0) 100%);

		.k-link {
			color: #333;
		}
	}
}

.k-filtercell .input-group.tf-filter+div+button {
	margin-right: 22px !important;
}

.customize-setting>.k-tool-icon {
	width: 0;
}

.tabstrip-fieldtripconfigs .general-options-wrapper .k-numeric-wrap {
	height: auto;
}

.grid-stack-item-content {
	.calendar-item {
		.k-calendar {
			.k-header {
				padding-bottom: 0 !important;

				.k-nav-fast {
					margin-top: -4px !important;
				}
			}

			.k-link {
				padding: 0px !important;
			}

			.k-month .k-link {
				height: 14px !important;
			}

			td.k-selected .k-link {
				background: none;
				border: none;
				box-shadow: none;
			}

			.k-calendar-view {
				width: auto;
			}
		}
	}
}

.k-dropdown-wrap {
	>span.k-input {
		&::before {
			content: none;
		}
	}
}

.k-filter-menu {
	.k-filter-menu-container {
		.k-numerictextbox {
			.k-numeric-wrap {
				height: 25px !important;
			}

			.k-hover {
				.k-icon {
					background: none !important;
				}
			}
		}
	}
}

.k-numerictextbox {
	.k-numeric-wrap {
		.k-input {
			background-color: transparent;
		}
	}
}

.k-animation-container .k-calendar-container {
	.k-calendar-view {
		.k-content {
			.k-today {
				>a.k-link {
					background-color: #ccc;
				}
			}

			.k-selected {
				>a.k-link {
					background-color: #007cc0;
				}
			}
		}
	}
}

.routing-tree-view {
	.left-color-picker-container.h50 {
		.k-colorpicker {
			height: 50px;
		}
	}

	.trip-text-info,
	.left-color-picker-container {
		.k-colorpicker {
			float: left;
			width: 15px;
			height: 60px;

			span {
				width: 15px;
				border-width: 0;
				padding: 0;
				height: inherit;

				.k-icon.k-i-arrow-60-down::before {
					content: '';
				}
			}
		}
	}

	.k-icon.k-i-expand,
	.k-icon.k-i-collapse,
	.k-treeview-toggle {
		display: none;
	}
}

.k-edit-cell {
	.k-dropdown {
		.k-dropdown-wrap {
			.k-select {
				width: 20px;
			}
		}
	}
}

.kendo-grid .k-grid-header-wrap .k-filtercell .k-datepicker .k-picker-wrap {
	>input {
		margin-left: 20px;
	}
}

.list-mover-mover .kendo-grid .k-grid-header-wrap .k-filtercell .k-datepicker .k-picker-wrap {
	>input {
		margin-left: 0px;
	}
}

.k-scheduler {
	.k-scheduler-toolbar {
		.k-scheduler-navigation {
			.k-button {

				&.k-focus,
				&:focus {
					box-shadow: none;
					border-color: #bfbfbf;
				}

				&.k-active,
				&.k-selected,
				&:active {
					background-color: #cacaca;
				}
			}
		}

		.k-scheduler-views-wrapper {
			.k-scheduler-views {
				.k-button {

					&.k-active,
					&.k-selected,
					&:active {
						border-color: #D0503C;
						background-color: #D0503C;
					}
				}
			}
		}
	}
}

.k-filter-row {
	.k-dropdown-wrap {
		.k-input::before {
			content: none;
		}
	}
}

.color-picker-container {
	.k-colorpicker {
		.k-picker-wrap {
			width: 22px;
		}
	}
}

.k-grid-header .k-link .k-icon.k-i-sort-asc-sm,
.k-grid-header .k-link .k-icon.k-i-sort-desc-sm,
.k-grid-header .k-link .k-icon.k-svg-i-sort-asc-small,
.k-grid-header .k-link .k-icon.k-svg-i-sort-desc-small {
	margin-left: 0px;

	svg {
		display: none;
	}
}

.grid-title {
	&.with-add-button {
		padding-right: 100px;
	}

	&.with-form {
		padding-right: 118px;
	}

	&.with-form-edit-button {
		padding-right: 218px;
	}

	.header {
		overflow: hidden;
		text-overflow: ellipsis;
		width: 100%;
	}
}

.grid-title.overlap-add-button {
	max-width: initial;
}

.tf-grid-color-field {
	width: 14px;
	height: 14px;
	border: 1px solid rgb(213, 213, 213);
}

.tags {
	.item {
		display: inline-flex;
		align-items: center;
		margin-right: 5px;

		&:first-child {
			margin-right: 0;
		}

		.tf-grid-color-field {
			display: inline-block;
			margin-right: 3px;
		}
	}
}

.form-color {
	width: 20px;
	height: 20px;
	border: 1px solid rgb(213, 213, 213);
}

.list-mover-mover {

	.selectedcolumngrid-container,
	.availablecolumngrid-container {
		.k-grid-norecords {
			width: auto;
			padding-left: 15px;
			padding-top: 15px;
			justify-content: flex-start;
			align-items: flex-start;
		}
	}

	.selectedcolumngrid-container {
		.no-matching-records {
			margin-top: 54px;
		}

		.k-grid-norecords {
			padding-top: 54px;
		}
	}
}

.kendo-grid.kendo-grid-container {
	.form-control.datepickerinput[disabled] {
		color: #858585;
		background-color: white;
		opacity: .6;
	}

	.k-disabled.datepickerbutton {
		background-color: white;
	}

	.k-disabled>.k-input[data-kendo-role="datepicker"] {
		background-color: #EFEFEF;
	}
}

.grid-loadingindicator {
	position: absolute;
	background: transparent;
	height: 100%;
	width: 100%;
	z-index: 3;

	.overlay {
		position: absolute;
		background: rgba(102, 102, 102, 0.8);
		z-index: 4;
	}

	.udfoverlay {
		position: absolute;
		background: rgba(241, 241, 241, 0.8);
		z-index: 4;
	}

	.spinner {
		position: absolute;
		width: 35px;
		height: 35px;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		background-image: url('../img/Spinner-White.gif');
		background-repeat: no-repeat;
		background-position: center;
		background-size: contain;
		z-index: 5;
	}
}

.custom-grid-position {
	position: relative;

	.grid-loadingindicator {
		top: 0px
	}
}

.eta-on-time-status {
	display: flex;
	align-items: center;

	.status-early,
	.status-on-time,
	.status-late {
		display: block;
		width: 15px;
		height: 15px;
		margin-right: 5px;
	}

	.status-early {
		background-color: #5d1d87;
	}

	.status-on-time {
		background-color: #199c2a;
	}

	.status-late {
		background-color: #3e5efb;
	}
}

.sub-work-item {
	width: 100% !important;
}

.k-grid-log-success {
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-top: 5px;
	background: url('../img/grid/success.png') no-repeat center center;
	background-size: 16px 16px;
	cursor: pointer;
}

.k-grid-log-error {
	display: inline-block;
	width: 16px;
	height: 16px;
	margin-top: 5px;
	background: url('../img/grid/error.png') no-repeat center center;
	background-size: 16px 16px;
	cursor: pointer;
}

.text-center {
	text-align: center !important;
}

.k-grid .k-command-cell>.k-button {
	margin-inline-end: var(--kendo-spacing-1, .25rem);
}

.k-grid .k-grid-md td,
.k-grid .k-grid-md .k-table-td,
.k-grid-md td,
.k-grid-md .k-table-td {
	padding-block: var(--kendo-spacing-1, .25rem);
}

.k-grid .k-table-td .tag {
	display: inline-block;
	margin-bottom: -3px;
	margin-right: 3px;
}

.k-grid .k-table-tbody>.k-table-row:not(.k-detail-row):hover {
	background-color: unset;
}

.grid-filter-parameter {
	.title {
		font-size: 15px;
		font-weight: bold;
	}

	input.number-days {
		padding-right: 0;
		width: 120px;
		display: inline-block;
		margin-left: 10px;
	}
}

.kendo-grid.data-list-grid {
	.k-grid-header {
		thead {
			th.k-header:last-child {
				.k-cell-inner {
					.k-link {
						cursor: default;

						.k-column-title {
							cursor: default;
						}
					}
				}
			}
		}
	}
}