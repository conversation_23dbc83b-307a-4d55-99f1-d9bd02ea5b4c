.grid-stack>.grid-stack-item>.grid-stack-item-content {
	.item-content {
		&.grid {
			height: calc(~"100% - 30px") !important;
			padding: 0px;
			margin: 0 8px 0 8px;
			background-color: #f5f5f5;
		}

		.kendo-grid,
		.kendo-grid-container {
			border: 1px solid #eee;
			height: calc(~"100% - 2px");
			position: relative;

			.k-pager-info {
				display: none;
			}

			.count-info {
				float: left;
			}

			.invoice-total-amount {
				position: absolute;
				display: inline-block;
				bottom: 4px;
				right: 10px;
			}
		}

		.kendo-grid-container {

			.count-info,
			.pageInfoSelect {
				display: none !important;
			}

			&.summarybar-showing {

				>.k-pager {
					width: calc(~"100% - 8px");
				}
			}

			.k-grid-header {
				position: relative;
				z-index: 1;
			}
		}

		.kendo-summarygrid-container {
			height: auto;

			.k-grid-content-locked {
				position: relative;
				z-index: -1;
			}
		}
	}

	.item-title {
		.badge-right {
			float: right;
		}
	}
}

.grid-stack>.grid-stack-item-content .item-content .k-pager-info {
	display: none;
}

.data-point.ui-draggable-dragging {
	.grid-stack-item-content {
		.item-content {
			&.grid {
				height: calc(~"100% - 13px") !important;
			}

			.kendo-grid,
			.kendo-grid-container {
				border: 1px solid #eee;
				height: calc(~"100% - 2px");

				.k-pager-info {
					display: none;
				}

				.count-info {
					float: left;
				}
			}

			.kendo-grid-container {

				.count-info,
				.pageInfoSelect {
					display: none !important;
				}

				&.summarybar-showing {

					>.k-pager {
						width: calc(~"100% - 8px");
					}
				}

				.k-grid-header {
					position: relative;
					z-index: 1;
				}
			}

			.kendo-summarygrid-container {
				height: auto;

				.k-grid-content-locked {
					position: relative;
					z-index: -1;
				}
			}
		}

		.item-title {
			.badge-right {
				float: right;
			}
		}

		.grid-top-right-button,
		.copy-address-btn {
			display: none !important;
		}
	}

	.in.grid {
		background-color: #f2f2f2;
	}
}

.grid-stack>.grid-stack-item>.grid-stack-item-content {
	&.no-permission {

		.kendo-grid,
		.kendo-grid-container.k-grid {
			background-color: #ddd;

			>.k-grid-pager {
				display: none;
			}

			.k-grid-content {
				color: #333;

				p {
					font-size: 17px;
					color: #555;
					width: 100%;
					text-align: center;
					line-height: 22px;
					top: 5px;
					position: absolute;
					margin: 0;
				}
			}
		}
	}
}