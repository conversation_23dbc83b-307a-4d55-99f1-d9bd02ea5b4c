.resetPasswordForm,
.password-change-dialog {
	input[type="password"]::-ms-reveal {
		display: none;
	}

	input[type="password"]::-ms-clear {
		display: none;
	}
	
	.progress-bar-control {
		margin-bottom: 5px;
	}

	.password-container {
		position: relative;
	}

	.password-input {
		width: 100%;
		padding-right: 30px;
	}

	.eye-icon {
		position: absolute;
		top: 50%;
		right: 8px;
		transform: translateY(-50%);
		cursor: pointer;
	}

	.icon-valid {
		color: green;
	}

	.icon-invalid {
		color: red;
	}

	ul {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	li {
		padding: 5px 0;
	}

	.progress-container {
		width: 100%;
		height: 10px;
		background: #e0e0e0;
		border-radius: 5px;
	}

	.progress-bar {
		height: 100%;
		width: 0;
		background: red;
		transition: width 0.3s, background 0.3s;
	}

	.strength-text {
		text-align: right;
		font-weight: bold;
		margin-top: 5px;
	}

	.image-icon {
		display: inline-block;
		width: 16px;
		height: 16px;
		background-repeat: no-repeat;
		background-size: contain;
		background-position: center;
		vertical-align: middle;
	}

	.icon-eye {
		background-image: url('../img/Icons/eye.svg');
		filter: grayscale(100%) brightness(50%);
	}

	.icon-eye-slash {
		background-image: url('../img/Icons/eye-slash.svg');
		filter: grayscale(100%) brightness(50%);
	}

	.icon-check {
		background-image: url(../img/Icons/checkmark.svg);
	}

	.icon-times {
		background-image: url(../img/Icons/xmark.svg);
	}

	.help-block {
		margin-bottom: 0px;
	}

	.tf-btn-black.change-pwd-btn {
		margin-right: 10px;
		width: 125px;
		padding: 0px;
		margin-top: 10px;
		height: 25px;
		padding: 0px 7px;
	}

	.strength-meter-container {
		display: flex;
		gap: 4px;
	}

	.strength-meter-span {
		flex-grow: 1;
		height: 8px;
		background: lightgray;
	}

	.strength-label-container {
		display: flex;
		justify-content: right;
	}

	.strength-label-span {
		margin-left: 10px;
	}

	.suggestions-container {
		margin-top: 10px;
		border: 1px solid #ccc;
		border-radius: 5px;
		padding: 10px;
	}

	.suggestions-list {
		padding-left: 15px;
		color: #333;
		font-size: 14px;
		list-style-type: disc;
	}

	.suggestions-list li {
		margin-bottom: 5px;
		padding: 0px;
	}

	.login-error {
		color: red;
		margin-left: 15px;
	}
}