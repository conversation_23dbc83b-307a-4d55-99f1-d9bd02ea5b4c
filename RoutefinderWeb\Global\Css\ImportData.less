@import "z-index";

.underline-template {
	&::first-letter {
		text-decoration: underline;
	}
}

.import-definition-list {
	.form-control {
		min-height: 130px;

		.item {
			margin: 0 -8px;
			padding: 0 8px;

			&.select {
				background-color: #FFFFCE !important;
			}
		}
	}
}

.student-import-options {
	.file-group {
		.file-name {
			float: left;
			width: calc(~"100% - 32px");
		}

		.view {
			float: right;
			width: 22px;
			height: 22px;
			margin-left: 10px;
		}
	}

	.checkbox-group {
		height: 20px;
		line-height: 20px;

		input {
			margin-top: 4px !important;
			outline: none;
		}

		.margin-right-five {
			margin-right: 5px;
		}

		input[type=checkbox] {
			position: initial;
		}
	}

	.residence-list {
		.form-control {
			min-height: 130px;
		}

		.input-group-btn {
			vertical-align: top;
		}
	}

	.disabled {
		opacity: 0.5;
	}

	p {
		margin: 0;

		&.underline {
			.underline-template;
		}
	}
}

.advanced-import-options {
	.btn {
		margin-top: 10px;
	}

	p {
		margin: 0;

		&.underline {
			.underline-template;
		}
	}
}

.import-field-option {
	.checkbox-group {
		height: 20px;
		line-height: 20px;
		margin-left: 15px;
		margin-right: -15px;

		input {
			margin-top: 4px !important;
			outline: none;
		}

		.margin-right-five {
			margin-right: 5px;
		}

		input[type=checkbox] {
			position: absolute;
		}
	}

	p {
		margin: 0;

		&.underline {
			.underline-template;
		}
	}
}

.import-merge-data {
	.kendo-grid .form-control {
		height: 22px;
	}

	.import-parcel-desc
	{
		padding-left: 15px;
		font-size: 14px;
	}
}

.import-data-modal {
	.k-grid-header-locked .k-header {
		background-color: #eae8e8;
	}

	.form-group {
		.k-dropdownlist {
			.k-input-inner {
				height: 20px;
			}
		}
	}

	.import-merge-data {
		.step-one {
			.section-header {
				font-size: 16px;
			}

			.radio {
				line-height: 20px;

				&.disabled {
					pointer-events: none;
					opacity: 0.5;
				}
			}
		}
	}

	.modal-footer {

		.positive,
		.other {
			height: 26px;
			width: 108px;
			background-color: #333333;
			border-color: #444444;
			color: #ffffff;
			padding: 3px 12px;
		}
	}

	.kendo-grid .form-control {
		height: 22px;
	}
}

.update-information {
	.record {
		margin-top: 20px;
	}

	p {
		margin: 0;

		&.underline {
			.underline-template;
		}
	}

	.buttons {
		margin-top: 18px;
	}

	.key {
		color: red;
	}

	.kendo-grid .k-grid-content table {
		tr.diff-row {
			background-color: #7f7fff;
			color: #FFFFCE;
			border-bottom: 1px solid #fff;

			&.k-alt>td {
				background-color: #7f7fff;
				color: #FFFFCE;
			}
		}

		.ignore-row {
			background-color: #c5c5c5;

			&.k-alt>td {
				background-color: #c5c5c5;
			}
		}
	}

	.blue-legend {
		&:extend(.update-information .legend);
		background: #7f7fff;
	}

	.gray-legend {
		&:extend(.update-information .legend);
		background: #c5c5c5;
	}

	.legend {
		display: inline-block;
		width: 10px;
		height: 10px;
		margin: 15px 5px 0px 0px;
	}
}

.update-information-print {

	.header,
	.buttons {
		display: none !important;
	}

	.kendo-grid {
		width: 60% !important;
		padding: 0 20% !important;
		height: auto !important;

		.k-grid-content {
			height: auto !important;
		}
	}
}

.import-status {
	height: 100%;

	.log-area {
		height: calc(~'100% - 117px');
		margin-top: 10px;

		.form-group {
			margin-bottom: 2px;
		}

		.textarea {
			height: 100%;
		}

		textarea {
			width: 100%;
			height: 100%;
			min-height: 200px;
			outline: none;
			white-space: pre;
		}
	}
}

.import-status-print {
	display: none !important;
	font-size: 16px;

	.file-group {
		input {
			font-size: 16px;
			height: 28px;
		}
	}

	&.show {
		display: block !important;
	}
}

.student-picture-import {
	.section-header {
		font-size: 16px;
	}

	.content-area {
		padding: 2px 32px;

		label {
			cursor: pointer;
			font-weight: normal;
		}

		input {
			left: -20px !important;
			cursor: pointer;
		}

		.check-item {
			position: relative;
			margin: 5px 0px;
		}

		.radio-item {
			display: inline-block;
			position: relative;
			margin-right: 50px; // &:not(:first-child) {
			// 	margin: 0 50px;
			// }
		}

		.upload-item {
			margin: 10px -16px;

			>input[type=file]#fileInput {
				display: none;
			}

			>label[for=fileInput] {
				background-color: #ccc;
				border: 1px solid #666;
				line-height: 24px;

				>span {
					padding-left: 15px;
					padding-right: 15px;
					color: #333;
					font-weight: 500;
				}

				>input[type=text]#input_result {
					width: 300px;
					border-top: none;
					border-bottom: none;
					border-left: none;
					border-right: 1px solid #666;
					background-color: #fff;
				}
			}
		}
	}
}

.step-header-container {
	border-bottom: 1px solid gray;
	padding-bottom: 10px;
	margin: 0 5px 20px 5px;
}

@media print {

	.import-status-print,
	.update-information-print {
		display: block !important;
	}
}

.import-or-merge-step-container {
	border-top-style: solid;
	border-width: 1px;
}

.import-or-merge-step-container,
.import-or-merge-from-excel-step-one {
	min-height: 460px;
	float: left;
	width: 100%;
}

.select-processing-options label {
	font-weight: normal;
}

.select-processing-options,
.import-from-excel-pre-verification {
	margin-left: 1px;
	margin-right: -31px;
}

.import-from-excel-select-data-type {
	width: 103%;
}

.import-or-merge-step-container,
.select-processing-options,
.import-from-excel-pre-verification,
.import-from-excel-select-data-type {
	.destination-text-ellipsis {
		max-width: 120px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.box-line-container {
		border-width: 1px;
		border-style: solid;

		.full-input-drop-down {
			padding: 0px 10px 15px 10px;
		}
	}

	.botton-container {
		margin-top: 10px;

		.btn-default:focus {
			background-color: #fff;
		}
	}

	.section-header {
		position: relative;
		top: -8px;
		left: 9px;
		background-color: white;
	}

	.import-or-merge-step-one {
		width: 500px;
		margin: 175px 100px;

		.option-box {
			margin-top: -12px;
			margin-left: 10px;
		}
	}

	.import-or-merge-step-two {
		.data-type-container {
			margin-top: 12px;
		}
	}

	.page-type-drop-down-list {
		width: 35%;
		margin-top: 6px;
	}

	.data-Type-show-header {
		width: 35%;
		margin-top: -10px;
	}

	.map-column-grid-container,
	.option-container {
		margin-top: 10px;
		float: left;
		height: 272px;
	}

	.map-column-grid-container {
		width: 73%;
		margin-right: 8px;

		.Map-Columns-container {
			margin: 0 4px;
		}
	}

	.option-container {
		width: 26%;

		.options-box {
			margin: 0 10px 10px 10px;

			input[type=checkbox] {
				margin-left: 0 !important;
			}
		}
	}

	.residence-selector {
		height: 23px;

		.list {
			display: inline-block;
			width: 185px;
			overflow-y: auto;
			border: 1px solid #ccc;
			height: 22px;
		}

		.button {
			display: inline-block;
		}
	}

	.data-preview-grid-container {
		width: 100%;
		margin-top: 7px;
		float: left;
		height: 175px;

		.Data-Preview-container {
			margin: -3px 4px;
		}
	}

	.import-or-merge-step-four {
		.header-options-box {
			display: flex;
			margin-top: 6px;
			justify-content: space-between;

			.input-group-btn {
				vertical-align: top;
			}

			.items-container {
				display: flex;

				>label {
					margin: 4px 2px 0 0;
				}
			}
		}

		.all-records-container {
			margin-top: 12px;
		}

		.footer-info {
			float: right;
		}
	}

	.import-or-merge-step-five {
		.warning-box {
			border-color: red;
			margin-top: 10px;
			float: left;
			height: 60px;

			.section-header {
				color: red;
			}

			.message-text {
				color: red;
				padding: 0 10px;
			}
		}

		.box-header {
			font: bold;
			float: left;
			margin-top: 5px;
		}

		.import-tables-info {
			margin-top: 10px;
			margin-right: 8px;
			height: 120px;
			width: 100%;
		}

		.import-result-info {
			margin-top: 10px;
			height: 380px;
			background: #b6b4b4;
			width: 100%;
		}
	}
}

.select-processing-options {

	.map-column-grid-container,
	.option-container {
		height: 294px;
	}
}

.external-source-auto-import-template {

	margin-left: 0 !important;
	margin-right: 0 !important;

	.box-line-container {
		width: 100%;
		padding-left: 10px;
		padding-right: 10px;
	}

	.map-column-grid-container {
		height: 250px;
	}

	.data-preview-container-external-source-template {
		width: 100%;
	}

	.template-name .required-form-group {
		margin-bottom: 10px !important;
		height: 45px !important;

		.template-name-input {
			width: 100%;
		}

		.input-group {
			width: 60%;
		}
	}
}

#operArea {
	display: flex;

	button {
		margin: 5px;
	}
}

.import-merge-data,
.student-import-options,
.select-processing-options,
.student-automation,
.external-source-automation {
	.safety-check-container {
		width: 100%;
		margin-top: 7px;
		float: left;
		height: 60px;

		.single-line {
			display: flex;
			flex-direction: row;

			.checkbox-group {
				flex: 1;
			}
		}

		input[type=checkbox] {
			margin-left: 5px;
		}
	}

	// customize kendo numeric input
	span.k-numerictextbox {
		vertical-align: top;
		display: inline-block;

		span.k-numeric-wrap {
			height: auto !important;
			padding-right: 0;

			input.percentage-input.k-input {
				color: #555;
			}
		}

		&.no-warning {
			&.k-state-invalid {
				border-color: #d5d5d5;

				.k-select {
					border-color: #d5d5d5;
				}
			}

			.k-icon.k-i-warning {
				display: none !important;
			}
		}

		&.no-arrows {

			.k-input-spinner,
			.k-select {
				display: none;
			}

		}
	}
}

.student-automation,
.external-source-automation {
	.form-group {
		.k-input.k-numerictextbox {
			display: inline-block;
		}
	}
}

.external-source-automation-file {
	.crate-new-button {
		line-height: 24px;
		color: #333333;
		border: 1px solid #999;
		text-align: center;
		cursor: pointer;
		border-radius: 5px;
		font-size: 14px;
		margin-left: -12px;
		margin-top: -2px;
	}

	.disabled-new {
		color: #333333;
		opacity: 0.5;
		cursor: not-allowed;
	}
}

.import-merge-data .import-or-merge-step-container
{
	border: none;
	min-height: 0;

	.kendo-grid .k-grid-content
	{
		.k-grid-content-expander
		{
			width: auto !important;
		}
	}

	.import-or-merge-step-one
	{
		margin: 30px 20px 20px 20px;
		border-color: #cacaca;
	}
}