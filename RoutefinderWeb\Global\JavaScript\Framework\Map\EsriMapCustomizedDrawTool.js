﻿(function()
{
	createNamespace("TF.Map").EsriMapCustomizedDrawTool = EsriMapCustomizedDrawTool;

	function EsriMapCustomizedDrawTool(map, arcgisTools)
	{
		var self = this;
		self._map = map;
		self._arcgis = arcgisTools;
		self._type = null;
		self._points = [];
		self._drawedPart = null;
		self._drawingPart = null;
		self._drawLayer = null;
		self._onDrawing = false;
		self._markerSymbol = null;
		self._lineSymbol = null;
		self._fillSymbol = null;
		self._defaultSpatialReference = null;
		self._lastClickInfo = { time: 0, clientX: 0, clientY: 0 };
		self._mouseDownInfo = null;

		self.onDrawStart = new TF.Events.Event();
		self.onDrawEnd = new TF.Events.Event();

		self._init();
	};

	EsriMapCustomizedDrawTool.prototype.constructor = TF.Map.EsriMapCustomizedDrawTool;


	/**
	* Initialize the draw tool.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._init = function()
	{
		this._initHelper();
		this._initSymbols();
		this._initLayers();
	};


	/**
	* Initialize helpers.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._initHelper = function()
	{
		this._defaultSpatialReference = this._map.spatialReference;
	};

	/**
	* Initialize symbol styles.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._initSymbols = function()
	{
		this._markerSymbol = new this._arcgis.SimpleMarkerSymbol();
		this._lineSymbol = new this._arcgis.SimpleLineSymbol();
		this._fillSymbol = new this._arcgis.SimpleFillSymbol();
	};

	/**
	* Initialize layers.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._initLayers = function()
	{
		this._drawLayer = new this._arcgis.GraphicsLayer("drawToolLayer");

		this._map.addLayer(this._drawLayer);
	};

	/**
	* Set map events for drawing.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._setMapEvents = function()
	{
		this._mouseDownEvent = this._map.on("mouse-down", this._mouseDownHandler.bind(this));
		this._mouseUpEvent = this._map.on("mouse-up", this._mouseUpHandler.bind(this));
		this._mouseMoveEvent = this._map.on("mouse-move", this._mouseMoveHandler.bind(this));
		this._keyDownEvent = this._map.on("key-down", this._keyDownHandler.bind(this));

		this._map.disableDoubleClickZoom();
	};

	/**
	* Clear map events.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._clearMapEvents = function()
	{
		var self = this;
		if (self._mouseDownEvent)
		{
			self._mouseDownEvent.remove();
		}
		if (self._mouseUpEvent)
		{
			self._mouseUpEvent.remove();
		}
		if (self._mouseMoveEvent.remove())
		{
			self._mouseMoveEvent.remove();
		}
		self._map.enableDoubleClickZoom();
	};

	/**
	* Activate the draw tool.
	*
	* @param {string} geometryType - the string to specify the geometry to be drawn, currently supports only polygon.
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype.activate = function(geometryType)
	{
		this._onDrawing = true;
		this._type = geometryType;
		this._points = [];

		switch (geometryType)
		{
			case "point":
				// Need implementation
				break;
			case "polyline":
				// Need implementation
				break;
			case "polygon":
				this._drawingPart = new this._arcgis.Graphic({ geometry: new this._arcgis.Polyline(this._map.spatialReference), symbol: this._lineSymbol });
				this._drawedPart = new this._arcgis.Graphic({ geometry: new this._arcgis.Polyline(this._map.spatialReference), symbol: this._lineSymbol });

				this._drawLayer.add(this._drawingPart);
				this._drawLayer.add(this._drawedPart);
				break;
			default:
				return;
		}

		this._setMapEvents();
	};

	/**
	* Deactivate the draw tool.
	*
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype.deactivate = function()
	{
		this._onDrawing = false;
		this._drawLayer.clear();
		this._clearMapEvents();
	};

	/**
	* The handler for map mouse-down event.
	*
	* @param {event} evt- the event parameter
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._mouseDownHandler = function(evt)
	{
		this._mouseDownInfo = evt;
	};

	/**
	* The handler for map mouse-up event.
	*
	* @param {event} evt- the event parameter
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._mouseUpHandler = function(evt)
	{
		// When the mouse moves too much between down and up, the click will not add a new node.
		if (evt.button === 0 && this._withinMovementBuffer(evt, this._mouseDownInfo, 20))
		{
			var curTime = new Date().getTime();

			// To check if this is a valid double click.
			if (curTime - this._lastClickInfo.time > 800 || !this._withinMovementBuffer(evt, this._lastClickInfo, 6))
			{
				this._clickHandler(this._mouseDownInfo);
			}
			else
			{
				this._dblClickHandler(evt);
			}

			this._lastClickInfo.time = curTime;
			this._lastClickInfo.clientX = evt.clientX;
			this._lastClickInfo.clientY = evt.clientY;
		}
	};

	/**
	* The handler for map mouse-move event.
	*
	* @param {event} evt- the event parameter
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._mouseMoveHandler = function(evt)
	{
		if (!this._onDrawing || this._points.length === 0) { return; }

		var point, pointSet = [],
			ptCount = this._points.length;

		if (ptCount > 0)
		{
			pointSet.push(this._points[0]);
			pointSet.push(this._map.toMap(evt.screenPoint));

			if (ptCount > 1)
			{
				pointSet.push(this._points[ptCount - 1]);
			}

			this._drawingPart.setGeometry(this._pointsToPolyline(pointSet));
		}
	};

	/**
	* The handler for map key-down event. Press "esc" to dismiss drawing.
	*
	* @param {event} evt- the event parameter
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._keyDownHandler = function(evt)
	{
		switch (evt.keyCode)
		{
			case $.ui.keyCode.ESCAPE:
				this.onDrawEnd.notify();
				this._resetDrawing();
				break;
			default:
				break;
		}
	};

	/**
	* The handler for a single click.
	*
	* @param {event} evt- the event parameter
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._clickHandler = function(evt)
	{
		if (this._onDrawing)
		{
			this._points.push(evt.mapPoint);

			if (this._points.length === 1)
			{
				this.onDrawStart.notify();
				return;
			}

			this._drawedPart.setGeometry(this._pointsToPolyline(this._points));
		}
	};

	/**
	* The handler for a double click.
	*
	* @param {event} evt- the event parameter
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype._dblClickHandler = function(evt)
	{
		if (this._onDrawing && this._points.length > 2)
		{
			var args = {
				"geometry": this._pointsToPolygon(this._points)
			};

			this.onDrawEnd.notify(args);
			this._resetDrawing();
		}
	};

	/**
	* The method to set symbol for drawing markers.
	*
	* @param {SimpleMarkerSymbol(arcgis)} symbol- specified SimpleMarkerSymbol
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype.setMarkerSymbol = function(symbol)
	{
		this._markerSymbol = symbol;
	};

	/**
	* The method to set symbol for drawing lines.
	*
	* @param {SimpleLineSymbol(arcgis)} symbol- specified SimpleLineSymbol
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype.setLineSymbol = function(symbol)
	{
		this._lineSymbol = symbol;
	};

	/**
	* The method to set symbol for drawing polygon.
	*
	* @param {SimpleFillSymbol(arcgis)} symbol- specified SimpleFillSymbol
	* @return {None} 
	*/
	EsriMapCustomizedDrawTool.prototype.setFillSymbol = function(symbol)
	{
		this._fillSymbol = symbol;
	};

	/**
	* Generate a Polyline with a set of points.
	*
	* @param {array<Point(arcgis)>} points - an array of Points
	* @return {Polyline(arcgis)} The generated Polyline
	*/
	EsriMapCustomizedDrawTool.prototype._pointsToPolyline = function(points)
	{
		return new this._arcgis.Polyline({
			"paths": [points.map(function(pt) { return [pt.x, pt.y]; })],
			"spatialReference": this._map.spatialReference
		});
	};

	/**
	* Generate a Polygon with a set of points.
	*
	* @param {array<Point(arcgis)>} points - an array of Points
	* @return {Polygon(arcgis)} The generated Polygon
	*/
	EsriMapCustomizedDrawTool.prototype._pointsToPolygon = function(points)
	{
		var startNode = points[0],
			endNode = points[points.length - 1];

		if (!this._compareTwoPoints(startNode, endNode))
		{
			points.push(startNode);
		}

		return new this._arcgis.Polygon({
			"rings": [points.map(function(pt) { return [pt.x, pt.y]; })],
			"spatialReference": this._map.spatialReference
		});
	};

	/**
	* Compare if two points are at same position.
	*
	* @param {Point(arcgis)} pt1 - point no.1
	* @param {Point(arcgis)} pt2 - point no.2
	* @return {boolean} whether the two points are at same position. 
	*/
	EsriMapCustomizedDrawTool.prototype._compareTwoPoints = function(pt1, pt2)
	{
		return (pt1.x === pt2.x && pt1.y === pt2.y);
	};

	/**
	* Reset drawing.
	*
	* @return {None}
	*/
	EsriMapCustomizedDrawTool.prototype._resetDrawing = function()
	{
		this._points = [];

		this._drawedPart.geometry.removePath(0);
		this._drawingPart.geometry.removePath(0);

		this._drawLayer.redraw();
	};

	/**
	* Check if the distance between two screen points is within a given buffer.
	*
	* @param {Point(arcgis)} pt1 - point no.1
	* @param {Point(arcgis)} pt2 - point no.2
	* @param {Point(arcgis)} buffer - the buffering distance
	* @return {boolean} whether the distance between is smaller than the buffer.
	*/
	EsriMapCustomizedDrawTool.prototype._withinMovementBuffer = function(pt1, pt2, buffer)
	{
		return Math.abs(pt1.clientX - pt2.clientX) < buffer && Math.abs(pt1.clientY - pt2.clientY) < buffer;
	};

	/**
	* Get the count of points in current drawing.
	*
	* @return {int} the count of points on drawing.
	*/
	EsriMapCustomizedDrawTool.prototype.getPointCount = function()
	{
		return this._points.length;
	};

	/**
	* Dispose.
	*
	* @return {None}
	*/
	EsriMapCustomizedDrawTool.prototype.dispose = function()
	{
		this._map = null;
		this._arcgis = null;
		this._type = null;
		this._points = null;
		this._drawedPart = null;
		this._drawingPart = null;
		this._drawLayer = null;
		this._onDrawing = null;
		this._markerSymbol = null;
		this._lineSymbol = null;
		this._fillSymbol = null;
		this._defaultSpatialReference = null;
		this._lastClickInfo = null;
		this._mouseDownInfo = null;
	};
})();