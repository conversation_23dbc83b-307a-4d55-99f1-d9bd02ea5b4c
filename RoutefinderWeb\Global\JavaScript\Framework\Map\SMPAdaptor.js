(function()
{
	createNamespace("TF.Map").SMPAdaptor = SMPAdaptor;
    let initialized = false;

    const editingMapServer = "MapEditingOneService/MapServer";
    const editingFeatureServer = "MapEditingOneService/FeatureServer";
    const routingMapServer = "RouteService/MapServer";
    let serverLayerFieldsMap = {};
    const SMPTravelModes = {
        DrivingTime: 1,
        DrivingDistance: 2,
        TruckingTime: 3,
        TruckingDistance: 4,
        WalkingTime: 5,
        WalkingDistance: 6,
        RuralDrivingTime: 7,
        RuralDrivingDistance: 8
    };
    const SMPLayerNames = {
        sde:
        {
            MAP_LMPOINT: "MAP_LMPOINT",
            MAP_LMPOLYGON: "MAP_LMPOLYGON",
            MAP_LMPOLYLINE: "MAP_LMPOLYLINE",
            MAP_MC: "MAP_MC",
            MAP_PC: "MAP_PC",
            MAP_WTPOLYGON: "MAP_WTPOLYGON",
            MAP_WTPOLYLINE: "MAP_WTPOLYLINE",
            MAP_R: "MAP_R",
            MAP_STREET: "MAP_STREET",
            SIGNPOST_FEATURE: "SIGNPOST_FEATURE",
            SIGNPOST_TABLE: "SIGNPOST_TABLE"
        },
        file:
        {
            Map_Turn: "RestrictedTurns",
            MAP_STREET: "Routing_Streets",
            routing_ND_Junctions: "Routing_ND_Junctions",
            StreetPolygon: "StreetPolygon",
            STREETINTERSECTR: "STREETINTERSECTR"
        }
    }

	function SMPAdaptor()
	{
	}

    SMPAdaptor.init = function()
    {
        if (initialized) return;
        initialized = true;
        updateArcgisLayersEnum();
        initServerLayerFieldsMap();
        interceptSMP(tf.map.ArcGIS.route, "solve", beforeRouteSolve, afterRouteSolve);
        interceptSMP(tf.map.ArcGIS.serviceArea, "solve", beforeServiceAreaSolve, afterServiceAreaSolve);
        interceptSMP(tf.map.ArcGIS.FeatureLayer.prototype, "queryFeatures", null, afterFeatureLayerQueryFeatures);
        interceptSMP(tf.map.ArcGIS.query, "executeQueryJSON", null, afterQueryExecuteQueryJSON);
        interceptSMP(tf.map.ArcGIS.query, "executeQueryPBF", null, afterQueryExecuteQueryPBF);
        interceptSMP(tf.map.ArcGIS, "esriRequest", beforeEsriRequest, afterEsriRequest);
		interceptVRPTool();
    }

    SMPAdaptor.resetArcgisLayersEnum = function (layerDefinitions, arcgisLayers, typeName)
    {
        if (!Array.isArray(layerDefinitions))
        {
            return;
        }

        const layerNameMap = SMPLayerNames[typeName];
        const arcgisLayer = arcgisLayers[typeName];

        Object.keys(layerNameMap).forEach(mapName => {
            const layerName = layerNameMap[mapName];
            const layerDefinition = layerDefinitions.find(x => x.name == layerName);

            if (layerDefinition)
            {
                arcgisLayer[mapName] = layerDefinition.id;
            }
            else
            {
                arcgisLayer[mapName] = -1;
            }
        });
    }

    function interceptSMP(object, methodName, fnBefore, fnAfter)
	{
		var method = object[methodName];
        object[methodName] = function()
        {
            let context = {};
            if (fnBefore)
            {
                fnBefore.call(this, context, arguments);
            }

            var ret = method.apply(this, arguments);
            if (fnAfter)
            {
                ret = fnAfter.call(this, ret, context, arguments);
            }
            return ret;
        }
	};

    // #region general
    // The init values are hardcoded, but they will be updated in tf.arcgisHelper.loadNationalMapSettings method to ensure the correct values are used.
    function updateArcgisLayersEnum()
    {
        TF.arcgisLayers = $.extend(true, TF.arcgisLayers, {
            sde:
            {
                MAP_LMPOINT: 4,
                MAP_LMPOLYGON: 5,
                MAP_LMPOLYLINE: 6,
                MAP_MC: 7,
                MAP_PC: 9,
                MAP_WTPOLYGON: 15,
                MAP_WTPOLYLINE: 16,
                MAP_R: 18,
                MAP_STREET: 19,
                SIGNPOST_FEATURE: 22,
                SIGNPOST_TABLE: 24
            },
            file:
            {
                Map_Turn: 3,
                MAP_STREET: 5,
                routing_ND_Junctions: 4,
                StreetPolygon: -1,
                STREETINTERSECTR: -1
            }
        });
    }
    // #endregion

    // #region tf.map.ArcGIS.esriRequest
    function beforeEsriRequest(context, [url, parameters])
    {
        if (url && url.endsWith("solveODCostMatrix"))
        {
            beforeSolveODCostMatrix(context, parameters);
        }
    }

    function afterEsriRequest(retValue, context, [url, parameters])
    {
        if (url && url.endsWith("solveODCostMatrix"))
        {
            return afterSolveODCostMatrix(retValue, context, parameters);
        }

        return retValue;
    }

    function beforeSolveODCostMatrix(context, params)
    {
        if (params.body)
        {
            let parameters = {};

            for(let [key, value] of params.body.entries())
            {
                switch(key)
                {
                    case "travelMode":
                        parameters[key] = JSON.parse(value);
                        break;
                    case "polygonBarriers":
                        parameters[key] = tf.map.ArcGIS.FeatureSet.fromJSON(JSON.parse(value));
                        break;
                    default:
                        parameters[key] = value;
                        break;
                }
            }

            convertRouteParameter(context, parameters, true);

            let travelMode = parameters["travelMode"];
            if (travelMode)
            {
                context.impedanceAttributeName = travelMode.impedanceAttributeName;
                context.isDistance = travelMode.impedanceAttributeName == travelMode.distanceAttributeName;
            }
            else
            {
                context.impedanceAttributeName = parameters.impedanceAttributeName || "TravelTime";
                context.isDistance = false;
            }

            let bodyParameters = new FormData();
            Object.keys(parameters).forEach(key =>
            {
                let parameter = parameters[key];
                if (key === "polygonBarriers")
                {
                    parameter = parameter.toJSON();
                }

                if (typeof parameter == "string")
                {
                    bodyParameters.append(key, parameter);
                }
                else
                {
                    bodyParameters.append(key, JSON.stringify(parameter));
                }
            });

            params.body = bodyParameters;
        }
    }

    function afterSolveODCostMatrix(retValue, context, params)
    {
        return retValue.then((result) =>
        {
            if (result && result.data && result.data.odLines)
            {
                let costAttributeName = "Total_" + context.impedanceAttributeName;
                let classicCostAttributeName = context.isDistance ? "Total_Length" : "Total_Time";

                (result.data.odLines.features || []).forEach(feature =>
                {
                    let cost = feature.attributes[costAttributeName];
                    if (context.isDistance)
                    {
                        cost = cost * 1000;
                    }
                    feature.attributes[classicCostAttributeName] = cost;
                    delete feature.attributes[costAttributeName];
                });

                result.data.odLines.fieldAliases[classicCostAttributeName] = classicCostAttributeName;
                delete result.data.odLines.fieldAliases[costAttributeName];

                let field = result.data.odLines.fields.find(x => x.name == costAttributeName);
                if (field)
                {
                    field.name = classicCostAttributeName;
                    field.alias = classicCostAttributeName;
                }
            }

            return result;
        });
    }

    // #endregion tf.map.ArcGIS.esriRequest

    // #region tf.map.ArcGIS.route.solve
    function beforeRouteSolve(context, [url, routeParameters]) {
        convertRouteParameter(context, routeParameters);
    }

    function afterRouteSolve(retValue, context, [url, routeParameters]) {
        return retValue.then((result) =>
        {
            (result.routeResults || []).forEach(routeResult => {
                (routeResult.directions?.features || []).forEach(feature => {
                    if (feature.attributes.DriveDistance !== undefined)
                    {
                        feature.attributes.length = feature.attributes.DriveDistance;
                    }

                    if (feature.attributes.ElapsedTime !== undefined)
                    {
                        feature.attributes.time = feature.attributes.ElapsedTime;
                    }
                });
            });

            return result;
        }).finally(() => {
            if (context.polygonBarriers)
            {
                routeParameters.polygonBarriers = context.polygonBarriers;
            }
        });
    }

    function convertRouteParameter(context, params, isOriginalFormat = false)
    {
        convertTravelMode(params);
        convertPolygonBarriers(context, params);
        convertGeneralRouteParameters(params, isOriginalFormat);
    };

    function convertGeneralRouteParameters(params, isOriginalFormat)
    {
        convertGeneralParameters(params, isOriginalFormat);

        if (params.returnDirections)
        {
            let directionsTimeAttributeKey = isOriginalFormat ? "directionsTimeAttributeName" : "directionsTimeAttribute";
            params[directionsTimeAttributeKey] = params.travelMode?.timeAttributeName || "TravelTime";
        }
    }

    function convertGeneralParameters(params, isOriginalFormat)
    {
        let restrictionAttributesKey = isOriginalFormat ? "restrictionAttributeNames" :  "restrictionAttributes";
        let impedanceAttributeKey = isOriginalFormat ? "impedanceAttributeName" : "impedanceAttribute";

        if (!params.travelMode)
        {
            params[impedanceAttributeKey] = convertImpedanceAttributeName(params.impedanceAttribute);
            params[restrictionAttributesKey] = undefined;

            return;
        }

        params[restrictionAttributesKey] = params.travelMode.restrictionAttributeNames;
        params[impedanceAttributeKey] = params.travelMode.impedanceAttributeName;
    }

    function convertTravelMode(params)
    {
        let travelMode = params.travelMode;
        if (!travelMode) return;

        let originalTravelMode = tf.arcgisHelper.getTravelModeFromCache(travelMode.name);
        if (!originalTravelMode) return;

        travelMode.impedanceAttributeName = originalTravelMode.impedanceAttributeName;
        travelMode.restrictionAttributeNames = originalTravelMode.restrictionAttributeNames;
    }

    function convertPolygonBarriers(context, params, isJob = false)
    {
        let travelMode = params.travelMode,
            polygonBarriers = params.polygonBarriers;
        if (!polygonBarriers) return;

        context.polygonBarriers = polygonBarriers;
        const clonedBarriers = polygonBarriers.clone();
        params.polygonBarriers = clonedBarriers;

        let timeAttrName = isJob ? "ScaledTimeFactor" : "Attr_" + ( travelMode?.timeAttributeName || "TravelTime" ),
            distanceAttrName = isJob ? "ScaledDistanceFactor" : "Attr_" + ( travelMode?.distanceAttributeName || "Kilometers" );

        clonedBarriers.features.forEach((barrier) =>
        {
            let attributes = barrier.attributes;
            if (attributes.Attr_Time !== undefined || attributes.Attr_walktime !== undefined)
            {
                let timeCost = attributes.Attr_Time || attributes.Attr_walktime;
                attributes[timeAttrName] = timeCost;
                delete attributes.Attr_Time;
                delete attributes.Attr_walktime;
            }

            if (attributes.Attr_Length !== undefined)
            {
                attributes[distanceAttrName] = attributes.Attr_Length;
                delete attributes.Attr_Length;
            }

            if (attributes.isChangeTime !== undefined)
            {
                delete attributes.isChangeTime;
            }
        });
    }

    function convertImpedanceAttributeName(impedanceAttributeName, travelMode)
    {
        switch(impedanceAttributeName)
        {
            case "Time":
                return travelMode?.timeAttributeName || "TravelTime";
            case "Length":
                return travelMode?.distanceAttributeName || "Kilometers";
            default:
                return impedanceAttributeName;
        }
    }
    // #endregion tf.map.ArcGIS.route.solve

    // #region tf.map.ArcGIS.serviceArea.solve
    function beforeServiceAreaSolve(context, [url, serviceAreaParameters]) {
        convertServiceAreaParameter(context, serviceAreaParameters);
    }

    function afterServiceAreaSolve(retValue, context, [url, serviceAreaParameters]) {
        return retValue.finally(() => {
            if (context.polygonBarriers)
            {
                serviceAreaParameters.polygonBarriers = context.polygonBarriers;
            }
        });
    }

    function convertServiceAreaParameter(context, params)
    {
        convertTravelMode(params);
        convertPolygonBarriers(context, params);
        convertGeneralServiceAreaParameters(params);
        convertServiceAreaParametersFacilities(params);
    };

    function convertGeneralServiceAreaParameters(params)
    {
        convertGeneralParameters(params);

        let defaultBreaks = params.defaultBreaks;
        if (defaultBreaks)
        {
            let breaksArray = _.isString(defaultBreaks) ? defaultBreaks.split(",") : [defaultBreaks];
            breaksArray = breaksArray.map(breakDistance => {
                breakMeters = +breakDistance;
                if (!isNaN(breakMeters))
                {
                    let breakKilometers = breakMeters / 1000;
                    if (params.impedanceAttribute === "Kilometers")
                    {
                        breakDistance = breakKilometers;
                    }
                    else if (params.impedanceAttribute === "Miles")
                    {
                        breakDistance = convertSpeed(breakKilometers);
                    }
                }
                return breakDistance;

            });
            params.defaultBreaks = breaksArray.join(",");
        }
    }

    function convertServiceAreaParametersFacilities(params)
    {
        if (!params.facilities?.features) return;

        params.facilities.features.forEach(feature => {
            if (feature.attributes?.Breaks_Time)
            {
                let breaksAttrName = "Breaks_" + params.impedanceAttribute;
                feature.attributes[breaksAttrName] = feature.attributes.Breaks_Time;
            }
        });
    }
    // #endregion tf.map.ArcGIS.serviceArea.solve

    // #region tf.map.ArcGIS.FeatureLayer.prototype.queryFeatures
    function convertSpeed(speed)
    {
        return tf.measurementUnitConverter.convert({
            value: speed,
            originalUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
            targetUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Imperial,
            precision: 5
        });
    }

    function initServerLayerFieldsMap()
    {
        serverLayerFieldsMap = {
            [routingMapServer] : {
                [TF.arcgisLayers.file.MAP_STREET]: {
                    "Street": "FULL_STREET_NAME",
                    "State": "ADMIN1_ABBR",
                }
            },
            [editingMapServer] : {

                // Street
                [TF.arcgisLayers.sde.MAP_STREET] : {
                    "Street": (attributes) => attributes["FULL_STREET_NAME"] || "Unnamed",
                    "FromElevation": "F_ZLEV",
                    "ToElevation": "T_ZLEV",
                    "RoadClass": "ROAD_CLASS",
                    "HIERARCHY": "HIERARCHY",
                    "Country": "ADMIN0_NAME",
                    "CountryCode": "COUNTRY_REGION_CODE",
                    "State": "ADMIN1_ABBR",
                    "HeightClearance": (attributes) => Math.min(attributes["FT_RST_HEIGHT"], attributes["TF_RST_HEIGHT"]),
                    "WeightLimit": (attributes) => Math.min(attributes["FT_RST_WEIGHT"] , attributes["TF_RST_WEIGHT"]),
                    "TraversableByWalkers": (attributes) => attributes["RST_PEDESTRIANS"] === "Y" ? "F" : "T",
                    "TraversableByVehicle": (attributes) => attributes["FT_RST_AUTOMOBILES"] === "Y" && attributes["TF_RST_AUTOMOBILES"] === "Y" ? "F" : "T",
                    "Speedleft": (attributes) => attributes["FT_AVERAGE_SPEED"] ? convertSpeed(attributes["FT_AVERAGE_SPEED"]) : calculate_mph(attributes["METERS"], attributes["FT_MINUTES"]),
                    "PostedLeft": (attributes) =>
                        {
                            const ft_speed_limit = attributes["FT_RST_SPEED_LIMIT"],
                                ft_truck_speed_limit = attributes["TF_RST_TRUCK_SPEED_LIMIT"],
                                speed_category = attributes["SPEED_CATEGORY"];
                            if (ft_speed_limit || ft_truck_speed_limit)
                            {
                                return convertSpeed(ft_speed_limit || ft_truck_speed_limit);
                            }
                            else if (speed_category)
                            {
                                return convertSpeedCategoryToMPH(speed_category);
                            }
                            else
                            {
                                return "";
                            }
                        },
                    "Speedright": (attributes) => attributes["TF_AVERAGE_SPEED"] ? convertSpeed(attributes["TF_AVERAGE_SPEED"]) : calculate_mph(attributes["METERS"], attributes["TF_MINUTES"]),
                    "PostedRight": (attributes) =>
                        {
                            const tf_speed_limit = attributes["TF_RST_SPEED_LIMIT"],
                                tf_truck_speed_limit = attributes["TF_RST_TRUCK_SPEED_LIMIT"],
                                speed_category = attributes["SPEED_CATEGORY"];
                            if (tf_speed_limit || tf_truck_speed_limit)
                            {
                                return convertSpeed(tf_speed_limit || tf_truck_speed_limit);
                            }
                            else if (speed_category)
                            {
                                return convertSpeedCategoryToMPH(speed_category);
                            }
                            else
                            {
                                return "";
                            }
                        },
                    "LeftPostalCode": "LEFT_POSTAL_CODE",
                    "RightPostalCode": "RIGHT_POSTAL_CODE",
                    "Fromleft": (attributes) => attributes["LEFT_START_ADDRESS"] || 0,
                    "Toleft": (attributes) => attributes["LEFT_END_ADDRESS"] || 0,
                    "Fromright": (attributes) => attributes["RIGHT_START_ADDRESS"] || 0,
                    "Toright": (attributes) => attributes["RIGHT_END_ADDRESS"] || 0,
                    "GroupID": (attributes) => !attributes["FULL_STREET_NAME"] ? attributes["ID"] : 0,
                    "LENGTH_GEO": "METERS" // both unit are in meters
                },

                // Railroad
                [TF.arcgisLayers.sde.MAP_R]: {
                    "Name": "NAME"
                },

                // Landmark
                [TF.arcgisLayers.sde.MAP_LMPOLYLINE]: {
                    "Name": "NAME",
                },
                [TF.arcgisLayers.sde.MAP_LMPOLYGON]: {
                    "Name": "NAME",
                },
                [TF.arcgisLayers.sde.MAP_LMPOINT]: {
                    "Name": "NAME",
                },

                // Municipal Boundary
                [TF.arcgisLayers.sde.MAP_MC]: {
                    "Name": "NAME",
                    "State": "ADMIN_LEVEL1",
                    "CountryCode": "COUNTRY_CODE",
                },

                // Postcode
                [TF.arcgisLayers.sde.MAP_PC] : {
                    "Name": "POSTAL_CODE",
                },

                // Water
                [TF.arcgisLayers.sde.MAP_WTPOLYGON]: {
                    "Name": "NAME",
                },
                [TF.arcgisLayers.sde.MAP_WTPOLYLINE]: {
                    "Name": "NAME",
                },
            }
        }
    }

    function afterFeatureLayerQueryFeatures(retValue, context, [query]) {
        let layer = this;
        let url = `${layer.url}/${layer.layerId}`;
        return afterExecuteQuery(retValue, url);
    }

    function afterQueryExecuteQueryJSON(retValue, context, [url, query, queryOptions]) {
        return afterExecuteQuery(retValue, url);
    }

    function afterQueryExecuteQueryPBF(retValue, context, [url, query, queryOptions]) {
        return afterExecuteQuery(retValue, url);
    }

    function afterExecuteQuery(retValue, url)
    {
        let [layerServer, layerId] = parseQueryUrl(url);
        if (!layerServer) return retValue;

        let layersMap = serverLayerFieldsMap[layerServer];
        if (!layersMap) return retValue;

        let fieldsMap = layersMap[layerId];
        if (!fieldsMap) return retValue;

        return retValue.then(result => {
            (result?.features || []).forEach(feature => {
                for (let fieldName in fieldsMap)
                {
                    let fieldReference = fieldsMap[fieldName];
                    let fieldValue = fieldReference instanceof Function ?
                                            fieldReference(feature.attributes) :
                                            feature.attributes[fieldReference];

                    feature.attributes[fieldName] = fieldValue;
                }
            });

            return result;
        });
    }

    function parseQueryUrl(url)
    {
        let serverName, layerId;
        let index = url.lastIndexOf("/");
        if (index !== -1)
        {
            let serverPath = url.slice(0, index),
                layerPath = url.slice(index + 1);
            layerId = layerPath.split("?")[0];
            switch (serverPath)
            {
                case window.arcgisUrls.NationalMapsMapEditingOneServiceDynamic:
                    serverName = editingMapServer;
                    break;
                case window.arcgisUrls.NationalMapsMapEditingOneService:
                    serverName = editingFeatureServer;
                    break;
                case window.arcgisUrls.NationalMapsMapEditingOneServiceFile:
                    serverName = routingMapServer;
                    break;
            }
        }
        return [serverName, layerId];
    }

    function calculate_mph(meter, minutes)
    {
        if (!minutes) return "";

        const distance = meter || 0;
        const METER_TO_MILE = 1.0 / 1609.34;
        const MINUTE_TO_HOUR = 1.0 / 60;
        const mph = (distance * METER_TO_MILE) / (minutes * MINUTE_TO_HOUR);
        return mph == 0 ? "" : mph;
    }

    function convertSpeedCategoryToMPH(speedCategory)
    {
        const SPEED_CATEGORY_TO_MPH = [0, 80, 70, 60, 45, 35, 25, 15, 5];
        const index = parseInt(speedCategory);
        if (index >= 0 && index <= 8)
        {
            return SPEED_CATEGORY_TO_MPH[index];
        }

        return 0;
    }

    // #endregion tf.map.ArcGIS.FeatureLayer.prototype.queryFeatures

	function interceptVRPTool()
	{
		if (TF.RoutingMap.RoutingPalette?.VRPTool?.prototype)
		{
			TF.RoutingMap.RoutingPalette.VRPTool.prototype.processVPRTask = function(url, params)
			{
				delete params.folder_path;
				delete params.active_folder;
				delete params.restrictions; // Use the default values.
				params.time_attribute = "TravelTime"; // "Time" -> "TravelTime"
				params.distance_units = "Miles"; // default "Kilometers" -> "Miles"

				return tf.map.ArcGIS.geoprocessor.execute(url, params);
			}
		}
	};
})();