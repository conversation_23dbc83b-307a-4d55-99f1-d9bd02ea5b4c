﻿(function()
{
	createNamespace("TF.Modal.ResourceScheduler").AddEditResourceGroupModalViewModel = AddEditResourceGroupModalViewModel;

	function AddEditResourceGroupModalViewModel(resourceGroupEntity, drivers, aides, vehicles, fieldTripId, assignedResGroups)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Resource Group');
		this.sizeCss = "modal-dialog-sm";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/resourcescheduler/addeditresourcegroup');
		this.buttonTemplate("modal/positivenegative");
		this.addEditResourceGroupViewModel = new TF.Control.ResourceScheduler.AddEditResourceGroupViewModel(resourceGroupEntity, drivers, aides, vehicles, fieldTripId, assignedResGroups);
		this.data(this.addEditResourceGroupViewModel);
	};

	AddEditResourceGroupModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AddEditResourceGroupModalViewModel.prototype.constructor = AddEditResourceGroupModalViewModel;

	AddEditResourceGroupModalViewModel.prototype.positiveClick = function()
	{
		return this.addEditResourceGroupViewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
			}.bind(this));
	};
})();