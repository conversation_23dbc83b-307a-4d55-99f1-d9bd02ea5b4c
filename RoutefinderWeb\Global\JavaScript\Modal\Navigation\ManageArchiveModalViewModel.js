(function()
{
	createNamespace("TF.Modal.Navigation").ManageArchiveModalViewModel = ManageArchiveModalViewModel;

	function ManageArchiveModalViewModel(refreshDataSourceGrid)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-md";
		this.title('Archives');
		this.contentTemplate('Navigation/ManageArchive');
		this.buttonTemplate('modal/positive');
		this.obPositiveButtonLabel("Done");
		this.obDisableControl(true);
		this.openDataSourceViewModel = new TF.Navigation.ManageArchiveViewModel(refreshDataSourceGrid);
		this.data(this.openDataSourceViewModel);
	}

	ManageArchiveModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ManageArchiveModalViewModel.prototype.constructor = ManageArchiveModalViewModel;

})();