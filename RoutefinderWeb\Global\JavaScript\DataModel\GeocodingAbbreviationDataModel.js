﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.GeocodingAbbreviationDataModel = function(dataModel)
	{
		namespace.BaseDataModel.call(this, dataModel);
	}

	namespace.GeocodingAbbreviationDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.GeocodingAbbreviationDataModel.prototype.constructor = namespace.GeocodingAbbreviationDataModel;

	namespace.GeocodingAbbreviationDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "ClientId", default: "" },
		{ from: "TypeString", default: "Replace" },
		{ from: "Type", default: 1 },
		{ from: "Term", default: "" },
		{ from: "ConvertTo", default: "" },
		{ from: "TempId", default: "" }
	];
})();