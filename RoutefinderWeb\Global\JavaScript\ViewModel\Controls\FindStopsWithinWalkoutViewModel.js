(function()
{
	createNamespace("TF.Control").FindStopsWithinWalkoutViewModel = FindStopsWithinWalkoutViewModel;

	FindStopsWithinWalkoutViewModel.BoundaryType = {
		StreetPath: 0,
		Radius: 1,
	};

	function FindStopsWithinWalkoutViewModel(data)
	{
		var self = this;
		this.boundaryTypes = [
			{ value: FindStopsWithinWalkoutViewModel.BoundaryType.StreetPath, text: 'Street Path' },
			{ value: FindStopsWithinWalkoutViewModel.BoundaryType.Radius, text: 'Radius' }
		];

		var setting = this._getSetting();
		this.selectedBoundaryType = ko.observable(setting.selectedBoundaryType);

		this.walkoutDistance = ko.observable(setting.distance);
		this.walkoutBuffer = ko.observable(setting.buffer);
		this.selectedDistanceUnit = ko.observable(setting.distanceUnit);
		this.selectedBufferUnit = ko.observable(setting.bufferUnit);

		this.bufferDisable = ko.computed(function()
		{
			return self.selectedBoundaryType() == FindStopsWithinWalkoutViewModel.BoundaryType.Radius;
		});

		this.requirements = data.ValidRequirements;
		this.student = data.Student;
		this.sessions = this.requirements.map(function(r, index)
		{
			var requirement = r.StudentRequirement,
				dateRange = requirement.StartDate && requirement.EndDate ? requirement.StartDate + " - " + requirement.EndDate : (
					!requirement.StartDate && !requirement.EndDate ? "All" : (requirement.StartDate ? "Starting " + requirement.StartDate : "Until " + requirement.EndDate)
				),
				location = new TF.DataModel.StudentRequirementModel(requirement.StudentLocation);

			return {
				text: TF.SessionType.toString(requirement.SessionID) + " - " + location.locationTypeName() + " - (" + dateRange + ")",
				value: index,
				location: location.locationTypeName(),
				dateRange: dateRange,
				session: TF.SessionType.toString(requirement.SessionID),
				daysString: TF.DataModel.StudentRequirementModel.daysToString(requirement)
			};
		});

		this.selectedSession = ko.observable(0);

		this.requirementsHeaderTemplate = '<table><tr >' +
			'<td style="width:100px;font-weight: bold;">Session</td>' +
			'<td style="width:80px;font-weight: bold;">Location</td>' +
			'<td style="width:150px;font-weight: bold;">Days</td>' +
			'<td style="width:200px;font-weight: bold;">Effective Dates</td></tr></table>';
		this.requirementsItemTemplate = '<table><tr><td style="width:100px;">${session}</td>' +
			'<td style="width:80px;">${location}</td>' +
			'<td style="width:150px;">${daysString}</td>' +
			'<td style="width:200px;">${dateRange}</td></tr></table>';
	}

	FindStopsWithinWalkoutViewModel.prototype.apply = function()
	{
		this._saveSetting();
		return {
			selectedBoundaryType: this.selectedBoundaryType(),
			distance: this.walkoutDistance(),
			distanceUnit: this.selectedDistanceUnit(),
			buffer: this.walkoutBuffer(),
			bufferUnit: this.selectedBufferUnit(),
			session: this.requirements[this.selectedSession()]
		};
	};

	FindStopsWithinWalkoutViewModel.prototype._saveSetting = function()
	{
		tf.storageManager.save("findStopsWithinWalkout", {
			selectedBoundaryType: this.selectedBoundaryType(),
			distance: this.walkoutDistance(),
			distanceUnit: this.selectedDistanceUnit(),
			buffer: this.walkoutBuffer(),
			bufferUnit: this.selectedBufferUnit()
		});
	};

	FindStopsWithinWalkoutViewModel.prototype._getSetting = function()
	{
		return $.extend({
			selectedBoundaryType: FindStopsWithinWalkoutViewModel.BoundaryType.StreetPath,
			distance: 100,
			distanceUnit: "meters",
			buffer: 30,
			bufferUnit: "meters",
		}, tf.storageManager.get("findStopsWithinWalkout"));
	};

})();