﻿(function()
{
	SchoolListItemsViewModel = function()
	{
		this.items = ko.observableArray();

		this.add = function(item)
		{
			this.items.push(item);
		};

		this.selectedValues = ko.computed(function()
		{
			var returnValue = [];
			for (var i = 0; i < this.items().length; i++)
			{
				if (this.items()[i].selected())
				{
					returnValue.push(this.items()[i].text);
				}
			}
			return returnValue;
		}, this);

		this.itemClicked = function(item, e)
		{
			this.clearSelections();
			item.selected(true);
			this.text = item.text;
		}.bind(this);

		this.clearSelections = function()
		{
			var items = this.items();
			for (var i = 0, length = items.length; i < length; i++)
			{
				if (items[i].selected())
				{
					items[i].selected(false);
				}
			}
		};
	};

	createNamespace("TF.Control").SchoolListItemsViewModel = SchoolListItemsViewModel;
})();