﻿(function()
{
	createNamespace("TF.Modal").ReminderViewModalViewModel = ReminderViewModalViewModel;

	function ReminderViewModalViewModel(reminder)
	{
		TF.Modal.BaseModalViewModel.call(this);
		if (reminder.Id > 0)
		{
			this.title('Edit Reminder');
		}
		else
		{
			this.title('New Reminder');
		}
		this.contentTemplate('modal/reminderviewcontrol');
		this.buttonTemplate('modal/positivenegative');
		this.viewModel = new TF.Control.ReminderViewViewModel(reminder, this);
		this.data(this.viewModel);
	}

	ReminderViewModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ReminderViewModalViewModel.prototype.constructor = ReminderViewModalViewModel;

	ReminderViewModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		if (this.saving)
		{
			return;
		}
		this.saving = true;
		this.viewModel.apply().then((result) =>
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}).finally(() =>
		{
			this.saving = false;
		});
	};

	ReminderViewModalViewModel.prototype.negativeClick = function()
	{
		this.viewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.positiveClick();
			}
			else if (result === false)
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};

	ReminderViewModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};

})();
