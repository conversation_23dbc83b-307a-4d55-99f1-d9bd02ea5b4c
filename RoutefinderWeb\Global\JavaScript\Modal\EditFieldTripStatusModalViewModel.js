﻿(function()
{
	createNamespace('TF.Modal').EditFieldTripStatusModalViewModel = EditFieldTripStatusModalViewModel;

	function EditFieldTripStatusModalViewModel(fieldTripRecords)
	{
		var self = this,
			tripPlural = tf.applicationTerm.getApplicationTermPluralByName("Field Trip");
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/editfieldtripstatuscontrol');
		this.buttonTemplate('modal/positivenegativeother');

		if (fieldTripRecords.length > 1)
		{
			self.title("Change Status of " + fieldTripRecords.length + " " + tripPlural);
		}
		else
		{
			var name = fieldTripRecords[0].Name;
			self.title("Change Status of " + name);
		}

		self.obPositiveButtonLabel = ko.observable("Change");
		self.obNegativeButtonLabel = ko.observable("Cancel");
		self.obOtherButtonLabel = ko.observable("Change without Commenting");

		self.editFieldTripStatusViewModel = new TF.Control.EditFieldTripStatusViewModel(fieldTripRecords);
		this.data(this.editFieldTripStatusViewModel);
		this.sizeCss = "modal-dialog-sm fieldtrip-status";
	}

	EditFieldTripStatusModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	EditFieldTripStatusModalViewModel.prototype.constructor = EditFieldTripStatusModalViewModel;

	EditFieldTripStatusModalViewModel.prototype.otherClick = function()
	{
		var self = this;
		self.editFieldTripStatusViewModel.applyWithoutComments().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};

	EditFieldTripStatusModalViewModel.prototype.positiveClick = function()
	{
		this.editFieldTripStatusViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();
