﻿.modal-pic-dialog {
	width: 750px;
}

.modal-pic-dialog img.normal {
	width: 146px;
	height: 146px;
}

.reversion {
	-moz-transform: scale(-1, 1);
	-ms-transform: scale(-1, 1);
	-o-transform: scale(-1, 1);
	-webkit-transform: scale(-1, 1);
	transform: scale(-1, 1);
}

.img-container {
	display: flex;
	align-items: center;
	justify-content: center;
}

.uploadedPhoto {
	max-width: 146px;
}

.modal-pic-dialog .img-preview,
.modal-pic-dialog img.normal {
	overflow: hidden;
	max-width: 146px;
	max-height: 146px;
	width: auto;
	height: auto;
}

.modal-pic-dialog .img-preview.small,
.modal-pic-dialog img.small {
	width: 66px;
	height: 66px;
}

.modal-pic-dialog .img-preview.extra-small,
.modal-pic-dialog img.extra-small {
	width: 40px;
	height: 40px;
}

.cropper-view-box {
	outline-color: #ffcc00;
}

.cropper-point {

	&.point-e,
	&.point-n,
	&.point-w,
	&.point-s {
		display: none;
	}
}

.cropper-line,
.cropper-point {
	background-color: #ffcc00;
}