(function()
{
	createNamespace('TF.Control').RoutingProfileViewModel = RoutingProfileViewModel;

	function RoutingProfileViewModel(routingProfileId, assignedUser, newCopy)
	{
		this.routingProfileId = routingProfileId;
		this.assignedUser = !!assignedUser;
		this.isNewCopy = !!newCopy;
		this.obEntityDataModel = ko.observable(new TF.DataModel.RoutingProfileDataModel());
		this.oldAssiginedUsers = [];
		this.obSelectedAssiginedUsers = ko.observableArray();
		this.obSelectedAssiginedUsersText = ko.computed(function()
		{
			return Enumerable.From(this.obSelectedAssiginedUsers()).OrderBy("$.LastName").ThenBy("$.FirstName").Select(function(c)
			{
				return c.LastName + " , " + c.FirstName;
			}).ToArray().join("<br/>");
		}, this);
		this.initDefStopBdyFill();
		this.obSelectedAssiginedUsers.subscribe(function(value)
		{
			var routingProfileId = this.obEntityDataModel().id();
			this.obEntityDataModel().routingProfileUsers(value.map(function(item)
			{
				return {
					RoutingProfileId: routingProfileId,
					UserId: item.Id,
					LastName: item.LastName,
					FirstName: item.FirstName
				};
			}));
		}, this);

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		this.obUseThisSpeed = ko.observable();

		this.obUseThisSpeed.subscribe(function(value)
		{
			this.obEntityDataModel().useThisSpeed(value == "averageSpeed");
		}, this);

		this.obNotUseThisSpeed = ko.computed(function()
		{
			return !this.obEntityDataModel().useThisSpeed();
		}, this);


		this.obUseSpeedLimitForPath = ko.observable();
		this.obUseSpeedLimitForPath.subscribe(function(value)
		{
			this.obEntityDataModel().useSpeedLimitForPath(value == "shortestTime");
		}, this);
		this.initRadioInput();
	}

	RoutingProfileViewModel.prototype.initRadioInput = function()
	{
		this.obUseThisSpeed(this.obEntityDataModel().useThisSpeed() ? "averageSpeed" : "routingSpeed");
		this.obUseSpeedLimitForPath(this.obEntityDataModel().useSpeedLimitForPath() ? "shortestTime" : "shortestDestance");
	};

	RoutingProfileViewModel.prototype.initDefStopBdyFill = function()
	{
		this.obDefStopBdyFills = ko.observableArray([
		{
			value: "1",
			text: "None"
		},
		{
			value: "50",
			text: "Semitransparent"
		},
		{
			value: "2",
			text: "Solid"
		}]);
		this.obSelectedDefStopBdyFill = ko.observable();
		this.obSelectedDefStopBdyFillText = ko.computed(function()
		{
			var item = Enumerable.From(this.obDefStopBdyFills()).Where(function(c)
			{
				return c.value === this.obEntityDataModel().defStopBdyFill();
			}.bind(this)).ToArray()[0];
			return item.text;
		}, this);

		this.obSelectedDefStopBdyFill.subscribe(function(data)
		{
			this.obEntityDataModel().defStopBdyFill(data.value);
		}, this);

		this.obSelectedDefStopBdyFill(this.obDefStopBdyFills().filter(function(item)
		{
			return item.value == this.obEntityDataModel().defStopBdyFill();
		}.bind(this))[0]);
	};

	RoutingProfileViewModel.prototype.init = function(model, el)
	{
		var self = this,
			validatorFields = {};
		this.$form = $(el);
		var nameInput = $(el).find("[name=name]");
		var isValidating = false;
		setTimeout(function()
		{
			function updateErrors($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			}

			validatorFields.name = {
				trigger: "blur change",
				validators:
				{
					notEmpty:
					{
						message: "required"
					},
					callback:
					{
						message: " must be unique",
						callback: function(value, validator, $field)
						{

							if (!value)
							{
								updateErrors($field, "unique");
								return true;
							}
							else
							{
								updateErrors($field, "required");
							}

							return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "routingprofile", "unique"),
								{
									data:
									{
										Name: value,
										Id: this.obEntityDataModel().id()
									}
								},
								{
									overlay: false
								})
								.then(function(apiResponse)
								{
									return apiResponse.Items[0];
								});

						}.bind(this)
					}
				}
			};

			validatorFields.defStopBdyLine = {
				trigger: "blur change",
				validators:
				{
					greaterThan:
					{
						value: 1,
						message: ' must be >= 1'
					},
					lessThan:
					{
						value: 7,
						message: ' must be <= 7'
					}
				}
			};

			validatorFields.averageSpeed = {
				trigger: "blur change",
				validators:
				{
					callback:
					{
						message: " must be greater than or equal to 1 and less than or equal to 99",
						callback: function(value, validator, $field)
						{

							if (this.obEntityDataModel().useThisSpeed())
							{
								var speed = this.obEntityDataModel().averageSpeed();
								if (speed >= 1 && speed <= 99)
								{
									return true;
								}
								return false;
							}
							return true;
						}.bind(this)
					}
				}
			};


			$(el).bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

			this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
		}.bind(this));


		setTimeout(function()
		{
			nameInput.focus();
		}, 1000);

		this.load();
	};

	RoutingProfileViewModel.prototype.load = function()
	{

		if (!this.routingProfileId)
		{
			return;
		}
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "routingprofile", this.routingProfileId))
			.then(function(response)
			{
				if (response && response.StatusCode === 404)
				{
					return Promise.reject(response);
				}

				var item = response.Items[0];

				this.obEntityDataModel(new TF.DataModel.RoutingProfileDataModel(item));
				this.initRadioInput();

				this.obSelectedAssiginedUsers(this.obEntityDataModel().routingProfileUsers().map(function(item)
				{
					return {
						Id: item.UserId,
						LastName: item.LastName,
						FirstName: item.FirstName
					};
				}));
				this.oldAssiginedUsers = this.obSelectedAssiginedUsers().slice();
				if (this.isNewCopy)
				{
					this.obEntityDataModel().id(0);
					this.obEntityDataModel().name('');
				}
			}.bind(this));
	};

	RoutingProfileViewModel.prototype.selectAssiginedUser = function()
	{
		var self = this;
		tf.modalManager.showModal(
				new TF.Modal.ListMoverSelectRecipientControlModalViewModel(
					this.obSelectedAssiginedUsers(),
					{
						title: "Assigined Users",
						description: "You may select one or more of the Routefinder Pro user accounts. ",
						filterCheckboxText: "Show Active",
						emailCheck: false,
						typeDisplayName: "assigned users",
						columnSources:
						{
							user: [
							{
								FieldName: "LoginId",
								DisplayName: "Login",
								Width: "100px",
								type: "string",
								isSortItem: true
							},
							{
								FieldName: "LastName",
								DisplayName: tf.applicationTerm.getApplicationTermSingularByName("Last Name"),
								Width: "100px",
								type: "string",
								isSortItem: true
							},
							{
								FieldName: "FirstName",
								DisplayName: tf.applicationTerm.getApplicationTermSingularByName("First Name"),
								Width: "100px",
								type: "string"
							}]
						},
						modifySource: function(source)
						{
							return Enumerable.From(source).OrderBy("$.LoginId").ThenBy("$.LastName").ThenBy("$.FirstName").ToArray();
						},
						checkResult: self.checkAssignedUser.bind(self)
					}
				)
			)
			.then(function(selected)
			{
				if (selected && $.isArray(selected))
				{
					this.obSelectedAssiginedUsers(selected);
				}
			}.bind(this));
	};

	RoutingProfileViewModel.prototype.checkAssignedUser = function(newUsers)
	{
		var self = this;
		var newUsersEnum = Enumerable.From(newUsers);
		var deletedUsers = Enumerable.From(this.oldAssiginedUsers).Where(function(item)
		{
			return !newUsersEnum.Any("$.Id==" + item.Id);
		}).ToArray();
		if (deletedUsers.length > 0)
		{
			return self.checkOtherDefault().then(function(otherDefault)
			{
				if (self.obEntityDataModel().isDefault() && !otherDefault)
				{
					return tf.promiseBootbox.alert("This Routing Profile is the Default.  Users cannot be unassigned from it.  They must be assigned to another Routing Profile.",
						"Warning"
					).then(function()
					{
						return Promise.resolve(false);
					});
				}
				else if (otherDefault && !self.obEntityDataModel().isDefault())
				{
					return tf.promiseBootbox.yesNo(
					{
						message: "One or more users have been unassigned from this Routing Profile ( " + self.obEntityDataModel().name() + " ).  They will be assigned to the default Routing Profile ( " + otherDefault.Name + " ) when you save the changes to this Routing Profile.  Are you sure you want to apply these changes?",
						title: "Confirmation Message"
					}).then(function(yesNo)
					{
						return Promise.resolve(yesNo);
					});
				}
				else if (!otherDefault)
				{
					return tf.promiseBootbox.alert("There is no Default Routing Profile.  Users cannot be unassigned.   They must be assigned to another Routing Profile.",
						"Warning"
					).then(function()
					{
						return Promise.resolve(false);
					});
				}
			});
		}
		console.log(deletedUsers);
		return Promise.resolve();
	};

	RoutingProfileViewModel.prototype.minusDefStopBdyLine = function()
	{
		var line = Number(this.obEntityDataModel().defStopBdyLine());
		this.obEntityDataModel().defStopBdyLine(line - 1 < 1 ? 1 : (line - 1));
	};

	RoutingProfileViewModel.prototype.addDefStopBdyLine = function()
	{
		var line = Number(this.obEntityDataModel().defStopBdyLine());
		this.obEntityDataModel().defStopBdyLine(line + 1 > 7 ? 7 : (line + 1));
	};

	RoutingProfileViewModel.prototype.checkOtherDefault = function()
	{
		var self = this;
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "routingprofile", "checkotherdefault"),
			{
				data: self.obEntityDataModel().toData()
			})
			.then(function(response)
			{
				return Promise.resolve(response.Items.length > 0 ? response.Items[0] : null);
			});
	};

	RoutingProfileViewModel.prototype.save = function()
	{
		var self = this;
		return this.pageLevelViewModel.saveValidate().then(function(result)
		{
			if (self.obEntityDataModel().isDefault())
			{
				return self.checkOtherDefault().then(function(otherDefault)
				{
					if (otherDefault && otherDefault.Name)
					{
						return tf.promiseBootbox.yesNo(
						{
							message: "Another Routing Profile is set as the Default ( " + otherDefault.Name + " ).  Are you sure you would like to set this Profile as the Default?",
							title: "Confirmation Message"
						}).then(function(yesNo)
						{
							return Promise.resolve(yesNo);
						});
					}
					return Promise.resolve(result);
				});
			}
			return Promise.resolve(result);
		}).then(function(result)
		{
			if (result)
			{
				return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "routingprofile"),
					{
						data: self.obEntityDataModel().toData()
					})
					.then(function(response)
					{
						return response.Items[0];
					}, function(response)
					{
						tf.promiseBootbox.alert(response.Message, "Warning");
					});
			}

			return Promise.resolve(false);

		});
	};


	RoutingProfileViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(data)
			{
				return data;
			});
	};


	RoutingProfileViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();
