﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListGradeDeletion = DataListGradeDeletion;

	function DataListGradeDeletion()
	{
		this.type = 'grades';
		this.deleteType = 'Grade';
		this.deleteRecordName = 'Grade';
		namespace.DataListBaseDeletion.call(this, true);
	}

	DataListGradeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListGradeDeletion.prototype.constructor = DataListGradeDeletion;

	DataListGradeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

	DataListGradeDeletion.prototype.getAssociatedData = function(ids)
	{
		const self = this;

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `grades/${ids[0]}/status`))
			.then(function(response)
			{
				if (response && response.InUse)
				{
					return [{
						type: 'grades',
						items: [1]
					}];
				}

				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `grades`))
					.then(function(response)
					{
						const grades = response.Items;
						const isFeedToGrade = grades.findIndex(g => ids.includes(g.FeedTo)) >= 0;
						if (!isFeedToGrade)
						{
							return []; // Next step would check the response is array OR not, if it is empty array, it would prompt a delete confirm message.
						}

						var confirmMessage = "This Grade feeds to another grade, are you sure you want to delete it?";
						return tf.promiseBootbox.yesNo(confirmMessage, "Delete Confirmation")
							.then(function(ans)
							{
								//if confirm no , delete nothing
								if (!ans)
								{
									self.deleteIds = [];
								}

								return; // Next step would check the response is array OR not, if it is undefined, it would prompt nothing.
							});
					});
			});
	};
})();