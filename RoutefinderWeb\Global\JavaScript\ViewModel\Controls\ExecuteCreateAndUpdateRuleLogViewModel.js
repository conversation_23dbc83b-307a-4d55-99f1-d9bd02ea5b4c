﻿(function()
{
	createNamespace('TF.Control').ExecuteCreateAndUpdateRuleLogViewModel = ExecuteCreateAndUpdateRuleLogViewModel;

	const ErrorItemTypeCode = 0;

	function ExecuteCreateAndUpdateRuleLogViewModel(executeCreateAndUpdateRuleResult, gridNavigator)
	{
		self = this;
		// Convert error message to a object instance.
		this.executeCreateAndUpdateRuleResult = prepareErrorMessage(executeCreateAndUpdateRuleResult);
		this.Summary = this.prepareSummary();
		this.LogTime = utc2Local(_.head(this.executeCreateAndUpdateRuleResult) ?.LogTime);

		this.GridNavigator = function()
		{
			if (!gridNavigator)
			{
				return;
			}
			const targetDataType = tf.dataTypeHelper.getIdByName(this.ActionDataType);
			const id = this.ErrorMessage?.HotlinkRecordId;

			if (!id)
			{
				return;
			}
			gridNavigator(targetDataType, [id]);

			//close log window
			tf.modalManager.hideAll();
		};
	}

	ExecuteCreateAndUpdateRuleLogViewModel.prototype.prepareSummary = function()
	{
		const validateResults = this.executeCreateAndUpdateRuleResult.map(l => l?.ErrorMessage);
		const totalRecords = validateResults.length;
		const successCount = validateResults.filter(v => !v?.FormRuleExecuteResultItems.some(x => x.ItemType === undefined || x.ItemType === ErrorItemTypeCode)).length;
		const failedCount = totalRecords - successCount;
		return `Total ${totalRecords} record(s), Succeeded: ${successCount}, Failed: ${failedCount}`;
	}

	ExecuteCreateAndUpdateRuleLogViewModel.prototype.shouldBeLink = function(record)
	{
		// create failed or has no permission will not display as link.
		const createFailed = hasError(record.ErrorMessage);

		if (createFailed)
		{
			return false;
		}
		const key = tf.dataTypeHelper.getKeyByName(record.ActionDataType);
		return tf.authManager.isAuthorizedForDataType(key, "read");
	}

	ExecuteCreateAndUpdateRuleLogViewModel.prototype.generateExecuteResultMessage = function(record)
	{
		validateResult = record?.ErrorMessage;

		const actionResult = hasError(validateResult) ? "failed" : "successfully";
		const actionDisplay = validateResult.ActionType === TF.Enums.FormRule.UpdateRecord ? "updated" : "created";
		const recordName = validateResult.RecordName != null ? validateResult.RecordName + ' ' : '';
		const actionMsg = hasError(validateResult) ? `${actionDisplay} ${actionResult}:` : `${actionResult} ${actionDisplay}.`

		const resultBuilder = [];
		let message = `Record ${recordName}${actionMsg}`;
		if (validateResult.ActionType === TF.Enums.FormRule.CreateAssociatedRecord)
		{
			message = `Associated record of ${recordName}${actionMsg}`;
		}
		resultBuilder.push(message);

		_.forEach(validateResult.FormRuleExecuteResultItems, errMsg =>
		{
			if (errMsg.Field === null || errMsg.Field === undefined ||  _.isEmpty(errMsg.Field))
			{
				resultBuilder.push(`${errMsg.ErrorMessage}`);
			} else
			{
				resultBuilder.push(`${errMsg.Field}: ${errMsg.ErrorMessage}`);
			}			
		})

		return resultBuilder.join("\r\n");
	}

	function utc2Local(value)
	{
		const dt = utcToClientTimeZone(value);
		return dt.isValid() ? dt.format("MM/DD/YYYY hh:mm A") : "";
	}

	function hasError(e)
	{
		return e.FormRuleExecuteResultItems && e.FormRuleExecuteResultItems.some(x => x.ItemType === undefined || x.ItemType === ErrorItemTypeCode);
	}

	function prepareErrorMessage(logs)
	{
		if (!logs)
		{
			logs = [];
		}

		let lastRuleName = null;
		for (let log of logs)
		{
			if (log?.ErrorMessage)
			{
				log.ErrorMessage = JSON.parse(log.ErrorMessage);
				log.ActionDataType = log?.ErrorMessage?.ActionDataType
					?? tf.dataTypeHelper.getNameById(log.RecordDataType);
				if (log.ErrorMessage?.RuleName !== lastRuleName)
				{
					log.isNewRuleSection = true;
					lastRuleName = log.ErrorMessage?.RuleName;
				}
			}
		}
		return logs;
	}

})();

