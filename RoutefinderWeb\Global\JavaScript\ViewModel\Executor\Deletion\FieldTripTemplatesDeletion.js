﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.FieldTripTemplatesDeletion = FieldTripTemplatesDeletion;

	function FieldTripTemplatesDeletion()
	{
		this.type = 'fieldtriptemplate';
		namespace.BaseDeletion.apply(this, arguments);
	}

	FieldTripTemplatesDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	FieldTripTemplatesDeletion.prototype.constructor = FieldTripTemplatesDeletion;

	FieldTripTemplatesDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtriphistory", "ids", "fieldtrip"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'fieldtriphistory',
				items: response.Items[0]
			});
		});

		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtripinvoice", "ids", "fieldtrip"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'fieldtripinvoice',
				items: response.Items[0]
			});
		});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	FieldTripTemplatesDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		var p0 = this.getDataPermission(ids, "fieldtriphistory", "fieldtrip");

		var p1 = this.getDataPermission(ids, "fieldtripinvoice", "fieldtrip");

		return Promise.all([p0]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	}


	FieldTripTemplatesDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "fieldtriphistory", "fieldtrip");

		var p2 = this.getDataStatus(this.ids, "fieldtripinvoice", "fieldtrip");

		return Promise.all([p0, p1, p2]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};
})();