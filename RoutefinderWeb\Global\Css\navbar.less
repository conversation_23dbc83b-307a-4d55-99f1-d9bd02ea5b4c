﻿@import 'z-index';

.tf-navbar {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	height: 45px;
	line-height: 47px;
	background-color: #6b6b6b;
	z-index: @navbar-z-index;

	.search {
		margin-top: 7px;
		margin-left: 50px;

		input {
			height: 30px;
			width: 300px;
			font-size: 16px;
			line-height: 30px;
		}

		.searchbutton {
			background-color: #D74B3C;
			color: white;
			width: 80px;
			height: 30px;
			cursor: pointer;
			text-align: center;
			line-height: 30px;
		}
	}
}

.tf-navbar div {
	height: inherit;
}

.tf-navbar .nav-logo {
	background-image: url(../img/icons/routefinder.png);
	display: block;
	width: 85px;
	background-repeat: no-repeat;
	background-position: 0 5px;
	background-size: contain;
	margin: 0 10px;
    cursor: pointer;
}

.tf-navbar div .nav-item {
	float: left;
	height: inherit;
}

.tf-navbar div .nav-textbutton {
	width: 60px;
	text-align: center;
	color: #ffffff;
	cursor: default;
}

.tf-navbar div .nav-textbutton:hover {
	background-color: #E4E4E4;
	color: black;
}

.tf-navbar div .nav-textbutton.contextmenu-open,
.tf-navbar .popup-message,
.tf-navbar div.nav-item.contextmenu-open {
	background-color: #db4d37;
}

.tf-navbar div .iconbutton {
	width: 40px;
}

.tf-navbar div .divider {
	border-left: 1px solid #E7E7E7;
	height: 50%;
	/* margin-top: 10%; */
	position: relative;
	top: 25%;
	margin: 0 5px;
}


.tf-navbar .popup-message {
	position: relative;
	top: 5px;
	left: 23px;
	width: 24px;
	height: 20px;
	line-height: 20px;
	border-radius: 5px;
	text-align: center;
	color: white;
	font-weight: bold;
}

.popup-menu.tf-contextmenu-wrap.popover-wrap {
	left: -164px;
}

.popup-menu > .tf-contextmenu.grid-popover:before {
	left: 160px;
	border-color: transparent transparent #797979;
	border-width: 10px;
	top: -18px;
}

.popup-menu > .tf-contextmenu.grid-popover:after {
	border-style: none;
}



.popup-menu {
	border-bottom: solid;
	border-bottom-width: 1px;
	border-bottom-color: #D7DADB;
	/*border-bottom: none;
	border-bottom-width: 0;
	border-bottom: solid;*/
	/*padding: 2px 10px;*/
	max-height: 650px;
}

.popup-menu ul {
	list-style: none;
	-webkit-padding-start: 0;
	margin-bottom: 0;
}

.popup-menu .header,
.popup-menu .button-item,
.popup-menu .reminder-data-type,
.popup-menu .reminder-data {
	height: 32px;
	line-height: 32px;
	padding: 0 10px;
	vertical-align: middle;
}

.popup-menu .reminder-data {
	padding-left: 43px;
	border-bottom-color: #f2f2f2;
	border-bottom-style: solid;
	border-bottom-width: 1px;
}

.popup-menu .text-eclipse {
	width: 95%;
}

.popup-menu .reminder-data:hover {
	background-color: #ECF2F9;
}

.popup-menu .title {
	font-weight: bold;
	margin: 0 5px;

	.normal {
		font-weight: normal;
	}
}

.popup-menu .header {
	display: flex;
	justify-content: space-between;
	-ms-align-content: center;
	-webkit-align-content: center;
	align-content: center;
	color: #fff;
	background-color: #797979;
}

.popup-menu .header > span {
	flex: 1;
}

.popup-menu .button-item {
	background-color: #f2f2f2;
}


.popup-menu .reminder-data-type {
	background-color: #f2f2f2;
}

.popup-menu .reminder-icon {
	height: 28px;
	width: 28px;
	margin: 2px 0;
	background-size: contain;
	display: inline-block;
	opacity: 0.3;
}

.popup-menu .scroll-item {
	overflow: auto;
}

.popup-menu .text-btn:hover {
	cursor: pointer;
}

.navbar-popup-menu {
	border: none;

	.tf-contextmenu.grid-popover {
		border: none;
	}

	.content {
		border-left: 1px solid #f2f2f2;
		border-right: 1px solid #f2f2f2;

		li {
			border-bottom: 1px solid #f2f2f2;

			&:last-child {
				border-bottom: none;
			}
		}
	}

	.text-btn {
		border: 1px solid #d7d7d7;
	}
}

.popup-menu .message-data-list {
	li {
		padding: 12px 5px 12px 24px;
		position: relative;
		cursor: pointer;

		&:hover {
			background-color: #f0f7ff;
		}
	}

	.unread-marker {
		position: absolute;
		left: 5px;
		top: 24px;
	}

	.date {
		color: #9a9a9a;
	}
}

.unread-marker {
	border-radius: 50%;
	background-color: #cc3300;
	width: 10px;
	height: 10px;
	display: inline-block;
}
