(function()
{
	createNamespace("TF.Map").PlaybackTool = PlaybackTool;

	function PlaybackTool(routingMapTool)
	{
		this.routingMapTool = routingMapTool;
		this.obTrips = routingMapTool.options.obTrips;
		this.map = this.routingMapTool.routingMapDocumentViewModel._map;
		this.playbackContainer = null;
		this.tripPlaybackControlTool = new TF.RoutingMap.TripPlaybackControlTool(() => { return this.map; }, this.obTrips);
		this.obIsTripTimeRange = ko.observable();
		this.obIsTripTimeRange.subscribe(() => { this.tripPlaybackControlTool.updateTimeRange = this.obIsTripTimeRange(); });
		this.obIsTripTimeRange(false);
		this.obTripsSubscribe = this.obTrips.subscribe(() =>
		{
			this.tripPlaybackControlTool.initTripData();
			this.obVisible() && this.hasGPS && this.loadAndDisplayGPS();
		});
		this.obTrips.extend({ notify: "always" });
		this.isDetailView = routingMapTool.options.isDetailView;
		this.obDate = ko.observable(moment().currentTimeZoneTime().toDate());
		this.obConnectPlannedVSActual = ko.observable(true);
		this.obTimeDifference = ko.observable(true);
		this.obVisible = ko.observable(false);
		this.hasGPS = tf.authManager.hasGPS() && tf.authManager.GPSConnectValid;
		this.initSetting();
		this.initGPS();
		this.date = moment(this.obDate()).format("MMM DD, YYYY");
	}

	PlaybackTool.prototype.initGPS = function()
	{
		if (!this.hasGPS)
		{
			return;
		}
		this.obDate.subscribe(this.loadAndDisplayGPS, this);
		this.symbol = new TF.Map.Symbol();
		this.obConnectPlannedVSActual.subscribe(this.displayConnectLine, this);
		this.obTimeDifference.subscribe(this.displayTimeDifference, this);
		this.tripPlaybackControlTool.onProcessEvent.subscribe(this.onProcess.bind(this));
	};

	PlaybackTool.prototype.initSetting = function()
	{
		var self = this;
		var key = "playbackPlanVsActualSetting";
		loadSetting();
		function saveSetting()
		{
			tf.storageManager.save(key, {
				connectPlannedVSActual: self.obConnectPlannedVSActual(),
				timeDifference: self.obTimeDifference(),
				tripTimeRange: self.obIsTripTimeRange()
			});
		}

		function loadSetting()
		{
			var setting = tf.storageManager.get(key);
			if (setting)
			{
				self.obConnectPlannedVSActual(setting.connectPlannedVSActual);
				self.obTimeDifference(setting.timeDifference);
				self.obIsTripTimeRange(setting.tripTimeRange);
			}
		}

		this.obConnectPlannedVSActual.subscribe(saveSetting);
		this.obTimeDifference.subscribe(saveSetting);
		this.obIsTripTimeRange.subscribe(saveSetting);
	};

	PlaybackTool.prototype.toggleDisplay = function()
	{
		if (this.tripPlaybackControlTool.visible())
		{
			this._hide();
		} else
		{
			this._show();
		}
	};

	PlaybackTool.prototype.playNowButtonClick = function()
	{
		this.tripPlaybackControlTool.tickTool.playNow();
	};

	PlaybackTool.prototype._createPlaybackControl = function()
	{
		if (!this.playbackContainer)
		{
			var $container = this.routingMapTool.$container;
			this.playbackContainer = $(getContentHtml(this));
			$container.append(this.playbackContainer);
			ko.applyBindings(this, this.playbackContainer[0]);
			$('.datepicker-bottom').on('keypress', e =>
			{
				e.preventDefault();
				return false;
			});
		}
	};

	PlaybackTool.prototype._show = function()
	{
		this.obVisible(true);
		this._createPlaybackControl();
		this.playbackContainer.show();
		this.tripPlaybackControlTool.open();
		if (this.hasGPS)
		{
			this.initGPSLayer();
			this.loadAndDisplayGPS();
		}
	};

	PlaybackTool.prototype._hide = function()
	{
		this.obVisible(false);
		this.tripPlaybackControlTool.close();
		this.playbackContainer.hide();
		if (this.hasGPS)
		{
			this.disposeGPS();
		}
	};

	PlaybackTool.prototype.adjustPopupPosition = function(sender)
	{
		let target = sender.element, calendar = sender.dateView.calendar.element, dateView = sender.dateView.div;
		let $datePickerInput = target.closest(".datepicker-bottom");
		let offset = $datePickerInput.offset();
		let $calendar = calendar.closest(".k-animation-container");
		$calendar.css({
			top: offset.top - $calendar.height() - 4,
			left: offset.left - 47
		});
		dateView.css({ left: '' });
	};

	PlaybackTool.prototype.timeClick = function(data, e)
	{
		if ($(e.target).hasClass('datepicker-bottom')) return;
		let $o = $(e.currentTarget);
		$o.find('.datepicker-bottom').triggerHandler("click");
	};

	PlaybackTool.prototype.onProcess = function()
	{
		this.onProcessTimeout = setTimeout(() =>
		{
			if (this.obVisible && this.obVisible())
			{
				this.drawGPSVehicle();
			}
		});
	};

	PlaybackTool.prototype.initGPSLayer = function()
	{
		var vehicleColor = "#FF0800";
		this.gpsPointLayer = new tf.map.ArcGIS.FeatureLayer({
			objectIdField: "oid",
			fields: [
				{
					name: "oid",
					type: "oid"
				},
				{
					name: "id",
					type: "string"
				},
				{
					name: "displayKey",
					type: "string"
				}],
			source: [],
			spatialReference: {
				wkid: 102100
			},
			id: "gpsPointLayer",
			geometryType: "point",
			renderer: {
				type: "unique-value",
				field: "displayKey",
				defaultSymbol: this.symbol.getEventSymbol(false, vehicleColor),
				uniqueValueInfos: []
			}
		});

		this.gpsPolylineLayer = new tf.map.ArcGIS.FeatureLayer({
			objectIdField: "oid",
			fields: [
				{
					name: "oid",
					type: "oid"
				},
				{
					name: "id",
					type: "string"
				},
				{
					name: "displayKey",
					type: "string"
				}],
			source: [],
			spatialReference: {
				wkid: 102100
			},
			id: "gpsPolylineLayer",
			geometryType: "polyline",
			renderer: {
				type: "unique-value",
				field: "displayKey",
				defaultSymbol: this.symbol.tripPath(vehicleColor),
				uniqueValueInfos: []
			}
		});

		this.gpsPathArrowLayer = new tf.map.ArcGIS.FeatureLayer({
			objectIdField: "oid",
			fields: [
				{
					name: "oid",
					type: "oid"
				}, {
					name: "angle",
					type: "double"
				},
				{
					name: "color",
					type: "string"
				}],
			source: [],
			spatialReference: {
				wkid: 102100
			},
			minScale: TF.Helper.MapHelper.zoomToScale(this.map, 13),
			id: "gpsPathArrowLayer",
			geometryType: "point",
			renderer: this.getArrowRender()
		});

		this.gpsVehicleLayer = new tf.map.ArcGIS.GraphicsLayer({
			id: "gpsVehicleLayer",
			title: "GPS Vehicle"
		});

		this.gpsConnectLineLayer = new tf.map.ArcGIS.GraphicsLayer({
			id: "gpsConnectLineLayer",
			title: "GPS Connect Line"
		});

		this.map.add(this.gpsPolylineLayer);
		this.map.add(this.gpsPointLayer);
		this.map.add(this.gpsPathArrowLayer);
		this.map.add(this.gpsConnectLineLayer);
		this.map.add(this.tripPlaybackControlTool.vehicleLayer);
		this.map.add(this.gpsVehicleLayer);

		this.extentChangeEvent = this.map.mapView.watch(["extent"], () =>
		{
			clearTimeout(this.extentChangeTimeout);
			this.extentChangeTimeout = setTimeout(() =>
			{
				if (this.map.mapView.stationary)
				{
					this.drawGPSArrow();
				}
			}, 500);
		});
	};

	PlaybackTool.prototype.clearGPSLayer = function()
	{
		return Promise.all([
			TF.Helper.MapHelper.clearLayer(this.gpsPolylineLayer),
			TF.Helper.MapHelper.clearLayer(this.gpsPointLayer),
			TF.Helper.MapHelper.clearLayer(this.gpsVehicleLayer),
			TF.Helper.MapHelper.clearLayer(this.gpsConnectLineLayer),
			TF.Helper.MapHelper.clearLayer(this.gpsPathArrowLayer),
		]);
	};

	PlaybackTool.prototype.disposeGPS = function()
	{
		this.map.remove(this.gpsPointLayer);
		this.map.remove(this.gpsPolylineLayer);
		this.map.remove(this.gpsVehicleLayer);
		this.map.remove(this.gpsConnectLineLayer);
		this.map.remove(this.gpsPathArrowLayer);
		clearTimeout(this.getGPSDataTimeout);
		clearTimeout(this.getLiveGPSDataTimeout);
		this.extentChangeEvent && this.extentChangeEvent.remove();
	};

	PlaybackTool.prototype.loadAndDisplayGPS = function()
	{
		var self = this;
		if (!this.hasGPS)
		{
			return;
		}
		clearTimeout(this.getGPSDataTimeout);
		this.getGPSDataTicket = (new Date()).getTime();
		this.getGPSDataTimeout = setTimeout((function(ticket)
		{
			return () =>
			{
				self.getGPSData().then(() =>
				{
					if (ticket == self.getGPSDataTicket && self.obVisible())
					{
						self.setTimeRangeForGPS();
						self.drawGPSData();
					}
				});
			};
		})(this.getGPSDataTicket), 500);

		function getLiveGpsInterval()
		{
			clearTimeout(self.getLiveGPSDataTimeout);
			self.getLiveGPSDataTimeout = setTimeout(() =>
			{
				self.getAndDisplayLiveGPSData().then((result) =>
				{
					if (result)
					{
						getLiveGpsInterval();
					}
				});
			}, 5000);
		}
		getLiveGpsInterval();
	};

	PlaybackTool.prototype.setTimeRangeForGPS = function()
	{
		if (!this.obIsTripTimeRange() || !this.hasGPS)
		{
			return;
		}
		var currentMin = this.tripPlaybackControlTool.tickTool.obTimeSliderMin();
		var allTimes = [this.tripPlaybackControlTool.tickTool.obTimeSliderMin(), this.tripPlaybackControlTool.tickTool.obTimeSliderMax()];

		for (var key in this.tripVehicleMapping)
		{
			if (this.tripVehicleMapping[key].vehicleEvents)
			{
				allTimes = allTimes.concat(this.tripVehicleMapping[key].vehicleEvents.map(x => this.tripPlaybackControlTool.tickTool.timeToSecond(convertToMoment(x.StartTime))));
			}
		}

		var minStartTime = Enumerable.From(allTimes).Min();
		var maxFinishTime = Enumerable.From(allTimes).Max();
		this.tripPlaybackControlTool.tickTool.setTimeSliderRange(minStartTime, maxFinishTime);
		if (minStartTime < currentMin)
		{
			this.tripPlaybackControlTool.tickTool.obTargetTimeAsSecond(minStartTime);
		}
	};

	PlaybackTool.prototype.getTripVehicles = function()
	{
		var tripIds = this.obTrips().map(x => x.Id);
		this.tripVehicleMapping = {};
		var colors = {};
		this.obTrips().forEach(c =>
		{
			if (c.VehicleId)
			{
				this.tripVehicleMapping[c.Id] = { Id: c.VehicleId };
			}
			colors[c.Id] = c.color;
		});
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "TripHistories"), {
			paramData: {
				"tripIds": tripIds.join(),
				"attendanceDate": this.getDate(),
			}
		}).then((response) =>
		{
			response.Items.forEach(c =>
			{
				if (c.VehicleId)
				{
					this.tripVehicleMapping[c.TripId] = {
						Id: c.VehicleId
					};
				}
			});
			var vehicleIds = [];
			for (var key in this.tripVehicleMapping)
			{
				vehicleIds.push(this.tripVehicleMapping[key].Id);
			}
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("vehicle")), {
				paramData: {
					"@fields": "BusNum,Id,Gpsid,VendorId",
					"@sort": "BusNum",
					"@filter": "in(id," + vehicleIds.join() + ")",
				}
			}).then((response) =>
			{
				var vehicles = response.Items;
				for (var key in this.tripVehicleMapping)
				{
					let vehicle = vehicles.find(c => c.Id == this.tripVehicleMapping[key].Id);
					if (vehicle)
					{
						vehicle.color = colors[key];
						this.tripVehicleMapping[key] = vehicle;
					}
				}
			});
		});
	};

	PlaybackTool.prototype.getGPSData = function()
	{
		var self = this;
		if (!self.obDate() || self.obTrips().length == 0)
		{
			return Promise.resolve();
		}
		return self.getTripVehicles().then(() =>
		{
			var gpsIds = [], vehicleIds = [], gpsVehicles = {};
			for (var key in self.tripVehicleMapping)
			{
				if (self.tripVehicleMapping[key].Gpsid)
				{
					const vehicle = self.tripVehicleMapping[key];
					vehicleIds.push(vehicle.Id);
					gpsIds.push(vehicle.Gpsid);
					const vehicleKey = TF.Helper.VehicleEventHelper.getKey(vehicle);
					gpsVehicles[vehicleKey] = vehicle;
				}
			}

			if (vehicleIds.length == 0)
			{
				return;
			}
			var startTime = toISOStringWithoutTimeZone(moment(self.obDate()).set({ hour: 0, minute: 0, seconds: 0 }));
			var endTime = toISOStringWithoutTimeZone(moment(self.obDate()).set({ hour: 23, minute: 59, seconds: 59 }));
			var getVehiclePosition = Promise.resolve();
			if (TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled)
			{
				getVehiclePosition = tf.promiseAjax.get(TF.Helper.VehicleEventHelper.getRequestURL(), {
					paramData:
					{
						startTime: startTime,
						endTime: endTime,
						vehicleIds: vehicleIds,
						databaseId: tf.datasourceManager.databaseId
					}
				});
			} else
			{
				getVehiclePosition = tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "busfindergps", "vehiclepositions"), {
					paramData:
					{
						startTime: startTime,
						endTime: endTime
					},
					data: gpsIds
				});
			}
			return getVehiclePosition.then((response) =>
			{
				if (self.obVisible())
				{
					for (var key in response.Items[0])
					{
						let vehicle = gpsVehicles[key];
						if (key == TF.Helper.VehicleEventHelper.getKey(vehicle))
						{
							vehicle.vehicleEvents = response.Items[0][key];
							TF.Helper.VehicleEventHelper.mappingEvents(vehicle.vehicleEvents);
							vehicle.vehicleEvents.forEach((item) =>
							{
								item.geometry = TF.xyToGeometry(item.AdjLongitude, item.AdjLatitude);
							});
						}
					}
				}
			});
		});
	};

	PlaybackTool.prototype.getAndDisplayLiveGPSData = function()
	{
		if (!this.obDate() || moment(this.obDate()).set({ hour: 0, minute: 0, seconds: 0 }).format("L") != moment().currentTimeZoneTime().format("L"))
		{
			return Promise.resolve(true);
		}
		var vehicleIds = [], gpsIds = [], gpsVehicles = {};
		for (var key in this.tripVehicleMapping)
		{
			if (this.tripVehicleMapping[key].Id)
			{
				const vehicle = this.tripVehicleMapping[key];
				vehicleIds.push(vehicle.Id);
				gpsIds.push(vehicle.Gpsid);
				const vehicleKey = TF.Helper.VehicleEventHelper.getKey(vehicle);
				gpsVehicles[vehicleKey] = vehicle;
			}
		}

		if (vehicleIds.length == 0)
		{
			return Promise.resolve(true);
		}
		var time = toISOStringWithoutTimeZone(moment().currentTimeZoneTime());
		var getVehiclePosition = Promise.resolve();
		if (TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled)
		{
			getVehiclePosition = tf.promiseAjax.get(TF.Helper.VehicleEventHelper.getRequestURL(), {
				paramData:
				{
					time: time,
					vehicleIds: vehicleIds,
					databaseId: tf.datasourceManager.databaseId
				}
			}, { overlay: false });
		} else
		{
			getVehiclePosition = tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "busfindergps", "vehiclelastpositions"),
				{
					data: gpsIds,
					paramData: {
						time: time
					}
				},
				{ overlay: false });
		}
		return getVehiclePosition.then((response) =>
		{
			var points = [];
			var paths = [];
			var polylineUniqueValueInfos = {}, pointUniqueValueInfos = {};
			for (var key in gpsVehicles)
			{
				var vehicle = gpsVehicles[key];
				var newData = [];
				if (TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled)
				{
					newData = response.Items.filter(x => x.EventVehicleID == vehicle.Gpsid && x.VendorId == vehicle.VendorId);
				} else
				{
					newData = response.Items.filter(x => x.VehicleExternalID == vehicle.Gpsid);
				}
				TF.Helper.VehicleEventHelper.mappingEvents(newData);
				if (vehicle.vehicleEvents && vehicle.vehicleEvents.length > 0)
				{
					var lastTime = vehicle.vehicleEvents[vehicle.vehicleEvents.length - 1].StartTime;
					newData = newData.filter(x => moment(x.StartTime).isAfter(moment(lastTime)));
				}
				if (newData.length > 0 && this.obVisible())
				{
					vehicle.vehicleEvents = (vehicle.vehicleEvents || []).concat(newData);
					var pathAndPoint = this.createGPSPathAndPoint(vehicle, newData, polylineUniqueValueInfos, pointUniqueValueInfos);
					points = points.concat(pathAndPoint.pointGraphics);
					pathAndPoint.pathGraphic.vehicle = vehicle;
					paths.push(pathAndPoint.pathGraphic);
				}
			}

			// update renderer
			TF.RoutingMap.GPSPalette.gpsMapHelper.mergeRender(this.gpsPolylineLayer, this.gpsPolylineLayer.renderer, Object.values(polylineUniqueValueInfos));
			TF.RoutingMap.GPSPalette.gpsMapHelper.mergeRender(this.gpsPointLayer, this.gpsPointLayer.renderer, Object.values(pointUniqueValueInfos));

			// update paths
			if (paths.length > 0)
			{
				var query = new tf.map.ArcGIS.Query();
				query.outFields = ["*"];
				query.where = "1=1";
				query.returnGeometry = true;
				this.gpsPolylineLayer.queryFeatures(query).then((featureSet) =>
				{
					var adds = paths;
					var updates = [];
					if (featureSet.features.length > 0)
					{
						for (var i = 0; i < featureSet.features.length; i++)
						{
							for (var j = 0; j < adds.length; j++)
							{
								if (adds[j].attributes.id == featureSet.features[i].attributes.id)
								{
									updates.push(featureSet.features[i]);
									featureSet.features[i].geometry = new tf.map.ArcGIS.Polyline({ spatialReference: this.map.mapView.spatialReference, paths: [adds[j].vehicle.vehicleEvents.map(c => [c.AdjLongitude, c.AdjLatitude])] });
									adds.splice(j, 1);
									break;
								}
							}
						}
					}

					if (adds.length > 0 || updates.length > 0)
					{
						var applies = {};
						if (adds.length > 0)
						{
							applies.addFeatures = adds;
						}
						if (updates.length > 0)
						{
							applies.updateFeatures = updates;
						}
						this.gpsPolylineLayer.applyEdits(applies);
						this.drawGPSVehicle();
					}
				});
			}
			// update points
			if (points.length > 0)
			{
				this.gpsPointLayer.applyEdits({
					addFeatures: points
				});
			}
			return true;
		}, () =>
		{
			return false;
		});
	};

	PlaybackTool.prototype.drawGPSData = function()
	{
		this.clearGPSLayer().then(() =>
		{
			var pointGraphics = [];
			var gpsPathGraphics = [];
			var vehicles = [];
			var pointUniqueValueInfos = {};
			var polylineUniqueValueInfos = {};
			for (var key in this.tripVehicleMapping)
			{
				if (this.tripVehicleMapping[key].vehicleEvents)
				{
					vehicles.push(this.tripVehicleMapping[key]);
				}
			}
			vehicles.forEach(vehicle =>
			{
				var events = vehicle.vehicleEvents;
				if (events.length > 0)
				{
					var pathAndPoints = this.createGPSPathAndPoint(vehicle, vehicle.vehicleEvents, polylineUniqueValueInfos, pointUniqueValueInfos);
					gpsPathGraphics.push(pathAndPoints.pathGraphic);
					pointGraphics = pointGraphics.concat(pathAndPoints.pointGraphics);
				}
			});

			setRenderer(this.gpsPolylineLayer, polylineUniqueValueInfos);
			setRenderer(this.gpsPointLayer, pointUniqueValueInfos);
			pointGraphics.length > 0 && this.gpsPointLayer.applyEdits({ addFeatures: pointGraphics });
			gpsPathGraphics.length > 0 && this.gpsPolylineLayer.applyEdits({ addFeatures: gpsPathGraphics });
			this.drawGPSVehicle();
			this.gpsPathArrowLayer.renderer = this.getArrowRender();
			this.drawGPSArrow();
		});

		function setRenderer(layer, values)
		{
			var render = layer.renderer.clone();
			render.uniqueValueInfos = Object.values(values);
			layer.renderer = render;
		}
	};

	PlaybackTool.prototype.createGPSPathAndPoint = function(vehicle, events, polylineUniqueValueInfos, pointUniqueValueInfos)
	{
		var pathGraphic;
		var pointGraphics = [];
		if (events.length > 0)
		{
			var points = Enumerable.From(events)
				.OrderBy((c) => { return c.StartTime; })
				.Select((c) =>
				{
					c.geometry = c.geometry || TF.xyToGeometry(c.AdjLongitude, c.AdjLatitude);
					return {
						event: c,
						geometry: c.geometry
					};
				}).ToArray();
			polylineUniqueValueInfos[vehicle.color] = { value: vehicle.color, symbol: this.symbol.tripPath(vehicle.color) };
			vehicle.path = new tf.map.ArcGIS.Polyline({ spatialReference: this.map.mapView.spatialReference, paths: [points.map(c => [c.geometry.x, c.geometry.y])] });
			pathGraphic = new tf.map.ArcGIS.Graphic({
				geometry: vehicle.path,
				attributes: {
					displayKey: vehicle.color,
					id: vehicle.Id,
				}
			});

			pointGraphics = points.map(c =>
			{
				var isStop = c.EventTypeId == 96;
				var displayKey = vehicle.color + "-" + (isStop ? "stop" : "");
				pointUniqueValueInfos[displayKey] = { value: displayKey, symbol: this.symbol.getEventSymbol(isStop, vehicle.color) };
				return new tf.map.ArcGIS.Graphic({
					geometry: c.geometry,
					attributes: {
						displayKey: displayKey,
					}
				});
			});
		}
		return {
			pathGraphic: pathGraphic,
			pointGraphics: pointGraphics
		};
	};

	PlaybackTool.prototype.drawGPSArrow = function()
	{
		var self = this,
			helper = TF.RoutingMap.MapEditingPalette.MyStreetsHelper;

		if (!this.gpsPathArrowLayer)
		{
			return;
		}
		var oldArrows = [], arrows = [];
		return self.gpsPathArrowLayer.queryFeatures().then(function(featureSet)
		{
			if (featureSet.features.length > 0)
			{
				oldArrows = featureSet.features;
			}
		}).then(() =>
		{
			if (this.gpsPathArrowLayer.minScale > 0 && this.gpsPathArrowLayer.minScale < this.map.mapView.scale)
			{
				return;
			}
			var vehicles = [];
			for (var key in this.tripVehicleMapping)
			{
				if (this.tripVehicleMapping[key].vehicleEvents)
				{
					vehicles.push(this.tripVehicleMapping[key]);
				}
			}
			var onStreet = TF.RoutingMap.BaseMapDataModel.getSettingByKey("arrowPosition", 0) === 0;
			(vehicles || []).forEach(function(vehicle)
			{
				var arrowGraphics = helper.createArrows(self.map, vehicle.path, true, "#FF000", onStreet, true);
				arrowGraphics.forEach(function(arrow)
				{
					arrow.attributes.color = vehicle.color;
				});
				arrows = arrows.concat(arrowGraphics);
			});

			if (arrows && arrows.length > 0)
			{
				if (self.originalArrowOnStreet != onStreet)
				{
					self.gpsPathArrowLayer.renderer = self.getArrowRender();
				}
			}
		}).then(() =>
		{
			if (oldArrows.length > 0 || arrows.length > 0)
			{
				var applies = {};
				if (oldArrows.length > 0)
				{
					applies.deleteFeatures = oldArrows;
				}
				if (arrows.length > 0)
				{
					applies.addFeatures = arrows;
				}
				return self.gpsPathArrowLayer.applyEdits(applies);
			}
		});
	};

	PlaybackTool.prototype.getArrowRender = function()
	{
		var onStreet = TF.RoutingMap.BaseMapDataModel.getSettingByKey("arrowPosition", 0) === 0;
		this.originalArrowOnStreet = onStreet;
		var arrowUniqueValueInfos = {};
		var vehicles = [];
		for (var key in this.tripVehicleMapping)
		{
			if (this.tripVehicleMapping[key].vehicleEvents)
			{
				vehicles.push(this.tripVehicleMapping[key]);
			}
		}

		vehicles.forEach((vehicle) =>
		{
			arrowUniqueValueInfos[vehicle.color] =
			{
				value: vehicle.color,
				symbol: this.originalArrowOnStreet ? this.symbol.arrow(vehicle.color) : this.symbol.arrowOnSide(vehicle.color)
			};
		});

		var arrowRender = {
			type: "unique-value",
			field: "color",
			defaultSymbol: this.symbol.arrow(),
			visualVariables: [{
				type: "rotation",
				field: "angle",
				rotationType: "geographic"
			}],
			uniqueValueInfos: Object.values(arrowUniqueValueInfos)
		};
		return arrowRender;
	};

	PlaybackTool.prototype.drawGPSVehicle = function()
	{
		var vehicles = {};
		for (var key in this.tripVehicleMapping)
		{
			var vehicle = this.tripVehicleMapping[key];
			if (vehicle.vehicleEvents && vehicle.vehicleEvents.length > 0)
			{
				var portion = TF.RoutingMap.GPSPalette.gpsMapHelper.getPortion(vehicle.vehicleEvents, this.tripPlaybackControlTool.tickTool.obTargetTimeAsSecond(), "StartTime");
				this.drawVehicleOnMap(vehicle, portion);
				vehicles[vehicle.Gpsid] = true;
			}
		}
	};

	PlaybackTool.prototype.drawVehicleOnMap = function(vehicle, portion)
	{
		if (!vehicle.vehicleEvents || vehicle.vehicleEvents.length == 0 || !this.obVisible())
		{
			return;
		}
		var currentEvent = vehicle.vehicleEvents[portion.index];
		var nextEvent = portion.index == vehicle.vehicleEvents.length - 1 ? currentEvent : vehicle.vehicleEvents[portion.index + 1];
		var geometry = new tf.map.ArcGIS.Polyline([[currentEvent.geometry.x, currentEvent.geometry.y], [nextEvent.geometry.x, nextEvent.geometry.y]]);
		geometry.spatialReference = this.map.mapView.spatialReference;
		var point = TF.RoutingMap.GPSPalette.gpsMapHelper.getPointAt(geometry, portion.portion);
		var symbol = this.symbol.getVehicleSymbol(point.heading);
		var graphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.gpsVehicleLayer, "key", vehicle.Gpsid);
		if (graphic)
		{
			graphic.geometry = point.geometry;
			graphic.symbol = symbol;
		} else
		{
			graphic = new tf.map.ArcGIS.Graphic({ geometry: point.geometry, symbol, attributes: { key: vehicle.Gpsid } });
			this.gpsVehicleLayer.add(graphic);
		}
		this.displayConnectLine();
		this.displayTimeDifference();
	};

	PlaybackTool.prototype.displayConnectLine = function()
	{
		if (!this.obVisible())
		{
			return;
		}
		if (this.obConnectPlannedVSActual())
		{
			for (var key in this.tripVehicleMapping)
			{
				if (this.tripVehicleMapping[key].Gpsid)
				{
					var vehicle = this.tripVehicleMapping[key];
					var gpsVehicleGraphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.gpsVehicleLayer, "key", vehicle.Gpsid);
					var planVehicleGraphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.tripPlaybackControlTool.vehicleLayer, "id", key);
					var lineGraphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.gpsConnectLineLayer, "key", vehicle.Gpsid);
					if (gpsVehicleGraphic && gpsVehicleGraphic.geometry && planVehicleGraphic && planVehicleGraphic.geometry && planVehicleGraphic.visible)
					{
						var geometry = new tf.map.ArcGIS.Polyline({ spatialReference: this.map.mapView.spatialReference, paths: [[[gpsVehicleGraphic.geometry.x, gpsVehicleGraphic.geometry.y], [planVehicleGraphic.geometry.x, planVehicleGraphic.geometry.y]]] });
						var symbol = {
							type: "simple-line",
							style: "solid",
							color: "#F9B385",
							width: 2
						};

						if (lineGraphic)
						{
							lineGraphic.geometry = geometry;
						} else
						{
							lineGraphic = new tf.map.ArcGIS.Graphic({ geometry, symbol, attributes: { key: vehicle.Gpsid } });
							this.gpsConnectLineLayer.add(lineGraphic);
						}
					}

					if (!planVehicleGraphic)
					{
						this.gpsConnectLineLayer.remove(lineGraphic);
					}
				}
			}
		} else if (this.gpsConnectLineLayer)
		{
			this.gpsConnectLineLayer.removeAll();
		}
	};

	PlaybackTool.prototype.displayTimeDifference = function()
	{
		if (this.obTimeDifference())
		{
			for (var key in this.tripVehicleMapping)
			{
				if (this.tripVehicleMapping[key].Gpsid)
				{
					var vehicle = this.tripVehicleMapping[key];
					var gpsVehicleGraphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.gpsVehicleLayer, "key", vehicle.Gpsid);
					var planVehicleGraphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.tripPlaybackControlTool.vehicleLayer, "id", key);
					var timeDifferenceGraphic = TF.RoutingMap.GPSPalette.gpsMapHelper.getGraphicByKey(this.gpsVehicleLayer, "gpsId", vehicle.Gpsid);
					if (gpsVehicleGraphic && gpsVehicleGraphic.geometry && planVehicleGraphic && planVehicleGraphic.geometry && planVehicleGraphic.visible)
					{
						var trip = this.obTrips().find(x => x.Id == key), sequence = planVehicleGraphic.attributes.sequence;
						var tripStop = trip.TripStops[sequence - 1], nextStop, prevStop;
						if (sequence < trip.TripStops.length)
						{
							nextStop = trip.TripStops[sequence];
						}
						if (sequence > 1)
						{
							prevStop = trip.TripStops[sequence - 2];
						}
						var stops = [prevStop, tripStop, nextStop];
						var supposedSegTime = 0;
						if (nextStop)
						{
							supposedSegTime = this.tripPlaybackControlTool.getTimeAsSecondFromISOString(nextStop.StopTime) - this.tripPlaybackControlTool.getTimeAsSecondFromISOString(tripStop.StopTime);
						}
						else
						{
							supposedSegTime = 0;
						}
						var nearestPoints = stops.map(stop =>
						{
							if (stop && stop.path && stop.path.geometry)
							{
								return tf.map.ArcGIS.geometryEngine.nearestCoordinate(stop.path.geometry, gpsVehicleGraphic.geometry);
							}
							return null;
						});
						var distances = nearestPoints.map(point =>
						{
							if (point)
							{
								return point.distance;
							}
							return 10000;
						});
						var index = distances.indexOf(Math.min(distances[0], distances[1], distances[2]));
						var timeDifference = "Cannot Determine";
						var status = {
							ahead:
							{
								background: "#FFF2F2",
								borderColor: "#CC0000"
							},
							behind:
							{
								background: "#FFFFCC",
								borderColor: "#FFCC33"
							},
							onscheduled:
							{
								background: "#ECFFD9",
								borderColor: "#336600"
							}
						};
						var currentStatus = status.ahead;
						if (distances[index] < 100 && stops[index].path)
						{
							var portion = planVehicleGraphic.attributes.portion;
							var actualPortion = this.getPortionInPath(stops[index].path.geometry, nearestPoints[index].vertexIndex, nearestPoints[index].coordinate);
							if (index == 1)
							{
								timeDifference = supposedSegTime * actualPortion - supposedSegTime * portion;
							} else if (index == 0)
							{
								var actualSegTime = this.tripPlaybackControlTool.getTimeAsSecondFromISOString(tripStop.StopTime) - this.tripPlaybackControlTool.getTimeAsSecondFromISOString(prevStop.StopTime);
								timeDifference = -1 * (supposedSegTime * portion + actualSegTime * (1 - actualPortion));
							} else if (index == 2)
							{
								actualSegTime = this.tripPlaybackControlTool.getTimeAsSecondFromISOString(nextStop.StopTime) - this.tripPlaybackControlTool.getTimeAsSecondFromISOString(tripStop.StopTime);
								timeDifference = supposedSegTime * (1 - portion) + actualSegTime * actualPortion;
							}
						}

						if (timeDifference == 0)
						{
							timeDifference = "On Schedule";
							currentStatus = status.onscheduled;
						} else if ($.isNumeric(timeDifference))
						{
							currentStatus = timeDifference > 0 ? status.ahead : status.behind;
							var timeDifferenceSeconds = Math.floor(Math.abs(timeDifference % 60));
							var timeDifferenceMinutes = Math.floor(Math.abs(timeDifference / 60));
							timeDifference = (timeDifference < 0 ? "- " : "+ ") + (timeDifferenceMinutes > 0 ? timeDifferenceMinutes + " min  " : "") + (timeDifferenceSeconds > 0 ? timeDifferenceSeconds + " sec " : "");
						}

						var symbol = this.symbol.labelSymbol(timeDifference, 120, 20, currentStatus.background, currentStatus.borderColor, -75, 25);
						if (timeDifferenceGraphic)
						{
							timeDifferenceGraphic.geometry = gpsVehicleGraphic.geometry;
							timeDifferenceGraphic.symbol = symbol;
						} else
						{
							timeDifferenceGraphic = new tf.map.ArcGIS.Graphic({ geometry: gpsVehicleGraphic.geometry, symbol, attributes: { gpsId: vehicle.Gpsid, type: "timeDifference" } });
							this.gpsVehicleLayer.add(timeDifferenceGraphic);
						}
					}

					if (!planVehicleGraphic)
					{
						this.gpsVehicleLayer.remove(timeDifferenceGraphic);
					}
				}
			}
		} else if (this.gpsVehicleLayer && this.gpsVehicleLayer.graphics)
		{
			var removes = [];
			var graphics = this.gpsVehicleLayer.graphics.items;
			removes = graphics.filter(c => c.attributes.type == "timeDifference");
			if (removes.length > 0)
			{
				this.gpsVehicleLayer.removeMany(removes);
			}
		}
	};

	PlaybackTool.prototype.getPortionInPath = function(pathGeometry, vertexIndex, point)
	{
		var totalDistance = 0, prevDistance = 0;
		for (var i = 0; i < pathGeometry.paths[0].length - 1; i++)
		{
			var distance = getDistance(pathGeometry.paths[0][i], pathGeometry.paths[0][i + 1]);
			totalDistance += distance;
			if (i < vertexIndex)
			{
				prevDistance += distance;
			}
			if (i == vertexIndex)
			{
				prevDistance += getDistance(pathGeometry.paths[0][i], [point.x, point.y]);
			}
		}
		if (totalDistance == 0)
		{
			return 0;
		}
		return prevDistance / totalDistance;
		function getDistance(point1, point2)
		{
			return Math.sqrt((point2[0] - point1[0]) * (point2[0] - point1[0]) + (point2[1] - point1[1]) * (point2[1] - point1[1]));
		}
	};

	PlaybackTool.prototype.getDate = function()
	{
		return toISOStringWithoutTimeZone(moment(this.obDate()).set(
			{
				hour: 0,
				minute: 0,
				seconds: 0
			}));
	};

	PlaybackTool.prototype.tripTimeRangeClick = function()
	{
		this.obIsTripTimeRange(!this.obIsTripTimeRange());
		if (this.obIsTripTimeRange())
		{
			this.tripPlaybackControlTool.setTimeSliderRange(this.obTrips());
			this.setTimeRangeForGPS();
		} else
		{
			this.tripPlaybackControlTool.tickTool.setTimeSliderRange(0, 24 * 60 * 60 - 1);
		}
	};

	PlaybackTool.prototype.dispose = function()
	{
		this._hide();
		ko.cleanNode(this.playbackContainer[0]);
		this.playbackContainer && this.playbackContainer.remove();
		this.obTripsSubscribe.dispose();
		clearTimeout(this.onProcessTimeout);
		tfdispose(this);
	};
})();

function getContentHtml(self)
{
	var keepCenterHtml = `<li data-bind="click:tripPlaybackControlTool.centerLockedClick.bind(tripPlaybackControlTool)">
							<div data-bind="css:{'check-white':tripPlaybackControlTool.obIsCenterLocked()}" class="menuIcon check-white"></div>
							<div class="text"><span>Keep Center</span></div>
						</li>`;
	var playSpeedHtml = `<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 1)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==1}" class="menuIcon check-white"></div>
										<div class="text"><span>Normal Speed</span></div>
									</li>
									<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 2)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==2}" class="menuIcon"></div>
										<div class="text"><span>2x</span></div>
									</li>
									<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 10)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==10}" class="menuIcon"></div>
										<div class="text"><span>10x</span></div>
									</li>
									<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 20)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==20}" class="menuIcon"></div>
										<div class="text"><span>20x</span></div>
									</li>
									<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 30)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==30}" class="menuIcon"></div>
										<div class="text"><span>30x</span></div>
									</li>
									<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 60)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==60}" class="menuIcon"></div>
										<div class="text"><span>60x</span></div>
									</li>
									<li data-bind="click:tripPlaybackControlTool.changeSpeed.bind($data, 120)">
										<div data-bind="css:{' check-white':tripPlaybackControlTool.tickTool.obPlaySpeed()==120}" class="menuIcon"></div>
										<div class="text"><span>120x</span></div>
									</li>`;
	var gpsMenu = `	<li>
							<div class="menuIcon"></div>
							<div class="text"><span data-bind="text:'Playback Speed ('+tripPlaybackControlTool.tickTool.playSpeedText()+ ')'"></span><span class="k-icon k-i-arrow-e"></span></div>
							<div>
								<ul class="sub-menu sub-context-menu sub-context-menu-contain-icon">
									${playSpeedHtml}
								</ul>
							</div>
						</li>

						<li data-bind="click:()=>{obConnectPlannedVSActual(!obConnectPlannedVSActual())}">
							<div data-bind="css:{' check-white':obConnectPlannedVSActual}" class="menuIcon"></div>
							<div class="text"><span>Connect Planned vs. Actual</span></div>
						</li>
						<li data-bind="click:()=>{obTimeDifference(!obTimeDifference())}">
							<div data-bind="css:{' check-white':obTimeDifference}" class="menuIcon"></div>
							<div class="text"><span>Time Difference</span></div>
						</li>`;
	var menuHtml = tf.authManager.hasGPS() && tf.authManager.GPSConnectValid ? gpsMenu : playSpeedHtml;
	return `<div class="playback-tool" style="width:100%;height:0;position:absolute;display:flex;align-items:flex-end;justify-content:center;bottom:20px;">
	<div style="width:${self.isDetailView ? 95 : 70}%;max-width:600px;background-color:rgba(255,255,255,0.6);position:relative;padding-top:10px;">
	<div class="glyphicon glyphicon-remove" data-bind="click:toggleDisplay.bind($data)" style="position:absolute;right:5px;top:5px;cursor:pointer;"></div>
<div class="playback-control-container" style="background-color:transparent;padding:15px ${self.isDetailView ? 20 : 30}px;border-bottom:none;">
    <div class="time-slider-container">
        <input type="text" data-slider-tooltip="hide" data-bind="bootstrapSlider:{ max:tripPlaybackControlTool.tickTool.obTimeSliderMax,min:tripPlaybackControlTool.tickTool.obTimeSliderMin,value:tripPlaybackControlTool.tickTool.obTargetTimeAsSecond,formatter:tripPlaybackControlTool.tickTool.secondToTimeFormatter}">
    </div>
    <div class="controls" style="margin:5px -6px 0 -6px;">
        <div class="icon">
			<i class="glyphicon glyphicon-time" style="margin-right:5px;" data-bind="click:playNowButtonClick"></i>
			<i class="glyphicon" data-bind="click:tripPlaybackControlTool.playButtonClick,css:{'glyphicon-play':!tripPlaybackControlTool.tickTool.obPlaying(),'glyphicon-pause':tripPlaybackControlTool.tickTool.obPlaying()}"></i>
		</div>
        <div class="time grid-map" data-bind="css:{'has-gps': hasGPS},click:timeClick">
			<!-- ko if:!hasGPS-->
			<div class="date-text" data-bind="text:date"></div>
			<!-- /ko -->
			<!-- ko if:hasGPS-->
			<!-- ko customInput:
				{type:"Date", value:obDate,attributes:{class:"form-control datepicker-bottom input-datepicker-no-border", name:"Date", autocomplete: 'off', noIcon: true, adjustPopupPosition: adjustPopupPosition, format: 'MMM DD, YYYY'}} -->
			<!-- /ko -->
			<!-- /ko -->
			<div data-bind="text:tripPlaybackControlTool.tickTool.secondToTimeFormatter(tripPlaybackControlTool.tickTool.obTargetTimeAsSecond())" style="white-space: nowrap;"></div>
		</div>
        <div class="controls-right">
			<div class="print-setting-group" data-bind="dropDownMenu">
				<div class="icon"><i class="glyphicon glyphicon-cog"></i></div>
				<div class="menu map-pinpoint-white withsub-menu">
					<ul>
					${self.isDetailView ? keepCenterHtml : ""}
					<li data-bind="click:tripTimeRangeClick">
                        <div data-bind="css:{'check-white':obIsTripTimeRange()}" class="menuIcon check-white"></div>
                        <div class="text"><span>${self.routingMapTool.routingMapDocumentViewModel.type == "route" ? "Route Time Range" : "Trip Time Range"}</span></div>
                    </li>
					<li class="menu-divider">
						<div class="rule"></div>
					</li>
						${menuHtml}
					</ul>
				</div>
			</div>
		</div>
    </div>
</div>
    </div>
</div>
`;
}