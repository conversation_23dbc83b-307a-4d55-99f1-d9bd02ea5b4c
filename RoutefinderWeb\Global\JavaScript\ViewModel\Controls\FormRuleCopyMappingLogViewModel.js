﻿(function()
{
	createNamespace('TF.Control').FormRuleCopyMappingLogViewModel = FormRuleCopyMappingLogViewModel;

	function FormRuleCopyMappingLogViewModel(options)
	{
		this.options = options;
		this.obLog = ko.observable(this.getLogMessage());
	}

	FormRuleCopyMappingLogViewModel.prototype.getLogMessage = function()
	{
		let copyMappingHelper = new TF.Form.FormRuleCopyMappingHelper();
		let rawLog = this.options.rawLog;
		let dataSource = this.options.dataSource;

		return copyMappingHelper.buildCopyLogSummary(rawLog, dataSource);
	}
})();

