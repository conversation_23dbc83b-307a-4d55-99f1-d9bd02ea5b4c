(function()
{
	var namespace = createNamespace("TF.Control");

	createNamespace('TF.Control').KendoListMoverWithDropdownListControlViewModel = KendoListMoverWithDropdownListControlViewModel;

	KendoListMoverWithDropdownListControlViewModel.prototype = Object.create(TF.Control.KendoListMoverControlViewModel.prototype);
	KendoListMoverWithDropdownListControlViewModel.prototype.constructor = KendoListMoverWithDropdownListControlViewModel;

	function KendoListMoverWithDropdownListControlViewModel(allItems, selectedItems, options, selectedPrimaryStaffTypeId, ModalName)
	{
		//this.options = $.extend({}, KendoListMoverWithDropdownListControlViewModel.defaults, options);
		this.options = $.extend({}, KendoListMoverWithDropdownListControlViewModel.defaults, options);
		TF.Control.KendoListMoverControlViewModel.call(this, allItems, selectedItems, this.options, ModalName);

		this.obSelectedPrimaryStaffTypeId = ko.observable();
		this.selectedPrimaryStaffTypeId = selectedPrimaryStaffTypeId;
		this.obPrimaryStaffTypeDataModels = ko.observableArray();

		this.selectItemChange.subscribe(this._selectStaffTypeChangeReceive.bind(this));

		// this.obErrorMessageDivIsShow = ko.observable(false);
		// this.obValidationErrors = ko.observableArray([]);

		// this.obErrorMessageTitle = ko.observable("Error Occurred");
		// this.obErrorMessageDescription = ko.observable("The following error occurred.");

		//drop down list
		this.obSelectedPrimaryStaffType = ko.observable();
		this.obSelectedPrimaryStaffType.subscribe(this.setPrimaryStaffTypeValue, this);
		this.obSelectedPrimaryStaffTypeText = ko.computed(this.setSelectedPrimaryStaffTypeTextComputer("primaryStaffType"), this);

		this.obPrimaryStaffTypeDataModels(this._getSelectedItems());
		this.obSelectedPrimaryStaffTypeId(this.selectedPrimaryStaffTypeId);
	}

	KendoListMoverWithDropdownListControlViewModel.prototype.getSelectDataCount = function()
	{
		return this._getSelectedItems().length;
	}

	KendoListMoverWithDropdownListControlViewModel.prototype.setPrimaryStaffTypeValue = function()
	{
		this.obSelectedPrimaryStaffTypeId(this.obSelectedPrimaryStaffType() ? this.obSelectedPrimaryStaffType().StaffTypeId : undefined);
	};

	KendoListMoverWithDropdownListControlViewModel.prototype.setSelectedPrimaryStaffTypeTextComputer = function(field)
	{
		return function()
		{
			var item = Enumerable.From(this.obPrimaryStaffTypeDataModels()).Where(function(c)
			{
				return c.StaffTypeId === this.obSelectedPrimaryStaffTypeId();
			}.bind(this)).ToArray()[0];
			return item ? item.StaffTypeName : "";
		}.bind(this);
	};

	KendoListMoverWithDropdownListControlViewModel.prototype.apply = function()
	{
		return new Promise(function(resolve, reject)
		{
			var selectedItems = this._getSelectedItems();

			if (this.options.mustSelect)
			{
				return this.pageLevelViewModel.saveValidate().then(function(result)
				{
					if (result)
					{
						resolve({ list: selectedItems, selected: this.obSelectedPrimaryStaffTypeId() });
					}
					else
					{
						reject();
					}
				}.bind(this));
			}
			else
			{
				resolve({ list: selectedItems, selected: this.obSelectedPrimaryStaffTypeId() });
			}
		}.bind(this));
	};

	KendoListMoverWithDropdownListControlViewModel.prototype._selectStaffTypeChangeReceive = function()
	{
		var selectedItems = this._getSelectedItems();
		this.obPrimaryStaffTypeDataModels(selectedItems);
		if (!this.obSelectedPrimaryStaffTypeId())
			this.obSelectedPrimaryStaffTypeId(this.obPrimaryStaffTypeDataModels()[0] ? this.obPrimaryStaffTypeDataModels()[0].StaffTypeId : undefined);
	};

	KendoListMoverWithDropdownListControlViewModel.prototype._getSelectedItems = function()
	{
		return this._getResult().map(function(item) { return item; });
	};

	KendoListMoverWithDropdownListControlViewModel.defaults = {
		formatter: function(obj) { return obj.toString(); },
		mustSelect: false
	};
})();
