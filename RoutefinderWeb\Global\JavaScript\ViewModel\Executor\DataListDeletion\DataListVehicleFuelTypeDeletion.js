﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleFuelTypeDeletion = DataListVehicleFuelTypeDeletion;

	function DataListVehicleFuelTypeDeletion()
	{
		this.type = 'vehiclefueltype';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Fuel Type';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleFuelTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleFuelTypeDeletion.prototype.constructor = DataListVehicleFuelTypeDeletion;

	DataListVehicleFuelTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?FuelTypeId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleFuelTypeDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclefueltype", pb.DELETE), ids);
	}

	DataListVehicleFuelTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();