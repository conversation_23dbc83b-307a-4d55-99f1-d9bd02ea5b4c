﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleBrakeTypeDeletion = VehicleBrakeTypeDeletion;

	function VehicleBrakeTypeDeletion()
	{
		this.type = 'vehiclebraketype';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleBrakeTypeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleBrakeTypeDeletion.prototype.constructor = VehicleBrakeTypeDeletion;

	VehicleBrakeTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();