(function()
{
	createNamespace("TF.Modal.ResourceScheduler").ConfigureTripDisplaySettingModalViewModel = ConfigureTripDisplaySettingModalViewModel;

	function ConfigureTripDisplaySettingModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Display Options");
		this.sizeCss = "modal-dialog-lg";
		this.contentTemplate('modal/resourcescheduler/configuretripdisplaysetting');
		this.buttonTemplate('modal/positivenegativeother');
		this.obPositiveButtonLabel("Apply");
		this.obNegativeButtonLabel("Cancel");
		this.obOtherButtonLabel("Reset");
		this.configureTripDisplaySettingViewModel = new TF.Control.ResourceScheduler.ConfigureTripDisplaySettingViewModel(this.shortCutKeyHashMapKeyName, this.obDisableControl);
		this.data(this.configureTripDisplaySettingViewModel);
	}

	ConfigureTripDisplaySettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ConfigureTripDisplaySettingModalViewModel.prototype.constructor = ConfigureTripDisplaySettingModalViewModel;

	ConfigureTripDisplaySettingModalViewModel.prototype.positiveClick = function()
	{
		return this.configureTripDisplaySettingViewModel.apply().then(result => result && this.positiveClose(result));
	}

	ConfigureTripDisplaySettingModalViewModel.prototype.otherClick = function(viewModel, e)
	{
		return this.configureTripDisplaySettingViewModel.reset();
	}
})();
