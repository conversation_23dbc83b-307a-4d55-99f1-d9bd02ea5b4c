(function()
{
	createNamespace("TF.ImportAndMergeData").ImportAndMergeDataWizard = ImportAndMergeDataWizard;

	function ImportAndMergeDataWizard(model)
	{
		TF.Control.BaseWizardViewModel.call(this);
		this.name("Import/Merge Data");
		this.data(new TF.ImportAndMergeData.ImportAndMergeDataModel());
		this.model = model;
		this.canSaveTemplate = ko.pureComputed(function()
		{
			var currentStep = this.currentStep() || {};
			return !!currentStep.saveTemplate;
		}.bind(this));

		this.importing = ko.pureComputed(function()
		{
			var currentStep = this.currentStep() || {};
			return !!currentStep.importing && currentStep.importing();
		}.bind(this));

		this.imported = ko.pureComputed(function()
		{
			var currentStep = this.currentStep() || {};
			return !!currentStep.imported && currentStep.imported();
		}.bind(this));

		this.nextButtonLabel = ko.pureComputed(function()
		{
			var currentStep = this.currentStep() || {};
			return currentStep.nextButtonLabel || "Next";
		}.bind(this));

		this.obNextButtonDisable = ko.pureComputed(function()
		{
			var currentStep = this.currentStep() || {};

			return currentStep.obHasAnyMatchOn && !currentStep.obHasAnyMatchOn();
		}.bind(this));
	}

	ImportAndMergeDataWizard.prototype = Object.create(TF.Control.BaseWizardViewModel.prototype);
	ImportAndMergeDataWizard.prototype.constructor = ImportAndMergeDataWizard;

	ImportAndMergeDataWizard.prototype.saveTemplate = function()
	{
		if (!this.canSaveTemplate()) return;

		this.currentStep().saveTemplate();
	};

	ImportAndMergeDataWizard.prototype.getNextStepType = function()
	{
		var currentStep = this.currentStep(), data = this.data();
		if (!currentStep)
		{
			return TF.ImportAndMergeData.ChooseTypeStep;
		}

		if (currentStep instanceof TF.ImportAndMergeData.ChooseTypeStep)
		{
			switch (data.type())
			{
				case TF.ImportAndMergeData.ImportAndMergeDataType.PredefinedImport:
					return TF.ImportAndMergeData.PerformArchiveStep;
				case TF.ImportAndMergeData.ImportAndMergeDataType.TransfinderDataSource:
					return TF.ImportAndMergeData.ImportStep;
				case TF.ImportAndMergeData.ImportAndMergeDataType.ExternalSource:
					return TF.ImportAndMergeData.PerformArchiveStep;
			}
		}

		if (currentStep instanceof TF.ImportAndMergeData.PerformArchiveStep)
		{
			switch (data.type())
			{
				case TF.ImportAndMergeData.ImportAndMergeDataType.PredefinedImport:
					return null;

				case TF.ImportAndMergeData.ImportAndMergeDataType.ExternalSource:
					return TF.ImportAndMergeData.SelectDataTypeStep;
			}
		}

		if (currentStep instanceof TF.ImportAndMergeData.SelectDataTypeStep)
		{
			return TF.ImportAndMergeData.SelectProcessingOptionsStep;
		}

		if (currentStep instanceof TF.ImportAndMergeData.SelectProcessingOptionsStep)
		{
			return TF.ImportAndMergeData.PreVerificationStep;
		}

		if (currentStep instanceof TF.ImportAndMergeData.PreVerificationStep)
		{
			return TF.ImportAndMergeData.ImportStep;
		}
	};

	ImportAndMergeDataWizard.prototype.apply = function()
	{
		return TF.Control.BaseWizardViewModel.prototype.apply.call(this).then(baseResult =>
		{
			if (!baseResult)
			{
				return null;
			}

			let currentStep = this.currentStep();
			if (!(currentStep instanceof TF.ImportAndMergeData.PerformArchiveStep))
			{
				return null;
			}

			let data = this.data();
			if (data.type() == TF.ImportAndMergeData.ImportAndMergeDataType.PredefinedImport)
			{
				tf.modalManager.showModal(new TF.Modal.StudentImportOptionsModalViewModel(data.predefinedData())).then(result =>
				{
					this.showImportResultModal(result);
				});

				return { close: true };
			}

			return null;
		});
	};

	ImportAndMergeDataWizard.prototype.showImportResultModal = function(result)
	{
		if (result && result.importStatus)
		{
			tf.modalManager.showModal(new TF.Modal.ImportStatusModalViewModel(result.importStatus));
		}
	}

	ImportAndMergeDataWizard.createImportOperation = function(importDataOptions)
	{
		var progressStatus = { Percentage: 0, Message: 'Importing Data...' };
		TF.updateProcessStatus(progressStatus);
		tf.loadingIndicator.show(true);

		//verify the token before import to ensure this token will not be expired between this import
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'tokens'), {
			paramData: {
				verify: true
			}
		});
		return tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportOperationsUrl(), {
			data: importDataOptions
		}, { overlay: false }).then(function(response)
		{
			var operation = response.Items[0];
			return { operationId: operation.Id, operationOptions: importDataOptions };
		}).catch(function(response)
		{
			tf.loadingIndicator.tryHide();
			return TF.showErrorMessageBox(response).then(function()
			{
				return null;
			});
		});
	};
})();