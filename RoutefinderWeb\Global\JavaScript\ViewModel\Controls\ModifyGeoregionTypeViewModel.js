﻿(function()
{
	createNamespace('TF.Control').ModifyGeoregionTypeViewModel = ModifyGeoregionTypeViewModel;

	ModifyGeoregionTypeViewModel.prototype = Object.create(TF.Control.BaseControl.prototype);
	ModifyGeoregionTypeViewModel.prototype.constructor = ModifyGeoregionTypeViewModel;

	function ModifyGeoregionTypeViewModel(georegionTypeId)
	{
		this._isSaving = false;
		this.allUnits = ["meters", "feet", "kilometers", "miles", "yards"];
		this.obEntityDataModel = ko.observable(new TF.DataModel.GeoregionTypeDataModel());

		this.obDistanceDisable = ko.observable(false);
		this.obBufferDisable = ko.observable(false);
		ko.computed(this.changeBoundaryComputer.bind(this));
		this.obEntityDataModel().id(georegionTypeId);

		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obSuccessMessageDivIsShow = ko.observable(false);// useless for now, may use later

		this.obErrorMessageTitle = ko.observable("Error Occurred");
		this.obErrorMessageDescription = ko.observable("The following error occurred.");
		this.obValidationErrors = ko.observableArray([]);

		const unit = tf.measurementUnitConverter.isImperial() ? 'feet' : 'meters';
		this.obEntityDataModel().distanceUnits(unit);
		this.obEntityDataModel().bufferUnits(unit);
		this.obSymbolContent = ko.observable("");
		this.symbolHelper = new TF.Map.Symbol();
		this.defaultSymbolSetting = this.getDefaultSymbolSetting();
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	ModifyGeoregionTypeViewModel.prototype.changeBoundaryComputer = function()
	{
		if (this.obEntityDataModel())
		{
			var $svg = $("#drawingSymbolSample");
			var $svgPath = $svg.find(".path");
			var $svgCircle = $svg.find("circle");

			this.obDistanceDisable(false);
			this.obBufferDisable(false);

			if (this.obEntityDataModel().boundary() == "Street Path")
			{
				if (this.boundaryValueDic)
				{
					this.obEntityDataModel().distance(this.boundaryValueDic.distance);
					this.obEntityDataModel().distanceUnits(this.boundaryValueDic.distanceUnits);
					this.obEntityDataModel().buffer(this.boundaryValueDic.buffer);
					this.obEntityDataModel().bufferUnits(this.boundaryValueDic.bufferUnits);
				}
				$svgPath.show();
				$svgCircle.hide();
			}
			else if (this.obEntityDataModel().boundary() == "Radius")
			{
				if (this.boundaryValueDic)
				{
					this.obEntityDataModel().distance(this.boundaryValueDic.distance);
					this.obEntityDataModel().distanceUnits(this.boundaryValueDic.distanceUnits);
					this.obEntityDataModel().buffer(null);
					this.obEntityDataModel().bufferUnits(null);
				}
				this.bootstrapValidator && this.bootstrapValidator.updateStatus("buffer", "NOT_VALIDATED");
				this.obBufferDisable(true);
				$svgPath.hide();
				$svgCircle.show();
			}
			else if (this.obEntityDataModel().boundary() == "User Defined")
			{
				if (this.boundaryValueDic)
				{
					this.obEntityDataModel().distance(null);
					this.obEntityDataModel().distanceUnits(null);
					this.obEntityDataModel().buffer(null);
					this.obEntityDataModel().bufferUnits(null);
				}
				this.bootstrapValidator && this.bootstrapValidator.updateStatus("distance", "NOT_VALIDATED");
				this.bootstrapValidator && this.bootstrapValidator.updateStatus("buffer", "NOT_VALIDATED");
				this.obDistanceDisable(true);
				this.obBufferDisable(true);
			}
		}
	};

	ModifyGeoregionTypeViewModel.prototype.symbolSettingClick = function()
	{
		let displaySetting = this.getSymbolSetting(),
			displayDetail = {
				symbol: displaySetting.symbol,
				size: displaySetting.size,
				color: displaySetting.color,
				name: "Georegion Type Symbol",
				borderishow: displaySetting.borderishow,
				bordersize: displaySetting.bordersize,
				bordercolor: displaySetting.bordercolor,
				defaultSymbol: this.getDefaultSymbolString()
			};
		tf.modalManager.showModal(new TF.Modal.AdjustValueDisplayModalViewModel(displayDetail, true)).then(result =>
		{
			if (result && result.changed)
			{
				this.obSymbolContent(this.GetSymbolString(result));
				this.obEntityDataModel().symbolSetting(JSON.stringify(result));
			}
		});
	}

	ModifyGeoregionTypeViewModel.prototype.getSymbolSetting = function()
	{
		let displaySetting = this.defaultSymbolSetting;
		let symbolSetting = this.obEntityDataModel().symbolSetting();
		if (symbolSetting)
		{
			displaySetting = JSON.parse(symbolSetting);
			if (displaySetting.symbol == "-1")
			{
				displaySetting.symbol = "default";
			}
		}
		return displaySetting;
	}

	ModifyGeoregionTypeViewModel.prototype.init = function(viewModel, el)
	{
		this._$form = $(el);
		setTimeout(function()
		{
			this.initValid();
			this.load().then(() =>
			{
				this.initSymbol();
				this.monitorBoundaryType();
			});
		}.bind(this), 0);
	};

	ModifyGeoregionTypeViewModel.prototype.initSymbol = function()
	{
		let displaySetting = this.getSymbolSetting();
		this.obSymbolContent(this.GetSymbolString(displaySetting));
	}

	ModifyGeoregionTypeViewModel.prototype.GetSymbolString = function(setting)
	{
		var borderColor, borderSize, maxdisplaySize = 24;
		if (setting.symbol === "default")
		{
			return this.getDefaultSymbolString();
		}

		if (setting.borderishow)
		{
			borderColor = setting.bordercolor;
			borderSize = setting.bordersize;
		}
		var symbolString = TF.Helper.AdjustValueSymbolHelper.getSVGSymbolString(setting.symbol, setting.color, setting.size, borderColor, borderSize, maxdisplaySize);
		symbolString = symbolString.replace("<svg", "<svg class='display'");
		return symbolString;
	}

	ModifyGeoregionTypeViewModel.prototype.getDefaultSymbolSetting = function()
	{
		return {
			symbol: "default",
			size: "6",
			color: "#1259d0",
			borderishow: false,
			bordersize: "1",
			bordercolor: "#000000"
		};
	}

	ModifyGeoregionTypeViewModel.prototype.getDefaultSymbolString = function()
	{
		const symbol = this.symbolHelper.getUnassignedStudentSymbol();
		let $symbol = $(TF.Map.Symbol.circleTemplate.split("{color}").join(symbol.color).replace("<svg", "<svg class='display'"));
		$symbol.attr({ width: this.defaultSymbolSetting.size, height: this.defaultSymbolSetting.size });
		return $('<div>').append($symbol).html();
	}

	ModifyGeoregionTypeViewModel.prototype.initValid = function()
	{
		var validatorFields = {}, isValidating = false, self = this;

		this._$form.find("input[required]").each(function(n, field)
		{
			var name = $(field).attr("name");
			validatorFields[name] = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "Name is required"
					},
					callback: {
						message: "Name must be unique",
						callback: function(value)
						{
							if (value == "")
							{
								return true;
							}

							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "georegiontypes"), null, { overlay: false })
								.then(function(apiResponse)
								{
									return !apiResponse.Items.some(function(item)
									{
										return item.Name.toLowerCase() === value.toLowerCase() && item.Id !== this.obEntityDataModel().id();
									}.bind(this));
								}.bind(this))
								.catch(function()
								{
									return true;
								});
						}.bind(this)
					}
				}
			};
		}.bind(this));

		validatorFields["distance"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Distance is required"
				},
				greaterThan: {
					value: 0,
					message: "Distance must be > 0",
					inclusive: false
				},
			}
		};

		validatorFields["buffer"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Buffer is required"
				},
				greaterThan: {
					value: 0,
					message: "Buffer must be > 0",
					inclusive: false
				}
			}
		};

		validatorFields["boundaryThickness"] = {
			trigger: "blur change",
			validators: {
				greaterThan: {
					value: 0,
					message: "Thickness must be >=1"
				},
				lessThan: {
					value: 7,
					message: "Thickness must be <=7"
				}
			}
		};

		this._$form.bootstrapValidator({
			excluded: [':hidden', ':not(:visible)', ":disabled"],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			var $parent = data.element.closest('.form-group');
			$parent.removeClass('has-success');
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.bootstrapValidator = this._$form.data("bootstrapValidator");
		this.pageLevelViewModel.load(this.bootstrapValidator);
	};

	ModifyGeoregionTypeViewModel.prototype.load = function()
	{
		let p = Promise.resolve();
		if (this.obEntityDataModel().id())
		{
			p = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "georegiontypes?Id=" + this.obEntityDataModel().id()))
				.then(function(data)
				{
					let dataModel = data && data.Items && data.Items[0];
					if (dataModel.BufferUnits)
					{
						dataModel.BufferUnits = dataModel.BufferUnits.replace(/ft/g, 'feet').replace(/mi$/g, 'miles');
						dataModel.DistanceUnits = dataModel.DistanceUnits.replace(/ft/g, 'feet').replace(/mi$/g, 'miles');
					}
					this.obEntityDataModel(new TF.DataModel.GeoregionTypeDataModel(dataModel));
					this.originalName = dataModel.Name;
					this.obEntityDataModel().apiIsDirty(false);
				}.bind(this));
		}

		this._$form.find("input[data-role=size]").on("keypress keyup blur", function(event)
		{
			var key = event.which || event.keyCode || 0;
			if ((key < 48 || key > 57)
				&& key != 37 // left
				&& key != 39 // right
				&& key != 8 // backspace
				&& key != 9 // tab
				&& key != 46 // Delete
			)
			{
				event.preventDefault();
			}
		});

		this.obEntityDataModel().apiIsDirty(false);

		return p;
	};

	ModifyGeoregionTypeViewModel.prototype.monitorBoundaryType = function()
	{
		const unit = tf.measurementUnitConverter.isImperial() ? 'feet' : 'meters';

		this.boundaryValueDic = {
			distance: this.obEntityDataModel().distance() || 0,
			distanceUnits: this.obEntityDataModel().distanceUnits() || unit,
			buffer: this.obEntityDataModel().buffer() || 0,
			bufferUnits: this.obEntityDataModel().bufferUnits() || unit,
		}

		this.obEntityDataModel().boundary.subscribe(function(value)
		{
			switch (value)
			{
				case "Street Path":
					this.boundaryValueDic.distance = this.obEntityDataModel().distance();
					this.boundaryValueDic.distanceUnits = this.obEntityDataModel().distanceUnits();
					this.boundaryValueDic.buffer = this.obEntityDataModel().buffer();
					this.boundaryValueDic.bufferUnits = this.obEntityDataModel().bufferUnits();
					break;
				case "Radius":
					this.boundaryValueDic.distance = this.obEntityDataModel().distance();
					this.boundaryValueDic.distanceUnits = this.obEntityDataModel().distanceUnits();
					break;
				default:
					break;
			}
		}, this, "beforeChange");
	};

	ModifyGeoregionTypeViewModel.prototype.save = function()
	{
		return this.saveValidate()
			.then((result) =>
			{
				if (!result)
				{
					return result;
				}

				return TF.DetailView.UserDefinedFieldHelper.checkInBoundarySetUsage(TF.Enums.UDFBoundarySetType.GeoRegionType, this.originalName, this.obEntityDataModel().name());
			})
			.then(function(result)
			{
				if (!!result)
				{
					if (this._isSaving)
					{
						return Promise.resolve(false);
					}

					this._isSaving = true;
					var obEntityDataModel = this.obEntityDataModel();
					var isNew = obEntityDataModel.id() ? false : true;

					return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefix(), "georegiontypes"),
						{
							data: [obEntityDataModel.toData()]
						})
						.then(function(data)
						{
							!isNew && TF.DetailView.UserDefinedFieldHelper.refreshInBoundarySetValueByType(TF.Enums.UDFBoundarySetType.GeoRegionType, obEntityDataModel.name());
							this._isSaving = false;
							obEntityDataModel.update(data.Items[0]);
							PubSub.publish(topicCombine(pb.DATA_CHANGE, "georegiontype", pb.EDIT), obEntityDataModel.id());
							return obEntityDataModel.toData();
						}.bind(this))
						.catch(function(data)
						{
							this._isSaving = false;
							this.obErrorMessageDivIsShow(true);
							this.obValidationErrors([{ name: "error", message: data.Message }]);
							throw data;
						}.bind(this));
				}
			}.bind(this));
	};

	ModifyGeoregionTypeViewModel.prototype.saveValidate = function()
	{
		return this.pageLevelViewModel.saveValidate(null, { hideToast: true });
	};

	ModifyGeoregionTypeViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(georegionTypeDataModel)
			{
				return georegionTypeDataModel;
			});
	};

	ModifyGeoregionTypeViewModel.prototype.tryClose = function()
	{
		return this.pendingSave().then(function(result)
		{
			if (result)
			{
				return tf.promiseBootbox.yesNo({ message: "You have unsaved changes.  Would you like to save your changes prior to closing?", backdrop: true, title: "Unsaved Changes", closeButton: true })
					.then(function(result)
					{
						if (result == true)
						{
							return this.save();
						}
						if (result == false)
						{
							return Promise.resolve(false);
						}
					}.bind(this));
			}
			return Promise.resolve(false);
		}.bind(this));
	};

	ModifyGeoregionTypeViewModel.prototype.pendingSave = function()
	{
		if (this.obEntityDataModel())
		{
			return Promise.resolve(this.obEntityDataModel().apiIsDirty());
		}
		return Promise.resolve(false);
	};

	ModifyGeoregionTypeViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};
})();