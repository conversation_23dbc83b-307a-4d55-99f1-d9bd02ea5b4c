﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.GeoregionDeletion = GeoregionDeletion;

	function GeoregionDeletion()
	{
		this.type = 'georegion';
		namespace.BaseDeletion.apply(this, arguments);
	}

	GeoregionDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	GeoregionDeletion.prototype.constructor = GeoregionDeletion;

	GeoregionDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}

	GeoregionDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	GeoregionDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		return Promise.all([p0]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};
})();