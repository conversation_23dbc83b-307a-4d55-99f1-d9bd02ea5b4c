﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.LogiReportDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	}

	namespace.LogiReportDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.LogiReportDataModel.prototype.constructor = namespace.LogiReportDataModel;

	namespace.LogiReportDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "DataTypeId", default: null },
		{ from: "ReportDataSchemaID", default: null },
		{ from: "Type", default: null },
		{ from: "Path", default: null },
		{ from: "CreatedOn", default: "1970-01-01T00:00:00" },
		{ from: "CreatedBy", default: 0 },
		{ from: "IsSystem", default: 0 }
	];
})();
