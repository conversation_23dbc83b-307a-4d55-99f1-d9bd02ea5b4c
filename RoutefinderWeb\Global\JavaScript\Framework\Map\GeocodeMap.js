﻿(function()
{
	var namespace = createNamespace("TF.Map");

	namespace.GeocodeMap = GeocodeMap;

	function GeocodeMap()
	{
		namespace.StandardMap.call(this);

		this.tolerance = 100;
		this.geocodingServiceUrl = "http://geocode.arcgis.com/arcgis/rest/services/World/GeocodeServer/";

		this.urlPrefix = "geocode.arcgis.com";
		this.proxyUrl = "http://localhost/proxy/DotNet/proxy.ashx"
		this.proxyRuleNumber = null;

		this.locator = null;
	};

	GeocodeMap.prototype = Object.create(namespace.StandardMap.prototype);

	GeocodeMap.prototype.createMap = function()
	{
		namespace.StandardMap.prototype.createMap.apply(this);
	};

	GeocodeMap.prototype.setProxy = function()
	{
		this.proxyRuleNumber = this.ArcGIS.urlUtils.addProxyRule({
			urlPrefix: this.urlPrefix,
			proxyUrl: this.proxyUrl
		});
	};

	GeocodeMap.prototype.geocoding = function(address, callback)
	{
		this.proxyRuleNumber == null ? this.setProxy() : null;

		//this.locator.outSpatialReference = this.map.spatialReference;  // 3857
		this.ArcGIS.locator.addressToLocations(this.geocodingServiceUrl, { address: address, outSpatialReference: new this.ArcGIS.SpatialReference({ wkid: 4326 }) }).then(results => 
		{
			if (results && results.addresses.length > 0)
			{
				callback(results.addresses[0].location);
			}
		}).catch(error => 
		{
			console.error(results);
		});
	};

	GeocodeMap.prototype.reverseGeocoding = function(mapPoint, callback)
	{
		console.log('reverseGeocoding not implemented...')

		//this.proxyRuleNumber == null ? this.setProxy() : null;
	};

	GeocodeMap.prototype.reverseGeocoding = function(longitude, latitude, callback)
	{
		var mapPoint = [longitude, latitude];
		this.reverseGeocoding(mapPoint, callback);
	};


})();