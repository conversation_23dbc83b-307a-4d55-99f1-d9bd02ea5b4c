(function()
{
	createNamespace("TF.Modal.Grid").SaveMergeDocumentNameModalViewModel = SaveMergeDocumentNameModalViewModel;

	function SaveMergeDocumentNameModalViewModel(defaultName, options)
	{
		const { title = 'Save Merge Document', positiveButtonLabel = 'Save' } = options || {};

		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-sm";
		this.modalClass = "saveNewThematic-modal";
		this.title(title);
		this.contentTemplate("Workspace/Grid/mergedocuments/SaveMergeDocumentName");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel(positiveButtonLabel);
		this.SaveMergeDocumentNameViewModel = new TF.Grid.SaveMergeDocumentNameViewModel(defaultName, options);
		this.data(this.SaveMergeDocumentNameViewModel);

		this.inheritChildrenShortCutKey = false;
	};

	SaveMergeDocumentNameModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SaveMergeDocumentNameModalViewModel.prototype.constructor = SaveMergeDocumentNameModalViewModel;

	/**
	 * React when the positive button is clicked.
	 * @return {void}
	 */
	SaveMergeDocumentNameModalViewModel.prototype.positiveClick = function()
	{
		this.SaveMergeDocumentNameViewModel.validate()
			.then((result) =>
			{
				result && this.positiveClose(result);
			});
	};

	/**
	 * React when the negative button is clicked.
	 * @return {void}
	 */
	SaveMergeDocumentNameModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose(false);
	};

	/**
	 * Dispose
	 * @return {void}
	 */
	SaveMergeDocumentNameModalViewModel.prototype.dispose = function()
	{
		this.data().dispose();
	};
})();