﻿(function()
{
	createNamespace('TF.Control').FieldTripStatusViewModel = FieldTripStatusViewModel;

	function FieldTripStatusViewModel(stageDataModel, selectStage, fieldtripId)
	{
		this._fieldtripId = fieldtripId;
		this._oldNotes = "";

		this.obStageDataModels = ko.observable(stageDataModel);
		this.obSelectedStage = ko.observable(selectStage);
		this.obCurrentStageName = ko.observable(selectStage.Name);
		this.obNotes = ko.observable();

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	FieldTripStatusViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate()
		.then(function(result)
		{
			if (result)
			{
				return { selectStage: this.obSelectedStage(), notes: this.obNotes(), isNotesChange: this._oldNotes != this.obNotes() };
			}
		}.bind(this));
	}

	FieldTripStatusViewModel.prototype.init = function(viewModel, el)
	{
		this.$form = $(el);
		var validatorFields = {}, isValidating = false, self = this;

		validatorFields.status = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: " required"
				}
			}
		}

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.$form.find("input[name=status]").focus();
		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));

		this.load();
	};

	FieldTripStatusViewModel.prototype.load = function()
	{
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "fieldtriphistory", this._fieldtripId, "fieldTripHistorys"))
		.then(function(data)
		{
			var item = data.Items[0];
			for (var i in data.Items)
			{
				if (moment(data.Items[i].TheDateTime) > moment(item.TheDateTime))
					item = data.Items[i];
			}
			this._oldNotes = item ? item.Notes : "";
			this.obNotes(this._oldNotes);
		}.bind(this))
	}

	FieldTripStatusViewModel.prototype.apply = function()
	{
		return this.save()
		.then(function(data)
		{
			return data;
			this.dispose();
		}.bind(this));
	};

	FieldTripStatusViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

