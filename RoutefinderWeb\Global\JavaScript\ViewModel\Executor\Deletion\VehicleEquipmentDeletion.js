﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleEquipmentDeletion = VehicleEquipmentDeletion;

	function VehicleEquipmentDeletion()
	{
		this.type = 'vehicleequipment';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleEquipmentDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleEquipmentDeletion.prototype.constructor = VehicleEquipmentDeletion;

	VehicleEquipmentDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}

	VehicleEquipmentDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	}
})();