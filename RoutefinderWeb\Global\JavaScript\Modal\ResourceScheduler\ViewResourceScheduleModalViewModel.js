﻿(function()
{
	createNamespace("TF.Modal.ResourceScheduler").ViewResourceScheduleModalViewModel = ViewResourceScheduleModalViewModel;

	function ViewResourceScheduleModalViewModel(resourceEntity, resourceType, trips, fieldtrips, restrictions, currentDate)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Resource Schedule for ' + moment(currentDate).locale("en-us").format("MMMM DD, YYYY"));
		this.sizeCss = "modal-dialog-md";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/resourcescheduler/viewresourceschedule');
		this.buttonTemplate("modal/negative");
		this.viewResourceScheduleViewModel = new TF.Control.ResourceScheduler.ViewResourceScheduleViewModel(resourceEntity, resourceType, trips, fieldtrips, restrictions);
		this.data(this.viewResourceScheduleViewModel);
	};

	ViewResourceScheduleModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ViewResourceScheduleModalViewModel.prototype.constructor = ViewResourceScheduleModalViewModel;
})();