(function()
{
	createNamespace('TF.Modal').ResetPasswordModalViewModel = ResetPasswordModalViewModel;

	function ResetPasswordModalViewModel(loginId)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/resetpasswordcontrol');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Reset Password");
		this.sizeCss = "modal-dialog reset-password";
		this.focusInFirstElement = false;
		this.obNegativeButtonClass("btn btn-link btn-sm negative dialog-btn-right");
		this.viewModel = new TF.Control.ResetPasswordViewModel(loginId);
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.data(this.viewModel);
		this.title("Change Password");
	}

	ResetPasswordModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ResetPasswordModalViewModel.prototype.constructor = ResetPasswordModalViewModel;

	ResetPasswordModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ResetPasswordModalViewModel.prototype.negativeClick = function()
	{
		if (this.viewModel.obNewPassword() || this.viewModel.obConfirmPassword())
		{
			return tf.promiseBootbox.confirm(
				{
					message: "Are you sure that you want to cancel your password reset?",
					title: "Confirmation Message"
				}).then(function(result)
				{
					if (result)
					{
						this.negativeClose();
					}
				}.bind(this));
		}
		else
		{
			this.negativeClose();
		}
	};

	ResetPasswordModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};

	ResetPasswordModalViewModel.prototype.afterRender = function(el)
	{
		this.viewModel.validateAndLoadSecuritySettings();
		this.viewModel.obLoadPasswordStrengthResult(true);
		TF.Modal.BaseModalViewModel.prototype.afterRender.call(this, el);
	};

})();

(function()
{
	createNamespace('TF.Control').ResetPasswordViewModel = ResetPasswordViewModel;

	function ResetPasswordViewModel(loginId)
	{
		this.loginId = loginId;
		this.obNewPassword = ko.observable();
		this.obConfirmPassword = ko.observable();
		this.obResetPasswordErrorMessage = ko.observable('');
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.obValidateStrongPassword = ko.observable(false);
		this.obLoadPasswordStrengthResult = ko.observable(false);
		this.obIsPasswordSecurityCriteriaMet = ko.observableArray(false);
		this.securitySettings = null;
		this.obRefreshPasswordStrengthResult = ko.observable(false);
		this.passwordViewModel = new TF.StrongPasswordIndicator(this);
		this.obNewPassword.subscribe((newValue) =>
		{
			this.passwordViewModel.obNewPassword(newValue);
		});

	}

	ResetPasswordViewModel.prototype.init = function(model, el)
	{
		var self = this;
		this.$form = $(el);
		var verifyPasswordInput = $(el).find("[name=confirmPassword]");
		var passwordInput = $(el).find("[name=newPassword]");
		var validatorFields = {},
			isValidating = false;

		validatorFields.confirmPassword = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				},
				identical:
				{
					field: 'newPassword',
					message: 'must match New Password.'
				}
			}
		};
		validatorFields.newPassword = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				},
				callback:
				{
					message: "Password must be strong",
					callback: function(value, validator, $field)
					{
						if (!value) return true;
						if (self.obValidateStrongPassword())
						{
							if (!self.obIsPasswordSecurityCriteriaMet())
							{
								return {
									valid: false,
									message: "must meet all criteria",
								};
							}
						}
						return true;
					}
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
		verifyPasswordInput.keyup(function(e)
		{
			var code = (e.keyCode ? e.keyCode : e.which);
			if (code == 13)
			{
				self.apply();
			}
		});
	};

	ResetPasswordViewModel.prototype.validateAndLoadSecuritySettings = function()
	{
		const clientKey = tf.authManager.clientKey;
		const userId = tf.authManager.authorizationInfo.authorizationTree.userId;

		return Promise.all([
			tf.promiseAjax.get(pathCombine(tf.api.server(), clientKey, "securitysettings")),
			tf.promiseAjax.get(pathCombine(tf.api.server(), clientKey, `users/${userId}/Roles?EnableStrongPassword=false&@exist=true`))
		]).then(([securitySettingsResponse, rolesResponse]) =>
		{
			const securitySettings = securitySettingsResponse.Items[0];
			this.securitySettings = securitySettings;
			this.obValidateStrongPassword(!rolesResponse);
		});
	};

	ResetPasswordViewModel.prototype.save = function()
	{
		var self = this;
		return this.pageLevelViewModel.saveValidate().then(function(result)
		{
			if (!result)
			{
				return Promise.resolve(false);
			}

			return self.checkPasswordValidation().then(isValid =>
			{
				if (isValid)
				{
					return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "passwords"),
						{
							data: JSON.stringify(self.obNewPassword())
						})
						.then(function(response)
						{
							if (response.Items[0])
							{
								tf.entStorageManager.save('password', self.obNewPassword());
								self.pageLevelViewModel.popupSuccessMessage("Password Changed.");
							}
							return response.Items[0];
						}.bind(self), function(response)
						{
							return tf.promiseBootbox.confirm(
								{
									message: "The Password for " + self.loginId + " was not able to be Reset.  You can try again or cancel and try later.  ",
									title: "Unable to Reset Password",
									buttons:
									{
										OK:
										{
											label: "Try Again",
											className: "btn-primary btn-sm btn-primary-black"
										},
										Cancel:
										{
											label: "Cancel",
											className: "btn-default btn-sm btn-default-link"
										}
									}
								}).then(function(tryAgain)
								{
									if (tryAgain)
									{
										return false;
									}
									else
									{
										tf.authManager.logOff();
									}

								});
						});
				}
			});
		});
	};

	ResetPasswordViewModel.prototype.checkPasswordValidation = function()
	{
		var newPassword = this.obNewPassword();
		this.obResetPasswordErrorMessage("");

		if (this.obValidateStrongPassword())
		{
			return tf.promiseAjax.post(pathCombine(tf.api.server(), tf.authManager.clientKey, "passwords/validate?username=" + tf.authManager.authorizationInfo.authorizationTree.username), {
				data: JSON.stringify(newPassword)
			})
				.then(() =>
				{
					this.obResetPasswordErrorMessage("");
					return true;
				})
				.catch((apiResponse) =>
				{
					this.obResetPasswordErrorMessage(apiResponse.Message);
					return false;
				});
		}

		return Promise.resolve(true);
	};

	ResetPasswordViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(data)
			{
				return data;
			});
	};

	ResetPasswordViewModel.prototype.dispose = function()
	{
		var self = this;
		setTimeout(function()
		{
			self.pageLevelViewModel.dispose();
		}, 4000);
	};

})();
