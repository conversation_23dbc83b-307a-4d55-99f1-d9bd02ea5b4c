(function()
{
	function preventEvent(element, valueAccessor)
	{
		var $element = $(element);
		var disable = valueAccessor().disable;
		$element.data("disable", disable);
		if (disable)
		{
			$element.addClass("disable");
			$element.attr("disabled", true);
		} else
		{
			$element.removeClass("disable");
			$element.removeAttr("disabled");
		}
	}

	ko.bindingHandlers.clickButton = {
		init: function(element, valueAccessor, allBindings, viewModel, bindingContext)
		{
			preventEvent(element, valueAccessor);
			$(element).on("click.disableClick", function(e)
			{
				if (!$(element).data("disable"))
				{
					valueAccessor().click.call(viewModel, viewModel, e);
				}
			});
		},
		update: function(element, valueAccessor, allBindings, viewModel, bindingContext)
		{
			preventEvent(element, valueAccessor);
		}
	};
})();

