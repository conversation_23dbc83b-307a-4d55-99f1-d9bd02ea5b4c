﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.GeoregionTypeDeletion = GeoregionTypeDeletion;

	function GeoregionTypeDeletion()
	{
		this.type = 'georegiontype';
		namespace.BaseDeletion.apply(this, arguments);
	}

	GeoregionTypeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	GeoregionTypeDeletion.prototype.constructor = GeoregionTypeDeletion;

	GeoregionTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();