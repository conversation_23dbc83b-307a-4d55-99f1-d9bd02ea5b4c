(function()
{
	var _DataFiledName = 'DisplayName',
		_KendoUid = "kendoUid",
		_keyPressName = ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"],
		_GridConifg = {
			gridSchema: {
				model: {
					fields: {
						'RawImage': { type: "string" }
					}
				},
			},
			gridColumns: [
				{
					width: '35px',
					type: "nofilter",
					FieldName: 'RawImage',
					DisplayName: 'Image',
					template: '<img style="width:20px; height:20px;" src="#: RawImage #" alt="" class="normal img-circle" />',
					filterable: false
				},
				{
					field: 'FullName',
					title: "Name",
					type: "string",
					FieldName: 'FullName',
					DisplayName: 'Name',
					filterable: {
						cell: {
							showOperators: false,
							operator: "contains"
						}
					}
				},
				{
					field: 'School',
					title: "School",
					type: "string",
					FieldName: 'School',
					DisplayName: 'School',
					filterable: {
						cell: {
							showOperators: false,
							operator: "contains"
						}
					}
				},
				{
					field: 'Grade',
					title: "Grade",
					type: "string",
					width: '60px',
					FieldName: 'Grade',
					DisplayName: 'Grade',
					filterable: {
						cell: {
							showOperators: false,
							operator: "contains"
						}
					}
				}
			],
			filterable: {
				extra: false,
				mode: "row"
			},
			height: 500,
			selectable: TF.isMobileDevice ? "row" : "multiple"
		};
	var _unassignedEntityGrid = null,
		_assignedEntityGrid = null,
		_obLeftGridSelectedUids = ko.observableArray(),
		_obRightGridSelectedUids = ko.observableArray();

	createNamespace("TF.Fragment").KendoListMoverViewModel = KendoListMoverViewModel;

	function KendoListMoverViewModel(EntityViewModels_unassigned, EntityViewModels_assigned, ungeoEntity, options, shortCutKeyHashMapKeyName, disableControl, assignStudents, unAssignedStudents, exceptionData)
	{
		this.options = options;
		_GridConifg.gridColumns.map(function(item) { item.title = tf.applicationTerm.getApplicationTermSingularByName(item.title); });
		this.configure = $.extend({}, _GridConifg, options.configure);
		this.obDisableControl = disableControl;
		this.obDisableControl(EntityViewModels_assigned.length === 0 && EntityViewModels_unassigned.length === 0);
		if (EntityViewModels_assigned.length > 0 || EntityViewModels_unassigned.length > 0 || ungeoEntity > 0)
		{
			var tempdata = EntityViewModels_assigned.concat(EntityViewModels_unassigned).concat(ungeoEntity);
			// get the fields from the data obtained by first open this viewmodel
			this.studentFieldsFirstGet = Object.keys(tempdata[0]);
		}
		this.assignStudents = assignStudents;
		this.unAssignedStudents = unAssignedStudents;

		this.obDescription = ko.observable(options.description);
		this.obshowAddButton = ko.observable(options.showAddButton);
		this.obshowRemoveColumnButton = ko.observable(options.showRemoveColumnButton);
		this._unassignedEntity = this.convertToSource(EntityViewModels_unassigned);
		this._unassignedUngeoEntity = this.convertToSource(ungeoEntity);
		this._rawUnassignedEntity = this.concat(this._unassignedEntity, this._unassignedUngeoEntity);
		this._assignedEntity = this.convertToSource(EntityViewModels_assigned);

		this.obunassignedEntity = ko.observableArray(this._unassignedEntity);
		this.obassignedEntity = ko.observableArray(this._assignedEntity);
		this.obassignedEntity.subscribe(() =>
		{
			if (!this.obDisableControl) return;
			this.obDisableControl(this.obassignedEntity().length === 0 && this.obunassignedEntity().length === 0)
		});
		this.obShowEnabled = ko.observable(this.options.showCheckBox);
		this.obFilterCheckboxText = ko.observable(this.options.filterCheckboxText);

		this.obleftTitle = ko.observable(options.leftTitle);
		this.obrightTitle = ko.observable(options.rightTitle);
		this.obDisplayCheckbox = ko.observable(this.options.displayCheckbox);
		this.originalColumns = [];
		this.columns = this.configure.gridColumns;
		this.obShowStudentDetail = ko.observable(this.options.hasLockedItems);
		_unassignedEntityGrid = null;
		_assignedEntityGrid = null;
		_obLeftGridSelectedUids([]);
		_obRightGridSelectedUids([]);
		this.obLeftGridSelected = ko.computed(function()
		{
			return _obLeftGridSelectedUids() && _obLeftGridSelectedUids().length > 0;
		}, this);
		this.obRightGridSelected = ko.computed(function()
		{
			return _obRightGridSelectedUids() && _obRightGridSelectedUids().length > 0;
		}, this);

		this.onKeyPress = this.onKeyPress.bind(this);
		tf.shortCutKeys.bind(_keyPressName, this.onKeyPress, shortCutKeyHashMapKeyName);
		this.pageLevelViewModel = new TF.PageLevel.ListMoverPageLevelViewModel(this);
		if (this.options.withOutSelectedValidate)
		{
			this.pageLevelViewModel.getValidationErrorsSpecifed = function()
			{
				return [];
			}
		}

		this.exceptionData = exceptionData;
		this.exceptionStudents = [];
	}

	KendoListMoverViewModel.prototype.addException = function()
	{
		if (!this.exceptionData) return;
		this.exceptionData = {
			...this.exceptionData,
			selectedData: _assignedEntityGrid.dataSource.data().slice().filter(x => !x.RequirementID),
			assignedStudent: _assignedEntityGrid.dataSource.data(),
			unAssignedStudent: _unassignedEntityGrid.dataSource.data()
		};
		tf.modalManager.showModal(new TF.Modal.AddExceptionModalViewModel(this.exceptionData)).then(result =>
		{
			if (!result) return;
			_assignedEntityGrid.dataSource.data(_assignedEntityGrid.dataSource.data().slice().concat(result));
			this.obassignedEntity(_assignedEntityGrid.dataSource.data());
		});
	}

	KendoListMoverViewModel.prototype.defineGridSchema = function(gridColumns)
	{
		for (var i = 0; i < gridColumns.length; i++)
		{
			if (gridColumns[i].FieldName != "RawImage" && gridColumns[i].DisplayName != "Image")
			{
				var filed;
				if (gridColumns[i].type == "integer")
				{
					filed = { type: "number" };
				}
				else
				{
					filed = { type: gridColumns[i].type };
				}
				this.configure.gridSchema.model.fields[gridColumns[i].FieldName] = filed;
			}
		}
	};

	KendoListMoverViewModel.prototype.studentDetailClick = function()
	{
		if (!this.obLeftGridSelected() && !this.obRightGridSelected())
		{
			return;
		}
		var selectedRows = [];
		for (var i = 0; i < _obLeftGridSelectedUids().length; i++)
		{
			selectedRows.push(_unassignedEntityGrid.dataSource.getByUid(_obLeftGridSelectedUids()[i]));
		}
		for (var i = 0; i < _obRightGridSelectedUids().length; i++)
		{
			selectedRows.push(_assignedEntityGrid.dataSource.getByUid(_obRightGridSelectedUids()[i]));
		}
		return tf.modalManager.showModal(new TF.RoutingMap.RoutingPalette.StudentDetailModalViewModel(selectedRows, null, null, false));
	}

	KendoListMoverViewModel.prototype.convertToSource = function(koSource)
	{
		if (!koSource)
		{
			return null;
		}

		return koSource.map(function(item)
		{
			var obj = item._entityBackup;
			if (!obj)
			{
				obj = item;
			}
			obj.FullName = obj.LastName + ', ' + obj.FirstName;
			return obj;
		});
	};

	KendoListMoverViewModel.prototype.concat = function(unassignedEntity, unassignedUngeoEntity)
	{
		if (!unassignedEntity)
		{
			return unassignedUngeoEntity;
		}

		if (!unassignedUngeoEntity)
		{
			return unassignedEntity;
		}

		return unassignedEntity.concat(unassignedUngeoEntity);
	};

	KendoListMoverViewModel.prototype.addRawImageColumn = function(allColumns)
	{
		var rawImage = {
			FieldName: 'RawImage',
			DisplayName: 'Image',
			Width: '35px',
			sortable: false,
			locked: false,
			type: 'nofilter',
			isSortItem: false,
			template: '<img style="width:20px; height:20px;" src="#: RawImage #" alt="" class="normal img-circle" />',
			filterable: false
		};
		allColumns.push(rawImage);
	};

	KendoListMoverViewModel.prototype.initializeGrid = function(viewModel, el)
	{
		this.unassignedEntityContainer = $(el).find(".unassignedEntity");
		this.assignedEntityContainer = $(el).find(".assignedEntity");
		$(el).bootstrapValidator();
		// this.$unGeo = $(el).find(".unGeo");
		// this.$unGeo.on("change", this.geoOnOFF.bind(this));
		this.pArray = [];
		if (!this.options.withOutImage)
		{
			this._unassignedEntity.forEach(function(item)
			{
				this.pArray.push(this.setImage(item));
			}.bind(this));
			if (this._unassignedUngeoEntity)
			{
				this._unassignedUngeoEntity.forEach(function(item)
				{
					this.pArray.push(this.setImage(item));
				}.bind(this));
			}
			this._assignedEntity.forEach(function(item)
			{
				this.pArray.push(this.setImage(item));
			}.bind(this));
		}

		Promise.all(this.pArray).then(function()
		{
			if (this.options.sticky != false)
			{
				var stickyColumns = this.getCurrentSelectedColumns(this.options.stickyKey || this.options.type);
				if (stickyColumns)
				{
					this.columns = stickyColumns;
				}
			}
			// this.originalColumns = this.columns.map(function(item) { return $.extend({}, item); });
			this.originalColumns = this.configure.gridColumns;
			this.columns = this.getKendoColumns(this.columns, "150px");
			this.defineGridSchema(this.columns);
			this.initLeftGrid();
			this.initRightGrid();
			this.obunassignedEntity(_unassignedEntityGrid.dataSource.data());
			this.obassignedEntity(_assignedEntityGrid.dataSource.data());

			this.bindLeftGridDraggable();
			this.bindRightGridDraggable();
			this.bindLeftGridDropTarget();
			this.bindRightGridDropTarget();

			this.createKendoDropTargetEvent();

			this.unassignedEntityContainer.on("dblclick", `tr.${TF.KendoClasses.STATE.SELECTED}`, function()
			{
				this.toRightClick();
			}.bind(this));

			this.assignedEntityContainer.on("dblclick", `tr.${TF.KendoClasses.STATE.SELECTED}`, function()
			{
				this.toLeftClick();
			}.bind(this));
			// in case that when first open the view model,UDF fields like ToSchoolTrip1TripName are already added to the columns.
			this.reGetDataSource();
		}.bind(this));

		this.pageLevelViewModel.load($(el).data("bootstrapValidator"));
	};

	KendoListMoverViewModel.prototype.addRemoveColumnClick = function(viewModel, el)
	{
		function initHiddenLockedField(column)
		{
			if (typeof (column.hidden) == "undefined")
			{
				column.hidden = false;
			}
			if (typeof (column.locked) == "undefined")
			{
				column.locked = false;
			}
		}
		var listMoverColumns = this.columns;
		var availableColumns = [];
		var selectedColumns = [];
		var allColumns = TF.Grid.FilterHelper.getGridDefinitionByType(this.options.type).Columns.slice(0);
		if (!this.options.withOutImage)
		{
			this.addRawImageColumn(allColumns);
		}
		allColumns = allColumns.concat(this.originalColumns.filter(function(item)
		{
			return !Enumerable.From(allColumns).Any("$.FieldName=='" + item.FieldName + "'");
		}).map(function(item)
		{
			return $.extend({}, item);
		}));
		allColumns = TF.Helper.KendoListMoverHelper.removeOnlyForFilterColumn(allColumns);
		availableColumns = allColumns.slice(0);
		allColumns.forEach(function(item)
		{
			item.hidden = true;
			initHiddenLockedField(item);
		});

		for (var i = 0, l = listMoverColumns.length; i < l; i++)
		{
			var existsColumn = null;
			for (var j = 0, jl = allColumns.length; j < jl; j++)
			{
				if (allColumns[j].FieldName && (allColumns[j].FieldName == listMoverColumns[i].FieldName || allColumns[j].FieldName == listMoverColumns[i].DisplayName))
				{
					existsColumn = listMoverColumns[i];
					var tempColumn = Enumerable.From(availableColumns).Where("$.FieldName=='" + allColumns[j].FieldName + "'").FirstOrDefault();
					tempColumn.FieldName = "";
					allColumns[j] = existsColumn;
					break;
				}
			}
			var columnClone = $.extend({}, listMoverColumns[i]);
			if (!columnClone.DisplayName || $.trim(columnClone.DisplayName) == "")
			{
				if (columnClone.FieldName !== 'RawImage')
				{
					columnClone.DisplayName = columnClone.FieldName;
				}
				else
				{
					columnClone.DisplayName = 'Image';
				}
			}
			initHiddenLockedField(columnClone);
			selectedColumns.push(columnClone);
			if (!existsColumn)
			{
				allColumns.unshift(columnClone);
			}
		}

		availableColumns = Enumerable.From(availableColumns).Where("$.FieldName!=''").ToArray();
		var resetColumns = this.originalColumns.map(function(item)
		{
			return Enumerable.From(allColumns).Where("$.FieldName=='" + item.FieldName + "'").FirstOrDefault();
		});
		var self = this;

		tf.modalManager.showModal(
			new TF.Modal.Grid.EditKendoColumnModalViewModel(
				availableColumns,
				selectedColumns,
				resetColumns
			)
		).then(function(editColumnViewModel)
		{
			if (!editColumnViewModel)
			{
				return;
			}
			var enumerable = Enumerable.From(self.originalColumns);
			//reset column setting to default
			editColumnViewModel.selectedColumns = editColumnViewModel.selectedColumns.map(function(item)
			{
				var oc = enumerable.Where("$.FieldName=='" + item.FieldName + "'").FirstOrDefault();
				return oc || item;
			});
			self.columns = editColumnViewModel.selectedColumns;
			// self.originalColumns = editColumnViewModel.selectedColumns;
			self.saveCurrentSelectedColumns(self.options.stickyKey || self.options.type, self.getKendoColumns(self.columns, "150px"));

			// When user add a UDF fields like ToSchoolTrip1TripName to column,send request to fetch the new field's value and update the grid data source.
			self.reGetDataSource();

			Promise.all([self.reFreshGrid(self.unassignedEntityContainer.data("kendoGrid"), true),
			self.reFreshGrid(self.assignedEntityContainer.data("kendoGrid"))]).then(function()
			{
				self.bindLeftGridDropTarget();
				self.bindRightGridDropTarget();
			});
		});
	};

	KendoListMoverViewModel.prototype.getUdfs = function()
	{
		const allColumns = TF.Grid.FilterHelper.getGridDefinitionByType(this.options.type).Columns.slice(0);
		return _.uniqBy(allColumns.filter(c => !!c.OriginalName), "UDFId");
	}

	// When there are fields like ToSchoolTrip1TripName in the column but not in the grid datasource,
	// send request to fetch the new field's value and update the grid data source.
	KendoListMoverViewModel.prototype.reGetDataSource = function()
	{
		var self = this;
		if (this.studentFieldsFirstGet)
		{
			var columnsInDatasource = this.studentFieldsFirstGet;

			var columnsNotInDatasource = self.columns.filter(x => 'FieldName' in x).filter(function(item)
			{
				return columnsInDatasource.indexOf(item.FieldName) == -1;
			}).map(function(item)
			{
				return item.FieldName;
			});

			if (columnsNotInDatasource.length > 0)
			{
				var allstudentIDs = self.assignStudents.map(function(student)
				{
					return student.id;
				}).concat(self.unAssignedStudents.geoStudents.map(function(student)
				{
					return student.id;
				})).concat(self.unAssignedStudents.ungeoStudents.map(function(student)
				{
					return student.id;
				}));
				var assignedEntity = self._assignedEntity;
				var unassignedEntity = self._unassignedEntity;
				var unassignedUngeoEntity = self._unassignedUngeoEntity;
				var p1 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint("student")), {
					data: {
						"idFilter": {
							IncludeOnly: allstudentIDs
						},
						fields: columnsNotInDatasource
					}
				}).then((data) =>
				{
					var newassignedDataSource = [];
					var newunassignedDataSource = [];
					var newunassignedUngeoDataSource = [];
					const udfs = self.getUdfs();
					data.Items.map((item) =>
					{
						if (Enumerable.From(assignedEntity).Any(function(x) { return x.id == item.Id }))
						{
							var originStudentData = Enumerable.From(assignedEntity).Where(function(c) { return c.id == item.Id }).ToArray()[0];
							Object.keys(item).forEach((key) =>
							{
								let udf = udfs.filter(udf => udf.OriginalName == key);
								if (udf.length > 0) { originStudentData[udf[0].FieldName] = item[key]; }
							});
							newassignedDataSource.push(Object.assign(originStudentData, item));
						}
						if (Enumerable.From(unassignedEntity).Any(function(x) { return x.id == item.Id }))
						{
							originStudentData = Enumerable.From(unassignedEntity).Where(function(c) { return c.id == item.Id }).ToArray()[0];
							Object.keys(item).forEach((key) =>
							{
								let udf = udfs.filter(udf => udf.OriginalName == key);
								if (udf.length > 0) { originStudentData[udf[0].FieldName] = item[key]; }
							});
							newunassignedDataSource.push(Object.assign(originStudentData, item));
						}
						if (Enumerable.From(unassignedUngeoEntity).Any(function(x) { return x.id == item.Id }))
						{
							originStudentData = Enumerable.From(unassignedUngeoEntity).Where(function(c) { return c.id == item.Id }).ToArray()[0];
							Object.keys(item).forEach((key) =>
							{
								let udf = udfs.filter(udf => udf.OriginalName == key);
								if (udf.length > 0) { originStudentData[udf[0].FieldName] = item[key]; }
							});
							newunassignedUngeoDataSource.push(Object.assign(originStudentData, item));
						}
					});
					return [newassignedDataSource, newunassignedDataSource, newunassignedUngeoDataSource];
				}).then(function(data)
				{
					self.assignedEntityContainer.data("kendoGrid").dataSource.data(self.convertToSource(data[0]));
					self.unassignedEntityContainer.data("kendoGrid").dataSource.data(self.convertToSource(data[1]));
					self._unassignedUngeoEntity = self.convertToSource(data[2]);
				})
			}

		}


	}

	KendoListMoverViewModel.prototype.reFreshGrid = function(kendoGrid, isAddStickColumns)
	{
		var self = this;
		return new Promise(function(resolve)
		{
			tf.loadingIndicator.showImmediately();
			setTimeout(function()
			{
				resolve();
			}, 0);
		})
			.then(function()
			{
				this.overlayShow = true;
				var columnsdefalultColumnWidth = '150px';
				var kendoOptions = kendoGrid.getOptions();
				kendoOptions.columns = this.getKendoColumns(this.columns, columnsdefalultColumnWidth);

				if (isAddStickColumns && self.configure.stickColumns)
				{
					if (kendoOptions.columns.length < 3)
					{
						var maxWidth = kendoGrid.element.find('.k-grid-header-wrap').width() / kendoOptions.columns.length;
						kendoOptions.columns.map(function(column)
						{
							maxWidth = column.minResizableWidth ? Math.max(column.minResizableWidth, maxWidth) : maxWidth;
							column.width = maxWidth + 'px';
							column.Width = maxWidth + 'px';
						});
					}
					kendoOptions.columns = kendoOptions.columns.concat(self.configure.stickColumns);
				}
				if (kendoOptions.columns.length == 1)
				{
					kendoOptions.columns[0].locked = false;
				}
				tf.measurementUnitConverter.handleUnitOfMeasure(kendoOptions.columns);
				kendoGrid.setOptions(kendoOptions);

				setTimeout(function()
				{
					this.overlayShow = false;
					tf.loadingIndicator.tryHide();
				}.bind(this), 1000);
			}.bind(this), 0);

	};

	KendoListMoverViewModel.prototype.getKendoColumns = function(currentColumns, defalultColumnWidth)
	{
		var self = this;
		var columns = currentColumns.map(function(definition)
		{
			var column = definition;
			if (definition.field)
			{
				column.field = definition.field;
			}
			else
			{
				column.field = definition.FieldName;
			}
			column.title = " ";
			if (definition.DisplayName !== "Image")
				column.title = definition.DisplayName;
			else
			{
				column.title = " ";
			}
			if (!column.width)
				column.width = definition.Width || defalultColumnWidth;
			else
				definition.Width = column.width;
			self.setColumnFilterableCell(column, definition);
			column.hidden = false; // Overwrite the value of hidden attribute which setting in api.
			column.locked = false;
			return column;
		});

		return columns;
	};

	KendoListMoverViewModel.prototype.setColumnFilterableCell = function(column, definition)
	{
		var self = this;
		switch (definition.type)
		{
			case "string":
				column.filterable = {
					cell: {
						showOperators: false,
						operator: "contains"
					}
				};
				break;
			case "number":
				column.format = definition.format ? definition.format : "{0:n2}";
				column.filterable = {
					cell: {
						showOperators: false,
						operator: "eq",
						template: function(args)
						{
							args.element.kendoNumericTextBox({
								decimals: definition.Precision ? definition.Precision : 2,
								format: definition.format ? definition.format : "{0:n2}"
							});
						}
					}
				};
				break;
			case "integer":
				column.format = definition.format ? definition.format : "{0:n0}";
				column.filterable = {
					cell: {
						showOperators: false,
						operator: "eq",
						template: function(args)
						{
							args.element.kendoNumericTextBox({
								format: "n0"
							});
						}
					}
				};
				break;
			case "time":
				column.format = "{0:h:mm tt}";
				column.filterable = {
					cell: {
						showOperators: false,
						template: function(args)
						{
							var span = $(args.element[0].parentElement);
							span.empty();
							span.append($("<span class='input-group tf-filter' data-kendo-bind='value: value' data-kendo-role='customizedtimepicker'></span>"));
						}
					}
				};
				break;
			case "date":
				column.format = "{0:MM/dd/yyyy}";
				column.filterable = {
					cell: {
						showOperators: false,
						operator: "eq",
						template: function(args)
						{
							args.element.kendoDatePicker();
							args.element.on("keypress", function(e)
							{
								if ((e.which < 45 || e.which > 57) && e.which != 37 && e.which != 39)
								{
									e.preventDefault();
								}
							});
						}
					}
				};
				break;

			case "datetime":
				column.format = "{0:MM/dd/yyyy hh:mm tt}";
				column.filterable = {
					cell: {
						showOperators: false,
						operator: "eq",
						template: function(args)
						{
							var span = $(args.element[0].parentElement);
							span.empty();
							span.append($("<span class='input-group tf-filter' data-kendo-bind='value: value' data-kendo-role='customizeddatetimepicker'></span>"));
						}
					}
				};
				break;

			case "boolean":
				column.filterable = {
					cell: {
						showOperators: false,
						template: function(args)
						{
							args.element.kendoDropDownList({
								dataSource: new kendo.data.DataSource({
									data: [
										{ someField: "", valueField: "" },
										{ someField: "True", valueField: "true" },
										{ someField: "False", valueField: "false" }
									]
								}),
								dataTextField: "someField",
								dataValueField: "valueField",
								valuePrimitive: true
							});
						}
					}
				};
				break;
			case "image":
				column.filterable = {
					cell: {
						template: function(args)
						{
							args.element.kendoDropDownList({
								dataSource: {
									data: self.getImageFilterableDataSource(definition.FieldName)
								},
								dataTextField: "someField",
								dataValueField: "valueField",
								valuePrimitive: true,
								valueTemplate: '<span class="icon-select-item #:data.someField#"></span>',
								template: '<span class="icon-select-item #:data.someField#"></span>'
							});
						},
						showOperators: false
					}
				};
				break;
			case "nofilter":
				break;
		}
	};

	KendoListMoverViewModel.prototype.onEnableCheckboxClick = function()
	{
		this.obShowEnabled(!this.obShowEnabled());

		_clearLeftSelection();
		if (this.obShowEnabled())
		{
			if (this.options.showUngeocodedStudent)
			{
				let unassignedEntityGrid = _unassignedEntityGrid.dataSource.data().reduce((accumulator, current) =>
				{
					accumulator[current.id] = current;
					return accumulator;
				}, {});

				this._unassignedUngeoEntity = this._unassignedUngeoEntity.filter(stu => !unassignedEntityGrid[stu.id]);
			}
			_unassignedEntityGrid.dataSource.data(Array.from(_unassignedEntityGrid.dataSource.data()).concat(this._unassignedUngeoEntity));
		}
		else
		{
			var data = Array.from(_unassignedEntityGrid.dataSource.data());
			for (var i = this.obunassignedEntity().length - 1; i > -1; i--)
			{
				var item = this.obunassignedEntity()[i];
				if (item.Xcoord == null || item.Xcoord == 0)
				{
					data = data.filter(x => x != item);
				}
			}
			_unassignedEntityGrid.dataSource.data(data);
		}
		this.obunassignedEntity(_unassignedEntityGrid.dataSource.data());
		this._processFilterDataSource();
	}

	KendoListMoverViewModel.prototype.getImageFilterableDataSource = function(fieldName)
	{
		var dataSource = [];
		switch (fieldName)
		{
			case "Ampmschedule":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmschedule("14"), valueField: "14" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmschedule("15"), valueField: "15" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmschedule("16"), valueField: "16" });
				break;
			case "Ampmtransportation":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmtransportation("10"), valueField: "10" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmtransportation("11"), valueField: "11" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmtransportation("12"), valueField: "12" });
				break;
			case "RidershipStatus":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_RidershipStatus("37"), valueField: "37" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_RidershipStatus("39"), valueField: "39" });
				break;
			case "PolicyDeviation":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_PolicyDeviation("37"), valueField: "37" });
				break;
			case "Notes":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Notes("5"), valueField: "5" });
				break;
			case "IsLocked":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_IsLocked("6"), valueField: "6" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_IsLocked(""), valueField: "neq" });
				break;
		}
		return dataSource;
	};

	KendoListMoverViewModel.prototype.saveCurrentSelectedColumns = function(gridType, columns)
	{
		return tf.storageManager.save(tf.storageManager.listMoverCurrentSelectedColumns(gridType, tf.authManager.authorizationInfo.authorizationTree.username), columns);
	};

	KendoListMoverViewModel.prototype.getCurrentSelectedColumns = function(gridType)
	{
		return tf.storageManager.get(tf.storageManager.listMoverCurrentSelectedColumns(gridType, tf.authManager.authorizationInfo.authorizationTree.username));
	};

	KendoListMoverViewModel.prototype.geoOnOFF = function(e)
	{
		if (e.currentTarget.checked)
		{//_unassignedUngeoEntity
			this._unassignedUngeoEntity.forEach(function(item)
			{
				_unassignedEntityGrid.dataSource.add(item);
			}.bind(this));
		}
		else
		{
			for (var i = this.obunassignedEntity().length - 1; i > -1; i--)
			{
				var item = this.obunassignedEntity()[i];
				if (item.GeometryPoint == null)
				{
					_unassignedEntityGrid.dataSource.remove(item);
				}
			}
		}
		this.obunassignedEntity(_unassignedEntityGrid.dataSource.data());
		this._processFilterDataSource();
	};

	KendoListMoverViewModel.prototype.createKendoDropTargetEvent = function()
	{
		this.assignedEntityContainer.find("tbody tr").kendoDropTarget({
			dragenter: function(e)
			{
				var targetItem = $(e.dropTarget[0]);
				targetItem.addClass("drag-target-insert-after-cursor");
				_removeDropTargetCursorTriangle();
				_appendDropTargetCursorTriangle(targetItem);
			},
			dragleave: function(e)
			{
				$(e.dropTarget[0]).removeClass("drag-target-insert-after-cursor");
				_removeDropTargetCursorTriangle();
			},
			drop: _selectedDrop.bind(this)
		});
	};

	KendoListMoverViewModel.prototype.onLeftGridChange = function(arg)
	{
		var self = this;
		var kendoGrid = self.unassignedEntityContainer.data("kendoGrid");
		var selectedUids = [];
		if (!self.options.hasLockedItems)
		{
			selectedUids = $.map(kendoGrid.select(), function(item)
			{
				return item.dataset[_KendoUid];
			});
		}
		else
		{
			var selected = [];
			selectedUids = $.map(kendoGrid.select(), function(item)
			{
				var row = $(item).closest("tr");
				var dataItem = kendoGrid.dataItem(row);
				if (!self.options.isDataItemCanSelect || self.options.isDataItemCanSelect(dataItem))
				{
					selected.push(item);
					return dataItem.uid;
				}
			});
			if (!self.changeLeftSelect)
			{
				clearTimeout(self.changeSelectIdsTimeout);
				self.changeSelectIdsTimeout = setTimeout(function()
				{
					self.changeLeftSelect = true;
					kendoGrid.clearSelection();
					kendoGrid.select(selected);
					self.changeLeftSelect = false;
				});
			}
		}
		selectedUids = Enumerable.From(selectedUids).Distinct().ToArray();
		_obLeftGridSelectedUids(selectedUids);
		if (_obLeftGridSelectedUids().length !== 0)
		{
			_clearRightSelection();
		}
	};

	KendoListMoverViewModel.prototype.onRightGridChange = function(arg)
	{
		var selected = $.map(this.select(), function(item)
		{
			return item.dataset[_KendoUid];
		}.bind(this));
		_obRightGridSelectedUids(selected);

		if (_obRightGridSelectedUids().length !== 0)
		{
			_clearLeftSelection();
		}

	};

	var _cancelKendoGridSelectedArea = function(kendoGrid)
	{
		kendoGrid.selectable.userEvents.unbind("start");
		kendoGrid.selectable.userEvents.unbind("move");
		kendoGrid.selectable.userEvents.unbind("end");
	};

	KendoListMoverViewModel.prototype.initLeftGrid = function()
	{
		var self = this;
		let gridType = self.options && self.options.type;
		var columnsArray = self.options.configure.stickColumns ? this.columns.concat(self.options.configure.stickColumns) : this.columns;
		if (self.options.configure.stickColumns && this.columns.length < 3)
		{
			var maxWidth = 351 / this.columns.length;
			this.columns.map(function(column)
			{
				column.width = maxWidth + 'px';
				column.Width = maxWidth + 'px';
			});
		}
		tf.measurementUnitConverter.handleUnitOfMeasure(columnsArray);
		columnsArray = tf.helpers.kendoGridHelper.handleUDFColumnsFormat(columnsArray, gridType);
		this.unassignedEntityContainer.kendoGrid({
			dataSource: new kendo.data.DataSource({
				data: this._unassignedEntity,
				sort: this.configure.sort,
				schema: this.configure.gridSchema
			}),
			columns: columnsArray,
			height: this.configure.height,
			selectable: this.configure.selectable,
			sortable: {
				mode: "single",
				allowUnsort: true
			},
			reorderable: true,
			columnReorder: function(e)
			{
				var assigned = $(".assignedEntity").data("kendoGrid");
				if (assigned.columns[e.oldIndex - 1])
				{
					assigned.reorderColumn(e.newIndex - 1, assigned.columns[e.oldIndex - 1]);
				}
				self.orderColumnsByGridColumns(assigned.columns);
			},
			filterable: this.configure.filterable,
			change: this.onLeftGridChange.bind(self),
			dataBound: function(e)
			{
				self.initGridScrollBar(self.unassignedEntityContainer);
				var kendoGrid = self.unassignedEntityContainer.data("kendoGrid");
				self.options.onDataBound && self.options.onDataBound(kendoGrid);
				if (self.configure.selectable && _obLeftGridSelectedUids().length > 0)
				{
					var items = kendoGrid.items();
					_obLeftGridSelectedUids().forEach(function(uid)
					{
						$.map(items, function(item)
						{
							if (item.dataset[_KendoUid] == uid)
							{
								kendoGrid.select(item);
								return;
							}
						});
					});
				}
				setTimeout(function()
				{
					if (self.options.hasLockedItems)
					{
						var data = self.unassignedEntityContainer.data("kendoGrid").dataSource.data();
						data.map(function(dataItem)
						{
							if (self.options.isDataItemCanSelect && !self.options.isDataItemCanSelect(dataItem))
							{
								var row = self.unassignedEntityContainer.data("kendoGrid").tbody.find("tr[data-kendo-uid='" + dataItem.uid + "']");
								$(row).addClass("disSelectable");
							}
						});
					}
				});
			}
		});
		_unassignedEntityGrid = this.unassignedEntityContainer.data("kendoGrid");
		_cancelKendoGridSelectedArea(_unassignedEntityGrid);

	};

	KendoListMoverViewModel.prototype.initRightGrid = function()
	{
		var self = this;
		let gridType = self.options && self.options.type;
		tf.measurementUnitConverter.handleUnitOfMeasure(this.columns);
		this.columns = tf.helpers.kendoGridHelper.handleUDFColumnsFormat(this.columns, gridType);
		this.assignedEntityContainer.kendoGrid({
			dataSource: new kendo.data.DataSource({
				data: this._assignedEntity,
				sort: this.configure.sort,
				schema: this.configure.gridSchema
			}),
			columns: this.columns,
			height: this.configure.height,
			selectable: this.configure.selectable,
			sortable: {
				mode: "single",
				allowUnsort: true
			},
			reorderable: true,
			columnReorder: function(e)
			{
				var unassigned = $(".unassignedEntity").data("kendoGrid");
				unassigned.reorderColumn(e.newIndex + 1, unassigned.columns[e.oldIndex + 1]);
				self.orderColumnsByGridColumns(unassigned.columns);
			},
			change: this.onRightGridChange,
			dataBound: function(e)
			{
				self.initGridScrollBar(self.assignedEntityContainer);
				var kendoGrid = self.assignedEntityContainer.data("kendoGrid");
				self.options.onDataBound && self.options.onDataBound(kendoGrid);
				if (self.configure.selectable && _obRightGridSelectedUids().length > 0)
				{
					var items = kendoGrid.items();
					_obRightGridSelectedUids().forEach(function(uid)
					{
						$.map(items, function(item)
						{
							if (item.dataset[_KendoUid] == uid)
							{
								kendoGrid.select(item);
								return;
							}
						});
					});
				}
			}
		});
		_assignedEntityGrid = this.assignedEntityContainer.data("kendoGrid");
		_cancelKendoGridSelectedArea(_assignedEntityGrid);
	};

	/**
	* when user sort columns by move column, the columns for list mover should also use this new order
	*/
	KendoListMoverViewModel.prototype.orderColumnsByGridColumns = function(gridColumns)
	{
		this.columns.forEach(column =>
		{
			column.sortIndex = gridColumns.findIndex(gridColumn => gridColumn.field == column.field);
		});
		this.columns.sort((a, b) => a.sortIndex - b.sortIndex);
	}

	KendoListMoverViewModel.prototype.initGridScrollBar = function(container)
	{
		var $gridContent = container.find(".k-grid-content");
		$gridContent.css({
			"overflow-y": "scroll",
			"height": container.hasClass("unassignedEntity") ? (this.configure.filterable ? "430px" : "466px") : "466px"
		});

		var grid = container.data("kendoGrid");
		if (grid)
		{
			grid.scrollables.unbind("scroll.kendoGrid").bind("scroll.kendoGrid", function(e)
			{
				grid.scrollables.not(e.currentTarget).scrollLeft(this.scrollLeft);
				if (grid.lockedContent && e.currentTarget == grid.content[0])
				{
					grid.lockedContent[0].scrollTop = this.scrollTop;
				}
			});
		}
	};

	KendoListMoverViewModel.prototype.bindLeftGridDraggable = function()
	{
		this.unassignedEntityContainer.kendoDraggable({
			filter: "tbody > tr:not(.disSelectable)",
			threshold: 100,
			holdToDrag: TF.isMobileDevice,
			hint: function(e)
			{
				if (e.hasClass(TF.KendoClasses.STATE.SELECTED))
				{
					var _assignedEntity = this.unassignedEntityContainer.find(`.${TF.KendoClasses.STATE.SELECTED}`);
					return _getHintElements(e, this.unassignedEntityContainer, _assignedEntity);
				}
				else
				{
					return _getHintElements(e, this.unassignedEntityContainer);
				}
			}.bind(this),
			dragstart: function(e)
			{
			}.bind(this),
			autoScroll: true,
			cursorOffset: { top: -10, left: -10 },
			dragend: function(e)
			{
				$(".list-mover-drag-hint").hide();
			}.bind(this)
		});
	};

	KendoListMoverViewModel.prototype.bindRightGridDraggable = function()
	{
		this.assignedEntityContainer.kendoDraggable({
			filter: "tbody > tr",
			threshold: 100,
			holdToDrag: TF.isMobileDevice,
			autoScroll: true,
			hint: function(e)
			{
				if (e.hasClass(TF.KendoClasses.STATE.SELECTED))
				{
					var _assignedEntity = this.assignedEntityContainer.find(`.${TF.KendoClasses.STATE.SELECTED}`);
					return _getHintElements(e, this.assignedEntityContainer, _assignedEntity);
				}
				else
				{
					return _getHintElements(e, this.assignedEntityContainer);
				}
			}.bind(this),
			dragstart: function(e)
			{
			}.bind(this),
			cursorOffset: { top: -10, left: -10 },
			dragend: function(e)
			{
				$(".list-mover-drag-hint").hide();
			}.bind(this)
		});
	};

	KendoListMoverViewModel.prototype.bindLeftGridDropTarget = function()
	{
		this.unassignedEntityContainer.kendoDropTarget({
			drop: function(e)
			{
				e.draggable.hint.hide();
				var selectedUids = e.draggable.currentTarget.hasClass(TF.KendoClasses.STATE.SELECTED) ? _obRightGridSelectedUids() : [e.draggable.currentTarget.data().kendoUid];
				if (!e.draggable.element.hasClass("unassignedEntity"))
				{
					this._moveItem(selectedUids, _assignedEntityGrid.dataSource, _unassignedEntityGrid.dataSource, true);
				}
				_sortUnassignedGrid();
				if (selectedUids.length > 0)
				{
					_obLeftGridSelectedUids(selectedUids);
				}
				var dropTargetTrs = e.dropTarget.find("tbody[role=rowgroup]").find("tr");
				var selectTrs = $.grep(dropTargetTrs, function(n)
				{
					return _obLeftGridSelectedUids().indexOf($(n).data().kendoUid) != -1;
				});
				if (selectTrs.length > 0)
				{
					$(selectTrs).addClass(TF.KendoClasses.STATE.SELECTED);
				}
			}.bind(this)
		});
	};

	KendoListMoverViewModel.prototype.bindRightGridDropTarget = function()
	{
		this.assignedEntityContainer.kendoDropTarget({
			dragenter: function(e)
			{
				var selectedColItems = this.assignedEntityContainer.find('tr');
				var targetItem;
				var insertBeforeTarget;
				if (e.draggable.hint.offset().top < $('.assignedEntity .k-grid-content').offset().top)
				{
					targetItem = $(selectedColItems[1]);
					targetItem.addClass("drag-target-insert-before-cursor"); //modify dropTarget element
					insertBeforeTarget = true;
				}
				else
				{
					targetItem = $(selectedColItems[selectedColItems.length - 1])
					targetItem.addClass("drag-target-insert-after-cursor");
				}
				_removeDropTargetCursorTriangle();
				_appendDropTargetCursorTriangle(targetItem, insertBeforeTarget);
			}.bind(this),
			dragleave: function(e)
			{
				var selectedColItems = this.assignedEntityContainer.find('tr');
				selectedColItems.removeClass("drag-target-insert-before-cursor");
				selectedColItems.removeClass("drag-target-insert-after-cursor"); //modify dropTarget element

				_removeDropTargetCursorTriangle();

			}.bind(this),
			drop: _selectedDrop.bind(this)
		});
	};

	KendoListMoverViewModel.prototype.toAllRightClick = function()
	{
		var self = this;
		var uids = _unassignedEntityGrid.dataSource.data().filter(function(item) { return !self.options.isDataItemCanSelect || self.options.isDataItemCanSelect(item); }).map(function(item)
		{
			return item.uid;
		});
		this._moveItem(uids, _unassignedEntityGrid.dataSource, _assignedEntityGrid.dataSource);
		this.sortGridForLastName();
	};

	KendoListMoverViewModel.prototype.toRightClick = function()
	{
		this._moveItem(_obLeftGridSelectedUids(), _unassignedEntityGrid.dataSource, _assignedEntityGrid.dataSource);
		this.sortGridForLastName();
	};

	KendoListMoverViewModel.prototype.toLeftClick = function()
	{
		this._moveItem(_obRightGridSelectedUids(), _assignedEntityGrid.dataSource, _unassignedEntityGrid.dataSource, true);
		this.sortGridForLastName();
	};

	KendoListMoverViewModel.prototype.toAllLeftClick = function()
	{
		this._moveItem(_getUids(_assignedEntityGrid.dataSource), _assignedEntityGrid.dataSource, _unassignedEntityGrid.dataSource, true);
		this.sortGridForLastName();
	};

	KendoListMoverViewModel.prototype.onKeyPress = function(e, keyCombination)
	{
		var _columnsHeight = 28;
		var _top = 0;
		var _keyNum = _keyPressName.indexOf(keyCombination);
		if (_keyNum > 0)
		{
			var _unassignedGridTrs = this.unassignedEntityContainer.find("div.k-grid-content").find("tbody[role=rowgroup]").find("tr");
			var _beforeKeyColumns = $.grep(_unassignedGridTrs, function(n)
			{
				return _keyPressName.indexOf(n.innerText.substring(0, 1).toLowerCase()) < _keyNum;
			});
			var _keyColumns = $.grep(_unassignedGridTrs, function(n)
			{
				return n.innerText.substring(0, 1).toLowerCase() == _keyPressName[_keyNum];
			});
			if (_keyColumns.length == 0)
			{
				return;
			}
			_top = _beforeKeyColumns.length * 28;
		}
		this.unassignedEntityContainer.find("div.k-grid-content").scrollTop(_top);
	};

	KendoListMoverViewModel.prototype.reset = function()
	{
		return this._applyDefaultColumns()
			.then(function(result)
			{
				if (result)
					return this;
			}.bind(this));
	};

	KendoListMoverViewModel.prototype._applyDefaultColumns = function()
	{
		var self = this;
		return (function()
		{
			self.toAllLeftClick();
			self.defaultLayoutColumns.map(function(column)
			{
				self._moveItem(self._getUidByColumnName(column.FieldName, _unassignedEntityGrid.dataSource), _unassignedEntityGrid.dataSource, _assignedEntityGrid.dataSource);
			});
			return Promise.resolve(true);
		})();
	};

	KendoListMoverViewModel.prototype._getUidByColumnName = function(columnFieldName, dataSource)
	{
		var uid;
		dataSource.data().map(function(item)
		{
			if (item.FieldName === columnFieldName)
			{
				uid = item.uid;
				return false;
			}
		});

		return [uid];
	};

	KendoListMoverViewModel.prototype.getSelectDataCount = function()
	{
		return this._assignedEntity.length;
	}

	KendoListMoverViewModel.prototype.apply = function()
	{
		return this._save()
			.then(function(result)
			{
				if (this.options.validation && !this.options.validation(this))
				{
					return false;
				}
				if (result)
					return this;
			}.bind(this));
	};

	KendoListMoverViewModel.prototype._save = function()
	{
		var self = this;
		return (function()
		{
			this._bindGridDataToColumns();

			return this.pageLevelViewModel.saveValidate().then(function(result)
			{
				if (result)
				{
					// self.saveCurrentSelectedColumns(self.options.type, $(".unassignedEntity").data("kendoGrid").columns);
					return Promise.resolve(true);
				}
				else
				{
					return Promise.resolve(false);
				}
			}.bind(this));
		}.bind(this))();
	};

	KendoListMoverViewModel.prototype._bindGridDataToColumns = function()
	{
		var getColKeys = function(col)
		{
			return {
				Id: col.Id
			};
		};

		var switchColumns = function(orign, dest, colKeys, filterCondition)
		{
			//var keys = Enumerable.From(colKeys).Where(function(r) { return typeof (r.hidden) === "undefined" || r.hidden === filterCondition; }).ToArray();
			for (var i = 0; i < orign.length; i++)
			{
				if (colKeys.some(function(key) { return key.Id === orign[i].Id; }))
				{
					dest.push(orign[i]);
					orign.splice(i, 1);
					i--;
				}
			}
		};

		var orderColumnsByKeys = function(columns, keys)
		{
			var columnsCopy = columns.splice(0, columns.length);

			keys.forEach(function(key)
			{
				var column = columnsCopy.filter(function(col) { return col.Id === key.Id })[0];
				if (column)
				{
					columns.push(column);
				}
			});
		};

		this._unassignedEntity = this._rawUnassignedEntity;

		var rightColKeys = _assignedEntityGrid.dataSource.data().map(getColKeys);
		var leftColKeys = _unassignedEntityGrid.dataSource.data().map(getColKeys);

		switchColumns(this._unassignedEntity, this._assignedEntity, rightColKeys, true);
		switchColumns(this._assignedEntity, this._unassignedEntity, leftColKeys, false);

		orderColumnsByKeys(this._unassignedEntity, leftColKeys);
		orderColumnsByKeys(this._assignedEntity, rightColKeys);
	};

	KendoListMoverViewModel.prototype._moveItem = function(selectedItemUids, depDataSource, distDataSource, moveLeft)
	{
		if (!selectedItemUids || selectedItemUids.length === 0)
		{
			return;
		}

		var selectedRows = [];
		for (var i = 0; i < selectedItemUids.length; i++)
		{
			selectedRows.push(depDataSource.getByUid(selectedItemUids[i]));
		}

		if (selectedRows.length > 0)
		{
			this.pageLevelViewModel.obValidationErrorsSpecifed([]);
		}

		var removeData = [];
		var newItems = [];
		var exceptionStudent = [];
		for (var i = 0; i < selectedRows.length; i++)
		{
			removeData.push(selectedRows[i]);

			if (!moveLeft)
			{
				if ((selectedRows[i].Xcoord == null || selectedRows[i].Xcoord == 0) && this._unassignedUngeoEntity)
				{
					for (var idx = 0; idx < this._unassignedUngeoEntity.length; idx++)
					{
						if (this._unassignedUngeoEntity[idx].Id === selectedRows[i].Id)
						{
							this._unassignedUngeoEntity.splice(idx, 1);
							break;
						}
					}
				}
			}
			else
			{
				if ((selectedRows[i].Xcoord == null || selectedRows[i].Xcoord == 0) && this._unassignedUngeoEntity && !!selectedRows[i].RequirementID)
				{
					this._unassignedUngeoEntity.push(selectedRows[i]);
					if (!this.obShowEnabled() && !this.options.showUngeocodedStudent)
					{
						continue;
					}
				}
				if (!selectedRows[i].RequirementID && !!this.exceptionData)
				{
					exceptionStudent.push(selectedRows[i]);
				}
			}

			newItems.push(selectedRows[i]);
		}

		distDataSource.data(distDataSource.data().slice().concat(newItems));

		depDataSource.data(depDataSource.data().filter(function(c)
		{
			return removeData.indexOf(c) < 0;
		}));

		depDataSource.transport.data = depDataSource.data();

		this._processFilterDataSource();

		_clearLeftSelection();
		_clearRightSelection();

		this.createKendoDropTargetEvent();

		this.updateExceptionStudents(exceptionStudent);
		this.obunassignedEntity(_unassignedEntityGrid.dataSource.data());
		this.obassignedEntity(_assignedEntityGrid.dataSource.data());
	};

	KendoListMoverViewModel.prototype.updateExceptionStudents = function(exceptionStudent)
	{
		exceptionStudent.forEach(item =>
		{
			if (!this.exceptionStudents.some(x => x.id == item.id))
			{
				this.exceptionStudents.push(item);
			}
		});
	}

	KendoListMoverViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
		_unassignedEntityGrid.destroy();
		_assignedEntityGrid.destroy();
	};

	KendoListMoverViewModel.prototype._processFilterDataSource = function()
	{
		var kendoAutoComplateContainers = $($('.unassignedEntity  .k-filtercell')).find('input');
		for (var j = 0; j < kendoAutoComplateContainers.length; j++)
		{
			var tmpAutoComplate;
			if ($(kendoAutoComplateContainers[j]).data('kendoAutoComplete'))
				tmpAutoComplate = $(kendoAutoComplateContainers[j]).data('kendoAutoComplete');
			// else if($(kendoAutoComplateContainers[j]).data('kendoNumericTextBox'))
			// 	tmpAutoComplate = $(kendoAutoComplateContainers[j]).data('kendoNumericTextBox');
			if (!tmpAutoComplate)
				continue;
			tmpAutoComplate.dataSource.data(_unassignedEntityGrid.dataSource.data());
			tmpAutoComplate.dataSource.transport.data = tmpAutoComplate.dataSource.data();
		}
		_unassignedEntityGrid.dataSource.transport.data = _unassignedEntityGrid.dataSource.data();
	};

	KendoListMoverViewModel.prototype._moveItemUpDown = function(targetIdx)
	{
		var selectedRows = _getDataRowsBySelectedUids(_obRightGridSelectedUids(), _assignedEntityGrid.dataSource);

		var gridDataSource = _assignedEntityGrid.dataSource;
		var gridData = gridDataSource.data();

		var insertBefore = Enumerable.From(gridData.slice(0, targetIdx)).Except(selectedRows).ToArray();
		var insertAfter = Enumerable.From(gridData.slice(targetIdx)).Except(selectedRows).ToArray();

		_assignedEntityGrid.dataSource.data([insertBefore, selectedRows, insertAfter].reduce(function(a, b) { return a.concat(b); }, []));

		_hightLightSelectedItems();

		this.createKendoDropTargetEvent();
	};

	KendoListMoverViewModel.prototype.filterMenuClick = function(viewModel, e)
	{
		return TF.Helper.KendoListMoverHelper.filterMenuClick.call(this, viewModel, e);
	};

	KendoListMoverViewModel.prototype.setImage = function(item)
	{
		return TF.Control.EditPhotoViewModel.prototype.getImage("student", item.Id)
			.then(function(image)
			{
				return item.RawImage = 'data:image/jpeg;base64,' + image;
			}.bind(this));
	};

	KendoListMoverViewModel.prototype.addClick = function(viewModel, e)
	{
		this.options.addItem.call(this).then(function(item)
		{
			if (item)
			{
				this._unassignedEntity.push(item);
				this._rawUnassignedEntity.push(item);
				this.obunassignedEntity.push(item);
			}
		}.bind(this));
	};

	KendoListMoverViewModel.prototype.sortGridForLastName = function()
	{
		_assignedEntityGrid.dataSource.sort(this.configure.sort);
		_unassignedEntityGrid.dataSource.sort(this.configure.sort);
	};

	var _selectedDrop = function(e)
	{

		e.draggable.hint.hide();
		if (e.draggable.currentTarget.hasClass(TF.KendoClasses.STATE.SELECTED))
		{
			if (!this.obLeftGridSelected() &&
				!this.obRightGridSelected())
			{
				_assignedEntityGrid.clearSelection();
				return;
			}

			var insertIdx = _getInsertIdx($(document.elementFromPoint(e.clientX, e.clientY)));

			if (this.obLeftGridSelected())
			{
				var tmp = _obLeftGridSelectedUids().slice();
				this._moveItem(_obLeftGridSelectedUids(), _unassignedEntityGrid.dataSource, _assignedEntityGrid.dataSource);
				_obRightGridSelectedUids(tmp);
				this._moveItemUpDown(insertIdx);
			}
			else
			{
				this._moveItemUpDown(insertIdx);
			}
		}
		else
		{
			var insertIdx = _getInsertIdx($(document.elementFromPoint(e.clientX, e.clientY)));
			var selectedUids = [e.draggable.currentTarget.data().kendoUid];
			if (e.draggable.element.hasClass("unassignedEntity"))
			{
				this._moveItem(selectedUids, _unassignedEntityGrid.dataSource, _assignedEntityGrid.dataSource);
			}
			_obRightGridSelectedUids(selectedUids);
			this._moveItemUpDown(insertIdx);
		}
	};

	var _getInsertIdx = function(dest)
	{
		var insertIdx = 0;

		if (dest.closest(".k-table-th").length > 0)
		{
			insertIdx = 0;
		}
		else
		{
			var destData = _assignedEntityGrid.dataSource.getByUid(dest.parent().data(_KendoUid));
			var gridData = _assignedEntityGrid.dataSource.data();

			insertIdx = gridData.length;
			if (destData && gridData)
			{

				gridData.forEach(function(col, idx)
				{
					if (col === destData)
					{
						insertIdx = Math.min(idx + 1, gridData.length);
						return;
					}
				});
			}
		}

		return insertIdx;
	};

	var _sortByDisplayName = function(a, b)
	{
		var x, y;
		x = a[_DataFiledName] ? a[_DataFiledName].toLowerCase() : '';
		y = b[_DataFiledName] ? b[_DataFiledName].toLowerCase() : '';
		return (x == y ? 0 : (x > y ? 1 : -1));
	};

	var _getUids = function(dataSource)
	{
		var uids = [];
		if (dataSource.data().length === 0)
		{
			return uids;
		}

		uids = $.map(dataSource.data(), function(dataItem)
		{
			return dataItem.uid;
		});

		return uids;
	};

	var _sortUnassignedGrid = function()
	{
		_unassignedEntityGrid.dataSource.sort({ field: _DataFiledName, dir: "asc" });
		_unassignedEntityGrid.dataSource.data().sort(_sortByDisplayName);
	};

	var _getDataRowsBySelectedUids = function(selectedUids, dataSource)
	{
		var dataRows = $.map(selectedUids, function(uid)
		{
			return dataSource.getByUid(uid);
		}.bind(this));
		return dataRows;
	};

	var _hightLightSelectedItems = function()
	{
		var items = _assignedEntityGrid.items();
		_obRightGridSelectedUids().forEach(function(uid)
		{
			$.map(items, function(item)
			{
				if (item.dataset[_KendoUid] == uid)
				{
					_assignedEntityGrid.select(item);
					return;
				}
			});
		});
	};

	var _removeDropTargetCursorTriangle = function()
	{
		$('#left-triangle').remove();
		$('#right-triangle').remove();
	};

	var _appendDropTargetCursorTriangle = function(targetItem, insertBeforeTarget)
	{
		var leftTriangle = $('<div id="left-triangle"></div>').addClass('drag-target-cursor-left-triangle');
		var rightTriangle = $('<div id="right-triangle"></div>').addClass('drag-target-cursor-right-triangle');

		leftTriangle.css("left", -1 + "px");
		rightTriangle.css("left", targetItem.width() - 14 + "px");

		if (insertBeforeTarget)
		{
			leftTriangle.css("top", "-6px");
			rightTriangle.css("top", "-6px");
		}


		targetItem.find('td:first').append(leftTriangle);
		targetItem.find('td:first').append(rightTriangle);
	};

	var _getHintElements = function(item, container, _assignedEntity)
	{
		var hintElements = $('<div class="k-grid list-mover-drag-hint" style=""><table><tbody></tbody></table></div>'),
			maxWidth = container.width(), tooLong = false;
		hintElements.css({
			"background-color": "#FFFFCE",
			"opacity": 0.8,
			"cursor": "move"
		});
		if (_assignedEntity == undefined)
		{
			tooLong = $(item).width() > maxWidth;
			hintElements.width(tooLong ? maxWidth : $(item).width());
			hintElements.find('tbody').append('<tr>' + (tooLong ? $(item.html())[0].outerHTML : item.html()) + '</tr>');
		}
		else
		{
			for (var i = 0; i < _assignedEntity.length; i++)
			{
				if (_assignedEntity[i].tagName === "SPAN") continue;
				tooLong = $(_assignedEntity[i]).width() > maxWidth;
				hintElements.width(tooLong ? maxWidth : $(_assignedEntity[i]).width());
				hintElements.find('tbody').append('<tr>' + (tooLong ? $($(_assignedEntity[i]).html())[0].outerHTML : $(_assignedEntity[i]).html()) + '</tr>');
			}
		}

		return hintElements;
	};

	var _clearRightSelection = function()
	{
		_obRightGridSelectedUids([]);
		_assignedEntityGrid.clearSelection();
	};

	var _clearLeftSelection = function()
	{
		_obLeftGridSelectedUids([]);
		_unassignedEntityGrid.clearSelection();
	};
})();