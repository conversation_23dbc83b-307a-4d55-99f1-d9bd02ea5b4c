
(function($)
{
	// shorten references to variables. this is better for uglification
	var kendo = window.kendo,
		ui = kendo.ui,
		Widget = ui.Widget,
		proxy = $.proxy,
		CHANGE = "change",
		extractFormat = kendo._extractFormat,
		isArray = $.isArray,
		NS = ".kendoCustomizedDateTimePicker",
		parse = kendo.parseDate,
		DATE = Date,
		TODAY = new DATE();

	TODAY = new DATE(TODAY.getFullYear(), TODAY.getMonth(), TODAY.getDate(), 0, 0, 0);

	var customizedTimePickerWidget = Widget.extend({
		init: function(element, options)
		{
			var that = this;
			var dateTimeBox = (new TF.Input.DateTimeBox(null, null, null, true, true)).getElement();
			$(element).append(dateTimeBox);
			Widget.fn.init.call(that, element, options);

			element = that.wrapper = that.element;
			options = that.options;

			normalize(options);

			that.inputElement = element.find(".datepickerinput");
			that.inputElement
				.on("blur" + NS, proxy(that._blur, that))
			that.inputElement
				.on("focus" + NS, proxy(that._focus, that))

			kendo.notify(that);
		},
		options: {
			name: "CustomizedDateTimePicker",
			icon: "",
			spriteCssClass: "",
			imageUrl: "",
			parseFormats: ["MM/dd/yyyy hh:mm tt", "MM/dd/yyyy hh:mm:ss tt"],
			enable: true
		},
		_blur: function()
		{
			var that = this;

			//to debug and find the real element
			that._change(that.inputElement.val());
		},
		_focus: function()
		{
			var that = this;
			var value = that.inputElement.val();
			var prefix = getOperatorName(that.inputElement);
			if (prefix && value.indexOf(prefix) > -1)
			{
				value = value.replace(prefix, "");
			}

			that.inputElement.val(value);
		},
		_change: function(value)
		{
			var that = this;

			const assignOperatorName = function()
			{
				let operatorName = getOperatorName(that.inputElement);
				let kendoDateTimePicker = that.inputElement.data('kendoDateTimePicker');
				let format = kendoDateTimePicker.options.format;
				if (operatorName && value?.toString().indexOf(operatorName) == -1)
				{
					that.inputElement.val(operatorName + kendo.format("{0:" + format + "}", moment(value).toDate()));
				}
			}

			value = that._parse(value);
			that._value = value;
			var left = that._old && that._old.toString();
			var right = value && value.toString();

			if (left !== right)
			{
				that._old = value;
				assignOperatorName();
				that.trigger(CHANGE);
			}
			else if (!!value)
			{
				assignOperatorName();
			}
		},
		_parse: function(value)
		{
			var that = this,
				options = that.options,
				current = that._value || TODAY;

			if (value instanceof DATE)
			{
				return value;
			}
			let toValidValue = value;
			if (withPrefix(value)) // for "On" prefix
			{
				toValidValue = RemovePrefix(value);
			}
			value = parse(toValidValue, options.parseFormats, options.culture);

			if (value)
			{
				var isTimeColumn = TF.DateTimeBoxHelper.testIsTimeColumn(this.element);
				if (isTimeColumn)
					value = new DATE(current.getFullYear(), current.getMonth(), current.getDate(),
						value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());
				//else
				// 					value = new DATE(value.getFullYear(), value.getMonth(), value.getDate(),
				//                                  value.getHours(), value.getMinutes(), value.getSeconds(), value.getMilliseconds());
				//value = new DATE(toISOStringWithoutTimeZone(moment(value)));
			}

			return value;
		},
		value: function(value)
		{
			var that = this;

			if (value === undefined)
			{
				return that._value;
			}
			if (that._old !== value || value === null)
			{
				that._old = that._value;
				that._value = value;

				if (moment(value).isValid())
				{
					var kendoDateTimePicker = that.inputElement.data('kendoDateTimePicker');
					kendoDateTimePicker.value(moment(value).toDate());
					var operatorName = getOperatorName(that.inputElement);
					operatorName && that.inputElement.val(operatorName + kendo.format("{0:" + kendoDateTimePicker.options.format + "}", moment(value).toDate()));
				}
				else
				{
					that.inputElement.val('');
				}
			}
		}
	});

	function withPrefix(value)
	{
		if (!value)
		{
			return false;
		}

		let result = null;
		let dateTimePrefix = ['On ', 'On or After ', 'On or Before '];
		dateTimePrefix.forEach((item) =>
		{
			if (value.startsWith(item, value))
			{
				result = item;
			}
		});
		return result;
	}

	function RemovePrefix(value)
	{
		if (!!value)
		{
			let prefix = withPrefix(value);
			if (prefix)
			{
				return value.replace(prefix, '');
			}
		}

		return value;
	}

	function normalize(options)
	{
		var parseFormats = options.parseFormats;

		options.format = extractFormat(options.format || kendo.getCulture(options.culture).calendars.standard.patterns.t);

		parseFormats = isArray(parseFormats) ? parseFormats : [parseFormats];
		parseFormats.splice(0, 0, options.format);
		options.parseFormats = parseFormats;
	};

	function getOperatorName(element)
	{
		var dateTimeDateParamFiltersNames = ['On X', 'On or After X', 'On or Before X'];
		var name = element.parent()?.parent()?.find('input.k-dropdown-operator')?.attr("aria-label");
		if (dateTimeDateParamFiltersNames.includes(name))
		{
			return name.replace("X", "");
		}
		else
		{
			return "";
		}
	}

	kendo.ui.plugin(customizedTimePickerWidget);

})(jQuery);