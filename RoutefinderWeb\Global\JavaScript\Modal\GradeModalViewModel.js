﻿(function()
{
	createNamespace('TF.Modal').GradeModalViewModel = GradeModalViewModel;

	function GradeModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/gradecontrol');
		this.buttonTemplate('modal/positivenegative');
		this.gradeViewModel = new TF.Control.GradeViewModel(options);
		this.data(this.gradeViewModel);
		this.sizeCss = "modal-dialog-sm";

		var viewTitle;

		///this is going to check if the popup form is add new records or edit an existing record
		if (options.id)
		{
			viewTitle = tf.applicationTerm.getApplicationTermSingularByName("Edit Grade");
		}
		else
		{
			viewTitle = tf.applicationTerm.getApplicationTermSingularByName("Add Grade");
			this.buttonTemplate('modal/positivenegativeextend');
		}

		this.title(viewTitle);

		this.containerLoaded = ko.observable(false);
	}

	GradeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	GradeModalViewModel.prototype.constructor = GradeModalViewModel;

	GradeModalViewModel.prototype.positiveClick = function()
	{
		this.obDisableControl(true);
		this.gradeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this))
			.finally(() => 
			{
				this.obDisableControl(false);
			});
	};

	GradeModalViewModel.prototype.saveAndNewClick = function()
	{
		const self = this;
		self.gradeViewModel.apply().then(function(result)
		{
			if (result)
			{
				self.obDisableControl(true);
				tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `grades`))
					.then(function(response)
					{
						const grades = response.Items;
						self.gradeViewModel.grades = grades;
						self.gradeViewModel.obEntityDataModel(new TF.DataModel.GradeDataModel());
						self.gradeViewModel.initFeedToDropdownList();
						self.newDataList.push(result);
						PubSub.publish(topicCombine(pb.DATA_CHANGE, "listmover"));
					}).finally(() => 
					{
						if ($("input[name=code]") && $("input[name=code]").length > 0)
						{
							$("input[name=code]").focus();
						}
						self.obDisableControl(false);
					});
			}
		});
	};

	GradeModalViewModel.prototype.dispose = function()
	{
		this.gradeViewModel.dispose();
	};

})();
