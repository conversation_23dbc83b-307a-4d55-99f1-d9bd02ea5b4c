﻿(function($)
{
	jQuery.fn.Popover = function(options)
	{
		options = options || {};

		var viewModel = {
			templateName: options.templateName,
			data: options
		};

		// define some constant
		var HEADER_HEIGHT = 40;
		var EVENT_HEIGHT = 100;
		var ARROW_HEIGHT = 13;
		var POPOVER_WIDTH = 450;
		var MAX_HEIGHT = 140;

		// set timer
		this.mouseenter(function(e)
		{
			var enterTime = moment();
			var target = $(this);
			target.data('timer-triggered', false);
			target.data("element", e.currentTarget)
			var timer = setTimeout(function()
			{
				$(".popover-wrapper").hide();
				triggerPopoverEnter.apply(target[0]);
				target.data('timer-triggered', true);
			}, 500, this);
			target.data('timer', timer);
		})
		.mouseleave(function()
		{
			var target = $(this);
			var timer = target.data('timer');
			clearTimeout(timer);
			target.data('timer', null);
			if (target.data('timer-triggered'))
			{
				setTimeout(function()
				{
					var popover = target.data('popover');
					var hasMouse = (popover && popover.data('has-mouse')) || false;
					// do not close the popover if it has mouse focus
					if (!hasMouse)
						triggerPopoverLeave.apply(target[0]);
				}, 100);
			}
		})
		.mousemove(function(e)
		{
			var target = $(this);
			target.data('mouse', e);
		});

		// bind target event
		function triggerPopoverEnter(e)
		{
			var target = $(this);
			//var popover = target.data('popover');
			var template = $('<div class="popover-wrapper" data-bind="template: {name: templateName, data: data}" /></div>')
										.mouseenter(function()
										{
											// prevent destorying popover when mouse on it
											$(this).data('has-mouse', true);
										})
										.mouseleave(function()
										{
											// destory this popover on loosing mouse
											$(this).remove();
										});

			var elements = $('[class="doc wrapper"]');
			elements.filter(function() { return $(this).css('visibility') != 'hidden' && $(this).css('display') != 'none' }).append(template);
			options.load(target.data("element"))
			.then(function()
			{
				// calculate position
				var offset = { top: target.data('mouse').pageY, left: 0 };
				var popoverWidth = options.MAX_WIDTH ? options.MAX_WIDTH : POPOVER_WIDTH;
				if (offset.top < (elements.height() / 2))
				{
					// popover to the bottom
					viewModel.data.arrowcss = 'arrow_box_top';
					offset.left = target.data('mouse').pageX - popoverWidth * 0.8;
					offset.top += ARROW_HEIGHT;
				}
				else
				{
					// popover to the top
					viewModel.data.arrowcss = 'arrow_box_bottom';
					offset.left = target.data('mouse').pageX - popoverWidth * 0.93;
					offset.top -= options.MAX_HEIGHT + ARROW_HEIGHT;
				}

				template.offset(offset);
				if (options.MAX_WIDTH)
				{
					template.css("width", options.MAX_WIDTH + "px");
				}
				ko.applyBindings(viewModel, template[0]);

			}.bind(this));

			target.data('popover', template);
		}

		function triggerPopoverLeave()
		{
			var target = $(this);
			var popover = target.data('popover');
			if (popover !== undefined && popover !== null)
			{
				popover.remove();
				target.data('popover', null);
			}
		}
	}
})(jQuery);