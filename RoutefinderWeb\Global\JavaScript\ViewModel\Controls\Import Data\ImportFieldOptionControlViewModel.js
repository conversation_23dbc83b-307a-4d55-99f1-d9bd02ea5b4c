(function()
{
	createNamespace('TF.Control').ImportFieldOptionControlViewModel = ImportFieldOptionControlViewModel;

	function ImportFieldOptionControlViewModel(fieldOption, shortCutKeyName)
	{
		var self = this,
			defaultOption = {
				"InputFieldName": "Import Field Name",
				"IsPrimaryKey": false,
				"Ungeocode": false,
				"FieldOption": 0
			};

		fieldOption = fieldOption ? {
			"InputFieldName": fieldOption.InputFieldName,
			"IsPrimaryKey": fieldOption.IsPrimaryKey,
			"Ungeocode": fieldOption.Ungeocode,
			"FieldOption": fieldOption.FieldAction
		} : defaultOption;
		self.fieldOption = fieldOption;

		self.obField = ko.observable(fieldOption.InputFieldName);
		self.obMatch = ko.observable(fieldOption.IsPrimaryKey);
		self.obGeocoding = ko.observable(fieldOption.Ungeocode);

		self.obAllImportOptions = [
			{ value: 0, text: "Import Field" },
			{ value: 1, text: "Ignore Field" },
			{ value: 2, text: "Use if Routefinder blank" },
			{ value: 3, text: "Use if Import is not blank" }
		];
		var fieldOptions = Enumerable.From(self.obAllImportOptions).Where(function(c)
		{
			return c.value === fieldOption.FieldOption;
		}).ToArray();
		self.obSelectImportOption = ko.observable(fieldOptions[0]);
		self.obSelectedImportOptionText = ko.observable(fieldOptions[0].text);

		self.obMatch.subscribe(function(value)
		{
			self.fieldOption.IsPrimaryKey = value;
		});
		self.obGeocoding.subscribe(function(value)
		{
			self.fieldOption.Ungeocode = value;
		});
		self.obSelectImportOption.subscribe(function(option)
		{
			self.fieldOption.FieldOption = option.value;
		});

		tf.shortCutKeys.bind("alt+k", self.matchCheckboxClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+g", self.geocodingCheckboxClick.bind(self), shortCutKeyName);
	};

	/**
	 * The event of change the match checkbox status.
	 * @return {void}
	 */
	ImportFieldOptionControlViewModel.prototype.matchCheckboxClick = function()
	{
		var self = this;
		self.obMatch(!self.obMatch());

		return false;
	};

	/**
	 * The event of change the geocoding checkbox status.
	 * @return {void}
	 */
	ImportFieldOptionControlViewModel.prototype.geocodingCheckboxClick = function()
	{
		var self = this;
		self.obGeocoding(!self.obGeocoding());

		return false;
	};

})();

