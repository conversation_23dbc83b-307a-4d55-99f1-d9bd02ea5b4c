﻿(function()
{
	createNamespace("TF.Control").EditEmailMergeTemplateTypeViewModel = EditEmailMergeTemplateTypeViewModel;

	function EditEmailMergeTemplateTypeViewModel(model, options)
	{
		var self = this;
		self.obSelectedTemplate = ko.observable(model);
		self.originalTemplate = self.obSelectedTemplate().toData();
		self.obTemplateNameEditable = ko.observable(options.templateNameEditable === undefined ? true : options.templateNameEditable);
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		self.roleTypeHelper = new TF.Helper.RoleTypeHelper();
		self.typeRoles = [];
		self.singleName = options.singleName || "Merge Document";
		self.dataType = options.dataType;
	}

	EditEmailMergeTemplateTypeViewModel.prototype.init = async function(viewModel, el)
	{
		const self = this;
		self.el = el;
		self.typeRoles = await self.roleTypeHelper.initRolesAccessUI($(el), self.obSelectedTemplate().roleMergeDocuments() || [], self.dataType, 'save');
	};

	EditEmailMergeTemplateTypeViewModel.prototype.save = function(viewModel, e)
	{
		const selectRoles = this.typeRoles && this.typeRoles.value() || [];
		const roleMergeDocuments = selectRoles.filter(role => role >= 0).map(role =>
		{
			return { RoleID: role }
		});
		this.obSelectedTemplate().roleMergeDocuments(roleMergeDocuments);
		return Promise.resolve(true);
	};

	EditEmailMergeTemplateTypeViewModel.prototype.dispose = function()
	{
		if (this.pageLevelViewModel)
		{
			this.pageLevelViewModel.dispose();
			this.pageLevelViewModel = null;
		}

		if (this.validator)
		{
			this.validator.destroy();
			this.validator = null;
		}

		this.typeRoles && this.typeRoles.destroy();
	};
})();

