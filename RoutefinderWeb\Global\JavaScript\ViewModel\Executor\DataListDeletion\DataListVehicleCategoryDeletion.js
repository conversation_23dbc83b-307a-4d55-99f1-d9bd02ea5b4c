﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleCategoryDeletion = DataListVehicleCategoryDeletion;

	function DataListVehicleCategoryDeletion()
	{
		this.type = 'vehiclecategory';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Category';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleCategoryDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleCategoryDeletion.prototype.constructor = DataListVehicleCategoryDeletion;

	DataListVehicleCategoryDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?categoryIds=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleCategoryDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclecategory", pb.DELETE), ids);
	}

})();