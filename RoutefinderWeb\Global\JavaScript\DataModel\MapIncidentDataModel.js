(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.MapIncidentDataModel = function(recordEntity)
	{
		namespace.BaseDataModel.call(this, recordEntity);
	}

	namespace.MapIncidentDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.MapIncidentDataModel.prototype.constructor = namespace.MapIncidentDataModel;

	namespace.MapIncidentDataModel.prototype.mapping = [
		{ "from": "Id", "default": 0 },
		{ "from": "ActiveFrom", "default": function() { return utcToClientTimeZone(moment().utc()).format("YYYY-MM-DDTHH:mm") } },
		{ "from": "ActiveTo", "default": null },
		{ "from": "CreatedBy", "default": 0 },
		{ "from": "CreatedOn", "default": null },
		{ "from": "IncidentType", "default": 0 },
		{ "from": "IncidentTypeName", "default": "" },
		{ "from": "XCoord", "default": null },
		{ "from": "YCoord", "default": null },
		{ "from": "Source", "default": "Routefinder PLUS" },
		{ "from": "CreatedInDBID", "default": function() { return tf.datasourceManager.databaseId } },
		{ "from": "CreatedInTripId", "default": null },
		{ "from": "CreatedInDatasourceName", "default": function() { return tf.datasourceManager.databaseName.replace('(', '').replace(')', '') } },
		{ "from": "CreatedInTripName", "default": "" }
	];
})();