(function()
{
	createNamespace("TF.Modal.Grid").FindScheduleForStudentModalViewModel = FindScheduleForStudentModalViewModel;

	function FindScheduleForStudentModalViewModel(studentGridViewModel, options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.studentGridViewModel = studentGridViewModel;
		self.sizeCss = "modal-dialog-sm";
		self.title("Find Student Schedule");
		self.contentTemplate("Modal/FindScheduleForStudent");
		self.buttonTemplate("modal/positive");
		self.obPositiveButtonLabel("Stop");
		self.model = new TF.Control.FindScheduleForStudentViewModel(options, self);
		self.data(self.model);
	}
	FindScheduleForStudentModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	FindScheduleForStudentModalViewModel.prototype.constructor = FindScheduleForStudentModalViewModel;

	FindScheduleForStudentModalViewModel.prototype.positiveClick = function()
	{
		this.tryToClose();
	};

	FindScheduleForStudentModalViewModel.prototype.tryToClose = function(prompt)
	{
		var self = this;
		self.model.tryToClose(prompt).then(function(close)
		{
			if (close) self.positiveClose();
		});
	};

	FindScheduleForStudentModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.tryToClose(true);
	};
})();