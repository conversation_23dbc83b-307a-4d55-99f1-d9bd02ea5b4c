﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.SchoolDeletion = SchoolDeletion;

	function SchoolDeletion()
	{
		this.type = 'school';
		this.disable = false;
		namespace.BaseDeletion.apply(this, arguments);
	}

	SchoolDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	SchoolDeletion.prototype.constructor = SchoolDeletion;

	SchoolDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "relatedstudent", "ids", "school"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'student',
				items: response.Items[0]
			});
		});
		var p1 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "trip", "ids", "tripbyschoolIds"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'trip',
				items: response.Items[0]
			});
		});
		var p2 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtriptemplate", "ids", "school"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'fieldtriptemplate',
				items: response.Items[0]
			});
		});
		var p3 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix("V2"), "redistricts", "ids", "school"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'redistrict',
				items: response.Items[0]
			});
		});
		var p4 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"), {
			paramData: {
				"@filter": "in(Id," + ids.join(",") + ")",
				"@fields": "Id"
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'district',
				items: response.Items.filter(function(item)
				{
					return !!item.DistrictIdString;
				})
			});
		});
		var p5 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtrip", "related", "ids", "school"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'fieldtrip',
				items: response.Items[0]
			});
		});
		var p7 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
				"AttachedToType": tf.dataTypeHelper.getId("school")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});

		return Promise.all([p0, p1, p2, p3, p4, p5]).then(function()
		{
			return associatedDatas;
		}.bind(this));
	};


	SchoolDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	SchoolDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "deltastuds", "school");

		return Promise.all([p0, p1]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};

})();