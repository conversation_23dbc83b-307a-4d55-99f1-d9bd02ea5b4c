﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleBrakeTypeDeletion = DataListVehicleBrakeTypeDeletion;

	function DataListVehicleBrakeTypeDeletion()
	{
		this.type = 'vehiclebraketype';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Brake Type';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleBrakeTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleBrakeTypeDeletion.prototype.constructor = DataListVehicleBrakeTypeDeletion;

	DataListVehicleBrakeTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?BrakeTypeId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleBrakeTypeDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclebraketype", pb.DELETE), ids);
	}

	DataListVehicleBrakeTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();