/*
* TF.Map.LayerTool
*
* Required:
* TF.Events.Event
* TF.RoutingMap.MenuItem
*/

(function()
{
	createNamespace("TF.Map").LayerTool = LayerTool;

	function LayerTool(options)
	{
		var self = this;
		self.uid = options.uid;
		self.map = options.map;
		self.menuItem = null;
		self.onLayerSelected = new TF.Events.Event();
	}

	LayerTool.prototype.initialize = function()
	{
		var self = this;
		self.menuItem = new TF.RoutingMap.MenuItem({
			header: 'Layers',
			icon: 'layers',
			click: self.click.bind(self),
			children: []
		});
		return self.menuItem;
	}

	LayerTool.prototype.updateLayerItem = function(layers)
	{
		const self = this, menuItemChildren = [];

		layers.forEach((item, index) =>
		{
			if (item === null) return;

			const child = new TF.RoutingMap.MenuItem({
				toggleStatus: ko.observable(false),
				header: item.name,
				activate: !!item.visible,
				closable: false,
				allowMultiple: true,
				config: {
					index: index
				},
				click: self.layerClick.bind(self)
			});
			menuItemChildren.push(child);
		});
		self.menuItem.children = menuItemChildren;
	}

	LayerTool.prototype.click = function(menuItem, event)
	{
		menuItem.children.forEach(subMenuItem => 
		{
			if (subMenuItem.config.activate)
			{
				this.activateLayerItem(subMenuItem);
			}
		});
		return;
	}

	LayerTool.prototype.layerClick = function(menuItem)
	{
		if (!menuItem.allowMultiple && menuItem.config.activate) return;  // layer is already shown,don't refresh it.

		var self = this;
		menuItem.config.activate = menuItem.allowMultiple ? !menuItem.config.activate : true;
		const layerIndex = menuItem.config.config.index;
		const isActivate = menuItem.config.activate;
		if (!menuItem.allowMultiple)
		{
			if (isActivate)
			{
				self.activateLayerItem(menuItem);

				// inactive other layers.
				self.menuItem.children.filter(child => child.header !== menuItem.header || child.config.config.index !== layerIndex)
					.map(item => 
					{
						self.inactiveLayerItem(item);
						item.config.activate = false;
					});
			}
			else
			{
				menuItem.toggleStatus(isActivate);
			}
		}
		else
		{
			menuItem.toggleStatus(isActivate);
			isActivate ? self.activateLayerItem(menuItem) : self.inactiveLayerItem(menuItem);
		}

		self.onLayerSelected.notify(self.menuItem.children.map((item) => { return { layer: item.header, visible: item.config.activate, index: item.config.config.index } }));
	}

	LayerTool.prototype.activateLayerItem = function(menuItem)
	{
		const $item = $(menuItem.html[0]);
		$item.find(".text").css("font-weight", "bold");
		$item.find(".check").css("display", "block");
	}

	LayerTool.prototype.inactiveLayerItem = function(menuItem)
	{
		const $item = $(menuItem.html[0]);
		$item.find(".text").css("font-weight", "normal");
		$item.find(".check").css("display", "none");
	}

	LayerTool.prototype.dispose = function()
	{
		this.menuItem = null;
		this.onLayerSelected.unsubscribeAll();
	}
})();