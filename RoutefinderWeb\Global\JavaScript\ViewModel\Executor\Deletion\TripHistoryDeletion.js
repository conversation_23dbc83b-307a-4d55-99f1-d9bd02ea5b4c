﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.TripHistoryDeletion = TripHistoryDeletion;

	function TripHistoryDeletion()
	{
		this.type = 'triphistory';
		namespace.BaseDeletion.apply(this, arguments);
	}

	TripHistoryDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	TripHistoryDeletion.prototype.constructor = TripHistoryDeletion;

	TripHistoryDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	};

	TripHistoryDeletion.prototype.deleteSingleVerify = function()
	{
		return [];
	};

})();