﻿(function()
{
	//PromiseAjax is a wrapper for Ajax when swtich from traditional jquery ajax to promise based ajax
	//resulting not so clean code, should combine PromiseAjax and ajax together sometime
	createNamespace("TF").PromiseAjax = PromiseAjax;

	function PromiseAjax(ajax, ajaxSettings)
	{
		this.ajax = ajax;
		this.ajaxSettings = $.extend({
			onResolve: function(args)
			{
				return Promise.resolve(args);
			},
			onReject: function(args)
			{
				return Promise.reject(args);
			}
		}, ajaxSettings)
	}

	PromiseAjax.prototype.generic = function(httpVerb, url, settings, option)
	{
		if (!settings)
		{
			settings = {};
		}

		var self = this;
		let processRequest = function()
		{
			return new Promise(function(resolve, reject)
			{
				Promise.resolve(self.ajax[httpVerb](url, settings, option))
					.then(function(args)
					{
						resolve(args);
					})
					.catch(function(args)
					{
						if (!args.responseJSON && args.responseText && settings.dataType && settings.dataType != 'json')
						{
							try
							{
								args.responseJSON = JSON.parse(args.responseText)
							}
							catch (ex)
							{
							}
						}

						if (option && option.reject)
						{
							option.reject(args.responseJSON, args);
							return;
						}

						if (args.responseJSON && args.status == 401 && !(option && option.auth && option.auth.noInterupt))
						{
							return;
						}

						reject({
							Message: TF.getErrorMessage(args.responseJSON),
							StatusCode: args.status
						});
					});
			})
			.then(
				self.ajaxSettings.onResolve,
				function(args)
				{
					return self.ajaxSettings.onReject({
						Message: args.Message,
						StatusCode: args.StatusCode
					})
				}
			);
		}

		if ((!option || (option && !option.isStopfinderLogin)) && tf.stopfinderUtil && tf.stopfinderUtil.isStopfinderRequest(url))
		{
			return tf.stopfinderUtil.getStopfinderToken().then(() =>
			{
				return processRequest();
			})
		}
		else
		{
			return processRequest();
		}
	}

	PromiseAjax.prototype.get = function(url, settings, option)
	{
		return this.generic("get", url, settings, option);
	};

	PromiseAjax.prototype.post = function(url, settings, option)
	{
		url = url.replace(/documentmini/g, "document");//change the documentmini to document,do not have any documentmini url in api
		return this.generic("post", url, settings, option);
	};

	PromiseAjax.prototype.put = function(url, settings, option)
	{
		return this.generic("put", url, settings, option);
	};

	PromiseAjax.prototype.delete = function(url, settings, option)
	{
		return this.generic("delete", url, settings, option);
	};

	PromiseAjax.prototype.patch = function(url, settings, option)
	{
		return this.generic("patch", url, settings, option);
	};
})()