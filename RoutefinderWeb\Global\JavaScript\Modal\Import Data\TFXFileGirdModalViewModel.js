(function()
{
	createNamespace('TF.Modal').TFXFileGirdModalViewModel = TFXFileGirdModalViewModel;

	function TFXFileGirdModalViewModel(options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-lg";
		self.contentTemplate('modal/import data/tfxfilegirdcontrol');
		self.buttonTemplate('modal/positive');
		self.title("Import File Data Preview (First 100 Records)");
		self.obPositiveButtonLabel("OK");

		self.tfxFileGirdViewModel = new TF.Control.TFXFileGirdViewModel(options);
		self.data(self.tfxFileGirdViewModel);

	}

	TFXFileGirdModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	TFXFileGirdModalViewModel.prototype.constructor = TFXFileGirdModalViewModel;

})();
