(function()
{
	createNamespace('TF.Control').STARSReportSettingViewModel = STARSReportSettingViewModel;

	const STARS_FIELDS = {
		schoolYear: {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				}
			}
		},
		coDistNo: {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: "must be digits",
					callback: function(value)
					{
						var verifyRegex = new RegExp("^\\d+$");
						return !value || verifyRegex.test(value);
					}
				},
			}
		},
		distName: {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				}
			}
		},
	};

	function STARSReportSettingViewModel()
	{
		TF.Control.BaseStateReportSettingViewModel.call(
			this,
			{
				dataType: "trip",
				customFields: STARS_FIELDS
			});

		this.schoolYear = ko.observable()
		this.coDistNo = ko.observable();
		this.distName = ko.observable();
	}

	STARSReportSettingViewModel.prototype.constructor = TF.Control.BaseStateReportSettingViewModel;
	STARSReportSettingViewModel.prototype = Object.create(TF.Control.BaseStateReportSettingViewModel.prototype);

	STARSReportSettingViewModel.prototype.apply = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(valid =>
			{
				if (!valid)
				{
					return false;
				}

				let settings = {
					CountyDistrictCode: this.coDistNo(),
					SchoolYearCode: this.schoolYear(),
					DistrictName: this.distName(),
					FilterId: this.filterId(),
					RecordIds: this.records().map(i => i.Id)
				};

				return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "statereportfiles/stars"),
					{
						data: settings,
						paramData: {
							databaseId: tf.datasourceManager.databaseId
						},
						dataType: 'text'
					})
					.then(xml =>
					{
						TF.saveStringAs(xml, 'text/xml', 'RouteImport.xml');
						return true;
					})
					.catch(res =>
					{
						res = res || {};
						tf.modalManager.showModal(new TF.Modal.ResultModalViewModel({ title: "Failed", content: res.Message || "An error happens." }));
						return false;
					});
			});
	};
})()