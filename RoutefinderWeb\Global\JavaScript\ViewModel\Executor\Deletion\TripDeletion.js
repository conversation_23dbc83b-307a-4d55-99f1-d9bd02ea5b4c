﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.TripDeletion = TripDeletion;

	function TripDeletion()
	{
		this.type = 'trip';
		namespace.BaseDeletion.apply(this, arguments);
	}

	TripDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);

	TripDeletion.prototype.constructor = TripDeletion;

	TripDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "tripstops"), {
			paramData: {
				"tripIDs": ids.join(","),
				"@fields": "Id"
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'tripstop',
				items: response.Items[0]
			});
		});

		var p1 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "student", "ids", "trip"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'student',
				items: response.Items[0]
			});
		});

		//var p2 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "deltastuds", "ids", "trip"), {
		//	data: ids
		//}).then(function(response)
		//{
		//	associatedDatas.push({
		//		type: 'deltastuds',
		//		items: response.Items[0]
		//	});
		//});

		var p3 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "attendance", "ids", "trip"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'attendance',
				items: response.Items[0]
			});
		});

		var p5 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
				"AttachedToType": tf.dataTypeHelper.getId("trip")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});

		var p6 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "trip", "ids"), {
			data: ids
		}).then(function(response)
		{
			var trips = response.Items, busAides = [], drivers = [], vehicles = [];
			$.each(trips, function(index, item)
			{
				if (item.AideId > 0 && busAides.indexOf(item.AideId) < 0)
				{
					busAides.push(item.AideId);
				}
				if (item.DriverId > 0 && drivers.indexOf(item.DriverId) < 0)
				{
					drivers.push(item.DriverId);
				}
				if (item.VehicleId > 0 && vehicles.indexOf(item.VehicleId) < 0)
				{
					vehicles.push(item.VehicleId);
				}
			});

			associatedDatas.push({
				type: 'vehicle',
				items: vehicles
			});
			associatedDatas.push({
				type: 'driver',
				items: drivers
			});
			associatedDatas.push({
				type: 'busaide',
				items: busAides
			});
		});

		return Promise.all([p0, p1, p3, p5, p6]).then(function()
		{
			return associatedDatas;
		});
	}

	TripDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		var p0 = this.getDataPermission(ids, "tripstop", "trip");

		var p1 = this.getDataPermission(ids, "attendance", "trip");

		return Promise.all([p0, p1]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	}

	TripDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "tripstop", "trip");

		var p2 = this.getDataStatus(this.ids, "student", "trip");

		var p3 = this.getDataStatus(this.ids, "deltastuds", "trip");

		var p4 = this.getDataStatus(this.ids, "attendance", "trip");

		return Promise.all([p0, p1, p2, p3, p4]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};

})();