(function()
{
	createNamespace("TF.Modal.Grid").VehicleSelectGPSIDModalViewModel = VehicleSelectGPSIDModalViewModel;

	function VehicleSelectGPSIDModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-lg";
		this.title('Select GPS ID');
		this.obPositiveButtonLabel("Apply");
		this.buttonTemplate('modal/positivenegative');
		this.contentTemplate('workspace/grid/vehicle/selectgpsid');
		this.vehicleSelectGPSIDViewModel = new TF.Control.VehicleSelectGPSIDViewModel(this.positiveClick);
		this.data(this.vehicleSelectGPSIDViewModel);
	};

	VehicleSelectGPSIDModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	VehicleSelectGPSIDModalViewModel.prototype.constructor = VehicleSelectGPSIDModalViewModel;

  VehicleSelectGPSIDModalViewModel.prototype.positiveClick = function()
  {
    this.vehicleSelectGPSIDViewModel.apply().then(function(result)
    {
      if (result)
      {
        this.positiveClose(result);
      }
    }.bind(this));
  };

})();
