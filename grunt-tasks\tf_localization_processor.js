var mustache = require("mustache"),
	path = require('path'),
	extend = require('extend')

module.exports = function(grunt)
{

	grunt.registerMultiTask('tf_localization_processor', '', function()
	{
		var options = this.options({
			partials: "",
			data: ""
		})

		var resources = {};
		var data = options.data;
		dataPathPattern = path.normalize(options.data + "/*.json")
		grunt.file.expand(dataPathPattern).forEach(function(dataPath)
		{
			try
			{
				resources[path.basename(dataPath, path.extname(dataPath))] = JSON.parse(grunt.file.read(dataPath))
			}
			catch (e)
			{
				console.warn("error when parsing " + dataPath);
			}
		})

		//render templates with mustache renderer

		debugger;
		var enUS = resources['en-US'];
		if (enUS)
		{
			for (var key in resources)
			{
				if (key != 'en-US')
				{
					resources[key] = extend(true, {}, enUS, resources[key]);
				}
			}
		}


		var counter = 0;
		for (var key in resources)
		{
			this.files.forEach(function(fileGroups)
			{
				fileGroups.src.filter(function(filepath)
				{
					var dest = path.join(options.output, key, fileGroups.dest);
					resources[key].locale = key;
					resources[key].region = key.split("-")[1];
					grunt.file.write(dest, mustache.render(grunt.file.read(filepath), resources[key], function(name)
					{
						var filePath = path.join(options.partials, name + ".mustache")
						if (grunt.file.exists(filePath))
						{
							return grunt.file.read(filePath)
						}
						return ""
					}))
					counter++;
					return true
				})
			})
		}

		// console.log("Rendered " + counter + " files for " + Object.keys(resources).join(","));
	})
};
