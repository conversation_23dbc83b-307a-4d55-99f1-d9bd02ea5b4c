﻿(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.GridBox = GridBox;

	function GridBox(initialValue, attributes, disable)
	{
		this.initialValue = !initialValue ? '' : initialValue;
		if (typeof (attributes.afterRender) === "function")
		{
			this.afterRenderCallback = attributes.afterRender;
		}
		this.gridOption = attributes.gridOption;

		namespace.StringBox.call(this, initialValue, attributes, disable);
	}

	GridBox.prototype = Object.create(namespace.StringBox.prototype);

	GridBox.constructor = GridBox;

	GridBox.prototype.type = "Grid";

	GridBox.prototype.initialize = function()
	{
		var self = this;

		var content = '<div class="input-group">';
		content += '<input type="text" class="form-control" name=' + this.type + ' data-tf-input-type=' + this.type + ' data-bind="value:obRawValue,disable:disable,style:{cursor:disable()?\'\':\'pointer\',backgroundColor:disable()?\'\':\'#fff\'}" readonly />';
		content += '<div class="input-group-btn">';
		content += '<button type="button" class="btn btn-default btn-sharp" data-bind="disabled:disable">';
		content += '<span class="glyphicon glyphicon-option-horizontal"></span>';
		content += '</button>';
		content += '</div>';
		content += '</div>';

		self.$element = $(content);

		self.$element.find("button").on("click", function(e)
		{
			if (!self.disable())
			{
				self.gridOption.selectedSource = self.$element.data("selectedSource");
				tf.modalManager.showModal(new TF.DetailView.GridFieldEditorModalViewModel(this.gridOption)).then(function(result)
				{
					if (result)
					{
						self.updateDataSource(result);
					}
					self.$element.focus();
				});
			}
		}.bind(this));

	};

	GridBox.prototype.updateDataSource = function(result)
	{
		this.obRawValue(result.text);
		this.$element.data("selectedSource", result.value);
	}

	GridBox.prototype.afterRender = function()
	{
		ko.applyBindings(this, this.$element[0]);

		if (typeof (this.afterRenderCallback) === "function")
		{
			this.afterRenderCallback();
		}
	}
})();