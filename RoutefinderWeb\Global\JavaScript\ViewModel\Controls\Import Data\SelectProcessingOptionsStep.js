(function()
{
	createNamespace("TF.ImportAndMergeData").SelectProcessingOptionsStep = SelectProcessingOptionsStep;

	function SelectProcessingOptionsStep(data)
	{
		TF.Control.BaseWizardStepViewModel.call(this, data);
		var self = this;
		self.tableUdfs = {};
		self.template = "modal/import data/SelectProcessingOptions";
		self.name("Select Processing Options");
		self.description("Specify data processing options here.");

		self.obHasAnyMatchOn = ko.observable(true);
		self.selectedDataType = ko.observable(self.data.tableConfigs[0].dataType());
		self.selectedTableConfig = ko.pureComputed(function()
		{
			var dataType = self.selectedDataType();
			return self.data.tableConfigs.find(function(item)
			{
				return item.dataType() === dataType;
			});
		});

		var dataTypeInfo = TF.ImportAndMergeData.ImportDataType.findById(self.data.tableConfigs[0].dataType());
		self.dataTypes = [dataTypeInfo];

		var gridInfo = self.data.getDataView(self.data.selectedExternalTableName(), 100);
		self.previewedData = new kendo.data.DataSource({ data: gridInfo.rows });
		self.gridColumns = gridInfo.columns;
		self.mappedColumns = self.createMappedColumns(gridInfo.columns);

		self.disposeActions = [];

		self.tableOption = ko.pureComputed(function()
		{
			var table = TF.ImportAndMergeData.ImportDataType.findById(self.selectedDataType()).displayName;
			return TF.Control.ImportDataHelper.instance.getTableOptionDefaultSetting(table);
		});

		self.disableUpdate = ko.pureComputed(function()
		{
			return self.tableOption().update.disabled;
		});

		self.disposables.push(self.disableUpdate.subscribe(function()
		{
			if (self.disableUpdate())
			{
				self.selectedTableConfig().isUpdateExisting(false);
			}
		}));

		self.obShowDelete = ko.pureComputed(function()
		{
			return self.tableOption().delete.display;
		});

		self.disableDelete = ko.pureComputed(function()
		{
			return self.tableOption().delete.disabled;
		});

		self.disposables.push(self.disableDelete.subscribe(function()
		{
			if (self.disableDelete())
			{
				self.selectedTableConfig().isDeleteNonexisting(false);
			}
		}));

		self.showFindSchedule = ko.pureComputed(function()
		{
			return self.tableOption().schedule.display;
		});

		var updateFindSchedule = function()
		{
			if (!self.showFindSchedule())
			{
				self.selectedTableConfig().needFindSchedule(false);
			}
		};

		self.disposables.push(self.showFindSchedule.subscribe(updateFindSchedule));

		self.showIncludeStopPool = ko.pureComputed(function()
		{
			return self.tableOption().stoppool.display;
		});

		self.disableIncludeStopPool = ko.pureComputed(function()
		{
			return self.tableOption().stoppool.display && !self.selectedTableConfig().isUpdateExisting();
		});

		var updateIncludeStopPool = function()
		{
			if (!self.showIncludeStopPool() || self.disableIncludeStopPool())
			{
				self.selectedTableConfig().includeStopPool(false);
			}
		};

		self.disposables.push(self.showIncludeStopPool.subscribe(updateIncludeStopPool));
		self.disposables.push(self.disableIncludeStopPool.subscribe(updateIncludeStopPool));

		self.showNeedResetLoadTimes = ko.pureComputed(function()
		{
			return self.tableOption().reset.display;
		});

		self.disableNeedResetLoadTimes = ko.pureComputed(function()
		{
			return self.tableOption().reset.display && !self.selectedTableConfig().isUpdateExisting();
		});

		var updateNeedResetLoadTimes = function()
		{
			if (!self.showNeedResetLoadTimes() || self.disableNeedResetLoadTimes())
			{
				self.selectedTableConfig().needResetLoadTimes(false);
			}
		}

		self.disposables.push(self.showNeedResetLoadTimes.subscribe(updateNeedResetLoadTimes));
		self.disposables.push(self.disableNeedResetLoadTimes.subscribe(updateNeedResetLoadTimes));

		self.showNeedFindSchoolResidence = ko.pureComputed(function()
		{
			return self.tableOption().school.display;
		});

		self.disposables.push(self.showNeedFindSchoolResidence.subscribe(function()
		{
			if (!self.showNeedFindSchoolResidence())
			{
				self.selectedTableConfig().needFindSchoolResidence(false);
			}
		}));

		self.showNeedFindPopulationRegion = ko.pureComputed(function()
		{
			return self.tableOption().school.display;
		});

		self.disposables.push(self.showNeedFindPopulationRegion.subscribe(function()
		{
			if (!self.showNeedFindPopulationRegion())
			{
				self.selectedTableConfig().needFindPopulationRegion(false);
			}
		}));

		self.enableUseStopPool = ko.pureComputed(function()
		{
			return self.selectedTableConfig().needFindSchedule() && self.selectedTableConfig().useStopPool();
		});

		self.stopPools = ko.observableArray([]);

		var updateStopPoolId = function()
		{
			if (self.enableUseStopPool() && self.stopPools().length && !self.selectedTableConfig().stopPoolId())
			{
				self.selectedTableConfig().stopPoolId(self.stopPools()[0].Id);
			}
		};

		self.disposables.push(self.enableUseStopPool.subscribe(updateStopPoolId));
		self.disposables.push(self.stopPools.subscribe(updateStopPoolId));

		self.residences = ko.observableArray([]);

		self.selectedResidences = ko.pureComputed(function()
		{
			var residenceIds = self.selectedTableConfig().residenceIds() || [], all = self.residences() || [], selected = [];
			residenceIds.forEach(function(id)
			{
				var r = all.find(function(item) { return item.Id === id });
				if (r)
				{
					selected.push(r);
				}
			});

			return selected;
		});

		self.showGeocode = ko.pureComputed(function()
		{
			return self.tableOption().geocode && self.tableOption().geocode.display;
		});
		self.obGeocodeSources = ko.observable(['Address Point', 'Street Address Range']);

		self.obAbortUngeocoded = ko.observable(!isNullObj(self.selectedTableConfig().AllowUngeocodedPercentage));
		self.obAbortUngeocoded.subscribe(self.abortUngeocodedBoxClick.bind(self, null, null, true));
		self.obAbortUngeocoded.subscribe(val =>
		{
			if (val)
			{
				setTimeout(() =>
				{
					$('input[name="UngeocodedNumber"]').data('kendoNumericTextBox').focus();
				});
			}
		});
		self.obUngeocodedNumber = ko.observable(self.selectedTableConfig().AllowUngeocodedPercentage);
		self.obAbortDeleted = ko.observable(!isNullObj(self.selectedTableConfig().AllowDeletedPercentage));
		self.obAbortDeleted.subscribe(self.abortDeletedBoxClick.bind(self, null, null, true));
		self.obAbortDeleted.subscribe(val =>
		{
			if (val)
			{
				setTimeout(() =>
				{
					$('input[name="DeletedNumber"]').data('kendoNumericTextBox').focus();
				});
			}
		});
		self.obDeletedNumber = ko.observable(self.selectedTableConfig().AllowDeletedPercentage);
		self.geocodedNumberChangeEvent = self.geocodedNumberChangeEvent.bind(self);
		self.obUngeocodedNumber.subscribe((nv) =>
		{
			self.selectedTableConfig().AllowUngeocodedPercentage = nv;
		})
		self.deletedNumberChangeEvent = self.deletedNumberChangeEvent.bind(self);
		self.obDeletedNumber.subscribe((nv) =>
		{
			self.selectedTableConfig().AllowDeletedPercentage = nv;
		})
	}

	SelectProcessingOptionsStep.prototype = Object.create(TF.Control.BaseWizardStepViewModel.prototype);

	SelectProcessingOptionsStep.prototype.constructor = SelectProcessingOptionsStep;

	SelectProcessingOptionsStep.prototype.validate = function()
	{
		var self = this,
			invalid = self.data.tableConfigs.some(function(tableConfig)
			{
				return tableConfig.dataType() == TF.ImportAndMergeData.ImportDataType.Student
					&& tableConfig.needFindSchoolResidence()
					&& (!tableConfig.residenceIds() || !tableConfig.residenceIds().length);
			}),
			invalidSafetyChecks = self.data.tableConfigs.some(function(tableConfig)
			{
				return tableConfig.dataType() == TF.ImportAndMergeData.ImportDataType.Student
					&& (self.obAbortUngeocoded() && isNullObj(self.obUngeocodedNumber())) || (self.obAbortDeleted() && isNullObj(self.obDeletedNumber()))
			});

		if (invalid)
		{
			return tf.promiseBootbox.alert({
				message: "Please select one school residence at least for importing Student data.",
				title: "Warning"
			}).then(function()
			{
				return false;
			});
		}

		if (invalidSafetyChecks)
		{
			return tf.promiseBootbox.alert({
				message: "Safety checks value is required.",
				title: "Warning"
			}).then(function()
			{
				return false;
			});
		}

		return TF.Control.BaseWizardStepViewModel.prototype.validate.call(self);
	};

	SelectProcessingOptionsStep.prototype.onMapColumnsDataBound = function(e, data)
	{
		var grid = e.sender, self = this;

		this.disposeActions.push(function()
		{
			grid.element.off("change.SelectProcessingOptionsStep");
		});

		grid.element.find(".Match-On-Selected").off(".selected").on("change.selected", function()
		{
			self.mapColumnsCheckBoxChanged(this);
			self.updateNextButtonDisable();
		});

		self.updateNextButtonDisable();

		setTimeout(function()
		{
			self.bindColumnSource(grid);
		}, 100);
	};

	SelectProcessingOptionsStep.prototype.mapColumnsCheckBoxChanged = function(checkbox)
	{
		var self = this,
			grid = self.mapColumnsGrid,
			checkBox = $(checkbox),
			$tr = checkBox.closest("tr"),
			dataItem = grid.dataItem($tr),
			target = dataItem.Target.toLowerCase();
		dataItem.MatchOn = checkBox.prop("checked");
		var data = Enumerable.From(self.selectedTableConfig().columnConfigs).FirstOrDefault(null, function(c)
		{
			return c.Target.toLowerCase() == target;
		});

		if (data !== null)
		{
			data.MatchOn = dataItem.MatchOn;
		}
	}

	SelectProcessingOptionsStep.prototype.updateNextButtonDisable = function()
	{
		const self = this,
			selected = self.mapColumnsGrid.element.find("input[class= 'Match-On-Selected']:checked");

		if (selected.length === 0)
		{
			self.obHasAnyMatchOn(false);
			return;
		}
		for (let index = 0; index < selected.length; index++)
		{
			const element = selected[index];
			let $row = $(element),
				$tr = $row.closest("tr"),
				dataItem = self.mapColumnsGrid.dataItem($tr);
			if (!dataItem.MatchOn || !dataItem.Source)
			{
				self.obHasAnyMatchOn(false);
				return;
			}
		}
		self.obHasAnyMatchOn(true);
	}

	SelectProcessingOptionsStep.prototype.initMapColumnGrid = function()
	{
		var self = this;

		self.mapColumnsGrid = self.$element.find(".Map-Columns-container").kendoGrid({
			dataSource: self.mappedColumns,
			columns: [{
				field: "Destination",
				type: "string",
				width: 80,
				template: function(item)
				{
					return '<div title="' + item.Destination + '" class="destination-text-ellipsis">' + item.Destination + '</div>'
				}
			}, {
				title: "Source",
				field: "Source",
				template: function(dataItem)
				{
					return '<div class="column-source"></div>'
				},
				width: 80
			},
			{
				title: "Match On",
				field: "MatchOn",
				template: function(dataItem)
				{
					return "<input class='Match-On-Selected' style='margin-left:0;" + (dataItem.IsUDF ? "display:none;" : "") + "' type='checkbox' " + (dataItem.MatchOn ? 'checked' : '') + "/>";
				},
				width: 40
			},
			{
				title: "Lookup Field",
				field: "LookupField",
				template: function(item)
				{
					if (item.LookupTable != null)
					{
						return '<div class="LookupField-source"></div>';
					}
					else
					{
						return $("<div>").text("<None>").html();
					}
				},
				width: 60
			},
			{
				title: "Lookup Action",
				field: "LookupAction",
				template: function(item)
				{
					if (item.LookupTable != null)
					{
						return '<div class="LookupAction-source"></div>';
					}
					else
					{
						return $("<div>").text("<None>").html();
					}
				},
				width: 70
			}],
			height: 264,
			sortable: false,
			pageable: false,
			dataBound: function(e)
			{
				self.onMapColumnsDataBound(e, self.mappedColumns);
			}
		}).data("kendoGrid");

		self.$element.find(".Map-Columns-container .k-grid-content").height(183);
	}

	SelectProcessingOptionsStep.prototype.InitResidences = function()
	{
		var self = this,
			p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "redistricts")),
			p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"));
		Promise.all([p1, p2]).then(function(responses)
		{
			var redistricts = (responses[0] || {}).Items || [],
				schools = (responses[1] || {}).Items || [],
				schoolGradeRange = {}, allSchools = [];

			schools.forEach(function(item)
			{
				schoolGradeRange[item.SchoolCode] = item.GradeRange;
			});

			redistricts.forEach(function(item)
			{
				allSchools = item.Schools.split('!');
				item.DisplaySchools = allSchools.map(function(schoolCode)
				{
					return schoolCode + (schoolGradeRange[schoolCode] ? "(" + schoolGradeRange[schoolCode] + ")" : "");
				}).join(',');
			});

			self.residences(redistricts);
		});
	};

	SelectProcessingOptionsStep.prototype.initStopPools = function()
	{
		var self = this;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "stoppoolcategories"), {
			paramData: {
				dbid: tf.datasourceManager.databaseId
			}
		}).then(function(apiResponse)
		{
			self.stopPools(apiResponse.Items);
		});
	};

	SelectProcessingOptionsStep.prototype.init = function(viewModel, element)
	{
		this.$element = $(element);
		const gridWidth = $($(this.$element[0]).find('.Data-Preview-container')[0]).width();
		this.columns = tf.helpers.kendoGridHelper.calculateColumnWidth(gridWidth, this.gridColumns);
		this.initStopPools();
		this.InitResidences();
		this.initMapColumnGrid();
	};

	SelectProcessingOptionsStep.prototype.dispose = function()
	{
		this.disposeActions.forEach(function(action)
		{
			action();
		});

		this.disposeActions = [];
		TF.Control.BaseWizardStepViewModel.prototype.dispose.call(this);
	};

	SelectProcessingOptionsStep.prototype.selectResidences = function()
	{
		var self = this;
		tf.modalManager.showModal(
			new TF.Modal.SelectBoundarySetsModalViewModel(
				self.residences(),
				self.selectedResidences()
			)
		).then(function(result)
		{
			if (result)
			{
				var resdictIds = result.map(function(res) { return res.Id; });
				self.selectedTableConfig().residenceIds(resdictIds);
			}
		});
	};

	SelectProcessingOptionsStep.prototype.resolveColumnConfigs = function(tableConfig, udfColumns)
	{
		if (!tableConfig.columnConfigs)
		{
			return;
		}

		var name = TF.ImportAndMergeData.ImportDataType.findById(tableConfig.dataType()).displayName;
		var columns = {};
		TF.Control.ImportDataHelper.instance.getColumnsByTable(name, true).forEach(function(item)
		{
			columns[item.Source.toLowerCase()] = item;
		});

		tableConfig.columnConfigs = tableConfig.columnConfigs.filter(function(columnConfig)
		{
			let name = columnConfig.Target.toLowerCase();
			columnConfig.IsUDF = !!udfColumns[name];
			return columnConfig.IsUDF || !!columns[name];
		});
	};

	SelectProcessingOptionsStep.prototype.createMappedColumns = function(sourceColumns)
	{
		var self = this,
			dataTypeInfo = TF.ImportAndMergeData.ImportDataType.findById(self.selectedDataType()),
			tableConfig = self.selectedTableConfig();
		return new kendo.data.DataSource({
			autoSync: true,
			schema: {
				model: {
					id: "Target",
					fields: {
						Destination: { editable: false },
						Target: { editable: false },
						Source: { editable: true },
						MatchOn: { editable: true },
						LookupField: { editable: true },
						LookupAction: { editable: true },
						LookupTable: { editable: false },
					}
				}
			},
			transport: {
				update: function(options)
				{
					var data = options.data,
						target = data.Target.toLowerCase(),
						colIndex = tableConfig.columnConfigs.findIndex(function(c)
						{
							return c.Target.toLowerCase() == target;
						}),
						col = tableConfig.columnConfigs[colIndex] || {};
					col.Target = target;
					col.Source = data.Source;
					col.MatchOn = data.MatchOn;
					col.LookupField = data.LookupField;
					col.LookupAction = data.LookupAction;
					col.LookupTable = data.LookupTable;
					if (colIndex == -1 && col.Source)
					{
						tableConfig.columnConfigs.push(col);
					}

					options.success(data);
				},
				read: function(options)
				{
					let columnList = TF.Control.ImportDataHelper.instance.getColumnsByTable(dataTypeInfo.displayName, true).filter(function(i) { return !i.ImportDisable; }),
						udfList = self.tableUdfs[dataTypeInfo.displayName],
						udfPromise;
					if (udfList)
					{
						udfPromise = Promise.resolve({ Items: udfList });
					}
					else
					{
						const typeDisplayName = dataTypeInfo.displayName === "Address Point" ? "Parcel Address Point" : dataTypeInfo.displayName;
						const dataTypeId = tf.dataTypeHelper.getIdByName(typeDisplayName);
						if (dataTypeId)
						{
							udfPromise = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "userdefinedfields"), {
								paramData: {
									DataTypeId: dataTypeId,
									DataSourceId: tf.datasourceManager.databaseId,
									"@fields": "Id,Guid,DisplayName,DataTypeId,TypeId"
								}
							});
						}
						else
						{
							/*
							data type of Data List type cannot get the the DataTypeId by getIdByName, it returns NULL, the Data List types does not have UDFs,
							if use NULL as DataTypeId, it will returns all UDFs
							*/
							udfPromise = Promise.resolve({ Items: [] });
						}
					}

					udfPromise.then(function(apiResponse)
					{
						let udfs = (apiResponse && apiResponse.Items) || [];
						const typesNotAllowed = [TF.Enums.UDFType.Rollup, TF.Enums.UDFType.Case, TF.Enums.UDFType.Image, TF.Enums.UDFType.InBoundary];
						udfs = udfs.filter(x => !typesNotAllowed.includes(x.TypeId));
						self.tableUdfs[dataTypeInfo.displayName] = udfs;

						let udfColumns = {};
						udfs.forEach(i =>
						{
							udfColumns[i.DisplayName.toLowerCase()] = i;
						});

						self.resolveColumnConfigs(tableConfig, udfColumns);
						udfs.forEach(item =>
						{
							columnList.push({ Destination: item.DisplayName, Source: item.DisplayName, MatchOn: false, IsUDF: true });
						});

						var data = columnList.map(function(colDef)
						{
							var item = {},
								target = colDef.Source,
								source = target,
								lowerCaseName = target.toLowerCase(),
								columnConfig = tableConfig.columnConfigs.find(function(c)
								{
									return c.Target.toLowerCase() == lowerCaseName;
								});

							item.Target = source;
							item.Destination = colDef.Destination;
							item.MatchOn = tableConfig.columnConfigs.length ? false : colDef.MatchOn;
							item.LookupField = colDef.LookupField;
							item.LookupAction = colDef.LookupAction;
							item.LookupTable = colDef.LookupTable;
							item.IsUDF = colDef.IsUDF;
							item.Alternate = colDef.Alternate;

							if (columnConfig)
							{
								source = columnConfig.Source;
								item.MatchOn = columnConfig.MatchOn;
								item.LookupField = columnConfig.LookupField;
								item.LookupAction = columnConfig.LookupAction;
							}

							var currentSourceColumn = source ? sourceColumns.find(function(c) { return c.toLowerCase() == source.toLowerCase(); }) : null;
							if (!currentSourceColumn)
							{
								currentSourceColumn = sourceColumns.find(function(c) { return c.toLowerCase() == item.Target.toLowerCase(); });
							}
							if (!currentSourceColumn && item.Alternate)
							{
								currentSourceColumn = sourceColumns.find(function(c) { return c.toLowerCase() == item.Alternate.toLowerCase(); });
							}

							item.Source = currentSourceColumn || "";
							item.LookupAction = item.LookupAction || null;
							if (columnConfig) // may come from template
							{
								columnConfig.Source = currentSourceColumn;
								columnConfig.TargetFieldName = item.TargetFieldName;
								columnConfig.LookupTable = item.LookupTable;
							}
							else
							{
								columnConfig = {
									Target: item.Target,
									Source: item.Source,
									MatchOn: item.MatchOn,
									LookupField: item.LookupField,
									LookupAction: item.LookupAction,
									LookupTable: item.LookupTable,
									IsUDF: item.IsUDF
								};

								tableConfig.columnConfigs.push(columnConfig);
							}

							return item;
						});

						data = self.sortMappedColumns(data);
						options.success(data);
					});
				},
			}
		});
	};

	SelectProcessingOptionsStep.prototype.sortMappedColumns = function(data)
	{
		let item, result = [], udfs = [];
		while (item = data.shift())
		{
			if (item.IsUDF)
			{
				udfs.push(item);
			}
			else
			{
				result.push(item);
			}
		}

		if (udfs.length)
		{
			udfs = Array.sortBy(udfs, "Destination");
			udfs.forEach(i => result.push(i));
		}

		return result;
	};

	SelectProcessingOptionsStep.prototype.bindColumnSource = function(grid)
	{
		var self = this, $sourceContainers, sourceList, $fieldSourceContainers, $actionSourceContainers;

		var $sourceContainers = grid.element.find('.column-source');
		sourceList = this.gridColumns.map(function(name, i) { return { ColumnName: name } });
		sourceList = [{ ColumnName: '<Not Mapped>' }].concat(sourceList);

		var $fieldSourceContainers = grid.element.find('.LookupField-source');
		var $actionSourceContainers = grid.element.find('.LookupAction-source');

		$sourceContainers.map(function(index, sourceContainer)
		{
			self.initColumnSourceDropDownList.call(self, sourceContainer, sourceList, null, grid);
		});

		$fieldSourceContainers.map(function(index, sourceContainer)
		{
			self.initColumnSourceDropDownList.call(self, sourceContainer, null, 'LookupField', grid);
		});

		$actionSourceContainers.map(function(index, sourceContainer)
		{
			self.initColumnSourceDropDownList.call(self, sourceContainer, null, 'LookupAction', grid);
		});
	}

	SelectProcessingOptionsStep.prototype.initColumnSourceDropDownList = function(sourceItemContainer, sourceList, type, grid)
	{
		var self = this, data = grid.dataItem(sourceItemContainer.closest("tr")),
			sourceName = 'Source', validName = 'ColumnName', displayName = 'ColumnName';
		if (!data)
		{
			return;
		}

		if (!sourceList)
		{
			sourceName = type;
			var lookupTable = TF.ImportAndMergeData.ImportDataType.findById(data.LookupTable).displayName;
			if (type == 'LookupField')
			{
				sourceList = TF.Control.ImportDataHelper.instance.getColumnsByTable(lookupTable, true).filter(function(item) { return item.MatchOn; }).map(function(i) { return { Destination: i.Destination, Source: i.Source } });
				sourceList = [{ Destination: '<None>', Source: '<None>' }].concat(sourceList);
				validName = 'Source';
				displayName = 'Destination';
			}
			else
			{
				sourceList = $.extend(true, [], TF.Control.ImportDataHelper.instance.getValidLookupActions(lookupTable));
				sourceList = [{ displayName: '<None>', name: '<None>', id: -1 }].concat(sourceList);
				validName = 'id';
				sourceList.map(function(s)
				{
					s.showName = s.displayName;
					delete s.displayName;
				});
				displayName = 'showName';
			}
		}
		sourceList = Array.sortBy(sourceList, "ColumnName");
		var obSourceItems = ko.observable(sourceList);
		var sourceItemSelectTemplate = function(name)
		{
			if (name === '<None>')
			{
				return "<a href=\"#\" role=\"option\" >&#60None&#62</a>";
			}
			else if (name === '<Not Mapped>')
			{
				return "<a href=\"#\" role=\"option\" >&#60Not Mapped&#62</a>";
			}
			else
			{
				return "<a href=\"#\" role=\"option\" >" + name + "</a>";
			}
		}

		var selectedSource = Enumerable.From(sourceList).FirstOrDefault(null, function(c) { return c[validName] == data[sourceName] });
		if (selectedSource === null)
		{
			selectedSource = sourceList[0];
		}

		var obSelectedSourceItem = ko.observable(selectedSource);
		var obSelectedSourceItemText = ko.observable(selectedSource[displayName]);
		var source = { obSourceItems: obSourceItems, obSelectedSourceItem: obSelectedSourceItem, obSelectedSourceItemText: obSelectedSourceItemText, sourceItemSelectTemplate: sourceItemSelectTemplate }
		var sourceItemDom = $('<div class="input-group" ' + '><div data-bind="typeahead:{source:obSourceItems,format:function(obj){return obj.' + displayName + ';},drowDownShow:true,notSort:true,selectedValue:obSelectedSourceItem}">\
						<!-- ko customInput:{type:"Select",value:obSelectedSourceItemText,attributes:{class:"form-control",name:"status"}} -->\
						<!-- /ko -->\
						</div>\
						<div class="input-group-btn"><button type="button" class="btn btn-default btn-sharp"><span class="caret"></span></button></div></div>')
		ko.applyBindings(source, sourceItemDom[0]);
		sourceItemContainer.append(sourceItemDom[0]);
		obSelectedSourceItemText.subscribe(self.bindSourceItemValue.bind(self, data, sourceName, sourceList));
	};

	SelectProcessingOptionsStep.prototype.bindSourceItemValue = function(dataItem, sourceName, sourceList, selectedSource)
	{
		var self = this, target = dataItem.Target.toLowerCase();
		var data = Enumerable.From(self.selectedTableConfig().columnConfigs).FirstOrDefault(null, function(c)
		{
			return c.Target.toLowerCase() == target;
		});

		if (sourceName == 'LookupAction')
		{
			if (selectedSource == "<None>")
			{
				selectedSource = null;
			}
			else
			{
				selectedSource = TF.ImportAndMergeData.LookupAction.find(selectedSource).id;
			}
		}
		else if (sourceName == 'LookupField')
		{
			if (selectedSource == "<None>")
			{
				selectedSource = null;
			}
			else
			{
				var selectedItem = sourceList.find(function(i) { return i.Destination == selectedSource });
				if (selectedItem)
				{
					selectedSource = selectedItem.Source;
				}
				else
				{
					selectedSource = null;
				}
			}
		}

		if (selectedSource == 'None' || (dataItem !== null && dataItem[sourceName] == selectedSource))
		{
			return;
		}
		else
		{
			if (selectedSource == "<Not Mapped>" || selectedSource == "<None>")
			{
				selectedSource = null;
			}

			dataItem[sourceName] = selectedSource;
			if (data !== null)
			{
				data[sourceName] = selectedSource;
			}
			self.updateNextButtonDisable();
		}
	};

	SelectProcessingOptionsStep.prototype.saveTemplate = function()
	{
		var template = this.data.saveTemplate();
		var xml = generateXml(template);
		TF.saveStringAs(xml, "text/xml", "template.xml");
	};

	/**
	  * Click abort ungeocoded checkbox.
	  * @return {void}
	  */
	SelectProcessingOptionsStep.prototype.abortUngeocodedBoxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (!checkClick)
		{
			self.obAbortUngeocoded(!self.obAbortUngeocoded());
			if (self.obAbortUngeocoded())
			{
				$('input[name="UngeocodedNumber"]').data('kendoNumericTextBox').focus();
			}
			return;
		}
	};

	SelectProcessingOptionsStep.prototype.geocodedNumberChangeEvent = function(viewModel)
	{
		var self = this;
		self.selectedTableConfig().AllowUngeocodedPercentage = self.obUngeocodedNumber();
	}

	SelectProcessingOptionsStep.prototype.deletedNumberChangeEvent = function(viewModel)
	{
		var self = this;
		self.selectedTableConfig().AllowDeletedPercentage = self.obDeletedNumber();
	}

	/**
  * Click abort deleted checkbox.
  * @return {void}
  */
	SelectProcessingOptionsStep.prototype.abortDeletedBoxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (!checkClick)
		{
			self.obAbortDeleted(!self.obAbortDeleted());
			if (self.obAbortDeleted())
			{
				$('input[name="DeletedNumber"]').data('kendoNumericTextBox').focus();
			}
			return;
		}
	};

	/**
 * Valid numeric input
 * @param  {viewModel} data SelectProcessingOptionsStep.
 * @param  {Event} e jQuery Event object.
 * @returns {boolean}
 */
	SelectProcessingOptionsStep.prototype.numericalValidation = function(data, e)
	{
		var key = e.which || e.keyCode || 0;
		// Only number keys and backspace, arrowleft,arrowright are available, value is in the range of [1,100]
		if (key == 37 || key == 39 || key == 8)
		{
			return true;
		}
		if (!isNumber(e.key) || (e.target.value + e.key > 100 || e.key == 0 && e.target.value == 0))
		{
			e.preventDefault();
			e.stopPropagation();
			return false;
		}

		return true;
	};

	/**
	 * Check the required fields
	 * @returns 
	 */
	SelectProcessingOptionsStep.prototype.execute = async function()
	{
		const dataTypes = ['Student', 'Address Point']
		if (!dataTypes.includes(this.dataTypes[0].displayName))
		{
			return true;
		}

		if (this.selectedTableConfig().isUpdateExisting())
		{
			const needConfirm = () =>
			{
				if (this.dataTypes[0].displayName === "Student")
				{
					const mappedLocalIdAndSchoolCols = this.data.tableConfigs[0].columnConfigs.filter(o => o.Source && (o.Target ?? "").toLowerCase() === "school");
					if (mappedLocalIdAndSchoolCols.length < 1)
					{
						return true;
					}
				}

				if (this.dataTypes[0].displayName === "Address Point")
				{
					const requiredFieldsToAdd = ["addressnumber", "street", "xcoord", "ycoord"];
					return this.data.tableConfigs[0].columnConfigs.some(o => !o.Source && requiredFieldsToAdd.includes(o.Target?.toLowerCase() ?? ""));
				}

				return false;
			}

			if (needConfirm())
			{
				return await tf.promiseBootbox.confirm({ title: "Confirmation Message", message: "Not all required fields for import data type are mapped. This import will create no new data records. Do you wish to proceed?" });
			}
		}
		return true;
	};

	function generateXml(element, noProlog)
	{
		var builder = [];
		if (!noProlog)
		{
			builder.push('<?xml version="1.0"?>');
		}

		builder.push('<' + element.$tag);
		$.each(element, function(k, v)
		{
			if (k.startsWith("$"))
			{
				return;
			}

			builder.push(' ' + k + '="' + v + '"');
		});

		builder.push('>');

		if (element.$text != null)
		{
			builder.push(element.$text);
		}
		else if (element.$children != null)
		{
			element.$children.forEach(function(child)
			{
				builder.push(generateXml(child, true));
			});
		}

		builder.push('</' + element.$tag + '>');
		return builder.join("");
	}
})();
