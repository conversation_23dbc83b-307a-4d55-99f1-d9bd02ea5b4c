﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListDocumentClassificationDeletion = DataListDocumentClassificationDeletion;

	function DataListDocumentClassificationDeletion()
	{
		this.type = 'documentclassification';
		this.deleteType = 'Name';
		this.deleteRecordName = 'Document Classification';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListDocumentClassificationDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListDocumentClassificationDeletion.prototype.constructor = DataListDocumentClassificationDeletion;

	DataListDocumentClassificationDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "documents?@fields=DocumentID&DocumentClassificationID=" + ids[0]))
		.then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items
			});
		});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}
})();