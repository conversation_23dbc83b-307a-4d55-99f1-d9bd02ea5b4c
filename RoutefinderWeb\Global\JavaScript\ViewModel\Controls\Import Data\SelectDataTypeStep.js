(function()
{
	createNamespace("TF.ImportAndMergeData").SelectDataTypeStep = SelectDataTypeStep;

	const DEFAULT_COLUMN_WIDTH = 100;

	function SelectDataTypeStep(data)
	{
		TF.Control.BaseWizardStepViewModel.call(this, data);
		var self = this;
		self.template = "modal/import data/SelectDataType";
		self.name("Select Data Type");
		self.description("Select the data you wish to Import.");
		self.availableTables = self.data.externalSource.SheetNames;

		self.columns = ko.pureComputed(function()
		{
			var columnList = self.data.getDataView(self.data.selectedExternalTableName(), 1).columns;
			const gridWidth = $($(self.validator.$form[0]).find('.kendo-grid')[0]).width();
			return tf.helpers.kendoGridHelper.calculateColumnWidth(gridWidth, columnList);
		});

		self.previewedData = ko.pureComputed(function()
		{
			return new kendo.data.DataSource({
				data: self.data.getDataView(self.data.selectedExternalTableName(), 100).rows
			});
		});
	}

	var ImportDataType = TF.ImportAndMergeData.ImportDataType;
	SelectDataTypeStep.avaliableDataTypeValues = [
		ImportDataType.AddressPoint,
		ImportDataType.AlternateSite,
		ImportDataType.Contractor,
		ImportDataType.District,
		ImportDataType.FieldTrip,
		ImportDataType.FieldTripAccount,
		ImportDataType.FieldTripActivity,
		ImportDataType.FieldTripBillingClassification,
		ImportDataType.FieldTripClassification,
		ImportDataType.FieldTripDestination,
		ImportDataType.FieldTripEquipment,
		ImportDataType.FieldTripTemplate,
		ImportDataType.GeoRegion,
		ImportDataType.GeoRegionType,
		ImportDataType.School,
		ImportDataType.Staff,
		ImportDataType.Student,
		ImportDataType.StudentDisabilityCodes,
		ImportDataType.StudentEthnicCodes,
		ImportDataType.Vehicle,
		ImportDataType.VehicleBodyType,
		ImportDataType.VehicleBrakeType,
		ImportDataType.VehicleCategory,
		ImportDataType.VehicleEquipment,
		ImportDataType.VehicleFuelType,
		ImportDataType.VehicleMake,
		ImportDataType.VehicleMakeOfBody,
		ImportDataType.VehicleModel,
	];

	SelectDataTypeStep.avaliableDataTypes = TF.ImportAndMergeData.avaliableDataTypes
		.filter(function(i) { return SelectDataTypeStep.avaliableDataTypeValues.indexOf(i.id) > -1 });

	SelectDataTypeStep.prototype = Object.create(TF.Control.BaseWizardStepViewModel.prototype);

	SelectDataTypeStep.prototype.constructor = SelectDataTypeStep;

	SelectDataTypeStep.prototype.execute = async function()
	{
		if (this.data.tableConfigs[0].dataType() === TF.ImportAndMergeData.ImportDataType.AddressPoint)
		{
			const performBackBeforeImporting = await tf.promiseBootbox.confirm({
				message: "Importing address point data affects map data in all data sources. It is highly recommended that you create an archive of your current maps before importing data. Do you want to create an archive of your current data?",
				title: "Confirmation Message"
			});

			if (performBackBeforeImporting)
			{
				try
				{
					await tf.map.startupLoadArcgisUrlsComplete.waitComplete(true);
					const backupMapDataTool = new TF.Map.BackupMapDataTool();
					// BackupMapDataTool.prototype.generateBackupFile = function(needDownload = true, needConfirm = true)
					return await backupMapDataTool.generateBackupFile(false, false);
				}
				catch (err)
				{
					return false;
				}
			}
		}

		return true;
	};

	SelectDataTypeStep.prototype.validate = function()
	{
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "operationvalidations"), {
			data: this.data.toData()
		}).then((response) =>
		{
			let validation = response.Items[0];
			if (!validation.Status)
			{
				tf.promiseBootbox.alert(validation.Message);
			}

			return validation.Status;
		});
	};
})();