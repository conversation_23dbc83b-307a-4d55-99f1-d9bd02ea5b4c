﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.SchoolDataModel = function(schoolEntity)
	{
		namespace.BaseDataModel.call(this, schoolEntity);
	}

	namespace.SchoolDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.SchoolDataModel.prototype.constructor = namespace.SchoolDataModel;

	// namespace.SchoolDataModel.prototype.mapping = [
	// 	{ from: "Id", default: 0 },
	// 	{ from: "DBID", default: function() { return tf.datasourceManager.databaseId; } },
	// 	{ from: "Name", default: "" },
	// 	{ from: "SchoolCode", default: "" },
	// 	{ from: "Capacity", default: 0 },
	// 	{ from: "DistrictIdString", default: "" },
	// 	{ from: "DistrictID", default: 0 },
	// 	{ from: "FeedSchl", default: "" },
	// 	{ from: "FeedSchlId", default: 0 },
	// 	{ from: "SifimportStudents", default: false },
	// 	{ from: "Tschl", default: false },
	// 	{ from: "Private", default: false },
	// 	{ from: "Contact", default: "" },
	// 	{ from: "ContactTitle", default: "" },
	// 	{ from: "Phone", default: function() { return tf.setting.userProfile.AreaCode; } },
	// 	{ from: "PhoneExt", default: "" },
	// 	{ from: "FaxNumber", default: "" },
	// 	{ from: "GradeRange", default: "" },
	// 	{ from: "Email", default: "" },
	// 	{ from: "ArrivalTime", default: function() { return tf.setting.userProfile.DefaultTime; } },
	// 	{ from: "DepartTime", default: function() { return tf.setting.userProfile.DefaultTime; } },
	// 	{ from: "BeginTime", default: function() { return tf.setting.userProfile.DefaultTime; } },
	// 	{ from: "EndTime", default: function() { return tf.setting.userProfile.DefaultTime; } },
	// 	{ from: "MailStreet1", default: "" },
	// 	{ from: "MailStreet2", default: "" },
	// 	{ from: "MailCity", default: function() { return tf.setting.userProfile.Mailcity; } },
	// 	{ from: "MailState", default: function() { return tf.setting.userProfile.MailState; } },
	// 	{ from: "MailZip", default: function() { return tf.setting.userProfile.Mailzip; } },
	// 	{ from: "GeometryPoint", default: null },
	// 	{ from: "GeoStreet", default: "" },
	// 	{ from: "GeoCity", default: "" },
	// 	{ from: "GeoZip", default: "" },
	// 	{ from: "GeoCounty", default: "" },
	// 	{ from: "Xcoord", default: 0 },
	// 	{ from: "Ycoord", default: 0 },
	// 	{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
	// 	{ from: "LastUpdatedId", default: 0 },
	// 	{ from: "LastUpdatedName", default: "" },
	// 	{ from: "LastUpdatedType", default: 0 },
	// 	{ from: "Comments", default: "" }
	// ];


	namespace.SchoolDataModel.prototype.mapping = [
		{ "from": "ArrivalTime", "default": null },
		{ "from": "BeginTime", "default": null },
		{ "from": "Capacity", "default": 0 },
		{ "from": "Comments", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "DepartTime", "default": null },
		{ "from": "DispGrade", "default": null },
		{ "from": "District", "default": null },
		{ "from": "DistrictID", "default": null },
		{ "from": "DistrictName", "default": null },
		{ "from": "EndTime", "default": null },
		{ "from": "FeedSchl", "default": "" },
		{ "from": "FeedSchoolName", "default": null },
		{ "from": "GeoCity", "default": "" },
		{ "from": "GeoConfidence", "default": null },
		{ "from": "GeoCounty", "default": "" },
		{ "from": "GeoStreet", "default": "" },
		{ "from": "GeoZip", "default": "" },
		{ "from": "GradeIds", "default": null },
		{ "from": "GradeRange", "default": "" },
		{ "from": "Guid", "default": null },
		{ "from": "Id", "default": 0 },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "MailCity", "default": function() { return tf.setting.userProfile.MailCityName; } },
		{ "from": "MailCityId", "default": function() { return tf.setting.userProfile.MailCity; } },
		{ "from": "MailState", "default": function() { return tf.setting.userProfile.MailStateName; } },
		{ "from": "MailStateId", "default": function() { return tf.setting.userProfile.MailState; } },
		{ "from": "MailStreet1", "default": "" },
		{ "from": "MailStreet2", "default": "" },
		{ "from": "MailZip", "default": function() { return tf.setting.userProfile.MailZipName; } },
		{ "from": "MailZipId", "default": function() { return tf.setting.userProfile.MailPostalCode; } },
		{ "from": "Name", "default": "" },
		{ "from": "Private", "default": false },
		{ "from": "SchoolCode", "default": "" },
		{ "from": "SchoolCodeWithName", "default": null },
		{ "from": "StudentCount", "default": 0 },
		{ "from": "StudentList", "default": null },
		{ "from": "Tschl", "default": false },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
		{ "from": "Xcoord", "default": 0 },
		{ "from": "Ycoord", "default": 0 }
	];
})();
