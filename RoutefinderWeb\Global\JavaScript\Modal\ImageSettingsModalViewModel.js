﻿(function()
{
	createNamespace("TF.Modal").ImageSettingsModalViewModel = ImageSettingsModalViewModel;

	var percentageAttr = "data-percentage",
		dataUrlAttr = "data-url";
	function ImageSettingsModalViewModel(image, options)
	{
		var self = this;

		TF.Modal.BaseModalViewModel.call(self);
		self.title(options.title);
		self.sizeCss = "modal-sm";
		self.contentTemplate('workspace/controlpanel/modal/ImageSettings');
		self.buttonTemplate('modal/positivenegative');
		self.obPositiveButtonLabel(options.positiveButtonLabel);
		self.image = image || document.createElement("img");

		var settings = getImageMetaData(self.image);
		self.data(new TF.Control.ImageSettingsViewModel(settings));
	}

	ImageSettingsModalViewModel.scale = function(image)
	{
		var settings = getImageAttributes(image);
		var naturalWidth = image.naturalWidth, naturalHeight = image.naturalHeight;
		if (!settings.usePercentage)
		{
			var percentageX = settings.width * 100 / naturalWidth,
			percentageY = settings.height * 100 / naturalHeight,
			percentage = percentageX;
			if (percentageX != percentageY && !Number.isNaN(percentageY))
			{
				percentage += "," + percentageY;
			}

			image.setAttribute(percentageAttr, percentage);
			return;
		}

		image.width = naturalWidth * settings.percentageX / 100;
		image.height = naturalHeight * settings.percentageY / 100;
	};

	ImageSettingsModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ImageSettingsModalViewModel.prototype.constructor = ImageSettingsModalViewModel;

	ImageSettingsModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.data().validate().then(function(result)
		{
			if (result)
			{
				var data = this.data(), image = this.image;
				if (data.usePercentage())
				{
					image.removeAttribute("width");
					image.removeAttribute("height");
					var percentage = data.percentageX();
					if (data.percentageX() != data.percentageY() && !Number.isNaN(data.percentageY()))
					{
						percentage += "," + data.percentageY();
					}

					image.setAttribute(percentageAttr, percentage);
					image.setAttribute("onload", "window.parent.TF.Modal.ImageSettingsModalViewModel.scale(this)");
				}
				else
				{
					image.removeAttribute(percentageAttr);
					var width = data.width();
					if (!width)
					{
						image.removeAttribute("width");
					}
					else
					{
						image.width = width;
					}

					var height = data.height();
					if (!height)
					{
						image.removeAttribute("height");
					}
					else
					{
						image.height = height;
					}
				}

				if (image.getAttribute("data-field") === null)
				{
					image.setAttribute("data-url", data.src());
					$(image).removeClass("linked-image-placeholder");
					image.setAttribute("onerror", "this.setAttribute('src', 'data:image/svg+xml,%3Csvg%20width%3D%27146%27%20height%3D%27146%27%20viewBox%3D%270%200%20146%20146%27%20preserveAspectRatio%3D%27xMidYMid%20meet%27%20xmlns%3D%27http%3A//www.w3.org/2000/svg%27%3E%3Ctext%20x%3D%2750%25%27%20y%3D%2750%25%27%20dominant-baseline%3D%27middle%27%20text-anchor%3D%27middle%27%3ENo%20Picture%20On%20File%3C/text%3E%3C/svg%3E');this.classList.add('linked-image-placeholder')");
				}

				image.alt = data.alt();
				image.src = data.src();

				return this.positiveClose(image);
			}
			return result;
		}.bind(this));
	};

	function getImageMetaData(image)
	{
		var $image = $(image);
		var width = $image.width() === 0 ? "" : $image.width();
		var height = $image.height() === 0 ? "" : $image.height();
		var naturalHeight = image.naturalHeight;
		var naturalWidth = image.naturalWidth;

		var percentageX = width / naturalWidth * 100;
		percentageX = $.isNumeric(percentageX) ? percentageX : 100;
		percentageX = percentageX.toFixed(0);

		var percentageY = height / naturalHeight * 100;
		percentageY = $.isNumeric(percentageY) ? percentageY : 100;
		percentageY = percentageY.toFixed(0);

		percentage = image.getAttribute(percentageAttr),
			usePercentage = false;

		var dataUrl = image.getAttribute(dataUrlAttr);

		return {
			usePercentage: usePercentage,
			src: image.src,
			alt: image.alt,
			width: width,
			height: height,
			percentageX: percentageX,
			percentageY: percentageY,
			dataUrl: dataUrl
		};
	};

	function getImageAttributes(image)
	{
		var width, height, percentageX, percentageY,
			percentage = image.getAttribute(percentageAttr),
			usePercentage = false;
		if (percentage)
		{
			var percentages = percentage.split(",");
			if (percentages.length)
			{
				percentageX = parseFloat(percentages[0]);
				if (!percentageX || Number.isNaN(percentageX))
				{
					percentageX = 100;
				}

				percentageY = parseFloat(percentages[1]);
				if (!percentageY || Number.isNaN(percentageY))
				{
					percentageY = percentageX;
				}
				usePercentage = true;
			}
		}

		width = parseFloat(image.width);
		if (width === 0)
		{
			width = null;
		}

		height = parseFloat(image.height);
		if (height === 0)
		{
			height = null;
		}

		return {
			usePercentage: usePercentage,
			src: image.src,
			alt: image.alt,
			width: width,
			height: height,
			percentageX: percentageX,
			percentageY: percentageY,
		}
	};
})();
