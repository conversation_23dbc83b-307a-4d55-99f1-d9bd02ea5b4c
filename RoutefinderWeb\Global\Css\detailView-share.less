@import "fontsize";

@systemColor: gray;
@systemLightColor: rgba(208, 80, 60, 0.15);

.base-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.calendar-template {
	.item-style {
		background: none;
		border-right: none;
	}

	.fontSize(1, -2);
	font-family: "SourceSansPro-SemiBold";
	border: none;
	box-sizing: border-box;
	padding: 8px;
	height: 100%;
	width: 100%;
	float: left;

	.k-calendar-view
	{
		padding: 0;
	}

	.k-header {
		border: 0;
		background-color: transparent;
		height: 8%;
		padding-bottom: 3px;

		.k-link {
			color: @systemColor;

			&.k-nav-fast {
				pointer-events: none;
				float: left;
				text-align: left;
				padding-left: 0;
				height: auto;
				line-height: normal;
				margin: 0;
			}

			&.k-nav-prev,
			&.k-nav-next {
				height: 18px;
				width: 14.3%;
				top: 0px;
				padding-bottom: 3px;
				box-sizing: border-box;
				.item-style;

				.k-icon {
					background: none;
					height: 0;
					width: 0;
				}
			}

			&.k-nav-prev {
				left: auto;
				right: 28.6%;

				&:before {
					content: '';
					position: absolute;
					top: 1px;
					left: calc(~"50% - 5px");
					border-right: 5px solid @systemColor;
					border-top: 5px solid transparent;
					border-bottom: 5px solid transparent;
				}
			}

			&.k-nav-next {
				right: 0;

				&:before {
					content: '';
					position: absolute;
					top: 1px;
					left: calc(~"50% - 2.5px");
					border-left: 5px solid @systemColor;
					border-top: 5px solid transparent;
					border-bottom: 5px solid transparent;
				}
			}
		}

		&+div {
			height: 100% !important;
		}
	}

	.k-footer {
		position: absolute;
		top: 8px;
		right: 14.3%;
		height: 18px;
		width: 14.3%;
		box-sizing: border-box;
		.item-style;

		.k-nav-today {
			height: 8px;
			width: 8px;
			border-radius: 4px;
			background-color: @systemColor;
			padding: 0;
			overflow: hidden;
			text-indent: -9999px;
			margin: 2px 0 0 calc(~"50% - 10px");
		}
	}

	.k-content,
	.k-calendar-table {
		height: 92%;
		pointer-events: none;

		thead {
			height: 7%;

			tr th {
				text-align: center;
				background-color: transparent;
				border: 0;
				padding: 0;
				color: #777777;
				font-family: "SourceSansPro-Regular";
				.item-style;
				height: 2.42857143em;
			}
		}

		tbody {
			height: 93%;

			tr td {
				text-align: center;
				box-shadow: none;
				outline: none !important;
				height: 18px;
				padding: 3px 0 6px 0;
				cursor: default;
				.item-style;

				&.k-hover,
				&.k-hover:hover {
					.item-style;
				}

				.k-link {
					outline: none;
					display: inline-block;
					padding: 0;
					min-height: 18px;
					line-height: normal;
					color: #262626;
					cursor: default;

					.date-text {
						width: 14px;
						height: 14px;
						line-height: normal;
						border-radius: 10px;
					}

					.events-group {
						position: absolute;
						left: calc(50% - 2px);
						display: none;

						&.show {
							display: block;
						}

						.events-point {
							width: 4px;
							height: 4px;
							border-radius: 2px;
							background-color: @systemColor;
							float: left;
						}
					}
				}

				&.k-weekend {
					.k-link {
						color: #9b9b9b;
					}

					&.k-hover,
					&.k-hover:hover {
						.k-link {
							color: #9b9b9b;
						}
					}
				}

				&.k-other-month {
					.k-link {
						color: #cacaca;
					}

					&.k-hover,
					&.k-hover:hover {
						.k-link {
							color: #cacaca;
						}
					}
				}

				&.k-today {
					padding-bottom: 0px;
					.item-style;

					.k-link {
						.date-text {
							color: #fff;
							font-family: "SourceSansPro-Bold";
							background-color: @systemColor;
						}
					}
				}
			}
		}
	}
}

.k-calendar-md
{

}

.calendar.k-calendar
{
	.k-header.k-hstack
	{
		justify-content: flex-end;

		.k-calendar-nav-prev {
			margin-right: 30px;
		}

		.k-calendar-nav-fast
		{
			pointer-events: none;
			position: absolute;
			left: 0;
			color: #808080;
		}

		.k-calendar-nav-prev,
		.k-calendar-nav-next
		{
			&:hover {
				color: #fff;
			}
			svg
			{
				fill: #808080;
			}
		}
	}

	&.k-calendar-md .k-calendar-monthview
	{
		--INTERNAL--kendo-calendar-cell-size: var(--kendo-calendar-md-month-cell-size, 10px);
	}

	.k-calendar-view 
	{
		width: auto;
		padding: 0;
		inline-size: auto;
		min-height: auto;

		.k-calendar-table
		{
			tbody td.k-calendar-td
			{
				width: auto;
				inline-size: 0;
				block-size: 0;

				.k-link 
				{
					padding-inline: 0;
					padding-block: 0;

					.date-text
					{
						margin: auto;
					}
				}
			}
		}
	}

	.k-calendar-footer {
		position: absolute;
		top: calc(4% + 4px);
    right: 48px;
		font-size: 0;
		width: 8px;
		height: 8px;
		background-color: #808080;
		border-radius: 50%;
		padding: 0;
		z-index: 2;

		&:hover
		{
			color: #808080;
		}

		.k-button:hover
		{
			color: #fff;
		}
	}

	.k-calendar-td:focus,
	.k-calendar-td.k-focus
	{
		.k-link
		{
			outline: none;
			box-shadow: none;

			.date-text
			{
				border-radius: 0 !important;
				border: 1px #007CC0 solid;
			}
		}
	}

	.k-calendar-td.k-selected
	{
		.k-link .date-text
		{			
			border-radius: 10px !important;
			border: 0;
		}
	}
}


.map-template {
	height: 100%;
	width: 100%;
}

.student-card {
	.checkbox {
		margin: 0px;

		input[type=checkbox] {
			left: 15px;
		}

		span.disabled {
			opacity: 0.5;
		}
	}
}