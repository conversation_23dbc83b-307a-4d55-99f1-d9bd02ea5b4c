﻿(function()
{
	createNamespace('TF.Control').EditFieldTripStatusViewModel = EditFieldTripStatusViewModel;

	var allFieldTripStatusMap = {};
	allFieldTripStatusMap[TF.FieldTripStageEnum.level1RequestSubmitted] = { id: 1, name: "Level 1 - Request Submitted", isApprove: true };
	allFieldTripStatusMap[TF.FieldTripStageEnum.level2RequestDeclined] = { id: 2, name: "Level 2 - Request Declined", isApprove: false };
	allFieldTripStatusMap[TF.FieldTripStageEnum.level2RequestApproved] = { id: 3, name: "Level 2 - Request Approved", isApprove: true };
	allFieldTripStatusMap[TF.FieldTripStageEnum.level3RequestDeclined] = { id: 4, name: "Level 3 - Request Declined", isApprove: false };
	allFieldTripStatusMap[TF.FieldTripStageEnum.level3RequestApproved] = { id: 5, name: "Level 3 - Request Approved", isApprove: true };
	allFieldTripStatusMap[TF.FieldTripStageEnum.level4RequestDeclined] = { id: 6, name: "Level 4 - Request Declined", isApprove: false };
	allFieldTripStatusMap[TF.FieldTripStageEnum.level4RequestApproved] = { id: 7, name: "Level 4 - Request Approved", isApprove: true };
	allFieldTripStatusMap[TF.FieldTripStageEnum.DeclinedByTransportation] = { id: 98, name: "Declined by Transportation", isApprove: false };
	allFieldTripStatusMap[TF.FieldTripStageEnum.TransportationApproved] = { id: 99, name: "Transportation Approved", isApprove: true };
	allFieldTripStatusMap[TF.FieldTripStageEnum.RequestCanceled] = { id: 100, name: "Canceled - Request Canceled", isApprove: false };
	allFieldTripStatusMap[TF.FieldTripStageEnum.RequestCompleted] = { id: 101, name: "Completed - Request Completed", isApprove: true };

	function EditFieldTripStatusViewModel(selectedRecords)
	{
		var self = this;
		self.obComments = ko.observable("");
		self.placehoder = selectedRecords.length > 1 ? "Add comments to all " + tf.applicationTerm.getApplicationTermPluralByName("Field Trip") + "..." : "Add comments here...";
		self.$form = null;
		self.selectedRecords = selectedRecords;
		self.fieldTripIds = selectedRecords.map(function(item) { return item.Id; });
		self.isAdmin = tf.authManager.authorizationInfo.isAdmin || tf.authManager.authorizationInfo.isAuthorizedFor("transportationAdministrator", "edit");
		self.fieldTripStatus = $.map(tf.helpers.fieldTripAuthHelper.getAccessableStageIds(), function(item)
		{
			return allFieldTripStatusMap[item];
		});

		//drop down list
		self.obFieldTripStatus = ko.observableArray(self.fieldTripStatus);
		self.obSelectedStatusId = ko.observable();
		self.obSelectedStatus = ko.observable();
		self.obSelectedStatus.subscribe(self.setStatusValue, self);
		self.obSelectedStatusText = ko.computed(self.setStatusTextComputer, self);

		if (self.selectedRecords.length > 0)
		{
			self.obSelectedStatusId(self.selectedRecords[0].FieldTripStageId || 1);
		}
		else
		{
			self.obSelectedStatusId(1);
		}

		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	EditFieldTripStatusViewModel.prototype.setStatusValue = function()
	{
		var self = this;
		if (self.obSelectedStatus())
		{
			self.obSelectedStatusId(self.obSelectedStatus().id);
		}
	};

	EditFieldTripStatusViewModel.prototype.setStatusTextComputer = function()
	{
		var self = this;
		var item = Enumerable.From(self.obFieldTripStatus()).Where(function(c)
		{
			return c.id === self.obSelectedStatusId();
		}).ToArray()[0];
		return item ? item.name : "";
	};

	EditFieldTripStatusViewModel.prototype.getStatusId = function()
	{
		return this.obSelectedStatusId();
	};

	EditFieldTripStatusViewModel.prototype.apply = function(noComments)
	{
		let self = this, statusId = self.getStatusId(), note = noComments ? "" : self.obComments();

		//RW-45290: copy from tripfinder
		let canceledAndDeclined = [2, 4, 6, 98, 100];
		if (canceledAndDeclined.includes(statusId) && (!note || note.trim() === "") && !noComments)
		{
			return tf.promiseBootbox.alert("A comment must be added before the trip is canceled/declined", "Alert")
				.then(function()
				{
					return false;
				});
		}

		self.selectedRecords.forEach(function(item)
		{
			item.FieldTripStageId = statusId;
			item.FieldTripStageNotes = note;
			// I have no idea what is this field for. 
			// but if don't set it to true, API will 
			// not add new records into FieldTripHistory when stage not change.
			item.IsFieldTripStageNotesChange = true;
		});
		let patchData = self.selectedRecords.reduce((pre, cur) =>
		{
			pre.push({
				Id: cur.Id,
				Op: "replace",
				Path: "FieldTripStageId",
				Value: statusId
			}, {
				Id: cur.Id,
				Op: "replace",
				Path: "FieldTripStageNotes",
				Value: note
			});
			return pre;
		}, []);

		return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), "FieldTrips"), {
			data: patchData
		}).then(function()
		{
			return true;
		});
	};

	EditFieldTripStatusViewModel.prototype.applyWithoutComments = function()
	{
		return this.apply(true);
	};

	EditFieldTripStatusViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};
})();

