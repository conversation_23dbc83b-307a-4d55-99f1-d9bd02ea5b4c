﻿(function()
{
	var namespace = createNamespace("TF.Events");
	namespace.PromiseEvent = PromiseEvent;

	function PromiseEvent()
	{
		TF.Events.Event.call(this);
	};

	PromiseEvent.prototype = Object.create(namespace.Event.prototype);

	PromiseEvent.prototype.constructor = PromiseEvent;

	//the difference between PromiseEvent and Event is PromiseEvent expect handler returns a promise and calls next handler on Promise.then()
	PromiseEvent.prototype.notify = function(args, e, scope)
	{
		var self = this;
		var results = [];
		return new Promise(function(resolve, reject)
		{
			e = e || new TF.Events.EventData();
			var removingHandlers = [];
			function callHandler(i)
			{
				if (i < self.handlers.length && !(e.isPropagationStopped() || e.isImmediatePropagationStopped()))
				{
					var handler = self.handlers[i],
						func = handler.func,
						caller = scope || handler.caller || self,
						handlerResult = func.call(caller, e, args);
					if (handler.one)
					{
						removingHandlers.push(handler);
					}
					// wrap it to a promise function, if it's not
					if (!handlerResult || !handlerResult.then)
					{
						handlerResult = Promise.resolve(handlerResult);
					}

					handlerResult.then(function(result)
					{
						results.push(result);
						callHandler(i + 1);
					}, function()
					{
						reject();
					});
				}
				else
				{
					resolve(results);
				}
			}

			callHandler(0);

			removingHandlers.forEach(i =>
			{
				const index = self.handlers.indexOf(i);
				if (index > -1)
				{
					self.handlers.splice(index, 1);
				}
			});
		});
	};
})();
