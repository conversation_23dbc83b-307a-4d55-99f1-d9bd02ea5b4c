﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.StaffDeletion = StaffDeletion;

	function StaffDeletion()
	{
		this.type = 'staff';
		namespace.BaseDeletion.apply(this, arguments);
	}

	StaffDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	StaffDeletion.prototype.constructor = StaffDeletion;

	StaffDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "trip", "ids", "staff"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'trip',
				items: response.Items
			});
		});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	StaffDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		var p0 = this.getDataPermission(ids, "trip", "staff");

		return Promise.all([p0]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	StaffDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "trip", "staff");

		return Promise.all([p0, p1]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};
})();