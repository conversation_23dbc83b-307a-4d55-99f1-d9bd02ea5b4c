(function()
{
	createNamespace("TF.Modal.Grid").SelectDatesModalViewModel = SelectDatesModalViewModel;

	function SelectDatesModalViewModel(options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-sm";
		self.title("Select Dates");
		this.contentTemplate("Workspace/Grid/SelectDates");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Apply");
		this.selectDatesViewModel = new TF.Control.SelectDatesViewModel(options);
		this.data(this.selectDatesViewModel);
	}

	SelectDatesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SelectDatesModalViewModel.prototype.constructor = SelectDatesModalViewModel;

	SelectDatesModalViewModel.prototype.positiveClick = function()
	{
		this.selectDatesViewModel.apply().then((res) =>
		{
			if (res)
			{
				this.positiveClose(res);
			}
		});
	};

	SelectDatesModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose(false);
	};

})();