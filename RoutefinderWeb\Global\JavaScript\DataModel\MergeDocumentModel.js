(function()
{

	createNamespace("TF.DataModel").MergeDocumentModel = MergeDocumentModel;

	function MergeDocumentModel(entity)
	{
		TF.DataModel.BaseDataModel.call(this, entity);

		var self = this;
		self._dataType = self.dataTypeId;
		self.dataTypeId = ko.pureComputed({
			read: function() { return self._dataType(); },
			write: function(v)
			{
				if (typeof v == "string")
				{
					v = parseInt(v);
				}

				self._dataType(v);
			}
		});
		self.dataTypeId.subscribe(self._changeTracker, [self, "dataTypeId"]);

		self._templateType = self.templateTypeId;
		self.templateTypeId = ko.pureComputed({
			read: function() { return self._templateType(); },
			write: function(v)
			{
				if (typeof v == "string")
				{
					v = parseInt(v);
				}

				self._templateType(v);
			}
		});
		self.templateTypeId.subscribe(self._changeTracker, [self, "templateTypeId"]);

		self.allTemplates = {};
		TF.MergeTemplateTypeHelper.getMergeTemplateTypesWithCustomItem().forEach(function(item)
		{
			self.allTemplates[item.Id] = new TF.DataModel.MergeTemplateTypeModel(item);
		});

		self.newTemplateType = new TF.DataModel.MergeTemplateTypeModel();
		self.templateType = ko.pureComputed(function()
		{
			return self.allTemplates[self._templateType()] || self.newTemplateType;
		});

		self.actualHasHeader = ko.pureComputed({
			read: function()
			{
				if (self.hasHeader() != null)
				{
					return self.hasHeader();
				}

				return self.templateType().hasHeader();
			},
			write: function(value)
			{
				if (value)
				{
					var templateData = self.templateType().toData(),
						preData = $.extend(templateData, self.toData()),
						data = $.extend({}, preData, { HasHeader: value }),
						result = TF.MergeTemplateTypeCalculationHelper.recalculatePageSettingFields(data, null, preData);
					if (!MergeDocumentModel.isValid(result))
					{
						return;
					}
				}

				self.hasHeader(value);
			}
		});

		self.actualHasFooter = ko.pureComputed({
			read: function()
			{
				if (self.hasFooter() != null)
				{
					return self.hasFooter();
				}

				return self.templateType().hasFooter();
			},
			write: function(value)
			{
				if (value)
				{
					var templateData = self.templateType().toData(),
						preData = $.extend(templateData, self.toData()),
						data = $.extend({}, preData, { HasFooter: value }),
						result = TF.MergeTemplateTypeCalculationHelper.recalculatePageSettingFields(data, null, preData);
					if (!MergeDocumentModel.isValid(result))
					{
						return;
					}
				}

				self.hasFooter(value);
			}
		});
	}

	MergeDocumentModel.isValid = function(data)
	{
		var verticalHeight = (data.CellHeight || 0) + (data.Rows - 1) * data.RowSpacing + data.MarginTop + data.MarginBottom
			+ (data.HasHeader ? data.HeaderHeight : 0) + (data.HasFooter ? data.FooterHeight : 0);
		return verticalHeight <= data.PageHeight;
	};

	MergeDocumentModel.prototype = Object.create(TF.DataModel.BaseDataModel.prototype);

	MergeDocumentModel.prototype.constructor = MergeDocumentModel;

	MergeDocumentModel.prototype.getPageSettings = function(items)
	{
		var self = this;
		var result;
		if (self.pageWidth())
		{
			result = new TF.DataModel.MergeTemplateTypeModel();
			TF.MergeTemplateTypeHelper.resetTemplateData(self.toData(), result);

			result.id(self.templateType().id());
			result.name(self.templateType().name());
		}
		else
		{
			self.templateType().roleMergeDocuments(self.roleMergeDocuments() || []);
			result = new TF.DataModel.MergeTemplateTypeModel(self.templateType().toData());
		}
		return result;
	}

	MergeDocumentModel.prototype.refresh = function(items)
	{
		var self = this;
		self.allTemplates = {};
		items.forEach(function(item)
		{
			self.allTemplates[item.Id] = new TF.DataModel.MergeTemplateTypeModel(item);
		});

		self._templateType.notifySubscribers();
	};

	MergeDocumentModel.prototype.mapping = [
		{ from: "Id", to: "id" },
		{ from: "Content", to: "content" },
		{ from: "DataTypeId", to: "dataTypeId" },
		{ from: "Description", to: "description" },
		{ from: "TemplateTypeId", to: "templateTypeId" },
		{ from: "Subject", to: "subject" },
		{ from: "Name", to: "name" },
		{ from: "LastUpdated", to: "lastUpdated" },


		{ from: "PageWidth", to: "pageWidth" }, //N
		{ from: "PageHeight", to: "pageHeight" }, //N

		{ from: "MarginTop", to: "marginTop" },
		{ from: "MarginRight", to: "marginRight" }, //N
		{ from: "MarginBottom", to: "marginBottom" },
		{ from: "MarginLeft", to: "marginLeft" }, //N

		{ from: "CellWidth", to: "cellWidth" }, //N
		{ from: "CellHeight", to: "cellHeight" }, //N

		{ from: "ColumnSpacing", to: "columnSpacing" }, //N
		{ from: "RowSpacing", to: "rowSpacing" },
		{ from: "CellPadding", to: "cellPadding" }, //N

		{ from: "Rows", to: "rows" },
		{ from: "Columns", to: "columns" }, // N

		{ from: "HeaderHeight", to: "headerHeight" },
		{ from: "FooterHeight", to: "footerHeight" },

		{ from: "HasHeader", to: "hasHeader", default: false },
		{ from: "HasFooter", to: "hasFooter", default: false },

		{ from: "HeaderContent", to: "headerContent" },
		{ from: "FooterContent", to: "footerContent" },
		{ from: "RoleMergeDocuments", to: "roleMergeDocuments" },
		{ from: "MergeDocumentReports", to: "mergeDocumentReports" }
	];
})();
