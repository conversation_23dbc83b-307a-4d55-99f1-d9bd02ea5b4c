﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.ReminderDataModel = function(gridFilterEntity)
	{
		namespace.BaseDataModel.call(this, gridFilterEntity);
	};

	namespace.ReminderDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ReminderDataModel.prototype.constructor = namespace.ReminderDataModel;

	namespace.ReminderDataModel.prototype.mapping = [
		{ from: "Id", default: 0, required: true },
		{ from: "DBID", default: 0 },
		{ from: "Name", default: "" },
		{ from: "DataTypeId", default: 0 },
		{ from: "GeoRegionIds", default: null },
		{ from: "NonEligibleZoneId", default: null },
		{ from: "TableName", default: "" },
		{ from: "Statement", default: "" },
		{ from: "UserID", default: 0 },
		{ from: "FilterName", default: "" },
		{ from: "FilterID", default: null },
		{ from: "StaticFilterName", default: null }
	];
})();
