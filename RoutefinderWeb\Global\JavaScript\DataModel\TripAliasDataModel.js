(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.TripAliasDataModel = function(mailingPostalCodeEntity)
	{
		namespace.BaseDataModel.call(this, mailingPostalCodeEntity);
	};

	namespace.TripAliasDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.TripAliasDataModel.prototype.constructor = namespace.TripAliasDataModel;

	namespace.TripAliasDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" }
	];
})();