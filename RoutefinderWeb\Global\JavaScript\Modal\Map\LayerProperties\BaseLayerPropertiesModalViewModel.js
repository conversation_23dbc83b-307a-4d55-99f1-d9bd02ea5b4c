﻿(function()
{
	createNamespace("TF.Modal.Map.LayerProperties").BaseLayerPropertiesModalViewModel = BaseLayerPropertiesModalViewModel;

	function BaseLayerPropertiesModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);

		this.title("Layer Properties");
		this.sizeCss = "modal-dialog-lg";
		this.contentTemplate("workspace/esrimap/layerproperties/base");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Apply");
		this.baseLayerPropertiesViewModel = new TF.Fragment.BaseLayerPropertiesViewModel();
		this.data(this.baseLayerPropertiesViewModel);
	};

	BaseLayerPropertiesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	BaseLayerPropertiesModalViewModel.prototype.constructor = BaseLayerPropertiesModalViewModel;

	BaseLayerPropertiesModalViewModel.prototype.initialize = function() { };

	BaseLayerPropertiesModalViewModel.prototype.positiveClick = function()
	{
		this.positiveClose();
	};

	BaseLayerPropertiesModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose();
	};

	BaseLayerPropertiesModalViewModel.prototype.dispose = function() { };
})();