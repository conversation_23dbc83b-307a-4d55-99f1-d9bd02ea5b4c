@systemColor: #db4d37;
@border-color: #999;

.tfmodal-container {
	.modal-body {
		.reserve-validation-space {
			height: 55px;
		}

		input.new-merge-doc-field {
			&[disabled] {
				color: @border-color;
			}
		}
	}
}

.merge-doc-template-advanced-settings-container {
	display: none;

	.form-group {
		margin-right: -15px;
		padding-left: 10px;
	}

	.input-wrapper-for-new-merge-document-template {
		padding-left: 0;
	}

	.k-dropdown .k-dropdown-wrap {
		background-color: white;
	}

	.k-dropdown .k-dropdown-wrap.k-disabled {
		background-color: #eee;
	}
}

.input-wrapper-for-new-merge-document-template .k-numerictextbox {
	width: 100%;

	.k-numeric-wrap {
		box-shadow: none;

		&.k-focus {
			border-color: rgba(0, 0, 0, 0.4);
		}

		.k-input {
			text-indent: 0px;
			height: 20px;
			line-height: 20px;
			padding: 0px 8px;
			border-radius: 0px;

			&[disabled] {
				cursor: not-allowed;
				background: #eee;
			}
		}
	}
}

.merge-doc-template-subtitle-wrap {
	width: 150px;
	margin-left: -4px;

	.merge-doc-icon {
		display: inline-block;
		height: 20px;
		width: 20px;
		background-size: contain;
		background-position-y: 5px;
		background-repeat: no-repeat;
	}

	.merge-doc-icon.arrow-down {
		background-image: url('../img/arrow_down_black.png');
	}

	.merge-doc-icon.arrow-right {
		background-image: url('../img/arrow_right_black.png');
	}

	.merge-doc-template-subtitle-control {
		font-weight: bold;
		display: inline-block;
		margin-bottom: 10px;
	}
}

.merge-doc-template-subtitle {
	font-weight: bold;
}

.merge-doc-template-subtitle-margin {
	margin-bottom: 5px;
}

.merge-doc-template-subtitle-control,
.merge-doc-template-subtitle {
	color: #000;
}

.save-as-template-checkbox {
	margin-left: -4px;
	margin-top: 20px;
	margin-bottom: 0px;
}

.line-box {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.left-input {
	flex-grow: 250;
}

.right-side {
	flex-grow: 1;
}

.setting {
	width: 22px;
	height: 22px;
	background-image: url('../../global/img/detail-screen/settings_gray.svg');
	background-size: 16px;
	background-color: #eee;
	background-repeat: no-repeat;
	background-position: center;
	border: 1px solid #ccc;
}

.none {
	display: none;
}

.setting {
	&:hover {
		background-color: transparent;
	}
}

.merge-doc-preview-paper-container {
	padding: 0;
}

.merge-doc-preview-paper-container .merge-doc-editor-outline {
	height: 476px;
	background-color: unset;
	border: unset;
}

.merge-doc-template-advanced-settings-container .merge-doc-preview-paper-container .merge-doc-editor-outline {
	padding: 20px 0 0 0 !important;
}

.merge-doc-form-group {
	margin-bottom: 0;

	.right-side {
		input.setting {
			vertical-align: middle;
		}
	}

	.k-dropdown .k-dropdown-wrap {
		background-color: white;
	}
}

.manageDocumentContact-container tr {

	th,
	td {
		&:last-child {
			border-left-width: 0;
		}
	}
}

.merge-template-filed-changed {
	animation: 3s highlightField;
}

@keyframes highlightField {
	0% {
		box-shadow: none;
	}

	17% {
		box-shadow: 0 0 3px yellow;
	}

	83% {
		box-shadow: 0 0 3px yellow;
	}

	100% {
		box-shadow: none;
	}
}

.modal-dialog .bv-form .expand-message-width .help-block {
	width: 214px;
	z-index: 1;
}

.modal-dialog .bv-form .expand-message-width.field-height-offset .help-block {
	margin-left: -162px;
}

.modal-dialog .bv-form .expand-message-width.field-cell-height-offset .help-block {
	margin-left: -116px;
}

.merge-doc-template-advanced-settings-container .merge-doc-editor-outline .mergedoc-editor-paper {
	margin: 0;
}

.merge-doc-editor-outline .mergedoc-editor-paper {
	position: relative;

	.merge-doc-editor-paper-section {
		overflow: hidden;
		position: absolute;
		border: 1px dotted @border-color;
		@supports(-moz-appearance:none)
		{
			border-width: 2px;
		}
	}

	.merge-doc-editor-paper-header {
		border-top: none;
		border-left: none;
		border-right: none;
	}

	.merge-doc-editor-paper-footer {
		border-bottom: none;
		border-left: none;
		border-right: none;
	}
}

.merge-doc-toolbar .k-editor-toolbar {

	.k-selected,
	.k-selected:link {
		background-color: @systemColor;
		border-color: @systemColor;
	}
}

.merge-doc-checkbox {
	margin-left: 15px;
	margin-top: 19px;
	margin-bottom: 3px;
}

#mergeTemplateTypeDropDownList_listbox li:first-child {
	border: none;
	padding: 1px 5px;
	border-bottom: 1px white;
}

#mergeTemplateTypeDropDownList_listbox li:first-child:after {
	background-color: transparent;
	bottom: 0;
	content: '';
	display: block;
	height: 1px;
	left: 50%;
	top: 22px;
	position: absolute;
	transform: translate(-50%, 0);
	width: calc(~"100% - 12px");
	border-bottom: 1px #2e2e2e dashed;
}