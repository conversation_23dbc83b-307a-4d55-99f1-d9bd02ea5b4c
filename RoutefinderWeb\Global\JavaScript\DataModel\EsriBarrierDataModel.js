﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.EsriBarrierDataModel = function(polygonBarrierEntity)
	{
		namespace.BaseDataModel.call(this, polygonBarrierEntity);
	};

	namespace.EsriBarrierDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.EsriBarrierDataModel.prototype.constructor = namespace.EsriBarrierDataModel;

	namespace.EsriBarrierDataModel.prototype.mapping = [
		{ from: "FID", default: null },
		{ from: "Name", default: "Barrier" },
		{ from: "Factor", default: 0, attr: true }
	];
})();