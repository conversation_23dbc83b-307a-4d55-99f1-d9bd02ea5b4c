﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListContactTypeDeletion = DataListContactTypeDeletion;

	function DataListContactTypeDeletion()
	{
		this.type = 'contacttypes';
		this.deleteType = 'Contact Relationship';
		this.deleteRecordName = 'Contact Relationship';
		namespace.DataListBaseDeletion.call(this, true);
	}

	DataListContactTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListContactTypeDeletion.prototype.constructor = DataListContactTypeDeletion;

	DataListContactTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

	DataListContactTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		const self = this;

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `contacttypes/${ids[0]}/status`))
			.then(function(response)
			{
				if (response && response.InUse)
				{
					return [{
						type: 'contacttypes',
						items: [1]
					}];
				}

				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `contacttypes`))
					.then(function(response)
					{
						const contactType = response.Items;
						const isFeedToContactType = contactType.findIndex(g => ids.includes(g.FeedTo)) >= 0;
						if (!isFeedToContactType)
						{
							return []; // Next step would check the response is array or not, if it is empty array, it would prompt a delete confirm message.
						}

						var confirmMessage = "This contact type feeds to another contact type, are you sure you want to delete it?";
						return tf.promiseBootbox.yesNo(confirmMessage, "Delete Confirmation")
							.then(function(ans)
							{
								//if confirm no , delete nothing
								if (!ans)
								{
									self.deleteIds = [];
								}

								return; // Next step would check the response is array OR not, if it is undefined, it would prompt nothing.
							});
					});
			});
	};

	DataListContactTypeDeletion.prototype.getAffectedRecords = function()
	{
		var actionUrl = pathCombine(tf.api.apiPrefixWithoutDatabase(), "recordcontacts"),
			paramData = {
				"@count": "true",
				"@filter": String.format("in(ContactTypeID,{0})", this.deleteIds.join(","))
			};

		return tf.promiseAjax.get(
			actionUrl,
			{
				paramData: paramData
			}
		).then(function(res)
		{
			return res
		});
	}

})();