(function()
{
	createNamespace("TF.Modal.Grid").FindStudentScheduleModalViewModel = FindStudentScheduleModalViewModel;

	function FindStudentScheduleModalViewModel(selectedCount, options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.sizeCss = "modal-dialog-sm";
		self.title("Find Student Schedule");
		self.contentTemplate("Modal/FindStudentSchedule");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel("Apply");
		self.obNegativeButtonLabel("Close");

		self.model = new TF.Control.FindStudentScheduleViewModel(selectedCount, options, self.obDisableControl);
		self.data(self.model);
	}

	FindStudentScheduleModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	FindStudentScheduleModalViewModel.prototype.constructor = FindStudentScheduleModalViewModel;

	FindStudentScheduleModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		var result = self.model.apply();
		self.positiveClose(result);
	};

})();