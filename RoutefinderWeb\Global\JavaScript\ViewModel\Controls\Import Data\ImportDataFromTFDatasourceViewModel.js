(function()
{
	createNamespace('TF.Control').ImportDataFromTFDatasourceViewModel = ImportDataFromTFDatasourceViewModel;

	function ImportDataFromTFDatasourceViewModel(viewModel, selectedDataSource)
	{
		var self = this;
		self.viewModel = viewModel;
		self.selectedDataSource = selectedDataSource;
		self.obStep = ko.observable(1);
		self.obDoArchive = ko.observable("0");
		self.obSelectedDataTypeTables = ko.observableArray();
		self.obAllDataSelectedDataTypeTables = ko.observableArray();
		self.obHasAnyMatchOn = ko.observable(true);
		self.obSelectedDataTypeTables.subscribe(function()
		{
			var array = self.obSelectedDataTypeTables();
			if (array.length == 0)
			{
				return;
			}

			if (array.length > 1)
			{
				array.sort(function(a, b)
				{
					if (a.FilterTable != b.FilterTable)
					{
						return a.FilterTable ? -1 : 1;
					}
					else
					{
						return a.TableName > b.TableName ? 1 : -1;
					}
				});
			}
			array = [{ DataType: 'All', Selected: true, TableName: 'All', FilterTable: false }].concat(array);
			self.obAllDataSelectedDataTypeTables(array);
			if (!self.obAllDataSelectedTable() || array.indexOf(self.obAllDataSelectedTable()) == -1)
			{
				self.obAllDataSelectedTable(array[1]);
			}

			self.initTableOption(self.obSelectedDataTypeTables());
		});

		self.currentTableType = null;
		self.currentAllDataTableType = null;

		self.obHeaderText = ko.computed(function()
		{
			switch (self.obStep())
			{
				case 1:
					return 'Perform Database Archive (Step 1 of 6)';
				case 2:
					return 'Select Data Type (Step 2 of 6)';
				case 3:
					return 'Select Processing Options (Step 3 of 6)';
				case 4:
					return 'Select Data to Import (Step 4 of 6)';
				case 5:
					return 'Pre-Verification (Step 5 of 6)';
				case 6:
					return 'Import Data (Step 6 of 6)';
				default:
					return '';
			}
		});

		self.obSubHeaderText = ko.computed(function()
		{
			switch (self.obStep())
			{
				case 1:
					return 'Select perform archive to create a backup of your current database before import.';
				case 2:
					return 'Select the data you wish to import';
				case 3:
					return 'Specify data processing options here';
				case 4:
					return 'Select the record(s) you would like to import. You may filter the records for table names that have an asterisk"*"';
				case 5:
					return 'Please review the following summary before continuing';
				case 6:
					return 'Prcessing Data';
				default:
					return '';
			}
		});

		self.obUpdate = ko.observable(false);
		self.obUpdateDisabled = ko.observable(false);
		self.obUpdateVisible = ko.observable(false);
		self.obUpdate.subscribe(self.updateExistingClick.bind(self, null, null, true));
		self.obDelete = ko.observable(false);
		self.obDeleteDisabled = ko.observable(false);
		self.obDeleteVisible = ko.observable(false);
		self.obDelete.subscribe(self.deleteRecordsClick.bind(self, null, null, true));
		self.obReset = ko.observable(false);
		self.obResetDisabled = ko.observable(false);
		self.obResetVisible = ko.observable(false);
		self.obReset.subscribe(self.resetManuallyboxClick.bind(self, null, null, true));
		self.obIncludeStopPool = ko.observable(false);
		self.obIncludeDisabled = ko.observable(false);
		self.obIncludeVisible = ko.observable(false);
		self.obIncludeStopPool.subscribe(self.includeStopPoolCheckboxClick.bind(self, null, null, true));
		self.obSchedule = ko.observable(false);
		self.obScheduleDisabled = ko.observable(false);
		self.obScheduleVisible = ko.observable(false);
		self.obSchedule.subscribe(self.scheduleCheckboxClick.bind(self, null, null, true));
		self.obUseStopPool = ko.observable(false);
		self.obUseStopPoolDisabled = ko.observable(false);
		self.obUseStopPoolVisible = ko.observable(false);
		self.obUseStopPool.subscribe(self.useStopPoolCheckboxClick.bind(self, null, null, true));
		self.obCreateDoorToDoor = ko.observable(false);
		self.obCreateDoorToDoorDisabled = ko.observable(false);
		self.obCreateDoorToDoorVisible = ko.observable(false);
		self.obCreateDoorToDoor.subscribe(self.createDoorToDoorCheckboxClick.bind(self, null, null, true));
		self.obStopPools = ko.observableArray([]);
		self.obSelectedStopPool = ko.observable();
		self.obResidence = ko.observable(false);
		self.obResidenceDisabled = ko.observable(false);
		self.obResidenceVisible = ko.observable(false);
		self.obResidence.subscribe(self.residenceCheckboxClick.bind(self, null, null, true));
		self.obUngeocodeClearsSchoolOfResidence = ko.observable(false);
		self.obPopulationRegion = ko.observable(false);
		self.obPopulationRegionDisabled = ko.observable(false);
		self.obPopulationRegionVisible = ko.observable(false);

		self.obAvailableDataModels = ko.observable([]);
		self.obSelectedDataModels = ko.observable([]);
		self.obSelectedResdictIds = ko.observable('');

		self.obSelectedTable = ko.observable();
		self.obSelectedTable.subscribe(self.tableChange.bind(self));
		self.obSelectedTableText = ko.computed(function()
		{
			if (self.obSelectedTable())
			{
				return self.obSelectedTable().TableName;
			}
			return "";
		}, self);

		self.obAllDataSelectedTable = ko.observable();
		self.obAllDataSelectedTable.subscribe(self.allDataTableChange.bind(self));
		self.obAllDataSelectedTableText = ko.computed(function()
		{
			if (self.obAllDataSelectedTable())
			{
				return (self.obAllDataSelectedTable().FilterTable ? '*' : '') + self.obAllDataSelectedTable().TableName;
			}
			return "";
		}, self);

		self.filterTableIds = {};
		self.allDataSelectedArray = {};
		self.allDataUnselectedArray = {};
		self.allDataUnselected = {};
		self.allDataSelected = {};
		self.currentTableCount = ko.observable(0);
		self.totalCount = ko.observable(0);
		self.selectedTotalCount = ko.observable(0);
		self.obFootInfo = ko.computed(function()
		{
			return self.currentTableCount() + ' of ' + self.totalCount() + ' (' + self.selectedTotalCount() + ' selected)';
		});

		self.mapColumnsData = {};
		self.tablesOption = {};
		self.obTotalCount = ko.observable(0);
		self.obTableInfos = ko.observable();
		self.obRecords = ko.observable();

		self.obSelectedFilter = ko.observable();
		self.filterList = [];

		self.obResultInfos = ko.observable();

		self.ImportDataHelper = new TF.Control.ImportDataHelper(self);

		self.UdfFields = {};

		self.obAbortUngeocoded = ko.observable(false);
		self.obUngeocodedNumber = ko.observable();
		self.obAbortUngeocoded.subscribe(self.abortUngeocodedBoxClick.bind(self, null, null, true));
		self.obAbortUngeocoded.subscribe(val =>
		{
			if (val)
			{
				setTimeout(() =>
				{
					$('input[name="UngeocodedNumber"]').data('kendoNumericTextBox').focus();
				});
			}
		});

		self.obAbortDeleted = ko.observable(false);
		self.obDeletedNumber = ko.observable();
		self.obAbortDeleted.subscribe(self.abortDeletedBoxClick.bind(self, null, null, true));
		self.obAbortDeleted.subscribe(val =>
		{
			if (val)
			{
				setTimeout(() =>
				{
					$('input[name="DeletedNumber"]').data('kendoNumericTextBox').focus();
				});
			}
		});

		self.ExcludeRecords = {};
	}

	ImportDataFromTFDatasourceViewModel.prototype.init = function(viewModel, element)
	{
		var self = this;
		self.$element = $(element);
		self.ImportDataHelper.init();

		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix("V2"), "redistricts"));
		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"));
		var p3 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "stoppoolcategories"), {
			paramData: {
				dbid: tf.datasourceManager.databaseId
			}
		});
		var p4 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "userdefinedfields"), {
			paramData: {
				DataTypeId: tf.dataTypeHelper.getIdByName("Staff")
			}
		}).then(function(apiResponse)
		{
			self.UdfFields["Staff"] = [];
			apiResponse.Items.forEach(item =>
			{
				self.UdfFields["Staff"].push({ Destination: item.DisplayName, Source: item.DisplayName, MatchOn: false, IsUDF: true });
			});
		});

		Promise.all([p1, p2, p3, p4]).then(function(response)
		{
			if (response[0] && response[0].Items && response[0].Items.length > 0)
			{
				var items = response[0].Items, schoolGradeRange = {}, allSchools = [];
				if (response[1] && response[1].Items && response[1].Items.length > 0)
				{
					response[1].Items.forEach(function(item)
					{
						schoolGradeRange[item.SchoolCode] = item.GradeRange;
					});
				}
				items.map(function(item)
				{
					allSchools = item.Schools.split('!');
					item.DisplaySchools = allSchools.map(function(schoolCode)
					{
						return schoolCode + (schoolGradeRange[schoolCode] ? "(" + schoolGradeRange[schoolCode] + ")" : "");
					}).join(',');
				});
				self.obAvailableDataModels(items);
			}

			if (response[2] && response[2].Items && response[2].Items.length > 0)
			{
				self.obStopPools(response[2].Items);
				self.obSelectedStopPool(self.obStopPools()[0].Id);
			}
		});
	};

	ImportDataFromTFDatasourceViewModel.prototype.initFilter = function()
	{
		var self = this, $filterElement, tableName = self.obAllDataSelectedTable().TableName;

		if (!self.obAllDataSelectedTable() || self.currentAllDataTableType === tableName)
		{
			return;
		}

		self.$element.find('.grid-filter-container .filter-list').remove();
		self.currentTableFilter = null;
		var filterItem = Enumerable.From(self.filterList).FirstOrDefault(null, function(c) { return c.TableName == tableName; });
		if (filterItem != null)
		{
			self.obSelectedFilter(filterItem.Filter);
		}
		else
		{
			self.obSelectedFilter(null);
		}

		if (this.obAllDataSelectedTable().FilterTable)
		{
			tableName = self.ImportDataHelper.getFilterNameByTableName(tableName);
		}
		else
		{
			tableName = 'student';
		}

		var source = { obSelectedFilter: self.obSelectedFilter };
		$filterElement = $("<div class='filter-list' data-bind='component: {name: " + '"filter-drop-down-list"' + ",params:{ type: " + '"' + tableName + '"' + ", filter:obSelectedFilter, onlyApply:true, disable: " + !this.obAllDataSelectedTable().FilterTable + " }}'></div>");
		ko.applyBindings(source, $filterElement[0]);
		self.$element.find('.grid-filter-container').append($filterElement[0]);

		self.obSelectedFilter.subscribe(self.filterChange, self);
	}

	ImportDataFromTFDatasourceViewModel.prototype.filterChange = function()
	{
		var self = this, tableName = self.obAllDataSelectedTable().TableName, apiUrl = self.ImportDataHelper.getApiUrl(tableName, self.selectedDataSource.DBID);
		if ((self.currentTableFilter && self.obSelectedFilter() && self.currentTableFilter.name() == self.obSelectedFilter().name())
			|| (self.currentTableFilter == null && self.obSelectedFilter() == null))
		{
			return;
		}

		self.currentTableFilter = self.obSelectedFilter();
		for (var i = self.filterList.length - 1; i >= 0; i--)
		{
			if (self.filterList[i].TableName == tableName)
			{
				self.filterList.splice(i, 1);
			}
		}
		if (self.currentTableFilter)
		{
			self.filterList.push({ Filter: self.currentTableFilter, TableName: tableName });
		}
		tf.promiseAjax.post(pathCombine(apiUrl, "search", self.ImportDataHelper.getTableTypeByName(tableName), 'id'),
			{
				paramData:
				{
					take: 100,
					skip: 0,
					getCount: false
				},
				data:
				{
					fields: self.ImportDataHelper.getPreviewColumnsByTable(tableName).map(function(c)
					{
						return c.field;
					}),
					idFilter: null,
					filterSet: null,
					filterClause: self.currentTableFilter ? self.currentTableFilter.whereClause() : null,
					isQuickSearch: false,
					sortItems: [{
						Name: "Id",
						isAscending: "asc",
						Direction: "Ascending"
					}]
				}
			}).then(function(result)
			{
				self.filterTableIds[tableName] = result.Items;
				self.allDataSelectedArray[tableName] = self.filterTableIds[tableName];
				if (self.allDataUnselectedArray.hasOwnProperty(tableName))
				{
					self.allDataSelectedArray[tableName] = self.allDataSelectedArray[tableName].filter(function(id)
					{
						return self.allDataUnselectedArray[tableName].indexOf(id) < 0;
					});
				}
			});

		self.initDataGrid(true);
		var tableStr = '';
		self.obAllDataSelectedDataTypeTables().map(function(c) { return tableStr += c.TableName + ','; });
		self.initFootInfo(tableStr);
	};

	ImportDataFromTFDatasourceViewModel.prototype.tableChange = function()
	{
		var self = this;
		if (!self.obSelectedTable() || self.currentTableType === self.obSelectedTable().TableName)
		{
			return;
		}

		self.currentTableType = self.obSelectedTable().TableName;
		self.initTableOption([self.obSelectedTable()]);
		self.initOptionButtons(self.tablesOption[self.obSelectedTable().TableName]);
		self.initMapColumnGrid();
		self.initDataPreviewGrid();
	}

	ImportDataFromTFDatasourceViewModel.prototype.initTableOption = function(tables)
	{
		var self = this;
		tables.map(function(dataTypeTable)
		{
			var table = dataTypeTable.TableName,
				option;
			if (self.tablesOption.hasOwnProperty(table))
			{
				option = self.tablesOption[table];
			}
			else
			{
				option = self.ImportDataHelper.getTableOptionDefaultSetting(table);
				self.tablesOption[table] = option;
			}
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.initOptionButtons = function(option)
	{
		var self = this;
		self.obUpdate(option.update.default);
		self.obUpdateDisabled(option.update.disabled);
		self.obUpdateVisible(option.update.display);
		self.obDelete(option.delete.default);
		self.obDeleteDisabled(option.delete.disabled);
		self.obDeleteVisible(option.delete.display);
		self.obReset(option.reset.default);
		self.obResetDisabled(option.reset.disabled);
		self.obResetVisible(option.reset.display);
		self.obIncludeStopPool(option.stoppool.default);
		self.obIncludeDisabled(option.stoppool.disabled);
		self.obIncludeVisible(option.stoppool.display);
		self.obSchedule(option.schedule.default);
		self.obScheduleDisabled(option.schedule.disabled);
		self.obScheduleVisible(option.schedule.display);
		self.obUseStopPool(option.useStopPool.default);
		self.obUseStopPoolDisabled(option.useStopPool.disabled);
		self.obUseStopPoolVisible(option.useStopPool.display);
		self.obCreateDoorToDoor(option.createDoorToDoor.default);
		self.obCreateDoorToDoorDisabled(option.createDoorToDoor.disabled);
		self.obCreateDoorToDoorVisible(option.createDoorToDoor.display);
		self.obResidence(option.school.default);
		self.obResidenceDisabled(option.school.disabled);
		self.obResidenceVisible(option.school.display);
		self.obSelectedDataModels(option.selectedDataModels);
		self.obSelectedStopPool(option.selectedStopPool);
		self.obPopulationRegion(option.school.default);
		self.obPopulationRegionDisabled(option.school.disabled);
		self.obPopulationRegionVisible(option.school.display);
	}

	ImportDataFromTFDatasourceViewModel.prototype.allDataTableChange = function()
	{
		this.initFilter();
		this.initDataGrid();
	}

	ImportDataFromTFDatasourceViewModel.prototype.bindALLDataSelectALL = function()
	{
		var self = this, tableName = self.obAllDataSelectedTable().TableName;
		self.$element.find('.allDataSelectALL').off('click').on('click', function()
		{
			var tableStr = '';
			self.obAllDataSelectedDataTypeTables().map(function(c) { return tableStr += c.TableName + ','; });
			var filterString = self.getFilterString();
			self.ExcludeRecords[tableName] = []
			return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "AllImportTables?@count=true"),
				{
					paramData: {
						"databaseId": self.selectedDataSource.DBID,
						"tables": tableStr,
						"getSelectedOnly": true,
						"desc": false,
						"customerSort": false,
						"filters": filterString
					},
					data: self.getExcludeRecords()
				}).then(function(result)
				{
					self.allDataGrid.dataSource._ranges.map(function(cacheData)
					{
						var count = cacheData.data.length;
						for (var i = 0; i < count; i++)
						{
							var item = cacheData.data[i];
							item.Selected = true;
						}
					});

					self.allDataGrid.refresh();

					if (tableName == 'All')
					{
						self.obSelectedDataTypeTables().map(function(c)
						{
							self.allDataSelected[c.TableName] = true;
							self.allDataUnselected[c.TableName] = false;
							self.allDataSelectedArray[c.TableName] = [];
							self.allDataUnselectedArray[c.TableName] = [];
						});
					}

					if (self.filterTableIds.hasOwnProperty(tableName))
					{
						self.allDataSelected[tableName] = false;
						self.allDataSelectedArray[tableName] = self.filterTableIds[tableName];
					}
					else
					{
						self.allDataSelected[tableName] = true;
						self.allDataSelectedArray[tableName] = [];
					}

					self.allDataUnselected[tableName] = false;
					self.allDataUnselectedArray[tableName] = [];
					self.selectedTotalCount(result);
				});
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.bindALLDataClearALL = function()
	{
		var self = this, tableName = self.obAllDataSelectedTable().TableName;
		self.$element.find('.allDataClearALL').off('click').on('click', function()
		{
			var allSelectedTableStr = '';
			self.obAllDataSelectedDataTypeTables().map(function(c) { return allSelectedTableStr += c.TableName + ','; });
			if (tableName == 'All')
			{
				self.ExcludeRecords = {};
			}
			else
			{
				self.ExcludeRecords[tableName] = [];
			}
			var filterString = self.getFilterString();
			return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "AllImportTables?@count=true"),
				{
					paramData: {
						"databaseId": self.selectedDataSource.DBID,
						"tables": allSelectedTableStr,
						"getSelectedOnly": true,
						"desc": false,
						"customerSort": false,
						"filters": filterString
					},
					data: self.getExcludeRecords()
				}).then(function(result)
				{
					self.allDataGrid.dataSource._ranges.map(function(cacheData)
					{
						var count = cacheData.data.length;
						for (var i = 0; i < count; i++)
						{
							var item = cacheData.data[i];
							item.Selected = false;
						}
					});

					self.allDataGrid.refresh();
					if (tableName == 'All')
					{
						self.obSelectedDataTypeTables().map(function(c)
						{
							self.allDataSelected[c.TableName] = false;
							self.allDataUnselected[c.TableName] = true;
							self.allDataSelectedArray[c.TableName] = [];
							self.allDataUnselectedArray[c.TableName] = [];
						});
					}

					self.allDataSelected[tableName] = false;
					self.allDataUnselected[tableName] = true;
					self.allDataSelectedArray[tableName] = [];
					self.allDataUnselectedArray[tableName] = [];
					self.selectedTotalCount(result);
				});
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.bindDataTypeSelectALL = function(data)
	{
		var self = this;
		self.$element.find('.dataTypeSelectALL').off('click').on('click', function()
		{
			var count = data.total();
			for (var i = 0; i < count; i++)
			{
				var item = data.at(i);
				item.Selected = true;
			}

			self.dataTypeGrid.refresh();
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.bindDataTypeClearALL = function(data)
	{
		var self = this;
		self.$element.find('.dataTypeClearALL').off('click').on('click', function()
		{
			self.obSelectedTable(null);
			var count = data.total();
			for (var i = 0; i < count; i++)
			{
				var item = data.at(i);
				item.Selected = false;
			}

			self.dataTypeGrid.refresh();
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.pageChange = function()
	{
		var self = this;
		self.obHasAnyMatchOn(true);
		if (self.obStep() === 2)
		{
			self.initDataTypeGrid();
		}
		else if (self.obStep() === 3)
		{
			self.updateNextButtonDisable();
		}
		else if (self.obStep() === 4)
		{
			self.initDataGrid(null, true);
			var tableStr = '';
			self.obAllDataSelectedDataTypeTables().map(function(c) { return tableStr += c.TableName + ','; });
			self.initFootInfo(tableStr);
		}
		else if (self.obStep() === 5)
		{
			self.initPageInfo();
			self.viewModel.obSaveAndNewButtonLabel("Next");
		}
		else if (self.obStep() === 6)
		{
			self.viewModel.positiveClose(self.operationData);
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.initOperationData = function()
	{
		var self = this, importDataOptions = self.getImportData();
		return TF.ImportAndMergeData.ImportAndMergeDataWizard.createImportOperation(importDataOptions).then(function(data)
		{
			if (data)
			{
				self.operationData = data;
				return true;
			}

			return false;
		});
	};

	ImportDataFromTFDatasourceViewModel.prototype.getImportData = function()
	{
		var self = this, importDataOptions = { TableConfigs: [] };
		self.obSelectedDataTypeTables().map(function(table)
		{
			var tableName = table.TableName,
				tableOptions = self.tablesOption[tableName],
				tableConfig = {};
			if (self.allDataUnselected[tableName] && (!self.allDataSelectedArray[tableName] || self.allDataSelectedArray[tableName].length == 0))
			{
				return;
			}

			tableConfig.DataType = TF.ImportAndMergeData.ImportDataType.find(tableName).id;
			tableConfig.IsUpdateExisting = tableOptions.update.display && !tableOptions.update.disabled && tableOptions.update.default;
			tableConfig.IsDeleteNonexisting = tableOptions.delete.display && !tableOptions.delete.disabled && tableOptions.delete.default;
			tableConfig.IsIncludeStopPool = tableOptions.stoppool.display && !tableOptions.stoppool.disabled && tableOptions.stoppool.default;
			tableConfig.NeedResetLoadTimes = tableOptions.reset.display && !tableOptions.reset.disabled && tableOptions.reset.default;
			tableConfig.IncludeIds = self.allDataSelectedArray[table.TableName];
			if (!tableConfig.IncludeIds || tableConfig.IncludeIds.length == 0)
			{
				tableConfig.ExcludeIds = self.allDataUnselectedArray[table.TableName];
			}
			else
			{
				tableConfig.ExcludeIds = [];
			}

			if (tableName == "Student" && self.obAbortUngeocoded() && tableConfig.IsUpdateExisting)
			{
				tableConfig.AllowUngeocodedPercentage = self.obUngeocodedNumber();
			}

			if (tableName == "Student" && self.obAbortDeleted() && tableConfig.IsDeleteNonexisting)
			{
				tableConfig.AllowDeletedPercentage = self.obDeletedNumber();
			}
			tableConfig.residenceIds = tableOptions.selectedDataModels;
			tableConfig.needFindSchoolResidence = tableOptions.school.display && !tableOptions.school.disabled && tableOptions.school.default;
			tableConfig.needFindSchedule = tableOptions.schedule.display && !tableOptions.schedule.disabled && tableOptions.schedule.default;
			tableConfig.ungeocodeClearsSchoolOfResidence = tableName == "Student" && self.obUngeocodeClearsSchoolOfResidence();
			tableConfig.useStopPool = tableOptions.useStopPool.display && !tableOptions.useStopPool.disabled && tableOptions.useStopPool.default;
			tableConfig.createDoorToDoor = tableOptions.createDoorToDoor.display && !tableOptions.createDoorToDoor.disabled && tableOptions.createDoorToDoor.default;
			tableConfig.stopPoolId = tableOptions.selectedStopPool;
			tableConfig.needFindPopulationRegion = self.obPopulationRegion();
			tableConfig.ColumnConfigs = [];
			self.mapColumnsData[tableName]?.forEach(function(c)
			{
				if (c.Source)
				{
					var columnConfig = {};
					columnConfig.Target = c.Target;
					columnConfig.Source = c.Source;
					columnConfig.MatchOn = c.MatchOn;
					columnConfig.LookupField = c.LookupField;
					columnConfig.LookupAction = c.LookupAction;
					columnConfig.LookupTable = c.LookupTable;
					if (tableName == "Staff" && self.UdfFields["Staff"].find(r => r.Source == c.Target))
					{
						columnConfig.IsUDF = true;
					}

					tableConfig.ColumnConfigs.push(columnConfig);
				}
			});

			importDataOptions.TableConfigs.push(tableConfig);
		});

		importDataOptions.Type = TF.ImportAndMergeData.ImportAndMergeDataType.TransfinderDataSource;
		importDataOptions.SourceDBID = self.selectedDataSource.DBID;
		importDataOptions.TargetDBID = tf.datasourceManager.databaseId;

		return importDataOptions;
	};

	ImportDataFromTFDatasourceViewModel.prototype.initPageInfo = function()
	{
		var self = this, text = '';

		self.obSelectedDataTypeTables().map(function(table)
		{
			var tableName = table.TableName;
			var matchText = '', optionTypeText, includeScheduleText, deleteText, stopPoolText;
			if (self.mapColumnsData.hasOwnProperty(tableName))
			{
				var option = self.tablesOption[tableName];
				optionTypeText = option.update.default ? 'Update Existing' : 'Add Only';
				if (tableName == 'Student')
				{
					includeScheduleText = option.schedule.default ? tableName + ' Include Trip Schedule (New Records Only)' : ' Ignore Trip Schedule';
				}
				deleteText = option.delete.default ? tableName + ' DELETE ALL records not found in Import Source' : '';
				if (tableName == 'Trip')
				{
					stopPoolText = option.stoppool.default ? tableName + ' Include Stop Pool' : ' Ignore Stop Pool';
				}
				self.mapColumnsData[tableName].map(function(data)
				{
					if (data.MatchOn)
					{
						matchText += data.Source + ', ';
					}
				});
			}
			else
			{
				var dataItems = self._getColumnsByTable(tableName);
				dataItems.map(function(t)
				{
					t.Target = t.Source;
					if (t.MatchOn)
					{
						matchText += t.Source + ', ';
					}
				});
				self.mapColumnsData[tableName] = dataItems;

				if (tableName == 'Student')
				{
					optionTypeText = 'Update Existing';
					includeScheduleText = tableName + ' Include Trip Schedule (New Records Only)';
				}
				else
				{
					optionTypeText = 'Add Only';
				}

				if (tableName == 'Trip')
				{
					stopPoolText = ' Ignore Stop Pool';
				}
			}

			if (matchText === '')
			{
				matchText = 'NONE';
			}
			else
			{
				matchText = matchText.substring(0, matchText.length - 2);
			}

			text += tableName + ' (' + optionTypeText + ' - Match on ' + matchText + ')\n';
			if (includeScheduleText)
			{
				text += includeScheduleText + '\n';
			}
			if (deleteText)
			{
				text += deleteText + '\n';
			}
			if (stopPoolText)
			{
				text += stopPoolText + '\n';
			}
		});

		self.obTableInfos(text);

		self.initSelectedRecordsDisplay();
	}

	ImportDataFromTFDatasourceViewModel.prototype.archiveClick = function()
	{
		var dbid = tf.datasourceManager.databaseId;
		var dbName = this._getArchiveDBName(tf.datasourceManager.databaseName);
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "datasources", dbid), {
			data: {
				IncludeDocument: false,
				IncludeStudentPicture: false
			},
			paramData: {
				archiveName: dbName,
				skipLockSource: true,
				isAsync: true
			}
		}).then(function(result)
		{
			tf.loadingIndicator.show();
			var operationId = result.Items[0].Id;
			return new Promise((resolve, reject) =>
			{
				var interval = setInterval(() =>
				{
					tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "operations", operationId), null, { overlay: false }).then(function(response)
					{
						var status = response.Items[0].Status;
						if (status.Finished)
						{
							tf.loadingIndicator.tryHide();
							clearInterval(interval);
							if (status.Error)
							{
								return tf.promiseBootbox.alert("Archive failed.").then(() => { resolve(false); });
							}
							tf.IsDownloading = true;
							window.location = pathCombine(tf.api.apiPrefixWithoutDatabase(), "ArchiveFiles?fileName=" + encodeURIComponent(dbName));
							setTimeout(function()
							{
								tf.IsDownloading = false;
								tf.promiseBootbox.alert('Archive completed').then(() => { resolve(true); });
							}, 500);
						}
					}).catch();
				}, 6000);
			});
		}).catch(function(exception)
		{
			TF.showErrorMessageBox(exception);
		});
	};

	ImportDataFromTFDatasourceViewModel.prototype._getArchiveDBName = function(dbName)
	{
		if (typeof tf.archiveDBNameIndex == "undefined")
		{
			tf.archiveDBNameIndex = 0;
		} else
		{
			tf.archiveDBNameIndex++;
		}

		return dbName + (tf.archiveDBNameIndex > 0 ? `(${tf.archiveDBNameIndex})` : "");
	};

	ImportDataFromTFDatasourceViewModel.prototype._getColumnsByTable = function(tableName)
	{
		let self = this;
		var dataItems = self.ImportDataHelper.getColumnsByTable(tableName);
		if (tableName == "Student" || tableName == "Staff" || tableName == "Vehicle")
		{
			// can not import card id from db to db
			dataItems = dataItems.filter(item => item.Source != "CardID");
		}

		if (tableName == "Staff")
		{
			dataItems = dataItems.concat(self.UdfFields["Staff"]);
		}

		return dataItems;
	};

	ImportDataFromTFDatasourceViewModel.prototype.getFilterString = function()
	{
		var filterString = 'empty', self = this;
		if (self.filterList.length > 0)
		{
			filterString = '';
			self.filterList.map(function(filter)
			{
				filterString += filter.Filter.id() + '|' + filter.TableName + ',';
			});

			filterString = filterString.substring(0, filterString.length - 1);
		}
		return filterString;
	}

	ImportDataFromTFDatasourceViewModel.prototype.initSelectedRecordsDisplay = function()
	{
		var self = this, currentModel = this, apiUrl = self.ImportDataHelper.getApiUrl('All', self.selectedDataSource.DBID), ajaxPromise, filterString;
		filterString = self.getFilterString();

		if (self.selectedDataGrid)
		{
			self.$element.find(".selected-records-container").data().kendoGrid.destroy();
			self.$element.find(".selected-records-container").empty();
		}

		self.selectedDataGrid = self.$element.find(".selected-records-container").kendoGrid({
			dataSource:
			{
				type: "odata",
				transport: {
					read: function(options)
					{
						var tableStr = '';
						self.obAllDataSelectedDataTypeTables().map(function(c) { return tableStr += c.TableName + ','; });
						ajaxPromise = tf.ajax.post(pathCombine(apiUrl, self.ImportDataHelper.getTableTypeByName('All') + "?@take=100&@skip=" + options.data.skip),
							{
								paramData: {
									"databaseId": self.selectedDataSource.DBID,
									"tables": tableStr,
									"getSelectedOnly": true,
									"desc": false,
									"customerSort": false,
									"filters": filterString
								},
								data: self.getExcludeRecords()
							});
						ajaxPromise.then(function(result)
						{
							self.obTotalCount(result.FilteredRecordCount);
							var data = self.ImportDataHelper.getDisplayData(result.Items, 'All');
							options.success({
								d: {
									__count: result.FilteredRecordCount,
									results: data
								}
							});
						});
					}
				},
				pageSize: 100,
				serverPaging: true,
				serverFiltering: true,
				serverSorting: true,
			},
			columns: [
				{
					field: "TableName",
					type: "string",
					width: 180
				},
				{
					field: "Record",
					type: "string",
					width: 700
				}],
			scrollable: {
				virtual: true
			},
			selectable: false,
			sortable: false,
			height: 200,
			pageable: false,
			dataBound: function(e)
			{
			}
		}).data("kendoGrid");
	}

	ImportDataFromTFDatasourceViewModel.prototype.initDataGrid = function(filterChange, refresh)
	{
		var self = this, filterString;
		filterString = self.getFilterString();;

		if (!refresh && !filterChange && (!self.obAllDataSelectedTable() || self.currentAllDataTableType === self.obAllDataSelectedTable().TableName))
		{
			return;
		}

		var currentModel = this, tableName = self.obAllDataSelectedTable().TableName,
			apiUrl = self.ImportDataHelper.getApiUrl(tableName, self.selectedDataSource.DBID), ajaxPromise,
			tableStr = '';
		self.obAllDataSelectedDataTypeTables().map(function(c) { return tableStr += c.TableName + ','; });
		self.currentAllDataTableType = self.obAllDataSelectedTable().TableName;
		if (self.allDataGrid)
		{
			self.$element.find(".all-records-container").data().kendoGrid.destroy();
			self.$element.find(".all-records-container").empty();
		}

		self.allDataGrid = self.$element.find(".all-records-container").kendoGrid({
			dataSource:
			{
				type: "odata",
				transport: {
					read: function(options)
					{
						var sortStr = '';
						if (tableName == 'All')
						{
							var sortByCheck = false, desc = false;
							if (options.data.sort)
							{
								options.data.sort.map(function(c)
								{
									if (c.field != 'Selected')
									{
										sortStr += c.field;
										if (c.dir != 'asc')
										{
											sortStr += '|' + c.dir;
										}
									}
									else if (c.field == 'Selected')
									{
										sortByCheck = true;
										if (c.dir != 'asc')
										{
											desc = true;
										}
									}
								});
							}
							ajaxPromise = tf.ajax.post(pathCombine(apiUrl, self.ImportDataHelper.getTableTypeByName(tableName) + "?@take=100&@skip=" + options.data.skip + (sortStr != '' ? '&@sort=' + sortStr : '')),
								{
									paramData: {
										"databaseId": self.selectedDataSource.DBID,
										"tables": tableStr,
										"getSelectedOnly": false,
										"desc": desc,
										"customerSort": sortByCheck,
										"filters": filterString
									},
									data: self.getExcludeRecords()
								});
						}
						else
						{
							var sortByCheck = false, desc = false, sortItem = {
								Name: "Id",
								isAscending: "asc",
								Direction: "Ascending"
							};
							if (options.data.sort)
							{
								options.data.sort.map(function(c)
								{
									if (c.field != 'Selected' && c.field != 'TableName')
									{
										sortStr += self.ImportDataHelper.getSortProperty(tableName);
										if (c.dir != 'asc')
										{
											sortStr += '|' + c.dir;
											sortItem.isAscending = 'desc';
											sortItem.Direction = 'Ascending';
										}
										sortItem.Name = self.ImportDataHelper.getSortProperty(tableName);
									}
									else if (c.field == 'Selected')
									{
										sortByCheck = true;
										if (c.dir != 'asc')
										{
											desc = true;
										}
									}
								});
							}

							if (self.obAllDataSelectedTable().FilterTable)
							{
								var extendParameter = null;
								if (sortByCheck)
								{
									extendParameter = {
										ImportExcludeRecords: self.getExcludeRecords(),
										ImportSortDesc: desc
									};
								}
								ajaxPromise = tf.ajax.post(pathCombine(apiUrl, "search", self.ImportDataHelper.getTableTypeByName(tableName)),
									{
										paramData:
										{
											take: 100,
											skip: options.data.skip,
											getCount: false
										},
										data:
										{
											fields: self.ImportDataHelper.getPreviewColumnsByTable(tableName).map(function(c)
											{
												return c.field;
											}),
											idFilter: null,
											filterSet: null,
											filterClause: self.obSelectedFilter() ? self.obSelectedFilter().whereClause() : "",
											isQuickSearch: false,
											sortItems: [sortItem],
											ExtendParameter: extendParameter
										}
									});
							}
							else
							{
								if (sortByCheck)
								{
									ajaxPromise = tf.ajax.post(pathCombine(apiUrl, self.ImportDataHelper.getTableTypeByName(tableName), "GetByExcludeRecords?@take=100&@skip=" + options.data.skip),
										{
											paramData: {
												"databaseId": self.selectedDataSource.DBID,
												"desc": desc
											},
											data: self.getExcludeRecords()
										});
								}
								else
								{
									ajaxPromise = tf.ajax.get(pathCombine(apiUrl, self.ImportDataHelper.getTableTypeByName(tableName) + "?@take=100&@skip=" + options.data.skip + (tableName == 'Filter' ? '&dbid=' + self.selectedDataSource.DBID : '') + (sortStr != '' ? '&@sort=' + sortStr : '')));
								}
							}
						}
						ajaxPromise.then(function(result)
						{
							var data = self.ImportDataHelper.getDisplayData(result.Items, tableName);
							var count = tableName == 'All' || !self.obAllDataSelectedTable().FilterTable ? self.totalCount() : result.FilteredRecordCount;
							self.currentTableCount(count);
							options.success({
								d: {
									__count: count,
									results: data
								}
							});
						});
					}
				},
				pageSize: 100,
				serverPaging: true,
				serverFiltering: true,
				serverSorting: true,
			},
			columns: self.getAllDataGridColumns(),
			scrollable: {
				virtual: true
			},
			selectable: false,
			sortable: true,
			height: 380,
			pageable: false,
			dataBound: function(e)
			{
				self.onAllDataGridDataBound(e);
			}
		}).data("kendoGrid");
		self.$element.find(".all-records-container .k-grid-content").height(350);

		self.bindALLDataSelectALL();
		self.bindALLDataClearALL();
	}

	ImportDataFromTFDatasourceViewModel.prototype.initFootInfo = function(tableStr)
	{
		var self = this, text = '', filterString;
		filterString = self.getFilterString();

		var p1 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "AllImportTables?@count=true"),
			{
				paramData: {
					"databaseId": self.selectedDataSource.DBID,
					"tables": tableStr,
					"getSelectedOnly": false,
					"desc": false,
					"customerSort": false,
					"filters": filterString
				},
				data: self.getExcludeRecords()
			});

		var p2 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "AllImportTables?@count=true"),
			{
				paramData: {
					"databaseId": self.selectedDataSource.DBID,
					"tables": tableStr,
					"getSelectedOnly": true,
					"desc": false,
					"customerSort": false,
					"filters": filterString
				},
				data: self.getExcludeRecords()
			});

		Promise.all([p1, p2]).then(function(result)
		{
			self.totalCount(result[0]);
			self.selectedTotalCount(result[1]);
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.allDataSelectedChange = function(data, checked)
	{
		var self = this, key = data.Id;
		if (!self.allDataSelectedArray.hasOwnProperty(data.TableName))
		{
			self.allDataSelectedArray[data.TableName] = [];
		}

		if (!self.allDataUnselectedArray.hasOwnProperty(data.TableName))
		{
			self.allDataUnselectedArray[data.TableName] = [];
		}

		if (checked)
		{
			self.allDataSelectedArray[data.TableName].push(key);
			var index = self.allDataUnselectedArray[data.TableName].indexOf(key);
			if (index >= 0)
			{
				self.allDataUnselectedArray[data.TableName].splice(index, 1);
			}
			self.allDataUnselected['All'] = false;
			self.removeExcludeRecord(data.TableName, data.Id);
			self.selectedTotalCount(self.selectedTotalCount() + 1);
		}
		else
		{
			var index = self.allDataSelectedArray[data.TableName].indexOf(key);
			if (index >= 0)
			{
				self.allDataSelectedArray[data.TableName].splice(index, 1);
			}
			self.allDataUnselectedArray[data.TableName].push(key);
			self.allDataSelected['All'] = false;
			self.addExcludeRecord(data.TableName, data.Id);
			self.selectedTotalCount(self.selectedTotalCount() - 1);
		}
	}

	ImportDataFromTFDatasourceViewModel.prototype.initMapColumnGrid = function()
	{
		var self = this, selectedTable = self.obSelectedTable().TableName, dataItems;

		dataItems = self._getColumnsByTable(selectedTable).filter(function(i) { return !i.ImportDisable; });
		dataItems.map(function(t)
		{
			t.Target = t.Source;
		});
		dataItems.map(function(dataItem)
		{
			if (self.mapColumnsData.hasOwnProperty(selectedTable))
			{
				var dataItemTemp = Enumerable.From(self.mapColumnsData[selectedTable]).FirstOrDefault(null, function(c)
				{
					return c.Destination == dataItem.Destination;
				});

				if (dataItemTemp !== null)
				{
					dataItem.MatchOn = dataItemTemp.MatchOn;
					dataItem.Source = dataItemTemp.Source;
					dataItem.LookupField = dataItemTemp.LookupField;
					dataItem.LookupAction = dataItemTemp.LookupAction;
				}
			}
		});

		var data = new kendo.data.DataSource({
			data: dataItems,
		});

		if (self.mapColumnsGrid)
		{
			self.$element.find(".Map-Columns-container").data().kendoGrid.destroy();
			self.$element.find(".Map-Columns-container").empty();
		}

		self.mapColumnsGrid = self.$element.find(".Map-Columns-container").kendoGrid({
			dataSource: data,
			columns: [{
				title: "Destination",
				field: "Destination",
				type: "string",
				width: 80,
				template: function(item)
				{
					return '<div title="' + item.Destination + '" class="destination-text-ellipsis">' + item.Destination + '</div>'
				}
			}, {
				title: "Source",
				field: "Source",
				template: function(dataItem)
				{
					return '<div class="column-source"></div>'
				},
				width: 80
			},
			{
				title: "Match On",
				field: "MatchOn",
				template: function(dataItem)
				{
					return "<input class='Match-On-Selected' style='margin-left:0;" + (dataItem.IsUDF ? "display:none;" : "") + "' type='checkbox' " + (dataItem.MatchOn ? 'checked' : '') + "/>";
				},
				width: 40
			},
			{
				title: "Lookup Field",
				field: "LookupField",
				template: function(item)
				{
					if (item.LookupTable != null)
					{
						return '<div class="LookupField-source"></div>';
					}
					else
					{
						return $("<div>").text("<None>").html();
					}
				},
				width: 60
			},
			{
				title: "Lookup Action",
				field: "LookupAction",
				template: function(item)
				{
					if (item.LookupTable != null)
					{
						return '<div class="LookupAction-source"></div>';
					}
					else
					{
						return $("<div>").text("<None>").html();
					}
				},
				width: 70
			}],
			height: 234,
			sortable: false,
			pageable: false,
			dataBound: function(e)
			{
				self.onMapColumnsDataBound(e, data, selectedTable);
			}
		}).data("kendoGrid");

		self.$element.find(".Map-Columns-container .k-grid-content").height(183);
	};

	ImportDataFromTFDatasourceViewModel.prototype.onMapColumnsDataBound = function(e, data, selectedTable)
	{
		var self = this;

		var grid = e.sender, self = this;
		if (!self.mapColumnsGrid)
		{
			self.mapColumnsGrid = grid;
		}

		var count = data.total();
		for (var i = 0; i < count; i++)
		{
			var item = data.at(i);
			if (self.mapColumnsData.hasOwnProperty(selectedTable))
			{
				var temp = Enumerable.From(self.mapColumnsData[selectedTable]).FirstOrDefault(null, function(c)
				{
					return c.Destination == item.Destination;
				});
				if (temp === null)
				{
					self.mapColumnsData[selectedTable].push(item);
				}
			}
			else
			{
				self.mapColumnsData[selectedTable] = [item];
			}
		}

		grid.element.find(".Match-On-Selected").off(".selected").on("change.selected", function()
		{
			self.mapColumnsCheckBoxChanged(this, selectedTable);
			self.updateNextButtonDisable();
		});

		setTimeout(function()
		{
			self.bindColumnSource();
		}, 100);
	}

	ImportDataFromTFDatasourceViewModel.prototype.mapColumnsCheckBoxChanged = function(checkbox, selectedTable)
	{
		var self = this, grid = self.mapColumnsGrid, checkBox = $(checkbox), $tr = checkBox.closest("tr"), dataItem;

		dataItem = grid.dataItem($tr);
		dataItem.MatchOn = checkBox.prop("checked");
		if (self.mapColumnsData.hasOwnProperty(selectedTable))
		{
			var data = Enumerable.From(self.mapColumnsData[selectedTable]).FirstOrDefault(null, function(c)
			{
				return c.Destination == dataItem.Destination;
			});

			if (data !== null)
			{
				data.MatchOn = dataItem.MatchOn;
			}
		}
	}

	ImportDataFromTFDatasourceViewModel.prototype.updateNextButtonDisable = function()
	{
		const self = this;
		//Only Select Processing Options can continue
		if (self.obStep() !== 3)
		{
			return;
		}

		let selected = self.mapColumnsGrid.element.find("input[class= 'Match-On-Selected']:checked");
		if (selected.length === 0)
		{
			self.obHasAnyMatchOn(false);
			return;
		}
		for (let index = 0; index < selected.length; index++)
		{
			const element = selected[index];
			let $row = $(element),
				$tr = $row.closest("tr"),
				dataItem = self.mapColumnsGrid.dataItem($tr);
			if (!dataItem.MatchOn || !dataItem.Source)
			{
				self.obHasAnyMatchOn(false);
				return;
			}
		}
		self.obHasAnyMatchOn(true);
	}

	ImportDataFromTFDatasourceViewModel.prototype.bindColumnSource = function()
	{
		var self = this, $sourceContainers, sourceList, $fieldSourceContainers, fieldSourceList, $actionSourceContainers, actionSourceList;

		var $sourceContainers = self.$element.find('.column-source');
		sourceList = self.ImportDataHelper.getDBColumnsByEntityName(self.obSelectedTable().TableName);
		sourceList = [{ ColumnName: '<Not Mapped>' }].concat(sourceList);
		if (self.obSelectedTable().TableName == "Staff")
		{
			let udfFields = self.UdfFields["Staff"];
			for (var i in udfFields)
			{
				let udf = udfFields[i];
				if (!sourceList.find(r => r.ColumnName == udf.Source))
				{
					sourceList.push({ ColumnName: udf.Source, PropertyName: udf.Source });
				}
			}
		}

		var $fieldSourceContainers = self.$element.find('.LookupField-source');
		var $actionSourceContainers = self.$element.find('.LookupAction-source');

		$sourceContainers.map(function(index, sourceContainer)
		{
			self.initColumnSourceDropDownList.call(self, sourceContainer, sourceList);
		});

		$fieldSourceContainers.map(function(index, sourceContainer)
		{
			self.initColumnSourceDropDownList.call(self, sourceContainer, null, 'LookupField');
		});

		$actionSourceContainers.map(function(index, sourceContainer)
		{
			self.initColumnSourceDropDownList.call(self, sourceContainer, null, 'LookupAction');
		});

	}

	ImportDataFromTFDatasourceViewModel.prototype.initColumnSourceDropDownList = function(sourceItemContainer, sourceList, type)
	{
		var self = this, data = self.mapColumnsGrid.dataItem(sourceItemContainer.closest("tr")),
			sourceName = 'Source', validName = 'ColumnName', displayName = 'ColumnName';;
		if (!data)
		{
			return;
		}

		if (!sourceList)
		{
			sourceName = type;
			var lookupTable = TF.ImportAndMergeData.ImportDataType.findById(data.LookupTable);
			if (type == 'LookupField')
			{
				sourceList = self._getColumnsByTable(lookupTable.displayName).filter(function(item) { return item.MatchOn; }).map(function(i) { return { Destination: i.Destination, Source: i.Source } });
				sourceList = [{ Destination: '<None>', Source: '<None>' }].concat(sourceList);
				validName = 'Destination';
				displayName = 'Destination';
			}
			else
			{
				sourceList = $.extend(true, [], self.ImportDataHelper.getValidLookupActions(lookupTable.displayName));
				sourceList = [{ displayName: '<None>', name: '<None>', id: -1 }].concat(sourceList);
				validName = 'id';
				sourceList.map(function(s)
				{
					s.showName = s.displayName;
					delete s.displayName;
				});
				displayName = 'showName';
			}
		}
		sourceList = Enumerable.From(sourceList).OrderBy(function(c) { return c.ColumnName; }).ToArray();
		var obSourceItems = ko.observable(sourceList);
		var sourceItemSelectTemplate = function(name)
		{
			if (name === '<None>')
			{
				return "<a href=\"#\" role=\"option\" >&#60None&#62</a>";
			}
			else if (name === '<Not Mapped>')
			{
				return "<a href=\"#\" role=\"option\" >&#60Not Mapped&#62</a>";
			}
			else
			{
				return "<a href=\"#\" role=\"option\" >" + name + "</a>";
			}
		}

		var selectedSource = Enumerable.From(sourceList).FirstOrDefault(null, function(c) { return c[validName] == data[sourceName] });
		if (selectedSource === null)
		{
			selectedSource = sourceList[0];
		}
		var obSelectedSourceItem = ko.observable(selectedSource);
		var obSelectedSourceItemText = ko.observable(selectedSource[displayName]);
		var source = { obSourceItems: obSourceItems, obSelectedSourceItem: obSelectedSourceItem, obSelectedSourceItemText: obSelectedSourceItemText, sourceItemSelectTemplate: sourceItemSelectTemplate }
		var sourceItemDom = $('<div class="input-group" ' + '><div data-bind="typeahead:{source:obSourceItems,format:function(obj){return obj.' + displayName + ';},drowDownShow:true,notSort:true,selectedValue:obSelectedSourceItem}">\
						<!-- ko customInput:{type:"Select",value:obSelectedSourceItemText,attributes:{class:"form-control",name:"status"}} -->\
						<!-- /ko -->\
						</div>\
						<div class="input-group-btn"><button type="button" class="btn btn-default btn-sharp"><span class="caret"></span></button></div></div>')
		ko.applyBindings(source, sourceItemDom[0]);
		sourceItemContainer.append(sourceItemDom[0]);
		obSelectedSourceItemText.subscribe(self.bindSourceItemValue.bind(self, data, sourceName, sourceList));
	};

	ImportDataFromTFDatasourceViewModel.prototype.dataTableFormatter = function(data)
	{
		return (data.FilterTable ? '*' : '') + data.TableName;
	}

	ImportDataFromTFDatasourceViewModel.prototype.bindSourceItemValue = function(dataItem, sourceName, sourceList, selectedSource)
	{
		var self = this, selectedTable = self.obSelectedTable().TableName;
		if (self.mapColumnsData.hasOwnProperty(selectedTable))
		{
			var dataItemTemp = Enumerable.From(self.mapColumnsData[selectedTable]).FirstOrDefault(null, function(c)
			{
				return c.Destination == dataItem.Destination;
			});

			if (sourceName == 'LookupAction')
			{
				if (selectedSource == "<None>")
				{
					selectedSource = null;
				}
				else
				{
					selectedSource = TF.ImportAndMergeData.LookupAction.find(selectedSource).id;
				}
			}
			else if (sourceName == 'LookupField')
			{
				if (selectedSource == "<None>")
				{
					selectedSource = null;
				}
				else
				{
					var selectedItem = sourceList.find(function(i) { return i.Destination == selectedSource });
					if (selectedItem)
					{
						selectedSource = selectedItem.Source;
					}
					else
					{
						selectedSource = null;
					}
				}
			}


			if (selectedSource == 'None' || (dataItemTemp !== null && dataItemTemp[sourceName] == selectedSource))
			{
				return;
			}
			else
			{
				if (selectedSource == "<Not Mapped>" || selectedSource == "<None>")
				{
					selectedSource = null;
				}

				dataItemTemp[sourceName] = selectedSource;
				self.updateNextButtonDisable();
			}
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.initDataPreviewGrid = function()
	{
		let self = this, tableName = self.obSelectedTable().TableName, apiUrl = self.ImportDataHelper.getApiUrl(tableName, self.selectedDataSource.DBID);
		let url = pathCombine(apiUrl, self.ImportDataHelper.getTableTypeByName(tableName) + "?@take=10");
		if (tableName == "Staff")
		{
			url = url + "&@relationships=udf";
		}
		else if (tableName == "Field Trip")
		{
			url = url + "&@relationships=fieldtripstage,TravelScenario";
		}

		return tf.promiseAjax.get(url).then(function(apiResponse)
		{

			if (self.dataPreviewGrid)
			{
				self.$element.find(".Data-Preview-container").data().kendoGrid.destroy();
				self.$element.find(".Data-Preview-container").empty();
			}

			let dataSource = apiResponse.Items;
			let columns = self.ImportDataHelper.getPreviewColumnsByTable(self.obSelectedTable().TableName);
			if (tableName == "Staff" && dataSource.length > 0)
			{
				for (var j in dataSource)
				{
					let udfItems = dataSource[j].UserDefinedFields;
					for (let i in udfItems)
					{
						let fieldName = udfItems[i].Guid;
						dataSource[j][fieldName] = udfItems[i].RecordValue;

						if (j == 0)
						{
							columns.push({ title: udfItems[i].DisplayName, field: fieldName, width: '150px', type: "string" });
						}
					}
				}
			}

			var data = new kendo.data.DataSource({
				data: dataSource,
			});

			self.dataPreviewGrid = self.$element.find(".Data-Preview-container").kendoGrid({
				dataSource: data,
				columns: columns,
				height: 150,
				sortable: false,
				pageable: false
			}).data("kendoGrid");

			self.$element.find(".Data-Preview-container .k-grid-content").height(115);
		}.bind(this));
	}

	ImportDataFromTFDatasourceViewModel.prototype.initDataTypeGrid = function()
	{
		var self = this;
		if (self.dataTypeGrid)
		{
			return;
		}

		var data = new kendo.data.DataSource({
			data: self.ImportDataHelper.getDataTypeGridSource(),
		});

		self.dataTypeGrid = self.$element.find(".data-type-container").kendoGrid({
			dataSource: data,
			columns: [{
				title: "Data Type",
				field: "DataType",
				type: "string",
				width: 80
			}, {
				title: "Include",
				field: "Selected",
				template: function(dataItem)
				{
					return "<input class='Data-Type-Selected' style='margin-left:0;' type='checkbox' " + (dataItem.Selected ? 'checked' : '') + "/>";
				},
				width: 40
			},
			{
				title: "Table",
				field: "TableName",
				type: "string",
				width: 190
			}],
			height: 405,
			sortable: true,
			pageable: false,
			dataBound: function(e)
			{
				self.onDataBound(e, data);
			}
		}).data("kendoGrid");

		self.bindDataTypeSelectALL(data);
		self.bindDataTypeClearALL(data);
	}

	ImportDataFromTFDatasourceViewModel.prototype.getAllDataGridColumns = function()
	{
		var self = this;

		return [{
			title: "Include",
			field: "Selected",
			template: function(dataItem)
			{
				return "<input class='All-Data-Selected' style='margin-left:0;' type='checkbox' " + (dataItem.Selected ? 'checked' : '') + "/>";
			},
			width: 100
		},
		{
			field: "TableName",
			type: "string",
			width: 180
		},
		{
			field: "Record",
			type: "string",
			width: 700
		}];
	}

	ImportDataFromTFDatasourceViewModel.prototype.onAllDataGridDataBound = function(e)
	{
		var grid = e.sender, self = this;
		if (!self.allDataGrid)
		{
			self.allDataGrid = grid;
		}

		grid.element.find(".All-Data-Selected").off(".selected").on("change.selected", function(e)
		{
			var item = self.allDataGrid.dataItem($(e.currentTarget).closest("tr"));
			var checked = $(e.currentTarget).prop('checked');
			item.set("Selected", checked);
			self.allDataSelectedChange(item, checked);
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.onDataBound = function(e, data)
	{
		var grid = e.sender, self = this;
		if (!self.dataTypeGrid)
		{
			self.dataTypeGrid = grid;
		}

		grid.element.find(".Data-Type-Selected").off(".selected").on("change.selected", function()
		{
			self.checkBoxChanged(this);
		});

		self.obSelectedDataTypeTables([]);
		var array = [];
		var count = data.total();
		for (var i = 0; i < count; i++)
		{
			var item = data.at(i);
			if (item.Selected)
			{
				array.push(item);
			}
		}

		array.sort(function(a, b)
		{
			return a.TableName > b.TableName ? 1 : -1;
		});

		if (array.length > 0)
		{
			self.obSelectedDataTypeTables(array);
			if (!self.obSelectedTable() || array.indexOf(self.obSelectedTable()) == -1)
			{
				self.obSelectedTable(array[0]);
			}
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.checkBoxChanged = function(checkbox)
	{
		var self = this, grid = self.dataTypeGrid, checkBox = $(checkbox), $tr = checkBox.closest("tr"), dataItem;

		dataItem = grid.dataItem($tr);
		dataItem.Selected = checkBox.prop("checked");
		self.checkRelatedTables(dataItem, grid.dataItems()).then(function(result)
		{
			if (!result)
			{
				return;
			}

			if (dataItem.Selected)
			{
				if (self.obSelectedDataTypeTables.indexOf(dataItem) >= 0)
				{
					return;
				}

				self.obSelectedDataTypeTables.push(dataItem);
				if (!self.obSelectedTable())
				{
					self.obSelectedTable(self.obSelectedDataTypeTables()[0]);
				}
			}
			else
			{
				self.obSelectedDataTypeTables.remove(dataItem);
				if (self.obSelectedTable() == dataItem)
				{
					self.obSelectedTable(null);
					if (self.obSelectedDataTypeTables().length > 0)
					{
						self.obSelectedTable(self.obSelectedDataTypeTables()[0]);
					}
				}
			}
		});

		// self.dataTypeGrid.refresh();
	}

	ImportDataFromTFDatasourceViewModel.prototype.checkRelatedTables = function(selectedData, allData)
	{
		var self = this, promise = Promise.resolve(true);

		function addRelatedTables(selectedData)
		{
			allData.filter(function(item)
			{
				if (item.RelatedTables && item.RelatedTables.indexOf(selectedData.TableName) >= 0)
				{
					item.Selected = true;
					addRelatedTables(item);
				}
			});
		}

		if (selectedData.Selected)
		{
			addRelatedTables(selectedData);
			self.dataTypeGrid.refresh();
		}
		else
		{
			if (selectedData.RelatedTables)
			{
				var relatedTables = [], relatedTableNames = '';
				selectedData.RelatedTables.map(function(relatedTable)
				{
					relatedTables.push(Enumerable.From(allData).FirstOrDefault(null, function(c) { return c.TableName == relatedTable; }));
				});

				relatedTables.map(function(relatedTable)
				{
					if (relatedTable.Selected)
					{
						relatedTableNames += relatedTable.TableName + ', ';
					}
				});

				if (relatedTableNames !== '')
				{
					promise = tf.promiseBootbox.yesNo({ message: 'You have selected to import ' + relatedTableNames.substring(0, relatedTableNames.length - 1) + ' but not ' + selectedData.TableName + '. This will result in related value being removed. Do you wish to continue?', backdrop: true, title: "Confirm Message", closeButton: true });
				}
			}
		}

		return promise.then(function(result)
		{
			if (!result)
			{
				selectedData.Selected = true;
				self.dataTypeGrid.refresh();
				return false;
			}
			return true;
		});
	}

	ImportDataFromTFDatasourceViewModel.prototype.updateExistingClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].update.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obUpdate(!self.obUpdate());
		}

		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].update.default = self.obUpdate();
			self.obResetDisabled(!self.obUpdate());
			self.tablesOption[self.obSelectedTable().TableName].reset.disabled = self.obResetDisabled();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.deleteRecordsClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].delete.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obDelete(!self.obDelete());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].delete.default = self.obDelete();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.resetManuallyboxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].reset.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obReset(!self.obReset());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].reset.default = self.obReset();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.includeStopPoolCheckboxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].stoppool.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obIncludeStopPool(!self.obIncludeStopPool());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].stoppool.default = self.obIncludeStopPool();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.createDoorToDoorCheckboxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].createDoorToDoor.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obCreateDoorToDoor(!self.obCreateDoorToDoor());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].createDoorToDoor.default = self.obCreateDoorToDoor();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.useStopPoolCheckboxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].useStopPool.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obUseStopPool(!self.obUseStopPool());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].useStopPool.default = self.obUseStopPool();
			if (self.obSelectedStopPool())
			{
				self.tablesOption[self.obSelectedTable().TableName].selectedStopPool = self.obSelectedStopPool();
			}
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.selectedStopPoolChange = function(e)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].useStopPool.disabled)
		{
			return;
		}

		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].selectedStopPool = e.sender.value();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.scheduleCheckboxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].schedule.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obSchedule(!self.obSchedule());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].schedule.default = self.obSchedule();
			self.obUseStopPoolDisabled(!self.obSchedule());
			self.tablesOption[self.obSelectedTable().TableName].useStopPool.disabled = self.obUseStopPoolDisabled();
			self.obCreateDoorToDoorDisabled(!self.obSchedule());
			self.tablesOption[self.obSelectedTable().TableName].createDoorToDoor.disabled = self.obCreateDoorToDoorDisabled();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.residenceCheckboxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].school.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obResidence(!self.obResidence());
		}
		if (self.obSelectedTable())
		{
			self.tablesOption[self.obSelectedTable().TableName].school.default = self.obResidence();
		}
	};

	ImportDataFromTFDatasourceViewModel.prototype.populationRegionClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (self.obSelectedTable() && self.tablesOption[self.obSelectedTable().TableName].school.disabled)
		{
			return;
		}

		if (!checkClick)
		{
			self.obPopulationRegion(!self.obPopulationRegion());
		}
	};

	/**
	 * Open school of residence modal.
	 * @return {void}
	 */
	ImportDataFromTFDatasourceViewModel.prototype.openSelectBoundaryModalBtnClick = function()
	{
		var self = this;
		tf.modalManager.showModal(
			new TF.Modal.SelectBoundarySetsModalViewModel(
				self.obAvailableDataModels(),
				self.obSelectedDataModels()
			)
		).then(function(result)
		{
			if (result)
			{
				self.obSelectedDataModels(result);
				var resdictIds = '';
				result.map(function(res)
				{
					resdictIds += res.Id + ','
				});
				self.obSelectedResdictIds(resdictIds.slice(0, -1));
				self.tablesOption[self.obSelectedTable().TableName].selectedDataModels = self.obSelectedDataModels();
			}
		});
	};

	ImportDataFromTFDatasourceViewModel.prototype.validatePage = function(pageIndex, operationType)
	{
		var self = this;
		if (pageIndex === 0)
		{
			self.viewModel.negativeClose();
			return Promise.resolve(false);
		}
		else if (pageIndex === 2 && self.obDoArchive() === '0')
		{
			return self.archiveClick().then(function()
			{
				self.obDoArchive('1');
				return Promise.resolve(true);
			});
		}
		else if (pageIndex === 3)
		{
			if (self.obSelectedDataTypeTables().length === 0)
			{
				tf.promiseBootbox.alert('No table selected');
				return Promise.resolve(false);
			}

			if (operationType == TF.ImportAndMergeData.ImportAndMergeOperationType.Next)
			{
				return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "operationvalidations"), {
					data: self.getImportData()
				}).then((response) =>
				{
					let validation = response.Items[0];
					if (!validation.Status)
					{
						tf.promiseBootbox.alert(validation.Message);
					}

					return validation.Status;
				});
			}
		}
		else if (pageIndex === 4)
		{
			if ((self.obAbortUngeocoded() && isNullObj(self.obUngeocodedNumber())) || (self.obAbortDeleted() && isNullObj(self.obDeletedNumber())))
			{
				tf.promiseBootbox.alert('Safety checks value is required.');
				return Promise.resolve(false);
			}
		}
		else if (pageIndex === 6)
		{
			if (self.obTotalCount() === 0)
			{
				tf.promiseBootbox.alert('No record selected');
				return Promise.resolve(false);
			}

			return self.initOperationData();
		}

		return Promise.resolve(true);
	};

	/**
	  * Click abort ungeocoded checkbox.
	  * @return {void}
	  */
	ImportDataFromTFDatasourceViewModel.prototype.abortUngeocodedBoxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (!checkClick)
		{
			self.obAbortUngeocoded(!self.obAbortUngeocoded());
			if (self.obAbortUngeocoded())
			{
				$('input[name="UngeocodedNumber"]').data('kendoNumericTextBox').focus();
			}
			return;
		}
	};

	/**
  * Click abort deleted checkbox.
  * @return {void}
  */
	ImportDataFromTFDatasourceViewModel.prototype.abortDeletedBoxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (!checkClick)
		{
			self.obAbortDeleted(!self.obAbortDeleted());
			if (self.obAbortDeleted())
			{
				$('input[name="DeletedNumber"]').data('kendoNumericTextBox').focus();
			}
			return;
		}
	};

	/**
 * Valid numeric input
 * @param  {viewModel} data ImportDataFromTFDatasourceViewModel.
 * @param  {Event} e jQuery Event object.
 * @returns {boolean}
 */
	ImportDataFromTFDatasourceViewModel.prototype.numericalValidation = function(data, e)
	{
		var key = e.which || e.keyCode || 0;
		// Only number keys and backspace, arrowleft,arrowright are available, value is in the range of [1,100]
		if (key == 37 || key == 39 || key == 8)
		{
			return true;
		}
		if (!isNumber(e.key) || (e.target.value + e.key > 100 || e.key == 0 && e.target.value == 0))
		{
			e.preventDefault();
			e.stopPropagation();
			return false;
		}

		return true;
	};

	ImportDataFromTFDatasourceViewModel.prototype.addExcludeRecord = function(type, recordID)
	{
		if (!this.ExcludeRecords[type])
		{
			this.ExcludeRecords[type] = [];
		}

		this.ExcludeRecords[type].push(recordID);
	};

	ImportDataFromTFDatasourceViewModel.prototype.removeExcludeRecord = function(type, recordID)
	{
		if (!this.ExcludeRecords[type])
		{
			return;
		}

		this.ExcludeRecords[type] = this.ExcludeRecords[type].filter(item => item != recordID);
	};

	ImportDataFromTFDatasourceViewModel.prototype.getExcludeRecords = function()
	{
		let response = []
		for (var type in this.ExcludeRecords)
		{
			if (this.ExcludeRecords[type])
			{
				response = response.concat(this.ExcludeRecords[type].map(id => { return { TableName: type, RecordID: id } }));
			}
		}

		return response;
	}
})();