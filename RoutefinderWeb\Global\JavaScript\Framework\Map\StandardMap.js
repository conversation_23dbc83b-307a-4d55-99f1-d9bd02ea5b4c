﻿(function()
{
	createNamespace("TF.Map").StandardMap = StandardMap;

	function StandardMap()
	{
		TF.Map.BaseMap.apply(this, arguments);

		this.divId = 'map';
		this.basemapStyle = {
			streets: 'streets',
			satellite: 'satellite',
			topo: 'topo'
		};
		this.basemap = this.basemapStyle.streets;
		this.mapPOI = {
			Schenectady: [-73.892176, 42.819396],
			Dubai: [55.2924552, 25.2163783],
			Manhattan: [-73.994, 40.728],
			USA: [-97.070492, 39.245916]
		};
		this.centerCoordinate = [-73.892176, 42.819396];
		this.zoomLevel = 12;

		this.sliderPositionStyle = {
			topLeft: 'top-left',
			topRight: 'top-right',
			bottomLeft: 'bottom-left',
			bottomRight: 'bottom-right'
		};
		this.sliderPosition = this.sliderPositionStyle.bottomRight;
		this.sliderStyle = {
			small: 'small'
		};
		this.logo = false;
		this.showAttribution = false;
		this.allowZoom = true;
		this.mapDiv = null;

		this.editing = false;
	};

	StandardMap.prototype = Object.create(TF.Map.BaseMap.prototype);
	StandardMap.prototype.constructor = StandardMap;

	StandardMap.prototype.createMap = function()
	{
		var divs = $('.' + this.divId);
		var mapDivCount = divs.length;
		var div = this.mapDiv == null ? divs[mapDivCount - 1] : this.mapDiv[0];

		$(div).attr('style', '');  // initial map container

		this.map = new this.ArcGIS.Map(div, {
			basemap: this.basemap,
			center: this.centerCoordinate,
			zoom: this.zoomLevel,
			logo: this.logo,
			showAttribution: this.showAttribution,
			sliderPosition: this.sliderPosition,
			sliderStyle: this.sliderStyle.small
		});
	};

	StandardMap.prototype.setSliderPosition = function(position)
	{
		this.sliderPosition = position;
	};

	StandardMap.prototype.setCenterCoordinate = function(mapPoint)
	{
		if (this.map)
		{
			this.map.centerAt(mapPoint);
		} else
		{
			this.centerCoordinate = mapPoint;
		}
	};

	StandardMap.prototype.setBasemap = function(basemapStyle)
	{
		if (this.map)
		{
			this.map.setBasemap(basemapStyle);
		} else
		{
			this.basemap = basemapStyle;
		}
	};

	StandardMap.prototype.setStreetsMap = function() { this.setBasemap(this.basemapStyle.streets); };

	StandardMap.prototype.setSatelliteMap = function() { this.setBasemap(this.basemapStyle.satellite); };

	StandardMap.prototype.setTopographicalMap = function() { this.setBasemap(this.basemapStyle.topo); };

	StandardMap.prototype.setPanCursor = function()
	{
		if (this.map)
		{
			this.map.on("mouse-drag-start", function()
			{
				this.map.setMapCursor("url(../../Global/img/PanTool16.png), auto");
			}.bind(this));

			this.map.on("mouse-drag-end", function()
			{
				this.editing ? this.map.setMapCursor('crosshair') : this.map.setMapCursor("default");
			}.bind(this));
		} else
		{
			console.warn("Create map first!");
		}
	};

	StandardMap.prototype.addLayer = function(layers, callback)
	{
		// layers - array of feature layers
		if (this.map && layers.length)
		{
			if (callback)
			{
				var mapLayersAddResultEvent = this.map.on('layers-add-result', function(layers)
				{
					mapLayersAddResultEvent.remove();

					callback(this._status.SUCCESS);
				}.bind(this));
			}
			this.map.addLayers(layers);
		}
	};

	StandardMap.prototype.addFeature = function(layerId, graphic)
	{
		if (this.map)
		{
			var map = this.map;
			var layer = null;

			layer = map.getLayer(layerId);
			if (layer)
			{
				layer.add(graphic);
			}

			layer = null;
			map = null;
		}
	};

	StandardMap.prototype.clearLayer = function(layerId)
	{
		if (this.map)
		{
			var layer = this.map.getLayer(layerId);
			if (layer)
				layer.clear();

			layer = null;
		}
	};

	StandardMap.prototype.redrawLayer = function(layerId)
	{
		if (this.map)
		{
			var layer = this.map.getLayer(layerId);
			if (layer)
				layer.redraw();

			layer = null;
		}
	};

	StandardMap.prototype.zoomToLayer = function(layerId, callback)
	{
		if (this.map)
		{
			if (layerId)
			{
				var layer = this.map.getLayer(layerId);
				if (layer)
				{
					var graphics = layer.graphics;
					var extent = this.ArcGIS.graphicsUtils.graphicsExtent(graphics);
					this.zoomToExtent(extent);

					if (callback)
					{
						var mapExtentChangeEvent = this.map.on('extent-change', function()
						{
							mapExtentChangeEvent.remove();
							mapExtentChangeEvent = null;

							callback(this._status.SUCCESS);
						}.bind(this));
					}
					graphics = null;
					extent = null;
				}
				layer = null;
			} else
			{
				// zoom to full extent

			}
		}
	};

	StandardMap.prototype.removeLayerById = function(layerId)
	{
		if (this.map)
		{
			var layer = this.map.getLayer(layerId);
			if (layer)
			{
				this.map.removeLayer(layer);
			}
			layer = null;
		}
	};

	StandardMap.prototype.removeLayer = function(layer)
	{
		if (this.map)
		{
			this.map.removeLayer(layer);
		}
	};

	StandardMap.prototype.zoomToExtent = function(extent)
	{
		if (extent.spatialReference.wkid == this.map.spatialReference.wkid)
		{
			// Sets the extent of the map. The extent must be in the same spatial reference as the map.
			// When true, for maps that contain tiled map service layers, you are guaranteed to have the input extent shown completely on the map. The default value is false.
			this.map.setExtent(extent, true);
		} else
		{
			console.warn('WARNING: The extent must be in the same spatial reference as the map!');
		}
	};



})();