.whiteboard-palette {
	background-color: #fff;

	.whiteboard-menu {
		.menu {
			&>ul {
				padding: 0 0 10px 0 !important;

				li.menu-divider {
					margin-bottom: 10px;
					margin-top: 10px;
				}
			}

			.sub-menu {
				.menu-item.nextLevelMenuItem {
					>.menu-label.text {

						>.text-content {
							display: inline-block;
							max-width: calc(100% - 25px);
							overflow: hidden;
							text-overflow: ellipsis;
							padding-right: 0;
							white-space: nowrap;
						}

						>.k-icon.k-i-arrow-e {
							position: relative;
							right: 0;
						}
					}
				}
			}
		}
	}

	.whiteboard-content-container {
		overflow: visible;

		.whiteboard {
			.whiteboard-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 33px;

				.title {
					max-width: 100%;
					width: auto;
					flex: 1;
				}

				.buttons {
					// width: 100px;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.icon {
						height: 20px;
						width: 16px;
						background-size: 16px 16px;
						margin-right: 5px;
						cursor: pointer;
						background-repeat: no-repeat;
						display: none;
						background-position: center center;
					}

					.eye-icon {
						width: 18px;
						height: 18px;
						margin: 10px 8px 10px 0px;
						cursor: pointer;
					}
				}

				&:hover {
					.icon {
						display: block;
					}
				}
			}

			.whiteboard-content {
				&:hover {
					overflow: visible;

					&.collapsed {
						overflow: hidden;
					}
				}

				overflow: hidden;

				transition: max-height 0.4s ease;

				.whiteboard-menu {
					height: 32px;

					.with-sub-menu {
						&>ul {
							padding: 0 0 10px 0 !important;

							li.menu-divider {
								margin-bottom: 10px;
								margin-top: 10px;

								.rule {
									background-color: #9b9b9b;
									height: 1px;
									width: 82% !important;
									margin-left: 40px;
									position: relative;
									top: 1px;
								}
							}
						}

						.sub-menu.last {
							border-top: 1px solid #c5c5c5;
						}

						li.nextLevelMenuItem {
							>.text {
								height: 30px;

								>span.text-content {
									text-overflow: ellipsis;
									overflow: hidden;
									width: 180px;
									white-space: nowrap;
									float: left;
									height: 30px;
									line-height: 30px;
									text-align: left;
								}
							}
						}
					}

					.parcelpoint-tool {
						box-sizing: unset !important;
					}
				}

				.whiteboard-layers {
					.whiteboard-layer-item {
						float: left;
						width: 100%;
						font-weight: bold;
						background-color: #fff;
						display: flex;
						justify-content: space-between;

						.sketch {
							float: left;
							width: 100%;
							background-color: #fff;
							position: relative;

							&.highlight {
								background-color: #FFFFEC;
							}

							&:hover .close-button {
								display: block;
							}

							.close-button {
								display: none;
								position: absolute;
								top: 0;
								left: -20px;
								height: 45px;
								width: 20px;
								cursor: pointer;
								z-index: 150;
								background-color: #4B4B4B;
								background-image: url('../Img/Routing Map/clear_white.png');
								background-size: 16px;
								background-position: 2px 13px;
								background-repeat: no-repeat;
							}

							.sketch-wrapper .buttons .icon {
								margin-right: 5px;
							}
						}

						.sketch-layer-footer {
							background-color: #F2F2F2;
							padding: 5px 1px;
							position: relative;
							box-sizing: content-box;
							display: flex;
							justify-content: center;
							align-items: center;
							flex-direction: column;
							font-size: 14px;
							font-weight: normal;
						}
					}

					.bottom-info-container {
						background-color: #F2F2F2;
						padding: 5px 1px;
						position: relative;
						border-bottom: 2px solid #dedede;
						box-sizing: content-box;
						display: flex;
						justify-content: center;
						align-items: center;
						flex-direction: column;
						font-size: 14px;
					}
				}
			}
		}
	}
}

.whiteboardManageModal {
	.newWhiteboard {
		margin-bottom: 6px;
	}

	.whiteboard-footer-records {
		text-align: right;
		padding-top: 5px;
		padding-right: 35px;
	}
}

.sharedWhiteboard-group {
	font-weight: bold;
	font-size: 15px;
}


.whiteboardModal {
	.whiteboardName-group {
		height: 45px;
	}

	.whiteboardDescription-group {
		height: 75px;

		.whiteboardDescription {
			height: 50px;
		}
	}

	.shareWhiteboard {
		margin-bottom: 20px;
	}

	.whiteboardURL-group {
		height: 45px;

		.copyUrl-container {
			display: flex;

			input {
				height: 28px;
				margin-right: 10px;
				background: none !important;
				cursor: auto;
			}

			.copy-button {
				margin: auto;
				width: 128px;
				line-height: 24px;
				color: #333333;
				border: 1px solid #999;
				text-align: center;
				cursor: pointer;
				border-radius: 5px;
				font-size: 14px;
			}
		}
	}
}


.routingmap_panel .list-container .whiteboard-palette {
	.show-eye {
		background: url('../img/Icons/eye.svg') center center no-repeat;
		filter: grayscale(1) brightness(0.3);
	}

	.hide-eye {
		background: url('../img/Icons/eye-slash.svg') center center no-repeat !important;
		filter: grayscale(1) brightness(0.3);
	}
}

.dock-parent-container.edit-whiteboard-sketch {
	.parcel-edit-overlay {
		z-index: 18999;
	}

	.parcel-edit-modal-container {
		z-index: 19999;
	}
}