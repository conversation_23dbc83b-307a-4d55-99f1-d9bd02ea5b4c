(function()
{
	createNamespace("TF.Helper").HelpTooltip = HelpTooltip;

	const dataKey = "tf-help-tooltip";
	const helpCache = {};
	let showingTooltip = null;
	let showingTooltipKey = null;

	function HelpTooltip(tooltipOptions)
	{
		TF.Helper.TFTooltip.call(this);
		tooltipOptions.el.data(dataKey, this);
		this.tooltipOptions = tooltipOptions;
		this.obTooltipText = ko.observable("");
		this.obTooltipLink = ko.observable("");
		this.obTooltipText(tooltipOptions.message);
		let link = tooltipOptions.link;
		if (link)
		{
			link = link.toLowerCase().replace(tf.defaultCommunityUrl, tf.communityUrl);
			link = `${tf.communityUrl}?external_login_url=${TF.URLHelper.getPageUrl("communitylogin.html")}&external_redirect_url=${link}`;
			this.obTooltipLink(link);
		}

		this.tooltipContent = $(this.createTooltipElement());
		ko.applyBindings(this, this.tooltipContent[0]);
		this.initTooltip();
	}

	HelpTooltip.prototype = Object.create(TF.Helper.TFTooltip.prototype);
	HelpTooltip.prototype.constructor = HelpTooltip;

	HelpTooltip.prototype.initTooltip = function()
	{
		if (showingTooltip)
		{
			showingTooltip.hide();
		}

		showingTooltip = this;
		let el = this.tooltipOptions.el;
		// bootstrap tooltip uses title attribute with higher prioriry,
		// so remove it. And will add it back in dispose method.
		this.originTitle = el.attr("title");
		el.removeAttr("title");

		var options = {};
		options.html = true;
		options.title = this.tooltipContent;
		options.trigger = "manual";
		options.className = "help-tooltip";
		options.animation = false;
		options.placement = this.tooltipOptions.placement || "bottom";
		this.init(el, options);

		/*
		Below events are to take of follwing scenarios : 
		1. When a user hovers over the help icon open the help text
		2. If a user moves their mouse over the help text keep the help text open
		3. If a user moves the mouse away from the help icon and help text the help text should close
		*/

		$('body').on('mouseleave.' + dataKey, '.help-tooltip', () =>
		{
			this.hide();
		});

		el.on('mouseleave.' + dataKey, () =>
		{
			if (this.hideTimer != null)
			{
				clearTimeout(this.hideTimer);
			}

			this.hideTimer = setTimeout(() =>
			{
				this.hideTimer = null;
				if (!options.title.closest(".tooltip:hover").length)
				{
					this.hide();
				}
			}, 100);
		}).on("click." + dataKey, () =>
		{
			this.hide();
		});;
	};

	HelpTooltip.prototype.hide = function()
	{
		this.tooltipOptions.el.tooltip('hide');
		this.dispose();
	};

	HelpTooltip.prototype.show = function()
	{
		this.tooltipOptions.el.tooltip('show');
	};

	HelpTooltip.prototype.createTooltipElement = function()
	{
		return `<div class="modal-dialog modal-dialog-xs">
						<div class="modal-content">
							<div class="modal-header">
								<div class="panel-heading">
									<div class="community-logo"></div>
									<span class="title">Guide</span>
								</div>
							</div>
							<div class="modal-body">
								  <div data-bind="text: obTooltipText()"></div>
							</div>
							<!-- ko if: obTooltipLink() -->
							<div class="modal-footer">
								<a data-bind="attr: { href: obTooltipLink() }" target="_blank"> Learn More in Community </a>
							</div>
							<!-- /ko -->
						</div>
					</div >`;
	};

	function getHelpTooltipText(key)
	{
		if (helpCache[key])
		{
			return Promise.resolve(helpCache[key]);
		}

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "helpmodes?key=" + key), null, { overlay: false })
			.then(r =>
			{
				let cache = r.Items[0] || {};
				cache.key = key;
				return helpCache[key] = cache;
			});
	};

	HelpTooltip.prototype.dispose = function()
	{
		this.destroy(this.tooltipOptions.el);
		$("body").off("." + dataKey);
		this.tooltipOptions.el.off("." + dataKey);
		this.tooltipOptions.el.removeData(dataKey);
		if (this.originTitle)
		{
			this.tooltipOptions.el.attr("title", this.originTitle);
		}

		if (this.hideTimer != null)
		{
			clearTimeout(this.hideTimer);
			this.hideTimer = null;
		}

		// Sometimes tooltipOptions.el is removed, destroy method doesn't work.
		// So we delete the tip element directly
		let exists = $.contains(document.body, this.tooltipOptions.el[0]);
		if (!exists)
		{
			$(".tooltip.tf-tooltip.help-tooltip").remove();
		}

		showingTooltip = showingTooltip === this ? null : showingTooltip;
	};

	HelpTooltip.init = function()
	{
		if (tf.userPreferenceManager.get("rfweb.showHelpMode"))
		{
			$(document.body).addClass("help-mode");
		}

		$(document.body).on("mouseenter", "[help-key]", eventData =>
		{
			if (!tf.userPreferenceManager.get("rfweb.showHelpMode"))
			{
				return;
			}

			let element = $(eventData.currentTarget);
			if (element.data(dataKey))
			{
				return;
			}

			let key = element.attr("help-key");
			if (!key)
			{
				return;
			}

			showingTooltipKey = key;
			getHelpTooltipText(key).then(r =>
			{
				if (showingTooltipKey != r.key)
				{
					return;
				}

				let placement = element.attr("help-placement") || "bottom";
				new HelpTooltip({ el: element, type: key, placement, message: r.Message, link: r.Link }).show();
				showingTooltipKey = null;
			});
		});
	};
})();