﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListNoTransportationTypeDeletion = DataListNoTransportationTypeDeletion;

	function DataListNoTransportationTypeDeletion(type)
	{
		this.type = 'notransportationtypes';
		this.deleteType = type || 'No Transportation Requirement Type';
		this.deleteRecordName = 'No Transportation Requirement Type';
		this.isDisplayName = true;
		namespace.DataListBaseDeletion.call(this, true);
	}

	DataListNoTransportationTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListNoTransportationTypeDeletion.prototype.constructor = DataListNoTransportationTypeDeletion;

	DataListNoTransportationTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

	DataListNoTransportationTypeDeletion.prototype.publishData = function(ids)
	{//need to refresh the grid
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "notransportationtypes", pb.DELETE), ids);
	};

	DataListNoTransportationTypeDeletion.prototype.getAssociatedData = function(deleteIds)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "studentrequirements"),
			{
				paramData: { "@filter": "in(NoTransportationId," + deleteIds.join(",") + ")" }
			})
			.then(function(response)
			{
				associatedDatas.push({
					type: 'studentrequirement',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	};
})();