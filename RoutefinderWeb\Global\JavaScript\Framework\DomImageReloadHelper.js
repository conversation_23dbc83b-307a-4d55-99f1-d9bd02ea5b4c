(function()
{
	createNamespace("TF").DomImageReloadHelper = DomImageReloadHelper;
	function DomImageReloadHelper()
	{
	};

	DomImageReloadHelper.urlTag = "data-url";
	DomImageReloadHelper.backGroundClass = "linked-image-placeholder";

	DomImageReloadHelper.ReloadImage = function(html)
	{
		if (html === null)
		{
			return html;
		}

		html = "<div>" + html + "</div>";
		var $content = $(html);
		var $imgs = $content.find("img[" + DomImageReloadHelper.urlTag + "]");
		$imgs.each(function(i,img)
		{
			var $img = $(img);
			$img.attr("src", $img.attr(DomImageReloadHelper.urlTag));
			$img.removeClass(DomImageReloadHelper.backGroundClass);
		});
		return $content.html();
	};
})();
