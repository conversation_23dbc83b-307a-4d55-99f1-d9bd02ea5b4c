﻿(function()
{
	createNamespace('TF.Control.ResourceScheduler').ResourceSubstitutionViewModel = ResourceSubstitutionViewModel;

	function ResourceSubstitutionViewModel(fromResourceId, allResourceEntities, resourceType, allEventSources, currentDate)
	{
		var fromResourceEntity, displayName, toResources = [], itemExtend,
			idFieldName = TF.Control.ResourceScheduler.Utility.getResourceIdColumnName(resourceType),
			nameFieldName = TF.Control.ResourceScheduler.Utility.getResourceNameColumnName(resourceType);

		this.allEventSources = allEventSources;
		this.resourceType = resourceType;
		this.resourceGroup = {};
		this.fromResourceId = fromResourceId;
		this.currentDate = currentDate;

		this.obResourceType = ko.observable(resourceType);

		$.each(allResourceEntities, function(_, item)
		{
			displayName = resourceType === "vehicles" ? String.format("{0}, {1}, {2}", item[nameFieldName], item.Capacity, item.WC_Capacity) : item[nameFieldName];

			itemExtend = $.extend(item, { DisplayName: displayName });

			if (item[idFieldName] === fromResourceId)
			{
				fromResourceEntity = itemExtend;
				return true;
			}

			toResources.push(itemExtend);
		});

		this.editTripModificationLevelData = new TF.Control.ResourceScheduler.EditTripOnCalendarLevelViewModel(
			{
				mode: "substitude",
				title: "Substitude Resource",
				description: "Specify date range you would like to substitude the resource:",
				currentDate: this.currentDate,
				disableEditingAssignment: true
			});
		this.editTripModificationLevelData.editLevelChanged.subscribe(function(e, value)
		{
			this.onEditLevelChanged(value);
		}.bind(this));

		this.editTripModificationLevelData.dateRangeChanged.subscribe(function()
		{
			this.onDateRangeChanged();
		}.bind(this));

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.addEditRestrictionData = new TF.Control.ResourceScheduler.AddEditRestrictionViewModel(fromResourceId, resourceType, currentDate, null, null, this.pageLevelViewModel);

		this.obFromResource = ko.observable(fromResourceEntity.DisplayName);
		this.obToResources = ko.observable([]);
		this.generateConflictSplittedToResources(toResources).then(function(res) { this.obToResources(res) }.bind(this));
		this.obSelectedToResource = ko.observable(null);
		this.obSelectedToResourceText = ko.computed(function()
		{
			return this.obSelectedToResource() ? this.obSelectedToResource().DisplayName : "";
		}.bind(this));

		this.obAddRestrictionCheckLabel = ko.observable("Add restriction for " + fromResourceEntity.DisplayName);
		this.obAddRestrictionChecked = ko.observable(false);
	}

	ResourceSubstitutionViewModel.prototype.onDateRangeChanged = function()
	{
		this.addEditRestrictionData.obStartDate(this.editTripModificationLevelData.obFromDate());
		this.addEditRestrictionData.obEndDate(this.editTripModificationLevelData.obToDate());
	};

	ResourceSubstitutionViewModel.prototype.onEditLevelChanged = function(value)
	{
		switch (value)
		{
			case TF.Control.ResourceScheduler.EditTripOnCalendarLevelViewModel.LEVEL_OPTION_ENUM.ALL_FUTURE:
				this.addEditRestrictionData.obDateType("allfuturedays");
				break;
			case TF.Control.ResourceScheduler.EditTripOnCalendarLevelViewModel.LEVEL_OPTION_ENUM.DATE_RANGE:
				this.addEditRestrictionData.obDateType("daterange");
				break;
		}
	};

	ResourceSubstitutionViewModel.prototype.generateConflictSplittedToResources = function(toResources)
	{
		var resourceId, resourceGroup = this.resourceGroup, fromResourceId = this.fromResourceId,
			resourceIdKey = TF.Control.ResourceScheduler.Utility.getResourceIdColumnName(this.resourceType);
		$.each(this.allEventSources, function(_, eventItem)
		{
			resourceId = eventItem.Type === "restriction" ? eventItem.ResourceId : eventItem[resourceIdKey];
			if (!resourceGroup[resourceId])
			{
				resourceGroup[resourceId] = [];
			}

			resourceGroup[resourceId].push(eventItem);
		});
		var daysOptionsKey = window.UsingNET8API ? "daysOptions[]" : "daysOptions";
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'conflictresschls'), {
			paramData: {
				databaseId: tf.datasourceManager.databaseId,
				resourceType: this.resourceType,
				resourceId: fromResourceId,
				startDate: this.editTripModificationLevelData.obFromDate() || this.editTripModificationLevelData.currentDate.format("YYYY-MM-DDTHH:mm:ss+00:00"),
				endDate: this.editTripModificationLevelData.obToDate(),
				[daysOptionsKey]: this.editTripModificationLevelData.getApiDayOptions()
			}
		}).then(function(res)
		{
			var conflicts = [], conflictToResources = [], freeConflictResources = [], allToResources = [];
			res.Items.forEach(function(item)
			{
				conflicts.push(item[resourceIdKey]);
			});
			$.each(toResources, function(_, resourceItem)
			{
				resourceId = resourceItem[resourceIdKey];
				if (conflicts.indexOf(resourceId) >= 0)
				{
					conflictToResources.push(resourceItem);
				} else
				{
					freeConflictResources.push(resourceItem);
				}
			});
			if (freeConflictResources.length > 0)
			{
				allToResources.push({ 'DisplayName': "[disable]-------No Conflicts-------" });
				Array.prototype.push.apply(allToResources, freeConflictResources);
			}

			if (conflictToResources.length > 0)
			{
				allToResources.push({ 'DisplayName': "[disable]-------Conflicts-------" });
				Array.prototype.push.apply(allToResources, conflictToResources);
			}

			return allToResources;
		})
	};

	ResourceSubstitutionViewModel.prototype.addRestriction = function()
	{
		return this.addEditRestrictionData.apply();
	};

	ResourceSubstitutionViewModel.prototype.applySubstitution = function()
	{
		var fromResourceTrips = this.resourceGroup[this.fromResourceId];

		if (!fromResourceTrips || fromResourceTrips.length === 0) return Promise.resolve(true);

		return this.editTripModificationLevelData.apply();
	};

	ResourceSubstitutionViewModel.prototype.validate = function()
	{
		return this.obSelectedToResource() != null;
	};

	ResourceSubstitutionViewModel.prototype.apply = function()
	{
		if (!this.validate())
		{
			this.pageLevelViewModel.popupErrorMessage("To resource is required.");
			return Promise.resolve(false)
		};

		var promiseAll = [this.applySubstitution()];

		if (this.obAddRestrictionChecked())
		{
			promiseAll.push(this.addRestriction(0));
		}

		return Promise.all(promiseAll)
			.then(results =>
			{
				if (this.obAddRestrictionChecked() && !results[1])
				{
					return null;
				}

				return [results[0], this.obSelectedToResource()];
			});
	};

	ResourceSubstitutionViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};
})();