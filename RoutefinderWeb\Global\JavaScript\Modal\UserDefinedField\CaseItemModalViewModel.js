(function()
{
	createNamespace("TF.Modal.UserDefinedField").CaseItemModalViewModel = CaseItemModalViewModel;

	function CaseItemModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/UserDefinedField/CaseItem');
		this.sizeCss = "modal-dialog-lg";
		this.buttonTemplate('modal/positivenegativeextend');
		this.obPositiveButtonLabel("Save");
		this.obSaveAndNewVisible(!options.isDefault && !options.isEdit);
		this.parentViewModel = options.parentViewModel;
		this.viewModel = new TF.UserDefinedField.CaseItemViewModel(options);
		this.data(this.viewModel);
		this.title(options.isDefault ? "Default Value" : "Case and Value");
	}

	CaseItemModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	CaseItemModalViewModel.prototype.constructor = CaseItemModalViewModel;

	CaseItemModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.save().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	CaseItemModalViewModel.prototype.negativeClick = function()
	{
		this.viewModel.cancel().then((result) =>
		{
			if (result)
			{
				this.positiveClick();
			}
			else
			{
				this.negativeClose(false);
			}
		});
	};

	CaseItemModalViewModel.prototype.saveAndNewClick = function()
	{
		this.viewModel.save().then(result =>
		{
			if (result)
			{
				this.viewModel.obSelectedFieldText(null)
				this.viewModel.obSelectedField(null);
				this.viewModel.dropdownAvailableTypes.value('');
				this.viewModel.obCase('');
				this.viewModel.obValue('');
				const list = Array.from(this.parentViewModel.kendoGrid.dataSource.data());
				this.parentViewModel.kendoGrid.dataSource.data(list.concat(result));
				this.parentViewModel.createKendoDropTargetEvent();
				if ($("input[name=Select]") && $("input[name=Select]").length > 0)
				{
					$("input[name=Select]").focus();
				}
			}
		});
	};

	CaseItemModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};
})();