(function()
{
	createNamespace('TF.Modal').PDE1049ReportSettingModalViewModel = PDE1049ReportSettingModalViewModel;

	function PDE1049ReportSettingModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.modalRootContainerCss = "pde1049-report-dialog";
		this.contentTemplate('modal/StateReport/PDE1049ReportSetting');
		this.buttonTemplate('modal/positivenegative');
		this.title("PA eTran Module");
		this.data(new TF.Control.PDE1049ReportSettingViewModel());
		this.obPositiveButtonLabel('Next');
	}

	PDE1049ReportSettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	PDE1049ReportSettingModalViewModel.prototype.constructor = PDE1049ReportSettingModalViewModel;

	PDE1049ReportSettingModalViewModel.prototype.positiveClick = function()
	{
		const vm = this.data();
		vm.apply().then((settingResponse) =>
		{
			if (settingResponse)
			{
				tf.modalManager.showModal(new TF.Modal.PDE1049DataValidationModalViewModel(settingResponse))
					.then(validationResponse =>
					{
						if (validationResponse === true)
						{
							this.positiveClose();
						}
						// Go back from data validation modal.
						else if (typeof validationResponse === "object")
						{
							vm.obSchoolYear(validationResponse.SchoolYear);
							vm.obAdminUnitNumber(validationResponse.AdminUnitNumber);
						}
					});
			}
		});
	}
})()