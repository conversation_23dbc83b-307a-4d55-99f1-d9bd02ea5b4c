.mapviewer-palette {
	background-color: #fff;

	.mapviewer-menu {
		.menu {
			&>ul {
				padding: 0 0 10px 0 !important;

				li.menu-divider {
					margin-bottom: 10px;
					margin-top: 10px;
				}
			}

			.workspace-item.menu-item.highlight .text {
				background-color: #E7F3FC;
			}

			.workspace-item.menu-item.workspace-item-checked {
				.menuIcon {
					background-image: url('../../global/img/grid/green_check.png');
				}
			}
		}
	}

	.mapviewer-workspaces {
		&.content-container {
			overflow: visible;
		}

		.workspace {
			overflow: inherit;
		}

		.workspace-content.collapsed {
			max-height: 0 !important;
			overflow: hidden;
		}

		.workspace-content {
			&:hover {
				overflow: visible;

				&.collapsed {
					overflow: hidden;
				}
			}

			overflow: hidden;

			transition: max-height 0.4s ease;

			.workspace-menu,
			.sketch-layer-toolbar {
				height: 32px;

				.with-sub-menu {
					&>ul {
						padding: 0 0 10px 0 !important;

						li.menu-divider {
							margin-bottom: 10px;
							margin-top: 10px;

							.rule {
								background-color: #9b9b9b;
								height: 1px;
								width: 82% !important;
								margin-left: 40px;
								position: relative;
								top: 1px;
							}
						}
					}

					.sub-menu.last {
						border-top: 1px solid #c5c5c5;
					}

					li.nextLevelMenuItem {
						>.text {
							height: 30px;

							>span.text-content {
								text-overflow: ellipsis;
								overflow: hidden;
								width: 180px;
								white-space: nowrap;
								float: left;
								height: 30px;
								line-height: 30px;
								text-align: left;
							}
						}
					}
				}

				.parcelpoint-tool {
					box-sizing: unset !important;
				}
			}

			.collapsed {
				max-height: 0;
			}
		}

		.pannel-item-content {

			.layer {
				float: left;
				width: 100%;
				background-color: #fff;
				position: relative;

				&:hover .close-button {
					display: block;
				}

				.close-button {
					display: none;
					position: absolute;
					top: 0;
					left: -20px;
					height: 45px;
					width: 20px;
					cursor: pointer;
					z-index: 150;
					background-color: #4B4B4B;
					background-image: url('../Img/Routing Map/clear_white.png');
					background-size: 16px;
					background-position: 2px 13px;
					background-repeat: no-repeat;
				}
			}

			.icon {
				&.collapse-all {
					background-image: url('../img/Routing Map/menuicon/collapse all.svg');
				}
			}
		}
	}

	.symbol {
		margin-top: 15px;
		height: 30px;

		&>label {
			float: left;
		}

		.dataLayerSymbol {
			float: left;
			margin-left: 10px;
			cursor: pointer;
		}
	}
}

.routingmap_panel .workspace .workspace-header,
.parcelpalette .pannel-item-content .content-wrapper .content-container.workspace-layer-container,
.parcelpalette .pannel-item-content .content-wrapper .sketch-layer-items .sketch-container {
	.eye-icon {
		width: 18px;
		height: 18px;
		margin: 10px 8px 10px 0px;
		cursor: pointer;
	}
}

.routingmap_panel .workspace .workspace-header {
	.eye-icon {
		margin: 8px 8px 6px 8px;
	}
}

.routingmap_panel .workspace .workspace-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 33px;

	.buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		width: auto !important;
		padding: 0;
	}

	.title {
		max-width: calc(100% - 35px);
		width: auto;
		flex: 1;
	}
}

.parcelpoint-tool {
	.icon.add-layer {
		background-image: url('../img/Routing Map/menuicon/add-layer-icon.svg');
		cursor: pointer;
		background-size: 70%;
	}

	.active {
		.icon.add-layer {
			background-image: url('../img/Routing Map/menuicon/add-layer-icon-active.svg');
		}
	}
}

.ui-sortable-helper.workspace .mapviewer-layer-item,
.ui-sortable-helper.mapviewer-layer-item {
	float: left;
	width: 100%;
	font-weight: bold;
	background-color: #fff;
	display: flex;
	justify-content: space-between;

	.content-container {
		display: flex;
		align-items: center;

		&.sketch-container {
			height: 45px;
			width: calc(100% - 15px);
			margin-left: 15px;
		}
	}
}

.sketch .sketch-wrapper .sketch-container {
	width: calc(100% - 45px) !important;
	margin-left: 45px !important;

	.sketch-name {
		font-weight: bold;
	}

	.sketch-comment {
		&::before {
			content: '-';
			padding-right: 5px;
		}

		padding-left: 5px;
		font-weight: normal;
	}
}

.ui-sortable-helper .sketch {
	background-color: #fff;

	.content-container {
		display: flex;
		align-items: center;

		&.sketch-container {
			height: 45px;
			width: calc(100% - 15px);
			margin-left: 15px;
		}

		.content {
			font-weight: bold;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
			color: #333333;
			font-size: 14px;
		}
	}
}

.ui-sortable-helper.mapviewer-layer-item,
.ui-sortable-helper.sketch {
	.content-wrapper {
		border: 1px solid #DADAD1;
	}
}

.ui-sortable-helper.workspace {
	overflow: inherit;

	.bottom-info-container {
		background-color: #F2F2F2;
		padding: 5px 1px;
		position: relative;
		border-bottom: 2px solid #dedede;
		box-sizing: content-box;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		font-size: 14px;
	}

	.sketch-layer-footer {
		background-color: #F2F2F2;
		padding: 5px 1px;
		position: relative;
		box-sizing: content-box;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		font-size: 14px;
	}

	.workspace-content {
		overflow: hidden;
		transition: none !important;
		border: 1px solid #DADAD1;
		border-top: 0;

		&.collapsed {
			max-height: 0 !important;
		}
	}

	.content-wrapper {
		border-bottom: 1px solid #DADAD1;
	}

	.sketch-layer-container,
	.layer-setting {
		transition: none !important;
	}

	.item-header .save-icon {
		background: url('../img/Routing Map/menuicon/save1.png') center center no-repeat;
		float: right;
		width: 18px;
		height: 18px;
		background-size: contain;
		margin: 9px 8px 5px 8px;
		position: relative;
		opacity: 0.8;
	}

	.item-header .badge {
		position: absolute;
		top: -8px;
		right: -7px;
		background-color: #f33541;
		padding: 2px 5px;
		font-size: 11px;
	}
}

.ui-sortable-helper.workspace .workspace-layer-container,
.ui-sortable-helper.mapviewer-layer-item .workspace-layer-container,
.parcelpalette .pannel-item-content .content-wrapper .content-container.workspace-layer-container {
	width: 100%;
	margin-left: 0;
	justify-content: space-between;

	.icon {
		margin-right: 5px;
	}

	.layer-content {
		padding-left: 16px;
		height: 100%;
		cursor: pointer;
		max-width: 95%;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: pre;
		line-height: 45px;

		&.readonly {
			cursor: default;
		}
	}
}

.routingmap_panel .list-container .mapviewer-palette {
	.show-eye {
		background: url('../img/Icons/eye.svg') center center no-repeat;
		filter: grayscale(1) brightness(0.3);
	}

	.hide-eye {
		background: url('../img/Icons/eye-slash.svg') center center no-repeat !important;
		filter: grayscale(1) brightness(0.3);
	}
}

.ui-sortable-helper.workspace .layer-wrapper,
.ui-sortable-helper.mapviewer-layer-item .layer-wrapper,
.parcelpalette .pannel-item-content.workspace-layers .content-wrapper.layer-wrapper {

	.sketch-layer-container,
	.layer-setting {
		border-top: 1px solid #DADAD1;
		overflow: hidden;
		max-height: 350px;
		transition: all 0.4s ease;
		transition-property: max-height, padding-top;
		padding-left: 30px;

		&.collapsed {
			max-height: 0;
			padding-top: 0;
		}

		.checkbox input[type="checkbox"] {
			margin-top: 4px;
			cursor: pointer;
		}

		.fresh-group-input {
			margin-left: 0;

			.k-dropdown {
				margin-left: 20px;

				.k-dropdown-wrap {
					border: none;

					&::after {
						content: '';
						position: absolute;
						top: 0;
						right: 0;
						bottom: 0;
						left: 0;
						border: 1px solid #ccc;
						pointer-events: none;
					}

					background-color: #eee;
					border-color: #ccc;

					.k-input {
						background-color: #fff;
						height: 22px;
						cursor: pointer;
					}

					.k-select {
						height: 22px;
						min-height: 22px;
						background-color: #eee;
						line-height: 20px;
						border-left: solid 1px #BFBFBF;
						cursor: pointer;

						.k-icon {
							display: inline-block;
							width: 0;
							height: 0;
							vertical-align: middle;
							border-top: 4px solid;
							border-right: 4px solid transparent;
							border-left: 4px solid transparent;
						}

						.k-icon:before {
							content: '';
						}
					}
				}
			}
		}

		.fresh-group-input-checkbox {
			margin-top: 0;
			cursor: pointer;
		}

		.label-input {
			background-color: #fff;
		}

		.transparency-range-group,
		.visible-range-group {
			.slider-wrap.slider-transparency-range {
				width: 100%;

				.slider.slider-horizontal {
					width: calc(100% - 20px);
					margin-left: 10px;
				}
			}

			.slider-wrap.slider-visible-range {
				width: calc(100% - 20px);
				margin-left: 10px;
			}
		}

		.setting-disabled {
			cursor: not-allowed !important;

			label,
			input {
				cursor: not-allowed !important;
			}

			button {
				border: 1px solid #555;
			}
		}

		.sketch-layer-footer {
			background-color: #F2F2F2;
			padding: 5px 1px;
			position: relative;
			box-sizing: content-box;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			font-size: 14px;
		}
	}

	.sketch-layer-container {
		padding-left: 0;

		&.collapsed {
			max-height: 0 !important;
			overflow: hidden !important;
		}

		.sketch {
			float: left;
			width: 100%;
			background-color: #fff;
			position: relative;

			&.highlight {
				background-color: #FFFFEC;
			}

			&:hover .close-button {
				display: block;
			}

			.close-button {
				display: none;
				position: absolute;
				top: 0;
				left: -20px;
				height: 45px;
				width: 20px;
				cursor: pointer;
				z-index: 150;
				background-color: #4B4B4B;
				background-image: url('../Img/Routing Map/clear_white.png');
				background-size: 16px;
				background-position: 2px 13px;
				background-repeat: no-repeat;
			}

			.sketch-wrapper .buttons .icon {
				margin-right: 5px;
			}
		}
	}
}

.list-container>.list>div>.mapviewer-layer-item .sketch-layer-container {
	transition: none;
}

.parcelpalette .pannel-item-content.workspace-layers .content-wrapper.layer-wrapper.expanded {
	.workspace-layer-container {
		background-color: #FFFFEC;
	}
}

.data-layer-form {
	.specific-record {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: normal;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		word-wrap: break-word;
		word-break: break-word;
	}
}

.fresh-group .fresh-group-input input.refresh-input-style {
	-moz-appearance: textfield;
	margin: 0px 3px;
	line-height: 16px;
}

.refresh-input-style::-webkit-outer-spin-button,
.refresh-input-style::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

.display-container.symbol-disable {
	cursor: default;
}

.mapviewer-palette .item-content,
.condition-preview .grid-stack-item-content .mapviewer-palette .item-content,
.grid-stack .grid-stack-item-content .mapviewer-palette .item-content {
	font-size: 14px;
}

.workspace-footer-records {
	text-align: right;
	padding-top: 5px;
	padding-right: 35px;
}

.tfmodal .modal-body {
	.add-from-file>.col-xs-24 {
		padding: 5px 15px;

		.field .file-upload {
			.select-file-button {
				padding: 1px 6px;
				font-family: "Arial";
				height: 22px;
				font-weight: 400;
				line-height: 11px;
				color: #000000;
			}

			.file-name {
				width: 200px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: inline-block;
				vertical-align: middle;
				font-weight: bold;
			}

			input[type='file'] {
				display: none
			}
		}
	}

	.add-from-web .row {
		padding: 5px 0;
	}
}

#slider-style {
	width: 100%;

	.slider-track {
		height: 2px;
		margin-top: 0px;

		.slider-selection {
			background: red;
		}

		.slider-handle {
			&.min-slider-handle {
				&.round {
					background: whitesmoke;
					margin-top: -9px;
					box-shadow: rgb(102, 102, 102) 0px 0px 12px;
				}
			}
		}
	}

	.tooltip {
		display: none;
	}
}

.sketchEditModal,
.fileLayer-settings-modal {
	.text {
		visibility: hidden;
		height: 0;
	}

	.sketch-edit-content {
		padding: 0 5px;

		&.showSwitchModal {
			margin-top: 15px;
		}

		.sketch-line-style {
			.polyline-style-label {
				float: left;
			}

			.polyline-style {
				margin-left: 100px;
			}
		}

		.sketch-edit-item-content {
			.text-setting {
				label {
					margin: 2px 0 0;
				}

				.text-type-radio-group {
					label {
						vertical-align: middle;

						input {
							vertical-align: top;
						}
					}
				}

				.startindex {
					margin-top: 2px;

					.start-index-display {
						position: absolute;
						left: 15px;
						top: 0;
						// According to the numericTextBox
						width: calc(100% - 26px - 2rem);
						z-index: 5;
						padding: 0 0 0 5px;
					}
				}
			}

			.color {
				margin-top: 10px;
			}

			.symbol-size {

				.size-value {
					float: right;
					font-size: 12pt;
					color: gray;

					&.border {
						font-weight: normal;
					}
				}

				.size-slider {
					padding-top: 10px;

					#symbol-size-slider {
						#slider-style
					}

					label {
						padding-top: 5px;
						font-size: 10pt;
						color: gray;
					}
				}
			}

			.border-thumbnail {
				.checkbox {
					margin-bottom: 18px;

					input {
						cursor: pointer;
					}
				}

				.border-slider {
					padding-top: 10px;

					#border-size-slider {
						#slider-style;
					}

					label {
						padding-top: 5px;
						font-size: 10pt;
						color: gray;
						font-weight: normal;
					}
				}

				.sequential-label-border-content.disabled {
					.color {
						pointer-events: none;
					}
				}
			}

			.symbol-thumbnail-container {
				padding-right: 0;
				padding-left: 0;
				display: flex;
				margin: 0 auto;
				justify-content: center;
				align-items: center;
				resize: both;
				width: 186px;
				height: 200px;
				background-image: url(../../Global/img/DisplayThumbnail.png);
				background-size: cover;
			}
		}
	}

	.dataentry-paginator {
		top: 10px;
		z-index: 9;
	}

	.textsketch-text-editor {
		height: 60px;

		table.k-editor,
		.k-editable-area {
			height: 60px;
		}

		.k-toolbar {
			display: none;
		}
	}

	.textsketch-text-editor-wrapper,
	.sketch-comments-editor-wrapper {
		table.k-editor {
			height: auto;
		}

		.k-editor-toolbar {
			padding: 2px 5px;

			&::before {
				height: auto;
			}

			.k-combobox {
				input {
					height: 100%;
				}
			}

			.k-tool-group {
				max-width: 220px !important;
			}

			.k-tool-group>.k-tool {
				width: 27px;
				height: 27px;
			}

			.k-colorpicker {

				>.k-select {
					opacity: unset !important;
				}
			}
		}
	}

	.textsketch-text-editor-wrapper {
		.k-editable-area {
			height: 0;
		}
	}

	.fillColor {
		margin-bottom: 0px;

		.color {
			margin-top: 10px;
		}
	}

	.point-symbol {
		display: inline-flex;
		margin-top: 10px;
		margin-bottom: 10px;

		.display-container {
			margin-left: 10px;
		}
	}

	.add-image {
		.add-image-container {
			border: 2px dashed #c5c5c5;
			background-color: #f2f2f2;
			font-weight: bold;
			padding: 15px 10px;
			margin-top: 10px;
			height: 54px;

			&.file-uploaded {
				background-color: #fff;

				label {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			&.drag-over {
				display: flex;
				justify-content: center;
				font-size: 18px;
				line-height: 20px;
				font-weight: bold;
			}

			label {
				color: blue;
				cursor: pointer;

				&:hover {
					text-decoration: underline;
				}
			}

			.uploaded-file-container {
				display: flex;
				flex-direction: row;
				justify-content: space-between;

				.clear-btn {
					font-size: 24px;
					width: 20px;
					line-height: 20px;
					cursor: pointer;
				}
			}

			#upload-image {
				opacity: 0;
				position: absolute;
				z-index: -1;
			}
		}
	}

	.transparency-range-group,
	.visible-range-group {
		.slider-wrap.slider-transparency-range {
			width: 100%;

			.slider.slider-horizontal {
				width: calc(100% - 20px);
				margin-left: 10px;
			}
		}

		.slider-wrap.slider-visible-range {
			width: calc(100% - 20px);
			margin-left: 10px;
		}
	}

	.sketch-transparency {
		min-height: 92px;
	}

	.sketch-border {
		min-height: 58px;
	}

	.polygonBorder {
		display: flex;
		margin-top: 15px;

		.borderThickness {
			width: 60px;

			.k-numeric-wrap {
				display: inline-flex;
				height: 22px;
				width: 50px;

				.k-select {
					width: 20px;
					display: block;

					.k-link {
						height: 10px;
						line-height: 10px;
					}
				}

				.input {
					height: 22px;
				}
			}
		}

		.k-numerictextbox {
			width: 50px;
			margin-right: 5px;
		}

		.input-group.colorbox {
			width: 30px !important;
			padding-left: 10px;
			padding-right: 10px;
		}
	}

}

.dock-parent-container.edit-map-viewer-sketch {
	.parcel-edit-overlay {
		z-index: 18999;
	}

	.parcel-edit-modal-container {
		z-index: 19999;
	}
}

.sketch-settings-modal {

	input[type=checkbox] {
		outline: none;
	}

	.checkbox {

		label,
		input,
		span {
			cursor: pointer;
		}
	}
}

.data-layer-popup-header {
	display: flex;
	justify-content: space-between;

	.detail-left,
	.change-layout {
		width: 50% !important;
	}

	.detail-left {
		margin-right: 10px;
	}

	.change-layout {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.layout-text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		text-align: right;
		padding-right: 10px;
	}

	.change-layout-btn {
		background: url('../img/Routing Map/layout_red.svg') no-repeat center center;
		background-size: 30px;
		border: none;
		outline: none;
		width: 24px;
		height: 24px;
	}

	.change-layout-content {
		position: relative;
	}

	.popup-layout-list {
		display: none;
		width: 260px;
		max-height: 350px;
		overflow-y: auto;
		position: absolute;
		top: 23px;
		left: 3px;

		ul {
			list-style: none;
			margin: 0;
			padding-left: 18px;
			position: relative;
			background-color: #fff;

			&::after {
				content: "";
				position: absolute;
				width: 18px;
				left: 0;
				top: 0;
				bottom: 0;
				background-color: #D0503C;
			}

			li {
				background-color: #fff;
				color: #000000;
				list-style: none;
				padding: 5px 10px;
				max-width: 100%;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
				cursor: pointer;

				&:hover {
					background-color: #E7F3FC;
				}

				&.selected {
					background-color: #FFFFEC;
				}
			}
		}

	}
}

.map-filter {
	.checkbox {
		label {
			input {
				margin-top: 6px !important;
			}

			span {
				font-weight: bold;
			}
		}
	}

	.select-value {
		width: 100%;
		border: 1px solid #ccc;
		outline: none;
		height: 22px;
		padding: 0 8px;
	}

	.input-group-color {
		display: flex;

		&.disabled {
			pointer-events: none;

			.color-btn {
				.k-colorpicker .k-selected-color {
					cursor: default;
				}
			}
		}

		.color-btn {
			width: 30px;
			cursor: pointer;
			margin-right: 10px;

			.k-colorpicker .k-selected-color {
				cursor: pointer;
			}
		}
	}
}

.k-popup-dropdowntree {
	.k-item .map-filter-item {
		max-width: calc(100% - 50px);
		text-overflow: ellipsis;
		overflow: hidden;
	}
}


.map-page.map-panel-phone-viewfinder {
	overflow: hidden;

	.slide-container,
	.left-resize-handle,
	.right-resize-handle,
	.bottom-resize-handle,
	.resize-handle {
		display: none;
	}

	.top-resize-phone {
		transform: rotate(-90deg);
		width: 30px;
		height: 51px;
		position: absolute;
		top: -40px;
		left: 10px;
		background: url('../img/Routing Map/left-arrow-white.png') #484848 no-repeat center center;
	}

	.routingmap_panel .map-expand-button.is-mobile-device {
		right: 20px;
		top: -55px;
	}

	.routingmap_panel.panel-collapse-phone {
		.top-resize-phone {
			background-image: url('../img/Routing Map/right-arrow-white.png');
		}
	}

	.parcelpalette .pannel-item-content .content-wrapper .content-container {
		.buttons {
			height: 100%;
		}

		.icon.zoom-map-to-layers,
		.eye-icon {
			width: 50px;
			position: relative;
			background-size: 20px !important;
			display: flex;
			align-items: center;
			justify-content: center;

			&::after {
				content: "";
				position: absolute;
				height: 35px;
				top: 5px;
				left: 0;
				width: 1px;
				background-color: #ccc;
				opacity: 0.2;
			}
		}

		.eye-icon {
			margin: 0;
			width: 59px;
			height: 100%;
		}

		.icon.zoom-map-to-layers {
			display: block;
			margin-right: 0;
			width: 55px;
			height: 100%;

			&::after {
				opacity: 1;
			}
		}
	}

	.routingmap_panel {
		.list-container {
			.bottom-info-container {
				color: #333333;
			}

			.mapviewer-layer-item {
				position: relative;

				.content-container {
					width: calc(100% - 30px);

					.content {
						flex: 1;
						width: auto;
						font-weight: bold;
						text-overflow: ellipsis;
						white-space: nowrap;
						overflow: hidden;
						color: #333333;
						font-size: 14px;
					}
				}

				.content-wrapper {
					display: flex;
				}

				.sketch-layer-container.collapsed {
					display: none;
				}
			}
		}

		.mapviewer-layer-item,
		.workspace-header {
			.sortable-mobile {
				width: 30px;
				height: 45px;
				background: url('../img/Routing Map/menuicon/mobile-sort.svg') center center no-repeat;
				background-size: 30px;
			}
		}

		&.panel-collapse-phone.expand-resize {
			.top-resize-phone.is-mobile-device {
				top: -56px;
			}
		}
	}

	&.map-viewer-show {
		min-width: 50% !important;

		.map-expand-button.is-mobile-device.active {

			display: block !important;
		}

		.map-expand-button.is-mobile-device.deactive {

			display: none !important;
		}

		.map-expand-button.is-mobile-device:not(.map-palette) {
			display: none;
		}
	}
}

.field-expanded .map-page.map-panel-phone-viewfinder,
.map-page.map-panel-phone-viewfinder.map-expanded {
	.grid-map-expand.map-palette {
		background-image: url('../img/map/restore.png');
	}
}

.mapviewer-palette .mapviewer-menu .viewfinder-mobile-menu {
	.fileMenu {
		&.full-width {
			width: 100% !important;
		}

		position: fixed !important;

		ul {
			padding-bottom: 0 !important;
		}

		.menu-item.phone-line {
			position: relative;

			&::after {
				content: "";
				position: absolute;
				border-bottom: 1px #4b4b4b solid;
				bottom: 0;
				left: 40px;
				right: 10px;
			}
		}

		li.menu-item.phone-line:first-child {
			padding-top: 0 !important;
			height: 30px !important;
		}

		li.menu-item.phone-line:last-child {
			&::after {
				border: none;
			}
		}

		.sub-context-menu {
			position: fixed;
			top: 110px;
			left: 10px;
			max-height: 130px;

			&.full-width {
				width: calc(100% - 10px) !important
			}
		}
	}
}

.geo-search-mobile {
	.map-page.map-panel-phone-viewfinder {
		.routingmap_panel {
			display: none;
		}

		.map-expand-button.is-mobile-device {
			display: block !important;
		}
	}
}