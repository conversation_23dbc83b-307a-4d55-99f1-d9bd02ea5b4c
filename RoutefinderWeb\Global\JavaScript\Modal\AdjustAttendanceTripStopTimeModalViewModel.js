﻿(function()
{
	createNamespace("TF.Modal").AdjustAttendanceTripStopTimeModalViewModel = AdjustAttendanceTripStopTimeModalViewModel;

	function AdjustAttendanceTripStopTimeModalViewModel(stopEntity)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Adjust ' + tf.applicationTerm.getApplicationTermSingularByName("Trip Stop") + ' Time');
		this.sizeCss = "modal-sm";
		this.contentTemplate('modal/AdjustAttendanceTripStopTimeControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Apply");
		this.model = new TF.Control.AdjustAttendanceTripStopTimeViewModel(stopEntity);
		this.data(this.model);
	};

	AdjustAttendanceTripStopTimeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AdjustAttendanceTripStopTimeModalViewModel.prototype.constructor = AdjustAttendanceTripStopTimeModalViewModel;

	AdjustAttendanceTripStopTimeModalViewModel.prototype.positiveClick = function()
	{
		this.model.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	AdjustAttendanceTripStopTimeModalViewModel.prototype.dispose = function()
	{
		this.model.dispose();
	};
})();

