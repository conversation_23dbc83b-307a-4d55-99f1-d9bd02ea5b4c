﻿(function()
{
	createNamespace("TF.Modal").MessageViewModalViewModel = MessageViewModalViewModel;

	function MessageViewModalViewModel(msgOption)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Transfinder Messages');
		this.sizeCss = "message-modal-dialog";
		this.obNegativeButtonLabel("Close");

		this.obActionButtonVisible = ko.observable(false);
		this.obActionButtonLabel = ko.observable("");
		this.obActionButtonUrl = ko.observable("");
		this.obActionBtnForeColor = ko.observable("#FFFFFF");
		this.obActionBtnBackColor = ko.observable("#0000FF");

		this.contentTemplate('modal/messageviewcontrol');
		this.buttonTemplate('modal/tfmessagebuttons');
		this.viewModel = new TF.Control.MessageViewViewModel({
			isStartup: msgOption.isStartup,
			messages: msgOption.messages,
			obActionButtonVisible: this.obActionButtonVisible,
			obActionButtonLabel: this.obActionButtonLabel,
			obActionButtonUrl: this.obActionButtonUrl,
			obActionBtnForeColor: this.obActionBtnForeColor,
			obActionBtnBackColor: this.obActionBtnBackColor,
		});
		this.data(this.viewModel);

		this.openActionUrl = this.openActionUrl.bind(this);
	}

	MessageViewModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	MessageViewModalViewModel.prototype.constructor = MessageViewModalViewModel;

	MessageViewModalViewModel.prototype.openActionUrl = function(vm, evt)
	{
		const self = this,
			btnVisible = self.obActionButtonVisible(),
			btnActionUrl = self.obActionButtonUrl();
		
		if (btnVisible && btnActionUrl)
		{
			window.open(btnActionUrl, "_blank");
		}
	};
})();

