﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.MailingCityDeletion = MailingCityDeletion;

	function MailingCityDeletion()
	{
		this.type = 'mailingcity';
		namespace.BaseDeletion.call(this, true);
	}

	MailingCityDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	MailingCityDeletion.prototype.constructor = MailingCityDeletion;

	MailingCityDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	};

	MailingCityDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();