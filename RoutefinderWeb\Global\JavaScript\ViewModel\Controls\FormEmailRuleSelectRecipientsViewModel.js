(function()
{
	createNamespace("TF.Control").FormEmailRuleSelectRecipientsViewModel = FormEmailRuleSelectRecipientsViewModel;

	function FormEmailRuleSelectRecipientsViewModel(selectedData, options, formConfig)
	{
		var self = this;
		const defaultOptions = {
			'description': 'Select how to send the email',
			'availableTitle': 'Available',
			'selectedTitle': 'Selected',
			"serverPaging": true,
			"updateColumnGrid": 'left',
			getUrl: function()
			{
				if (options && (options.type == "contact" || options.type == "staff"))
				{
					return tf.dataTypeHelper.getSearchUrl(tf.datasourceManager.databaseId, options.type);
				}
				return pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", "users");
			},
			defaultFilterData: {
				filterSet: {
					FilterItems: [{ FieldName: "Deactivated", Operator: "EqualTo", TypeHint: "String", Value: "false" }],
					FilterSets: [],
					LogicalOperator: 'and'
				}
			},
			filterSetField: "AccountEnabled",
		}

		self.options = $.extend({}, defaultOptions, options);
		self.formConfig = formConfig;
		self.modalViewModel = self.options.modalViewModel;

		let selectedUDFEmails = this.formConfig.udfEmails || [];
		let selectedQuestionEmails = this.formConfig.questionEmails || [];
		self.initEmailType(selectedUDFEmails, selectedQuestionEmails);
		self.initUDFEmails(selectedUDFEmails);
		self.initQuestionEmails(selectedQuestionEmails);

		self.selectedItems = selectedData;

		TF.Control.ListMoverSelectRecipientControlViewModel.call(self, selectedData, self.options);
	};

	FormEmailRuleSelectRecipientsViewModel.prototype = Object.create(TF.Control.ListMoverSelectRecipientControlViewModel.prototype);
	FormEmailRuleSelectRecipientsViewModel.prototype.constructor = FormEmailRuleSelectRecipientsViewModel;

	FormEmailRuleSelectRecipientsViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.$element = $(el);
		TF.Control.ListMoverSelectRecipientControlViewModel.prototype.init.call(this, viewModel, el);
	};

	FormEmailRuleSelectRecipientsViewModel.prototype.initEmailType = function(selectedUDFEmails, selectedQuestionEmails)
	{
		let self = this;
		self.obEmailType = ko.observable(0);

		self.obEmailType.subscribe((v) =>
		{
			if (!v)
			{
				// adjust the kendo grid ui
				setTimeout(() =>
				{
					let availableKendoGrid = self.$element.find(".availablecolumngrid-container").data("kendoGrid");
					availableKendoGrid.refresh();
					let selectedKendoGrid = self.$element.find(".selectedcolumngrid-container").data("kendoGrid");
					selectedKendoGrid.refresh();
				});
			}
		})

		let emailTypeFlag = self._getEmailType(selectedUDFEmails, selectedQuestionEmails);
		this.obEmailType(emailTypeFlag);
	}

	FormEmailRuleSelectRecipientsViewModel.prototype._getEmailType = function(selectedUDFEmails, selectedQuestionEmails)
	{
		return (!selectedUDFEmails?.length && !selectedQuestionEmails?.length) ? 0 : 1;
	}

	FormEmailRuleSelectRecipientsViewModel.prototype.initUDFEmails = function(selectedUDFEmails)
	{
		this.obUDFEmails = ko.observableArray();
		let dataType = this.formConfig.dataType;

		tf.UDFDefinition.RetrieveByType(dataType).then(() =>
		{
			let dataSourceIds = this.formConfig.dataSource.map((ds) => { return ds.value });
			let emailUDFs = tf.UDFDefinition.get(dataType).userDefinedFields.filter(item =>
			{
				return item.UDFType === 'email' && item.UDFDataSources.some(dataSource => dataSourceIds.includes(dataSource.DBID)) > 0;
			});
			let emailFields = emailUDFs.map(udf =>
			{
				let hasSelectedData = selectedUDFEmails.findIndex(data => data === udf.UDFGuid) > -1;
				udf.obFieldChecked = ko.observable(hasSelectedData);
				return udf;
			}).sort((a, b) => a.DisplayName.toLowerCase() > b.DisplayName.toLowerCase() ? 1 : -1);
			this.obUDFEmails(emailFields);
		});
	}

	FormEmailRuleSelectRecipientsViewModel.prototype.initQuestionEmails = function(selectedQuestionEmails)
	{
		let self = this;
		let rets = JSON.parse(JSON.stringify(self.formConfig.allCurrentFormEmailQuestions));
		rets = formatQuestionName(rets);
		rets = rets.sort((a, b) => a.LabelText.toLowerCase() > b.LabelText.toLowerCase() ? 1 : -1);
		self.obQuestionEmails = ko.observableArray(rets);

		function formatQuestionName(oldQuestions)
		{
			const nameCount = {};
			for (const item of oldQuestions)
			{
				const name = item.Name;
				nameCount[name] = (nameCount[name] || 0) + 1;
			}

			return oldQuestions.map(question =>
			{
				question.LabelText = nameCount[question.Name] > 1 ? question.FullName : question.Name;

				let hasSelectedData = selectedQuestionEmails.findIndex(data => data === question.Guid) > -1;
				question.obFieldChecked = ko.observable(hasSelectedData);

				return question;
			});
		}
	}

	FormEmailRuleSelectRecipientsViewModel.prototype.apply = function()
	{
		let self = this, emailType = self.obEmailType();
		let data = {};
		data.emailType = emailType;
		if (emailType)
		{
			data.emailUDFGuids = self._getSelectedUDFEmailGuids();
			data.emailQuestionGuids = self._getSelectedEmailQuestionGuids();

			if (!data.emailUDFGuids?.length && !data.emailQuestionGuids?.length)
			{
				return tf.promiseBootbox.alert({
					message: "Please select at least one User Defined Email Field OR Email Question.",
					title: "Warning"
				}).then(function()
				{
					return false;
				});
			}

			return Promise.resolve(data);
		}
		else
		{
			return TF.Control.ListMoverSelectRecipientControlViewModel.prototype.apply.call(this).then(function(selectedData)
			{
				return selectedData;
			});
		}
	};

	FormEmailRuleSelectRecipientsViewModel.prototype._getSelectedUDFEmailGuids = function()
	{
		let emailUDFGuids = this.obUDFEmails().filter(i => i.obFieldChecked()).map((udf) => { return udf.UDFGuid });
		return emailUDFGuids || [];
	}

	FormEmailRuleSelectRecipientsViewModel.prototype._getSelectedEmailQuestionGuids = function()
	{
		let emailQuestionGuids = this.obQuestionEmails().filter(i => i.obFieldChecked()).map((question) => { return question.Guid });
		return emailQuestionGuids || [];
	}

	FormEmailRuleSelectRecipientsViewModel.prototype.cancel = function()
	{
		return new Promise((resolve, reject) =>
		{
			if (this.isDataChanged())
			{
				return tf.promiseBootbox.yesNo("You have unsaved changes.  Are you sure you want to cancel?", "Confirmation Message").then(function(result)
				{
					if (result)
					{
						resolve(true);
					}
					else
					{
						reject();
					}
				});
			} else
			{
				resolve(true);
			}
		});
	};

	FormEmailRuleSelectRecipientsViewModel.prototype.isDataChanged = function()
	{
		let self = this, oldEmailType = self._getEmailType(self.formConfig?.udfEmails, self.formConfig?.questionEmails);
		if (self.obEmailType() !== oldEmailType)
		{
			return true;
		}

		if (self.obEmailType() === 0)
		{
			return !isArraySame(self.oldData, self.selectedData);
		}

		let emailUDFGuids = self.obUDFEmails().filter(i => i.obFieldChecked()).map((udf) => { return udf.UDFGuid });
		let emailQuestionGuids = self.obQuestionEmails().filter(i => i.obFieldChecked()).map((q) => { return q.Guid });

		return !isArraySame(self.formConfig.udfEmails, emailUDFGuids) ||
			!isArraySame(self.formConfig.questionEmails, emailQuestionGuids);
	}

	FormEmailRuleSelectRecipientsViewModel.prototype.dispose = function()
	{
		var self = this;
		TF.Control.ListMoverSelectRecipientControlViewModel.prototype.dispose.call(this);
	};

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var oldIds = oldData.map(function(item)
		{
			return item.Id;
		});
		var newIds = newData.map(function(item)
		{
			return item.Id;
		});
		var diffData1 = Enumerable.From(newIds).Where(function(x)
		{
			return !Array.contain(oldIds, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldIds).Where(function(x)
		{
			return !Array.contain(newIds, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();