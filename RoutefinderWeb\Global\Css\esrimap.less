﻿@import "z-index";
@import "mobile";

.esri-view {
	font-family: "SourceSansPro-Regular", Arial;
	font-size: 14px;
}

.esri-view .esri-view-surface:focus:after {
	outline: none;
	outline-offset: initial;
}

.esri-attribution__sources {
	text-align: left;
}

.esriPopup {
	font-family: <PERSON><PERSON>, Arial, sans-serif;
	font-size: 13px;
	font-weight: 300;

	.esriPopupWrapper {
		.contentPane {
			padding: 15px;

			.map-tooltip.BaseArcGisGridMap-map-tooltip {
				height: 220px;

				.callout-head {
					height: 20px;
				}

				.callout-content {
					overflow-y: auto;
					height: 200px;
				}

				.page-group-wrap {
					position: absolute;
					right: 30px;
					top: 11px;
				}

				.student-image {
					max-height: 100%;
					max-width: 100%;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translateY(-50%) translateX(-50%);
				}

				.ellipsis {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.drill-down-links {
					cursor: pointer;
				}

				.calloutcomments {
					width: 100%;
				}

				&.student-tooltip {
					.calloutcomments {
						width: 100%;
					}

					.calloutLoadTime {
						width: 100%;
						text-align: right;
						color: #333;
					}
				}

				.page-group {
					text-align: center;
					min-width: 30px;

					.iconbutton {
						width: 10px;
						display: inline-block;
						background-size: 100% 100%;
						height: 20px;
						margin-right: 3px;
					}

					.page-indicator {
						font-size: 9px;
						line-height: 0;
					}
				}

				.flex-style {
					display: flex;
					flex-direction: column;
					white-space: nowrap;

					.sub-tab {
						width: 100%;
						display: flex;
						flex-direction: row;
						flex-wrap: wrap;
						justify-content: space-between;

						div {
							min-width: 0px;
						}

						.gap {
							padding-right: 20px;
						}

						.ellipsis {
							display: block;
						}
					}

					.ellipsis {
						overflow: hidden;
						display: inline-block;
						-webkit-flex: 1;
						min-width: 0;
						text-overflow: ellipsis;
					}
				}
			}
		}
	}

	.actionsPane,
	.pointer.bottom,
	.contentPane,
	.esriPopupWrapper {
		background-color: white;
		box-shadow: none;
		-webkit-box-shadow: none;
		border-radius: 0;
		-webkit-border-radius: 0;
	}

	.pointer.bottom,
	.esriPopupWrapper {
		background-color: white;
		border: 1px solid rgb(204, 204, 204);
	}

	&.newest-style {
		font-family: "SourceSansPro-Regular";

		.base-box-shadow {
			box-shadow: 0 2px 16px rgba(0, 0, 0, 0.3);
		}

		.pointer-caret {
			border: 0;
			.base-box-shadow;
			height: 32px !important;
			width: 32px !important;
			background-color: white;
		}

		.display-center {
			display: flex;
			align-items: center;
			justify-content: center;
		}

		@skin-baseColor: #8F52A1;

		.esriPopupWrapper {
			border: 0;
			.base-box-shadow;

			.sizer {
				width: 592px !important;
			}

			.sizer {
				display: none;

				&.content {
					display: block;
				}
			}

			.sizer.content .contentPane {
				padding: 0;
				max-height: none !important;
				overflow: inherit;
			}

			.pointer {
				.pointer-caret;
				margin-left: -16px !important;

				&.bottom {
					bottom: -12px !important;
				}

				&.top {
					top: -12px !important;
					background-color: #F3EDF5 !important;
				}
			}
		}

		.outerPointer {
			.pointer-caret;
			margin-top: -16px !important;

			&.left {
				left: 12px;
			}

			&.right {
				right: 12px;
			}
		}
	}
}

.esriSimpleSlider.esriSimpleSliderVertical.esriSimpleSliderTL {
	border: none;
	box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px;
	border-radius: 0;

	&>div {
		width: 28px;
		height: 27px;
		font-size: 18px;
	}

	.esriSimpleSliderIncrementButton {
		border-bottom: 1px solid rgb(230, 230, 230);
	}
}

.esri-map-toolbar-top,
.esri-map-toolbar-bottom {
	position: absolute;
	left: 0;
	width: 100%;
	height: auto;
	box-sizing: border-box;
	padding: 15px 20px 15px 20px;

	&.active {
		pointer-events: auto;
		display: block;
	}

	.toolbar-btn {
		height: 31px;
		cursor: pointer;
		opacity: 1;
		float: left;
		text-align: center;
		padding: 8px;
		font-size: 12px;
		font-family: "SourceSansPro-Regular";

		&.white {
			background-color: white;
			color: black;
			box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px;

			&:hover:not(.inactive) {
				background-color: #ebebeb;
			}

			&.inactive {
				cursor: default;
				color: #999999;
			}
		}

		&.black {
			background-color: transparent;
			color: white;

			&.inactive {
				cursor: default;
				color: #999999;
			}
		}
	}
}

.esri-map-toolbar-top {
	top: 0;
	pointer-events: none;

	&>div {
		float: left;
		margin: 0 20px 0 0;
		background-color: #fff;
		pointer-events: auto;
	}

	.tool-button {
		&>div {
			background-repeat: no-repeat;
			background-position: center;
			width: 31px;
		}

		.draw-btn {
			background-image: url(../img/map/view/Geo-Search-b.png);

			&.applied {
				background-image: url(../img/map/view/Geo-Search-Applied-b.png);
			}
		}

		.thematic {
			background-image: url(../img/map/view/thematics.svg);

			&.applied {
				background-color: #4b4b4b;
				background-image: url(../img/map/view/thematics-rev.svg);
			}
		}

		.fullextent-btn {
			background-image: url(../img/map/view/CenterInMap.png);

			&.applied {
				background-image: url(../img/map/view/CenterInMapApplied.png);
			}
		}
	}

	.basemap-switch {
		.toolbar-btn.white.switch-btn.applied {
			color: black;
			font-weight: bold;
		}

		.toolbar-btn.white.switch-btn {
			color: rgb(86, 86, 86);
		}
	}
}

.esri-map-toolbar-bottom {
	height: 61px;
	bottom: 0;
	background-color: rgba(20, 20, 20, 0.7);
	display: none;

	&.active {
		display: block;
	}

	&>div {
		width: 70px;
	}

	.exit-btn {
		float: right;
	}
}

.map .esriMapTooltip {
	width: 120px;
}

// New Map Tools
.on-map-tool {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: @MAP_ONMAP_TOOL;
	pointer-events: none;

	&.geosearch {
		bottom: 61px;
	}

	.zoom-controls {
		position: absolute;
		right: 15px;
		bottom: 15px;
		width: 44px;
		pointer-events: auto;
		z-index: @MAP_ONMAP_TOOL + 10;

		&.left-align {
			left: 15px;
			right: auto;
		}

		.zoom-btn {
			box-sizing: content-box;
			height: 40px;
			width: 40px;
			margin-bottom: 15px;
			border: 2px solid #fff;
			border-radius: 22px;
			background-color: #fff;
			background-position: center center;
			background-repeat: no-repeat;
			box-shadow: 0 5px 2px -2px rgba(0, 0, 0, 0.15);
			cursor: pointer;

			&.zoom-in {
				background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAABlBMVEUAAAAAAAClZ7nPAAAAAXRSTlMAQObYZgAAABlJREFUeAFjoBZgZBwYQUYUgE+Q/u6kEgAALEwANQNmk1oAAAAASUVORK5CYII=);
			}

			&.zoom-out {
				margin-bottom: 0;
				background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAABlBMVEUAAAAAAAClZ7nPAAAAAXRSTlMAQObYZgAAABJJREFUeAFjGPKAEQlgFxzqAAAVVAAZNiceeAAAAABJRU5ErkJggg==);
			}
		}
	}
}

.off-map-tool {
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	z-index: 12032;
	overflow: hidden;
	pointer-events: none;

	&.hide {
		display: none;
	}

	&.active {
		pointer-events: auto;

		&.basemap,
		&.thematics {
			.map-tool-container .map-tool-label {
				display: none;
				opacity: 0;
			}

			.tool-icon {
				opacity: 0.5;

				&.active {
					opacity: 1;
				}
			}
		}

		.map-tool-background {
			pointer-events: auto;
			opacity: 0.75;
		}

		.map-tool-container {
			.generate-sequence(10);

			.generate-sequence(@n, @i: 1) when (@i =< @n) {
				.sequence-@{i} {
					transform: translate3d(0px, ((@i - 1) * 64 + 76px), 0px);
				}

				.generate-sequence(@n, (@i + 1));
			}

			.map-tool-label {
				display: block;
				opacity: 1;
			}
		}

		.map-tool-container.landscape {
			.generate-sequence(10);

			.generate-sequence(@n, @i: 1) when (@i =< @n) {
				.sequence-@{i} {
					transform: translate3d(((@i - 1) * -64 - 70px), 6px, 0px);
				}

				.generate-sequence(@n, (@i + 1));
			}

			.map-tool-label {
				display: none !important;
			}
		}

		.map-tool-container {
			&.down {
				.generate-sequence(10);

				.generate-sequence(@n, @i: 1) when (@i =< @n) {
					.sequence-@{i} {
						transform: translate3d(0px, ((@i - 1) * 64 + 76px), 0px);
					}

					.generate-sequence(@n, (@i + 1));
				}

				.map-tool-label {
					display: block;
					opacity: 1;
				}
			}

			&.up {
				.generate-sequence(10);

				.generate-sequence(@n, @i: 1) when (@i =< @n) {
					.sequence-@{i} {
						transform: translate3d(0px, ((@i - 1) * -64 - 56px), 0px);
					}

					.generate-sequence(@n, (@i + 1));
				}

				.map-tool-label {
					display: none !important;
				}
			}
		}

		.map-tool-container.landscape {
			&.left {
				.generate-sequence(10);

				.generate-sequence(@n, @i: 1) when (@i =< @n) {
					.sequence-@{i} {
						transform: translate3d(((@i - 1) * -64 - 70px), 6px, 0px);
					}

					.generate-sequence(@n, (@i + 1));
				}
			}

			&.right {
				.generate-sequence(10);

				.generate-sequence(@n, @i: 1) when (@i =< @n) {
					.sequence-@{i} {
						transform: translate3d(((@i - 1) * 64 + 70px), 6px, 0px);
					}

					.generate-sequence(@n, (@i + 1));
				}
			}

			.map-tool-label {
				display: none !important;
			}
		}
	}

	&.left-align {
		.map-tool-container {
			.map-tool-label {
				padding-right: 0;
				padding-left: 20px;

				label {
					text-align: left;
				}
			}
		}
	}

	.map-tool-background {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: black;
		opacity: 0;
		pointer-events: none;
		z-index: @MAP_MASK_BACKGROUND;
	}

	.map-tool-container {
		position: absolute;
		right: 65px;
		top: 15px;
		z-index: @MAP_TOOLKIT_CONTAINER;

		.tool-icon {
			position: absolute;
			right: 0;
			margin-left: 6px;
			margin-right: 6px;
			width: 40px;
			height: 40px;
			border-radius: 40px;
			border: 2px solid white;
			transform: translate3d(0px, 6px, 0px);
			box-sizing: content-box;

			&.disable {
				opacity: .5;
				cursor: initial;
			}

			&.force-disable:after {
				content: "";
				display: block;
				width: 36px;
				height: 36px;
				border-bottom: 2px solid #101010;
				transform: translateY(-13px) translateX(10px) rotate(27deg);
				position: absolute;
			}
		}

		.zoom {
			background-image: url("../img/Routing Map/menuicon/ZoomToBounds_Black.svg");
			background-size: contain;
		}

		.questionlayers {
			background-size: 54px;
			background-image: url("../img/map/view/questionlayers.svg");
		}

		.basemap {
			background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAABEklEQVQoFQXBMUiUAQAG0PefuCvddJtDBlrnKQ62hLS1mUho4iKok0MKbrrd0FC7ow4Rgi1CTVFOHXRLcCBBcYNLKHFwqIgnfr5XABjz2ojHCk1/ffALAHir61BTRHx3pg4AX5yZtOKfiHMTVl34DFDXU7YmIiJ65lREHWrujOBKREScGMSsqJYsKDw0aNwzC76BUx180rLKgYh5QElD3NkxhC0/aYtrZQBfRUTHA1O6JQX6vAB93psGDBjWhQMRUcO2iIiImnnNkhZgFEsAuHVpWYNHrkTs4kRERPz3TtcT2BTRs+63iLgRERsA+yKibd+iiqof4iMAvHEhDr301CtH2jaBAsCwGc+N6/fHsT0t4B4DN3sOpk3/YQAAAABJRU5ErkJggg==);
		}

		.compareMapCanvas {
			background-image: url("../img/map/view/CompareMapCanvas.png");
			background-size: 22px;
		}

		.thematics {
			background-image: url("../img/map/view/thematics.svg");

			&.disable {
				cursor: not-allowed;
				opacity: 0.5;
				pointer-events: none;
			}
		}

		.geofinder {
			background-image: url("../img/map/view/Geo-Finder.png");
		}

		.studentWalk {
			background-image: url("../img/map/view/students-walking.png");
		}

		.geosearch {
			background-image: url("../img/map/view/Geo-Search-b.png");
		}

		.layers {
			background-image: url("../img/map/view/layers.svg");
		}

		.clear-geosearch {
			background-image: url("../img/map/view/Clear-Geo-Search.png");
		}

		.measurement {
			background-image: url("../img/map/view/Measure Icon.svg");
		}

		.print {
			background-size: 24px;
			background-image: url("../../global/img/detail-screen/print.svg");
		}

		.playback {
			background-size: 34px;
			background-image: url("../../global/img/detail-screen/play.svg");
		}

		.palettes {
			background-size: 24px;
			background-image: url("../img/map/view/palettes-black.png");
		}

		.home {
			background-image: url("../img/map/view/home.svg");
			background-size: 24px;
		}

		.location-arrow {
			background-image: url("../img/map/view/location-arrow.svg");
			background-size: 24px;
		}

		.trash {
			background-image: url("../img/menu/Delete-Black.svg");
			background-size: 24px;
		}

		.location-marker {
			background-image: url("../img/map/view/location-marker.svg");
			background-size: 24px;
		}

		.manuallypin {
			background-image: url("../img/map/view/pin.png");
			background-position: center;
			background-size: 24px;

			&.on:after {
				content: "";
				display: block;
				width: 18px;
				height: 18px;
				position: absolute;
				top: 20px;
				left: 20px;
				background: url(../img/icons/checkmark.png) center no-repeat;
				background-size: 12px 10px;
			}
		}

		.googlestreet {
			background-size: 24px;
			background-image: url("../img/map/view/GoogleStreet.svg");
		}

		.map-tool-label {
			display: none;
			opacity: 0;
			position: absolute;
			margin-top: 20px;
			margin-right: 20px;
			user-select: none;
			white-space: nowrap;

			label {
				text-align: right;
				display: block;
				font-size: 17px;
				color: white;
				font-weight: normal;
				margin-bottom: 20px;
				height: 44px;
				line-height: 44px;
				cursor: pointer;
			}
		}
	}

	.esri-basemap-gallery__item-container {
		flex-flow: row;
		flex-wrap: wrap;
		gap: 0;
		padding-block: 0;
		padding-inline: 0;
	}

	.esriBasemapGallery,
	.esri-basemap-gallery {
		display: none;
		position: absolute;
		margin-right: 20px;
		right: 90px;
		width: 342px;
		max-width: 500px;
		height: auto;
		padding-left: 15px;
		background-color: #fff;
		z-index: @MAP_TOOLKIT_MENU;
		flex-direction: row;
		flex-wrap: wrap;

		&.active {
			display: block;
		}

		.esriBasemapGalleryNode,
		.esri-basemap-gallery__item {
			margin: 10px 15px 0 0;
			padding: 0;
			flex-direction: column;
			width: 93px;
			-webkit-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			cursor: pointer;
			border: none;

			&.esriBasemapGallerySelectedNode .esriBasemapGalleryThumbnail,
			&.esri-basemap-gallery__item--selected .esri-basemap-gallery__item-thumbnail {
				border: 2px solid #00b1b0 !important;
				box-shadow: 0 5px 2px -2px rgba(0, 0, 0, .15);
			}

			.esriBasemapGalleryThumbnail,
			.esri-basemap-gallery__item-thumbnail {
				width: inherit;
				height: 63px;
				margin: 0 0 10px 0;
				background-size: 93px 63px;
				box-shadow: none;
				min-width: 0;
				border-inline-end: 0px;
			}

			.esriBasemapGalleryLabelContainer,
			.esri-basemap-gallery__item-title {
				height: 56px;
				margin-bottom: 10px;
				font-family: "SourceSansPro-Regular";
				font-size: 15px;
				text-align: center;
				display: block;

				span {
					text-align: center;
					vertical-align: middle;
					font-size: 13px;
					line-height: 1.42857143;
					color: #333;
				}
			}
		}
	}

	.tool-menu {
		margin-left: 20px;

		&.left-align .caret {
			left: -13px;
			right: auto;
		}

		.caret {
			float: right;
			width: 22px;
			height: 22px;
			position: absolute;
			top: 86px;
			right: -11px;
			background-color: #fff;
			transform: rotate(45deg);
			border: none !important;
			z-index: -1;
		}
	}
}

.map-tool-bar {
	position: absolute;
	z-index: 11080;

	&>li.map-tool-bar-item {
		display: inline-block;
		width: 40px;
		height: 40px;
		margin-right: 20px;
		background-repeat: no-repeat;
		background-position: center;
		background-color: white;
		border-radius: 20px;
		box-shadow: 0 5px 2px -2px rgba(0, 0, 0, 0.15);

		&:hover {
			cursor: initial;
		}

		&.toolbar-item-selected {
			background-color: #e3e3e3;
		}

		&.toolbar-item-pin {
			background-image: url('../img/direction-setting-panel/drop-destination-dark.png');
		}

		&.toolbar-item-polygon {
			background-image: url('../img/Routing Map/menuicon/Select-Polygon-Black.png');
		}

		&.toolbar-item-rectangle {
			background-image: url('../img/Routing Map/menuicon/Select-Rectangle-Black.png');
		}

		&.toolbar-item-draw {
			background-image: url('../img/Routing Map/menuicon/Select-Freeform-Black.png');
		}

		&.toolbar-item-circle {
			background-image: url('../img/Routing Map/menuicon/Select-Circle-Black.png');
		}

		&.toolbar-item-clear {
			font-family: 'WebComponentsIcons';
			font-size: 24px;
			text-align: center;
			line-height: 40px;
			vertical-align: top;

			&.trash {
				background-image: url(../img/menu/delete-black.svg);
				background-size: 16px;
			}

			&.disable {
				display: none;
			}
		}

		&.disable {
			opacity: 0.5;
			cursor: initial;
		}
	}
}

.map-draw-circle-radius {
	display: none;
	position: absolute;
	right: 16px;
	bottom: 30px;
	width: 120px;
	background-color: white;
	border-radius: 10px;
	padding: 5px;
	opacity: .8;

	.radius-value {
		text-align: right;
		font-size: 29px;
		margin-bottom: 0;
	}

	.radius-unit {
		text-align: right;

		&:hover {
			span.down-arrow.icon {
				background-image: url(../img/map/view/down-arrow.svg);
				padding-right: 10px;
				height: 6px;
				background-size: 8px;
				display: inline-block;
			}
		}
	}

	.measurement-unit-menu {
		bottom: 30px;
		right: 30px;
	}

	.close {
		display: block;
		float: right;
		margin-right: 5px;
	}
}

.esri-ui {
	.esri-ui-corner-container {
		.esri-ui-top-left {
			.esri-component.esri-zoom {
				z-index: @MAP_TOOLKIT_ICON !important;
			}
		}
	}
}

.on-map-tool,
.off-map-tool {
	&.active {
		.map-tool-btn {
			transform: rotate(180deg);
			background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAS1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADmYDp0AAAAGHRSTlMAAwYaNzpPUlRVV1laW1xdws7v9PX2+Pm2BE6WAAAAi0lEQVR42lWP2xLDIAhEkSTGaNNG8cL/f2kRHTv1gXH3LIjwJG9gHuPTA4lLwKHRZ04gJZ+o+sxcPBh1zNA56EUzaP+iFN40tDqWuDWm3jidT2NuL4RlXJW5+mX0ebVyttMxt8z3Vy9GuSsCx7MOhbuOUMBddMOdBEh0IDogMq19HHGELR6/7+9x+wLgPgj5LD2YpQAAAABJRU5ErkJggg==);
		}
	}

	&.left-align {
		&.active {
			.map-tool-btn {
				transform: rotate(-180deg);
			}
		}
	}

	.animate {
		transition: all 400ms ease;
	}

	.tool-icon {
		background-position: center center;
		background-repeat: no-repeat;
		background-color: #fff;
		margin-bottom: 20px;
		cursor: pointer;
	}

	.map-tool-btn {
		z-index: @MAP_TOOLKIT_ICON;
		position: absolute;
		width: 56px;
		height: 56px;
		border-radius: 56px;
		top: 15px;
		right: 15px;
		background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAAM1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACjBUbJAAAAEHRSTlMAECAwQFBgcICPn6+/z9/vIxqCigAAAJ5JREFUeAGNz+EOgyAQA+Aihwz0vHv/p52sccLCEvvDhK+mIh5lWaaaVQOApNZpyOr+8dV97fxkOvbmJY7OQlBdbvcKcMnUD3o0b8n0MxbB95lKN5Fr9Ex7KD0CTBsy5Kqhdza8+eBs0twZeix/3LzOHMW9b+RVrv3aN8l1p/80WtqZPjSe2jeAruHdNg3LtuNurtl42Phn5XsQwfO8AeAwCo2Wpm9pAAAAAElFTkSuQmCC);
		box-shadow: 0 5px 2px -2px rgba(0, 0, 0, 0.15);
		transform: rotate(0deg);
		pointer-events: auto;
	}

	.map-applied-geo-search {
		.map-tool-btn;
		background-image: url(../img/map/view/Geo-Search-b.png);
		background-color: rgba(18, 89, 208, 0.8),
	}
}

#measurementInfoPanel.measurement-info-panel {
	display: none;
	position: absolute;
	bottom: 30px;
	height: 110px;
	width: 240px;
	border-radius: 4px;
	background: transparent;
	font-family: "SourceSansPro-Regular";
	color: #333;
	overflow: hidden;
	pointer-events: auto;

	&.active {
		display: block;
	}

	.measurement-tab-container {
		width: 86px;
		float: left;
		background: transparent;

		.measurement-tab:last-child {
			margin-bottom: 0;
		}

		.measurement-tab {
			height: 36px;
			line-height: 36px;
			margin-right: 1px;
			margin-bottom: 1px;
			padding-left: 10px;
			font-size: 17px;
			background-color: rgba(250, 250, 250, 0.8);
			box-sizing: border-box;
			cursor: pointer;

			&.active {
				width: 86px;
				margin-right: 0;
				background-color: rgba(255, 255, 255, 0.9);
				font-family: "SourceSansPro-SemiBold";
			}
		}
	}

	.measurement-panel-container {
		width: 154px;
		height: 100%;
		float: right;
		background-color: rgba(255, 255, 255, 0.9);

		.measurement-panel {
			position: relative;
			width: 100%;
			height: 100%;
			display: none;

			&.active {
				display: inline-block;
				align-items: center;
				justify-content: center;
			}

			&.distance,
			&.area {
				.measurement-value {
					float: right;
					height: 38px;
					line-height: 38px;
					margin-top: 35px;
					margin-right: 24px;
					text-align: right;
					font-family: "SourceSansPro-SemiBold";
					font-size: 29px;
					cursor: pointer;
				}
			}

			&.location {
				padding-top: 8px;

				&.active.no-pin-location {
					display: flex;

					.location-track {
						width: auto;
					}

					.location-pin {
						display: none;
					}

					.measurement-latlon {
						margin-left: 0;
					}
				}

				.measurement-latlon:first-child {
					margin-bottom: 6px;
				}

				.measurement-latlon {
					position: relative;
					height: 34px;
					margin-left: 34px;
					margin-bottom: 8px;
					cursor: pointer;

					&.location-pin .latlon-icon {
						&::after {
							content: "";
							width: 8px;
							height: 8px;
							border-radius: 4px;
							border: 1px solid #333;
							margin-top: 13px;
							margin-left: 4px;
							position: relative;
							display: block;
						}
					}

					.latlon-icon {
						position: absolute;
						left: -24px;
						width: 24px;
						height: 34px;
						cursor: pointer;
					}

					.latlon-container {
						height: 17px;
						width: 86px;
						font-size: 12px;
						overflow-wrap: normal;

						.latlon-label {
							float: left;
							width: 28px;
							font-family: "SourceSansPro-SemiBold";
						}

						.latlon-value {
							float: right;
							font-family: "SourceSansPro-Regular";
							width: 58px;
							text-align: right;
							word-break: keep-all;
						}
					}

					&.location-track {
						cursor: default;

						.latlon-icon {
							background-image: url("../img/map/view/cursor.svg");
							background-size: 20px;
							background-position: -3px center;
							cursor: default;
						}
					}
				}

				.measurement-unit {
					position: absolute;
					bottom: 8px;
					right: 10px;
				}
			}

			.measurement-unit {
				position: relative;
				float: right;
				width: 100%;
				margin-top: -5px;
				padding-right: 24px;
				font-size: 12px;
				text-align: right;
				white-space: nowrap;
				cursor: pointer;

				&:hover,
				&.menu-opened {
					text-decoration: underline;

					.down-arrow {
						display: block;
					}
				}

				.down-arrow {
					position: absolute;
					display: none;
					width: 8px;
					height: 8px;
					top: 6px;
					right: 11px;
					background-image: url("../img/map/view/down-arrow.svg");
					background-size: 8px;
				}
			}
		}
	}

	.close-btn {
		position: absolute;
		top: 8px;
		right: 8px;
		width: 8px;
		height: 8px;
		background-image: url("../img/map/view/Close.svg");
		background-size: 8px 8px;
	}

	.btn {
		margin: 0;
		padding: 0;
		background-repeat: no-repeat;
		background-position: center center;
		cursor: pointer;

		&:active {
			box-shadow: none;
		}
	}

	.icon {
		margin: 0;
		padding: 0;
		background-repeat: no-repeat;
		background-position: center center;
	}
}

.measurement-unit-menu {
	position: absolute;
	display: block;
	pointer-events: auto;
	background-color: #fafafa;
	border: 1px solid #e4e4e4;
	color: #333;
	font-family: "SourceSansPro-Regular";
	font-size: 12px;
	box-shadow: 0 0 4px 0 rgba(51, 51, 51, 0.5);

	ul.unit-menu-list {
		position: relative;
		padding: 8px 0;
		margin: 0;

		li {
			list-style: none;
			padding: 0 8px;
			cursor: pointer;

			span {
				cursor: inherit;
			}

			&:hover {
				background-color: #b9b9b9;
			}
		}
	}
}

#measurementWidget {
	display: none !important;
}

.pin-cursor {
	cursor: url("../img/map/view/pin.png") 10 20, auto !important;
}

.expand-button {
	width: 32px;
	height: 32px;
	background: url(../img/map/expand.png) center no-repeat white;
	background-size: 24px 24px;
	cursor: pointer;

	&:hover {
		background-color: #f0f0f0;
	}

	&.restore {
		background-image: url('../img/map/restore.png');
	}

	&.calendar-expand-button {
		right: 43%;
		left: auto;
		display: block;
		height: 18px;
		position: absolute;
		width: 14.3%;
		top: 5px;
		background-size: 14px;
		z-index: 1;

		&:hover {
			background-color: white;
		}
	}
}



// expand map tool
.map-page {
	.map-expand-button {
		position: absolute;
		top: 15px;
		left: 79px;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
	}

	.map-expand-button.is-mobile-device {
		top: auto;
		bottom: 30px;
		left: 15px;
	}
}

#mobileBasemapGallery {
	.esri-basemap-gallery {
		.esri-basemap-gallery__item-container {
			flex-direction: row;
			flex-wrap: wrap;
			gap: 0;
			padding-block: 0;
			padding-inline: 0;

			.esri-basemap-gallery__item {
				-webkit-user-select: none;
				-moz-user-select: none;
				-ms-user-select: none;
				cursor: pointer;

				@media @small {
					display: inline-block;
					margin: 0 0 10px 0;
					padding: 5px 10px 0 10px;
					width: calc(100% / 3);
				}

				@media @small-landscape {
					display: inline-block;
					margin: 0 0 10px 0;
					padding: 5px 10px 0 10px;
					width: calc(100% / 6);
				}

				&.esri-basemap-gallery__item--selected {
					border-left-color: #FFFFFF !important;
				}

				&.esri-basemap-gallery__item--selected .esri-basemap-gallery__item-thumbnail {
					border: 2px solid #00b1b0 !important;
					box-shadow: 0 5px 2px -2px rgba(0, 0, 0, .15);
				}

				.esri-basemap-gallery__item-thumbnail {
					width: inherit;
					height: 63px;
					margin: 0 0 10px 0;
					background-size: 93px 63px;
					min-width: 0;
					border-inline-end: 0px;

					@media @small {
						width: auto;
					}

					@media @small-landscape {
						width: auto;
					}
				}

				.esri-basemap-gallery__item-title {
					width: 93px;
					height: 63px;
					text-align: center;
					display: block;

					@media @small {
						width: 100%;
					}
				}
			}
		}
	}
}

.calcite-mode-light {
	--calcite-color-brand: transparent;
}