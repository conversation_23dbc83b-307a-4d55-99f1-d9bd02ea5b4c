(function()
{
	createNamespace("TF.Control").SelectItemViewModel = SelectItemViewModel;

	function SelectItemViewModel(source, options)
	{
		this.selectedItems = [];
		this.source = source.slice();
		this.options = options;
	}

	SelectItemViewModel.prototype.init = function(viewModel, element)
	{
		this.$element = $(element);
		this.initGrid();
	};

	SelectItemViewModel.prototype.initGrid = function()
	{
		var self = this;
		self.selectedItems = [];

		this.$element.find(".kendo-grid").kendoGrid({
			dataSource: new kendo.data.DataSource({
				data: this.source,
			}),
			columns: self.options.columns || [],
			height: 400,
			selectable: self.options.multiple ? "multiple" : "row",
			sortable: {
				mode: "single",
				allowUnsort: true
			},
			pageable: {
				pageSize: 5000,
				messages: {
					display: ""
				}
			},
			hideScrollNotOverflow: true,
			dataBinding: function()
			{
				self.setFooterDisplay();
			},
			change: function()
			{
				var selectedRows = this.select();
				self.selectedItems = [];
				for (var i = 0; i < selectedRows.length; i++)
				{
					var dataItem = this.dataItem(selectedRows[i]);
					self.selectedItems.push(dataItem);
				}
				self.setFooterDisplay();
			}
		});
		this.kendoGrid = this.$element.find(".kendo-grid").data("kendoGrid");
	};

	SelectItemViewModel.prototype.setFooterDisplay = function()
	{
		this.$element.find(".kendo-grid").find(".k-grid-pager").css("text-align", "left").html(String.format("{0} of {1}", this.selectedItems.length, this.source.length));
	};

	SelectItemViewModel.prototype.apply = function()
	{
		var self = this;
		return Promise.resolve(self.selectedItems);
	};

	SelectItemViewModel.prototype.cancel = function()
	{
		return Promise.resolve(true);
	};

})();