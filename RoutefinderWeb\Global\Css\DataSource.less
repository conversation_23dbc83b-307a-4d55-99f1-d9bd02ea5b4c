.datasource-archive {
	.kendo-grid input[type=checkbox] {
		position: relative !important;
		margin-left: 0px !important;
	}

	.checkbox input[type="checkbox"] {
		margin-left: -20px;
	}
}

.kendo-grid {
	.k-grid-archive {
		height: 16px !important;
		background-image: url('../../global/img/Icons/archive.svg');
		background-size: 16px;
	}

	.k-grid-copyAsNew {
		height: 16px !important;
		opacity: 0.6;
		background-image: url('../../global/img/Routing Map/menuicon/Copy-Black.png');
	}

	.k-grid-info {
		height: 16px !important;
		opacity: 0.6;
		background-image: url('../../global/img/Routing Map/menuicon/Info-Black.png');
	}
}

.new-db-menu-container {
	display: inline-block;
	letter-spacing: -10px;
	margin-bottom: 10px;

	.btn-sharp {
		width: 24px;
		padding-left: 7px;
		border-left: none;
	}
}

.db-version-info {
	height: 30px;

	.db-version,
	.client-key {
		float: left;
		line-height: 30px;
	}

	.db-datasource-count {
		float: right;
		line-height: 30px;
	}
}

.manage-archive {
	.kendo-grid {
		.text-center.k-command-cell {
			a.k-grid-restore {
				opacity: 0.8;

				&:hover {
					opacity: 1;
				}
			}
		}
	}
}