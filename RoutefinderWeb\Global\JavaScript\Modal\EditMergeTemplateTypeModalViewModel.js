(function()
{
	createNamespace("TF.Modal").EditMergeTemplateTypeModalViewModel = EditMergeTemplateTypeModalViewModel;

	function EditMergeTemplateTypeModalViewModel(options, newRecordModel, templateType)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.templateType = templateType;
		this.title(options.title);
		this.sizeCss = "modal-dialog-md";
		this.buttonTemplate(options.buttonTemplate || "modal/positiveNegative");
		if (options.positiveButtonLabel) this.obPositiveButtonLabel(options.positiveButtonLabel);
		if (options.negativeButtonLabel) this.obNegativeButtonLabel(options.negativeButtonLabel);
		if (options.otherButtonLabel) this.obOtherButtonLabel(options.otherButtonLabel);


		var editMergeTemplateTypeViewModel = null;
		if (this.templateType && this.templateType.isEmail())
		{
			this.contentTemplate("workspace/controlpanel/modal/EditEmailMergeTemplateType");
			editMergeTemplateTypeViewModel = new TF.Control.EditEmailMergeTemplateTypeViewModel(newRecordModel, options);
			this.obOtherButtonVisible(false);
		} else
		{
			this.contentTemplate("workspace/controlpanel/modal/editmergetemplatetype");
			editMergeTemplateTypeViewModel = new TF.Control.EditMergeTemplateTypeViewModel(newRecordModel, options);
		}
		this.data(editMergeTemplateTypeViewModel);
	}

	EditMergeTemplateTypeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	EditMergeTemplateTypeModalViewModel.prototype.constructor = EditMergeTemplateTypeModalViewModel;

	EditMergeTemplateTypeModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		var self = this;
		self.negativeClose();
		return true;
	};

	EditMergeTemplateTypeModalViewModel.prototype.otherClick = function(viewModel, el)
	{
		var self = this;
		TF.MergeTemplateTypeHelper.resetTemplateData(self.templateType.toData(), self.data().obSelectedTemplate());
	};

	EditMergeTemplateTypeModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		if (self.templateType && self.templateType.isEmail())
		{
			self.data().save(self.data().obSelectedTemplate());
			self.positiveClose(self.data().obSelectedTemplate());
			return;
		}

		if (self.data().obSaveAsTemplateEnabled())
		{
			var newTemplate = self.data()._recalculatePageSetting()
			if (newTemplate.DataChanged)
			{
				tf.promiseBootbox.alert("The page setting has been updated, please review it.");
				return;
			}
		}

		self.data().apply().then(function(success)
		{
			if (!success)
			{
				return;
			}

			self.positiveClose(self.data().obSelectedTemplate());
		});
	};

	EditMergeTemplateTypeModalViewModel.prototype.dispose = function()
	{
		if (this.data() && this.data().dispose)
		{
			this.data().dispose();
		}
	};
})();


