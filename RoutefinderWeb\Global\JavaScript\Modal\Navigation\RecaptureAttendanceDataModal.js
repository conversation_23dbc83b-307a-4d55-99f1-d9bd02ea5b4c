(function()
{
	createNamespace("TF.Modal.Navigation").RecaptureAttendanceDataModal = RecaptureAttendanceDataModal;

	function RecaptureAttendanceDataModal()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-400";
		this.title('Recapture Attendance Data');
		this.contentTemplate('Navigation/RecaptureAttendanceData');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Recapture Data");
		this.openDataSourceViewModel = new TF.Navigation.RecaptureAttendanceDataViewModel();
		this.data(this.openDataSourceViewModel);
	}

	RecaptureAttendanceDataModal.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	RecaptureAttendanceDataModal.prototype.constructor = RecaptureAttendanceDataModal;

	RecaptureAttendanceDataModal.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();