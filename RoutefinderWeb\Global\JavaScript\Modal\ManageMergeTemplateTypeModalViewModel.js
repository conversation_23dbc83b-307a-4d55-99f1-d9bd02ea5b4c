(function()
{
	createNamespace("TF.Modal").ManageMergeTemplateTypeModalViewModel = ManageMergeTemplateTypeModalViewModel;

	function ManageMergeTemplateTypeModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Manage Documents Layout");
		this.sizeCss = "modal-dialog-lg";
		this.contentTemplate("workspace/controlpanel/modal/manageMergeTemplateType");
		this.buttonTemplate("modal/negative");
		this.obNegativeButtonLabel("Close");
		this.obResizable(false);
		this.data(new TF.Control.ManageMergeTemplateTypeViewModel());
	}

	ManageMergeTemplateTypeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ManageMergeTemplateTypeModalViewModel.prototype.constructor = ManageMergeTemplateTypeModalViewModel;

	ManageMergeTemplateTypeModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		var self = this;
		self.negativeClose();
		return true;
	};
})();


