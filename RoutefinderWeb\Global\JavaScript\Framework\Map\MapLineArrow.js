(function()
{
	var MapLineArrowDefaults = {
		templateImageUrl: '',
		templateSvg: 'M0 0L 200 0',
		value: 'none',
		svg: '',
	};
	var _allLineArrows = [];

	function MapLineArrow()
	{
	}

	createNamespace("TF.Map").MapLineArrow = new MapLineArrow();

	MapLineArrow.prototype.getAll = function()
	{
		return _allLineArrows.slice(0);
	};

	MapLineArrow.prototype.getByValue = function(value)
	{
		var lineArrow = _allLineArrows.filter(function(c)
		{
			return c.value == value;
		});
		if (lineArrow.length > 0)
		{
			return lineArrow[0];
		}
		return null;
	};

	MapLineArrow.prototype.getTemplateSvg = function(value)
	{
		var lineArrow = this.getByValue(value);
		if (lineArrow && lineArrow.svg)
		{
			var template = lineArrow.svg;
			return MapLineArrowDefaults.templateSvg +
				this.transform(0, [30, 0], template) +
				this.transform(0, [75, 0], template) +
				this.transform(0, [120, 0], template) +
				this.transform(0, [165, 0], template);
		}

		return MapLineArrowDefaults.templateSvg;
	};

	function _createLineArrow(options)
	{
		_allLineArrows.push($.extend({}, MapLineArrowDefaults, options));
	}

	_createLineArrow({
		templateImageUrl: 'linearrow/arrow-none.svg',
		value: 'none'
	});

	_createLineArrow({
		templateImageUrl: 'linearrow/arrow-end.svg',
		value: 'end'
	});

	_createLineArrow({
		templateImageUrl: 'linearrow/arrow-begin.svg',
		value: 'begin'
	});

	_createLineArrow({
		templateImageUrl: 'linearrow/arrow-begin-end.svg',
		value: 'begin-end'
	});
})();