(function()
{
	createNamespace("TF.Modal").ListQuestionValueModalViewModel = ListQuestionValueModalViewModel;

	function ListQuestionValueModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);

		this.contentTemplate('modal/ListQuestionData');
		this.buttonTemplate('modal/positivenegative');
		this.title(options.title);
		this.sizeCss = "modal-dialog-sm";
		this.obPositiveButtonLabel("OK");
		this.viewModel = new TF.Control.ListQuestionValueViewModel(options);
		this.data(this.viewModel);
	}

	ListQuestionValueModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ListQuestionValueModalViewModel.prototype.constructor = ListQuestionValueModalViewModel;

	ListQuestionValueModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then((result) =>
		{
			this.positiveClose(result);	
		});
	};

	ListQuestionValueModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose(false);
	};
})();