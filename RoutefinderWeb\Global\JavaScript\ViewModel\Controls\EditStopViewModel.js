﻿(function()
{
	createNamespace("TF.Fragment").EditStopViewModel = EditStopViewModel;

	function EditStopViewModel(tripStopViewModel, tripViewModel)
	{
		this.cityName = ko.observable(tripStopViewModel.city());
		this.map = null;

		// Stop information.
		this.duration = null;
		this.locationName = tripStopViewModel.street();
		this.numberOfStudentsAtStop = tripStopViewModel.numStuds();
		this.stopTime = moment(tripStopViewModel.stopTime()).format('h:mm a');
		this.tripColor = '#' + tripViewModel.colorTheme;
		this.tripName = tripViewModel.tripDataModel.name();
		this.tripStartTime = moment(tripViewModel.startTime()).format('h:mm a');

		this._locationPoint = tripStopViewModel.featureStopPoint().geometry;

		var busStopTime = 60; // Hardcoded 60 seconds per stop.
		var studentLoadTime = 20; // Hardcoded 20 seconds per student;

		this.stopAndLoadTime = ko.computed(function()
		{
			return busStopTime + studentLoadTime * this.numberOfStudentsAtStop;
		}, this);
	}

	EditStopViewModel.prototype.drawMiniMap = function(vm, el)
	{
		var tile = new OpenLayers.Layer.TMS("TMS albany", pathCombine(TileServer, "tms/"),
		{
			layername: 'general', type: 'png',
			tileSize: new OpenLayers.Size(256, 256),
			transitionEffect: 'resize'
		});

		this.map = new OpenLayers.Map(el, {
			projection: new OpenLayers.Projection("EPSG:900913"),
			maxResolution: 78271.516964,
			units: 'm',
			numZoomLevels: 19,
			maxExtent: new OpenLayers.Bounds(-20037508.3428, -20037508.3428, 20037508.3428, 20037508.3428)
		});
		//createNamespace("tf.debug").currentMap = map;
		this.map.addLayers([tile]);

		var markerLayer = new OpenLayers.Layer.Vector("markerLayer")
		this.map.addLayer(markerLayer);

		MapPageView.AddMarker2(markerLayer, "img/stop_4A4A4A.png", 18, 18, this._locationPoint);

		var olGeometry = tf.converter.GeoJson2OlGeometry(this._locationPoint);
		this.map.setCenter([olGeometry.x, olGeometry.y], 16);
	};


	EditStopViewModel.prototype.destroy = function()
	{
		this.map.destroy();
	}
})()


