﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.RouteDataModel = function(tripEntity)
	{
		namespace.BaseDataModel.call(this, tripEntity);
	}

	namespace.RouteDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.RouteDataModel.prototype.constructor = namespace.RouteDataModel;

	namespace.RouteDataModel.prototype.mapping = [
		{ "from": "Id", "default": 0 },
		{ "from": "Name", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } }
	];
})();