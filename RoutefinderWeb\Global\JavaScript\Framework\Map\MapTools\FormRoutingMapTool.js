!function()
{
	createNamespace("TF.Map").FormRoutingMapTool = FormRoutingMapTool;

	function FormRoutingMapTool(routingMapDocumentViewModel, options)
	{
		let self = this;
		TF.Map.RoutingMapTool.call(self, routingMapDocumentViewModel, options);
	}

	TF.Map.FormRoutingMapTool.prototype = Object.create(TF.Map.RoutingMapTool.prototype);
	TF.Map.FormRoutingMapTool.constructor = FormRoutingMapTool;

	FormRoutingMapTool.prototype.init = function()
	{
		TF.Map.RoutingMapTool.prototype.init.call(this);
	};

	FormRoutingMapTool.prototype.showMenuInternal = function(menuItem)
	{
		let self = this;
		let dataIds = self.routingMapDocumentViewModel.parentDocument.gridMap
			.parentDocument.gridViewModel.searchGrid.allIds;
		let dataCheckPromises = [];
		for (let menuItem of self.mapLayersMenu.children)
		{
			if (menuItem.isDevider)
			{
				continue;
			}

			if (menuItem.viewModel.isCreatedLocation)
			{
				let currentFieldItem = menuItem;
				dataCheckPromises.push(self.hasAnyLocationData(dataIds).then(hasAny =>
				{
					menuItem.viewModel.obIsDisable(!hasAny);
					currentFieldItem.isDisable = !hasAny;
				}));
			}
			else
			{
				let currentFieldItem = menuItem;
				dataCheckPromises.push(self.hasAnyFieldData(dataIds, menuItem.viewModel.field).then(hasAny =>
				{
					menuItem.viewModel.obIsDisable(!hasAny);
					currentFieldItem.isDisable = !hasAny;
				}));
			}
		}

		return Promise.all(dataCheckPromises).then(() =>
		{
			return TF.Map.BaseMapTool.prototype.showMenuInternal.call(this, menuItem);
		});
	}

	FormRoutingMapTool.prototype.hasAnyLocationData = function(dataIds)
	{
		let requestOptions = {};
		requestOptions.data = $.extend(requestOptions.data, {
			fields: ["Latitude"],
			idFilter: { IncludeOnly: dataIds }
		});

		return tf.promiseAjax.post(
			pathCombine(tf.api.apiPrefix(), "search/formResults"),
			requestOptions
		).then(function(response)
		{
			return (response.Items.some(x => x.Latitude > 0));
		});
	};

	FormRoutingMapTool.prototype.hasAnyFieldData = function(dataIds, field)
	{
		let strfieldsFilter = `&eq(UDGridField,${field})`;
		return tf.promiseAjax.get(
			pathCombine(tf.api.apiPrefixWithoutDatabase(), "MapUDGridRecords"),
			{
				paramData: {
					dbid: tf.datasourceManager.databaseId,
					"@filter": `in(UDGridRecordID,${dataIds.join(",")})${strfieldsFilter}`
				}
			}).then(res => 
			{
				let mapRecords = res.Items || [];
				return (mapRecords.some(x => JSON.parse(x.ShapeData).length));
			});
	}

	FormRoutingMapTool.buildMenuItem = function(header, icon, viewModel, click)
	{
		return new TF.FormRoutingMap.FormMenuItem({
			header: header,
			icon: icon,
			children: [],
			isToggled: true,
			disable: click ? false : true,
			toggleStatus: viewModel.obShow,
			click: function()
			{
				click && click(viewModel);
			}
		}, viewModel);
	};

}()
