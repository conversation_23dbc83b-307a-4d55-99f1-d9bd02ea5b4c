﻿(function()
{
	createNamespace('TF.Control').AddTwoFieldsViewModel = AddTwoFieldsViewModel;

	function AddTwoFieldsViewModel(fieldName, id, entity)
	{
		this.fieldName = fieldName;
		this.obName = ko.observable();
		this.obDescriptionVisible = ko.observable(true);
		this.obDescription = ko.observable("Description");//description might be another name either.
		this.obDescriptionRequired = ko.observable(false);
		this.entityDataModel = null;
		this.obShowAccessRole = ko.observable(false);
		this.obAccessRoleTitle = ko.observable("");
		this.isSaving = false;
		switch (fieldName)
		{
			case 'documentclassification':
				this.entityDataModel = TF.DataModel.DocumentClassificationDataModel;
				this.obName(tf.applicationTerm.getApplicationTermSingularByName("Name"));
				break;
			case 'vehiclebodytype':
				this.entityDataModel = TF.DataModel.VehicleBodyTypeDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'vehiclebraketype':
				this.entityDataModel = TF.DataModel.VehicleBrakeTypeDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'vehiclefueltype':
				this.entityDataModel = TF.DataModel.VehicleFuelTypeDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'vehiclemake':
				this.entityDataModel = TF.DataModel.VehicleMakeDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'vehiclemodel':
				this.entityDataModel = TF.DataModel.VehicleModelDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'vehiclemakeofbody':
				this.entityDataModel = TF.DataModel.VehicleMakeOfBodyDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'vehiclecategory':
				this.entityDataModel = TF.DataModel.VehicleCategoryDataModel;
				this.obName("Name");
				this.obDescriptionVisible(false);
				break;
			case 'dashboards':
				this.entityDataModel = TF.DataModel.CustomizedDashboardDataModel;
				this.obName("Name");
				this.obShowAccessRole(!entity);
				this.obAccessRoleTitle("Dashboard Access by Role");
				break;
			case 'stafftypes':
				this.entityDataModel = TF.DataModel.StaffTypeDataModel;
				this.obName("Type");
				break;
			case 'genders':
				this.entityDataModel = TF.DataModel.GenderDataModel;
				this.obName("Code");
				this.obDescription("Name");
				this.obDescriptionRequired(true);
				break;
			case 'chartviewscreens':
				this.entityDataModel = TF.DataModel.ChartViewLayoutDataModel;
				this.obName("Name");
				this.obDescription("Description");
				this.obDescriptionRequired(false);
				this.obDescriptionVisible(false);
				break;
			default:
				return;
		}
		this.obEntityDataModel = ko.observable(new this.entityDataModel());
		this.obEntityDataModel().id(id);
		this.obEntityDataModel().apiIsDirty(false);
		this.entity = entity;
		this.typeRoles = null;

		this.pageLevelViewModel = new TF.PageLevel.AddTwoFieldsPageViewModel();
		this.roleTypeHelper = new TF.Helper.RoleTypeHelper();

		if ((fieldName === 'dashboards' && entity && entity.DashboardLibraryId != null) || (fieldName === 'chartviewscreens'))
		{
			this.obEntityDataModel().name(entity.Name);
			this.obEntityDataModel().description(entity.Description);
		}
	}

	AddTwoFieldsViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(result)
			{
				if (result)
				{
					var isNew = this.obEntityDataModel().id() ? false : true;
					let roleDashboards = null;
					if (this.obShowAccessRole() && this.typeRoles)
					{
						roleDashboards = (this.typeRoles.value() || []).filter(item => item >= 0).map(item =>
						{
							return {
								RoleID: item
							};
						})
					}

					if ((this.fieldName || '').toLowerCase() === 'dashboards')
					{
						var dashboardData = this.obEntityDataModel().toData();
						let params;
						if (this.entity)
						{
							let entity = {
								Description: dashboardData.Description,
								Layout: this.entity.Layout,
								Name: dashboardData.Name,
								RoleDashboards: this.entity.RoleDashboards
							};
							params = {
								data: [entity],
								paramData: { "@relationships": "RoleDashboard" }
							};
						}
						else
						{
							params = {
								data: [{
									Name: dashboardData.Name.trim(),
									Description: dashboardData.Description,
									RoleDashboards: roleDashboards || [],
								}],
								paramData: {
									"@relationships": "RoleDashboard"
								}
							};
						}
						if (this.isSaving)
						{
							return;
						}
						this.isSaving = true;
						return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName, isNew ? "" : this.obEntityDataModel().id()),
							params)
							.then(function(data)
							{
								this.isSaving = false;
								var item = data && data.Items[0];
								PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT), {
									callback: () =>
									{

										var documentData = new TF.Document.DocumentData(TF.Document.DocumentData.CustomizedDashboardDetail, {
											type: "CustomizedDashboardDetail",
											tabName: item.Name,
											id: item.Id,
											dashboardEntity: item
										});
										tf.documentManagerViewModel.add(documentData, false);
									}
								});

								return item;
							}.bind(this)).catch(() =>
							{
								this.isSaving = false;
							});
					}

					if ((this.fieldName || '').toLowerCase() === 'chartviewscreens')
					{
						var chartViewData = this.obEntityDataModel().toData();
						let params;
						if (this.entity)
						{
							let entity = {
								Description: chartViewData.Description,
								Layout: this.entity.Layout,
								Name: chartViewData.Name,
								DataTypeId: this.entity.DataTypeId
							};
							params = {
								data: [entity]
							};
						}
						else
						{
							params = {
								data: [{
									Name: dashboardData.Name.trim(),
									Description: dashboardData.Description
								}],								
							};
						}
						if (this.isSaving)
						{
							return;
						}
						this.isSaving = true;
						return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName, isNew ? "" : this.obEntityDataModel().id()),
							params)
							.then(function(data)
							{
								this.isSaving = false;
								var item = data && data.Items[0];								

								return item;
							}.bind(this)).catch(() =>
							{
								this.isSaving = false;
							});
					}

					if ((this.fieldName || '').toLowerCase() === 'stafftypes')
					{
						var stafftypeData = this.obEntityDataModel().toData();
						return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName),
							{
								data: [{
									StaffTypeID: isNew ? 0 : this.obEntityDataModel().id(),
									StaffTypeName: stafftypeData.StaffTypeName,
									StaffTypeDescription: stafftypeData.StaffTypeDescription,
									IsSystemDefined: false
								}]
							})
							.then(function(data)
							{
								PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
								return data.Items[0];
							}.bind(this))
					}

					if ((this.fieldName || '').toLowerCase() === 'genders')
					{
						var genderData = this.obEntityDataModel().toData();
						if (this.obEntityDataModel().id() > 0)
						{
							isNew = false;
						}
						return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName),
							{
								data: [{
									ID: isNew ? 0 : this.obEntityDataModel().id(),
									Code: genderData.Code,
									Name: genderData.Name,
									IsSystemField: genderData.IsSystemField
								}]
							})
							.then(function(data)
							{
								PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
								return data.Items[0];
							}.bind(this))
					}

					return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefix(), this.fieldName, isNew ? "" : this.obEntityDataModel().id()), { data: this.obEntityDataModel().toData() })
						.then(function(data)
						{
							PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
							return data.Items[0];
						}.bind(this))
				}
			}.bind(this));
	}

	AddTwoFieldsViewModel.prototype.init = async function(viewModel, el)
	{
		var self = this;
		var fieldName = this.fieldName;
		this.$form = $(el);
		let entityModel = this.obEntityDataModel();
		if (entityModel.validator && entityModel.validator.maxLength)
		{
			['name', 'description'].forEach(property =>
			{
				this.$form.find(`[name="${property}"]`).attr('maxlength', entityModel.validator.maxLength[property]);
			});
		}
		var validatorFields = {}, isValidating = false, self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.name = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: self.obName() + " is required"
				},
				callback: {
					message: self.obName() + " must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}

						if ((fieldName || '').toLowerCase() === 'dashboards')
						{
							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), fieldName), {
								paramData: {
									"@filter": "eq(Name," + value.trim() + ")",
									"@fields": "Id"
								}
							}, { overlay: false })
								.then(function(apiResponse)
								{
									return !(apiResponse.Items[0] && apiResponse.Items[0].Id);
								});
						}
						if ((fieldName || '').toLowerCase() === 'chartviewscreens')
						{
							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), fieldName), {
								paramData: {
									"@filter": "eq(Name," + value.trim() + ")",
									"@fields": "Id"
								}
							}, { overlay: false })
								.then(function(apiResponse)
								{
									return !(apiResponse.Items[0] && apiResponse.Items[0].Id);
								});
						}
						if ((fieldName || '').toLowerCase() === 'stafftypes')
						{
							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), fieldName), {
								paramData: {
									"@filter": "eq(StaffTypeName," + value + ")",
									"@fields": "StaffTypeId"
								}
							}, { overlay: false })
								.then(function(apiResponse)
								{
									return !(apiResponse.Items[0] && apiResponse.Items[0].StaffTypeId && apiResponse.Items[0].StaffTypeId != self.obEntityDataModel().id());
								});
						}
						if ((fieldName || '').toLowerCase() === 'genders')
						{
							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), fieldName), {
								paramData: {
									"@filter": "eq(Code," + value.trim() + ")",
									"@fields": "ID"
								}
							}, { overlay: false })
								.then(function(apiResponse)
								{
									return !(apiResponse.Items[0] && apiResponse.Items[0].ID && apiResponse.Items[0].ID != self.obEntityDataModel().id());
								});
						}

						return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), fieldName, "unique"), {
							data: new this.entityDataModel(this.getUniqueObject(value, this.obEntityDataModel().id())).toData()
						}, { overlay: false })
							.then(function(apiResponse)
							{
								return apiResponse.Items[0];
							});
					}.bind(this)
				}
			}
		}

		if (this.fieldName === 'genders')
		{
			validatorFields.description = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: self.obDescription() + " is required"
					},
					callback: {
						message: self.obDescription() + " must be unique",
						callback: function(value, validator, $field)
						{
							if (!value)
							{
								updateErrors($field, "unique");
								return true;
							}
							else
							{
								updateErrors($field, "required");
							}

							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), fieldName), {
								paramData: {
									"@filter": "eq(Name," + value.trim() + ")",
									"@fields": "ID"
								}
							}, { overlay: false })
								.then(function(apiResponse)
								{
									return !(apiResponse.Items[0] && apiResponse.Items[0].ID && apiResponse.Items[0].ID != self.obEntityDataModel().id());
								});
						}.bind(this)
					}
				}
			}
		}

		this.load().then(() =>
		{
			$(el).bootstrapValidator({
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

			this.$form.find("input[name=name]").focus();

			this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));

			this.LimitInput();
		});

		if (this.obShowAccessRole() && !this.typeRoles)
		{
			this.typeRoles = await self.roleTypeHelper.initRolesAccessUI($(el), [], 'dashboards', 'save');
		}
	};

	AddTwoFieldsViewModel.prototype.load = function()
	{
		if (this.obEntityDataModel().id() != null)
		{
			let promise;
			if (this.fieldName === 'genders')
			{
				promise = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName), {
					paramData: {
						ID: this.obEntityDataModel().id()
					}
				});
			}
			else
			{
				promise = tf.promiseAjax.get(pathCombine(this.fieldName == "stafftypes" ? tf.api.apiPrefixWithoutDatabase() : tf.api.apiPrefix(), this.fieldName, this.obEntityDataModel().id()));
			}
			return promise.then(function(data)
			{
				this.obEntityDataModel(new this.entityDataModel(data.Items[0]));
				this.$form.find("input[name=name]").change();
				return;
			}.bind(this))
		}
		return Promise.resolve();
	};

	AddTwoFieldsViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(data)
			{
				return data;
				this.dispose();
			}, function()
			{
			});
	};

	AddTwoFieldsViewModel.prototype.LimitInput = function()
	{
		switch (this.fieldName)
		{
			case 'dashboards':
			case 'chartviewscreens':
			case 'documentclassification':
				var $name = this.$form.find("input[name=name]");
				$name.attr("maxlength", 200);
				break;
			case 'genders':
				var $name = this.$form.find("input[name=name]");
				$name.attr("maxlength", 1);
				var $description = this.$form.find("input[name=description]");
				$description.attr("maxlength", 50);
				break;
			case 'vehiclebodytype':
			case 'vehiclebraketype':
			case 'vehiclefueltype':
			case 'vehiclemake':
			case 'vehiclemodel':
			case 'vehiclemakeofbody':
			case 'vehiclecategory':
				var $name = this.$form.find("input[name=name]");
				$name.attr("maxlength", 180);
				break;
		}
	};
	AddTwoFieldsViewModel.prototype.getUniqueObject = function(value, id)
	{
		switch (this.fieldName)
		{
			case 'dashboards':
			case 'chartviewscreens':
			case 'documentclassification':
			case 'vehiclebodytype':
			case 'vehiclebraketype':
			case 'vehiclefueltype':
			case 'vehiclemake':
			case 'vehiclemodel':
			case 'vehiclemakeofbody':
			case 'vehiclecategory':
				return { Name: value, Id: this.obEntityDataModel().id() };
		}
		return {};
	}

	AddTwoFieldsViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

