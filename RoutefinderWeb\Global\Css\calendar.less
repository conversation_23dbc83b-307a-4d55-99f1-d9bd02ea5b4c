﻿@eventLineHeight: 25px;
@blackFontColor: #333333;
@k-calendar-button-color: #D0503C;

.k-scheduler {
	.k-header {
		background-color: transparent;
	}

	.k-floatwrap.k-header.k-scheduler-toolbar {
		background-color: transparent;

		li {
			border-radius: 0px;
			background-color: transparent;
			background-image: none;

			&.k-selected {
				border-color: #D0503C;
				background-color: #D0503C;
			}

			&.k-hover {
				background-color: #cacaca;
				border-color: #cacaca;
				color: @blackFontColor;

				>.k-link {
					color: @blackFontColor;
				}
			}
		}
	}

	.k-scheduler-content {

		.k-event,
		.k-task {
			border-color: #ACACAC;
			border-style: solid;
			border-width: 1px;
			border-radius: 0px;
			line-height: @eventLineHeight;
			box-shadow: none;
			color: @blackFontColor;
		}

		.k-event-actions:first-child {
			margin: 0;
		}

		.k-event-actions>.k-icon {

			&.k-i-arrow-e,
			&.k-i-arrow-w {
				display: none;
			}
		}

		.calendar-event {
			position: relative;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			border: 1px solid #ACACAC;
			line-height: 21px;

			.content {
				position: absolute;
				display: flex;
				top: 0px;
				left: 10px;
				right: 0px;

				.title {
					max-width: calc(100% - 52px);
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			&::before {
				content: "\00a0";
				width: 8px;
				display: inline-block;
				margin-right: 2px;
			}

			&.not-editable {

				~.k-event-actions,
				~.k-event-delete {
					display: none;
				}

				~.k-resize-handle {
					display: none;
				}

				&::before {
					background-color: white;
					background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' width='10' height='10'><line x1='6' y1='-1' x2='-1' y2='6' style='stroke:gray;stroke-width:1'/><line x1='11' y1='4' x2='4' y2='11' style='stroke:gray;stroke-width:1'/></svg>");
					background-size: 10px;
					border-right: 1px solid #ACACAC;
				}
			}

			&.school-year {
				~.k-resize-handle {
					display: none;
				}
			}

			&.vacation {

				~.k-resize-handle,
				~.k-event-actions,
				~.k-event-delete {
					filter: brightness(10);
				}
			}

			&.closed {

				~.k-resize-handle,
				~.k-event-actions,
				~.k-event-delete {
					filter: brightness(10);
				}
			}
		}

		.k-task .calendar-event {
			border: none;

			&.not-editable::before {
				border-top: 1px solid #ACACAC;
			}
		}

		.k-scheduler-mark {
			border: 1px solid #ACACAC;
		}
	}

	.k-event-actions,
	.k-event>.k-link,
	.k-task>.k-link {
		top: 0px;
	}

	.k-event {

		.tf-light-fore-color,
		.tf-light-fore-color+span {
			color: white;
		}

		.tf-dark-fore-color {
			color: #2E2E2E;
		}
	}
}

.k-calendar-button-theme {
	opacity: 1;
	color: @k-calendar-button-color;
}

.k-animation-container {
	.k-calendar {
		.k-header .k-link.k-nav-fast {
			font-weight: bold;
		}

		.k-footer .k-nav-today,
		.k-link.k-nav-fast {
			.k-calendar-button-theme();
		}
	}
}