﻿(function()
{
	EsriListItemsViewModel = function(mapViewModel)
	{
		this.items = ko.observableArray();

		this.onClick = null;

		this.add = function(item)
		{
			this.items.push(item);
		}

		this.findSelections = function()
		{
			var returnValue = [];
			for (var i = 0; i < this.items().length; i++)
			{
				if (this.items()[i].selected())
				{
					var model = this.items()[i].model;
					if (model)
					{
						returnValue.push(model);
					}
				}
			}

			return returnValue;
		};

		this.selectedValues = ko.computed(function()
		{
			var returnValue = [];
			for (var i = 0; i < this.items().length; i++)
			{
				if (this.items()[i].selected())
				{
					returnValue.push(this.items()[i].value);
				}
			}
			return returnValue;
		}, this);

		this.values = ko.computed(function()
		{
			var returnValue = [];
			for (var i = 0; i < this.items().length; i++)
			{
				returnValue.push(this.items()[i].value);
			}
			return returnValue;
		}, this);

		this.clearSelections = function()
		{
			for (var i = 0; i < this.items().length; i++)
			{
				if (this.items()[i].selected())
				{
					this.items()[i].selected(false);
				}
			}
		};

		this.itemClicked = function(item, e)
		{
			this.clearSelections();
			item.selected(true);
			this.text = item.text;
			this.color = item.color;

			mapViewModel._updateApproachStopSymbol(this.color);
		}.bind(this);
	};


	EsriListItemsViewModel.ColorBarListItemsViewModel = function(mapViewModel)
	{
		EsriListItemsViewModel.call(this, mapViewModel);
	}

	EsriListItemsViewModel.ImageListItemsViewModel = function()
	{
		ListItemsViewModel.call(this);
	}

	function ListItemArguments()
	{
		this.text = null;
		this.value = null;
	}

	ListItem = function(text, value, selected)
	{
		this.text = text;
		this.value = value;
		this.selected = ko.observable(selected ? true : false);
	}

	ListItem.ColorBarListItem = function(text, value, selected, color)
	{
		var self = this;
		ListItem.call(self, text, value, selected);
		this.color = color;
	}

	ListItem.ImageListItem = function(text, value, selected, image)
	{
		var self = this;
		ListItem.call(self, text, value, selected);
		this.image = image;
	}

	createNamespace("TF.Control").EsriListItemsViewModel = EsriListItemsViewModel;
	createNamespace("TF.Control").ListItem = ListItem;
})();