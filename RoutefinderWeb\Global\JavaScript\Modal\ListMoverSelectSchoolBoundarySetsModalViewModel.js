﻿(function()
{
	createNamespace('TF.Modal').ListMoverSelectSchoolBoundarySetsModalViewModel = ListMoverSelectSchoolBoundarySetsModalViewModel;

	function ListMoverSelectSchoolBoundarySetsModalViewModel(selectedData, options)
	{
		//options.displayCheckbox = false;
		//options.showRemoveColumnButton = true;
		// options.displayAddButton = true;
		// options.mustSelect = false;
		selectedData = selectedData.map(function(item) { return item; });
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverSelectSchoolViewModel = new TF.Control.ListMoverSelectSchoolBoundarySetsViewModel(selectedData, options);
		this.data(this.ListMoverSelectSchoolViewModel);
	}

	ListMoverSelectSchoolBoundarySetsModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectSchoolBoundarySetsModalViewModel.prototype.constructor = ListMoverSelectSchoolBoundarySetsModalViewModel;

	ListMoverSelectSchoolBoundarySetsModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverSelectSchoolViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ListMoverSelectSchoolBoundarySetsModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverSelectSchoolViewModel.cancel().then(function(result)
		{
			// if (result)
			// {
			this.negativeClose(false);
			// }
		}.bind(this));
	};

})();
