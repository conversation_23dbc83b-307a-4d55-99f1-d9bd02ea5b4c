(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.SchoolNonEligibleDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	};

	namespace.SchoolNonEligibleDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.SchoolNonEligibleDataModel.prototype.constructor = namespace.SchoolNonEligibleDataModel;

	namespace.SchoolNonEligibleDataModel.prototype.mapping = [
		{ from: "OBJECTID", default: 0 },
		{ from: "Name", default: "" },
		{ from: "Comments", default: "" },
		{ from: "FilterSpec", default: "" },
		{ from: "FilterName", default: "" },
		{ from: "School", default: "" },
		{ from: "DBID", default: 0 },
		{ from: "Visible", default: 1 },
		{ from: "Color", default: "#ffa850" },
		{ from: "IShow", default: false }
	];

})();
