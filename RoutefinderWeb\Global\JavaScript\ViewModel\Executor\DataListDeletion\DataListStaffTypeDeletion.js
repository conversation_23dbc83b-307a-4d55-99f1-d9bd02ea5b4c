﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListStaffTypeDeletion = DataListStaffTypeDeletion;

	function DataListStaffTypeDeletion()
	{
		namespace.DataListBaseDeletion.apply(this, arguments);
		this.type = 'stafftypes';
		this.deleteType = 'Type';
		this.deleteRecordName = 'Staff Type';
	}

	DataListStaffTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListStaffTypeDeletion.prototype.constructor = DataListStaffTypeDeletion;

	DataListStaffTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var self = this;
		var associatedDatas = [];
		var isSystemDefined = false;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.type, ids[0] + "?@relationships=StaffStaffType")).then(function(response)
		{
			isSystemDefined = response.Items[0].IsSystemDefined;
			response.Items = response.Items[0].StaffStaffTypes;
			associatedDatas.push({
				type: self.type,
				items: response.Items
			});
			if (isSystemDefined)
			{
				const rejectMessage = "<pre  class='titleNotify'>" + "This " + self.deleteRecordName.toLowerCase() + " is system defined.  It cannot be deleted. </pre>";
				const rejectTitle = self.deleteRecordName + " Cannot be Deleted";
				return tf.promiseBootbox.alert(rejectMessage, rejectTitle)
					.then(function()
					{
						self.deleteIds = [];
						return Promise.resolve(false);
					}.bind(self));
			}
			else
			{
				return associatedDatas;
			}
		});
	}

	DataListStaffTypeDeletion.prototype.deleteSelectedItems = function()
	{
		if (this.deleteIds.length === 0)
		{
			return;
		}
		var ids = this.deleteIds,
			singleOrMultiple = ids.length > 1 ? 'multiple' : 'single',
			successMessage = i18n.t('deletion.delete_success_' + singleOrMultiple),
			errorMessage = i18n.t('deletion.delete_failed_' + singleOrMultiple),
			requestData = ids;
		if ($.isFunction(this.getDeletionData))
		{
			requestData = this.getDeletionData();
		}
		return tf.promiseAjax.delete(pathCombine(this.apiPrefix, tf.dataTypeHelper.getEndpoint(this.type)),
			{
				paramData: { "@filter": "in(StaffTypeId," + requestData.join(",") + ")" }
			})
			.then(function()
			{
				this.publishData(ids);
				return ids;
			}.bind(this))
			.catch(function()
			{
			}.bind(this));
	};

	DataListStaffTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

})();