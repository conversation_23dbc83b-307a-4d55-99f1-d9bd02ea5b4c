(function()
{
	createNamespace("TF.Map").CompareMapCanvasTool = CompareMapCanvasTool;

	const TITLE = "Compare Map Canvas";
	const HELP_KEY = "comparemapcanvas";
	const CLOSE_TITLE = "Close Compare Map Canvas";
	const CompareMapSliderId = "compareMapSlider";

	function CompareMapCanvasTool(routingMapTool)
	{
		this.routingMapTool = routingMapTool;
		this.toggleCompareMapMenuClick = this.toggleCompareMapMenuClick.bind(this);
		this.subscribeDocumentChangeEvent();
	}

	/**
	 * build menu on routing map tool
	 */
	CompareMapCanvasTool.prototype.buildMenu = function()
	{
		var subMenus = this.buildCompareToMenu();
		this.menuItem = new TF.RoutingMap.MenuItem({
			helpKey: HELP_KEY,
			header: TITLE,
			icon: 'compareMapCanvas',
			disable: subMenus.length === 0,
			children: subMenus
		});

		this.routingMapTool.rootMenuItem.addChild(this.menuItem);
		this.routingMapTool.routingMapDocumentViewModel.mapLoadedPromise.then(() =>
		{
			this.toggleCompareToMenuStatus(this.routingMapTool.routingMapDocumentViewModel.mapMultiDocumentViewModel, false);
		})
	}

	CompareMapCanvasTool.prototype.openCompareMapCanvasByRouteState = function(routeState)
	{
		this.triggerItemClick(x => x.entityItem.targetRouteState == routeState);
	}

	CompareMapCanvasTool.prototype.toggleCompareMapMenuClick = function(item, allChildren)
	{
		// close previous compare target
		allChildren.forEach(child =>
		{
			var isOtherOnMenu = child.entityItem.targetRouteState != item.targetRouteState && child.entityItem.obShow();
			if (isOtherOnMenu)
			{
				child.entityItem.obShow(false);
				this.clearCompareMap();
			}
		});

		item.obShow(!item.obShow());
		// force close menu when click compare map
		hideRoutingMapTool(this.routingMapTool);

		// close compare if toggle off
		if (!item.obShow())
		{
			this.clearCompareMap();
			return;
		}

		this.openCompareMap(item.targetRouteState);
	}

	CompareMapCanvasTool.prototype.subscribeDocumentChangeEvent = function()
	{
		// watch map document open to change sub context menu
		this.routingMapDocumentsSubscription = tf.documentManagerViewModel.allDocuments.subscribe(() =>
		{
			this.menuItem.children = this.buildCompareToMenu();
			this.menuItem.isDisabled = this.menuItem.children.length === 0;
			var iconButton = this.routingMapTool.$mapToolContainer.find(`div[help-key='${HELP_KEY}']`);
			this.menuItem.isDisabled ? iconButton.addClass("disable") : iconButton.removeClass("disable");
		});

		// update compare map display when user change current document
		this.currentDocumentSubscription = tf.documentManagerViewModel.obCurrentDocument.subscribe(() =>
		{
			var currentDocument = tf.documentManagerViewModel.obCurrentDocument();
			var displayThis = currentDocument && currentDocument.routeState == this.routingMapTool.routingMapDocumentViewModel.routeState;
			if (displayThis && this.currentStatus)
			{
				clearTimeout(this.displayByCurrentStatusSubscription);
				this.displayByCurrentStatusSubscription = setTimeout(() =>
				{
					//use set timeout to make sure other clear job is finished
					this.displayByCurrentStatus();
				}, 100);
			} else
			{
				this.clearCompareMap(false);
			}
		});

		// subscript document visible change, then close routing map tool menu
		this.documentVisibleSubscription = this.routingMapTool.routingMapDocumentViewModel.mapMultiDocumentViewModel.visible.subscribe((visible) =>
		{
			if (!visible && this.routingMapTool.$offMapTool.hasClass("active"))
			{
				hideRoutingMapTool(this.routingMapTool);
			}

			if (!visible && this.currentStatus && this.currentStatus.backDoc && this.currentStatus.backDoc.mapCanvasPage)
			{
				hideRoutingMapTool(this.currentStatus.backDoc.mapCanvasPage.RoutingMapTool);
			}

			if (visible && !this.currentStatus)
			{
				this.clearCompareMap(false);
			}
		})
	}

	function hideRoutingMapTool(routingMapTool)
	{
		if (routingMapTool.$offMapTool.hasClass("active"))
		{
			routingMapTool.toolkitBtnClick();
			routingMapTool.hideSubMenu();
		}
	}

	/**
	 * build compare to sub context menu
	 */
	CompareMapCanvasTool.prototype.buildCompareToMenu = function()
	{
		var self = this;
		var children = [];

		if (tf.isViewfinder) { return []; }
		var mapCanvases = tf.documentManagerViewModel.routingMapDocuments();
		var otherMapCanvas = mapCanvases.filter(x => x.routeState != this.routingMapTool.routingMapDocumentViewModel.routeState);
		var compareToRouteState = this.currentStatus?.compareToRouteState;
		otherMapCanvas.forEach(element =>
		{
			let item = {
				obShow: ko.observable(compareToRouteState === element.routeState),
				targetRouteState: element.routeState
			}

			let menuItem = new TF.RoutingMap.MenuItem({
				header: element.obtabName(),
				children: [],
				isToggled: true,
				disable: false,
				toggleStatus: item.obShow,
				click: function()
				{
					self.toggleCompareMapMenuClick(item, children);
				}
			});
			menuItem.entityItem = item;
			children.push(menuItem);
		});

		return children;
	}

	CompareMapCanvasTool.prototype.openCompareMap = function(compareToRouteState)
	{
		this.setCurrentStatus(compareToRouteState);
		this.displayByCurrentStatus();
	}

	CompareMapCanvasTool.prototype.setCurrentStatus = function(compareToRouteState)
	{
		var self = this;
		if (compareToRouteState == null)
		{
			this.currentStatus = null;
			return;
		}

		var mapCanvases = tf.documentManagerViewModel.routingMapDocuments();
		this.currentStatus = {
			frontDoc: mapCanvases.filter(x => x.routeState == self.routingMapTool.routingMapDocumentViewModel.routeState)[0],
			backDoc: mapCanvases.filter(x => x.routeState == compareToRouteState)[0],
			left: '50%',
			compareToRouteState: compareToRouteState
		}
	}

	CompareMapCanvasTool.prototype.displayByCurrentStatus = function()
	{
		var self = this, $main = $("#main");

		// compare target is closed then clear status
		if (!this.currentStatus.backDoc || !this.currentStatus.backDoc.$element)
		{
			self.clearCompareMap();
			return;
		}

		// clear status first then create new one
		if ($main.children("#" + CompareMapSliderId).length > 0)
		{
			self.clearCompareMap(false);
		}

		var $frontDocWrapper = this.currentStatus.frontDoc.$element.closest(".doc.wrapper"),
			$backDocWrapper = this.currentStatus.backDoc.$element.closest(".doc.wrapper"),
			$frontMap = this.currentStatus.frontDoc.mapCanvasPage.$mapDiv,
			$backMap = this.currentStatus.backDoc.mapCanvasPage.$mapDiv,
			viewFront = this.currentStatus.frontDoc.mapCanvasPage._mapView,
			viewBack = this.currentStatus.backDoc.mapCanvasPage._mapView,
			map = this.currentStatus.frontDoc.mapCanvasPage._map,
			$slider = $(getSliderHtml()),
			backDetailViewVisible = this.currentStatus.backDoc.detailViewVisible(),
			openedFloatModels = [];
		this.currentType = "";
		this.dragging = false;

		this.showDocNameLabel();
		$main.append($slider);

		initDocumentStyle();
		openedFloatModels = getFloatModel();
		setLeft(this.currentStatus.left);
		$slider.css("left", this.currentStatus.left);
		makeSliderDraggable();
		initListenMapExtentChangeEvent();

		function initDocumentStyle()
		{
			$frontDocWrapper.css({ 'clip-path': 'inset(0px)' });
			$backDocWrapper.css({ "visibility": "visible", "display": "block", 'clip-path': 'inset(0px)' });
			viewFront.resizeAlign = 'left';
			viewBack.resizeAlign = 'right';
		}

		function makeSliderDraggable()
		{
			$slider.draggable({
				axis: "x",
				containment: "parent",
				start: function()
				{
					self.dragging = true;
					backDetailViewVisible = self.currentStatus.backDoc.detailViewVisible();
					openedFloatModels = getFloatModel();
				},
				drag: function(event, ui)
				{
					var left = (ui.position.left / $main.width()) * 100 + '%';
					setLeft(left);
				},
				stop: function()
				{
					self.dragging = false;
					$slider.css("left", self.currentStatus.left);
				}
			});
		}

		function initListenMapExtentChangeEvent()
		{
			var backOk = self.currentStatus.backDoc.mapCanvasPage.mapLoadedPromise,
				backUpdateOk = self.currentStatus.backDoc.mapCanvasPage.mapFinishUpdatePromise,
				frontOk = self.currentStatus.frontDoc.mapCanvasPage.mapLoadedPromise,
				frontDocUpdateOk = self.currentStatus.frontDoc.mapCanvasPage.mapFinishUpdatePromise;

			Promise.all([backOk, backUpdateOk, frontOk, frontDocUpdateOk]).then(() =>
			{
				var frontCenter = viewFront.center.clone();
				self.keepCenterTimeout = setTimeout(() =>
				{
					viewBack.zoom = viewFront.zoom;
					viewFront.center = frontCenter;
					keepCenter('front');
					self.toggleCompareToMenuStatus(self.currentStatus.backDoc, true);
					if (self.menuItem.children.length == 1)
					{
						self.toggleCompareToMenuStatus(self.currentStatus.frontDoc, true);
					}
					self.watchHandlerFront = listenEvent(viewFront, viewBack, "front");
					self.watchHandlerBack = listenEvent(viewBack, viewFront, "back");
				}, 200);
			});
		}

		function setLeft(left)
		{
			var right = (100 - parseFloat(left.replace('%', ''))) + '%';
			$frontDocWrapper.css('right', right);
			$backDocWrapper.css('left', left);
			self.currentStatus.left = left;
			backDetailViewVisible && keepCenter('front');
			makeFloatModelNotOverflow();
		}

		function makeFloatModelNotOverflow()
		{
			openedFloatModels.forEach(item =>
			{
				var $item = $(item);
				if ($item.css("left").indexOf("calc") >= 0) { return; }
				var elementWidth = $item.outerWidth();
				if ($item.position().left + elementWidth >= $item.parent().width())
				{
					$item.css('left', `calc(100% - ${elementWidth}px)`);
				}
			});
		}

		function getFloatModel()
		{
			var modalSelector = "div.parcel-edit-modal-container:visible";
			return $frontDocWrapper.find(modalSelector)
				.add($backDocWrapper.find(modalSelector)).toArray();
		}

		// make 2 maps looks like 1 map
		function keepCenter(from)
		{
			var totalWidth = $frontMap.width() + $backMap.width();
			var viewFrom = from == "back" ? viewBack : viewFront;
			var viewTo = from == "back" ? viewFront : viewBack;
			var center = viewFrom.center;
			var point = {
				x: center.x,
				y: center.y,
				spatialReference: map.mapView.spatialReference
			};
			var sp = map.mapView.toScreen(point);
			var newX = (from == "back" ? (- 1) : 1) * (totalWidth / 2);
			var newCenter = map.mapView.toMap({ x: sp.x + newX, y: sp.y });
			viewTo.center = newCenter;
		}

		// listen event to synchronize map extent
		function listenEvent(view, viewTo, type)
		{
			return tf.map.ArcGIS.reactiveUtils.watch(() => view.extent, () =>
			{
				var canSyncExtent = !self.dragging && (self.currentType == "" || self.currentType == type);
				if (!canSyncExtent) { return }

				self.currentType = type;
				viewTo.zoom = view.zoom;
				keepCenter(type);
				clearTimeout(self.currentTypeTimeOut);
				self.currentTypeTimeOut = setTimeout(() =>
				{
					self.currentType = "";
				}, 200);
			});
		}
	}

	/**
	 * show map canvas label on top center
	 */
	CompareMapCanvasTool.prototype.showDocNameLabel = function()
	{
		if (!this.currentStatus)
		{
			return;
		}

		tf.mapCanvasFrontLabel = createLabel(this.currentStatus.frontDoc);
		tf.mapCanvasBackLabel = createLabel(this.currentStatus.backDoc);

		function createLabel(doc)
		{
			var $label = $("<div class='map-label-container'><div class='map-title'></div></div>");
			$label.find(".map-title").text(doc.obtabName());
			doc.mapCanvasPage.element.append($label);
			return $label;
		}
	}

	/**
	 * hide map canvas label
	 */
	CompareMapCanvasTool.prototype.hideDocNameLabel = function()
	{
		if (tf.mapCanvasFrontLabel)
		{
			tf.mapCanvasFrontLabel.remove();
			tf.mapCanvasBackLabel.remove();
		}
	}

	/**
	 * clear compare map canvas status
	 */
	CompareMapCanvasTool.prototype.clearCompareMap = function(clearStatus = true)
	{
		this.hideDocNameLabel();
		this.revertUIStatus(this.currentStatus?.backDoc);
		this.revertUIStatus(tf.documentManagerViewModel.routingMapDocuments().filter(x => x.routeState == this.routingMapTool.routingMapDocumentViewModel.routeState)[0]);
		$("#" + CompareMapSliderId).remove();
		this.disableSyncEvent();
		clearStatus && this.setCurrentStatus(null);
	};

	CompareMapCanvasTool.prototype.revertUIStatus = function(doc)
	{
		if (!doc || !doc.$element) { return }

		var $docWrapper = doc.$element.closest(".doc.wrapper");
		$docWrapper.css({ 'right': 0, 'left': 0, 'clip-path': 'none' });
		if (!doc.visible())
		{
			$docWrapper.css({ "visibility": "hidden", "display": "none" });
		}
		this.toggleCompareToMenuStatus(doc, false);

		var mapView = doc.mapCanvasPage?._mapView;
		if (mapView)
		{
			mapView.resizeAlign = 'center';
		}

		if (doc.mapCanvasPage?.RoutingMapTool)
		{
			hideRoutingMapTool(doc.mapCanvasPage?.RoutingMapTool);
		}
	}

	/**
	 * disable listen synchronize map extent change event
	 */
	CompareMapCanvasTool.prototype.disableSyncEvent = function()
	{
		this.watchHandlerFront && this.watchHandlerFront.remove();
		this.watchHandlerBack && this.watchHandlerBack.remove();
	}

	/**
	 * toggle compare to menu status for background map
	 */
	CompareMapCanvasTool.prototype.toggleCompareToMenuStatus = function(doc, disable)
	{
		var $mapToolContainer = doc.mapCanvasPage.RoutingMapTool.$mapToolContainer;
		var icons = $mapToolContainer.find("div.tool-icon");
		var icon = icons.filter('.compareMapCanvas');
		var index = icons.index(icon);
		$mapToolContainer.find("div.map-tool-label>label").eq(index).text(disable ? CLOSE_TITLE : TITLE);
		disable ? icon.addClass("force-disable") : icon.removeClass("force-disable");
		let subMenu = doc.mapCanvasPage.RoutingMapTool.rootMenuItem.children.filter(x => x.header == TITLE)[0];
		// only 2 map canvas, use can directly open compare map without show submenu
		let isOnlyOneChild = subMenu.children.length === 1;
		subMenu.isForceDisable = disable || isOnlyOneChild;
		icon.off("click.compareToggleStatus")

		if (!subMenu.isForceDisable) { return }
		icon.on("click.compareToggleStatus", () =>
		{
			this.triggerItemClick(x => x.toggleStatus() || isOnlyOneChild);
			hideRoutingMapTool(doc.mapCanvasPage.RoutingMapTool);
		});
	}

	CompareMapCanvasTool.prototype.triggerItemClick = function(filter)
	{
		var activeItem = this.routingMapTool.rootMenuItem.children.filter(x => x.header == TITLE)[0].children.filter(filter);
		if (activeItem && activeItem.length == 1)
		{
			activeItem[0].onclick();
		}
	}

	function getSliderHtml()
	{
		return `
			<div id='${CompareMapSliderId}' tabindex="0" touch-action="none" role="slider" title="drag to compare" class="esri-swipe__container esri-swipe--horizontal" 
				style="left: 50%; top:0;bottom: 28px;z-index:1000;margin:0 -2px; cursor: col-resize;width:4px;overflow:visible;">
				<div role="presentation" class="esri-swipe__divider" style="margin:0;left:0;"></div>
				<div role="presentation" class="esri-swipe__handle" style="margin-left:-13px;top: 80%;">
					<span aria-hidden="true" class="esri-swipe__handle-icon esri-icon-drag-vertical"></span>
				</div>
			</div>
		`;
	}

	CompareMapCanvasTool.prototype.dispose = function()
	{
		this.clearCompareMap();
		clearTimeout(this.setSliderLeftTimeout);
		clearTimeout(this.currentTypeTimeOut);
		clearTimeout(this.keepCenterTimeout);
		clearTimeout(this.displayByCurrentStatusSubscription);
		this.currentDocumentSubscription && this.currentDocumentSubscription.dispose();
		this.routingMapDocumentsSubscription && this.routingMapDocumentsSubscription.dispose();
		this.documentVisibleSubscription && this.documentVisibleSubscription.dispose();
		tfdispose(this);
	};

})();