(function()
{
	createNamespace("TF.Control").TripDetailViewModel = TripDetailViewModel;

	function TripDetailViewModel(student, tripId, tripStopDetailInfo)
	{
		this.student = student;
		this.tripId = tripId;
		this.tripStopDetailInfo = tripStopDetailInfo;
		this.obTrip = ko.observable({});
		this.loadData();
	}

	TripDetailViewModel.prototype.loadData = function()
	{
		var self = this;
		Promise.all([this.loadTrip(), this.loadStudent()]).then(function(data)
		{
			var student = data[1];
			var trip = data[0];
			const currentUnitOfMeasure = tf.measurementUnitConverter.getCurrentUnitOfMeasure();
			trip.distWTS = student.DistanceFromAMStop > 0 ? tf.measurementUnitConverter.convert({
				originalUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
				targetUnit: currentUnitOfMeasure,
				value: student.DistanceFromPmStop,
			}) : "";
			if (trip.Session == 0)
			{
				trip.rideTime = student.AmRideTime || "";
			} else
			{
				trip.rideTime = student.PmRideTime || "";
			}
			self.obTrip(data[0]);
		});
	};

	TripDetailViewModel.prototype.loadTrip = function()
	{
		var self = this;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("trip")) + "?id=" + this.tripId + "&@relationships=TripStop,Vehicle,Driver,BusAide").then(function(data)
		{
			var trip = data.Items[0];
			trip.Stop = self.tripStopDetailInfo;//trip.TripStops[0];
			trip.StartTime = self.tripStopDetailInfo.tripInfo.startTime;
			trip.FinishTime = self.tripStopDetailInfo.tripInfo.finishTime;
			trip.TimeRange = !trip.StartTime && !trip.FinishTime ? "All" : (!trip.StartTime ? ("Until " + trip.FinishTime) :
				!trip.FinishTime ? ("Starting " + trip.StartTime) : (trip.StartTime + " - " + trip.FinishTime));
			return trip;
		});
	};

	TripDetailViewModel.prototype.loadStudent = function()
	{
		var url = pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint("student"));
		var filterData = {
			fields: ["Id", "DistanceFromAMStop", "AmRideTime", "DistanceFromPmStop", "PmRideTime"],
			idFilter: { IncludeOnly: [this.student.Id] },
		};
		return tf.promiseAjax.post(url, {
			data: filterData
		}, { overlay: false }).then(function(res)
		{
			var student = res.Items[0];
			return student;
		});
	};
})();