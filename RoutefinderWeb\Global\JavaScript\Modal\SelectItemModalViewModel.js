(function()
{
	createNamespace("TF.Modal").SelectItemModalViewModel = SelectItemModalViewModel;

	function SelectItemModalViewModel(sources, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		var defaults = { multiple: false, title: "", description: "" };
		options = $.extend(defaults, options);
		this.title(options.title);
		this.description = ko.observable(options.description);
		this.sizeCss = "modal-dialog-sm";
		this.contentTemplate("Modal/SelectItem");
		this.buttonTemplate("modal/PositiveNegative");
		this.obPositiveButtonLabel("Apply");
		this.obNegativeButtonLabel("Cancel");
		this.viewModel = new TF.Control.SelectItemViewModel(sources, options);
		this.data(this.viewModel);
	}

	SelectItemModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SelectItemModalViewModel.prototype.constructor = SelectItemModalViewModel;

	SelectItemModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	SelectItemModalViewModel.prototype.negativeClick = function()
	{
		var self = this;
		this.viewModel.cancel().then(function(result)
		{
			if (result)
			{
				self.hide();
				self.resolve();
			}
		});
	};

})();