(function()
{
	createNamespace('TF.Control').TFXFileGirdViewModel = TFXFileGirdViewModel;

	function TFXFileGirdViewModel(options)
	{
		var self = this;
		self.convertedTfxFileName = options.convertedTfxFileName;
		self.tfdFields = options.tfdFields;
		self.tfdRows = options.tfdRows;
		self.count = options.tfdRows ? options.tfdRows.length : 0;

		//unbind the enter key avoid to close the modal
		setTimeout(() =>
		{
			tf.shortCutKeys.unbind("enter", Math.random().toString(36).substring(7));
		});
	};

	/**
	 * Initialize the tfx file grid modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	TFXFileGirdViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.$element = $(el);
		self.$grid = self.$element.find(".tfxfile-container");

		self.initGrid();
	};

	/**
	 * Initialize the grid.
	 * @return {void}
	 */
	TFXFileGirdViewModel.prototype.initGrid = function()
	{
		var self = this;
		this.columns = this.gridDefinition().Columns;

		this.searchGrid = new TF.Grid.LightKendoGrid(this.$element.find(".tfxfile-container"),
			{
				gridDefinition:
				{
					Columns: this.columns
				},
				kendoGridOption: {
					dataSource: {
						serverPaging: false,
						pageSize: 200
					},
					sortable: false,
					filterable: false,
				},
				selectable: 'row',
				showOmittedCount: false,
				showSelectedCount: false,
				gridType: "student",
				isSmallGrid: true,
				url: TF.Helper.ApiUrlHelper.postImportedTFXDataGetByFileNameUrl(self),
				showBulkMenu: false,
				showLockedColumn: false,
				setRequestOption: function(options)
				{
					var filter = { FieldName: "ShowMapped", Operator: "EqualTo", Value: "False" };
					if (!options.data.filterSet)
					{
						options.data.filterSet = {
							FilterItems: [],
							FilterSets: null,
							LogicalOperator: 'and'
						}
					}
					options.data.filterSet.FilterItems.push(filter);
					return options;
				}
			});
	};

	TFXFileGirdViewModel.prototype.gridDefinition = function(tfdField)
	{
		function convertType(tfdField)
		{
			var dbType = tfdField.FieldType;
			var type = "string";
			switch (dbType)
			{
				case "System.DateTime":
				case "System.Nullable`1[System.DateTime]":
					if (tfdField.FieldLength === 10)
						type = "date"
					break;
				case "System.Decimal":
				case "System.Nullable`1[System.Decimal]":
					type = "float"
					break
				case "System.Boolean":
				case "System.Nullable`1[System.Boolean]":
					type = "boolean"
					break
				case "System.String":
				default:
					break;
			}
			return type;
		}

		let ColumnsDefinition = [];
		ColumnsDefinition = this.tfdFields.map(function(tfdField)
		{
			let primaryKeyMarker = tfdField.IsPrimaryKey === true ? "*" : "";

			if (tfdField.IsContactField) // Field of Contact relationship entity
			{
				if (tfdField.IsUdfOfSubEntity)
				{
					return {
						FieldName: tfdField.Guid,
						DisplayName: `${primaryKeyMarker}Contact${tfdField.ContactIndex} ${tfdField.DBFieldName}`,
						Width: "150px",
						type: convertType(tfdField),
						template: function(FieldName, dataItem)
						{
							let ctIndex = this.ContactIndex - 1; // ContactIndex is 1-based
							let udfDisplayName = this.DBFieldName;
							if (dataItem.Contacts && dataItem.Contacts.length > ctIndex)
							{
								return dataItem.Contacts[ctIndex].UDFValuesDict[udfDisplayName];
							}

							return "";
						}.bind(tfdField, tfdField.DBFieldName)
					};
				}
				else
				{
					return {
						FieldName: `Contact${tfdField.ContactIndex}_${tfdField.DBFieldName}`,
						DisplayName: `${primaryKeyMarker}Contact${tfdField.ContactIndex} ${tfdField.DBFieldName}`,
						Width: "150px",
						type: convertType(tfdField),
						template: function(FieldName, dataItem)
						{
							let ctIndex = this.ContactIndex - 1; // ContactIndex is 1-based
							if (dataItem.Contacts && dataItem.Contacts.length > ctIndex)
							{
								return dataItem.Contacts[ctIndex][FieldName];
							}

							return "";
						}.bind(tfdField, tfdField.DBFieldName)
					};
				}
			}
			else // Field of main Student entity
			{
				if (tfdField.IsUdf)
				{
					return {
						FieldName: tfdField.Guid,
						DisplayName: `${primaryKeyMarker}${tfdField.DBFieldName}`,
						Width: "150px",
						type: convertType(tfdField),
						template: function(FieldName, dataItem)
						{
							let udfValue = dataItem.UDFValuesDict[FieldName] ? dataItem.UDFValuesDict[FieldName] : "";
							return udfValue;
						}.bind(null, tfdField.DBFieldName)
					};
				}
				else
				{
					return {
						FieldName: tfdField.DBFieldName,
						DisplayName: `${primaryKeyMarker}${tfdField.DBFieldName}`,
						Width: "150px",
						type: convertType(tfdField),
					};
				}
			}


		});

		return {
			'Columns': ColumnsDefinition
		}
	};

	TFXFileGirdViewModel.prototype.getStudentColumn = function()
	{
		var studentColumns = tf.studentGridDefinition.gridDefinition().Columns;
		var ColumnsDefinition = this.tfdFields.map((current) =>
		{
			var field = studentColumns.filter(item =>
			{
				return item.DBName && item.DBName.toLowerCase() === current.DBFieldName.toLowerCase()
			});
			var type = field.length > 0 ? field[0].type : "string";
			var column = {
				FieldName: current.DBFieldName,
				DisplayName: field.length > 0 ? field[0].DisplayName : current.DBFieldName.replace(/_/g, " ").replace(/\b(\w)|\s(\w)/g, item => item.toUpperCase()),
				Width: '150px',
				type: type
			};
			setColumnFilterableCell(type, column);
			return column;
		});
		return ColumnsDefinition;
	}

	function setColumnFilterableCell(type, column)
	{
		switch (type)
		{
			case "string":
				column.filterable = {
					cell: {
						operator: "contains",
					}
				};
				break;
			case "number":
			case "integer":
			case "date":
			case "time":
			case "datetime":
			case "boolean":
				column.filterable = {
					cell: {
						operator: "eq",
					}
				}
				break;
		}
	}

	function filterData(list, filterObj)
	{
		let filterList = list;
		if (filterObj.logic === 'and')
		{
			filterList = and(filterObj, filterList);
		} else if (filterObj.logic === 'or')
		{
			filterList = or(filterObj, []);
		}
		return filterList;

		function and(filterObj, filterList)
		{
			for (let i = 0; i < filterObj.filters.length; i++)
			{
				let filter = filterObj.filters[i];
				if (filter.logic === 'or')
				{
					filterList = or(filterObj.filters[i], []);
				} else if (filter.logic === 'and')
				{
					filterList = and(filterObj.filters[i], filterList);
				} else
				{
					let operator = filter.operator;
					let filterValue = filter.value.toLowerCase();
					if (operator === 'eq') filterList = filterList.filter(item => item[filter.field].toLowerCase() === filterValue);
					if (operator === 'neq') filterList = filterList.filter(item => item[filter.field].toLowerCase() != filterValue);
					if (operator === 'contains') filterList = filterList.filter(item => item[filter.field].toLowerCase().includes(filterValue));
					if (operator === 'doesnotcontain') filterList = filterList.filter(item => !item[filter.field].toLowerCase().includes(filterValue));
					if (operator === 'startswith') filterList = filterList.filter(item => item[filter.field].toLowerCase().substr(0, filter.value.length) === filterValue);
					if (operator === 'endswith') filterList = filterList.filter(item => item[filter.field].toLowerCase().substr(item[filter.field].length - filter.value.length, filter.value.length) === filterValue);
					if (operator === 'isempty') filterList = filterList.filter(item => item[filter.field].toLowerCase() === '');
					if (operator === 'isnotempty') filterList = filterList.filter(item => item[filter.field].toLowerCase() != '');
				}
			}
			return filterList;
		}
		function or(filterObj, filterList)
		{
			for (let i = 0; i < filterObj.filters.length; i++)
			{
				let filter = filterObj.filters[i];
				if (filter.logic === 'or')
				{
					filterList = or(filterObj.filters[i], []);
				} else if (filter.logic === 'and')
				{
					filterList = and(filterObj.filters[i], filterList);
				} else
				{
					let filter = filterObj.filters[i];
					let operator = filter.operator;
					let filterValue = filter.value ? filter.value.toLowerCase() : '';
					if (operator === 'eq') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase() === filterValue));
					if (operator === 'neq') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase() != filterValue));
					if (operator === 'contains') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase().includes(filterValue)));
					if (operator === 'doesnotcontain') filterList = filterList.concat(list.filter(item => !item[filter.field].toLowerCase().includes(filterValue)));
					if (operator === 'startswith') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase().substr(0, filter.value.length) === filterValue));
					if (operator === 'endswith') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase().substr(item[filter.field].length - filter.value.length, filter.value.length) === filterValue));
					if (operator === 'isempty') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase() === ''));
					if (operator === 'isnotempty') filterList = filterList.concat(list.filter(item => item[filter.field].toLowerCase() != ''));
				}
			}
			return filterList;
		}
	}

})();

