﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.EsriRoutingDataModel = function(routingEntity)
	{
		namespace.BaseDataModel.call(this, routingEntity);
	};

	namespace.EsriRoutingDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.EsriRoutingDataModel.prototype.constructor = namespace.EsriRoutingDataModel;

	namespace.EsriRoutingDataModel.prototype.mapping = [
		{ from: "StartTime", default: (new Date()).toLocaleString("en-US") },
		{ from: "ReturnToStart", default: true },
		{ from: "ConsiderBarrier", default: false },
		{ from: "MaxVehicles", default: 1 },
		{ from: "MaxStopsPerVehicle", default: 0 },
		{ from: "TravelMode", default: "" }
	];
})();