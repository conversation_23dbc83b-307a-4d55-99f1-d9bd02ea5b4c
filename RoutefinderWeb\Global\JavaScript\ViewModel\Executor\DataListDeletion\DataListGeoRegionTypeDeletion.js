﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListGeoRegionTypeDeletion = DataListGeoRegionTypeDeletion;

	function DataListGeoRegionTypeDeletion()
	{
		this.type = 'georegiontype';
		this.deleteType = 'Geo Region Type';
		this.deleteRecordName = 'Geo Region Type';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListGeoRegionTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListGeoRegionTypeDeletion.prototype.constructor = DataListGeoRegionTypeDeletion;

	DataListGeoRegionTypeDeletion.prototype.getAssociatedData = function()
	{
		return Promise.resolve([]);
	};

	DataListGeoRegionTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

	DataListGeoRegionTypeDeletion.prototype.deleteSelectedItems = function()
	{
		return TF.DetailView.UserDefinedFieldHelper.checkInBoundarySetUsage(TF.Enums.UDFBoundarySetType.GeoRegionType, this.item.Name).then((result) =>
		{
			if (result)
			{
				return namespace.DataListBaseDeletion.prototype.deleteSelectedItems.apply(this, arguments);
			}
			return false;
		})
	}
})();