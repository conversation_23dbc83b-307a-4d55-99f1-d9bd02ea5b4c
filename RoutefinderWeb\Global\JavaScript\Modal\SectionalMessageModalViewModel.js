﻿(function()
{
	createNamespace("TF.Modal").SectionalMessageModalViewModel = SectionalMessageModalViewModel;

	function SectionalMessageModalViewModel(messages, title, width)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title(title);
		this.sizeCss = "modal-dialog-md";
		if (width)
		{
			this.modalWidth = width + "px";
		}
		this.obPositiveButtonLabel("OK");
		this.contentTemplate('modal/sectionalmessagecontrol');
		this.buttonTemplate('modal/positive');
		this.viewModel = new TF.Control.SectionalMessageViewModel(this, messages);
		this.data(this.viewModel);
	}

	SectionalMessageModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SectionalMessageModalViewModel.prototype.constructor = SectionalMessageModalViewModel;

})();