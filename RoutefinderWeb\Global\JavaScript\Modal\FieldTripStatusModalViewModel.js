﻿(function()
{
	createNamespace('TF.Modal').FieldTripStatusModalViewModel = FieldTripStatusModalViewModel;

	function FieldTripStatusModalViewModel(stageDataModel, selectStage, fieldtripId)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/fieldtripstatuscontrol');
		this.buttonTemplate('modal/positivenegative');
		this.fieldTripStatusViewModel = new TF.Control.FieldTripStatusViewModel(stageDataModel, selectStage, fieldtripId);
		this.data(this.fieldTripStatusViewModel);
		this.sizeCss = "modal-dialog-sm";
		this.obPositiveButtonLabel("Apply");

		this.title("Edit Field Trip Status");
		this.containerLoaded = ko.observable(false);
	}

	FieldTripStatusModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	FieldTripStatusModalViewModel.prototype.constructor = FieldTripStatusModalViewModel;

	FieldTripStatusModalViewModel.prototype.positiveClick = function()
	{
		this.fieldTripStatusViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	FieldTripStatusModalViewModel.prototype.dispose = function()
	{
		this.fieldTripStatusViewModel.dispose();
	};

})();
