@systemColor: #D0503C;
@buttonColor: #333333;
@editableTextColor: #333333;
@editableTextBorderColor: #c5c5c5;

.main-body .form-page {
	background-color: #f9f9f9;

	.page-header {
		height: 56px;
		border-bottom: 2px solid #f2f2f2;
		padding: 0;
		font-family: SourceSansPro-SemiBold;
		font-weight: 700;
		margin: 0 10px;

		.page-title {
			letter-spacing: 3px;
			font-size: 26px;
			color: #262626;
			line-height: 54px;
			white-space: nowrap;
			height: 54px;
			text-transform: uppercase;
			float: left;
		}

		.view-option {
			float: right;
			padding: 20px 0;
			width: 120px;

			.k-input {
				height: 24px;

				.view-option-text {
					display: none;
				}

				.view-option-icon {
					float: right;
				}
			}

			&>span.k-dropdown,
			&>span.k-dropdown>span.k-dropdown-wrap {
				background-color: transparent;
			}

			&>span>span>span.k-select {
				top: -0.2em
			}

			.view-option-icon {
				width: 16px;
				height: 16px;
				background-size: contain;
				margin: 2px 5px;
				float: left;
			}

			.view-option-text {
				float: left;
				margin: 2px 0;
			}

			.view-option-list {

				.view-option-icon {
					background-image: url(../Img/Icons/list.svg);
				}
			}

			.view-option-tiles {

				.view-option-icon {
					background-image: url(../Img/Icons/tiles.svg);
				}
			}

			&>span.k-dropdown>span.k-dropdown-wrap {
				border-style: none;
			}
		}
	}

	.forms-container {
		overflow-y: auto;
		width: 100%;

		.no-forms {
			height: 100%;
			margin: 0 20px;

			.message {
				font-size: 16px;
				height: 30px;
			}

			.logo-icon {
				background-image: url('../Img/formfinderlogin.png');
				background-position: center;
				background-repeat: no-repeat;
				background-size: initial;
				height: calc(~"100% - 40px");
				width: 100%;
			}
		}

		.k-cards {
			margin: 10px 30px;
			padding: 5px 0;
			border-bottom: 2px solid #f2f2f2;

			.cards-title {
				font-weight: bold;
				margin: 0 0 0 -20px;
			}

			.k-card-deck {
				box-sizing: border-box;
				overflow: hidden;
				margin-bottom: -10px;

				.card-container {
					display: flex;
					flex-direction: row;
					flex-wrap: nowrap;
					align-items: stretch;
					flex: 0 0 auto;
				}

				.k-card {
					width: 250px;
					height: 200px;
					margin: 10px;
					position: relative;
					cursor: pointer;

					&:hover {
						box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
						transform: scale(1.05);

						.k-card-favorite-btn {
							display: block;
						}
					}

					.k-card-head {
						height: 100px;
						border-bottom: 1px solid #bfbfbf;

						img {
							height: 100px;
						}
					}

					.k-card-body {
						height: 150px;
						padding: 0;
						margin: 4px;
						overflow: hidden;

						.k-card-title {
							text-align: center;
							font-weight: bold;
						}

						.k-card-subtitle {
							overflow: hidden;
							text-overflow: ellipsis;
							margin-top: 0;
						}
					}

					.k-card-favorite-btn {
						display: none;
						position: absolute;
						right: 10px;
						bottom: 10px;
						width: 22px;
						height: 22px;
						background-size: 22px;
					}

					.k-card-favorite-btn,
					.k-card-favorite-btn-selected:hover {
						background-image: url('../img/card/empty-star.svg');
					}

					.k-card-favorite-btn-selected,
					.k-card-favorite-btn:hover {
						background-image: url('../img/card/star.png');
					}
				}
			}

			.k-card-deck-scrollwrap {
				margin-left: -32px;
				margin-right: -32px;
				padding-left: 32px;
				padding-right: 32px;

				>.k-button,
				>.k-button:focus,
				>.k-button:active {
					background: none;
					border: 0;
					box-shadow: none;

					.k-icon {
						font-size: 24px;
					}
				}
			}

			.k-card-deck-scrollwrap>.k-button {
				&.left-scroll-button {
					left: 10px;
				}

				&.right-scroll-button {
					right: 10px;
				}
			}

			&.view-option-list {

				.k-card-deck {
					.card-container {
						flex-direction: column;
						width: 100%;

						.k-card {
							width: 100%;
							height: 40px;
							margin: 0;
							background-color: #f9f9f9;
							border-radius: 0;

							&:hover {
								transform: scale(1);
								background-color: #fff;
							}

							.k-card-list {
								display: flex;
								flex-direction: row;
								flex-wrap: nowrap;
								padding-right: 40px;
								position: relative;

								.k-card-list-item {
									margin: 0 5px;
									line-height: 40px;
									text-overflow: ellipsis;
									min-width: 0;

									&.k-card-list-item-icon {
										width: 30px;

										img {
											width: 30px;
											height: 30px;
										}
									}

									&.k-card-list-item-name {
										width: 300px;
									}

									&.k-card-list-item-fav {
										width: 24px;
										height: 24px;
										position: absolute;
										right: 0;
										top: 50%;
										transform: translateY(-50%);

										.k-card-favorite-btn {
											position: unset;
										}
									}
								}
							}
						}
					}
				}

				.k-button-scroll {
					display: none;
				}
			}
		}
	}
}

.k-mobile .main-body .form-page .forms-container {
	.k-cards {
		margin: 10px;

		.cards-title {
			margin: 0;
		}

		.k-card-deck {
			overflow-x: auto;

			.k-card {
				width: 150px;
				height: 150px;

				&:hover {
					box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
					transform: scale(1);
				}

				.k-card-head {
					height: 60px;

					img {
						height: 60px;
					}
				}

				.k-card-favorite-btn {
					display: block;
				}

				.k-card-body {
					height: 90px;

					.k-card-title {
						font-size: 14px;
					}

					.k-card-subtitle {
						font-size: 12px;
					}
				}
			}
		}

		&.view-option-list {
			.k-card-deck {
				.k-card {
					.k-card-list {
						.k-card-list-item {
							&.k-card-list-item-name {
								width: 200px;
							}

							&.k-card-list-item-fav {
								display: block;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width:767px) {
	.k-mobile .main-body .form-page .forms-container {
		.k-cards {
			&.view-option-list {
				.k-card-deck {
					.k-card {
						.k-card-list {
							.k-card-list-item {
								&.k-card-list-item-desc {
									display: none;
								}
							}
						}
					}
				}
			}
		}
	}
}

//Form(ImageQuestionCropper)
.cropscreen,
.preview-video,
.pinmapscreen {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: black;
	z-index: 99999;
}

.cropscreen .canvas-container {
	position: absolute;
	top: 40px;
	left: 0;
	right: 0;
	bottom: 55px;
}

.cropscreen .canvas-container canvas {
	max-width: 100%;
	max-height: 100%;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
}

.cropscreen .row.hint,
.preview-video .row.hint {
	position: absolute;
	top: 5px;
	left: 0;
	right: 0;
	color: white;
	font-size: 18px;
	text-align: center;
}

.cropscreen .row.imgbutton {
	position: absolute;
	bottom: 65px;
	left: 0;
	right: 0;
	color: white;
	font-size: 18px;
	text-align: center;
	cursor: pointer;
	height: 20px;
	line-height: 20px;
}

.cropscreen .row.button,
.preview-video .row.button {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	color: white;
	font-size: 18px;
	text-align: center;
	cursor: pointer;
	height: 55px;
	line-height: 55px;
}

.cropscreen canvas {
	display: none;
}

//Form(QRVideoSteam)
.video-stream-container {
	height: 100%;
	width: 986px;
	left: calc(50% - 493px);
	position: fixed;
	z-index: 50000;
	top: 0;
	bottom: 0;
	line-height: 0;
	display: none;

	.scan-tips {
		width: 210px;
		height: 60px;
		line-height: 60px;
		text-align: center;
		background-color: rgba(0, 0, 0, 1);
		color: white;
		font-size: 20px;
		position: absolute;
		top: 10%;
		left: calc(50% - 105px);
		border-radius: 15px;
		opacity: 0.7;
	}

	.scan-icon {
		display: block;
		position: absolute;
		background-image: url("../../Global/Img/Icons/scanner.png");
		background-color: transparent;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
	}

	.scan-close-button {
		display: block;
		position: absolute;
		border: 0;
		width: 30px;
		height: 30px;
		top: 15px;
		right: 15px;
		background-image: url("../../Global/Img/Icons/Close-White.svg");
		background-color: transparent;
		background-size: 30px 30px;
		background-repeat: no-repeat;
		background-position: center;
		z-index: 9999;
		cursor: pointer;
	}

	video {
		width: 100%;
		height: 100%;
		object-fit: contain;
		background-color: black;
	}
}

//Form(Question)
.form-container {
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	background-color: white;

	.form-layer {
		height: 100%;

		.form {
			width: 100%;
			font-family: SourceSansPro-SemiBold;
			font-size: 14px;

			.form-header {
				height: 125px;
				color: #FFFFFF;
				padding: 20px 30px;
				position: absolute;
				left: -15px;
				right: -15px;
				top: 0;
				margin-top: -15px;
				transition: height 300ms;

				.form-title {
					font-size: 30px;
					padding: 10px 0;
				}

				.form-print {
					position: absolute;
					right: 20px;
					top: 40px;
					width: 28px;
					height: 28px;
					background-image: url(../../global/img/detail-screen/print.svg);
					background-size: 24px;
					filter: invert(100%);

					&:focus {
						outline: 1px solid #000;
					}
				}

				.form-subtitle {
					font-size: 16px;
					line-height: 22px;
					overflow: hidden;
					display: -moz-box;
					display: -webkit-box;
					line-clamp: 1;
					-webkit-line-clamp: 1;
					box-orient: vertical;
					-webkit-box-orient: vertical;
					transition: height 300ms display 300ms;

					&.form-subtitle-mini {
						line-clamp: 1;
						-webkit-line-clamp: 1;
					}
				}

				.show-more {
					position: absolute;
					bottom: 2px;
					font-size: 14px;
					left: 50%;
					transform: translateX(-50%);
					color: rgb(255, 255, 255);
					cursor: pointer;
					transition: transform 300ms;
					display: none;

					&.rotate {
						transform: translateX(-50%) rotate(180deg);
					}
				}
			}

			.form-body {
				background-color: white;
				overflow-y: auto;
				height: initial;
				position: absolute;
				top: 110px;
				left: -15px;
				right: -15px;
				bottom: 0;
				transition: top 300ms;

				.external-id {
					display: flex;
					position: absolute;
					top: 5px;
					right: 5px;
					padding-right: 5px;

					span.id {
						max-width: 85px;
						display: inline-block;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}

				input[type=radio],
				input[type=checkbox] {
					position: initial;
				}

				.number-question.k-numerictextbox {
					height: 30px;

					.k-input-spinner button {
						min-height: 13px;
					}

					.k-numeric-wrap {
						height: 30px;
						background-color: transparent;

						&::before {
							display: none;
						}

						input.k-input {
							color: @editableTextColor
						}

						span.k-select {
							display: flex;
							flex-direction: column;
							justify-content: center;
							background-color: transparent;
						}
					}
				}

				.form-entity-container {
					padding: 30px 30px 40px 30px;
					border-bottom: 2px solid #f2f2f2;

					.form-entity-title {
						padding: 10px;
					}

					span.form-entity-input {
						display: inline-block;
						overflow: inherit;

						&:focus-within {
							box-shadow: none;
						}
					}

					input.form-entity-input {
						padding-left: 20px;
					}

					.form-entity-input {
						border: 0;
						width: 452px;
						padding-right: 20px;
						height: 28px;

						&.k-focus {
							box-shadow: none;
						}

						&.k-hover,
						&.k-focus {
							.k-clear-value {
								top: 12px;
								display: block;
							}
						}

						.current-filter-label {
							position: absolute;
							display: none;
							width: 190px;

							.filter-label {
								position: absolute;
								padding-top: 10px;
								font-family: "SourceSansPro-Regular", Arial;

								.filter-type-label {
									font-family: "SourceSansPro-SemiBold";
								}
							}

							.k-clear-value {
								right: -258px;
								top: 10px;
								cursor: pointer;
								position: absolute;
							}
						}

						&.filter-input {
							padding-right: 40px;
							z-index: auto;

							.k-clear-value:not(.k-icon) {
								right: calc(1em + 10px);
								top: 12px;
							}

							.k-icon.tf-filter {
								display: inline-block;
								background-image: url("../../Global/Img/Icons/icon-Adjust.svg");
								background-color: transparent;
								background-size: 16px 16px;
								width: 16px;
								height: 16px;
								background-repeat: no-repeat;
								background-position: center;
								position: absolute;
								cursor: pointer;
								top: 5px;
								right: 5px;

								&.filter-applied {
									background-image: url("../../Global/Img/Icons/icon-Adjust-Selected.svg");
								}
							}

							.filter-disabled {
								//display: none !important;
								cursor: default !important;
							}

						}
					}

					.form-entity-input.k-input {
						border: 1px solid #c5c5c5;
					}

					.k-disabled .form-entity-input.k-input {
						padding-left: 0;
					}

					.k-clear-value {
						right: -50px;
						outline-color: transparent;
					}

					.form-entity-input .k-clear-value:not(.filter-close) {
						right: 4px;
						padding-left: 0px !important;
					}

					.k-icon.k-i-search {
						left: 5px;
						top: 50%;
						margin: -8px 0 0;
						position: absolute;
					}

					.multi-records {
						width: 480px;
						display: inline-flex;

						.form-entity-title {
							display: table-cell;
						}

						.form-entity-input {
							border: 1px solid #ccc;
							padding-left: 10px;
						}

						.input-group-btn .btn {
							height: 28px;
							width: 23px;
							min-width: 23px;
						}

						.k-clear-value {
							top: 3px;
							right: 36px;
							padding-left: 0px !important;
							outline: 0;
							font-size: 100%;
							display: inline-block;
							position: absolute;
						}
					}
				}

				.form-section {
					&.form-section-title {
						padding: 20px 30px 20px 30px;
						background-color: #f2f2f2;
						word-wrap: break-word;
						white-space: normal;
						overflow: hidden;

						.form-entity-title {
							font-size: 16px;
							font-weight: bold;
							color: #000000;
						}
					}

					&.form-section-mobile-title {
						padding: 15px 30px 10px 30px !important;
						background-color: #f2f2f2;
					}
				}

				.form-base-container {
					padding: 20px 30px;

					.k-picker-wrap.k-state-border-down {
						padding-bottom: 0px;
						border-bottom-width: 1px;
					}

					.k-datepicker.date-question.question,
					.k-datetimepicker.datetime-question.question,
					.k-timepicker.time-question.question {
						height: 30px !important;
					}


					.k-datepicker .k-picker-wrap .k-select {
						height: 29px;
						line-height: 29px;
						width: 2em;
						background-color: #fff;
					}

					.k-autocomplete.k-hover,
					.k-autocomplete.k-focus,
					.k-dropdown-wrap.k-hover,
					.k-dropdown-wrap.k-focus,
					.k-numeric-wrap.k-hover,
					.k-numeric-wrap.k-focus,
					.k-picker-wrap.k-hover,
					.k-picker-wrap.k-focus {
						border-color: @editableTextColor;
					}

					.k-picker-wrap.k-hover,
					.k-picker-wrap.k-focus,
					.k-numeric-wrap.k-hover,
					.k-numeric-wrap.k-focus {
						.k-select {
							border-color: @editableTextColor;
						}
					}

					.k-maskedtextbox .k-textbox {

						&:hover,
						&:focus {
							border-color: @editableTextColor;
						}
					}

					.form-question,
					.e-sign-wrapper {
						&.invalid {
							border: 1px solid red;

							.invalid-message {
								color: red;
								display: inline;
							}
						}
					}

					.form-question {
						position: relative;
						padding: 5px;
						margin: 5px;
						padding-bottom: 10px;

						&:last-child {
							padding-bottom: 5px;
						}

						.question-title {
							padding-bottom: 10px;
							overflow-wrap: break-word;
							word-break: break-word;

							.title-close-warn-msg {
								color: red;
								padding-left: 4px;
							}

							.invalid-message {
								display: none;
							}

							td,
							th {
								border: 1px solid #dddddd;
								text-align: left;
								padding: 8px;
							}

						}

						.invalid-message {
							display: none;
						}

						.question-content {
							@clearXColor: black;
							position: relative;

							.date-question {
								background-color: white;

								span.k-picker-wrap {
									display: flex;
									align-items: center;
								}

								.clear-x {
									visibility: visible;
									color: @clearXColor;
									margin-left: -21px;
									font-size: 22px;
									font-family: 'SourceSansPro-Regular', Arial;
									line-height: 1;
								}

								.empty.clear-x {
									visibility: hidden;
								}
							}

							.date-range-question {

								.start-date,
								.end-date,
								.days-of-the-week {
									padding-right: 0px;
								}

								.checkbox-inline {
									width: 100px;
									padding-left: 0px;

									input[type=checkbox] {
										margin-right: 5px;
									}
								}

								.checkbox-inline+.checkbox-inline {
									margin-left: 0;
								}
							}

							input[time]+span.clear-x {
								margin-left: -14px;
								color: @clearXColor;
								position: relative;
								z-index: 22;
								visibility: visible;
								font-size: 21px;
								font-family: 'SourceSansPro-Regular', Arial;

								&.empty {
									visibility: hidden;
								}
							}

							.datetime-question-container {
								display: flex;

								.input-group {
									padding-left: 10px;
								}

								.k-datepicker.k-input {
									box-shadow: none;
								}
							}

							.question {
								width: 100%;

								&:is(input[type=text]) {
									padding-left: 8px;
									padding-right: 8px;
									margin-right: 10px;
								}
							}

							.systemfield-question {
								&:is(input[type=text]) {
									padding-left: 8px;
								}
							}

							.number-question,
							.date-question {
								text-indent: 0em;

								.k-picker-wrap {
									.date-question {
										padding-left: 8px;
									}
								}
							}

							.list-from-data-question {
								.k-combobox {
									width: 100%;

									.k-dropdown-wrap.k-disabled {
										input.nil {
											opacity: .9;
											width: 100%;
										}
									}

									input.nil {
										opacity: .6;
										width: 120px;
									}

									span.k-icon.k-i-arrow-60-down {
										top: -1px;
									}

									span.k-icon.k-i-x {
										margin-right: -20px;
									}

									.k-dropdown-wrap {
										height: 24px;

										.k-clear-value {
											padding-bottom: 3px;
											right: 35px;
										}

										&:hover {
											border-color: #515151;
											background-color: #fff;
											color: #333;
										}

										&.k-disabled {
											height: 26px;

											.k-input {
												height: 1.65em;
											}
										}
									}
								}

								.k-multiselect {
									span.k-icon.k-i-arrow-60-down {
										padding-top: 12px;
										top: -1px;
									}

									input.k-input {
										width: 120px !important;
									}

									li.k-button>span[unselectable="on"] {
										overflow: hidden;
										text-overflow: ellipsis;
									}

									span.k-clear-value {
										top: 3px;
										line-height: 14px;

										&>.k-i-x {
											padding-right: 22px;
											top: 2px;
										}
									}
								}

								.k-multiselect {
									&.k-hover {
										.k-clear-value {
											top: 3px;
											line-height: 14px;
										}

										.k-multiselect-wrap {
											border-color: rgb(51, 51, 51);
										}

										span.k-icon.k-i-arrow-60-down {
											padding-top: 12px;
										}

										span.k-icon.k-i-x {
											padding-right: 22px;
											top: 2px;
										}
									}

									&.k-focus {
										.k-multiselect-wrap {
											border-color: rgb(51, 51, 51);
										}
									}
								}

								.k-input-inner {
									font-family: SourceSansPro-SemiBold;
									color: #333 !important;
								}
							}

							textarea {
								min-height: 26px;
								resize: vertical !important;
								padding-left: 8px;
								padding-right: 8px;
							}


							.list-question-option .text-question[disabled] {
								border: 1px solid @editableTextBorderColor;
							}

							.grid-icon.grid-icon-reddot {
								height: 16px;
								width: 16px;
								background-size: 16px;
								background-image: url(../../Global/img/grid/reddot.png);
							}

							.grid-icon.grid-icon-yellowdot {
								height: 16px;
								width: 16px;
								background-size: 16px;
								background-image: url(../../Global/img/grid/yellowdot.png);
							}

							.email-question.question {
								resize: none !important;
							}

							.text-question.question:not([disabled]),
							.email-question.question:not([disabled]),
							.hyperlink-question.question:not([disabled]),
							.memo-question.question,
							.currency-question .question,
							.telphone-question,
							.currency-question .question,
							.systemfield-question.question,
							.image-question .image-box .caption textarea {
								border: 1px solid @editableTextBorderColor;
								outline: none;

								&:hover,
								&:focus {
									border-color: @editableTextColor;
								}
							}

							.currency-question input[type=number] {
								padding-left: 16px !important;
							}

							.number-question,
							.datetime-question,
							.date-question,
							.time-question {
								height: 28px;
							}

							.time-question input.form-control {
								font-size: 14px;
							}

							.telphone-question {
								height: 28px;
								font-size: 14px;
								color: @editableTextColor;

								&::placeholder {
									color: rgb(117, 117, 117);
								}

								&[readonly] {
									background: #fff;
									cursor: default;
								}
							}

							.date-question.k-datepicker {
								box-shadow: none;
							}

							.date-question.k-datepicker .k-picker-wrap {
								height: 30px;
							}

							input.date-question[readonly] {
								color: @editableTextColor;
							}

							.number-question {
								.k-numeric-wrap {
									.number-question {
										padding-left: 8px;
									}
								}

								input[type=number] {
									-moz-appearance: textfield;

									&::-webkit-inner-spin-button,
									::-webkit-outer-spin-button {
										display: none;
									}
								}
							}

							.currency-question {
								position: relative;

								.currency-symbol {
									position: absolute;
									padding: 0px 8px;
									line-height: 28px;
								}

								input[type=number] {
									height: 28px;
									padding-left: 15px;
									border: 1px solid #c5c5c5;
									-moz-appearance: textfield;

									&::-webkit-inner-spin-button,
									::-webkit-outer-spin-button {
										display: none;
									}

									&:hover,
									&:focus {
										border-width: 1px;
										outline: none;
									}
								}
							}

							.image-box-mixin() {
								.image-box {
									.image {
										width: 150px;
										height: 150px;
										background-repeat: no-repeat;
										background-position: center;
										margin-bottom: 5px;
										background-size: cover;

										&.loading {
											background-image: url("../../global/img/Spinner-White.gif");
											background-size: auto;
										}

										&.default {
											background-image: url("../../global/Img/detail-screen/udf-default-image.png");
										}

										&.no-image {
											border: 1px solid #c5c5c5;
											background-color: #fff;
											background: none;

											&::after {
												content: "No Image Uploaded";
												position: absolute;
												width: 150px;
												height: 150px;
												color: #bfbfbf;
												border: 1px solid #bfbfbf;
												display: flex;
												justify-content: center;
												align-items: center;
											}
										}
									}

									.caption {
										textarea {
											width: 100%;
											color: #333;
											border: 1px solid #c5c5c5;
											outline: none;
										}
									}
								}
							}

							.systemfield-question {
								height: 28px;

								&.link {
									line-height: 28px;
									padding-left: 8px;
									background: rgba(239, 239, 239, 0.3);
									text-overflow: ellipsis;
									white-space: nowrap;
									overflow: hidden;
								}

								&:hover,
								&:active,
								&:focus {
									outline: 0;
									border-width: 1px;
								}

								&.image-udf {
									height: auto;
									border: none;
									.image-box-mixin();

									.image-box .image {
										background-size: contain;
									}
								}
							}

							.image-question {
								height: auto;
								border: none;

								.image-box {
									display: block;

									.thumb {
										border-radius: 5px;
										border: 3px dotted #d3d3d3;
										padding: 2px;
										width: 120px;
										height: 120px;
										margin-bottom: 20px;

										.image {
											position: absolute;
											width: 110px;
											height: 110px;
											z-index: 2;
											background-repeat: no-repeat;
											background-position: center;
											background-size: contain;
										}

										.image-text {
											position: absolute;
											color: #9d9d9d;
											background-color: #eeeeee;
											width: 110px;
											height: 110px;
											text-align: center;
											line-height: 110px;
											z-index: 1;
											display: none;
										}

										.image-input {
											display: none;
										}

										.image-remove {
											position: absolute;
											right: 2px;
											top: 2px;
											padding: 8px;
											background-repeat: no-repeat;
											height: 13px;
											width: 13px;
											background-size: 10px 10px;
											background-position: center;
											background-color: rgba(255, 255, 255, .5);
											background-image: url('../img/black-del.png');
											cursor: pointer;
											z-index: 3;
										}

										&.loading {
											pointer-events: none !important;

											.image {
												background-size: auto;
												background-image: url("../../global/img/Spinner-White.gif");
											}
										}

										&.default {

											.image,
											.image-remove {
												display: none;
											}

											.image-text {
												display: block;
											}
										}
									}

									.caption {

										.count-down {
											position: absolute;
											top: 120px;
											right: 0px;
											display: none
										}

										textarea {
											max-height: 100px;
											width: 100%;
											color: #333;
											border: 1px solid #c5c5c5;
											outline: none;
											resize: none;
										}
									}
								}
							}

							.question[disabled] {

								&:hover,
								&:active,
								&:focus {
									border: 1px solid #c5c5c5;
								}
							}

							.rating-question,
							.rating-scale-matrix-question {
								width: 100%;
								position: relative;

								.left-label {
									margin-top: 15px;
								}

								.right-label {
									position: absolute;
									right: 10px;
								}

							}

							.map-question {
								width: 100%;
								position: relative;
								height: 400px;

								div.map-question-zoom-cover {
									background-color: rgba(0, 0, 0, 0.45);
									text-align: center;

									&>p.map-question-zoom-cover-tip {
										font-size: 22px;
										color: white;
										font-family: "SourceSansPro-SemiBold", Arial, sans-serif;
										position: relative;
										margin: 0;
										top: 50%;
										-webkit-transform: translateY(-50%);
										-ms-transform: translateY(-50%);
										transform: translateY(-50%);
									}
								}
							}

							.slider.slider-horizontal {
								width: 100%;
								height: 45px;
								margin-top: -5px;

								.tooltip-arrow,
								.tooltip-inner {
									background-color: white;
									color: black;
									border: 0;
								}
							}

							.slider .tooltip.top {
								margin-top: -26px;
							}

							.tooltip-min,
							.tooltip-max {
								display: none;
							}

							.input-group {
								.form-control {
									height: 30px;
									border-radius: 0;
									-webkit-transition: none !important;
									transition: none !important;
									-o-transition: none !important;
								}

								.input-group-addon.datepickerbutton {
									background-color: #fff;
									width: 2em;
									font-size: 14px;
								}
							}

							input:not([type="checkbox"]):not([type="radio"]),
							textarea {
								border-radius: 0;
							}

							.input-group:not(.disabled):hover,
							.input-group:not(.disabled):focus {

								.form-control,
								.input-group-addon {
									border-color: @editableTextColor !important;
								}
							}

							&.readonly {

								input[type='text'],
								input[type='number'],
								textarea {

									&[readonly],
									&[disabled] {

										&:hover,
										&:focus {
											border-color: @editableTextBorderColor !important;
										}
									}
								}

								.k-datepicker.k-input,
								.k-numerictextbox.k-input {
									box-shadow: none;
									border-color: @editableTextBorderColor !important;

									.k-input-button {
										cursor: default;
									}
								}

								.k-numerictextbox.k-input {
									.k-spin-button .k-button {
										cursor: default;
										background-image: none;
										background-color: #fff;
										box-shadow: none;
									}
								}


								.input-group.datetime-question,
								.input-group.time-question {

									input[type='text'],
									.datepickerbutton {
										border-color: @editableTextBorderColor;
									}
								}

								.k-i-calendar,
								.k-link .k-icon {
									cursor: default !important;
								}

								.number-question {

									.k-select,
									.k-link,
									.k-link span {
										&:hover {
											cursor: default !important;
											background-color: transparent;
										}
									}
								}

								.time-question {

									.form-control,
									.datepickerbutton {
										&:hover {
											border-color: @editableTextBorderColor !important;
										}
									}
								}

								.clear-x {
									display: none;
								}
							}
						}
					}
				}

				.qr-and-barcode-wrapper {
					.qr-and-barcode-content {
						border: 2px dashed lightgrey;
						border-radius: 5px;
						font-size: 12px;
						padding: 5px 20px;
						min-height: 34px;

						.qr-and-barcode-buttons {
							height: 20px;

							.scan-button {
								color: blue;
								cursor: pointer;
								text-decoration: underline;
								display: block;
								float: left;
							}

							.clear-button {
								cursor: pointer;
								float: right;
							}
						}

						.qr-and-barcode-answer {
							margin: 5px 0px;
							padding: 0px;
							color: #bbb;
							display: none;
							word-break: break-all;
						}
					}
				}

				.e-sign-wrapper {
					.e-sign-element {
						background-color: white;

						&.disabled {

							.e-sign-content canvas,
							.e-sign-cover-layer {
								cursor: default !important;
							}
						}

						.question-title {
							.title {
								display: inline;
							}

							.modal-title {
								display: none;
							}

							.e-sign-close-button {
								display: none;
							}
						}

						.e-sign-content {
							border: 2px dashed lightgrey;
							border-radius: 5px;

							canvas {
								cursor: pointer;
							}

							.e-sign-cover-layer {
								position: absolute;
								top: 0;
								width: 100%;
								height: 50px;
								cursor: pointer;
								background-color: transparent;

								.place-holder {
									margin-top: 12px;
								}
							}
						}

						.e-sign-action-bar {
							display: none;
						}
					}

					&.full-page.invalid {
						border: none;
					}

					&.full-page {
						position: fixed;
						height: initial;
						left: 0;
						right: 0;
						top: 0;
						bottom: 0;
						z-index: 12040;
						background-color: rgba(0, 0, 0, 0.5);
						padding: 0;

						.e-sign-element {
							margin: 0;
							padding: 0;
							position: absolute;
							left: calc(~"50% - 340px");
							right: calc(~"50% - 400px");
							top: 50%;
							margin-top: -190px;
							height: 380px;

							.question-title {
								padding: 5px 0 5px 10px;
								background-color: #333;
								color: white;
								height: 35px;
								line-height: 25px;

								.title {
									display: none;
								}

								.modal-title {
									display: inline;
									padding: 0;
								}

								.e-sign-close-button {
									display: block;
									position: absolute;
									border: 0;
									width: 16px;
									height: 16px;
									top: 7px;
									right: 7px;
									background-image: url("../../global/img/detail-screen/Close-White.svg");
									background-color: transparent;
									background-size: 16px 16px;
									background-repeat: no-repeat;
									background-position: center;
								}
							}

							.e-sign-content {
								border: 0;
								margin: 1px;

								canvas {
									border: 0;
								}

								.e-sign-cover-layer {
									display: none;
								}
							}

							.e-sign-action-bar {
								display: block;
								padding: 5px 0;
								background-color: #f2f2f2;
								border-top: 1px solid #c4c4c4;

								.k-button {
									color: #fff;
									background-color: #333;
									border-color: #444;
									margin-left: 10px;
									background-image: none;

									&:active {
										-webkit-box-shadow: unset;
										box-shadow: unset;
									}

									&:focus {
										box-shadow: unset;
									}

									&:focus:active:not(.k-disabled):not([disabled]) {
										background-color: @buttonColor;
										border-color: @buttonColor;
									}
								}
							}

							&.invalid {
								border: 0;
							}

							.invalid-message {
								display: none;
							}
						}
					}

					&.disabled {
						.e-sign-element .e-sign-content .e-sign-cover-layer {
							cursor: default;

							.place-holder {
								display: none;
							}
						}
					}
				}
			}

			.form-footer {
				height: 46px;
				background-color: white;
				position: absolute;
				left: calc(~"50% - 340px");
				right: calc(~"50% - 400px");
				bottom: 0;

				.action-bar {
					.form-button {
						border: 1px solid transparent;
						outline: 1px solid transparent;
						box-sizing: border-box;
						color: white;
						width: 108px;
						height: 26px;
						text-align: center;
						font-size: 14px;
						margin: 10px;

						&.form-save,
						&.form-next,
						&.form-close.form-primary-btn {
							background-color: #333333;
							color: #ffffff;
						}

						&.form-save.disabled {
							background-color: lightgray;
							cursor: not-allowed;
						}

						&.form-previous,
						&.form-close {
							background-color: #ffffff;
							color: #333;
						}

						&.form-close {
							float: right;
						}

						.action {
							-webkit-box-align: center;
							-webkit-box-pack: center;
							height: 100%;
							align-items: center;
							justify-content: center;
							flex-wrap: nowrap;
							display: flex;
							vertical-align: baseline;
						}
					}
				}
			}
		}
	}
}

.k-mobile .form-container {
	background-color: white !important;
	z-index: 9999;

	.form-layer {
		.form {
			padding: 0;

			.form-header {
				height: auto;
				padding: 5px 20px;
				position: unset;

				.form-title {
					padding: 5px 0;
					font-size: 24px;
				}

				.form-subtitle {
					font-size: 14px;
					line-clamp: unset !important;
					-webkit-line-clamp: unset;
				}
			}

			.form-body {
				position: absolute;
				top: 0px;
				left: 0;
				bottom: 60px;
				right: 0;
				height: initial !important;

				.form-entity-container {
					padding: 30px 5px 40px;

					.form-entity-input {
						width: 252px;
					}
				}

				.form-question-container {
					padding: 20px;

					.question-content {

						input:not([type="checkbox"]):not([type="radio"]),
						textarea {
							-webkit-appearance: none;
						}
					}
				}
			}

			.form-footer {
				left: 0;
				right: 0;

				.action-bar {
					padding: 0 20px;

					.form-button {
						width: 100px;

						&.btn-close {
							right: 0;
							width: 30px;
						}
					}
				}
			}
		}
	}
}

.modal-footer {
	.form-next {
		margin-left: 0px !important;
	}
}

@media (min-width:768px) {
	.k-mobile .form-container {

		//left: 60px;
		.form-layer {
			.form {
				padding: 0;

				.form-body {
					height: initial !important;
				}
			}
		}
	}
}

.view-option {
	float: right;
	padding: 20px 0;
	width: 120px;

	.k-input {
		height: 24px;
	}
}

.view-option-list {

	.view-option-icon {
		width: 16px;
		height: 16px;
		background-size: contain;
		margin: 6px 5px;
		float: left;
		background-image: url(../Img/Icons/list.svg);
	}

	.view-option-text {
		display: inline-block;
		margin: 2px 0;
	}
}

.view-option-tiles {

	.view-option-icon {
		width: 16px;
		height: 16px;
		background-size: contain;
		margin: 5px;
		float: left;
		background-image: url(../Img/Icons/tiles.svg);
	}

	.view-option-text {
		display: inline-block;
		margin: 2px 0;
	}
}

@media (max-width:767px) {
	.k-mobile .forms-container {
		.no-forms {
			.logo-icon {
				background-size: contain;
			}
		}
	}
}

.modal-content .btn.btn-mobile-confirm {
	background-color: @systemColor;
	border-color: @systemColor;
	color: #fff;
}

.modal1.mobile-alerttfmodal,
.bootbox.unsave-mobile,
.bootbox.mobile-alert {
	.modal-dialog {
		margin: 0 15px;
	}
}

.bootstrap-datetimepicker-widget.dropdown-menu {
	margin-left: 0;
}

.k-animation-container .k-calendar-container,
.k-calendar {
	.k-content {

		td,
		.k-link {
			border-radius: 0;
		}
	}

	.k-month .k-link {
		width: auto;
	}

	.k-today .k-link {
		color: @editableTextColor;
	}

	tbody td.k-focus .k-link {}

	tbody td.k-selected .k-link {
		color: #fff;
		background-image: none;
	}
}

.k-list-container .k-list>.k-item.k-focus {
	border-color: @editableTextColor;
}

.form-record-tooltip.tf-tooltip.tooltip {
	.tooltip-inner {
		max-width: 100%;
		background-color: white;
		color: #262626;
		border-radius: 5px;
		padding: 15px 10px;
		cursor: pointer;
	}
}

@import "rating";
@import "form-attach";

.tfmodal.modal.is-form-on-mobile {
	.modal-dialog {
		.modal-content {
			.modal-body {
				padding: 0px;

				.form-container {
					.form-layer {
						background-color: transparent !important;

						.form {
							.form-header {
								padding-top: 20px;
							}

							.form-body {
								.attach-document-block {
									opacity: .2;
								}
							}
						}
					}
				}
			}
		}
	}
}

.form-record-selector-list.k-list-container {
	.k-list-scroller {
		ul li {
			&.k-selected {
				background-color: white;
			}

			&.k-hover {
				background-color: #eaeaea;
			}
		}
	}
}

.line-clamp-2 {
	line-clamp: 2 !important;
	-webkit-line-clamp: 2 !important;
}

.line-clamp-3 {
	line-clamp: 3 !important;
	-webkit-line-clamp: 3 !important;
}

.modal-dialog .modal-content {

	.list-question-option,
	.boolean-question-option {
		>label[for] {
			font-weight: 500;
			color: initial;
		}
	}
}

.input-group.create-update-record-action-select-data-source {
	table-layout: fixed;
}

.ellipsis-list-item {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}