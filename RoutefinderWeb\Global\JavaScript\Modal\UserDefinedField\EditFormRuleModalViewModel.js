(function()
{
	createNamespace("TF.Modal.UserDefinedField").EditFormRuleModalViewModel = EditFormRuleModalViewModel;

	function EditFormRuleModalViewModel(options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.options = options;
		self._init();
	};

	EditFormRuleModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	EditFormRuleModalViewModel.prototype.constructor = EditFormRuleModalViewModel;

	EditFormRuleModalViewModel.prototype._init = function()
	{
		let self = this;
		let isNew = !self.options.ruleDataEntity;
		let isCopy = self.options.ruleDataEntity && self.options.ruleDataEntity.isCopy;
		let isAdd = isNew || isCopy;
		let title = (isAdd ? "Add " : "Edit ") + toCapitalizeFirstLetter(self.options.ruleType) + " Rule";
		self.sizeCss = "modal-lg form-rule-modal";
		self.title(title);
		self.contentTemplate('modal/UserDefinedField/FormRule');
		self.buttonTemplate(isAdd ? 'modal/positivenegativeextend' : 'modal/positivenegative');
		self.obPositiveButtonLabel("Apply");
		self.obSaveAndNewButtonLabel("Apply & New");
		self.editUserDefinedFieldViewModel = new TF.UserDefinedField.EditFormRuleViewModel(self.options);
		self.data(self.editUserDefinedFieldViewModel);
	};

	EditFormRuleModalViewModel.prototype.init = function()
	{
		console.log('EditFormRuleModalViewModel.prototype.init');
	};

	EditFormRuleModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.editUserDefinedFieldViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this)).finally();
	};

	EditFormRuleModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.editUserDefinedFieldViewModel.cancel()
			.then(function()
			{
				this.negativeClose();
			}.bind(this));
	};

	EditFormRuleModalViewModel.prototype.saveAndNewClick = function()
	{
		var self = this;
		this.editUserDefinedFieldViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
				tf.requiredFieldDataHelper.getRequiredOnlyRecordsWithTypes([self.options.gridType, 'document', 'contact']).then(items =>
				{
					self.options.renewModal(self.options.ruleType, items);
				});

			}
		}.bind(this)).finally();
	};
})();