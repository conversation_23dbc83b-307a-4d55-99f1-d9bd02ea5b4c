(function()
{
	createNamespace("TF.Grid").KendoNewGridWithSelectedRecordsMenu = KendoNewGridWithSelectedRecordsMenu;

	function KendoNewGridWithSelectedRecordsMenu()
	{
	}

	KendoNewGridWithSelectedRecordsMenu.prototype.newGridWithSelectedRecordsClick = function(e)
	{
		tf.contextMenuManager.showMenu(e.target, new TF.ContextMenu.TemplateContextMenu("workspace/grid/newgridwithselectedrecordsmenu", new TF.Grid.GridMenuViewModel(this, this.searchGrid)));
	};

})();
