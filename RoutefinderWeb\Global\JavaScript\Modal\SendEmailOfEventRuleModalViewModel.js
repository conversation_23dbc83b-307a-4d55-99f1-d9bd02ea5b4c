(function()
{
	createNamespace("TF.Modal").SendEmailOfEventRuleModalViewModel = SendEmailOfEventRuleModalViewModel;

	function SendEmailOfEventRuleModalViewModel(options, type)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Send To");
		this.contentTemplate("modal/SendEmailOfEventRuleControl");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Save");
		this.sendEmailOfEventRuleViewModel = new TF.Control.SendEmailOfEventRuleViewModel(options, type);
		this.data(this.sendEmailOfEventRuleViewModel);
	}

	SendEmailOfEventRuleModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SendEmailOfEventRuleModalViewModel.prototype.constructor = SendEmailOfEventRuleModalViewModel;

	SendEmailOfEventRuleModalViewModel.prototype.positiveClick = function()
	{
		this.sendEmailOfEventRuleViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	SendEmailOfEventRuleModalViewModel.prototype.negativeClick = function()
	{
		this.sendEmailOfEventRuleViewModel.close().then(function(result)
		{
			if (result)
			{
				this.negativeClose();
			}
		}.bind(this));
	};

	SendEmailOfEventRuleModalViewModel.prototype.dispose = function()
	{
		this.sendEmailOfEventRuleViewModel.dispose();
	};

})();
