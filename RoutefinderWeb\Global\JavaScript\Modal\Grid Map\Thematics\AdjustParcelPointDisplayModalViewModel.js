(function()
{
	class AdjustParcelPointDisplayModalViewModel extends TF.Modal.AdjustValueDisplayModalViewModel
	{
		constructor(displayDetail)
		{
			super(displayDetail);
			this.contentTemplate('Modal/Grid Map/Thematics/AdjustParcelPointDisplay');
			this.sizeCss = "modal-dialog-md adjust-value-display";
			this.obFillPattern = ko.observable(displayDetail.fillPattern || "Semi");
			this.obBoundaryThickness = ko.observable(displayDetail.boundaryThickness || 2);
			this.obBoundaryColor = ko.observable(displayDetail.boundaryColor || "#00ACC1");
			this.obIdenticalBoundaryColor = ko.observable(displayDetail.identicalBoundaryColor || "#ff0000");
			this.obBoundaryBackgroundColor = ko.computed(() =>
			{
				return this._hexToRgb(this.obBoundaryColor());
			});
			this.obIdenticalBoundaryBackgroundColor = ko.computed(() =>
			{
				return this._hexToRgb(this.obIdenticalBoundaryColor());
			});
		}

		init(model, e)
		{
			super.init(model, e);
			const cookieBoundaryColor = "BoundaryColor", cookieIdenticalColor = "IdenticalBoundaryColor";
			this.boundaryColorPicker = this._initColorPicker(cookieBoundaryColor, this.obBoundaryColor, 'symbol-boundary-color-selector')
			this.identicalBoundaryColorPicker = this._initColorPicker(cookieIdenticalColor, this.obIdenticalBoundaryColor, 'symbol-identical-boundary-color-selector');
		}

		_initColorPicker(cookieName, theValue, elementId)
		{
			this.$form.find("#" + elementId + " [name=color]").kendoColorPicker(
				{
					buttons: false,
					value: theValue(),
					cookieName: cookieName,
					change: (e) =>
					{
						theValue(e.sender.element[0].value);
						var newCookie = { activeColor: theValue() }, cookie = JSON.parse($.cookie(cookieName) || '{}');
						if (cookie && cookie.colorArray && cookie.colorArray.length > 0)
						{
							newCookie.colorArray = cookie.colorArray;
						}
						$.cookie(cookieName, JSON.stringify(newCookie));
					}
				}).data("kendoColorPicker");
		}

		_getOpacity()
		{
			switch (this.obFillPattern())
			{
				case 'Semi':
					return 0.5;
				case 'Solid':
					return 1;
				default:
					return 0;
			}
		}

		_hexToRgb(hex)
		{
			const opacity = this._getOpacity();
			hex = hex.replace(/^#/, '');
			const r = parseInt(hex.substring(0, 2), 16);
			const g = parseInt(hex.substring(2, 4), 16);
			const b = parseInt(hex.substring(4, 6), 16);

			return `rgba(${r}, ${g}, ${b}, ${opacity})`;
		}

		positiveClick()
		{
			var data = this.dataSave();
			data.fillPattern = this.obFillPattern();
			data.boundaryThickness = this.obBoundaryThickness();
			data.boundaryColor = this.obBoundaryColor();
			data.identicalBoundaryColor = this.obIdenticalBoundaryColor();
			return this.positiveClose(data);
		}
	}

	createNamespace('TF.Modal').AdjustParcelPointDisplayModalViewModel = AdjustParcelPointDisplayModalViewModel;
})();
