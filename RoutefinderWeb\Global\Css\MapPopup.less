@import (reference) "main.less";

.esri-ui .esri-popup {
	z-index: 2000;

	.esri-popup__main-container .esri-features__container:not(:empty) {
		padding: 0px;
	}

	calcite-flow,
	calcite-flow-item {
		overflow: visible;
	}
}

.esri-ui .esri-popup .esri-widget {
	line-height: unset;
}

.resizable-doc,
.customizeddashboard {
	.esri-popup {
		.esri-popup__main-container {
			width: 592px;
			overflow: visible;
		}

		&.popup_schoollocation,
		&.popup_georegionlocation {
			.esri-popup__main-container {
				width: 286px;
			}
		}

		&.popup_mapViewerSketch,
		&.popup_whiteboardSketch {
			&.no-sketch-content .esri-popup__main-container {
				width: 296px !important;

				.map-callout-layout-content {
					width: 296px !important;
				}
			}
		}
	}
}

.resizable-doc.small {
	.esri-popup .esri-popup__main-container {
		width: 350px;
	}

	.head-details {
		.detail-left {
			white-space: nowrap;
		}
	}
}

@skin-baseColor: @systemColor;

@lighter-baseColor: #f5e9e8;

.display-center() {
	display: flex;
	align-items: center;
	justify-content: center;
}

.resizable-doc,
.customizeddashboard {

	.no-content {
		.esri-popup__pointer-direction {
			background-color: @lighter-baseColor;
		}
	}

	.esri-popup--aligned-bottom-center,
	.esri-popup--aligned-bottom-left,
	.esri-popup--aligned-bottom-right {
		.esri-popup__pointer-direction {
			background-color: @lighter-baseColor;
		}
	}

	.esri-popup--aligned-top-center,
	.esri-popup--aligned-bottom-center,
	.transform-direction {
		.esri-popup__pointer-direction {
			transform: scale(1.5, 2) rotate(45deg);
		}
	}

	.esri-popup--aligned-top-right,
	.esri-popup--aligned-top-left {
		.esri-popup__pointer.transform-direction {
			bottom: 0;
			transform: rotate(0);
		}
	}

	.esri-popup--aligned-bottom-right,
	.esri-popup--aligned-bottom-left {
		.esri-popup__pointer.transform-direction {
			top: 0;
			transform: rotate(0);
		}
	}

	.esri-popup__footer--has-actions {
		display: none;
	}
}

.resizable-doc,
.customizeddashboard {
	.esri-popup__main-container {
		max-height: 530px;
	}
}

.custom-map-content-table {
	td {
		padding: 2px 5px 5px 2px;
	}
}

.map-callout-layout-content {
	table.layout-table {
		width: 100%;
		table-layout: fixed;

		tr:nth-child(even) {
			background-color: #f5e9e8;
		}

		td {
			padding: 2px 5px 5px 2px;
			border: solid 1px lightgray;

			&.display-name,
			&.field-name {
				padding: 5px;
			}

			&.display-name {
				width: 50%;
			}

			&.field-name {
				overflow: hidden;
				text-overflow: ellipsis;
				word-break: keep-all;
				white-space: nowrap;

				.boolean-mark {
					position: relative;
					display: table;
					margin: 0;
					padding: 0;
					height: 14px;
					width: 14px;

					&+input[type=checkbox] {
						display: none;
					}

					&::after {
						position: absolute;
						display: table-cell;
						content: 'x';
						text-align: center;
						vertical-align: middle;
						height: 14px;
						width: 14px;
						line-height: 9px;
						color: gray;
						border-radius: 2px;
						border: solid 1px gray;
					}
				}
			}

			.open-detail-view {
				cursor: pointer;

				&:hover {
					text-decoration: underline;
				}
			}

			.sketch-display-name {
				width: 100%;
				margin-bottom: 20px;
			}

		}
	}

	.sketch-comment {
		overflow-y: auto;
		text-overflow: ellipsis;
		word-break: break-all;
		white-space: break-spaces;
		margin: 15px 10px;
	}

	.no-comment {
		font-family: "SourceSansPro-Italic";
		color: #777;
		font-size: 16px;
		text-align: center;
	}
}

.custom-map-palette {
	.esri-popup--feature-updated {
		transition: none;
	}

	.esri-popup__header {
		position: absolute;
		right: 0;
		top: 7px;
		display: block !important;

		.esri-popup__button {
			cursor: pointer;

			span {
				cursor: pointer;
			}

			&:hover {
				background-color: transparent;
			}
		}
	}

	.popupPage .content {
		overflow: auto;
		max-height: 300px;
	}
}

#FullPageCalloutContainer,
.resizable-doc .esri-popup__main-container,
.customizeddashboard .esri-popup__main-container {
	cursor: default;

	.esri-popup__header {
		display: none;
	}

	.esri-popup__content {
		margin: 0;
		overflow: visible;
	}

	.ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: keep-all;
		white-space: nowrap;
	}

	.ellipsis.line-clamp-1 {
		white-space: nowrap;
	}

	a.ellipsis,
	a.ellipsis.line-clamp-2 {
		display: block;
	}

	.ellipsis.line-clamp-2 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.popupPage {
		font-size: 15px;
		font-family: "SourceSansPro-Regular";

		.head {
			background-color: @lighter-baseColor;
			padding: 16px 20px 10px;
			color: @skin-baseColor;


			.cover {
				width: 95px;
				height: 48px;
				position: absolute;
				margin-top: -16px;
				margin-left: -10px;
				background-color: @lighter-baseColor;
			}

			.photo {
				width: 74px;
				height: 74px;
				position: absolute;
				overflow: hidden;
				border-radius: 36px;
				border: 2px solid @lighter-baseColor;
				margin-top: -53px;

				&.back {
					box-shadow: 0 2px 16px rgba(0, 0, 0, .3);
				}

				img {
					width: 100%;
				}

				&.no-image {
					background-color: @skin-baseColor;
					color: @lighter-baseColor;
					text-align: center;
					font-size: 36px;
					line-height: 72px;
				}
			}

			.head-pager {
				height: 31px;

				.page-group {
					text-align: right;
					float: right;
					margin-right: 30px;

					.page-indicator {
						font-size: 14px;
						line-height: inherit;
						float: left;
					}

					.right-caret,
					.left-caret {
						float: left;
						margin-top: 2px;
						cursor: pointer;

						&:before {
							content: '';
							position: absolute;
							border-top: 8px solid transparent;
							border-bottom: 8px solid transparent;
						}

						&.useable {
							cursor: default;
							pointer-events: none;

							&:after {
								content: '';
								position: absolute;
								border-top: 6px solid transparent;
								border-bottom: 6px solid transparent;
								margin-top: 2px;
								margin-left: 1px;
							}
						}
					}

					.left-caret {
						margin-left: 10px;

						&:before {
							border-right: 8px solid @skin-baseColor;
						}

						&.useable {
							&:after {
								border-right: 6px solid #F3EDF5;
							}
						}
					}

					.right-caret {
						margin-left: 20px;

						&:before {
							border-left: 8px solid @skin-baseColor;
						}

						&.useable {
							&:after {
								border-left: 6px solid #F3EDF5;
							}
						}
					}
				}
			}

			.head-details {
				.detail-left {
					font-size: 21px;
					min-height: 22px;
					font-weight: 700;
					float: left;
					width: auto;
					max-width: calc(~'100% - 133px');
					border-bottom: 2px solid transparent;

					&:not(.disable):hover {
						cursor: pointer;
						border-bottom: 2px solid;
					}

					&.cover-float {
						float: initial;
					}
				}

				.full-width {
					max-width: 100%;
				}

				.detail-right {
					font-family: "SourceSansPro-SemiBold";
					float: right;
				}

				.close-btn {
					width: 24px;
					height: 24px;
					float: right;
					cursor: pointer;
					margin-left: 5px;
					position: relative;
				}

				.close-btn:before,
				.close-btn:after {
					position: absolute;
					left: 15px;
					content: ' ';
					height: 24px;
					width: 2px;
					background-color: @systemColor;
				}

				.close-btn:before {
					transform: rotate(45deg);
				}

				.close-btn:after {
					transform: rotate(-45deg);
				}
			}
		}

		.content {
			padding: 10px 20px 0 20px;
			margin-bottom: 10px;
			&.map-callout-layout-content {
				max-height: 300px;
				width: 592px;
				overflow: auto;
			}

			.module {
				float: left;
				width: 50%;

				&.full-width {
					width: 100% !important;
					padding-right: 0px !important;
				}

				&.no-padding {
					padding-right: 0px !important;
				}

				&.align-right {
					text-align: right;
				}

				&.with-margin-top {
					margin-top: 10px;
				}

				.module-head {
					font-weight: bold;
					color: @skin-baseColor;
				}

				.color-block {
					width: 40px;
					display: inline-block;
					line-height: 12px;
					border: solid 1px black;
				}
			}

			.content-main {
				display: flex;
			}

			.content-extend {
				margin-top: 20px;

				.tab-header {
					height: 30px;
					line-height: 28px;
					position: relative;
					text-align: center;
					color: @skin-baseColor;
					margin: auto;
					display: table;

					& ul {
						list-style: none;
						padding: 0;
						cursor: pointer;

						& li {
							float: left;
							width: 110px;
							border: 1px solid @skin-baseColor;
							border-right-width: 0;

							&.first {
								border-top-left-radius: 4px;
								border-bottom-left-radius: 4px;
							}

							&.last {
								border-right-width: 1px;
								border-top-right-radius: 4px;
								border-bottom-right-radius: 4px;
							}

							&.select {
								background-color: @skin-baseColor;
								color: white;
								border-width: 1px;
							}
						}

						&.small-width {
							li {
								width: 110px;
							}
						}
					}
				}

				.tab-content {
					position: relative;
					margin-top: 20px;

					.sub-content {
						display: none;
						overflow: hidden;
						width: 100%;

						&.auto-width {
							width: auto;
						}

						.cointainer {
							width: 100%;
							float: left;
						}

						&.select {
							display: block;
						}

						.module {
							margin-top: 20px;
							padding-right: 20px;

							.section-head {
								font-weight: bold;
								color: @skin-baseColor;

								a.drill-down-links {
									color: @skin-baseColor;
								}
							}

							.section-subhead {
								font-family: "SourceSansPro-SemiBold";
								font-size: 11px;
								font-weight: normal;
								color: @skin-baseColor;
								line-height: 12px;
								margin-top: 10px;
							}

							.line {
								margin-bottom: 10px;
							}

							&.vertical {
								width: 100%;
								padding-right: 0px;

								&.hide {
									display: none;
								}

								table {
									width: 100%;

									&:first-child {
										thead tr.fixed {
											position: absolute;
											z-index: 1;
										}
									}

									thead {
										tr.fixed {
											background-color: white;
											height: 25px;
											top: 0;
											z-index: 1;
											width: 100%;

											th {
												float: left;
												margin-top: 0;
												line-height: 20px;

												&:first-child {
													width: 55%;
												}

												&.grade,
												&.middle {
													width: 15%;
												}
											}
										}
									}

									tbody.fixed {
										margin-top: 25px;
										float: left;
										width: 100%;

										&.notfirst {
											margin-top: 0;
										}

										tr {
											float: left;
											width: 100%;
											height: 24px;

											td {
												float: left;
												overflow: hidden;
												text-overflow: ellipsis;
												word-break: normal;
												white-space: nowrap;

												&:first-child {
													width: 55%;

													&.full {
														width: 100%;
													}
												}

												&:nth-child(2) {
													width: 15%;

													&.full {
														width: 45%;
													}
												}

												&:nth-child(3) {
													width: 30%;
												}
											}
										}
									}

									tr {
										height: 24px;

										th:first-child {
											width: 50%;
										}
									}
								}

								.left-layout,
								.right-layout {
									float: left;
								}

								.left-layout {
									width: 140px;

									.subtext {
										font-size: 13px;
									}
								}

								.right-layout {
									width: 412px;

									.ellipsis {
										min-height: 18px;
									}

									.small-width,
									.lager-width,
									.full-width {
										float: left;
									}

									.full-width {
										width: 100%;
									}

									.full-width-extend {
										width: 100%;
										height: auto;
										overflow: auto;

										.first-section-subhead {
											margin-top: 5px;
										}

										.small-width {
											width: 140px;
										}

										.lager-width {
											width: 272px;
											padding-left: 5px;
										}
									}
								}

								.w50p {
									width: 50%;
								}

								.graderange {
									margin-right: 10px;
								}

								.studentnumber {
									width: 100px;
								}
							}

							&:first-child {
								margin-top: 0px;
							}
						}

						&.main-part {
							overflow: auto;
							margin-right: -20px;
						}

						.empty-content {
							.display-center();
							height: 100%;
						}

						.empty {
							font-family: "SourceSansPro-Italic";
							color: #777;
							font-size: 16px;
							text-align: center;
						}

						.notes-tab {
							height: 100%;
							width: 100%;
							display: block;

							&.empty-note {
								.display-center();

								.center-container {
									min-height: 68px;

									.empty {
										display: block;
									}

									.notes-control {
										.display-center();
										visibility: visible;

										.addNote {
											display: block;
										}
									}

									textarea {
										display: none;
									}
								}
							}

							&.edit-note {
								align-items: flex-start;

								.center-container {
									textarea {
										display: block !important;
										align-self: flex-start0;
										border: 1px solid #bcbcbc;
									}

									.empty {
										display: none;
									}

									.notes-control {
										width: 100%;
										visibility: visible;
										display: block;
										align-self: flex-end;

										button {
											&.addNote {
												display: none;
											}

											&.saveEdit,
											&.cancelEdit {
												display: block;
												float: left;
											}
										}
									}
								}
							}

							.center-container {
								width: 100%;
								height: auto;

								>div {
									float: none;
								}

								.empty-note {
									float: none;
								}

								.empty {
									display: none;
								}

								textarea {
									width: 100%;
									height: calc(~"100% - 50px");
									padding: 10px;
									font-family: "SourceSansPro-Regular";
									font-size: 14px;
									outline: none;
									border: 1px solid #fff;
									box-sizing: border-box;
									min-height: 94px;

									&.editable {
										pointer-events: auto;
									}

									&.non-editable {
										pointer-events: none;
									}

									&:hover {
										border: 1px solid #bcbcbc;
									}

									&:focus {
										outline-color: @skin-baseColor;
									}
								}

								&.no-permission {
									textarea {
										border: 0;
										min-height: auto;
										padding: 0;
									}
								}

								.notes-control {
									height: 50px;
									width: 100%;

									button {
										padding: 10px;
										border: 1px solid @skin-baseColor;
										border-radius: 3px;
										background: #fff;
										color: @skin-baseColor;
										font-family: "SourceSansPro-Regular";
										font-size: 14px;
										margin-top: 10px;
										margin-right: 10px;

										&.addNote,
										&.saveEdit,
										&.cancelEdit {
											display: none;
										}

										&.cancelEdit {
											border-color: rgba(255, 255, 255, 0);
										}
									}
								}
							}
						}
					}
				}
			}
		}

		.foot {
			background-color: #FFF;
			padding: 0;
			min-height: 20px;
			margin-top: 10px;

			.action {
				padding: 0 10px 20px 10px;
				display: flex;
				flex-direction: column;
				justify-content: flex-end;
				align-items: flex-end;

				.right {
					display: flex;

					.button:not(:last-child) {
						margin-right: 5px;
					}
				}
			}
		}

		.icon-callout {
			background-repeat: no-repeat;
			background-position: left;
			background-size: 12px;
			padding-left: 16px !important;
		}

		.icon-callout.icon-email {
			background-image: url(../../global/img/Email.png);
		}

		.icon-callout.icon-phone {
			background-image: url(../../global/Img/Phone.png);
		}
	}
}

.resizable-doc,
.customizeddashboard {
	.esri-popup.detail-view-popup .esri-popup__main-container {
		width: 350px;

		.popupPage .head .head-details {
			width: 310px;

			.detail-left {
				max-width: none;
				width: 280px;
				font-size: 16px;
				border-bottom: 1px solid transparent;
				cursor: default;

				&.drill-down-links:hover {
					cursor: pointer;
					border-bottom: 1px solid;
				}
			}
		}

		.popupPage .content {
			text-align: left;
			max-height: 150px;

			&.map-callout-layout-content {
				max-height: 200px;
				width: 100%;
				overflow: auto;
			}
		}
	}
}

.resizable-doc,
.customizeddashboard {
	.vehicle-popup {
		.esri-popup__main-container {
			width: 120px;
		}

		.esri-popup__header {
			display: block;
		}

		.esri-popup__pointer-direction {
			transform: scale(0.75, 2) rotate(45deg);
		}
	}

	.event-popup-container {
		.esri-popup__main-container {
			width: 400px;
		}

		.esri-popup__header {
			display: flex;
		}

		.esri-popup__content {
			margin: 0 15px 12px;
			overflow: visible;
		}

		&.esri-popup--aligned-top-center {
			.esri-popup__pointer-direction {
				transform: scale(0.75, 2) rotate(45deg);
			}
		}
	}
}

.full-page-call-out-navigation-bar {
	display: flex;
	justify-content: space-between;
	padding: 5px 16px;
	font-size: 15px;

	#pager {
		display: flex;
	}

	.page-indicator {
		padding: 0 0 0 4px;
	}
}

#FullPageCalloutContainer .popupPage {
	.head .head-details .detail-left {
		max-width: initial;
	}

	.head .head-details .detail-left:not(.disable):hover {
		border: none;
		padding-bottom: 0;
	}

	.content .content-extend {
		.tab-content .sub-content {
			.notes-tab:not(.edit-note) textarea {
				border: none;
			}

			.notes-tab textarea {
				-webkit-appearance: none;
			}


			.module .section-subhead:nth-child(3) {
				padding-left: 2px;
				width: 30%;
			}

			.module.vertical table tbody.fixed tr td:nth-child(3) {
				padding-left: 2px;
			}

			.notes-tab.empty-note .center-container {
				min-height: 74px;
			}

			.module.vertical table:first-child thead tr.fixed {
				position: initial;
			}

			.module .section-head a.drill-down-links:hover,
			.module.vertical table tbody.fixed tr td a.drill-down-links:hover {
				text-decoration: none;
			}

			&.main-part {
				margin-right: initial;
			}

			&.select {
				display: flex;
				flex-direction: column;
			}
		}

		.tab-header {
			display: flex;
			height: initial;

			ul {
				display: flex;
				flex-direction: row;
				width: 100%;

				li {
					display: flex;
					flex: auto;
					justify-content: center;
					width: initial;
				}
			}
		}
	}
}

.resizable-doc {
	.is-mobile-device:not(.detail-view-panel) {

		.esri-component.esri-popup,
		.esri-component.esri-popup .esri-popup__main-container,
		.esri-component.esri-popup .esri-popup__main-container .popupPage .head {
			border-radius: 5px;
			width: 180px;
		}

		.esri-component.esri-popup {
			opacity: .9;
			transform: translate(-17px, -17px);
		}

		.esri-popup .esri-popup__main-container {
			width: initial;
		}

		.esri-popup__main-container .popupPage .head {
			display: flex;
			justify-content: space-between;
			padding: 10px;
			width: 100%;
			color: white;
			background-color: @systemColor;
			font-weight: 600;

			.title {
				width: calc(100% - 10px);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.plus-button {
				font-size: 24px;
			}
		}

		.esri-popup__pointer {
			display: none;
		}
	}
}

.k-mobile {
	.customizeddashboard {

		.esri-component.esri-popup,
		.esri-component.esri-popup .esri-popup__main-container,
		.esri-component.esri-popup .esri-popup__main-container .popupPage .head {
			border-radius: 5px;
			width: 180px;
		}

		.esri-component.esri-popup {
			opacity: .9;
			transform: translate(-17px, -17px);
		}

		.esri-popup .esri-popup__main-container {
			width: initial;
		}

		.esri-popup__main-container .popupPage .head {
			display: flex;
			justify-content: space-between;
			padding: 10px;
			width: 100%;
			color: white;
			background-color: @systemColor;
			font-weight: 600;

			.title {
				width: calc(100% - 10px);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.plus-button {
				font-size: 24px;
			}
		}

		.esri-popup__pointer {
			display: none;
		}
	}

	#FullPageCalloutContainer {
		height: calc(100% - 88px);

		.popupPage {
			height: 100%;

			.content.map-callout-layout-content {
				padding: 0;
				width: 100%;
				max-height: calc(100% - 58px);

				>div {
					display: inline-block;
				}

				table.layout-table td {

					&.display-name,
					&.field-name {
						padding-left: 15px;
						white-space: inherit;
					}

					input[type=checkbox] {
						position: inherit;
						margin-top: 3px;
					}
				}

				.sketch-comment {
					display: inline-block;
				}
			}
		}
	}
}

.event-popup-container {
	.esri-popup__main-container {
		flex-flow: column nowrap;

		.esri-features__content-container {
			padding: 0 15px 12px;
		}

		.event-popup {
			font-family: "SourceSansPro-Regular", Arial;
			font-size: 12px;
			color: #323232;
			line-height: normal;
		}

		.popup-close-container {
			height: 28px;
			margin-top: 8px;
			margin-bottom: 2px;

			.popup-close-title {
				border-radius: 2px;
				font-size: 14px;
				font-weight: bold;
			}

			.popup-close-button {
				float: right;
			}
		}
	}
}