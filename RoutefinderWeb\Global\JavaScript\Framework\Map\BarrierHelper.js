﻿(function()
{
	var namespace = createNamespace("TF.Map");

	namespace.BarrierHelper = BarrierHelper;

	function BarrierHelper()
	{
	};

	BarrierHelper.getBarriers = function(travelScenarioId, sourceID, impedanceAttribute, needSetCurbGeometry = false, needOffsetCurbGeometry = false)
	{
		let attrName = "Attr_" + impedanceAttribute;
		var pointBarriers = new tf.map.ArcGIS.FeatureSet();
		var lineBarriers = new tf.map.ArcGIS.FeatureSet();
		var polygonBarriers = new tf.map.ArcGIS.FeatureSet();
		var allBarriers = [pointBarriers, lineBarriers, polygonBarriers];

		var queryPromise = tf.arcgisHelper.loadArcgisUrls().then(function()
		{
			return tf.arcgisHelper.queryTravelScenarios(travelScenarioId);
		});
		return queryPromise.then((res) =>
		{
			var travelRegions = (res && res[1]) || [];
			travelRegions.forEach(function(graphic)
			{
				var barrier = new tf.map.ArcGIS.Graphic();
				barrier.geometry = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(graphic.geometry);
				var type = graphic.attributes.Type == 2 ? 0 : 1;
				var weight = graphic.attributes.Weight.toFixed(2);
				barrier.attributes = {
					"BarrierType": type,
					"isChangeTime": graphic.attributes.IsChangeTime
				};
				if (impedanceAttribute)
				{
					barrier.attributes[attrName] = weight;
				}
				else
				{
					barrier.attributes.Attr_Time = weight;
					barrier.attributes.Attr_Length = weight;
				}
				polygonBarriers.features.push(barrier);
			});

			var curbs = res && res[0];
			if (!curbs || !curbs.length)
			{
				return;
			}

			let getStreetsPromise = needSetCurbGeometry && needOffsetCurbGeometry 
				? TF.StreetHelper.getStreetsByIds(curbs.map(i => i.attributes.StreetSegmentID), "file")
				: Promise.resolve([]);
			return getStreetsPromise.then(streets =>
			{
				let streetMap = {};
				streets.forEach(i => streetMap[i.OBJECTID] = i);
				curbs.forEach(curb =>
				{
					let barrier = new tf.map.ArcGIS.Graphic({symbol: new tf.map.ArcGIS.SimpleMarkerSymbol() });
					if (needSetCurbGeometry)
					{
						let geometry = curb.geometry;
						if (needOffsetCurbGeometry)
						{
							let street = streetMap[curb.attributes.StreetSegmentID];
							if (street && street.geometry && street.geometry.paths && street.geometry.paths[0])
							{
								const curbPoint = [geometry.x, geometry.y], rightSide = curb.attributes.SideOfStreet === 1;
								const offsetPoint = TF.RoutingMap.MapEditingPalette.MyStreetsHelper.getOffsetStreetCurbPoint(curbPoint, rightSide, street);
								if (offsetPoint && offsetPoint[0] && offsetPoint[1])
								{
									geometry = new tf.map.ArcGIS.Point({ x: offsetPoint[0], y: offsetPoint[1], spatialReference: geometry.spatialReference });
								}
							}
						}

						barrier.geometry = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(geometry);
					}

					var fullEdge = curb.attributes.Type == 0 ? true : false;
					barrier.attributes = {
						"FullEdge": fullEdge,
						"BarrierType": curb.attributes.Type,

						// TODO, QUESTION
						// use Attr_Time or Attr_Length based on impedanceAttribute?
						// the value is additional cost, is it enough to add only 0.1 minutes or 0.1 meters?
						"Attr_Time": 0.1,

						"SideOfEdge": curb.attributes.SideOfStreet,
						"CurbApproach": 1,
						"SourceOID": curb.attributes.StreetSegmentID,
						"SourceID": sourceID,
						"PosAlong": 0.474451,
					};
					pointBarriers.features.push(barrier);
				});
			});
		}).then(() =>
		{
			return allBarriers;
		});;
	}

	BarrierHelper.recalculateDirectionCostWithBarriers = function(directions, polygonBarriers, impedanceAttribute, directionsLengthUnits)
	{
		if (!directions || !polygonBarriers)
		{
			return;
		}

		let travelRegions = polygonBarriers?.features || [];
		
		return impedanceAttribute == "Length"
			? recalculateDirectionLengthWithBarriers(directions, travelRegions, directionsLengthUnits)
			: recalculateDirectionTimeWithBarriers(directions, travelRegions);
	}

	function recalculateDirectionLengthWithBarriers(directions, travelRegions, directionsLengthUnits)
	{
		let totalAddedLength = 0;
		directions.features.forEach((feature) =>
		{
			if (feature.attributes && feature.attributes.length != 0)
			{
				let geometry = feature.geometry;
				let addedLength = 0;
				travelRegions.forEach((tr) =>
				{
					if (tr.attributes.BarrierType == 1 && !tr.attributes.isChangeTime)
					{
						let travelRegionGeom = tr.geometry;
						if (tr.geometry.spatialReference.wkid != 102100)
						{
							travelRegionGeom = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(travelRegionGeom);
						}

						let affectedRoute = tf.map.ArcGIS.geometryEngine.intersect(travelRegionGeom, geometry);
						if (affectedRoute)
						{
							let affectedFactor = parseFloat(tr.attributes.Attr_Length);
							let affectedSegLength = tf.map.ArcGIS.geometryEngine.geodesicLength(affectedRoute, directionsLengthUnits);
							addedLength = affectedSegLength - affectedSegLength * affectedFactor;
						}
					}
				});

				feature.attributes.length = feature.attributes.length + addedLength;
				totalAddedLength = totalAddedLength + addedLength;
			}
		});

		if (directions.summary)
		{
			directions.summary.totalLength = directions.summary.totalLength + totalAddedLength;
		}
		directions.totalLength = directions.totalLength + totalAddedLength;
	}

	function recalculateDirectionTimeWithBarriers(directions, travelRegions)
	{
		let totalAddedTime = 0;
		directions.features.forEach(function(feature)
		{
			if (feature.attributes && feature.attributes.length != 0)
			{
				let geometry = feature.geometry;
				let addedTime = 0;
				travelRegions.forEach(function(tr)
				{
					if (tr.attributes.BarrierType == 1 && !tr.attributes.isChangeTime)
					{
						let travelRegionGeom = tr.geometry;
						if (tr.geometry.spatialReference.wkid != 102100)
						{
							travelRegionGeom = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(travelRegionGeom);
						}

						let affectedRoute = tf.map.ArcGIS.geometryEngine.intersect(travelRegionGeom, geometry);
						if (affectedRoute)
						{
							let affectedFactor = parseFloat(tr.attributes.Attr_Time);
							let affectedSegLength = tf.map.ArcGIS.geometryEngine.geodesicLength(affectedRoute, default_directionsLengthUnits);
							let normalSegLength = feature.attributes.length - affectedSegLength;
							let actualSpeed = (normalSegLength + affectedSegLength * affectedFactor) / feature.attributes.time;
							let actualTime = (affectedSegLength / actualSpeed);
							addedTime = addedTime + actualTime * (1 - affectedFactor);
						}
					}
				});

				feature.attributes.time = feature.attributes.time + addedTime;
				totalAddedTime = totalAddedTime + addedTime;
			}
		});

		if (directions.summary)
		{
			directions.summary.totalTime = directions.summary.totalTime + totalAddedTime;
		}
		directions.totalTime = directions.totalTime + totalAddedTime;
	}
})();