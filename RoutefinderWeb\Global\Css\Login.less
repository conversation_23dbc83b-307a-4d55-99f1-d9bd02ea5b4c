﻿.k-mobile body {
	overflow-x: auto;
	min-width: 0;

	.unsave-mobile {
		.modal-dialog {
			margin: 0 auto;
		}

		@media (min-width: 300px) and (max-width: 500px) {
			.modal-dialog {
				width: 280px;
			}
		}

		@media (min-width: 500px) and (max-width: 767px) {
			.modal-dialog {
				width: 350px;
			}
		}
	}

	.unsave-mobile .modal-footer .btn.btn-yes-mobile {
		background-color: #D74B3C !important;
	}

	.main-body.customizeddashboard.read-mode {
		min-width: 1024px;
	}

	.modal-fullscreen {
		.modal-content {
			height: 100%;
		}

		.modal-body {
			height: 100%;
			width: 100%;
			overflow-x: hidden;
		}
	}

	.modal-dialog .modal-content .tf-login {

		.icon-viewfinder-final {
			width: 100%;
			height: 120px;
			margin: 0 auto;
		}

		.tf-login-row {
			width: 100%;
		}

		.login_logo {
			width: 100%;
		}

		.icon-subtitle {
			position: inherit;
			top: 480px;
			float: unset;
			text-align: center;
			font-size: 14px;
		}

		.login_form {
			width: 100%;
			margin: 0;

			.panel.panel-default {
				width: auto;
				margin: 0 auto;
				border: none;
				box-shadow: none;

				.panel-heading {
					display: none;
				}
			}
		}

		.form-group {
			button[type="submit"] {
				height: 40px;
			}
		}
	}
}

.modal-fullscreen {
	width: 100%;
	height: 100%;
	margin: 0;

	.login-page-header {
		margin: -15px -15px 0 -15px;
		padding: 0;
		min-width: 1024px;
	}

	.modal-content {
		border-width: 0;
	}

	.modal-body {
		height: 100vh;
	}
}


.tf-navbar {
	position: relative;

	.nav-logo {
		border: 0;
		/*margin-left: 10px;*/
	}
}

.modal-dialog .modal-content .tf-login {
	label[for] {
		height: 22px;
	}

	.checkbox>label {
		line-height: 1.8;
	}

	.icon-viewfinder-final {
		background-repeat: no-repeat;
		background-position: center;
		background-image: url('../../global/img/login_logo.png');
		background-size: contain;
		width: 448px;
		height: 287px;
		margin-top: 30px;
	}

	.tf-login-row {
		width: 838px;
		margin: 0 auto;

		.password-container {
			input[type="password"]::-ms-reveal {
				display: none;
			}
			input[type="password"]::-ms-clear {
				display: none;
			}
			input[id="password"] {
				padding-right: 30px;
			}	
			position: relative;

			.eye-icon {
				position: absolute;
				top: 50%;
				right: 8px;
				transform: translateY(-60%);
				cursor: pointer;
			}

			.image-icon {
				display: inline-block;
				width: 16px;
				height: 16px;
				background-repeat: no-repeat;
				background-size: contain;
				background-position: center;
				vertical-align: middle;
			}

			.icon-eye-slash {
				background-image: url(../Img/icons/eye-slash.svg);
				filter: grayscale(100%) brightness(50%);
			}

			.icon-eye {
				background-image: url(../Img/icons/eye.svg);
				filter: grayscale(100%) brightness(50%);
			}
		}
	}

	.login_form {
		width: 370px;
		margin-left: 20px;
	}

	.login_logo {
		width: 448px;
	}

	.icon-subtitle {
		position: relative;
		top: 280px;
		float: right;
		right: 14px;
		font-size: 9px;
	}

	.form-group {
		margin-bottom: 0px;

		.secondary-btn {
			width: auto;
			margin-left: 40px;
			background-color: #fff;
			color: #333333;
			font-weight: bold;
		}

		.primary-btn,
		button[type="button"] :not(.secondary-btn) {
			width: auto;
			background-color: #333333;
			border: 2px solid #333;
			color: #fff;

			&.tf-btn-gray {
				background-color: transparent;
				color: #333;
				border: 0;
			}

			&:focus:not(:disabled) {
				border: 2px solid #999;
			}
		}
	}

	.panel-heading {
		background-color: #6b6b6b;
		color: #fff;
	}

	.release-notes {
		color: #D74B3C;
		cursor: pointer;
	}

	.login-error {
		color: #FF0000;
		margin-top: 15px;
	}

	.edit-bottombar {
		background-color: #f2f2f2;
		line-height: 40px;
		height: 40px;
		padding: 0 25px;
	}

	.login-code-error {
		color: #FF0000;
	}

	.multifactor-authentication-box {
		height: 125px;
		margin-bottom: 5px;
		line-height: 23px;

		.email {
			display: inline-block;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			max-width: 180px;
			vertical-align: bottom;
			font-weight: bold;
			font-size: 14px;
		}

		.pin-code-box {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 8px;

			.form-group {
				width: 100%;
			}

			input {
				height: 45px;
				text-align: center;

				&::placeholder {
					font-size: 13px;
				}
			}

			.resend-code-float {
				float: right;
				font-size: 15px;
			}

			a {
				color: black;
				cursor: pointer;
				margin-top: 3px;
				outline: none !important;

				&.disabled {
					opacity: 0.4;
					pointer-events: none;
				}
			}
		}
	}

	.mfa-panel-body {
		padding-top: 27.5px;
	}

	.mfa-panel-heading {
		padding: 11px 15px;
		line-height: 23px;
	}

	.mfa-submit {
		width: 130px;
		height: 26px;

		&>button {
			height: 26px !important;
			font-size: 15px !important;
		}
	}

	.input-body-mfa-mobile {
		padding-right: 8px;
		padding-left: 8px;
	}

	.input-body-mobile {
		@media (max-width:768px) {
			width: 338px;
			margin: 0 auto;
		}
	}

	.tf-login-mobile {
		padding-left: unset;
		padding-right: unset;
		max-width: 368px;

		display: flex;
		flex-direction: column;
		justify-content: center;

		// 45px: banner height
		// 15px: padding from .modal-body
		height: calc(100vh - 45px - 15px);
	}

	.login-button-group {
		display: flex;
		margin-top: 30px;
		margin-bottom: 10px;

		@media (max-width:767px) {
			padding-left: 30%;
		}
	}

	.release-notes-mobile {
		margin-top: 15px;
	}

	.submit-button-mobile {
		margin-left: 0 !important;
		width: 368px !important;
	}

	.panel-body-mobile {
		@media (max-width:768px) {
			padding-left: unset;
			padding-right: unset;
		}
	}
}

.tf-login {
	#userName:-webkit-autofill {
		animation-name: onAutoFillStart;
	}

	#userName:autofill {
		animation-name: onAutoFillStart;
	}
}

@keyframes onAutoFillStart {
	from {
		animation-delay: 0s;
	}

	to {
		animation-delay: 0s;
	}
}