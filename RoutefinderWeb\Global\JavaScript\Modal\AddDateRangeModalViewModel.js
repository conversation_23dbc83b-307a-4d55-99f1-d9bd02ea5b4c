﻿(function()
{
	createNamespace('TF.Modal').AddDateRangeModalViewModel = AddDateRangeModalViewModel;

	function AddDateRangeModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Add Date Interval");
		this.sizeCss = "modal-dialog-md";
		this.contentTemplate('modal/AddDateRange');
		this.buttonTemplate('modal/PositiveNegative');
		this.obPositiveButtonLabel("Save");
		this.obNegativeButtonLabel("Cancel");
		this.AddDateRangeViewModel = new TF.Control.AddDateRangeViewModel(options)
		this.data(this.AddDateRangeViewModel);
	}

	AddDateRangeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AddDateRangeModalViewModel.prototype.constructor = AddDateRangeModalViewModel;

	AddDateRangeModalViewModel.prototype.positiveClick = function()
	{
		this.AddDateRangeViewModel.apply().then(result =>
		{
			if (result)
			{
				this.positiveClose(result);
			}
		});
	};

	AddDateRangeModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose(false);
	};

})();
