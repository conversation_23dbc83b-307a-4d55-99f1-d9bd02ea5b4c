(function()
{
	var namespace = createNamespace("TF.ContextMenu");
	namespace.GroupTabsContextMenu = GroupTabsContextMenu;

	function GroupTabsContextMenu(templateName, menuViewModel)
	{
		namespace.TemplateContextMenu.call(this, templateName, menuViewModel);
	}

	GroupTabsContextMenu.prototype = Object.create(namespace.TemplateContextMenu.prototype);

	GroupTabsContextMenu.prototype.constructor = GroupTabsContextMenu;

	//this method needs to be called in subclass
	GroupTabsContextMenu.prototype.createContainer = function($wrapper, target)
	{
		var $target = $(target);
		this._target = $target;

		var $container = $('<div></div>');
		this._$container = $container;
		var self = this;
		$container.on("mouseover", function()
		{
			clearTimeout(self._timer);
		});
		$container.on("mouseout", function()
		{
			clearTimeout(self._timer);
			//NOTE: context menu close
			self.resetTimer();
		});
		$container.on("contextMenuClose", function()
		{
			//$container.hide();
			self.dispose();
		});

		if (this.isElementTarget($target[0]))
		{
			var $handlePlaceholder = $('<div></div>');
			this.handleWidth = $target.outerWidth();
			this.handleHeight = $target.outerHeight();
			$handlePlaceholder.css({
				width: this.handleWidth,
				height: this.handleHeight,
				position: "absolute"
			})
			//$handlePlaceholder.appendTo($container);
			var offset = $target.offset();
			$container.css({
				position: "absolute",
				left: offset.left,
				top: $target.hasClass("active") ? offset.top + 1 : offset.top//check if this is the active tab, because the active tab is 1px heigh than normal
			});
			$target.addClass("contextmenu-open");
		}
		else
		{
			$container.css({
				position: "absolute",
				left: $target[0].left,
				top: $target[0].top
			});
		}
		$container.appendTo($wrapper);
		return $container;
	}

	GroupTabsContextMenu.prototype.clearTime = function()
	{
		clearTimeout(this._timer);
	}

	GroupTabsContextMenu.prototype.resetTimer = function()
	{
		this._timer = setTimeout(function()
		{
			this.dispose();
		}.bind(this), 300);
	}
})();
