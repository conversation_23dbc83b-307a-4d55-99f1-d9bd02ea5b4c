﻿(function()
{
	createNamespace('TF.Modal').AddThreeFieldsModalViewModel = AddThreeFieldsModalViewModel;

	function AddThreeFieldsModalViewModel(fieldName, id)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/addthreefieldscontrol');
		this.buttonTemplate('modal/positivenegative');
		this.addThreeFieldsViewModel = new TF.Control.AddThreeFieldsViewModel(fieldName, id);
		this.data(this.addThreeFieldsViewModel);
		this.sizeCss = "modal-dialog-sm";

		var viewTitle;

		switch (fieldName)
		{
			case 'ethniccode':
				viewTitle = " Ethnic Code";
				if (!id)
					this.buttonTemplate('modal/positivenegativeextend');
				break;
			case 'disabilitycode':
				viewTitle = " " + tf.applicationTerm.getApplicationTermSingularByName("Disability") + " Code";
				if (!id)
					this.buttonTemplate('modal/positivenegativeextend');
				break;
			case 'vehicleequipment':
				viewTitle = " " + tf.applicationTerm.getApplicationTermPluralByName("Vehicle") + " Equipment Code";
				if (!id)
					this.buttonTemplate('modal/positivenegativeextend');
				break;
			default:
				viewTitle = " Three Fields";
				break;
		}

		///this is going to check if the popup form is add new records or edit an existing record
		if (id)
		{
			viewTitle = "Edit" + viewTitle;
		}
		else
		{
			viewTitle = "Add" + viewTitle;
		}

		this.title(viewTitle);
	}

	AddThreeFieldsModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	AddThreeFieldsModalViewModel.prototype.constructor = AddThreeFieldsModalViewModel;

	AddThreeFieldsModalViewModel.prototype.positiveClick = function()
	{
		this.addThreeFieldsViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	AddThreeFieldsModalViewModel.prototype.saveAndNewClick = function()
	{
		this.addThreeFieldsViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.addThreeFieldsViewModel.obEntityDataModel(new TF.DataModel.DisabilityCodeDataModel());
				this.newDataList.push(result);
				if ($("input[name=code]") && $("input[name=code]").length > 0)
				{
					$("input[name=code]").focus();
				}
				PubSub.publish(topicCombine(pb.DATA_CHANGE, "listmover"));
			}
		}.bind(this));
	};

	AddThreeFieldsModalViewModel.prototype.dispose = function()
	{
		this.addThreeFieldsViewModel.dispose();
	};

})();
