﻿(function()
{
	createNamespace("TF.Modal.Navigation").AboutModalViewModel = AboutModalViewModel;

	function AboutModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('About');
		this.obLicensedTo = ko.observable("");
		this.obDatabaseName = ko.observable("None Open");
		this.obDatabaseDirectory = ko.observable("");
		this.obRouteServer = ko.observable("");
		this.obDatabaseLocation = ko.observable("");
		this.obDataVersion = ko.observable("01.00.00");

		this.contentTemplate('Navigation/About');
		this.buttonTemplate('modal/positive');
		this.obPositiveButtonLabel = ko.observable("Close");

		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "vendoraccessinfo"))
			.then(function(apiResponse)
			{
				this.obLicensedTo(apiResponse.Items[0].CustomerName);
			}.bind(this));

		var datasourceId = tf.storageManager.get("datasourceId");
		if (datasourceId > 0)
		{
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), datasourceId))
				.then(function(apiResponse)
				{
					var datasource = apiResponse.Items[0];
					this.obDatabaseName(datasource.DatabaseName);
					this.obDatabaseDirectory(datasource.Location + '\\' + datasource.Subdirectory);
					this.obDatabaseLocation("to do");
					//this.obDataVersion(datasource.DbfullVersion);// may not read from access DB
					return true;
				}.bind(this));

			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "routeserver", "version"))
				.then(function(response)
				{
					this.obRouteServer(response.Items[0]);
				}.bind(this));
		}
	};

	AboutModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AboutModalViewModel.prototype.constructor = AboutModalViewModel;

})();