(function()
{
	createNamespace('TF.Modal').MapIncidentTypeModalViewModel = MapIncidentTypeModalViewModel;
	function MapIncidentTypeModalViewModel(recordEntity, callBackFunction)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.modalWidth("810px");
		self.applyTemplate(recordEntity);
		self.saveAndNewCallback = callBackFunction;
		if (recordEntity)
		{
			self.obSaveAndNewVisible(false);
		}
	}

	MapIncidentTypeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	MapIncidentTypeModalViewModel.prototype.constructor = MapIncidentTypeModalViewModel;

	MapIncidentTypeModalViewModel.prototype.applyTemplate = function(recordEntity)
	{
		var self = this,
			isEdit = !!recordEntity,
			incidentMetadata = (new TF.DataModel.MapIncidentTypeDataModel()).getIncidentTypeConfig(),
			contentTemplate = incidentMetadata.editorContentTemplate,
			viewModelControlType = TF.Control[incidentMetadata.editorViewModelType],
			viewTitle, viewModel;

		// init Modal UI by config type
		self.sizeCss = "modal-dialog-sm";
		viewTitle = String.format("{0} {1}", isEdit ? "Edit" : "Add", incidentMetadata.singular);
		self.title(viewTitle);
		self.buttonTemplate("modal/positivenegativeextend");

		// init Modal viewModel by config type
		viewModel = new viewModelControlType(incidentMetadata.value, recordEntity);

		self.contentTemplate(contentTemplate);
		self.viewModel = viewModel;
		self.data(self.viewModel);
	}

	MapIncidentTypeModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.viewModel.apply().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};

	MapIncidentTypeModalViewModel.prototype.negativeClick = function()
	{
		if (this.viewModel.obEntityDataModel().apiIsDirty()) 
		{
			tf.promiseBootbox.yesNo({ message: "You have unsaved changes. Would you like to save your changes prior to closing?", backdrop: true, title: "Unsaved Changes", closeButton: true })
				.then(function(result)
				{
					if (result)
					{
						this.positiveClick();
					}
					else
					{
						this.negativeClose();
					}
				}.bind(this));
		}
		else
		{
			this.negativeClose();
		}
	};

	MapIncidentTypeModalViewModel.prototype.saveAndNewClick = function()
	{
		var self = this;
		self.viewModel.apply().then(function(result)
		{
			if (result)
			{
				self.title("Add Map Incident Type");
				self.viewModel.resetDataModel();
				self.viewModel.initIncidentData();
				self.viewModel.setFocus();
				if (self.saveAndNewCallback)
				{
					self.saveAndNewCallback(result);
				}
			}
		});
	};

	MapIncidentTypeModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
		this.viewModel = null;
	};
})();