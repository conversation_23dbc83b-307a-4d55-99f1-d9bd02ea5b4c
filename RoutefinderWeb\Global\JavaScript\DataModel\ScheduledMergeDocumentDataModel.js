﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.ScheduledMergeDocumentDataModel = function(scheduledMergeDocumentEntity)
	{
		namespace.BaseDataModel.call(this, scheduledMergeDocumentEntity);
	};

	namespace.ScheduledMergeDocumentDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ScheduledMergeDocumentDataModel.prototype.constructor = namespace.ScheduledMergeDocumentDataModel;

	namespace.ScheduledMergeDocumentDataModel.prototype.mapping = [
		{ from: "Id", default: 0, required: true },
		{ from: "Name", default: "" },
	];
})();