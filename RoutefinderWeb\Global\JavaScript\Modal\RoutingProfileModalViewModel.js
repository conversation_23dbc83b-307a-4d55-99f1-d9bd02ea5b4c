(function()
{
	createNamespace('TF.Modal').RoutingProfileModalViewModel = RoutingProfileModalViewModel;

	function RoutingProfileModalViewModel(routingProfileId, assignedUser, newCopy)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/routingprofilecontrol');
		this.buttonTemplate('modal/positivenegative');
		this.sizeCss = "modal-lg";
		this.viewModel = new TF.Control.RoutingProfileViewModel(routingProfileId, assignedUser, newCopy);
		this.data(this.viewModel);
		this.title("Routing Profile");
	}

	RoutingProfileModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	RoutingProfileModalViewModel.prototype.constructor = RoutingProfileModalViewModel;

	RoutingProfileModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	RoutingProfileModalViewModel.prototype.negativeClick = function()
	{
		var self = this;
		if (this.viewModel.obEntityDataModel().apiIsDirty())
		{
			return tf.promiseBootbox.yesNo(
				{
					message: "You have unsaved changes.  Would you like to save your changes prior to closing?",
					backdrop: true,
					title: "Unsaved Changes",
					closeButton: true
				})
				.then(function(result)
				{
					if (result)
					{
						self.positiveClick();
					}
					else
					{
						self.negativeClose();
					}
				});
		}
		else
		{
			this.negativeClose();
		}


	};


	RoutingProfileModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};

})();
