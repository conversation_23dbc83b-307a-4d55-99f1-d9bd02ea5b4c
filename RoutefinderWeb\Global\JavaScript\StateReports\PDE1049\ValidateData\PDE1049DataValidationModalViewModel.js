(function()
{
	createNamespace('TF.Modal').PDE1049DataValidationModalViewModel = PDE1049DataValidationModalViewModel;

	function PDE1049DataValidationModalViewModel(options)
	{
		const MODAL_WIDTH = 950;

		TF.Modal.BaseModalViewModel.call(this);
		this.modalRootContainerCss = "pde1049-validate-dialog";

		this.contentTemplate("modal/StateReport/PDE1049DataValidation");
		this.buttonTemplate('modal/positivenegativeother');
		this.title("PA eTran Module Validate Data");
		this.data(new TF.Control.PDE1049DataValidationViewModel(options));
		this.obPositiveButtonLabel("Export");
		this.obNegativeButtonLabel("Back");
		this.obOtherButtonLabel("Cancel");
		this.modalWidth(`${MODAL_WIDTH}px`);
	}

	PDE1049DataValidationModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	PDE1049DataValidationModalViewModel.prototype.constructor = PDE1049DataValidationModalViewModel;

	PDE1049DataValidationModalViewModel.prototype.positiveClick = function()
	{
		this.data().apply().then((res) =>
		{
			if (res)
			{
				this.positiveClose(true);
			}
		});
	};

	/**
	 * Go back to last phase.
	 *
	 */
	PDE1049DataValidationModalViewModel.prototype.negativeClick = function()
	{
		const vm = this.data();
		const config = {
			SchoolYear: vm.obSchoolYear(),
			AdminUnitNumber: vm.obAdminUnitNumber(),
		};

		this.positiveClose(config);
	};

	/**
	 * Cancel the whole export process.
	 *
	 */
	PDE1049DataValidationModalViewModel.prototype.otherClick = function()
	{
		this.negativeClose(true);
	};
})()