﻿(function()
{
	createNamespace('TF.Control').ImageSettingsViewModel = ImageSettingsViewModel;

	function ImageSettingsViewModel(data)
	{
		this.src = ko.observable(data.dataUrl || data.src || "http://");
		this.alt = ko.observable(data.alt);
		this.width = ko.observable(data.width);
		this.height = ko.observable(data.height);
		this.percentageX = ko.observable(data.percentageX);
		this.percentageY = ko.observable(data.percentageY);
		this.usePercentage = ko.observable(data.usePercentage);
	}

	ImageSettingsViewModel.prototype.constructor = ImageSettingsViewModel;

	ImageSettingsViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.el = el;
		self.setupValidator(el);
	};

	ImageSettingsViewModel.prototype.setupValidator = function(el)
	{
		var self = this;

		var validatorFields = {
			'width-px': {
				trigger: "blur change",
				validators: {
					greaterThan: {
					}
				}
			},
			'height-px': {
				trigger: "blur change",
				validators: {
					greaterThan: {
					}
				}
			},
			'width-percentage': {
				trigger: "blur change",
				validators: {
					greaterThan: {
					}
				}
			},
			'height-percentage': {
				trigger: "blur change",
				validators: {
					greaterThan: {
					}
				}
			}
		};

		var $el = $(el);
		if (self.validator)
		{
			$el.data("bootstrapValidator", null);
		}
		self.validator = ($el.bootstrapValidator({
			excluded: [":hidden", ":not(:visible)", ":disabled"],
			live: "enabled",
			fields: validatorFields
		})).data("bootstrapValidator");
	};

	ImageSettingsViewModel.prototype.ScaleByPixel = function(viewModel, event)
	{
		var self = this;
		self.usePercentage(false);
		self.setupValidator(self.el);
	};

	ImageSettingsViewModel.prototype.ScaleByPercentage = function(viewModel, event)
	{
		var self = this;
		self.usePercentage(true);
		self.setupValidator(self.el);
	};
	
	ImageSettingsViewModel.prototype.validate = function()
	{
		var self = this;
		return self.validator.validate();
	};
})();
