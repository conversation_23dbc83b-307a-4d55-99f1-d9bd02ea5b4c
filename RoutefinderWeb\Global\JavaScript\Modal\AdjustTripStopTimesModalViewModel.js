﻿(function()
{
	createNamespace("TF.Modal").AdjustTripStopTimesModalViewModel = AdjustTripStopTimesModalViewModel;

	function AdjustTripStopTimesModalViewModel(stopTimeCalc, totalStopTimeCalc, tripstop, tripdata)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Adjust ' + tf.applicationTerm.getApplicationTermSingularByName("Trip Stop") + ' Times');
		this.sizeCss = "modal-sm";
		this.contentTemplate('modal/AdjustTripStopTimesControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Save");
		this.model = new TF.Control.AdjustTripStopTimesViewModel(stopTimeCalc, totalStopTimeCalc, tripstop, tripdata);
		this.data(this.model);
	};

	AdjustTripStopTimesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AdjustTripStopTimesModalViewModel.prototype.constructor = AdjustTripStopTimesModalViewModel;

	AdjustTripStopTimesModalViewModel.prototype.positiveClick = function()
	{
		this.model.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	AdjustTripStopTimesModalViewModel.prototype.dispose = function()
	{
		this.model.dispose();
	};
})();

