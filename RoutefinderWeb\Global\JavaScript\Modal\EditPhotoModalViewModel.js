﻿(function()
{
	createNamespace('TF.Modal').BaseEditImageModalViewModel = BaseEditImageModalViewModel;

	function BaseEditImageModalViewModel(imageType, imageId, recordName, databaseId, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.obImageLabel = ko.observable("Photo");
	}

	BaseEditImageModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	BaseEditImageModalViewModel.prototype.constructor = BaseEditImageModalViewModel;

	BaseEditImageModalViewModel.prototype.deletePhotoClick = function(viewModel, e)
	{
		this.EditPhotoViewModel.deletePhoto()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
			}.bind(this));
	};

	BaseEditImageModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		if (this.EditPhotoViewModel.imageUpdated)
		{
			return tf.promiseBootbox.yesNo(
				{
					message: "You have unsaved changes.  Would you like to save your changes prior to closing this form?",
					backdrop: true,
					title: "Unsaved Changes",
					closeButton: true
				})
				.then(function(result)
				{
					if (result == true)
					{
						this.positiveClick();
					}
					else if (result == false)
					{
						this.negativeClose();
					}
				}.bind(this));
		}
		else
		{
			this.negativeClose();
		}
	};

	BaseEditImageModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.EditPhotoViewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
			}.bind(this));
	};
})();

(function()
{
	createNamespace('TF.Modal').EditPhotoModalViewModel = EditPhotoModalViewModel;

	function EditPhotoModalViewModel(imageType, imageId, recordName, databaseId, imageData)
	{
		TF.Modal.BaseEditImageModalViewModel.call(this);
		this.title(tf.applicationTerm.getApplicationTermSingularByName("Edit Photo"));
		this.sizeCss = 'modal-pic-dialog';
		this.contentTemplate('modal/EditPhotoControl');
		this.buttonTemplate('modal/editPhotoPositiveNegative');
		this.EditPhotoViewModel = new TF.Control.EditPhotoViewModel(imageType, imageId, recordName, databaseId, imageData);
		this.data(this.EditPhotoViewModel);
	}

	EditPhotoModalViewModel.prototype = Object.create(TF.Modal.BaseEditImageModalViewModel.prototype);
	EditPhotoModalViewModel.prototype.constructor = EditPhotoModalViewModel;

})();

(function()
{
	createNamespace('TF.Modal').EditImageModalViewModel = EditImageModalViewModel;

	function EditImageModalViewModel(imageType, imageId, recordName, databaseId, imageData, options)
	{
		TF.Modal.BaseEditImageModalViewModel.call(this);
		this.obImageLabel("Image");
		this.title(tf.applicationTerm.getApplicationTermSingularByName("Edit Image"));
		this.sizeCss = 'modal-md';
		this.contentTemplate('modal/EditPhotoControl');
		this.buttonTemplate('modal/editPhotoPositiveNegative');
		this.EditPhotoViewModel = new TF.Control.EditImageViewModel(imageType, imageId, recordName, databaseId, imageData, options);
		this.data(this.EditPhotoViewModel);
	}

	EditImageModalViewModel.prototype = Object.create(TF.Modal.BaseEditImageModalViewModel.prototype);
	EditImageModalViewModel.prototype.constructor = EditImageModalViewModel;

	EditImageModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.EditPhotoViewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
				else if (result === false)
				{
					this.negativeClose();
				}
			}.bind(this));
	};
})();

