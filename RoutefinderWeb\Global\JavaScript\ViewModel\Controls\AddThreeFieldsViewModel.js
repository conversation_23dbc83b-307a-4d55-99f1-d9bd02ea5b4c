﻿(function()
{
	createNamespace('TF.Control').AddThreeFieldsViewModel = AddThreeFieldsViewModel;

	function AddThreeFieldsViewModel(fieldName, id)
	{
		this.fieldName = fieldName;
		this.obEntityDataModel = ko.observable(new TF.DataModel.DisabilityCodeDataModel());
		this.obEntityDataModel().id(id);
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);

		this.obErrorMessageTitle = ko.observable("Error Occurred");
		this.obErrorMessageDescription = ko.observable("The following error occurred.");
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.isSaving = false;
	}

	AddThreeFieldsViewModel.prototype.save = function()
	{
		var validator = this.$form.data("bootstrapValidator");
		return this.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(valid)
			{
				if (!valid)
				{
					var messages = validator.getMessages(validator.getInvalidFields());
					var $fields = validator.getInvalidFields();
					var validationErrors = [];
					$fields.each(function(i, fieldData)
					{
						if (i == 0)
						{
							$(fieldData).focus();
						}
						var message = messages[i].replace('&lt;', '<').replace('&gt;', '>');
						if (message == " required")
						{
							message = " is required";
						}
						validationErrors.push({ name: ($(fieldData).attr('data-bv-error-name') ? $(fieldData).attr('data-bv-error-name') : $(fieldData).closest("div.form-group").find("label").text()), message: message, field: $(fieldData) });
					}.bind(this));
					if (validationErrors.length > 1)
					{
						this.obErrorMessageTitle("Errors Occurred");
						this.obErrorMessageDescription("The following errors occurred.");
					}
					else
					{
						this.obErrorMessageTitle("Error Occurred");
						this.obErrorMessageDescription("The following error occurred.");
					}
					this.obValidationErrors(validationErrors);
					this.obErrorMessageDivIsShow(true);
					return Promise.reject();
				}
				else
				{
					var isNew = this.obEntityDataModel().id() ? false : true;
					if (this.isSaving)
					{
						return;
					}
					this.isSaving = true;
					return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(this.fieldName)),
						{ data: [this.obEntityDataModel().toData()] })
						.then(function(data)
						{
							this.isSaving = false;
							PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
							return data.Items[0];// .obEntityDataModel().toData();
						}.bind(this)).catch(() =>
						{
							this.isSaving = false;
						});
				}
			}.bind(this));
	};

	AddThreeFieldsViewModel.prototype.init = function(viewModel, el)
	{
		var fieldName = this.fieldName;
		this.$form = $(el);

		var validatorFields = {}, isValidating = false, self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.code = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Code is required"
				},
				callback: {
					message: "Code must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(fieldName)), {
							paramData: {
								"@filter": "eq(Code," + value + ")"
							}
						}, { overlay: false })
							.then(function(apiResponse)
							{
								return !apiResponse.Items.some(function(item)
								{
									return item.Code.toLowerCase() == value.toLowerCase() && item.Id != this.obEntityDataModel().id();
								}.bind(this));
							}.bind(this));
					}.bind(this)
				}
			}
		};
		setTimeout(function()
		{
			$(el).bootstrapValidator({
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});
			self.pageLevelViewModel.load(self.$form.data("bootstrapValidator"));
		}, 1000);
		this.$form.find("input[name=code]").focus();
		this.load();
		this.LimitInput();
	};

	AddThreeFieldsViewModel.prototype.load = function()
	{
		if (this.obEntityDataModel().id())
		{
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(this.fieldName)), {
				paramData: { Id: this.obEntityDataModel().id() }
			})
				.then(function(data)
				{
					this.obEntityDataModel(new TF.DataModel.DisabilityCodeDataModel(data.Items[0]));
				}.bind(this));
		}

		this.$alert = this.$form.parent().find(".alertmessage");
	};

	AddThreeFieldsViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(data)
			{
				return data;
			}, function()
			{
			});
	};

	AddThreeFieldsViewModel.prototype.LimitInput = function()
	{
		var $description = this.$form.find("input[name=description]");
		$description.attr("maxlength", 32);

		var $comments = this.$form.find("textarea[name=comments]");
		$comments.attr("maxlength", 128);
	};

	AddThreeFieldsViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

	AddThreeFieldsViewModel.prototype.focusField = function(viewModel)
	{
		if (viewModel.field)
		{
			$(viewModel.field).focus();
		}
	};

})();

