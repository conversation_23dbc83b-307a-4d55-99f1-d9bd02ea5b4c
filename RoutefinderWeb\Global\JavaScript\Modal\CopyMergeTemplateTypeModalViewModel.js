(function()
{
	createNamespace("TF.Modal").CopyMergeTemplateTypeModalViewModel = CopyMergeTemplateTypeModalViewModel;

	function CopyMergeTemplateTypeModalViewModel(newRecordModel)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Copy Document Layout");
		this.sizeCss = "modal-dialog-sm";
		this.contentTemplate("workspace/controlpanel/modal/copymergetemplatetype");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Copy");
		var copyMergeTemplateTypeViewModel = new TF.Control.CopyMergeTemplateTypeViewModel(newRecordModel);
		this.data(copyMergeTemplateTypeViewModel);
	}

	CopyMergeTemplateTypeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	CopyMergeTemplateTypeModalViewModel.prototype.constructor = CopyMergeTemplateTypeModalViewModel;

	CopyMergeTemplateTypeModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		var self = this;
		self.negativeClose();
		return true;
	};

	CopyMergeTemplateTypeModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.data().apply().then(function(success)
		{
			if (!success) return;

			var newData = self.data().obEntityDataModel().toData();
			if (newData.id == null)
			{
				delete newData.Id;
			}
			return tf.promiseAjax.post(
				pathCombine(tf.api.apiPrefixWithoutDatabase(), 'mergetemplatetypes'),
				{
					data: [newData]
				}).then(function(ret)
				{
					self.positiveClose();
				}).catch(function(error) 
				{
					tf.promiseBootbox.alert("Copy New Merge Document Layout Type Failed", "Warning");
					return false;
				});
		});
	};

	CopyMergeTemplateTypeModalViewModel.prototype.dispose = function()
	{
		if (this.copyMergeTemplateTypeViewModel && this.copyMergeTemplateTypeViewModel.dispose)
		{
			this.copyMergeTemplateTypeViewModel.dispose();
		}

	};
})();


