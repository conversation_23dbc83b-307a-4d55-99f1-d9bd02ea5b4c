(function()
{
	createNamespace('TF.Control').StateReportRunResultViewModel = StateReportRunResultViewModel;
	var fileTypeEnum = {
		studentFile: 0,
		tripFile: 1,
		errorFile: 2,
		warningFile: 3,
		zip: -1
	}
	function StateReportRunResultViewModel(option)
	{
		var self = this;
		self.enableDetailDownload = ko.observable(false);
		self.contextId = option.Id;
		self.obSummarizationText = ko.observable(option && option.Summary)
	}
	StateReportRunResultViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		if (self.contextId)
		{
			var pref = pathCombine(tf.api.apiPrefixWithoutDatabase(), 'drtrsprocessfiles') + `?contextId=${this.contextId}&fileType=`;
			self.obStudentFile = ko.observable(pref + fileTypeEnum.studentFile);
			self.obTripFile = ko.observable(pref + fileTypeEnum.tripFile)
			self.obErrorLogFile = ko.observable(pref + fileTypeEnum.errorFile)
			self.obWarningFile = ko.observable(pref + fileTypeEnum.warningFile)
		}
	}

	StateReportRunResultViewModel.prototype.downloadzip = function()
	{
		var zipUrl = pathCombine(tf.api.apiPrefixWithoutDatabase(), 'drtrsprocessfiles') + `?contextId=${this.contextId}&fileType=${fileTypeEnum.zip}`;
		window.open(zipUrl);
	}
})()