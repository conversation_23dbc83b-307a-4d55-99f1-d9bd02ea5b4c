(function()
{
	createNamespace("TF.Modal").CopyDataSourceMappingSettingModalViewModel = CopyDataSourceMappingSettingModalViewModel;

	function CopyDataSourceMappingSettingModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);

		this.contentTemplate('modal/CopyDataSourceMappingSetting');
		this.buttonTemplate('modal/positivenegative');
		this.title(`Copy Mapping From ${options.sourceDataSourceName}`);
		this.sizeCss = "modal-dialog-md";
		this.obPositiveButtonLabel("Apply");
		this.copyDataSourceMappingSettingViewModel = new TF.Control.CopyDataSourceMappingSettingViewModel(options);
		this.data(this.copyDataSourceMappingSettingViewModel);
	}

	CopyDataSourceMappingSettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	CopyDataSourceMappingSettingModalViewModel.prototype.constructor = CopyDataSourceMappingSettingModalViewModel;

	CopyDataSourceMappingSettingModalViewModel.prototype.positiveClick = function()
	{
		this.copyDataSourceMappingSettingViewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
			}.bind(this));
	};

	CopyDataSourceMappingSettingModalViewModel.prototype.negativeClick = function()
	{
		this.copyDataSourceMappingSettingViewModel.cancel()
			.then(function()
			{
				this.negativeClose();
			}.bind(this));
	};
})();