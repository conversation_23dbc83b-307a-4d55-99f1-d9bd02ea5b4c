﻿(function()
{
	createNamespace('TF.Control').ListMoverSelectTripStopViewModel = ListMoverSelectTripStopViewModel;
	function ListMoverSelectTripStopViewModel(selectedData, options)
	{
		options.getUrl = function(gridType)
		{
			return pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint("tripstop"));
		};
		options.serverPaging = true;
		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
	}

	ListMoverSelectTripStopViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectTripStopViewModel.prototype.constructor = ListMoverSelectTripStopViewModel;

	ListMoverSelectTripStopViewModel.prototype.columnSources = {
		tripstop: [
			{
				FieldName: "Name",
				DisplayName: "Trip Name",
				Width: '60px',
				type: "string"
			},
			{
				FieldName: "Street",
				DisplayName: "Street",
				Width: '60px',
				type: "string"
			},
			{
				FieldName: "StopTime",
				DisplayName: "Stop Time",
				Width: '30px',
				type: "string"
			}
		]
	};

	ListMoverSelectTripStopViewModel.prototype.getFields = function()
	{
		return ["Id", "Name", "Street", "StopTime"];
	};

	ListMoverSelectTripStopViewModel.prototype.apply = function()
	{
		// Todo: input data struct (studentDataModel) different with output data struct (subClass),  need convert data
		// TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this);
		return TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this).then(function(selectedData)
		{
			if (this.options.mustSelect && this.selectedData.length === 0)
			{
				return null;
			}

			return selectedData;
		}.bind(this));
	};

	ListMoverSelectTripStopViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (!isArraySame(this.oldData, this.selectedData))
			{
				return tf.promiseBootbox.yesNo("You have unsaved changes.  Are you sure you want to cancel?", "Confirmation Message").then(function(result)
				{
					if (result)
					{
						resolve(true);
					}
					else
					{
						reject();
					}
				});
			} else
			{
				resolve(true);
			}
		}.bind(this));
	};


	ListMoverSelectTripStopViewModel.prototype.afterInit = function()
	{
		this.$form.find(".checkbox.list-mover-grid-right-label").hide();
		this.$form.find(".checkbox.list-mover-grid-right-label").hide();
	}

	ListMoverSelectTripStopViewModel.prototype.onBeforeLeftGridDataBound = function(leftSearchGrid)
	{
	};

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var oldIds = oldData.map(function(item)
		{
			return item.Id;
		});
		var newIds = newData.map(function(item)
		{
			return item.Id;
		});
		var diffData1 = Enumerable.From(newIds).Where(function(x)
		{
			return !Array.contain(oldIds, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldIds).Where(function(x)
		{
			return !Array.contain(newIds, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();
