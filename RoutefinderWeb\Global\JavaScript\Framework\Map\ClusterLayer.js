define([
	"dojo/_base/declare",
	"dojo/_base/array",
	"esri/Color",
	"dojo/_base/connect",

	"esri/SpatialReference",
	"esri/geometry/Point",
	"esri/graphic",
	"esri/symbols/SimpleMarkerSymbol",
	"esri/symbols/TextSymbol",
	"esri/symbols/Font",
	'esri/graphicsUtils',

	"esri/dijit/PopupTemplate",
	"esri/layers/GraphicsLayer"
], function(
	declare, arrayUtils, Color, connect,
	SpatialReference, Point, Graphic, SimpleMarkerSymbol, TextSymbol, Font, graphicsUtils,
	PopupTemplate, GraphicsLayer
)
{
	return declare([GraphicsLayer], {
		constructor: function(options)
		{
			// options:
			//   data:  Object[]
			//     Array of objects. Required. Object are required to have properties named x, y and attributes. The x and y coordinates have to be numbers that represent a points coordinates.
			//   distance:  Number?
			//     Optional. The max number of pixels between points to group points in the same cluster. Default value is 50.
			//   labelColor:  String?
			//     Optional. Hex string or array of rgba values used as the color for cluster labels. Default value is #fff (white).
			//   labelOffset:  String?
			//     Optional. Number of pixels to shift a cluster label vertically. Defaults to -5 to align labels with circle symbols. Does not work in IE.
			//   resolution:  Number
			//     Required. Width of a pixel in map coordinates. Example of how to calculate: 
			//     map.extent.getWidth() / map.width
			//   showSingles:  Boolean?
			//     Optional. Whether or graphics should be displayed when a cluster graphic is clicked. Default is true.
			//   singleSymbol:  MarkerSymbol?
			//     Marker Symbol (picture or simple). Optional. Symbol to use for graphics that represent single points. Default is a small gray SimpleMarkerSymbol.
			//   singleTemplate:  PopupTemplate?
			//     PopupTemplate</a>. Optional. Popup template used to format attributes for graphics that represent single points. Default shows all attributes as "attribute = value" (not recommended).
			//   maxSingles:  Number?
			//     Optional. Threshold for whether or not to show graphics for points in a cluster. Default is 1000.
			//   webmap:  Boolean?
			//     Optional. Whether or not the map is from an ArcGIS.com webmap. Default is false.
			//   spatialReference:  SpatialReference?
			//     Optional. Spatial reference for all graphics in the layer. This has to match the spatial reference of the map. Default is 102100. Omit this if the map uses basemaps in web mercator.
			//   clusterLabelSize: Number?
			//     Optional. Number of the cluster label font size.
			//   clusterLevel: Number?
			//     Optional. Number of map zoom level.  When map zoom level is more than the number, do not cluster points.
			//   minimumClusterSize: Number?
			//     Optional. Minimum number of cluster point count.
			//   maximumSingleSize: Number?
			//     Optional. Maximum number of non-cluster point count.  Default is 10;

			this.isClusterLayer = true;
			this._clusterTolerance = options.distance || 50;
			this._clusterData = options.data || [];
			this._clusters = [];
			this._clusterLabelColor = options.labelColor || "#000";
			// labelOffset can be zero so handle it differently
			this._clusterLabelOffset = (options.hasOwnProperty("labelOffset")) ? options.labelOffset : -5;
			// graphics that represent a single point
			this._singles = []; // populated when a graphic is clicked
			this._showSingles = options.hasOwnProperty("showSingles") ? options.showSingles : true;
			// symbol for single graphics
			var SMS = SimpleMarkerSymbol;
			this._singleSym = options.singleSymbol || new SMS("circle", 6, null, new Color("#888"));
			this._singleTemplate = options.singleTemplate || new PopupTemplate({ "title": "", "description": "{*}" });
			if (options.singleTemplate == false)
			{
				this._singleTemplate = null;
			}

			this._webmap = options.hasOwnProperty("webmap") ? options.webmap : false;

			this._sr = options.spatialReference || new SpatialReference({ "wkid": 102100 });

			this._zoomEnd = null;
			this._panEnd = null;
			this._clusterLabelSize = options.labelSize || 12;
			this._clusterLevel = options.clusterLevel || 14;
			this._map = options._map;
			this._autoZoom = options.autoZoom === undefined ? true : options.autoZoom;
			this._minimumClusterSize = options.minimumClusterSize || 200;
			this._enableMouseClick = options.enableMouseClick;
			this._maximumSingleSize = options.maximumSingleSize || 10;

			// release objects
			for (var i in options)
			{
				options[i] = null;
			}
			options = null;
		},

		// override esri/layers/GraphicsLayer methods 
		_setMap: function(map, surface)
		{
			// calculate and set the initial resolution
			this._clusterResolution = map.extent.getWidth() / map.width; // probably a bad default...

			this._isBrokenCluster = true;

			if (!this._autoZoom)
			{
				this._setZoomEnd(map);
				this._setPanEnd(map);
				this._clusterGraphicDetails(this._map.getZoom());

				// GraphicsLayer will add its own listener here
				var div = this.inherited(arguments);

				return div;
			}

			this._initExtent();
			if (this._extent.xmax)
			{

				this._clusterGraphics().then(function()
				{
					this._setZoomEnd(map);
					this._setPanEnd(map);
				}.bind(this));
			}
			else
			{
				this._setZoomEnd(map);
				this._setPanEnd(map);
			}

			// GraphicsLayer will add its own listener here
			var div = this.inherited(arguments);

			return div;
		},

		_setZoomEnd: function(map)
		{
			// connect to onZoomEnd so data is re-clustered when zoom level changes
			this._zoomEnd = connect.connect(map, "onZoomEnd", this, function()
			{
				this._isBrokenCluster = true;

				// update resolution
				this._clusterResolution = this._map.extent.getWidth() / this._map.width;

				if (this.graphics.length > 0)
				{
					this.clear();
					var level = this._map.getLevel();
					this._clusterGraphicDetails(level);
				}

				this.setOpacity(1);
			});
		},

		_setPanEnd: function(map)
		{
			this._panEnd = connect.connect(map, "onPanEnd", this, function()
			{
				if (!this.visible) return;
				if (this.redrawPromise)
				{
					clearTimeout(this.redrawPromise);
				}

				this.redrawPromise = setTimeout(function()
				{
					this._isBrokenCluster = true;
					this._clusterResolution = this._map.extent.getWidth() / this._map.width;

					if (this.graphics.length > 0)
					{
						this.clear();
						var level = this._map.getLevel();
						this._clusterGraphicDetails(level);
					}

					this.setOpacity(1);
					this.redrawPromise = null;

				}.bind(this), 1000);
			});
		},

		_unsetMap: function()
		{
			this.inherited(arguments);
			connect.disconnect(this._zoomEnd);
			connect.disconnect(this._panEnd);
		},

		// public ClusterLayer methods
		add: function(p)
		{
			// Summary:  The argument is a data point to be added to an existing cluster. If the data point falls within an existing cluster, it is added to that cluster and the cluster's label is updated. If the new point does not fall within an existing cluster, a new cluster is created.
			//
			// if passed a graphic, use the GraphicsLayer's add method
			if (p.declaredClass)
			{
				this.inherited(arguments);
				p = null;

				return;
			}

			// add the new data to _clusterData so that it's included in clusters
			// when the map level changes
			this._clusterData.push(p);
			var clustered = false;
			// look for an existing cluster for the new point
			for (var i = 0; i < this._clusters.length; i++)
			{
				var c = this._clusters[i];
				if (this._clusterTest(p, c))
				{
					// add the point to an existing cluster
					this._clusterAddPoint(p, c);
					// update the cluster's geometry
					this._updateClusterGeometry(c);
					// update the label
					this._updateLabel(c);
					clustered = true;
					break;
				}
			}

			if (!clustered)
			{
				this._clusterCreate(p);
				p.attributes.clusterCount = 1;
				this._showCluster(p);
			}

			// release
			p = null;
		},

		clear: function()
		{
			// Summary:  Remove all clusters and data points.
			this.inherited(arguments);
			this._clusters.length = 0;
		},

		clearSingles: function(singles)
		{
			// Summary:  Remove graphics that represent individual data points.
			var s = singles || this._singles;
			arrayUtils.forEach(s, function(g)
			{
				this.remove(g);
			}, this);
			this._singles.length = 0;
		},

		onClick: function(e)
		{
			if (!this._enableMouseClick) { return; }

			var graphic = e.graphic;

			if (!graphic.attributes.hasOwnProperty("clusterCount") || graphic.attributes.clusterCount == 1)
			{
				e.stopPropagation();
				graphic = null;
				return;
			}
			// UX for map
			this.setOpacity(0);

			// remove any previously showing single features
			this.clearSingles(this._singles);

			this.clear();

			// find single graphics that make up the cluster that was clicked
			// would be nice to use filter but performance tanks with large arrays in IE
			var singles = [];
			for (var i = 0, il = this._clusterData.length; i < il; i++)
			{
				if (graphic.attributes.clusterId == this._clusterData[i].attributes.clusterId)
				{
					singles.push(this._clusterData[i]);
				}
			}

			// stop the click from bubbling to the map
			e.stopPropagation();

			this._addPoints(singles);

			// distingish cluster points with singles
			e.afterCluster = (singles.length > 1);

			graphic = null;
		},

		_initExtent: function()
		{
			var graphics = [],
				extent = {
					getWidth: function()
					{
						return 0;
					},
					getHeight: function()
					{
						return 0;
					}
				};
			if (this._clusterData.length > 0)
			{
				this._clusterData.forEach(function(p)
				{
					var g = new Graphic({ geometry: new Point(p.x, p.y, this._sr) });
					graphics.push(g);
				}.bind(this));
			}
			if (graphics.length > 0)
			{
				extent = graphicsUtils.graphicsExtent(graphics);
				//this._map.setExtent(extent, true);
			}
			this._extent = extent;
		},
		// internal methods 
		_clusterGraphics: function()
		{
			return this._map.setExtent(this._extent, true)
				.then(function()
				{
					var level = this._map.getLevel();
					this._clusterGraphicDetails(level);
				}.bind(this), function(err)
				{
					console.log(err);
				}.bind(this), function(update)
				{
					console.log(err);
				}.bind(this));
		},

		_clusterGraphicDetails: function(level)
		{
			// first time through, loop through the points
			for (var j = 0, jl = this._clusterData.length; j < jl; j++)
			{
				// see if the current feature should be added to a cluster
				var point = this._clusterData[j];
				if (point.symbol.hideOnMap)
				{
					continue;
				}
				var clustered = false;
				if (level < this._clusterLevel && jl >= this._minimumClusterSize)
				{
					var numClusters = this._clusters.length;
					for (var i = 0; i < numClusters; i++)
					{
						var c = this._clusters[i];
						if (this._clusterTest(point, c))
						{
							this._clusterAddPoint(point, c);
							clustered = true;
							c = null;
							break;
						}
						c = null;
					}
				}

				if (!clustered)
				{
					this._clusterCreate(point);
				}

				// release
				point = null;
			}
			this._showAllClusters();

		},

		_clusterTest: function(p, cluster)
		{
			var distance = (
				Math.sqrt(
					Math.pow((cluster.x - p.x), 2) + Math.pow((cluster.y - p.y), 2)
				) / this._clusterResolution
			);
			return (distance <= this._clusterTolerance);
		},

		// points passed to clusterAddPoint should be included 
		// in an existing cluster
		// also give the point an attribute called clusterId 
		// that corresponds to its cluster
		_clusterAddPoint: function(p, cluster)
		{
			// average in the new point to the cluster geometry
			var count, x, y;
			count = cluster.attributes.clusterCount;
			x = (p.x + (cluster.x * count)) / (count + 1);
			y = (p.y + (cluster.y * count)) / (count + 1);
			//Once the cluster point is dynamic, the distance test may not correct, so fix the point.
			//cluster.x = x;
			//cluster.y = y;

			// build an extent that includes all points in a cluster
			// extents are for debug/testing only...not used by the layer
			if (p.x < cluster.attributes.extent[0])
			{
				cluster.attributes.extent[0] = p.x;
			} else if (p.x > cluster.attributes.extent[2])
			{
				cluster.attributes.extent[2] = p.x;
			}
			if (p.y < cluster.attributes.extent[1])
			{
				cluster.attributes.extent[1] = p.y;
			} else if (p.y > cluster.attributes.extent[3])
			{
				cluster.attributes.extent[3] = p.y;
			}

			// increment the count
			cluster.attributes.clusterCount++;
			// attributes might not exist
			if (!p.hasOwnProperty("attributes"))
			{
				p.attributes = {};
			}
			// give the graphic a cluster id
			p.attributes.clusterId = cluster.attributes.clusterId;

			// release
			cluster = null;
		},

		// point passed to clusterCreate isn't within the 
		// clustering distance specified for the layer so
		// create a new cluster for it
		_clusterCreate: function(p)
		{
			var clusterId = this._clusters.length + 1;
			// console.log("cluster create, id is: ", clusterId);
			// p.attributes might be undefined
			if (!p.attributes)
			{
				p.attributes = {};
			}
			p.attributes.clusterId = clusterId;
			// create the cluster
			var cluster = {
				"x": p.x,
				"y": p.y,
				"symbol": p.symbol,
				"attributes": $.extend(p.attributes, {
					"clusterCount": 1,
					"clusterId": clusterId,
					"extent": [p.x, p.y, p.x, p.y]

				})
			};
			this._clusters.push(cluster);

			cluster = null;
			p = null;
		},

		_showAllClusters: function()
		{
			var clusters = this._clusters,
				clusterCount = clusters.length,
				clusterData = this._clusterData,
				clusterDataCount = clusterData.length,
				MAX_SIZE = this._maximumSingleSize,
				c, data, singles, i, j;

			if (this._isBrokenCluster)
			{
				// show single point when the cluster count is less than maximumSingleSize
				for (i = 0; i < clusterCount; i++)
				{
					c = clusters[i];
					if (c.attributes.clusterCount <= MAX_SIZE)
					{
						if (c.attributes.clusterCount == 1)
						{
							this._addPoints([c]);
						} else
						{
							// when cluster count between 2 and MAX_SIZE, should be broken
							singles = [];
							for (j = 0; j < clusterDataCount; j++)
							{
								data = clusterData[j];
								if (c.attributes.clusterId == data.attributes.clusterId)
								{
									data.attributes.clusterCount = 1;
									singles.push(data);
								}
								data = null;
							}

							this._addPoints(singles);

							singles = null;
						}
					} else
					{
						this._showCluster(c);
					}
					c = null;
				}

				this._isBrokenCluster = false;
			} else
			{
				for (i = 0; i < clusterCount; i++)
				{
					c = clusters[i];
					this._showCluster(c);
					c = null;
				}
			}

			clusters = null;
			clusterData = null;
		},

		_showCluster: function(c)
		{
			var point = new Point(c.x, c.y, this._sr);
			this.add(new Graphic({
				geometry: point,
				symbol: c.attributes.clusterCount == 1 ? c.symbol : null,
				attributes: c.attributes
			}));
			// code below is used to not label clusters with a single point
			if (c.attributes.clusterCount == 1)
			{
				c = null;
				point = null;
				return;
			}

			// show number of points in the cluster
			var label = new TextSymbol(c.attributes.clusterCount.toString())
				.setColor(new Color(this._clusterLabelColor))
				.setOffset(0, this._clusterLabelOffset)
				.setFont(new Font(this._clusterLabelSize, Font.STYLE_NORMAL,
					Font.VARIANT_NORMAL, Font.WEIGHT_BOLD, "sans-serif"));  // add Font size
			this.add(new Graphic({
				geometry: point,
				symbol: label,
				attributes: c.attributes
			}));

			// release
			c = null;
			label = null;
			point = null;
		},

		_addPoints: function(points)
		{
			// add single graphics to the map
			arrayUtils.forEach(points, function(p)
			{
				var g = new Graphic({
					geometry: new Point(p.x, p.y, this._sr),
					symbol: p.symbol,
					attributes: p.attributes,
					popupTemplate: this._singleTemplate
				});
				this._singles.push(g);
				if (this._showSingles)
				{
					this.add(g);
				}
				g = null;
			}, this);

			if (!this._isBrokenCluster && this._singles.length > this._maximumSingleSize)
			{
				this._zoomToExtent(this._singles);
			}
			// release
			points = null;
		},

		_zoomToExtent: function(graphics)
		{
			// zoom to this._singles extent. ViewFinder - Leo Deng.11082016
			var extent = graphicsUtils.graphicsExtent(graphics);
			this._map.setExtent(extent, true);

			// release
			extent = null;
		},

		_updateClusterGeometry: function(c)
		{
			// find the cluster graphic
			var cg = arrayUtils.filter(this.graphics, function(g)
			{
				return !g.symbol &&
					g.attributes.clusterId == c.attributes.clusterId;
			});
			if (cg.length == 1)
			{
				cg[0].geometry.update(c.x, c.y);
			} else
			{
				console.log("didn't find exactly one cluster geometry to update: ", cg);
			}
		},

		_updateLabel: function(c)
		{
			// find the existing label
			var label = arrayUtils.filter(this.graphics, function(g)
			{
				return g.symbol &&
					g.symbol.declaredClass == "esri.symbol.TextSymbol" &&
					g.attributes.clusterId == c.attributes.clusterId;
			});
			if (label.length == 1)
			{
				this.remove(label[0]);
				var newLabel = new TextSymbol(c.attributes.clusterCount)
					.setColor(new Color(this._clusterLabelColor))
					.setOffset(0, this._clusterLabelOffset);
				this.add(
					new Graphic({
						geometry: new Point(c.x, c.y, this._sr),
						symbol: newLabel,
						attributes: c.attributes
					})
				);
			}
		},

		disableClickEvent: function()
		{
			this._enableMouseClick = false;
		},

		enableClickEvent: function()
		{
			this._enableMouseClick = true;
		},

		setClusterTolerance: function(value)
		{
			this._clusterTolerance = value;
		},

	});
});

