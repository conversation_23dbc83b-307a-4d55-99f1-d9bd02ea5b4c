﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.TripPolicyDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	};

	namespace.TripPolicyDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.TripPolicyDataModel.prototype.constructor = namespace.TripPolicyDataModel;

	namespace.TripPolicyDataModel.prototype.mapping = [
		{ from: "From", default: "" },
		{ from: "To", default: "" },
		{ from: "EfficiencyPercentage", default: 0 },
		{ from: "MaxPercentage", default: 0 },
		{ from: "MaxStud", default: 0 },
		{ from: "DaysPerYear", default: 0 },
		{ from: "VehicleStopTime", default: 10 },
		{ from: "DBID" },
		{ from: "ID" }
	];

})();
