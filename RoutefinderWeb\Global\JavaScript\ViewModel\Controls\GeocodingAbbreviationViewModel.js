(function()
{
	createNamespace('TF.Control').GeocodingAbbreviationViewModel = GeocodingAbbreviationViewModel;

	function GeocodingAbbreviationViewModel(fieldName, modal, uniqueChecking)
	{
		this.fieldName = fieldName;
		this.uniqueChecking = uniqueChecking;
		this.obName = ko.observable();
		this.obDescriptionVisible = ko.observable(true);
		this.obDescription = ko.observable("Description");//description might be another name either.
		this.obDescriptionRequired = ko.observable(false);
		this.entityDataModel = null;
		switch (fieldName)
		{
			case 'geocodingabbreviation':
				this.entityDataModel = TF.DataModel.GeocodingAbbreviationDataModel;
				this.obName("Term");
				this.obDescription("Convert To");
				this.obDescriptionRequired(true);
				break;
			default:
				return;
		}

		this.obEntityDataModel = ko.observable(new this.entityDataModel(modal));
		this.obEntityDataModel().apiIsDirty(false);

		this.pageLevelViewModel = new TF.PageLevel.GeocodingAbbreviationPageViewModel();
		if (modal)
		{
			this.TempId = modal.TempId;
		}

		ko.computed(this.typeChangeComputer, this);
	}

	GeocodingAbbreviationViewModel.prototype.typeChangeComputer = function()
	{
		if (this.obEntityDataModel().typeString() == "Replace" || this.obEntityDataModel().typeString() == "!SPACE")
		{
			this.obName("Convert From");
			this.obDescriptionVisible(true);
		}
		else if (this.obEntityDataModel().typeString() == "!EOLNOSPACE")
		{
			this.obName("Character");
			this.obDescriptionVisible(false);
		}
		else
		{
			this.obName("Characters");
			this.obDescriptionVisible(false);
		}
		if (this.pageLevelViewModel._validator)
		{
			this.setValidatorFields();
		}
		switch (this.obEntityDataModel().typeString())
		{
			case "Replace":
				this.obEntityDataModel().type(1);
				break;
			case "!SPACE":
				this.obEntityDataModel().type(2);
				break;
			case "!EOLNOSPACE":
				this.obEntityDataModel().type(3);
				break;
			case "!ELOSPACE":
				this.obEntityDataModel().type(4);
				break;
			case "!NOSPACE":
				this.obEntityDataModel().type(5);
				break;
		}
	};

	GeocodingAbbreviationViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate()
		.then(function(result)
		{
			if (result)
			{
				if (this.obEntityDataModel().typeString() != "Replace" && this.obEntityDataModel().typeString() != "!SPACE")
				{
					this.obEntityDataModel().convertTo("not applicable");
				}
				return this.obEntityDataModel().toData();
			}
		}.bind(this));
	}

	GeocodingAbbreviationViewModel.prototype.init = function(viewModel, el)
	{
		var fieldName = this.fieldName;
		this.$form = $(el);

		var self = this, validatorFields = {},
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.name = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: "must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						return !this.uniqueChecking("term", value, this.TempId);
					}.bind(this)
				}
			}
		}

		if (this.fieldName == "geocodingabbreviation")
		{
			//add convert to validation either.
			validatorFields.description = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					}
				}
			}
		}
		this.wholeValidatorFields = validatorFields;
		this.initValidator();

		this.load();

		this.LimitInput();
	};

	GeocodingAbbreviationViewModel.prototype.initValidator = function()
	{
		var isValidating = false, self = this

		this.$form.bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: this.wholeValidatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});
	};

	GeocodingAbbreviationViewModel.prototype.setValidatorFields = function()
	{
		this.pageLevelViewModel.obValidationErrors.removeAll();
		if (this.obEntityDataModel().typeString() == "Replace" || this.obEntityDataModel().typeString() == "!SPACE")
		{
			var descriptionValidator = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					}
				}
			}
			this.pageLevelViewModel._validator.addField("description", descriptionValidator);
		}
		else
		{
			this.pageLevelViewModel._validator.removeField("description");
		}
	};

	GeocodingAbbreviationViewModel.prototype.afterInit = function()
	{
		this.$form.find("input[name=name]").focus();
	};

	GeocodingAbbreviationViewModel.prototype.load = function()
	{
		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	};

	GeocodingAbbreviationViewModel.prototype.apply = function()
	{
		return this.save()
		.then(function(data)
		{
			return data;
			this.dispose();
		}, function()
		{
		});
	};

	GeocodingAbbreviationViewModel.prototype.LimitInput = function()
	{
		switch (this.fieldName)
		{
			case 'geocodingabbreviation':
				var $name = this.$form.find("input[name=name]");
				$name.attr("maxlength", 100);
				var $description = this.$form.find("input[name=description]");
				$description.attr("maxlength", 20);
				break;
		}
	};

	GeocodingAbbreviationViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

