(function()
{
	Tool = TF.RoutingMap.Locate.Tool;

	Tool.prototype._initSymbol = function()
	{
		var self = this;
		self._addressPointFill = '#D4E5FF';
		self._addressPointBorderColor = '#599BFF';
		self._addressPointBorderWidth = 1;
		self._addressPointSize = 9;  // in pixels

		self._centerPointFill = [83, 138, 240, 1];
		self._centerPointBorderColor = [83, 138, 240, 0.3];
		self._centerPointBorderWidth = 20;
		self._centerPointSize = 12;
	};

	Tool.prototype._disposeSymbol = function()
	{
		var self = this;
		self._addressPointFill = null;
		self._addressPointBorderColor = null;
		self._addressPointBorderWidth = null;
		self._addressPointSize = null;

		self._centerPointFill = null;
		self._centerPointBorderColor = null;
		self._centerPointBorderWidth = null;
		self._centerPointSize = null;
	};

	Tool.prototype._addressSymbol = function()
	{
		var self = this;
		return self._arcgis.SimpleMarkerSymbol()
			.setStyle(self._arcgis.SimpleMarkerSymbol.STYLE_CIRCLE)
			.setSize(self._addressPointSize)
			.setColor(self._addressPointFill)
			.setOutline(new self._arcgis.SimpleLineSymbol().setColor(self._addressPointBorderColor).setWidth(self._addressPointBorderWidth));
	};

	Tool.prototype._mapCenterSymbol = function()
	{
		var self = this;
		return self._arcgis.SimpleMarkerSymbol()
			.setStyle(self._arcgis.SimpleMarkerSymbol.STYLE_CIRCLE)
			.setSize(self._centerPointSize)
			.setColor(new self._arcgis.Color(self._centerPointFill))
			.setOutline(new self._arcgis.SimpleLineSymbol().setColor(new self._arcgis.Color(self._centerPointBorderColor)).setWidth(self._centerPointBorderWidth));
	};
})();