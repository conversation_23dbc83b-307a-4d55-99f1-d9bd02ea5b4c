(function()
{
	createNamespace("TF.Control.ResourceScheduler").SubstituteResourcesViewModel = SubstituteResourcesViewModel;

	function SubstituteResourcesViewModel(entityIds, options)
	{
		var self = this;
		self.options = options;
		self.obPageDescription = ko.observable("Specify on which dates and days of the week you would like to substitute resources for.");
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		self.dateTimeFormat = 'YYYY-MM-DDT00:00:00';
		self.obBusAides = ko.observableArray([]);
		self.obSelectedBusAideName = ko.observable('');
		self.obSelectedBusAide = ko.observable();
		self.obDrivers = ko.observableArray([]);
		self.obSelectedDriverName = ko.observable('');
		self.obSelectedDriver = ko.observable();
		self.obVehicles = ko.observableArray([]);
		self.obSelectedVehicleName = ko.observable('');
		self.obSelectedVehicle = ko.observable();
		self.dataRange = ko.observable();
		self.entityIds = entityIds;
		self.tripOriginalResources = [];
		self.isEdit = options && options.isEdit || false;
		self.supportAssign = options && options.supportAssign || false;
		self.needAssign = ko.observable(false);
		self.fromDate = ko.observable();
		self.toDate = ko.observable();
		if (self.isEdit)
		{
			self.fromDate(moment(options.startDate).format("YYYY-MM-DD"));
			self.toDate(moment(options.endDate).format("YYYY-MM-DD"));
		}
		else
		{
			let clientTimeZoneDate = utcToClientTimeZone(moment().utc()).format("YYYY-MM-DD");
			self.fromDate(clientTimeZoneDate);
			self.toDate(clientTimeZoneDate);
		}

		self.adjustTime = 0;
		self.startTimeArray = [];
		self.endTimeArray = [];
		self.adjustTimeArray = [];

		self.vehicleMapping = {};
		self.busNumMapping = {};
		self.driverMapping = {};
		self.busAideMapping = {};
		self.currentVehicle = ko.observable();
		self.currentVehicleId = ko.observable(0);
		self.currentDriver = ko.observable();
		self.currentDriverId = ko.observable(0);
		self.currentBusAide = ko.observable();
		self.currentBusAideId = ko.observable(0);
		self.vehicleList = [];
		self.driverList = [];
		self.busaideList = [];
		self.conflictTripResources;

		self.weekday = ko.observable(false);
		self.days = {
			Sunday: ko.observable(false),
			Monday: ko.observable(false),
			Tuesday: ko.observable(false),
			Wednesday: ko.observable(false),
			Thursday: ko.observable(false),
			Friday: ko.observable(false),
			Saturday: ko.observable(false)
		};
		self.daysEnable = {
			Sunday: ko.observable(false),
			Monday: ko.observable(false),
			Tuesday: ko.observable(false),
			Wednesday: ko.observable(false),
			Thursday: ko.observable(false),
			Friday: ko.observable(false),
			Saturday: ko.observable(false)
		};
		self.initDaysStatus();

		self.obEnableBusAide = ko.observable(false);
		self.obEnableDriver = ko.observable(false);
		self.obEnableVehicle = ko.observable(false);

		self.dbId = tf.storageManager.get("datasourceId", true, true) || tf.storageManager.get("datasourceId");
		self.type = options && options.type;
		this.isResourceChanged = ko.computed(() =>
		{
			if (self.obEnableBusAide() || self.obEnableDriver() || self.obEnableVehicle())
			{
				return true;
			}

			return false;
		});
	}

	SubstituteResourcesViewModel.prototype = Object.create(TF.Control.BaseControl.prototype);
	SubstituteResourcesViewModel.prototype.constructor = SubstituteResourcesViewModel;

	SubstituteResourcesViewModel.prototype.DAY_MILLISECOND = 1000 * 60 * 60 * 24;

	SubstituteResourcesViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		var validatorFields = {}, dateFormat = "MM/DD/YYYY";
		this._$form = $(el);
		this.loadResources().then(function(response)
		{
			this.prepareResource(response);
			this.loadTrips().then(function(result)
			{
				this.conflictTripResources = result[1].Items;
				this.generateResource();
				this.setDefaultValue();
				this.initTimesData();
			}.bind(this));
		}.bind(this));
		setTimeout(function()
		{
			validatorFields.FromDate = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					date: {
						message: 'invalid date',
						format: dateFormat
					},
					callback: {
						message: "",
						callback: function(value)
						{
							if (value != "")
							{
								let startDate = new moment(value);
								if (self.toDate())
								{
									var endDate = new moment(self.toDate());
									if (endDate.isBefore(startDate))
									{
										return {
											message: " must <= To Date",
											valid: false
										};
									}
								}

								if (!self.isEdit && startDate.isBefore(moment(utcToClientTimeZone(moment().utc()).format('YYYY-MM-DD'))))
								{
									return {
										message: " must >= Today",
										valid: false
									};
								}
							}
							return true;
						}
					}
				}
			};
			validatorFields.ToDate = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					date: {
						message: 'invalid date',
						format: dateFormat
					},
					callback: {
						message: "",
						callback: function(value)
						{
							if (value != "")
							{
								if (self.fromDate())
								{
									var startDate = new moment(self.fromDate());
									var endDate = new moment(value);
									if (endDate.isBefore(startDate))
									{
										return {
											message: " must >= From Date",
											valid: false
										};
									}
									return true;
								}
							}
							return true;
						}
					}
				}
			};
			validatorFields.DateRange = {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "Days of the week are required"
					}
				}
			};

			$(el).bootstrapValidator({
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			});

			this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
			this.datePickerChangeEventBinding();
			this.updateDays();

		}.bind(this));
	};

	SubstituteResourcesViewModel.prototype.setDefaultValue = function()
	{
		var self = this,
			selectedDriver = "[unassigned]", selectedVehicle = "[unassigned]", selectedBusAide = "[unassigned]",
			toDay = moment(self.toDate()), fromDay = moment(self.fromDate()),
			dateOffset = Math.abs((toDay - fromDay) / self.DAY_MILLISECOND), tempDate = moment(self.fromDate()), currentHistory;

		self.currentDriver("");
		self.currentBusAide("");
		self.currentVehicle("");
		self.obSelectedDriverName("");
		self.obSelectedBusAideName("");
		self.obSelectedVehicleName("");

		self.startTimeArray = [];
		self.endTimeArray = [];
		self.adjustTimeArray = [];

		var checkResources = function(data, type)
		{
			if (self.adjustTimeArray.find(item => item.Id === data.Id))
			{
				self.startTimeArray.find(item => item.Id === data.Id).Val = data.StartTime;
				self.endTimeArray.find(item => item.Id === data.Id).Val = data.EndTime || data.FinishTime;
				self.adjustTimeArray.find(item => item.Id === data.Id).Val = 0;
			}
			else
			{
				self.startTimeArray.push({ "Id": data.Id, "Val": data.StartTime });
				self.endTimeArray.push({ "Id": data.Id, "Val": data.EndTime || data.FinishTime });
				self.adjustTimeArray.push({ "Id": data.Id, "Val": 0 });
			}

			if (self.currentDriver() !== "Multiple Drivers")
			{
				if (!self.isMultipleAssigned(selectedDriver, data.DriverId))
				{
					if (self.isEdit && (+self.options.DriverId !== +data.DriverId))
					{
						self.obEnableDriver(true);
						selectedDriver = self.options.DriverId;
					}
					else
					{
						selectedDriver = data.DriverId;
					}

				}
				else
				{
					self.currentDriver("Multiple Drivers");
				}
			}

			if (self.currentBusAide() !== "Multiple Bus Aides")
			{
				if (!self.isMultipleAssigned(selectedBusAide, data.AideId))
				{
					if (self.isEdit && (+self.options.AideId !== +data.AideId))
					{
						self.obEnableBusAide(true);
						selectedBusAide = self.options.AideId;
					}
					else
					{
						selectedBusAide = data.AideId;
					}
				}
				else
				{
					self.currentBusAide("Multiple Bus Aides");
				}
			}

			if (self.currentVehicle() !== "Multiple Vehicles")
			{
				var vehicle = data.VehicleId;
				if (!self.isMultipleAssigned(selectedVehicle, vehicle))
				{
					if (self.isEdit && (+self.options.VehicleId !== +data.VehicleId))
					{
						self.obEnableVehicle(true);
						selectedVehicle = self.options.VehicleId;
					}
					else
					{
						selectedVehicle = vehicle;
					}
				}
				else
				{
					self.currentVehicle("Multiple Vehicles");
				}
			}
		};

		for (var i = 0; i <= dateOffset; i++)
		{
			tempDate.add(i === 0 ? 0 : 1, 'day');
			if (!self.days[tempDate.format('dddd')]())
			{
				continue;
			}

			$.each(self.tripOriginalResources, function(index, trip)
			{
				currentHistory = null;
				$.each(trip.TripResources, function(index, tripResource)
				{
					if (moment(tripResource.StartDate).format(self.dateTimeFormat) <= tempDate.format(self.dateTimeFormat) && tempDate.format(self.dateTimeFormat) <= moment(tripResource.EndDate).format(self.dateTimeFormat))
					{
						currentHistory = tripResource;
						return false;
					}
				});

				if (currentHistory)
				{
					checkResources(currentHistory, "history");
				}
				else
				{
					checkResources(trip, "trip");
				}
				if (self.allResourcesMultipleAssigned())
				{
					return false;
				}
			});

			if (self.allResourcesMultipleAssigned())
			{
				break;
			}
		}

		if (self.currentDriver() !== "Multiple Drivers")
		{
			self.currentDriver(self.driverMapping[selectedDriver]);
			self.obSelectedDriverName(self.driverMapping[selectedDriver]);
			self.currentDriverId(selectedDriver);
		}
		if (self.currentBusAide() !== "Multiple Bus Aides")
		{
			self.currentBusAide(self.busAideMapping[selectedBusAide]);
			self.obSelectedBusAideName(self.busAideMapping[selectedBusAide]);
			self.currentBusAideId(selectedBusAide);
		}
		if (self.currentVehicle() !== "Multiple Vehicles")
		{
			self.currentVehicle(self.busNumMapping[selectedVehicle]);
			self.obSelectedVehicleName(self.busNumMapping[selectedVehicle]);
			self.currentVehicleId(selectedVehicle);
		}

		self.generateInitEntity();
	}

	SubstituteResourcesViewModel.prototype.allResourcesMultipleAssigned = function()
	{
		return this.currentDriver() === "Multiple Drivers" && this.currentBusAide() === "Multiple Bus Aides" && this.currentVehicle() === "Multiple Vehicles";
	}

	SubstituteResourcesViewModel.prototype.isMultipleAssigned = function(selectedItem, newValue)
	{
		if (selectedItem === '[unassigned]' || selectedItem === newValue)
		{
			return false;
		}
		return true;
	}

	SubstituteResourcesViewModel.prototype.datePickerChangeEventBinding = function()
	{
		var self = this, startInput = this._$form.find("input[name=FromDate]"), endInput = this._$form.find("input[name=ToDate]");
		if (startInput.length > 0)
		{
			startInput.bind("change", function(e)
			{
				self.onFromDateChange(e);
			});
		}

		if (endInput.length > 0)
		{
			endInput.bind("change", function(e)
			{
				self.onToDateChange(e);
			});
		}
	};

	SubstituteResourcesViewModel.prototype.onFromDateChange = function(e)
	{
		const fromDate = e.currentTarget.value;
		if (fromDate)
		{
			var toDate = this.toDate(),
				Dateformat = toISOStringWithoutTimeZone(moment(fromDate));
			if (toDate !== "Invalid date"
				&& fromDate
				&& toDate
				&& fromDate > new Date(toDate))
			{
				this.fromDate(Dateformat);
				this.toDate(this.fromDate());
			}
			else
			{
				this.fromDate(Dateformat);
			}
			this.processUpdateDays();
		}
	};

	SubstituteResourcesViewModel.prototype.onToDateChange = function(e)
	{
		const toDate = e.currentTarget.value;
		if (toDate)
		{
			var fromDate = this.fromDate(),
				Dateformat = toISOStringWithoutTimeZone(moment(toDate));
			if (fromDate !== "Invalid date"
				&& toDate
				&& fromDate
				&& new Date(fromDate) > toDate)
			{
				this.toDate(Dateformat);
				this.fromDate(this.toDate());
			}
			else
			{
				this.toDate(Dateformat);
			}
			this.processUpdateDays();
		}
	};

	SubstituteResourcesViewModel.prototype.processUpdateDays = function()
	{
		var self = this;
		self.updateDays();
		self.loadTrips().then(function(result)
		{
			self.conflictTripResources = result[1].Items;
			self.generateResource();
			self.setDefaultValue();
		});
	}

	SubstituteResourcesViewModel.prototype.updateDays = function()
	{
		var self = this,
			dayName = "",
			toDay = moment(self.toDate()), fromDay = moment(self.fromDate()),
			dateOffset = (toDay - fromDay) / self.DAY_MILLISECOND, tempDate = moment(self.fromDate());
		dateOffset = dateOffset > 6 ? 6 : dateOffset;
		for (var key in self.days)
		{
			self.days[key](false);
			self.daysEnable[key](false);
		}

		for (var i = 0; i <= dateOffset; i++)
		{
			tempDate.add(i === 0 ? 0 : 1, 'day');
			dayName = tempDate.format('dddd');
			if (self.isEdit)
			{
				if (self.options.days[dayName])
				{
					self.days[dayName](true);
					self.daysEnable[dayName](true);
				}
			}
			else
			{
				self.days[dayName](true);
				self.daysEnable[dayName](true);
			}
		}
	};

	SubstituteResourcesViewModel.prototype.loadResources = function()
	{
		const authInfo = tf.authManager.authorizationInfo;
		var p0 = (authInfo.isAdmin || authInfo.authorizationTree.securedItems.vehicle) && tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles"));
		var p1 = (authInfo.isAdmin || authInfo.authorizationTree.securedItems.staff) && tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "staff?staffTypeId=2"));
		var p2 = (authInfo.isAdmin || authInfo.authorizationTree.securedItems.staff) && tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "staff?staffTypeId=1"));
		return Promise.all([p0, p1, p2]);
	};

	SubstituteResourcesViewModel.prototype.loadTrips = function()
	{
		var fromDate = moment(this.fromDate()).format(this.dateTimeFormat);
		var endDate = moment(this.toDate()).format(this.dateTimeFormat);
		var filter = this.type === 'routes' ? 'RouteId' : 'id';
		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "trips?@startTime=" + fromDate + "&@endTime=" + endDate + "&@relationships=TripResource&@filter=in(" + filter + "," + this.entityIds.join(",") + ")"))
			.then(function(trips)
			{
				this.tripOriginalResources = trips.Items;
			}.bind(this));

		var p2 = this.loadConflictTrip();
		return Promise.all([p1, p2]);
	};

	SubstituteResourcesViewModel.prototype.loadConflictTrip = function()
	{
		var fromDate = moment(this.fromDate()).format(this.dateTimeFormat);
		var endDate = moment(this.toDate()).format(this.dateTimeFormat);
		var urlSuffix = this.type === 'routes' ? "trips/conflict?routeIds=" + this.entityIds.join(",") + "&@startTime=" + fromDate + "&@endTime=" + endDate + "&@relationships=TripResource" : "trips?tripIds=" + this.entityIds.join(",") + "&@startTime=" + fromDate + "&@endTime=" + endDate + "&@onlyConflictTrip=true" + "&@relationships=TripResource";
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), urlSuffix));
	};

	SubstituteResourcesViewModel.prototype.prepareResource = function(data)
	{
		// prepare the data
		var vehicleData = data[0],
			driverData = data[1],
			aideData = data[2];

		this.vehicleList = (vehicleData && Array.isArray(vehicleData.Items)) ? vehicleData.Items.filter(function(item)
		{
			return !!$.trim(item.BusNum);
		}) : [];
		$.each(this.vehicleList, function(index, item)
		{
			this.vehicleMapping[item.BusNum.trim()] = item.Id;
			this.busNumMapping[item.Id] = item.BusNum.trim();
		}.bind(this));

		this.driverList = (driverData && Array.isArray(driverData.Items)) ? driverData.Items.filter(function(item)
		{
			return !!$.trim(item.FullName);
		}) : [];
		$.each(this.driverList, function(index, item)
		{
			item.FullName = this.concatFullName(item);
		}.bind(this));
		$.each(this.driverList, function(index, item)
		{
			this.driverMapping[item.Id] = item.FullName;
		}.bind(this));

		this.busaideList = (aideData && Array.isArray(aideData.Items)) ? aideData.Items.filter(function(item)
		{
			return !!$.trim(item.FullName);
		}) : [];
		$.each(this.busaideList, function(index, item)
		{
			item.FullName = this.concatFullName(item);
		}.bind(this));
		$.each(this.busaideList, function(index, item)
		{
			this.busAideMapping[item.Id] = item.FullName;
		}.bind(this));
	};

	SubstituteResourcesViewModel.prototype.generateResource = function()
	{
		var conflictTripResources = this.conflictTripResources;
		// definition variable for vehicle
		var conflictVehicleList = [],
			notConflictVehicleList = [],
			conflictVehicleIdList = [],
			vehicleItems = [];

		// definition variable for driver
		var conflictDriverList = [],
			notConflictDriverList = [],
			conflictDriverIdList = [],
			driverItems = [];

		// definition variable for bus aide
		var conflictAideList = [],
			notConflictAideList = [],
			conflictAideIdList = [],
			busaideItems = [];

		// 1. if the trip exist trip history then traverse the trip history to get conflict data ids.
		// 2. if the trip not exist trip history then get conflict data ids from the trip
		if (conflictTripResources.length > 0)
		{
			conflictTripResources.forEach(function(item)
			{
				var histories = item.TripResources;
				if (histories.length > 0)
				{
					histories.forEach(function(tripHistory)
					{
						var VehicleId = 0;
						VehicleId = tripHistory.VehicleId;

						// get conflict vehicle ids.
						if (conflictVehicleIdList.indexOf(VehicleId) === -1 && VehicleId > 0)
						{
							conflictVehicleIdList.push(VehicleId);
						}

						// get conflict driver ids.
						if (conflictDriverIdList.indexOf(tripHistory.DriverId) === -1 && tripHistory.DriverId > 0)
						{
							conflictDriverIdList.push(tripHistory.DriverId);
						}

						// get conflict bus aide ids.
						if (conflictAideIdList.indexOf(tripHistory.AideId) === -1 && tripHistory.AideId > 0)
						{
							conflictAideIdList.push(tripHistory.AideId);
						}

					}.bind(this));
				} else
				{
					// get conflict resource ids from original trip.
					if (conflictVehicleIdList.indexOf(item.VehicleId) === -1 && item.VehicleId > 0)
					{
						conflictVehicleIdList.push(item.VehicleId);
					}

					if (conflictDriverIdList.indexOf(item.DriverId) === -1 && item.DriverId > 0)
					{
						conflictDriverIdList.push(item.DriverId);
					}

					if (conflictAideIdList.indexOf(item.AideId) === -1 && item.AideId > 0)
					{
						conflictAideIdList.push(item.AideId);
					}
				}
			}.bind(this));

			// traverse original vehicle to depart conflict vehicle and not conflict vehicle.
			this.vehicleList.forEach(function(value)
			{
				if (conflictVehicleIdList.indexOf(value.Id) !== -1)
				{
					conflictVehicleList.push(value);
				} else
				{
					notConflictVehicleList.push(value);
				}
			});

			// traverse original driver to depart conflict driver and not conflict driver.
			this.driverList.forEach(function(value)
			{
				if (conflictDriverIdList.indexOf(value.Id) !== -1)
				{
					conflictDriverList.push(value);
				} else
				{
					notConflictDriverList.push(value);
				}
			});

			// traverse original bus aide to depart conflict aide and not conflict aide.
			this.busaideList.forEach(function(value)
			{
				if (conflictAideIdList.indexOf(value.Id) !== -1)
				{
					conflictAideList.push(value);
				} else
				{
					notConflictAideList.push(value);
				}
			});
		}
		else
		{
			// if no trip conflict with current trips, all resource will be original resource.
			notConflictVehicleList = this.vehicleList;
			notConflictDriverList = this.driverList;
			notConflictAideList = this.busaideList;
		}

		// generate vehicle drop down list source
		vehicleItems = [{ BusNum: "[disable]-------No Conflicts-------" }]
			.concat(sortArray(notConflictVehicleList, "BusNum"));
		vehicleItems = conflictVehicleList.length > 0 ? vehicleItems.concat([{ BusNum: "[disable]-------Conflicts-------" }])
			.concat(sortArray(conflictVehicleList, "BusNum")) : vehicleItems.concat([]);
		this.obVehicles(vehicleItems);

		// generate driver drop down list source
		driverItems = [{ FullName: "[disable]-------No Conflicts-------" }]
			.concat(sortArray(notConflictDriverList, "FullName"));
		driverItems = conflictDriverList.length > 0 ? driverItems.concat([{ FullName: "[disable]-------Conflicts-------" }])
			.concat(sortArray(conflictDriverList, "FullName")) : driverItems.concat([]);
		this.obDrivers(driverItems);

		// generate bus aide drop down list source
		busaideItems = [{ FullName: "[disable]-------No Conflicts-------" }]
			.concat(sortArray(notConflictAideList, "FullName"));
		busaideItems = conflictAideList.length > 0 ? busaideItems.concat([{ FullName: "[disable]-------Conflicts-------" }])
			.concat(sortArray(conflictAideList, "FullName")) : busaideItems.concat([]);
		this.obBusAides(busaideItems);
	};

	SubstituteResourcesViewModel.prototype.afterSelect = function(obValue, selectValue)
	{
		if (selectValue && selectValue !== "")
		{
			obValue(true);
		}
	};

	SubstituteResourcesViewModel.prototype.initTimesData = function()
	{
		var self = this;
		if (self.isEdit && self.options && self.options.startTime)
		{
			self.adjustTime = self.minutesDiff(self.initEntity.startTime[0].Val, self.options.startTime);
		}
	};

	SubstituteResourcesViewModel.prototype.initDaysStatus = function()
	{
		var self = this;
		if (self.options && self.options.days)
		{
			var days = self.options.days;
			self.days["Monday"](days.Monday);
			self.days["Tuesday"](days.Tuesday);
			self.days["Wednesday"](days.Wednesday);
			self.days["Thursday"](days.Thursday);
			self.days["Friday"](days.Friday);
			self.days["Saturday"](days.Saturday);
			self.days["Sunday"](days.Sunday);
		}
		else
		{
			var dayName = moment().format('dddd');
			self.days[dayName](true);
		}

		for (var key in self.days)
		{
			self.days[key].subscribe(self.processDaysOfTheWeek, self);
			self.daysEnable[key].subscribe(self.processDaysOfTheWeek, self);
		}
	};

	SubstituteResourcesViewModel.prototype.processDaysOfTheWeek = function()
	{
		var self = this,
			days = self.days,
			daysEnable = self.daysEnable;
		if ((days.Monday() || !daysEnable.Monday()) &&
			(days.Tuesday() || !daysEnable.Tuesday()) &&
			(days.Wednesday() || !daysEnable.Wednesday()) &&
			(days.Thursday() || !daysEnable.Thursday()) &&
			(days.Friday() || !daysEnable.Friday()) &&
			(daysEnable.Monday() && daysEnable.Tuesday() && daysEnable.Wednesday()
				&& daysEnable.Thursday() && daysEnable.Friday()))
		{
			self.weekday(true);
		}
		else
		{
			self.weekday(false);
		}
		self.dataRange((days.Monday() ||
			days.Tuesday() ||
			days.Wednesday() ||
			days.Thursday() ||
			days.Friday() ||
			days.Saturday() ||
			days.Sunday()) ? "hasvalue" : "");

		self._$form.find("input[name=DateRange]").change();
		if (self.conflictTripResources)
		{
			self.generateResource();
			self.setDefaultValue();
		}
	};

	SubstituteResourcesViewModel.prototype.checkWeekdays = function()
	{
		var isChecked = document.getElementById('weekdays').checked;

		this.daysEnable.Monday() && this.days.Monday(isChecked);
		this.daysEnable.Tuesday() && this.days.Tuesday(isChecked);
		this.daysEnable.Wednesday() && this.days.Wednesday(isChecked);
		this.daysEnable.Thursday() && this.days.Thursday(isChecked);
		this.daysEnable.Friday() && this.days.Friday(isChecked);
		this.weekday(isChecked);

		if (this.weekday())
		{
			this.days.Saturday(false);
			this.days.Sunday(false);
		}

		return true;
	};

	SubstituteResourcesViewModel.prototype.generateInitEntity = function()
	{
		var days = [];
		for (var key in this.days)
		{
			days.push(this.days[key]());
		}
		this.initEntity = {
			fromDate: this.fromDate(),
			toDate: this.toDate(),
			startTime: JSON.parse(JSON.stringify(this.startTimeArray)),
			endTime: JSON.parse(JSON.stringify(this.endTimeArray)),
			days: days,
			enableBusAide: this.obEnableBusAide(),
			enableDriver: this.obEnableDriver(),
			enableVehicle: this.obEnableVehicle(),
			selectedBusAideName: this.obSelectedBusAideName(),
			selectedDriverName: this.obSelectedDriverName(),
			selectedVehicleName: this.obSelectedVehicleName()
		};
	};

	SubstituteResourcesViewModel.prototype.resetResources = function()
	{
		var self = this;
		return this.pageLevelViewModel.saveValidate().then(function(valid)
		{
			if (!valid)
			{
				return false;
			}
			return tf.promiseBootbox.yesNo("Are you sure you want to reset resources?", "Confirmation Message")
				.then(response =>
				{
					if (response)
					{
						return self.saveAction(true);
					}

					return false;
				});
		});
	};

	SubstituteResourcesViewModel.prototype.apply = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(valid =>
			{
				if (!valid)
				{
					return false;
				}

				if (this.checkChange())
				{
					return this.saveAction();
				}
			});
	};

	SubstituteResourcesViewModel.prototype.cancel = function()
	{
		if (this.checkChange())
		{
			return tf.promiseBootbox.yesNo("You have unsaved changes. Would you like to save your changes prior to canceling?", "Unsaved Changes")
				.then(r =>
				{
					if (r)
					{
						return this.pageLevelViewModel.saveValidate()
							.then(valid =>
							{
								if (!valid)
								{
									return false;
								}

								return this.saveAction();
							});
					}
					return null;
				});
		}

		return Promise.resolve(null);
	};

	SubstituteResourcesViewModel.prototype.checkChange = function()
	{
		var days = [];
		for (var key in this.days)
		{
			days.push(this.days[key]());
		}
		var status = true;

		if (this.obEnableBusAide() || this.obEnableDriver() || this.obEnableVehicle())
		{
			var initFromDate = moment(this.initEntity.fromDate).format(this.dateTimeFormat),
				currentFromDate = moment(this.fromDate()).format(this.dateTimeFormat),
				initToDate = moment(this.initEntity.toDate).format(this.dateTimeFormat),
				currentToDate = moment(this.toDate()).format(this.dateTimeFormat);

			status = initFromDate === currentFromDate && initToDate === currentToDate && _.isEqual(this.initEntity.days, days);
		}

		if (this.obEnableBusAide())
		{
			status = status && this.initEntity.selectedBusAideName === this.obSelectedBusAideName();
		}

		if (this.obEnableDriver())
		{
			status = status && this.initEntity.selectedDriverName === this.obSelectedDriverName();
		}

		if (this.obEnableVehicle())
		{
			status = status && this.initEntity.selectedVehicleName === this.obSelectedVehicleName();
		}

		return !status;
	};

	SubstituteResourcesViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

	SubstituteResourcesViewModel.prototype.existingFutureResource = function()
	{
		let getTimeZone = tf.ajax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "clientconfigs"),
			{
				paramData:
				{
					clientId: tf.authManager.clientKey
				}
			}).then(data =>
			{
				if (data.Items && data.Items.length)
				{
					return data.Items[0].TimeZone;
				}

				return null;
			});

		let tripIds = [], trips = {};
		this.tripOriginalResources.forEach(trip =>
		{
			tripIds.push(trip.Id);
			trips[trip.Id] = trip;
		});

		let getTripResources = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "TripResources"), {
			paramData: {
				"DBID": tf.datasourceManager.databaseId,
				"@filter": `in(TripId,${tripIds.join(",")})`,
				"@fields": "TripId,StartDate,EndDate,IsTripLevel"
			}
		}).then(response => (response && response.Items && response.Items || []).filter(c => !c.IsTripLevel));

		return Promise.all([getTimeZone, getTripResources]).then(([timeZone, resources]) =>
		{
			const now = moment(),
				currentUtcOffset = now.utcOffset(),
				clientUtcOffset = timeZone ? moment.parseZone(timeZone).utcOffset() : currentUtcOffset,
				offset = clientUtcOffset - currentUtcOffset,
				clientToday = now.add(offset, "m").startOf('day'),
				result = [];
			resources.forEach(c =>
			{
				const startDate = moment(c.StartDate).startOf('day'),
					endDate = moment(c.EndDate).startOf('day');
				if (!clientToday.isAfter(startDate) || !clientToday.isAfter(endDate))
				{
					let trip = trips[c.TripId];
					if (trip && result.indexOf(trip) == -1)
					{
						result.push(trip);
					}
				}
			});

			return result;
		});
	};

	SubstituteResourcesViewModel.prototype.saveTripLevelResources = function()
	{
		return this.existingFutureResource().then(exsitingTrips =>
		{
			if (!exsitingTrips.length)
			{
				return false;
			}

			let message = "Future resource substitutions exist for trip";
			if (exsitingTrips.length > 1)
			{
				message += "s";
			}

			for (let index = 0; index < exsitingTrips.length; index++)
			{
				if (index > 0)
				{
					if (index == exsitingTrips.length - 1)
					{
						message += " and";
					}
					else
					{
						message += ",";
					}
				}

				message += " " + exsitingTrips[index].Name;
			}

			message += ".<br/>Do you want to replace them with this change?";

			return tf.promiseBootbox.yesNo(
				{
					message: message,
					title: "Confirmation Message",
					maxHeight: 300
				}
			);
		}).then(affectFutureResource =>
		{
			let pathDataList = [];
			this.tripOriginalResources.forEach(trip =>
			{
				if (this.obEnableVehicle())
				{
					pathDataList.push({
						Id: trip.Id,
						Op: "replace",
						Path: "VehicleId",
						Value: this.obSelectedVehicle() ? this.obSelectedVehicle().Id : null
					});
				}

				if (this.obEnableBusAide())
				{
					pathDataList.push({
						Id: trip.Id,
						Op: "replace",
						Path: "AideId",
						Value: this.obSelectedBusAide() ? this.obSelectedBusAide().Id : null
					});
				}

				if (this.obEnableDriver())
				{
					pathDataList.push({
						Id: trip.Id,
						Op: "replace",
						Path: "DriverId",
						Value: this.obSelectedDriver() ? this.obSelectedDriver().Id : null
					});
				}

				pathDataList.push({
					Id: trip.Id,
					Op: "relationship",
					Path: "AffectFutureResource",
					Value: affectFutureResource
				});
			});

			return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), "trips"),
				{
					data: pathDataList
				}).then(() => true);
		});
	};

	SubstituteResourcesViewModel.prototype.saveTripResources = async function(reset)
	{
		let confirmed = true;
		if (this.type === 'routes')
		{
			let trips = '<br/>';
			this.tripOriginalResources.forEach(i => { trips += `${i.Name}<br/>`; });
			confirmed = await tf.promiseBootbox.yesNo(
				{
					message: `You are about to substitute resources for the following Trips: ${trips} Are you sure you want to continue?`,
					backdrop: true,
					title: "Confirmation Message",
					closeButton: true,
					maxHeight: 200
				});
		}

		if (!confirmed)
		{
			return false;
		}

		const postData = [], tripsNotAvailableInRange = [], tripsPartialInRange = [];
		this.tripOriginalResources.forEach(trip =>
		{
			if (!reset)
			{
				const always = (trip?.TripDateRanges?.length || 0) === 0;
				const hasAvailableRange = always || trip.TripDateRanges.some(dateRange =>
				{
					const start = moment(dateRange.StartDate),
						end = moment(dateRange.EndDate),
						fromDate = moment(this.fromDate()),
						toDate = moment(this.toDate()),
						newFromDate = moment.max(start, fromDate),
						newToDate = moment.min(end, toDate);
					const isAllDateWithIn = (fromDate.isAfter(start) || fromDate.isSame(start)) && (toDate.isBefore(end) || toDate.isSame(end));
					const isValidDateRange = !newFromDate.isAfter(newToDate);
					if (!isAllDateWithIn && !newFromDate.isAfter(newToDate))
					{
						tripsPartialInRange.push(trip);
					}
					return isValidDateRange;
				});

				if (!hasAvailableRange)
				{
					tripsNotAvailableInRange.push(trip);
				} else
				{
					const saveDataModelNotExistTripCalender = this.generateTripCalenderDataModel(trip);
					saveDataModelNotExistTripCalender.VehicleId = saveDataModelNotExistTripCalender.VehicleId ? saveDataModelNotExistTripCalender.VehicleId : trip.VehicleId;
					saveDataModelNotExistTripCalender.DriverId = saveDataModelNotExistTripCalender.DriverId ? saveDataModelNotExistTripCalender.DriverId : trip.DriverId;
					saveDataModelNotExistTripCalender.AideId = saveDataModelNotExistTripCalender.AideId ? saveDataModelNotExistTripCalender.AideId : trip.AideId;
					postData.push(saveDataModelNotExistTripCalender);
				}
			}
			else 
			{
				let resetDataModelNotExistTripCalender = this.generateTripCalenderDataModel(trip);
				resetDataModelNotExistTripCalender.VehicleId = trip.VehicleId;
				resetDataModelNotExistTripCalender.DriverId = trip.DriverId;
				resetDataModelNotExistTripCalender.AideId = trip.AideId;
				postData.push(resetDataModelNotExistTripCalender);
			}
		});

		if (tripsNotAvailableInRange.length === this.tripOriginalResources.length)
		{
			let trips = '<br/>';
			tripsNotAvailableInRange.forEach(i => { trips += `${i.Name}<br/>`; });
			await tf.promiseBootbox.alert({
				message: `The date range selected for substitution can not apply to applicable days within the date range(s) of the following trip(s): ${trips}`,
				buttons: {
					success: {
						label: "Close",
						className: TF.isPhoneDevice ? "btn-mobile-confirm" : "tf-btn-black"
					}
				}
			});
			return false;
		}

		if (tripsPartialInRange.length > 0)
		{
			let trips = '<br/>';
			tripsPartialInRange.forEach(i => { trips += `${i.Name}<br/>`; });
			await tf.promiseBootbox.alert(`Not all days in the date range selected for substitution are included in the date range(s) of the following trip(s): ${trips}`);
		}

		return await tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tripresources"),
			{
				data: postData
			}).then(() => true);
	};

	SubstituteResourcesViewModel.prototype.saveAction = function(reset)
	{
		if (!reset && !this.obEnableVehicle() && !this.obEnableBusAide() && !this.obEnableDriver())
		{
			return;
		}

		if (this.needAssign())
		{
			return this.saveTripLevelResources();
		}

		return this.saveTripResources(reset)
	};

	SubstituteResourcesViewModel.prototype.generateTripCalenderDataModel = function(trip)
	{
		var self = this;
		let startTime = trip.StartTime;
		let endTime = trip.EndTime || trip.FinishTime;
		startTime = moment(moment(startTime, 'HH:mm:ss').add(self.adjustTime, 'm').toDate()).format('HH:mm:ss');
		endTime = moment(moment(endTime, 'HH:mm:ss').add(self.adjustTime, 'm').toDate()).format('HH:mm:ss');

		var tripCalendarEntity =
		{
			TripId: trip.Id,
			VehicleId: this.obSelectedVehicle() && this.obEnableVehicle() ? this.obSelectedVehicle().Id : this.currentVehicleId(),
			DriverId: this.obSelectedDriver() && this.obEnableDriver() ? this.obSelectedDriver().Id : this.currentDriverId(),
			AideId: this.obSelectedBusAide() && this.obEnableBusAide() ? this.obSelectedBusAide().Id : this.currentBusAideId(),
			StartDate: moment(this.fromDate()).format("YYYY-MM-DDT00:00:00"),
			EndDate: moment(this.toDate()).format("YYYY-MM-DDT00:00:00"),
			StartTime: startTime,
			EndTime: endTime,
			Monday: this.days.Monday(),
			Tuesday: this.days.Tuesday(),
			Wednesday: this.days.Wednesday(),
			Thursday: this.days.Thursday(),
			Friday: this.days.Friday(),
			Saturday: this.days.Saturday(),
			Sunday: this.days.Sunday(),
			DBID: this.dbId,
			ShouldNotUpdateTrip: true,
		};
		return tripCalendarEntity;
	};

	SubstituteResourcesViewModel.prototype.concatFullName = function(item)
	{
		if (item.FirstName && item.LastName)
		{
			return item.LastName + ", " + item.FirstName
		}
		else if (item.FirstName)
		{
			return item.FirstName;
		}
		else if (item.LastName)
		{
			return item.LastName;
		}

		return "";
	};

	function sortArray(array, sortField)
	{
		return array.sort(function(a, b)
		{
			if (a[sortField].toUpperCase() === b[sortField].toUpperCase())
			{
				return 0;
			}
			return a[sortField].toUpperCase() > b[sortField].toUpperCase() ? 1 : -1;
		});
	}

	SubstituteResourcesViewModel.prototype.minutesDiff = function(startTime, endTime, crossDay = false)
	{
		var self = this;
		if (crossDay === true && self.timeToDate(endTime) === self.timeToDate('00:00'))
		{
			endTime = "24:00";
		}
		if (startTime == endTime) 
		{
			return 0;
		}

		var endTime = moment(self.timeToDate(endTime)).toDate(),
			startTime = moment(self.timeToDate(startTime)).toDate();

		var minutesDiff = moment(endTime).diff(moment(startTime), "minutes");
		return minutesDiff;
	};

	SubstituteResourcesViewModel.prototype.timeToDate = function(time)
	{
		var baselineDate = "12/30/1899";
		// drop date info
		if (!isNaN(new Date(time).getMinutes()))
		{
			var newDateTime = new Date(time);
			time = newDateTime.getHours() + ':' + newDateTime.getMinutes();
		}
		if (time === "24:00")
		{
			baselineDate = "12/31/1899";
			time = "00:00";
		}

		// suppose time string.
		var dateTime = new Date(baselineDate + " " + time);
		return baselineDate + " " + dateTime.getHours() + ':' + dateTime.getMinutes();
	}
})();