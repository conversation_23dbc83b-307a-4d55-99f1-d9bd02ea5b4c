(function()
{
	createNamespace('TF.RoutingMap.Locate').Tool = Tool;

	function Tool(map, arcgis)
	{
		var self = this;
		self._map = map;
		self._arcgis = arcgis;
		self.LAYER = {
			'ADDRESS': 'ADDRESS'
		};

		self._initialize();
	};

	Tool.prototype.contructor = Tool;

	Tool.prototype._initialize = function()
	{
		var self = this;
		self._initLayers();
		self._initSymbol();
		self._initEvents();

		self._addLayers();
	};

	Tool.prototype._initLayers = function()
	{
		var self = this;
		self._addressLayer = new self._arcgis.GraphicsLayer({ 'id': 'locate_addressLayer', "title": "Locate Address" });
		self._locationLayer = new self._arcgis.GraphicsLayer({ 'id': 'locate_locationLayer', "title": "Locate Location" });
	};

	Tool.prototype._addLayers = function()
	{
		var self = this;
		if (self._map)
		{
			self._map.addMany([self._addressLayer, self._locationLayer]);
		}
	};

	Tool.prototype._removeLayers = function()
	{
		var self = this;
		if (self._addressLayer)
		{
			self._map.remove(self._addressLayer);
			self._addressLayer = null;
		}

		if (self._locationLayer)
		{
			self._map.remove(self._locationLayer);
			self._locationLayer = null;
		}
	};

	Tool.prototype.dispose = function()
	{
		var self = this;
		self._removeLayers();

		self._disposeSymbol();
		self._disposeEvents();
	};

	Tool.prototype.addAddressSearchResults = function(results)
	{
		var self = this,
			featureCount = results.length,
			result = null,
			feature = null,
			geometry = null;
		if (featureCount === 0) return;

		self._addressLayer.clear();

		for (var i = featureCount - 1; i >= 0; --i)
		{
			result = results[i];
			feature = result.feature;
			feature.symbol = self._addressSymbol();
			self._addressLayer.add(feature);
		}
		self._addressLayer.redraw();

		if (featureCount > 1)
		{
			self._zoomToLayer(self.LAYER.ADDRESS);
		} else if (featureCount === 1)
		{
			feature = results[0].feature;
			geometry = feature.geometry;
			self._map.centerAt(geometry);
		}
	};

	Tool.prototype.locateSearchResults = function(result)
	{
		var self = this,
			longitude = parseFloat(result.x()),
			latitude = parseFloat(result.y());
		self._centerMap(longitude, latitude);
	};

	Tool.prototype._zoomToLayer = function(layerName)
	{
		var self = this,
			graphics = null,
			extent = null;
		switch (layerName)
		{
			case self.LAYER.ADDRESS:
				graphics = self._addressLayer.graphics;
				break;
		}

		extent = self._arcgis.graphicsUtils.graphicsExtent(graphics);
		self._map.setExtent(extent, true);
	};

	// allow subscribe to different events
	Tool.prototype.subscribe = function(event, callback)
	{
		var handler = this._getEventHandlerByName(event);

		if (handler) { handler.subscribe(callback); }
	};

	Tool.prototype.unsubscribe = function(event, callback)
	{
		var handler = this._getEventHandlerByName(event);

		if (handler) { handler.unsubscribe(callback); }
	};

	Tool.prototype._getEventHandlerByName = function(event)
	{
		switch (event)
		{
			case 'onAddressChanged':
				return this._onAddressChanged;
			default:
				return;
		}
	};

	/**
	 * Centers the map on the specified x,y location
	 * @param  {number} longitude The value of longitude, between -90 and 90.
	 * @param  {number} latitude The value of latitude, between -180 and 180.
	 * @returns {void}
	 */
	Tool.prototype._centerMap = function(longitude, latitude)
	{
		if (isNaN(longitude)
			|| isNaN(latitude)
			|| longitude > 180
			|| longitude < -180
			|| latitude > 90
			|| latitude < -90
		)
		{
			return;
		}

		var self = this,
			mapPoint = new self._arcgis.Point(longitude, latitude),
			symbol = self._mapCenterSymbol(),
			graphic = new self._arcgis.Graphic({ geometry: mapPoint, symbol });

		self._locationLayer.clear();
		self._locationLayer.add(graphic);
		self._map.centerAt(mapPoint);
	};
})();