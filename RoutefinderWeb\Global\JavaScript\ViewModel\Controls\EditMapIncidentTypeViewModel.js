﻿(function()
{
	createNamespace('TF.Control').EditMapIncidentTypeViewModel = EditMapIncidentTypeViewModel;

	const DEFAULT_FONT_SIZE = 110;
	const DEFAULT_SYMBOL_COLOR = "#0000ff";
	const DEFAULT_BORDER_SIZE = 2;
	const DEFAULT_BORDER_COLOR = "#333";
	const DEFAULT_BACKGROUND_COLOR = "#FFF";
	const CUSTOM_SYMBOL_STATE = "(Custom)";
	const GENERATE_SVG_FROM_BASE64IMG = (base64Img, iconSize) => `<image x="0" y="0" width="${iconSize}" height="${iconSize}" xlink:href="${base64Img}"/>`;
	const SymbolType = {
		SELECTED: 1,
		UPLOADED: 2,
	}

	function EditMapIncidentTypeViewModel(_configType, recordEntity)
	{
		var dataModel = new TF.DataModel.MapIncidentTypeDataModel(recordEntity);
		this.incidentsEndpoint = dataModel.getIncidentTypeConfig().apiEndpoint;
		this.obEntityDataModel = ko.observable(dataModel);
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		this.symbolColorPicker = null;
		this.backgroundColorPicker = null;
		this.borderColorPicker = null;
		this.dropDownDataSource = this.getDropDownDataSource();
		this.selectedSymbolValue = -1;

		this.allUnits = ["minutes", "hours", "days"];
		this.obDistanceUnit = ko.observable(tf.measurementUnitConverter.isImperial() ? 'miles' : 'kilometers');
		this.obNotificationUnit = ko.observable("minutes");
		this.obNotificationExtentUnit = ko.observable("minutes");
		this.obBorderSize = ko.observable();
		this.obAddBorder = ko.observable(true);
		this.obAddBorder.subscribe(this.onAddBorderOptionToggle.bind(this));
		this.obIsNotificationType = ko.observable(false);
		this.obIsNoneType = ko.observable(false);
		this.obWayfinderReports = ko.observable(false);
		this.obSnapToStreet = ko.observable(true);

		this.obIsNoneTypeChanged = ko.computed(function()
		{
			this.validator && this.validator.updateStatus("notify_extend", "NOT_VALIDATED");
			this.validator && this.validator.updateStatus("notify_remove", "NOT_VALIDATED");
			this.validator && this.validator.updateStatus("notify_display", "NOT_VALIDATED");
			this.validator && this.validator.updateStatus("notify_prior", "NOT_VALIDATED");
			var isNoneChecked = this.obEntityDataModel().notificationType() === 3;

			if (this.notifyDisplayInput)
			{
				this.notifyDisplayInput.enable(!isNoneChecked);
				if (isNoneChecked && this.notifyDisplayInput.value() === 0)
				{
					this.notifyDisplayInput.value(null);
					this.obEntityDataModel().displayTime(null);
				}
			}

			if (this.notifyDistanceInput)
			{
				this.notifyDistanceInput.enable(!isNoneChecked);
				if (isNoneChecked && this.notifyDistanceInput.value() === 0)
				{
					this.notifyDistanceInput.value(null);
					this.obEntityDataModel().notificationDistance(null);
				}
			}

			return isNoneChecked;
		}, this);

		this.obIsNotificationTypeChanged = ko.computed(function()
		{
			this.validator && this.validator.updateStatus("notify_extend", "NOT_VALIDATED");
			this.validator && this.validator.updateStatus("notify_remove", "NOT_VALIDATED");
			var isNotificationChecked = this.obEntityDataModel().notificationType() === 2;
			var isNoneChecked = this.obEntityDataModel().notificationType() === 3;
			if (this.notifyExtendInput)
			{
				this.notifyExtendInput.enable(!isNotificationChecked && !isNoneChecked);
				if ((isNotificationChecked || isNoneChecked) && this.notifyExtendInput.value() === 0)
				{
					this.notifyExtendInput.value(null);
					this.obEntityDataModel().extendTime(null);
				}
			}

			if (this.notifyRemoveInput)
			{
				this.notifyRemoveInput.enable(!isNotificationChecked && !isNoneChecked);
				if ((isNotificationChecked || isNoneChecked) && this.notifyRemoveInput.value() === 0)
				{
					this.notifyRemoveInput.value(null);
					this.obEntityDataModel().removeCount(null);
				}
			}
			return isNotificationChecked;
		}, this);

		this.obIsNoneType = this.obIsNoneTypeChanged;
		this.obIsNotificationType = this.obIsNotificationTypeChanged;
		this.displayTimeValueChanged = { originTime: 0, valuebeforeConversion: 0, unitebeforeConversion: 'minutes' };
		this.extentTimeValueChanged = { originTime: 0, valuebeforeConversion: 0, unitebeforeConversion: 'minutes' };
	};

	EditMapIncidentTypeViewModel.prototype.init = function(viewModel, el)
	{
		this.$form = $(el);
		this.$previewContainer = this.$form.find(".icon-preview");
		this.$uploadInput = this.$form.find("input[type=file]#uploadIconFile");

		this.initIncidentData();
		this.initValidation();
		this.initSymbolPicker(); //esri default symbols
		this.initColorPickers(); //symbol color, background color, border color
		this.initNumbericInput();
	};

	EditMapIncidentTypeViewModel.prototype.initNumbericInput = function()
	{
		this.notifyDisplayInput = this.$form.find('[name="notify_display"]').kendoNumericTextBox({
			format: "#",
			decimals: 0,
			min: 0,
			value: this.obEntityDataModel().displayTime(),
			spinners: false,
			restrictDecimals: true,
			enable: !this.obIsNoneType(),
			change: (e) =>
			{
				this.obEntityDataModel().displayTime(parseInt(e.sender.element[0].value));
			}
		}).data('kendoNumericTextBox');

		this.notifyDistanceInput = this.$form.find('[name="notify_prior"]').kendoNumericTextBox({
			format: "0.00",
			decimals: 2,
			min: 0,
			value: this.obEntityDataModel().notificationDistance(),
			spinners: false,
			restrictDecimals: true,
			enable: !this.obIsNoneType(),
			change: (e) =>
			{
				this.obEntityDataModel().notificationDistance(parseFloat(e.sender.element[0].value));
			}
		}).data('kendoNumericTextBox');

		this.notifyExtendInput = this.$form.find('[name="notify_extend"]').kendoNumericTextBox({
			format: "#",
			decimals: 0,
			min: 0,
			value: this.obEntityDataModel().extendTime(),
			spinners: false,
			restrictDecimals: true,
			enable: !this.obIsNotificationType() && !this.obIsNoneType(),
			change: (e) =>
			{
				this.obEntityDataModel().extendTime(parseInt(e.sender.element[0].value));
			}
		}).data('kendoNumericTextBox');

		this.notifyRemoveInput = this.$form.find('[name="notify_remove"]').kendoNumericTextBox({
			format: "#",
			decimals: 0,
			min: 0,
			value: this.obEntityDataModel().removeCount(),
			spinners: false,
			restrictDecimals: true,
			enable: !this.obIsNotificationType() && !this.obIsNoneType(),
			change: (e) =>
			{
				this.obEntityDataModel().removeCount(parseInt(e.sender.element[0].value));
			}
		}).data('kendoNumericTextBox');
	}

	EditMapIncidentTypeViewModel.prototype.minusDisplayTime = function()
	{
		var time = Number(this.obEntityDataModel().displayTime());
		if (time > 1)
		{
			this.notifyDisplayInput.value(time - 1);
			this.obEntityDataModel().displayTime(time - 1);
		}
	}

	EditMapIncidentTypeViewModel.prototype.addDisplayTime = function()
	{
		var time = Number(this.obEntityDataModel().displayTime());
		time = !time ? 0 : time;
		var needValidate = !time;
		if (time < 9999)
		{
			this.notifyDisplayInput.value(time + 1);
			this.obEntityDataModel().displayTime(time + 1);
			if (needValidate)
			{
				this.validator.validate();
			}
		}
	}

	EditMapIncidentTypeViewModel.prototype.minusExtendTime = function()
	{
		var time = Number(this.obEntityDataModel().extendTime());
		if (time > 1)
		{
			this.notifyExtendInput.value(time - 1);
			this.obEntityDataModel().extendTime(time - 1);
		}
	}

	EditMapIncidentTypeViewModel.prototype.addExtendTime = function()
	{
		var time = Number(this.obEntityDataModel().extendTime());
		time = !time ? 0 : time;
		var needValidate = !time;
		if (time < 9999)
		{
			this.notifyExtendInput.value(time + 1);
			this.obEntityDataModel().extendTime(time + 1);
			if (needValidate)
			{
				this.validator.validate();
			}
		}
	}

	EditMapIncidentTypeViewModel.prototype.minusRemoveCount = function()
	{
		var count = Number(this.obEntityDataModel().removeCount());
		if (count > 1)
		{
			this.notifyRemoveInput.value(count - 1);
			this.obEntityDataModel().removeCount(count - 1);
		}
	}

	EditMapIncidentTypeViewModel.prototype.addRemoveCount = function()
	{
		var count = Number(this.obEntityDataModel().removeCount());
		count = !count ? 0 : count;
		var needValidate = !count;
		if (count < 9999)
		{
			this.notifyRemoveInput.value(count + 1);
			this.obEntityDataModel().removeCount(count + 1);
			if (needValidate)
			{
				this.validator.validate();
			}
		}
	}

	EditMapIncidentTypeViewModel.prototype.resetDataModel = function()
	{
		this.obEntityDataModel(new TF.DataModel.MapIncidentTypeDataModel());
		this.notifyExtendInput.value(null);
		this.notifyRemoveInput.value(null);
		this.notifyDistanceInput.value(null);
		this.notifyDisplayInput.value(null);
	}

	EditMapIncidentTypeViewModel.prototype.initColorPickers = function()
	{
		this.symbolColorPicker = this.$form.find("input[name=symbol-color]").kendoColorPicker({
			buttons: false,
			value: this.obEntityDataModel().symbolColor() || DEFAULT_SYMBOL_COLOR,
			sticky: true,
			change: (e) =>
			{
				// If symbol color is set when custom symbol is applied, switch to default system symbol.
				if (this.obEntityDataModel().symbolType() === SymbolType.UPLOADED)
				{
					this.$uploadInput.val(null);
					this.obEntityDataModel().symbolType(SymbolType.SELECTED);
					this.obEntityDataModel().symbolColor(e.value);
					this.obEntityDataModel().symbol(this.dropDownDataSource[0].valueTemplate);
					this.$form.find(".currentSymbol").html(this.dropDownDataSource[0].template);
					this.selectedSymbolValue = 0;
				}
				else
				{
					this.obEntityDataModel().symbolColor(e.value);
				}
				this.generateDisplayIcon();
			}
		}).data("kendoColorPicker");

		this.backgroundColorPicker = this.$form.find("[name=symbol-background-color]").kendoColorPicker({
			buttons: false,
			value: this.obEntityDataModel().backgroundColor() || DEFAULT_BACKGROUND_COLOR,
			sticky: true,
			change: (e) =>
			{
				this.obEntityDataModel().backgroundColor(e.value);
				this.generateDisplayIcon();
			}
		}).data("kendoColorPicker");

		this.borderColorPicker = this.$form.find("[name=border-color]").kendoColorPicker({
			buttons: false,
			value: this.obEntityDataModel().borderColor() || DEFAULT_BORDER_COLOR,
			change: (e) =>
			{
				this.obEntityDataModel().borderColor(e.value);
				this.generateDisplayIcon();
			}
		}).data("kendoColorPicker");
	};

	EditMapIncidentTypeViewModel.prototype.initIncidentData = function()
	{
		let content = CUSTOM_SYMBOL_STATE;
		const recordEntity = this.obEntityDataModel();
		if (!recordEntity)
		{
			return;
		}
		this.onInit = true;

		// convert from string to int for numberic fields
		recordEntity.notificationDistance(parseFloat(recordEntity.notificationDistance()));
		recordEntity.notificationType(parseInt(recordEntity.notificationType()));
		recordEntity.displayTime(parseInt(recordEntity.displayTime()));
		recordEntity.extendTime(parseInt(recordEntity.extendTime()));
		recordEntity.removeCount(parseInt(recordEntity.removeCount()));

		if (!recordEntity.notificationType())
		{
			recordEntity.notificationType(1);
		}
		this.obWayfinderReports(recordEntity.wayfinderReports() === 'true');
		this.obSnapToStreet(!!recordEntity.snapToStreet());

		var [actualValue, unit] = [0, ''];
		[actualValue, unit] = convertToUpperUnit(recordEntity.displayTime());
		this.displayTimeValueChanged.originTime = recordEntity.displayTime();
		this.displayTimeValueChanged.unitebeforeConversion = unit;
		this.displayTimeValueChanged.valuebeforeConversion = actualValue;
		recordEntity.displayTime(actualValue);
		this.obNotificationUnit(unit);
		[actualValue, unit] = convertToUpperUnit(recordEntity.extendTime());
		this.extentTimeValueChanged.originTime = recordEntity.extendTime();
		this.extentTimeValueChanged.unitebeforeConversion = unit;
		this.extentTimeValueChanged.valuebeforeConversion = actualValue;
		recordEntity.extendTime(actualValue);
		this.obNotificationExtentUnit(unit);

		if (recordEntity.symbolType() === SymbolType.SELECTED)
		{
			if (recordEntity.symbol())
			{
				const assetId = this.getEsriSvgAssetId(recordEntity.symbol());
				const matchedSymbol = assetId > -1 ? this.dropDownDataSource.find(d => d.valueTemplate.includes(`<title>Asset ${assetId}</title>`)) : null;
				if (matchedSymbol)
				{
					this.selectedSymbolValue = Number(matchedSymbol.value);
					content = matchedSymbol.template;
				}
			}
			else
			{
				this.selectedSymbolValue = 0;
				content = this.dropDownDataSource[0].template;
				recordEntity.symbol(this.dropDownDataSource[0].valueTemplate);
			}
		}
		this.$form.find(".currentSymbol").html(content);

		this.symbolColorPicker?.value(recordEntity.symbolColor() || DEFAULT_SYMBOL_COLOR);
		this.backgroundColorPicker?.value(recordEntity.backgroundColor() || DEFAULT_BACKGROUND_COLOR);
		this.borderColorPicker?.value(recordEntity.borderColor() || DEFAULT_BORDER_COLOR);

		this.obBorderSize(recordEntity.borderSize());
		this.obAddBorder(null); //reset
		this.obAddBorder(this.obBorderSize() > 0);
		this.obEntityDataModel().apiIsDirty(false);
		this.onInit = false;
	};

	EditMapIncidentTypeViewModel.prototype.initValidation = function()
	{
		var self = this, validatorFields = {};

		validatorFields["name"] = {
			trigger: "change",
			validators: {
				notEmpty: {
					message: "Name is required"
				},
				callback: {
					message: "Must be unique",
					callback: function(value)
					{
						if (value)
						{
							return self.fetchIncidentData().then((d) =>
							{
								const found = d.find(r => r.Id !== self.obEntityDataModel().id() && r.Name && r.Name.trim().toUpperCase() === value.trim().toUpperCase());
								return !found ? true : false;
							});
						}
						return true;
					}.bind(self)
				}
			}
		};

		validatorFields["notify_prior"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Distance is required"
				},
				greaterThan: {
					value: 0,
					message: "Distance must be > 0",
					inclusive: false
				},
				lessThan: {
					value: 10000,
					message: "Distance must be < 10000",
					inclusive: false
				}
			}
		};

		validatorFields["notify_display"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Display time is required"
				},
				greaterThan: {
					value: 0,
					message: "Dislay time must be > 0",
					inclusive: false
				},
			}
		};

		validatorFields["notify_extend"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Extend time is required"
				},
				greaterThan: {
					value: 0,
					message: "Extend time must be > 0",
					inclusive: false
				},
			}
		};

		validatorFields["notify_remove"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Remove count is required"
				},
				greaterThan: {
					value: 0,
					message: "Remove count must be > 0",
					inclusive: false
				},
			}
		};

		self.validator = self.$form.bootstrapValidator({
			excluded: [':disabled'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).data("bootstrapValidator");
	};

	EditMapIncidentTypeViewModel.prototype.initSymbolPicker = function()
	{
		const self = this;
		self.$form.find("#symbol-selector .symbol-container").on("click", this.openSymbolsPanel.bind(this));
		$("body").on("mousedown.adjustModal", function(e)
		{
			if ($(e.target).closest(".symbols-panel").length <= 0)
			{
				self.$form.parent().find(".symbols-panel").hide();
			}
		});
	};

	EditMapIncidentTypeViewModel.prototype.openSymbolsPanel = function()
	{
		var self = this, symbolPanel = self.$form.find(".symbols-panel");
		symbolPanel.show();
		if (symbolPanel.children().length <= 0)
		{
			self.addSymbolsToPanel(symbolPanel);
		}
		symbolPanel.find(".symbol-item").removeClass("selected");
		if (self.selectedSymbolValue >= 0)
		{
			symbolPanel.find(".symbol-item[value=" + self.selectedSymbolValue + "]").addClass("selected");
		}
	};

	EditMapIncidentTypeViewModel.prototype.addSymbolsToPanel = function(symbolPanel)
	{
		var self = this, groupedSource = {}, symbols, categoryContainer;
		$.each(self.dropDownDataSource, function(index, item)
		{
			if (!groupedSource[item.category])
			{
				groupedSource[item.category] = [];
			}
			groupedSource[item.category].push(item);
		});

		$.each(Object.keys(groupedSource), function(index, key)
		{
			symbols = groupedSource[key];
			categoryContainer = $("<div class='category-container'></div>");
			categoryContainer.append("<div class='category-header'>" + key + "</div>");
			symbolPanel.append(categoryContainer);
			$.each(symbols, function(index, symbol)
			{
				categoryContainer.append($("<div class='symbol-item' value=" + symbol.value + ">" + symbol.template + "</div>"));
			});
		});

		symbolPanel.find(".symbol-item").on("click.selectsymbol", function(e)
		{
			var $target = $(e.target).closest(".symbol-item"), content;
			self.selectedSymbolValue = Number($target.attr("value"));
			if (self.selectedSymbolValue >= 0)
			{
				for (var i = 0; i < self.dropDownDataSource.length; i++)
				{
					if (self.dropDownDataSource[i].value === self.selectedSymbolValue)
					{
						//If symbol is selected when custom symbol is applied, switch to default symbol color.
						if (self.obEntityDataModel().symbolType() === SymbolType.UPLOADED)
						{
							self.obEntityDataModel().symbolColor(DEFAULT_SYMBOL_COLOR);
							self.symbolColorPicker?.value(DEFAULT_SYMBOL_COLOR);
						}

						self.obEntityDataModel().symbol(self.dropDownDataSource[i].valueTemplate);
						self.obEntityDataModel().symbolType(SymbolType.SELECTED);
						self.$form.find(".currentSymbol").html(self.dropDownDataSource[i].template);
						self.$uploadInput.val(null);
						self.generateDisplayIcon();
						break;
					}
				}
			}
			self.$form.parent().find(".symbols-panel").hide();
		});
	};

	EditMapIncidentTypeViewModel.prototype.getEsriSvgAssetId = function(svgValueTemplateString)
	{
		let assetId = -1;
		if (svgValueTemplateString)
		{
			const titleTag = /<title>(.*?)<\/title>/g.exec(svgValueTemplateString);
			if (titleTag)
			{
				assetId = Number(titleTag[1].replace("Asset", ''));
			}
		}
		return assetId;
	}

	/**
	 * On "Add Border" checkbox value toggled.
	 *
	 * @param {Boolean} value
	 */
	EditMapIncidentTypeViewModel.prototype.onAddBorderOptionToggle = function(value)
	{
		if (value === null) return;

		if (!value)
		{
			this.obEntityDataModel().borderColor(DEFAULT_BORDER_COLOR);
			this.borderColorPicker?.value(DEFAULT_BORDER_COLOR);
		}

		const borderSize = value ? (this.obBorderSize() === 0 ? DEFAULT_BORDER_SIZE : this.obBorderSize()) : 0
		this.obBorderSize(borderSize);
		this.updateBorderSize();
	};

	EditMapIncidentTypeViewModel.prototype.getDropDownDataSource = function()
	{
		var self = this, dDataSourcSVG, dDataSource = [];
		dDataSourcSVG = thematicSymbolPath;
		for (var i = 0; i < dDataSourcSVG.length; i++)
		{
			var obj = {};
			obj.value = dDataSourcSVG[i].id;
			obj.category = dDataSourcSVG[i].category;
			obj.valueTemplate = TF.Helper.AdjustValueSymbolHelper.getOriginSVGSymbolItemString(obj.value, true); //symbol saved in database
			obj.template = TF.Helper.AdjustValueSymbolHelper.getSVGSymbolString(obj.value, "#000000", "24"); // template for display purpose
			dDataSource.push(obj);
		}
		return dDataSource;
	}
	EditMapIncidentTypeViewModel.prototype.updateBorderSize = function()
	{
		this.obEntityDataModel().borderSize(this.obBorderSize());
		this.generateDisplayIcon();
	};

	EditMapIncidentTypeViewModel.prototype.updateBorderSizeSliderColor = function()
	{
		const borderColor = this.obEntityDataModel().borderColor();
		this.$form.find("#border-size-slider .slider-selection").css("background", borderColor);
	};

	EditMapIncidentTypeViewModel.prototype.onUploadCustomSymbol = function(viewModel, e)
	{
		this.$uploadInput?.trigger('click');
	};

	EditMapIncidentTypeViewModel.prototype.UploadIconChange = function(model, e)
	{
		var self = this;
		var files = Array.from(e.target.files)
		if (files.length === 1)
		{
			var file = files[0];
			if (file.size > 2 * 1024 * 1024)
			{
				tf.promiseBootbox.alert("Icon size must be less than 2 MB.", "Error");
				return;
			}

			fileToBase64(file).then((base64_img) =>
			{
				const file_icon = GENERATE_SVG_FROM_BASE64IMG(base64_img, DEFAULT_FONT_SIZE);
				self.obEntityDataModel().symbol(file_icon);
				self.obEntityDataModel().symbolType(SymbolType.UPLOADED);
				self.obEntityDataModel().symbolColor(DEFAULT_BACKGROUND_COLOR);
				self.symbolColorPicker?.value(DEFAULT_BACKGROUND_COLOR);
				self.$form.find(".currentSymbol").html(CUSTOM_SYMBOL_STATE);
				self.selectedSymbolValue = -1;
				self.$uploadInput.val(null);
				self.generateDisplayIcon();
			})
		}
	}

	EditMapIncidentTypeViewModel.prototype.apply = function()
	{
		var self = this;
		var recordEntity = self.obEntityDataModel();
		if (!recordEntity)
		{
			return Promise.resolve(null);
		}

		recordEntity.wayfinderReports(this.obWayfinderReports());
		recordEntity.snapToStreet(this.obSnapToStreet());
		return self.validator.validate()
			.then(function(isValidated)
			{
				if (isValidated)
				{
					return self.obEntityDataModel().id() ? self.updateIncident(recordEntity) : self.createIncident(recordEntity)
				}
				return null;
			})
			.catch(() => null);
	};

	EditMapIncidentTypeViewModel.prototype.createIncident = function(recordEntity)
	{
		if (!recordEntity)
		{
			return Promise.resolve(null);
		}

		const incidentData = [{
			Name: recordEntity.name().trim(),
			SymbolType: recordEntity.symbolType(),
			Symbol: recordEntity.symbol(),
			SymbolColor: recordEntity.symbolColor(),
			BackgroundColor: recordEntity.backgroundColor(),
			BorderColor: recordEntity.borderColor(),
			BorderSize: recordEntity.borderSize(),
			DisplayIcon: recordEntity.displayIcon(),
			WayfinderReports: recordEntity.wayfinderReports(),
			NotificationType: recordEntity.notificationType(),
			NotificationDistance: this.convertToCorrectUnitOfMeasure(recordEntity.notificationDistance()),
			DisplayTime: convertToUpperUnit(recordEntity.displayTime(), this.obNotificationUnit()),
			ExtendTime: convertToUpperUnit(recordEntity.extendTime(), this.obNotificationExtentUnit()),
			RemoveCount: recordEntity.removeCount(),
			SnapToStreet: recordEntity.snapToStreet()
		}];

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.incidentsEndpoint),
			{
				data: incidentData
			})
			.then(
				(result) => {
					if (result && result.Items && result.Items.length > 0) 
					{
						const item = result.Items[0];
						return { Id: item.Id, Name: item.Name };
					}
					return null;
				}
			)
			.catch(
				() => null
			);
	}

	EditMapIncidentTypeViewModel.prototype.updateIncident = function(recordEntity)
	{
		var self = this;
		if (!recordEntity)
		{
			return Promise.resolve(null);
		}

		var incidentPatchData = [
			{ Op: "replace", Path: "/Name", Value: recordEntity.name().trim() },
			{ Op: "replace", Path: "/SymbolType", Value: recordEntity.symbolType() },
			{ Op: "replace", Path: "/Symbol", Value: recordEntity.symbol() },
			{ Op: "replace", Path: "/SymbolColor", Value: recordEntity.symbolColor() },
			{ Op: "replace", Path: "/BackgroundColor", Value: recordEntity.backgroundColor() },
			{ Op: "replace", Path: "/BorderColor", Value: recordEntity.borderColor() },
			{ Op: "replace", Path: "/BorderSize", Value: recordEntity.borderSize() },
			{ Op: "replace", Path: "/DisplayIcon", Value: recordEntity.displayIcon() },
			{ Op: "replace", Path: "/WayfinderReports", Value: recordEntity.wayfinderReports() },
			{ Op: "replace", Path: "/NotificationType", Value: recordEntity.notificationType() },
			{ Op: "replace", Path: "/NotificationDistance", Value: self.convertToCorrectUnitOfMeasure(recordEntity.notificationDistance()) },
			{ Op: "replace", Path: "/DisplayTime", Value: getTimeValueToSave(recordEntity.displayTime(), self.obNotificationUnit(), this.displayTimeValueChanged) },
			{ Op: "replace", Path: "/ExtendTime", Value: getTimeValueToSave(recordEntity.extendTime(), self.obNotificationExtentUnit(), this.extentTimeValueChanged) },
			{ Op: "replace", Path: "/RemoveCount", Value: recordEntity.removeCount() },
			{ Op: "replace", Path: "/SnapToStreet", Value: recordEntity.snapToStreet() }
		];

		return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), self.incidentsEndpoint, `${recordEntity.id()}`), { data: incidentPatchData })
			.then((result) => result && result.Items && result.Items.length > 0 ? result.Items[0].Id : null)
			.catch(() => null);
	}

	EditMapIncidentTypeViewModel.prototype.fetchIncidentData = function()
	{
		var self = this;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), self.incidentsEndpoint), {}, { overlay: false })
			.then((result) => result && result.Items ? result.Items : [])
			.catch(() => []);
	}

	EditMapIncidentTypeViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

	EditMapIncidentTypeViewModel.prototype.convertSvgToPng = function(base64_svg)
	{
		var self = this;
		var image = new Image();
		if (self.onInit) return;
		image.src = base64_svg;
		image.onload = () =>
		{
			const canvas = document.createElement("canvas");
			canvas.classList.add("tf-map-incident-type-canvas");
			const context = canvas.getContext("2d");
			canvas.width = image.width;
			canvas.height = image.height;

			context.fillStyle = 'transparent';

			context.drawImage(image, 0, 0);
			if ($(".tf-map-incident-type-canvas").length > 0)
			{
				$(".tf-map-incident-type-canvas").remove();
			}

			var base64_png = canvas.toDataURL("image/png");
			self.obEntityDataModel().displayIcon(base64_png);
		}
	};

	EditMapIncidentTypeViewModel.prototype.generateDisplayIcon = function()
	{
		const recordEntity = this.obEntityDataModel();
		const symbol = recordEntity.symbol();
		if (symbol)
		{
			// Generate icon preview data.
			const symbolColor = recordEntity.symbolColor();
			const backgroundColor = recordEntity.backgroundColor();
			var svgHtml = "";

			if (this.obAddBorder())
			{
				const borderSize = recordEntity.borderSize();
				const borderColor = recordEntity.borderColor();
				svgHtml = calc_svg_html(symbol, backgroundColor, DEFAULT_FONT_SIZE, symbolColor, borderSize, borderColor);
			}
			else
			{
				svgHtml = calc_svg_html(symbol, backgroundColor, DEFAULT_FONT_SIZE, symbolColor);
			}

			// Update preview element.
			this.$previewContainer.html(svgHtml);

			// Update value in record entity.
			const base64_svg = generate_base64_svg_image();
			this.convertSvgToPng(base64_svg);
		}
	}

	EditMapIncidentTypeViewModel.prototype.setFocus = function()
	{
		var inputName = this.$form.find("input[name=name]");
		if (inputName)
		{
			inputName.focus();
		}
	}

	EditMapIncidentTypeViewModel.prototype.convertToCorrectUnitOfMeasure = function(distance)
	{
		if (tf.measurementUnitConverter.isImperial())
		{
			return distance * 1.609344; // miles -> kilometers
		}
		else
		{
			return distance;
		}
	}

	function getTimeValueToSave(value, unit, valueType)
	{
		if (!value)
		{
			return null;
		}

		if (value !== valueType.valuebeforeConversion || unit !== valueType.unitebeforeConversion)
		{
			return convertToUpperUnit(value, unit);
		} else
		{
			return valueType.originTime;
		}
	}

	function convertToUpperUnit(num, unit = '')
	{
		//when saving/updating time value
		if (unit !== '')
		{
			if (!num)
			{
				return null;
			}

			if (unit === 'days')
			{
				return Math.round(num * 1440);
			} else if (unit === 'hours')
			{
				return Math.round(num * 60);
			} else
			{
				return Math.round(num);
			}
		}

		//when initilizing time value on the modal
		if (num >= 1440 && num % 1440 === 0)
		{
			return [num / 1440, 'days'];
		} else if (num >= 60 && num % 60 === 0)
		{
			return [(num / 60), 'hours'];
		}

		return [num, 'minutes'];
	}

	function fileToBase64(file)
	{
		return new Promise((resolve, reject) =>
		{
			var reader = new FileReader()
			reader.readAsDataURL(file)
			reader.onload = () => resolve(reader.result)
			reader.onerror = (e) => reject(e)
		});
	}

	function get_svg_template(size, borderSize = 0, borderColor = '#000')
	{
		const icon_size = size, maxRadius = icon_size / 2;
		var svg_icon_template;

		//uploaded icon
		svg_icon_template =
			// `<?xml version="1.0" standalone="no"?>` +
			`<svg id="svg_template" xmlns="http://www.w3.org/2000/svg" version="1.1" width="${icon_size}px" height="${icon_size}px" viewboxstring="0 0 ${icon_size} ${icon_size}" viewBox="0 0 ${icon_size} ${icon_size}" preserveAspectRatio="xMidYMid meet" style="fill:#symbol_color#;">` +
			`<g  id='border' fill='${borderColor}'>` +
			`<rect x="0" y="${maxRadius}" width="${maxRadius}" height="${maxRadius}" />` +
			`<circle cx="${maxRadius}" cy="${maxRadius}" r="${maxRadius}" />` +
			`</g>` +
			`<g id='shape' fill='#svg_color#' stroke="none">` +
			`<rect x="${borderSize}" y="${maxRadius}" width="${maxRadius - borderSize}" height="${maxRadius - borderSize}" />` +
			`<circle cx="${maxRadius}" cy="${maxRadius}" r="${maxRadius - borderSize}" />` +
			`</g>` +
			`<g style="transform:scale(0.66);transform-origin:center;" >` +
			`#svg_icon#` +
			`</g>` +
			`</svg>`;
		return svg_icon_template;
	}

	function calc_svg_html(svgIcon, bkgColor, size, symbolColor, borderSize, borderColor)
	{
		var svgHtml = get_svg_template(size, borderSize, borderColor)
			.replace("#svg_icon#", svgIcon)
			.replace("#svg_color#", bkgColor || DEFAULT_BACKGROUND_COLOR)
			.replace("fill:#231f20", `fill:${symbolColor || DEFAULT_SYMBOL_COLOR}`)
			.replace("#symbol_color#", symbolColor || DEFAULT_SYMBOL_COLOR);
		return svgHtml.trim();
	}

	function generate_base64_svg_image()
	{
		var svg = document.getElementById("svg_template");
		var xmlString = (new XMLSerializer()).serializeToString(svg);
		var base64_svg = window.btoa(xmlString);
		var image_base64 = 'data:image/svg+xml;base64,' + base64_svg;
		return image_base64;
	}
})();

