(function()
{
	createNamespace("TF.Modal.Grid").FindScheduleForStudentInteractiveModalViewModel = FindScheduleForStudentInteractiveModalViewModel;

	function FindScheduleForStudentInteractiveModalViewModel(data)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.sizeCss = "modal-dialog-xl";
		self.title(data.student.Name + " Weekly Schedule");
		self.contentTemplate("Modal/FindScheduleWithRequirementInteractive");
		self.obPositiveButtonLabel("Stop");
		self.model = new TF.Control.FindScheduleWithRequirementInteractiveViewModel(self, data);
		self.data(self.model);
	}

	FindScheduleForStudentInteractiveModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	FindScheduleForStudentInteractiveModalViewModel.prototype.constructor = FindScheduleForStudentInteractiveModalViewModel;

})();