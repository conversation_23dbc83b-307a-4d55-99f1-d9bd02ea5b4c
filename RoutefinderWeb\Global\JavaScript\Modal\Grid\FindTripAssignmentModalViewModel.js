(function()
{
	createNamespace("TF.Modal.Grid").FindTripAssignmentModalViewModel = FindTripAssignmentModalViewModel;

	function FindTripAssignmentModalViewModel(selectedCount)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.sizeCss = "modal-dialog-sm";
		self.title("Find Student Assignments");
		self.contentTemplate("Modal/FindTripAssignment");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel("Apply");
		self.obNegativeButtonLabel("Close");

		self.model = new TF.Control.FindTripAssignmentViewModel(selectedCount, self.obDisableControl);
		self.data(self.model);
	}

	FindTripAssignmentModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	FindTripAssignmentModalViewModel.prototype.constructor = FindTripAssignmentModalViewModel;

	FindTripAssignmentModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		var result = self.model.apply();
		self.positiveClose(result);
	};

})();