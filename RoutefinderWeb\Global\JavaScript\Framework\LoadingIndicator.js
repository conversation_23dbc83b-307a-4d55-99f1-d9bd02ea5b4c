﻿(function()
{
	createNamespace("TF").LoadingIndicator = LoadingIndicator;

	//the main purpose of this class is not to indicate loading state but to block user input of different kinds
	function LoadingIndicator($element)
	{
		var self = this;
		this._$element = $element;
		$element.hide();
		this._$overlay = $element.find(".overlay").hide();
		this._$spinner = $element.find(".spinner").hide();
		this._$progressbar = $element.find(".progressbar").hide();
		self.$kendoProgress = self._$progressbar.find(".progress-bar");
		this._$subtitle = $element.find(".spinner .subtitle").hide();
		this._counter = 0;
		this._handle = null;

		this.subtitle = ko.observable("");
		this.subtitle.subscribe(this._updateSubtitleDiplay.bind(this));
		this._updateSubtitleDiplay(this.subtitle());

		this._defaultSubtitle = 'Loading';

		this.reminderLoadingStatus = new TF.Events.Event();

		self.progressLabel = ko.observable("Waiting");
		self.$kendoProgress.kendoProgressBar({
			min: 0,
			max: 100,
			type: "percent",
			animation: false
		});
	}

	LoadingIndicator.prototype = {
		setSubtitle: function(subtitle)
		{
			if (this._counter <= 0)
				this.subtitle(subtitle);
		},
		tryHide: function(clearAll)
		{
			this._counter--;
			this._hidehandle = setTimeout(function()
			{
				this._hide();
			}.bind(this), 200);
			if (clearAll && this._counter > 0)
			{
				this.tryHide(clearAll);
			}
		},
		ensureHide: function()
		{
			this._counter = 0;
			this._hide();
		},
		_hide: function()
		{
			if (this._counter <= 0)
			{
				this._counter = 0;
				clearTimeout(this._handle);
				this._$overlay.hide();
				this._$element.removeClass("no-overlay").hide();
				this._$spinner.hide();
				this._$progressbar.hide();
				this.subtitle(this._defaultSubtitle);
				this.reminderLoadingStatus.notify(false);
			}
		},
		show: function(progressbar, overlay, delayTime, operations)
		{
			this._counter++;
			if (operations && operations.elements)
			{
				this._$element.find('#operArea').remove()
				var operArea = $("<div id='operArea' ></div>")
				_.each(operations.elements, ele =>
				{
					operArea.append(ele);
				})
				this._$element.find('.spinner').append(operArea);

			} else
			{
				this._$element.find('#operArea').remove()
			}
			this._$element.show();
			this.reminderLoadingStatus.notify(true);
			clearTimeout(this._hidehandle);
			var self = this;
			this._handle = setTimeout(function()
			{
				if (self._counter != 0)
				{
					this._$overlay.show();
					if (overlay === false)
					{
						this._$element.addClass("no-overlay");
					}
					this._$spinner.show();
					if (progressbar)
					{
						this._$progressbar.show();
					}
				}
			}.bind(this), delayTime || (progressbar ? 0 : 1000));
		},
		showImmediately: function()
		{
			if (!this.subtitle())
			{
				this.setSubtitle(this._defaultSubtitle);
			}

			this._counter++;
			this._$element.show(0);
			this.reminderLoadingStatus.notify(true);
			if (this._counter != 0)
			{
				this._$overlay.show(0);
				this._$spinner.show(0);
			}
		},
		isShowing: function()
		{
			return this._counter != 0 && this._$element.is(":visible");
		},
		resetProgressbar: function()
		{
			var self = this,
				progressbar = self.$kendoProgress.data("kendoProgressBar");
			progressbar.value(0);
			self.progressLabel("Waiting");
		},
		changeProgressbar: function(val, message, force, showStatus)
		{
			var self = this;
			if (force)
			{
				self._$progressbar.show();
			}

			if (self._$progressbar.is(":visible"))
			{
				if ($.isNumeric(val))
				{
					var progressbar = self.$kendoProgress.data("kendoProgressBar");
					progressbar.value(val);
				}
				if (message)
				{
					self.progressLabel(message);
					self.subtitle(message);
				}
			}
			self._$progressbar.find(".k-progress-status").css({ display: showStatus ? "none" : "block" });
		},
		_updateSubtitleDiplay: function(subtitleText)
		{
			if (subtitleText != "")
			{
				this._$subtitle.show();
			}
			else
			{
				this._$subtitle.hide();
			}
		},
		hideWhenError: function()
		{

		},
		getCurrentProgress: function()
		{
			var self = this;
			if (!self._$progressbar.is(":visible"))
			{
				return NaN;
			}

			return self.$kendoProgress.data("kendoProgressBar").value();
		},
	};
})();