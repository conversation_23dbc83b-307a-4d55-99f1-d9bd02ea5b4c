(function()
{
	createNamespace('TF.Control').PDE1049ReportSettingViewModel = PDE1049ReportSettingViewModel;

	function PDE1049ReportSettingViewModel()
	{
		TF.Control.BaseStateReportSettingViewModel.call(
			this,
			{
				dataType: "trip",
				customFields: tf.PDE1049DataValidationVehicleGridDefinition.getPDE1049Validators()
			});

		this.obSchoolYear = ko.observable();
		this.obAdminUnitNumber = ko.observable();

		var startYear = endYear = new Date().getUTCFullYear();
		var schoolTime = new Date(startYear, 7, 1);
		if (new Date() >= schoolTime)
		{
			endYear = endYear + 1;
		}
		else
		{
			startYear = startYear - 1;
		}
		this.obSchoolYear(`${startYear}-${endYear}`);
	}

	PDE1049ReportSettingViewModel.prototype.constructor = TF.Control.BaseStateReportSettingViewModel;
	PDE1049ReportSettingViewModel.prototype = Object.create(TF.Control.BaseStateReportSettingViewModel.prototype);

	PDE1049ReportSettingViewModel.prototype.apply = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(isValid =>
			{
				if (!isValid)
				{
					return;
				}

				let getTripIds = null;

				switch (+this.specifyRecordsType())
				{
					case TF.Enums.SpecifyRecordsType.Filter:
						const selectedFilter = this.filters().find(o => o.Id === this.filterId());
						getTripIds = tf.dataTypeHelper.getRecordIds(
							null,
							"trip",
							null,
							{ WhereClause: selectedFilter.WhereClause }
						).then(res => res.Items);
						break;
					case TF.Enums.SpecifyRecordsType.SpecificRecords:
						getTripIds = this.records().map(o => o.Id);
						break;
					case TF.Enums.SpecifyRecordsType.AllRecords:
					default:
						break;
				}

				return Promise.resolve(getTripIds)
					.then((idList) =>
					{
						console.log(idList);

						const result = {
							SchoolYear: this.obSchoolYear(),
							AdminUnitNumber: this.obAdminUnitNumber(),
							TripIds: idList
						};

						return result;
					});
			});
	};
})()