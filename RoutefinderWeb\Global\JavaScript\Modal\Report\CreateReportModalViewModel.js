(function()
{
	createNamespace('TF.Modal.Report').CreateReportModalViewModel = CreateReportModalViewModel;

	function CreateReportModalViewModel(options)
	{
		var self = this,
			modalTitle = "Create New Report",
			positiveButtonLabel = "Create";

		TF.Modal.BaseModalViewModel.call(self);

		self.title(modalTitle);
		self.sizeCss = "modal-dialog-sm";
		self.contentTemplate("workspace/report/CreateReport");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel(positiveButtonLabel);
		self.viewModel = new TF.Control.Report.CreateReportViewModel(options);
		self.data(self.viewModel);
	}

	CreateReportModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	CreateReportModalViewModel.prototype.constructor = CreateReportModalViewModel;

	CreateReportModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.viewModel.validate().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};

	CreateReportModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose();
	};

	CreateReportModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};
})();

