.routingmap_panel .item-header.panel-grid-header.custommap-palette-head {
	background-color: #E0E0E0;
	border: 1px solid #9633f3;
	margin-left: 0px;
	width: 100%;

	&.custommap-palette-head {
		border: 1px solid #9633f3;

		&:hover {
			.center {
				display: block;
			}
		}
	}

	.title {
		line-height: 26px;
		margin-top: 2px;
		border: none;
		background-color: #E0E0E0;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

.routingmap_panel .item-header.panel-grid-header {
	.file_blank.icon {
		background-image: url('../img/Routing Map/CustomMap/file_blank.png');
	}

	.file_csv.icon {
		background-image: url('../img/Routing Map/CustomMap/file_csv.png');
	}

	.file_gpx.icon {
		background-image: url('../img/Routing Map/CustomMap/file_gpx.png');
	}

	.file_kml.icon {
		background-image: url('../img/Routing Map/CustomMap/file_kml.png');
	}

	.file_shp.icon {
		background-image: url('../img/Routing Map/CustomMap/file_shp.png');
	}

	.file_geojson.icon {
		background-image: url('../img/Routing Map/CustomMap/file_geojson.png');
	}

	.globe.icon {
		background-image: url('../img/Routing Map/CustomMap/globe_white.png');
	}

	.web-link.icon {
		background-image: url('../img/Routing Map/CustomMap/web-link.png');
	}

	.trash.icon {
		background-image: url('../../global/img/menu/Delete-Black.svg');
	}
}

.routingmap_panel .list .item-container .item-header.panel-grid-header.mapediting-palette-head .head-icon {
	margin: 8px 8px 6px 8px;
}

.fresh-group {
	margin-bottom: 20px;

	.fresh-group-input {
		margin-left: 15px;
		margin-top: 5px;
		line-height: 20px;
		display: flex;

		.fresh-group-input-checkbox {
			vertical-align: top;
			margin-right: 2px;
		}

		input[type="text"] {
			margin: 0px 3px;
			line-height: 16px;
		}

		.fresh-group-input-textbox {
			width: 50px;
			text-align: right;
		}
	}
}


.form-group .k-colorpicker {
	border-radius: inherit;
	display: block;
	width: 30px;

	padding-right: 0;

	>.k-select {
		opacity: 0;
	}
}