(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.MailingStateDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	};

	namespace.MailingStateDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);
	namespace.MailingStateDataModel.prototype.constructor = namespace.MailingStateDataModel;

	namespace.MailingStateDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "", toMapping: function(v) { return (v || "").trim().toUpperCase(); } }
	];
})();