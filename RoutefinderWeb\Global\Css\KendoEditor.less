﻿@systemColor: #db4d37;

body {
	height: 100%;
	min-height: 100%;
}

.editor-options-wrap {
	display: flex;
	-moz-user-select: none;
	-ms-user-select: none;
	-webkit-user-select: none;
	user-select: none;

	.option {
		position: relative;
		top: -1px;
		color: white;
		background-color: #BCBCBC;
		border: 1px solid #BCBCBC;
		border-top: none;
		width: 60px;
		text-align: center;
		padding-top: 8px;
		padding-bottom: 5px;
		cursor: pointer;

		&.design {
			border-right: none;
		}

		&.html {
			border-left: none;
		}
	}

	.selected {
		position: relative;
		top: -1px;
		color: #333333;
		background-color: #eae8e8;
		border: 1px solid #D0D0D0;
		border-top: none;
		width: 60px;
		text-align: center;
		padding-top: 8px;
		padding-bottom: 5px;
		cursor: default;
	}
}

.html-editor-wrapper {
	background-image: none, linear-gradient(to bottom, rgba(255, 255, 255, 0.6) 0, rgba(255, 255, 255, 0) 100%);
	background-position: 50% 50%;
	background-color: #eae8e8;
	border-spacing: 2px;
	border: 1px solid #C5C5C5;

	.merge-doc-header {
		margin-top: 6px;
		width: calc(~"100%");
		height: calc(~"25%");
		outline: none;
	}

	.MergeDocumentContent {
		padding: 0;
		width: calc(~"100%");
		height: calc(~"100%");
		outline: none;
	}

	.header-xor-footer {
		height: calc(~"75%");
	}

	.header-and-footer {
		height: calc(~"50%");
	}

	.merge-doc-footer {
		padding: 0;
		width: calc(~"100%");
		height: calc(~"25%");
		outline: none;
	}

	.merge-doc-html-editor {
		padding: 0;
		margin: 6px 2px 1px 4px;
		width: calc(~"100% - 8px");
		height: calc(~"100% - 39px");
		border: 1px solid #d5d5d5;
		outline: none;

		&.without-title {
			height: calc(~"100% - 9px");
		}
	}

	.merge-doc-header .title,
	.MergeDocumentContent .title,
	.merge-doc-footer .title {
		font-family: "SourceSansPro-SemiBold";
		font-weight: bold;
		color: #262626;
		white-space: nowrap;
		padding-top: 9px;
		padding-bottom: 9px;
		padding-left: 9px;
		font-size: 16px;
		width: 100%;
		height: 42px;
		outline: none;
	}

	.merge-doc-header .title {
		margin-top: 7px;
	}
}

a.k-tool-icon {
	cursor: pointer;
}

.k-editor-dialog {
	.k-dialog-insert {
		background: #333333;
		box-shadow: none;
		border-color: #444444;

		&:hover {
			background: #333333;
			box-shadow: none;
			border-color: #444444;
		}

		&:active {
			background: #333333;
			box-shadow: none;
			border-color: #444444;
		}

		&:focus {
			background: #333333;
			box-shadow: none !important;
			border-color: #444444;
		}
	}

	.k-dialog-close {
		background: #e4e4e4;
		box-shadow: none;
		border-color: #e4e4e4;

		&:hover {
			background: #e4e4e4;
			box-shadow: none;
			border-color: #e4e4e4;
		}

		&:active {
			background: #e4e4e4;
			box-shadow: none;
			border-color: #e4e4e4;
		}

		&:focus {
			background: #e4e4e4;
			box-shadow: none !important;
			border-color: #e4e4e4;
		}
	}
}

.message-description {
	color: #999;
}

body .tfmodal-container .message-center {
	font-size: 13px;
}

.k-mobile .message-center-mobile {
	width: 85%;

	.modal-footer .btn {

		&.negative,
		&.other {
			display: none;
		}

		&.tf-btn-black {
			background-color: #333333;
			width: 100%;
		}
	}

	.content {
		overflow: auto;
		max-height: 300px;
		width: 100%;
	}

	.language-options {
		text-align: center;
		margin-bottom: 15px;

		.divider {
			display: inline;
		}

		.option {
			display: inline;

			&.selected {
				color: #333333;
			}
		}
	}
}

message-center-mobile,
.message-center {
	.negative.btn {
		display: none;
	}

	p {
		margin: 0 0 1em;
		padding: 0 .2em
	}

	.k-marker {
		display: none;
	}

	.k-paste-container,
	.Apple-style-span {
		position: absolute;
		left: -10000px;
		width: 1px;
		height: 1px;
		overflow: hidden
	}

	ul,
	ol {
		padding-left: 2.5em
	}

	span {
		-ms-high-contrast-adjust: none;
	}

	a {
		color: #00a
	}

	code {
		font-size: 1.23em
	}

	telerik\3Ascript {
		display: none;
	}

	.k-table {
		table-layout: fixed;
		width: 100%;
		border-spacing: 0;
		margin: 0 0 1em;
	}

	.k-table td {
		min-width: 1px;
		padding: .2em .3em;
	}

	.k-table,
	.k-table td {
		outline: 0;
		border: 1px dotted #ccc;
	}

	.k-table p {
		margin: 0;
		padding: 0;
	}
}

.k-mobile .page-container .main-body .setting-configuration .edit-content {
	height: calc(~"100% - 140px");
}

.k-group-start.k-group-end.k-tool {
	border-radius: 0;
}

.k-tool.k-group-start,
.k-toolbar .k-split-button .k-button,
.k-toolbar .k-button-group .k-group-start {
	border-radius: 0;
}

.k-tool.k-group-end,
.k-toolbar .k-button-group .k-group-end,
.k-toolbar .k-split-button .k-split-button-arrow {
	border-radius: 0;
}

.container.kendoEditorContainerMergeDoc {
	width: 100% !important;
	height: 100%;

	.merge-doc-checkin {
		height: 100%;
		margin: 0 -15px;

		.editor-wrapper {
			height: calc(~"100% - 50px");

			&.merge-doc-with-subject {
				height: calc(~"100% - 118px");
			}

			.text-editor-wrapper,
			.html-editor-wrapper {
				height: calc(~"100% - 39px");
			}
		}

		&.disabled {
			pointer-events: none;

			.mergedoc-editor-paper,
			.category {
				pointer-events: none;
			}
		}
	}
}

.merge-doc-subject-container {
	margin-bottom: 10px;

	.k-editor {
		width: 100%;
		height: 30px;
		border-spacing: 0px;
		border: none;

		.k-editable-area {
			height: 25px;
			padding-top: 4px;
			padding-left: 5px;
			padding-right: 5px;
		}
	}

	.k-editor-toolbar {
		display: none;
	}
}

.merge-doc-subject-body {
	border: 1px solid #D0D0D0 !important;
	overflow: hidden;
	white-space: nowrap
}

.merge-document.btn-edit-page-settings {
	width: 40px;
	margin-left: auto;
	background-color: #BCBCBC;
	border: 1px solid #D0D0D0;
	border-bottom: none;
	background-repeat: no-repeat;
	background-position: center;
	background-image: url(../../global/img/menu/Edit-Black.png);
}

.mergedoc-variables-panel {
	height: 100%;
	border-right: #4b4b4b 3px solid;

	.mergedoc-vars-panel-wrapper {
		position: relative;
		outline: none;
		height: 100%;

		.mergedoc-subject-label {
			margin-right: 5px;
		}

		@mergedoc-vars-panel-header-height: 56px;

		.header {
			height: @mergedoc-vars-panel-header-height;
			line-height: @mergedoc-vars-panel-header-height;
			padding-left: 16px;
			color: #262626;
			white-space: nowrap;
			border-bottom: 1px solid #d5d5d5;
		}

		.merge-search-container {
			width: 307px;
			height: 33px;
			position: relative;
			margin-top: 1px;
			padding: 0 10px;

			.merge-search-box-container {
				position: relative;
				//margin-bottom: 6px;
				display: block;

				.merge-nested-search-box {
					font-family: Arial, Helvetica, sans-serif;
					font-size: 12px;
					color: #545454;
					padding: 8px 32px 8px 22px;
					height: 32px;
					width: calc(100% - 8px);
					margin: 0;
					border: none;
					//border-bottom: 1px solid #d5d5d5;
					background-image: url(../../global/img/search/DataFieldSearch.svg);
					background-size: 12px 12px;
					background-repeat: no-repeat;
					background-position: left center;
					transition: background-image 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
				}

				.merge-nested-search-box:focus+.nested-search-input-focus-underline {
					background-color: #4E80F4;
				}

				.nested-search-input-focus-underline {
					background-color: #ccc;
					height: 1px;
					width: 100%;
					position: absolute;
					bottom: 0;
					left: 0;
					transition: all 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
				}

				.merge-nested-search-box:focus {
					width: 100%;
					border: none;
					//border-bottom: 1px solid #d5d5d5;
					background-image: url("../../global/img/search/DataFieldSearchActive.svg");
					background-size: 12px 12px;
				}

				input {
					outline: none;
				}

				input:placeholder-shown {
					font-style: italic;
				}

				input:focus {
					outline-color: transparent;
				}
			}

			.nested-search-clear-button {
				width: 10px;
				height: 10px;
				cursor: pointer;
				position: absolute;
				right: 21px;
				top: 11px;
				opacity: .8;
				background-image: url('../../global/img/search/SearchBoxClearButton.svg');
				background-size: cover;
				background-position: center;
				background-repeat: no-repeat;
			}

			.nested-search-clear-button:hover {
				filter: brightness(80%);
			}

			.nested-search-clear-button:active {
				opacity: 1;
			}
		}

		.list {
			height: calc(~"100% - @{mergedoc-vars-panel-header-height} - 33px");
			border: 1px solid #ccc;
			overflow-y: auto;
			overflow-x: hidden;
			pointer-events: auto;
			border-top: none;

			.top-title {
				position: absolute;
				top: 57px;
				background: #fff;
				width: calc(100% - 20px);
			}

			.category {
				margin-left: 14px;
				position: relative;

				.category-title-container {
					font-weight: bold;
					cursor: pointer;
					position: sticky;
					top: 0px;
					width: 100%;
					background: #fff;
					z-index: 2;

					.category-title {
						text-overflow: ellipsis;
						overflow: hidden;
						white-space: nowrap;
					}
				}

				.wrTrEi {
					cursor: pointer;
					margin-left: 5px;
					height: 12px;
					width: 12px;
					vertical-align: middle;
					transform: rotate(-90deg);
					background-image: none !important;
					background-size: 12px 12px;
					background-repeat: no-repeat;
					background-position: center;
					float: left;
				}
			}

			.category.category-collapse .category-title-container:before {
				content: " ";
				border-top: 5px solid transparent;
				border-bottom: 5px solid transparent;
				border-left: 5px solid black;
				position: absolute;
				left: 6px;
				top: 4px;
			}

			.category.category-expand .category-title-container:before {
				content: " ";
				border-left: 5px solid transparent;
				border-right: 5px solid transparent;
				border-top: 5px solid black;
				position: absolute;
				left: 3px;
				top: 7px;
			}

			.mergedoc-item-container {
				list-style-type: none;
				padding-left: 0px;

				.mergedoc-item {
					&:focus {
						background-color: #ddedfb;
					}

					outline: none;
					padding-left: 30px;
					display: flex;
					align-items: center;
					cursor: pointer;

					&:hover {
						background-color: #ddedfb;
						-webkit-touch-callout: all;
						-webkit-user-select: all;
						-khtml-user-select: all;
						-moz-user-select: all;
						-ms-user-select: text;
						user-select: all;

						&.disabled {
							background-color: transparent;
							-webkit-touch-callout: none;
							-webkit-user-select: none;
							-khtml-user-select: none;
							-moz-user-select: none;
							-ms-user-select: none;
							user-select: none;
						}
					}

					&.disabled {
						opacity: 0.7;
						cursor: default;
					}
				}
			}
		}
	}
}

.textwidget {
	font-family: "SourceSansPro-Regular", Arial;

	span[data-field] {
		cursor: pointer;
	}
}

.merge-doc-body {
	margin: 0;

	&.mergedoc-editor-label {
		overflow: hidden;
	}

	*[data-field] {
		cursor: default;
	}

	p {
		margin-bottom: 6px;
	}

	table {
		margin: 0 .2em;
	}

	img.linked-image-placeholder {
		background-image: url(../../global/img/photo.png);
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	img[data-field] {
		background-image: url(../../global/img/photo.png);
		background-size: 100% 100%;
		background-repeat: no-repeat;
		vertical-align: baseline;
	}

	span[data-field] {
		border: 1px solid #C5C5C5;
		border-radius: 3px;
		margin: 0px 1px;
	}

	&[contenteditable=true] {
		span[data-field] {
			user-select: all;
			-moz-user-select: all;
			white-space: nowrap;

			&::after {
				background-image: url(../../global/img/gray_del.svg);
				background-position: right center;
				background-repeat: no-repeat;
				content: " ";
				display: inline-block;
				width: 16px;
				height: 16px;
				vertical-align: middle;
				cursor: pointer;
			}
		}

		table[data-field] {
			user-select: all;
			-moz-user-select: all;
			background-color: #f4f5f7;
			font-weight: bold;
			border-style: none;
			max-width: 9.5in;
			width: calc(~"100% - 20px");
			position: relative;

			&::after {
				background-image: url(../../global/img/gray_del.svg);
				background-position: right center;
				background-repeat: no-repeat;
				content: " ";
				width: 16px;
				height: 100%;
				cursor: pointer;
				position: absolute;
				top: 0;
				right: -17px;
				background-color: #f4f5f7;
				border: 1px solid #ccc;
				border-left: none;
			}

			td,
			span,
			p {
				font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
				font-size: 14px !important;
				padding-left: 3px !important;
				margin-left: 0px !important;
				font-style: unset !important;
				text-decoration: none !important;
				background-color: transparent !important;
				color: black !important;
				text-align: left !important;
			}

			td {
				border: 1px solid #ccc !important;
			}
		}
	}
}

.label-right {
	label {
		text-align: right;
		padding-right: 1px;
	}

	input[type=number] {
		display: inline-block;
		width: 40%;
		padding-right: 0;

		-moz-appearance: textfield;

		&:focus,
		&:hover {
			-moz-appearance: number-input;
		}
	}
}

.k-editor .k-editor-toolbar {
	.k-dropdown-wrap>.k-select {
		width: 26px;
		height: 26px;
		min-height: 26px;
		line-height: 26px;
	}

	.k-input {
		height: 26px;
	}

	span.k-dropdown-wrap {
		height: 26px;
		padding-right: 26px;
		box-sizing: content-box;
	}

	span.k-colorpicker.k-header {
		width: auto;
	}

	span.k-picker-wrap {
		height: 26px;
		box-sizing: content-box;
	}

	span.k-picker-wrap .k-select {
		height: 26px;
		line-height: 26px;
	}
}


.merge-document-dataentry {
	.dataentry-paginator .iconbutton {
		background-size: auto;
		height: 24px;
	}
}

.document-dataentry .dataentry-container-init.merge-document-dataentry {
	height: 100%;
	padding-bottom: 0;
}

.mergedoc-subject-label,
.mergedoc-edit-title,
.mergedoc-subtitle {
	font-family: "SourceSansPro-SemiBold";
	font-weight: bold;
	color: #262626;
	white-space: nowrap;
}


.mergedoc-subject-label,
.mergedoc-edit-title {
	font-size: 27px;
}

.mergedoc-subject-label {
	text-transform: uppercase;
}

.mergedoc-subtitle {
	display: block;
	padding-top: 9px;
	font-size: 16px
}

.mergedoc-edit-title {
	margin: 0 6px 0 4px;
	margin-left: -3px;
	padding: 4px 2px;
	width: 100%;

	line-height: 34px;
	outline: none;
	border: 1px solid transparent;
	color: #262626;
}

.mergedoc-edit-title-group.has-error .mergedoc-edit-title,
.mergedoc-edit-title:hover,
.mergedoc-edit-title:focus {
	border: 1px solid #ccc;
}

.mergedoc-editor-header {
	display: flex;
	height: 56px;
	padding: 0 18px;
	justify-content: space-between;
	align-items: center;

	.mergedoc-edit-title-group {
		margin-bottom: 0;
		width: calc(~"100% - 280px");
		max-width: 1366px;
	}

	.alert {
		display: inline;
		font-size: 12px;
	}

	.buttons {
		display: flex;
		align-items: center;

		.iconbutton {
			margin-left: 20px;
			float: left;
			width: 26px;
			height: 26px;
			background-size: 26px 26px;
			margin-top: 3px;
		}
	}
}

.mergedoc-editor-panel {
	height: calc(~'100% - 56px');
	padding: 0 18px 0 18px;
	.attach-report-container {
		margin-top: 20px;
		table {
			width: calc(~'100% - 1px');
		}
	}
}

.merge-document-dataentry {
	.parent-container {
		display: flex;
	}

	.left-container {
		width: 310px;
	}

	.height-reset {
		position: relative;
		height: 100%;
	}

	.right-container {
		width: 100%;
	}
}

.merge-doc-toolbar {
	border-bottom-style: none;
	padding-left: 5px;
	padding-top: 4px;
	background-image: none, linear-gradient(to bottom, rgba(255, 255, 255, .6) 0%, rgba(255, 255, 255, .6) 100%);
}

.merge-doc-with-subject .text-editor-wrapper div.k-editor:not(.merge-doc-toolbar) {
	border-top-style: none;
	height: calc(~"100% - 35px");
	border-spacing: 2px;
	padding: 0px 2px 2px;
	box-sizing: border-box;
}

.text-editor-wrapper table.k-editor.k-editor-widget {
	border-top-style: none;
	height: calc(~"100% - 35px");
	border-spacing: 2px;
	padding: 0px 2px 2px;
	box-sizing: border-box;
}

.merge-doc-editor-outline {
	border: 1px solid #c5c5c5;
	background-color: #f0f0f0;
	height: calc(~"100% - 37px");
	overflow: auto;
	padding: 20px 0;
	pointer-events: auto;

	.mergedoc-editor-paper {
		border-spacing: 0px;
		border: 1px solid #999;
		background-color: white;
		margin: 0 auto;
		overflow: hidden;
		position: relative;

		.k-editor {
			padding: 0;
			border-spacing: 0;
			border: none;
			position: absolute;

			iframe {
				display: block;
			}
		}

		.k-editable-area {
			border: 0;
		}

		.mergedoc-page-section {
			position: absolute;
			width: 100%;
			height: 0.5in;

			iframe {
				height: 0.5in;
			}
		}
	}
}

.k-checkbox:checked+.k-checkbox-label:after {
	color: @systemColor;
	border-color: #acacac;
}

.k-checkbox:focus+.k-checkbox-label:after,
.k-radio:focus+.k-radio-label:before {
	border-color: #acacac;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.k-checkbox-label:hover:after,
.k-checkbox:checked+.k-checkbox-label:hover:after {
	border-color: #acacac;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.k-checkbox-label:active:before {
	-webkit-box-shadow: none;
	box-shadow: none;
	border-color: transparent;
}

.k-checkbox:checked+.k-checkbox-label:active:before {
	-webkit-box-shadow: none;
	box-shadow: none;
	border-radius: unset;
}

.k-checkbox:checked+.k-checkbox-label:active:after {
	border-color: none;
}

/*fixed the bug of upgrade kendo ui version*/
.text-editor-wrapper table.k-editor.k-editor-widget {
	background-color: #eae8e8;
	margin-top: -2px;

	.k-editable-area {
		border: 1px #d5d5d5 solid;
	}
}

.merge-doc-with-subject .text-editor-wrapper div.k-editor:not(.merge-doc-toolbar) {
	background-color: #eae8e8;
	margin-top: -2px;

	.k-editable-area {
		border: 1px #d5d5d5 solid;
	}
}

.merge-doc-toolbar {
	background-color: #eae8e8 !important;
	background-image: none;
	padding: 3px 0 0 0;

	.k-editor-toolbar {
		background-color: #eae8e8 !important;
		border-bottom: 0;
	}
}

#ThankYouMessageEditor_forms .old-thank-you-message {
	text-align: center;
}

#ThankYouMessageEditor_forms 	table[data-field] { 
	&::after {
		box-sizing: border-box;
	}
}
.container.kendoEditorContainerMergeDoc .merge-doc-checkin .editor-wrapper.merge-doc-with-subject {
	height: calc(100% - 360px);
}

.k-editor {
	.k-editor-toolbar {
		padding: 2px 5px;
		background-color: #eae8e8;
		border: 0;

		.k-editor-widget {
			background-color: #eae8e8;
		}

		.k-colorpicker {
			width: auto;

			.k-picker-wrap {
				.k-icon.k-tool-icon {
					top: -1px !important;

					.k-selected-color {
						bottom: 0;
					}
				}
			}
		}
	}
}

.k-window.k-focus:has(.k-editor-dialog.k-editor-table-wizard-window) {
	height: calc(~"100% - 10px");
}