(function()
{
	const GRID_ROW_COLOR = {
		EVEN: "#ecf2f9",
		ODD: "#fff",
		SELECTED: "#ffffce"
	};

	createNamespace("TF.Grid").LightKendoGrid = LightKendoGrid;
	var defaults = {
		gridDefinition: { Columns: [] },
		additionGridDefinition: { Columns: [] },
		setRequestOption: null,
		setRequestURL: null,
		kendoGridOption: {},
		url: "",
		gridType: "",
		storageKey: "",
		height: "auto",
		onGridStateChange: null,
		showLockedColumn: true,
		showEyeColumn: false,
		eyeColumnVisible: true,
		showBulkMenu: true,
		loadLayout: true,
		onDataBound: null,
		showOperators: true,
		selectable: "multiple",
		directory: null,
		lockColumnTemplate: null,
		Id: "Id",
		isBigGrid: false,
		loadAllFields: false,
		showOverlay: true,
		isFromRelated: false,
		showSelectedCount: true,
		showOmittedCount: true,
		gridTypeInPageInfo: "",
		routeState: "",
		removeTabIndex: false,
		hasDetail: false
	};
	var bigGridTypes = ["staff", "student", "trip", "tripstop", "vehicle", "school", "georegion", "fieldtrip", "route", "studentattendanceschedule",
		"district", "contractor", "altsite", "document", "fieldtriptemplate", "contact", "report", "logireport", "reportlibrary", "scheduleddashboard", 'scheduledmergedocument', 'boundaryset', 'mapincident', 'street', 'parceladdresspoint'];
	var dataFormat = 'YYYY-MM-DD';
	var udfLazyLoadType = ['roll-up', 'case'];

	const SELECTED_ROW_CLASS = TF.KendoClasses.STATE.SELECTED;
	const MAIN_ROW_CLASS = "k-master-row";
	const SUB_ROW_CLASS = "k-detail-row";

	TF.Grid.KendoGridFilterCellHackHelper.init();

	function LightKendoGrid($container, options, gridState, geoFields, lazyLoadScrollContainer)
	{
		this.gridDefinitionHelper = new TF.Helper.GridDefinitionHelper();
		this.kendoGridFilterHelper = new TF.Helper.KendoGridFilterHelper(this);

		this.pendingTaskOnNextDataBound = null;
		this.geoFields = geoFields;
		this.filterClass = '.k-svg-i-filter';
		if (geoFields)
		{
			this.geoData = [];
		}
		this.initParameter($container, options, gridState);
		this.loadAndCreateGrid();

		if (options.hasDetail)
		{
			this.lightKendoGridDetail = new TF.Grid.LightKendoGridDetail(this);
		}
		this.lazyloadFields = { general: [], udf: [] };
		this.alreadyLoadId = { general: {}, udf: {} };
		this.fullfillGridBlankTimer = null;
		this.clickEventName = TF.isMobileDevice ? "touchstart" : "click";
		this.addHotLinkTimer = null;
		this.addHotLink = this.addHotLink.bind(this);
		this.isFilterReset = false;

		this.customTouchMoveLock = false;
		this.customTouchMoveEventName = "touchmove";
		this.customTouchClickEventName = TF.isMobileDevice ? "touchend" : "click";
		this.lazyLoadScrollContainer = lazyLoadScrollContainer;
	}

	LightKendoGrid.prototype._excludeOnlyForFilterColumns = function functionName(gridDefintion)
	{
		gridDefintion.Columns = gridDefintion.Columns.filter((column) => !column.onlyForFilter);
		return gridDefintion;
	};

	LightKendoGrid.prototype.initParameter = function($container, options, gridState)
	{
		var self = this;
		self.listFilters = TF.ListFilterHelper.initListFilters();
		self.$container = $container;
		self.options = $.extend(true, {}, defaults, options);
		self.subscriptions = [];

		if (!gridState)
		{
			gridState = { gridFilterId: null, filteredIds: null, filteredExcludeAnyIds: null, filterClause: null };
		}

		self.obSelectedGridFilterClause = ko.observable("");
		self.obHeaderFilters = ko.observableArray([]);
		self.obHeaderFilterSets = ko.observableArray([]);
		self._filteredIds = gridState.filteredIds;
		self._showBulkMenu = self.options.showBulkMenu === false ? false : true;
		self._gridState = gridState;

		// These two variables are for geo-search or any other outer source to affect grid filter ids. These ids only from map.
		self.additionalFilterIds = null;
		// Because the geo-search is based on the grid content, so the first search should ignore geo-search filter id list.
		self.shouldIncludeAdditionFilterIds = false;
		// Check the geo search is start or not, if not and filter changed, need recalculate the geo-search filter id list.
		self.isGeoSearching = false;
		// These are any other outer source to affect grid filter ids, these ids only from gird.
		self.additionalGridFilterIds = null;

		self._gridType = self.options.gridType;

		self.refreshGridColumnDefinition();
		self._filterHeight = 0;

		self.kendoGrid = null;
		self.searchOption = null;
		self._availableColumns = [];
		self._obSelectedColumns = ko.observableArray([]);
		self._obSelectedInvisibleColumns = ko.observableArray([]);

		self.obHasAnyLockedColumn = ko.observable(false);
		self.obFilteredExcludeAnyIds = ko.observable(self._gridState.filteredExcludeAnyIds);
		self.obTempOmitExcludeAnyIds = ko.observable([]);
		self.obTempOmitExcludeAnyIds.subscribe(self._omitIdsChange, self);

		self.getSelectedIds = ko.observableArray([]);
		self.getSelectedIds.subscribe(self._selectedIdsChange, self);
		self.getSelectedRecords = ko.observableArray([]);
		self.obSelectedIndex = ko.observable(-1);
		self.onCurrentGridRequestOption = ko.observable();

		self._obSortedItems = ko.observableArray([]);
		self.onDoubleClick = new TF.Events.Event();
		self.onRowsChangeCheck = new TF.Events.Event();
		self.onRowsChanged = new TF.Events.Event();
		self.onDataBoundEvent = new TF.Events.Event();
		self.onIdsChanged = new TF.Events.Event();
		self.onClearFilter = new TF.Events.Event();
		self.onFilterWithGeoSearch = new TF.Events.Event();
		self.onCtrlIPress = self.onCtrlIPress.bind(self);
		self.onCtrlOPress = self.onCtrlOPress.bind(self);
		self.onCtrlAPress = self.onCtrlAPress.bind(self);
		self.onShiftDown = self.onShiftDown.bind(self);
		self.onShiftUp = self.onShiftUp.bind(self);
		self.refreshClick = self.refreshClick.bind(self);
		self.onAltPressClick = new TF.Events.Event();
		self.isMacOs = isMacintosh();
		if (self.options.showOmittedCount && !self.options.isMiniGrid)
		{
			const bindCutKeys = (key) =>
			{
				tf.shortCutKeys.bind(`${key}+o`, self.onCtrlOPress, self.options.routeState, null, { isLastKey: self.options.isLastKey, isDetailGrid: self.options.isDetailGrid });
			};
			bindCutKeys("ctrl");
			self.isMacOs && bindCutKeys("meta");
		}
		//if delayShortCutKeysBinding is true shortcut key will bind in onDataBound function
		if (self.options.selectable && self.options.selectable.indexOf("multiple") != -1 && !self.options.delayShortCutKeysBinding && !self.options.isMiniGrid)
		{
			self.bindCtrlAAndCtrlI();
		}

		self.obClassicFilterSet = ko.observable(null);
		self.obFilteredRecordCount = ko.observable(0);
		self.obTotalRecordCount = ko.observable(0);
		self.obIsScrollAtBottom = ko.observable(false);
		self.tobeLockedColumns = [];
		self.allIds = [];
		self.obAllIds = ko.observableArray([]);
		self.overlay = true;

		self.obShowEyeColumn = ko.observable(self.options.showEyeColumn);
		self.permanentLockCount = ko.computed(function()
		{
			return self.options.permanendLockCount || self.obShowEyeColumn() ? 2 : 1; //There is always a locked column on the left; "map visible" column can be activated by openning map section.
		});
		self.filterDropDownListTimer;

		self.$alert = self.$container.closest(".doc.document-grid").find(".gridAlert");
		self.gridAlert = new TF.Grid.GridAlertViewModel(self.$alert);
		self.suspendRefresh = false;
		self.isBigGrid = options.isBigGrid === true || bigGridTypes.includes(self._gridType) && !options.isSmallGrid;
		self.currentDisplayColumns = ko.observableArray();
	};

	LightKendoGrid.prototype.bindCtrlAAndCtrlI = function()
	{
		const self = this;
		const bindCutKeys = (key) =>
		{
			tf.shortCutKeys.bind(`${key}+a`, self.onCtrlAPress, self.options.routeState, null, { isLastKey: self.options.isLastKey, isDetailGrid: self.options.isDetailGrid });
			tf.shortCutKeys.bind(`${key}+i`, self.onCtrlIPress, self.options.routeState, null, { isLastKey: self.options.isLastKey, isDetailGrid: self.options.isDetailGrid });
		}

		bindCutKeys("ctrl");
		self.isMacOs && bindCutKeys("meta");
	}

	LightKendoGrid.prototype.refreshGridColumnDefinition = function()
	{
		var self = this;

		self.options.gridDefinition = self._excludeOnlyForFilterColumns(self.options.gridDefinition);
		self._gridDefinition = self.options.gridDefinition = self.extendAdditionGridDefinition(self.options.gridDefinition, self.options.additionGridDefinition);
	};

	LightKendoGrid.prototype.loadAndCreateGrid = function()
	{
		//use setTimeout to fix "You cannot apply bindings multiple times to the same element." error on time box when use lightKendoGrid on init event;
		setTimeout(function()
		{
			// FilterItems or FilterSets of filterSet must have content, otherwise don't set defaultFilterData param.
			if (this.options.defaultFilterData?.filterSet)
			{
				this.options.kendoGridOption.autoBind = false;
			}

			this.createGrid();
			this._applyDefaultFilter();
		}.bind(this));
	};

	LightKendoGrid.prototype._applyDefaultFilter = function()
	{
		if (this.options.defaultFilterData?.filterSet)
		{
			// Convert FilterItems and FilterSets of filterSet to kendo filter items
			var kendoFilterItems = this.convertRequest2KendoFilterSet(this.options.defaultFilterData.filterSet);

			// default filter read, then set the filter icon after the element render, this settimeout is wait for the element render.
			this.kendoGrid.dataSource.filter(kendoFilterItems);
			setTimeout(function()
			{
				this.kendoGridFilterHelper.setColumnCurrentFilterIcon(this.$container, this.kendoGrid);
				this.setColumnCurrentFilterInput();
			}.bind(this), 50);
		}
	};

	LightKendoGrid.prototype._setGridState = function(state)
	{
		var promise;
		if (this.kendoGrid)
		{
			state = state || {};
			state.gridSuccessResponse = this.gridSuccessResponse;
			promise = this.rebuildGrid(state);
		}
		this.getSelectedIds([]);
		return promise || Promise.resolve();
	};

	/**
	 * Handler when grid selection has been changed.
	 *
	 * @param {Event} e
	 * @returns
	 */
	LightKendoGrid.prototype.onChange = function(e)
	{
		var self = this, records = [],
			selectedItems = $.map(self.kendoGrid.select(), function(item)
			{
				var row = $(item).closest("tr");
				if (row.hasClass(SUB_ROW_CLASS))
				{
					row.removeClass(SELECTED_ROW_CLASS);
					return;
				}
				var dataItem = self.kendoGrid.dataItem(row);

				if (!self._isRowSelectable(row))
				{
					row.removeClass(SELECTED_ROW_CLASS);
					return;
				}

				if (dataItem &&
					($.isNumeric(dataItem[self.options.Id]) || self.options.idSeparator) &&
					!Enumerable.From(records).Any(function(x) { return x[self.options.Id] === dataItem[self.options.Id]; })
				)
				{
					records.push(dataItem);
					return item;
				}
			});

		self.onRowsChangeCheck.notify(selectedItems);

		self.getSelectedRecords(records);
	};

	LightKendoGrid.prototype._isRowSelectable = function(row)
	{
		return !row.has('.disSelectable').length;
	}

	LightKendoGrid.prototype.bindDoubleClickEvent = function()
	{
		var self = this;
		this.$container.delegate(">.k-grid-container>.k-grid-content-locked>table>tbody>tr, >.k-grid-container>.k-grid-content>.k-virtual-scrollable-wrap>table>tbody>tr", "dblclick", function(e)
		{
			self.onGridDoubleClick(e);
		});
	};

	LightKendoGrid.prototype.onGridDoubleClick = function(e)
	{
		if (e.shiftKey || e.ctrlKey)
		{
			return;
		}

		var self = this,
			records = self.getSelectedRecords();
		if (records.length === 0 ||
			(!$(e.currentTarget).hasClass(SELECTED_ROW_CLASS) && !$(e.currentTarget).hasClass('fillItem')))
		{
			return;
		}

		self.onDoubleClick.notify(records[records.length - 1], e);
	};

	LightKendoGrid.prototype._initLinkTd = function()
	{
		this.$container.find(">.k-grid-container>.k-grid-content>.k-virtual-scrollable-wrap>table>tbody>tr>td").each(function(i, item)
		{
			var htmlText = $(item).html(),
				validateLink = this._validateLink(htmlText),
				validateMail = this._validateMail(htmlText);

			// Should skip here if the column is a HotLink
			if (this.HotLinkConfig && this.HotLinkConfig[this._gridType])
			{
				var fieldName = $(item).data("kendo-field");
				if (Array.isArray(this.HotLinkConfig[this._gridType]) && this.HotLinkConfig[this._gridType].indexOf(fieldName) > -1)
				{
					return;
				}
			}

			//if (validateLink || validateMail)
			if (validateLink)
			{
				var content;
				if (TF.isMobileDevice && validateLink)
				{
					content = $('<a>' + htmlText + '</a>').addClass('link').addClass('tf-grid-mobile-inner-link').on('click', function(e)
					{
						if (tf.isFromViewfinderApp)
						{
							TF.sendMessageToCordovaApp?.("OPEN_LINK", { url: htmlText });
						}
						else
						{
							window.open(htmlText, $(item).attr('linkTarget') || '_blank ');
						}

						e.stopPropagation();
					});
				}
				else
				{
					content = $('<div>' + htmlText + '</div>').addClass('link').css('padding', '0 .6em').css('cursor', 'pointer').attr('title', 'Press "Alt" and left click to open link');
					if ($(item).attr('linkTarget'))
					{
						content.attr("linkTarget", $(item).attr('linkTarget'));
					}
				}

				if (this._gridType === 'form')
				{
					content.addClass("form-link");
				}

				$(item).empty().append(content);
				$(item).addClass("has-link");
			}
			if (validateMail)
			{
				let content;
				if (TF.isMobileDevice && tf.isFromViewfinderApp)
				{
					content = $('<a>' + htmlText + '</a>').addClass('link').addClass('tf-grid-mobile-inner-link').on('click', function(e)
					{
						TF.sendMessageToCordovaApp?.("OPEN_LINK", { url: "mailto:" + htmlText });
						e.stopPropagation();
					});
					$(item).addClass("has-link");
				}
				else
				{
					content = $('<div><a href=mailto:' + htmlText + '>' + htmlText + '</a></div>').addClass('mail');
				}

				$(item).empty().append(content);
			}
		}.bind(this));
	};

	function onProcessHotLink(e)
	{
		if (e.altKey)
		{
			const link = $(e.currentTarget).children('.link');
			if (link.length > 0)
			{
				window.open(link.text(), link.attr("linkTarget") || "_blank");
			}
		}
		else if ($(e.currentTarget).children('.mail').length > 0)
		{
			window.open("mailto:" + $(e.currentTarget).children().text());
		}
	}

	function onKendoGridTDClickEvent(e)
	{
		onProcessHotLink.call(this, e);
	}

	function onKendoGridTDClickInAppEvent(e)
	{
		let url = null;
		const link = $(e.currentTarget).children('.link');
		if (link.length > 0)
		{
			url = link.text();
		}
		else if ($(e.currentTarget).children('.mail').length > 0)
		{
			url = "mailto:" + $(e.currentTarget).children().text();
		}

		url && TF.sendMessageToCordovaApp?.("OPEN_LINK", { url: url });
	}

	function onKendoGridTRClickEvent(e, self)
	{
		// click on toggle detail button, avoid select event.
		const row = $(this);
		if (row.hasClass("k-filter-row") || row.hasClass(SUB_ROW_CLASS) || row.find("th").hasClass("k-header"))
		{
			return;
		}

		if ($(e.target).closest(".k-hierarchy-cell").length > 0 && row.find(".k-hierarchy-cell").length > 0 && !row.hasClass(SELECTED_ROW_CLASS))
		{
			return;
		}
		self.needClearMainGridSelect = true;
		self._refreshGridBlank(true);
		if (self.kendoGrid)
		{
			var dataItem = self.kendoGrid.dataItem(this);
			if (!isHotLink(e))
			{
				self._onGridItemClick(dataItem, e);
			}
		}
	}

	function isHotLink(e)
	{
		return (e.altKey && TF.LightKendoGridHelper.isHotLinkNode($(e.target))) ||
			(TF.isMobileDevice && TF.LightKendoGridHelper.isHotLinkNode($(e.target)))
	}

	LightKendoGrid.prototype.bindAltClickEvent = function()
	{
		const rowSelector = `.k-grid-content tr:not('.${SUB_ROW_CLASS}') td`;

		// Only mobile device needs touch events.
		if (tf.isFromViewfinderApp)
		{
			this.$container.delegate(rowSelector, "click", onKendoGridTDClickInAppEvent.bind(this));
		}
		else if (!TF.isMobileDevice)
		{
			this.$container.delegate(rowSelector, "click", onKendoGridTDClickEvent.bind(this));
		}
		else
		{
			this.$container.delegate(rowSelector, "touchstart", function(e)
			{
				var self = this;
				var callRevert = self._onTouchStart(e);
				if (callRevert)
					setTimeout(function() { self._revertLinkToText(e); }, 10 * 1000);
			}.bind(this));

			this.$container.delegate(rowSelector, "touchend touchmove", function(e)
			{
				this._revertLinkToText(e);
			}.bind(this));
		}
	};

	LightKendoGrid.prototype._onTouchStart = function(e)
	{
		var callRevert = false;
		var htmlText = $(e.currentTarget).children().html();
		if ($(e.currentTarget).children('.link').length > 0)
		{
			// $(e.currentTarget).children().html("<div class='tf-grid-mobile-inner-link' href='" + htmlText + "'>" + htmlText + "</div>");
			// callRevert = true;
		}
		// else if ($(e.currentTarget).children('.mail').length > 0)
		// {
		// 	$(e.currentTarget).children().html("<a class='tf-grid-mobile-inner-link' href='mailto:" + htmlText + "'>" + htmlText + "</a>");
		// 	callRevert = true;
		// }
		return callRevert;
	};

	LightKendoGrid.prototype._revertLinkToText = function(e)
	{
		var $link = $(e.currentTarget).find('.tf-grid-mobile-inner-link');
		if ($link.length === 0)
			return;

		var htmlText = $link.html();
		$(e.currentTarget).children().html(htmlText);
	};

	LightKendoGrid.prototype._validateLink = function(pattern)
	{
		return TF.URLHelper.isValidFormat(pattern);
	};

	LightKendoGrid.prototype._validateMail = function(pattern)
	{
		var mailExpression = new RegExp(/^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/);
		return mailExpression.test(pattern);
	};

	LightKendoGrid.prototype.refreshClick = function()
	{
		this.obTempOmitExcludeAnyIds([]);
		this.refresh();
	};

	LightKendoGrid.prototype.initStatusBeforeRefresh = function()
	{
		this.obTempOmitExcludeAnyIds([]);
		this.getSelectedIds([]);
		this.allIds = [];
	};

	LightKendoGrid.prototype.clearDateTimeNumberFilterCellBeforeRefresh = function()
	{
		var self = this;
		var $dateNumberCells = self.$container.find('input[data-kendo-role="numerictextbox"].k-input-inner.date-number');
		$dateNumberCells.each((index) =>
		{
			let dateNumber = $($dateNumberCells[index]).data('kendoNumericTextBox');
			if (dateNumber && dateNumber.value() !== null)
			{
				dateNumber.value(null);
			}
		});
	};

	LightKendoGrid.prototype.refresh = function(isFromDashboardActiveFilter)
	{
		let self = this;
		self.overlayShow = true;
		self.getSelectedIds([]);
		self.allIds = [];
		isFromDashboardActiveFilter && (self.options.isFromDashboardActiveFilter = isFromDashboardActiveFilter);
		if (self.options.showOverlay && self.options.customGridType !== "dashboardwidget")
		{
			tf.loadingIndicator.showImmediately();
			//console.log("LightKendoGrid +1");
		}
		if (self.options.refreshForm && self.options.gridType.toLowerCase() === 'form')
		{
			self.options.refreshForm();
		}

		const alreadyInLazyloadQueue = self.lazyLoadScrollContainer
			&& self.lazyLoadScrollContainer.lazyLoadGridRequestList
			&& self.lazyLoadScrollContainer.lazyLoadGridRequestList.some(x => x.target === self.$container[0]);
		if (self.kendoGrid && self.kendoGrid.dataSource && !alreadyInLazyloadQueue)
		{
			self.kendoGrid.dataSource.read();
		}
		if (self.options.showOverlay)
		{
			setTimeout(function()
			{
				self.overlayShow = false;
				tf.loadingIndicator.tryHide();
				//console.log("LightKendoGrid -1");
			}, 1500);
		}
		this.clearLazyLoadId();
	};

	LightKendoGrid.prototype.clearLazyLoadId = function()
	{
		this.alreadyLoadId = { general: {}, udf: {} };
	}

	LightKendoGrid.prototype.rebuildGrid = function(state)
	{
		var self = this;
		state = state || {};
		if (state.gridSuccessResponse === false)
		{
			this.$container.find(".grid-filter-clear-all").trigger('mousedown');
			return Promise.resolve();
		}
		var visible = this.$container.is("visible");
		//tf.loadingIndicator.showImmediately();
		return new Promise(function(resolve)
		{
			visible && self.options.customGridType !== "dashboardwidget" && tf.loadingIndicator.showImmediately();
			//console.log("LightKendoGrid +1");
			setTimeout(function()
			{
				resolve();
			}, 0);
		})
			.then(function()
			{
				if (!this.kendoGrid)
				{
					visible && tf.loadingIndicator.tryHide();
					//console.log("LightKendoGrid -1");
					return Promise.reject();
				}
				this.getSelectedIds([]);
				this.clearLazyLoadId();
				this.overlayShow = true;
				this.obTempOmitExcludeAnyIds(state.obTempOmitExcludeAnyIds || []);
				var kendoOptions = this.kendoGrid.getOptions();
				if (kendoOptions.height !== 0)
				{
					// Fix UI issues after user change columns;
					// FIx UI issues if a light kendo grid is in tab;
					kendoOptions.height = this.getGridFullHeight();
				}
				kendoOptions.columns = this.getKendoColumn();
				kendoOptions.dataSource.sort = this.getKendoSortColumn();
				if (kendoOptions.columns.length == 1)
				{
					kendoOptions.columns[0].locked = false;
				}
				kendoOptions.sortable.mode = this.sortModel;
				this._removeInvisibleListFilterItems(this.getKendoColumn());
				kendoOptions.autoBind = true;
				if (state.resetFilter)
				{
					kendoOptions.dataSource.filter = {};
				}
				self.kendoGrid.setOptions(kendoOptions);
				this.disableBulkMenuColumnResize();
				this._initFilterDropDownListTimer();
				TF.CustomFilterHelper.initCustomFilterBtn(this.$container);
				TF.ListFilterHelper.initListFilterBtn(this.$container);
				this.kendoGridFilterHelper.addFilterClass(this.$container, this.kendoGrid);
				this.kendoGridFilterHelper.setColumnCurrentFilterIcon();
				this.setColumnCurrentFilterInput();
				this.bindGridFilter();
				this.bindFilterIconClick();
				this.setFilterIconByKendoDSFilter();
				this._refillAllListFilterFilterCellInput();
				this.disableFilterCellWhenEmptyOrNotEmptyApplied();
				this.createFilterClearAll();
				if (kendoOptions.columns.length == 1 && this.options.showLockedColumn)
				{
					this.$container.children("div.k-grid-content").children("table.k-grid-table.k-selectable").width(30);
				}
				this.autoLoadDataOnScrollBottom();
				this.bindCalendarButton();
				if (this.options.canDragDelete)
				{
					this.createDragDelete();
				}
				setTimeout(function()
				{
					this.overlayShow = false;
					visible && tf.loadingIndicator.tryHide();
				}.bind(this), this.isBigGrid ? 600 : 50);
				if (self.lightKendoGridDetail)
				{
					self.lightKendoGridDetail.detail = self.lightKendoGridDetail.setDetail();
					self.lightKendoGridDetail.onCreateGrid();
				}

			}.bind(this), 0);
	};

	LightKendoGrid.prototype._removeInvisibleListFilterItems = function(columns)
	{
		var self = this;

		var listFilterFieldNames = Object.keys(self.listFilters);
		var needDeleteListFilterColumns = listFilterFieldNames.filter(function(listFilterFieldName)
		{
			var result = columns.filter(function(column)
			{
				return listFilterFieldName === column.FieldName;
			});
			return result.length === 0;
		});

		needDeleteListFilterColumns.forEach(fieldName =>
		{
			delete self.listFilters[fieldName];
		});
	};

	LightKendoGrid.prototype._setCustomizetimePickerborderradius = function()
	{
		var CustomizetimePickers = $(".form-control.datepickerinput");
		CustomizetimePickers.map(function(idx, item)
		{
			$(item).css("border-radius", "0");
			$(item).css("float", "right");
			$(item).next().css("border-radius", "0");
		});
	};

	LightKendoGrid.BaseOperator = {
		string: {
			eq: "Equal To",
			neq: "Not Equal To",
			contains: "Contains",
			doesnotcontain: "Does Not Contain",
			startswith: "Starts With",
			endswith: "Ends With",
			isempty: "Empty",
			isnotempty: "Not Empty"
		},
		number: {
			eq: "Equal To",
			neq: "Not Equal To",
			isempty: "Empty",
			isnotempty: "Not Empty",
			lt: "Less Than",
			lte: "Less Than or Equal To",
			gt: "Greater Than",
			gte: "Greater Than or Equal To"
		},
		date: {
			eq: "Equal To",
			neq: "Not Equal To",
			isempty: "Empty",
			isnotempty: "Not Empty",
			lt: "Less Than",
			lte: "Less Than or Equal To",
			gt: "Greater Than",
			gte: "Greater Than or Equal To"
		},
		time: {
			eq: "Equal To",
			neq: "Not Equal To",
			isempty: "Empty",
			isnotempty: "Not Empty",
			lt: "Less Than",
			lte: "Less Than or Equal To",
			gt: "Greater Than",
			gte: "Greater Than or Equal To"
		},
		datetime: {
			eq: "Equal To",
			neq: "Not Equal To",
			isempty: "Empty",
			isnotempty: "Not Empty",
			lt: "Less Than",
			lte: "Less Than or Equal To",
			gt: "Greater Than",
			gte: "Greater Than or Equal To",
		},
		enums: {
			eq: "Equal To",
			neq: "Not Equal To"
		}
	};

	LightKendoGrid.DefaultOperator = {
		string: { custom: 'Custom' },
		number: { custom: 'Custom' },
		date: { custom: 'Custom' },
		time: { custom: 'Custom' },
		datetime: {
			custom: 'Custom'
		},
		enums: { custom: 'Custom' }
	};
	LightKendoGrid.DefaultOperator = jQuery.extend(true, {}, LightKendoGrid.BaseOperator, LightKendoGrid.DefaultOperator);

	LightKendoGrid.DefaultDateTimeOperator = {
		eq: "Equal To",
		neq: "Not Equal To",
		isempty: "Empty",
		isnotempty: "Not Empty",
		lt: "Less Than",
		lte: "Less Than or Equal To",
		gt: "Greater Than",
		gte: "Greater Than or Equal To",
	};

	LightKendoGrid.ExtendedDateTimeOperator = {
		all: 'All',
		lastmonth: 'Last Month',
		lastweek: 'Last Week',
		lastyear: 'Last Year',
		lastxdays: "Last X Days",
		lastxhours: "Last X Hours",
		lastxmonths: 'Last X Months',
		lastxweeks: 'Last X Weeks',
		lastxyears: 'Last X Years',
		nextbusinessday: 'Next Business Day',
		nextmonth: 'Next Month',
		nextweek: 'Next Week',
		nextyear: 'Next Year',
		nextxdays: 'Next X Days',
		nextxhours: 'Next X Hours',
		nextxmonths: 'Next X Months',
		nextxweeks: 'Next X Weeks',
		nextxyears: 'Next X Years',
		olderthanxdays: 'Older than X Days',
		olderthanxmonths: 'Older than X Months',
		olderthanxyears: 'Older than X Years',
		onyearx: 'On Year X',
		onx: 'On X',
		onorafterx: 'On or After X',
		onorbeforex: 'On or Before X',
		thismonth: 'This Month',
		thisweek: 'This Week',
		thisyear: 'This Year',
		today: 'Today',
		tomorrow: 'Tomorrow',
		yesterday: 'Yesterday',
		custom: 'Custom'
	};

	LightKendoGrid.OperatorWithDateTime = {
		string: jQuery.extend(true, {}, LightKendoGrid.DefaultDateTimeOperator, LightKendoGrid.ExtendedDateTimeOperator),
		datetime: jQuery.extend(true, {}, LightKendoGrid.DefaultDateTimeOperator, LightKendoGrid.ExtendedDateTimeOperator)
	};

	LightKendoGrid.OperatorWithDateTime = jQuery.extend(true, {}, LightKendoGrid.OperatorWithDateTime);

	LightKendoGrid.DefaultDateOperator = jQuery.extend(true, {}, LightKendoGrid.DefaultDateTimeOperator, LightKendoGrid.ExtendedDateTimeOperator);
	delete LightKendoGrid.DefaultDateOperator.lastxhours;
	delete LightKendoGrid.DefaultDateOperator.nextxhours;

	LightKendoGrid.OperatorWithDate = {
		string: jQuery.extend(true, {}, LightKendoGrid.DefaultDateOperator),
		date: jQuery.extend(true, {}, LightKendoGrid.DefaultDateOperator)
	};

	LightKendoGrid.OperatorWithDate = jQuery.extend(true, {}, LightKendoGrid.OperatorWithDate);

	// operator for stopfinder
	LightKendoGrid.OperatorWithDateTimeForStopfinder = {
		string: jQuery.extend(true, {}, LightKendoGrid.DefaultDateTimeOperator),
		datetime: jQuery.extend(true, {}, LightKendoGrid.DefaultDateTimeOperator)
	};

	LightKendoGrid.OperatorWithDateTimeForStopfinder = jQuery.extend(true, {}, LightKendoGrid.OperatorWithDateTimeForStopfinder);

	LightKendoGrid.DefaultDateOperatorForStopfinder = jQuery.extend(true, {}, LightKendoGrid.DefaultDateTimeOperator);

	LightKendoGrid.OperatorWithDateForStopfinder = {
		string: jQuery.extend(true, {}, LightKendoGrid.DefaultDateOperatorForStopfinder),
		date: jQuery.extend(true, {}, LightKendoGrid.DefaultDateOperatorForStopfinder)
	};

	LightKendoGrid.OperatorWithDateForStopfinder = jQuery.extend(true, {}, LightKendoGrid.OperatorWithDateForStopfinder);

	LightKendoGrid.OperatorWithList = {
		string: {
			custom: 'Custom',
			list: 'List'
		},
		number: {
			custom: 'Custom',
			list: 'List'
		},
		date: {
			custom: 'Custom',
			list: 'List'
		},
		enums: {
			custom: 'Custom',
			list: 'List'
		}
	};
	LightKendoGrid.OperatorWithList = jQuery.extend(true, {}, LightKendoGrid.BaseOperator, LightKendoGrid.OperatorWithList);

	LightKendoGrid.DefaultGeneralOperator = { //user for number/integer/time
		eq: "Equal To",
		neq: "Not Equal To",
		isempty: "Empty",
		isnotempty: "Not Empty",
		lt: "Less Than",
		lte: "Less Than or Equal To",
		gt: "Greater Than",
		gte: "Greater Than or Equal To",
		custom: 'Custom'
	};

	LightKendoGrid.OperatorWithNumber = {
		string: jQuery.extend(true, {}, LightKendoGrid.DefaultGeneralOperator),
		number: jQuery.extend(true, {}, LightKendoGrid.DefaultGeneralOperator)
	};

	LightKendoGrid.OperatorWithTime = {
		// here need set "date" not "time" for time type column
		string: jQuery.extend(true, {}, LightKendoGrid.DefaultGeneralOperator),
		date: jQuery.extend(true, {}, LightKendoGrid.DefaultGeneralOperator)
	};

	LightKendoGrid.Operator2DisplayValue = {
		eq: "Equal To",
		neq: "Not Equal To",
		contains: "Contains",
		doesnotcontain: "Does Not Contain",
		startswith: "Starts With",
		endswith: "Ends With",
		isempty: "Empty",
		isnotempty: "Not Empty",
		lt: "Less Than",
		lte: "Less Than or Equal To",
		gt: "Greater Than",
		gte: "Greater Than or Equal To",
		custom: 'Custom',
		list: 'List',
		wi: 'Is Within',
		lastxdays: "Last X Days",
		lastxhours: "Last X Hours",
		lastxmonths: 'Last X Months',
		lastxweeks: 'Last X Weeks',
		lastxyears: 'Last X Years',
		nextxdays: 'Next X Days',
		nextxhours: 'Next X Hours',
		nextxmonths: 'Next X Months',
		nextxweeks: 'Next X Weeks',
		nextxyears: 'Next X Years',
		olderthanxdays: 'Older than X Days',
		olderthanxmonths: 'Older than X Months',
		olderthanxyears: 'Older than X Years',
		onyearx: 'On Year X',
		all: 'All',
		lastmonth: 'Last Month',
		lastweek: 'Last Week',
		lastyear: 'Last Year',
		nextbusinessday: 'Next Business Day',
		nextmonth: 'Next Month',
		nextweek: 'Next Week',
		nextyear: 'Next Year',
		thismonth: 'This Month',
		thisweek: 'This Week',
		thisyear: 'This Year',
		today: 'Today',
		tomorrow: 'Tomorrow',
		yesterday: 'Yesterday',
		onx: 'On X',
		onorafterx: 'On or After X',
		onorbeforex: 'On or Before X'
	};

	LightKendoGrid.prototype.fetchData = function(requestOption)
	{
		// update the data source id when from the map viewer layer call out
		let url = this.getApiRequestURL(this.options.url);
		if (tf.helpers.kendoGridHelper.isDetailView(this.lazyLoadScrollContainer))
		{
			let dbid = tf.helpers.detailViewHelper.getDataSourceId(this.lazyLoadScrollContainer);
			if (requestOption && requestOption.paramData && requestOption.paramData.databaseId)
			{
				requestOption.paramData.databaseId = dbid;
			}

			// replace the current data source id to map viewer layer data source id
			url = url.replace(`/${tf.datasourceManager.databaseId}/`, `/${dbid}/`);
		} else
		{
			this.setLazyLoadFields(requestOption.data);
		}
		let gridRequestOption = this.resetRequestOption(requestOption);
		this.onCurrentGridRequestOption(gridRequestOption);
		return tf.promiseAjax.post(url, gridRequestOption, {
			overlay: this.overlay && this.options.showOverlay && this.options.customGridType !== "dashboardwidget",
			context: tf.helpers.kendoGridHelper.isDetailView(this.lazyLoadScrollContainer) ? this.lazyLoadScrollContainer : null
		});
	};

	LightKendoGrid.prototype.createGrid = function()
	{
		var self = this;

		// Send first request before rendering grid so we can improve performance RW-55485
		this.getApiRequestOption(null, true).then(firstRequestOption =>
		{
			if (firstRequestOption)
			{
				tf.loadingIndicator.showImmediately();
				this.firstRequestPromise = this.fetchData(firstRequestOption).finally(() =>
				{
					tf.loadingIndicator.tryHide();
				});
			}
		});

		var kendoGridOption = {
			dataSource: {
				type: "odata",
				transport: {
					read: async function(options)
					{
						function setEmpty()
						{
							options.success({
								d: {
									__count: 0,
									results: []
								}
							});
						}
						if (self.options.withoutData ||
							(self.options.isMiniGrid && self.options.hasPermission === false))
						{
							setEmpty();
							return;
						}
						//the count of request in the process of change filter
						if (!self.kendoDataSourceTransportReadCount) self.kendoDataSourceTransportReadCount = 0;
						self.kendoDataSourceTransportReadCount = self.kendoDataSourceTransportReadCount + 1;
						if (!self.hasSendRequst)
						{
							self.hasSendRequst = true;

							function initListFilterPromise()
							{
								if (self.getQuickFilter && Object.keys(self.getQuickFilter().data).length !== 0)
								{
									var quickFilterData = self.getQuickFilter().data;
									TF.ListFilterHelper.initListFilterIdsByQuickFilter(quickFilterData, self.listFilters, self._gridDefinition.Columns);
								}
								return Promise.resolve();
							}

							self.beforeSendFirstRequest.bind(self)()
								.then(function(result)
								{
									initListFilterPromise();
									return Promise.resolve(result);
								})
								.then(async function(result)
								{
									// if (typeof result !== 'boolean')
									// {
									// 	self.kendoGrid.dataSource.filter(result); // Paul: may cause call search result api twice, not found soluntion yet.
									// 	options.data.filter = {}
									// 	options.data.filter.filters = result;
									// }

									if (result == false)
									{
										self.kendoGrid.dataSource._pending = false;
										options.success({
											d: {
												__count: 0,
												results: []
											}
										});
										return;
									}

									self.setFilterIconByKendoDSFilter.bind(self)();

									// mini grid edit mode need construct the api request for initial filter
									let requestOptions = await self.getApiRequestOption(options);
									if (self.options.miniGridEditMode)
									{
										setEmpty();
										return;
									}
									self.lazyLoadGrid(requestOptions);
								});
						}
						else
						{
							var isGeoSearching = self.isGeoSearching;
							var requestOption = await self.getApiRequestOption(options);
							if (!requestOption || self.options.miniGridEditMode)
							{
								setEmpty();
								return;
							}
							if (!isGeoSearching && self.additionalFilterIds && self.shouldIncludeAdditionFilterIds)
							{
								if (requestOption.data.idFilter)
								{
									requestOption.data.idFilter.IncludeOnly = self.getIncludeOnlyIds(false);
								}
								return tf.promiseAjax.post(pathCombine(self.getApiRequestURL(self.options.url), "id"), {
									paramData: requestOption.paramData,
									data: requestOption.data
								}, { overlay: self.options.customGridType !== "dashboardwidget" })
									.then(data =>
									{
										self.onFilterWithGeoSearch.notify({
											dataIds: data.Items, callback: ids =>
											{
												self.additionalFilterIds = ids;
												if (requestOption.data.idFilter)
												{
													requestOption.data.idFilter.IncludeOnly = self.getIncludeOnlyIds();
												}
												self.lazyLoadGrid(requestOption);
											}
										});
									});
							}
							else
							{
								self.lazyLoadGrid(requestOption);
							}
						}
					}
				},
				schema: {
					model: {
						fields: self.getKendoField(),
						id: self.getIdName()
					}
				},
				pageSize: 200,
				serverPaging: true,
				serverFiltering: true,
				serverSorting: true,
			},
			allowCopy: true,
			scrollable: {
				virtual: true
			},
			height: self.getGridFullHeight(),
			sortable: {
				mode: "single",
				allowUnsort: true
			},
			reorderable: self.options.reorderable === false ? false : true,
			resizable: self.options.resizable !== false,
			pageable: {},
			columns: self.getKendoColumn(),
			columnResize: self.columnResizeEvent.bind(this),
			columnReorder: self.columnReorderEvent.bind(this),
			selectable: this.options.selectable,
			change: self.onChange.bind(this),
			columnHide: self.columnHideEvent.bind(this),
			columnShow: self.columnShowEvent.bind(this),
			dataBound: self.onDataBound.bind(this),
			noRecords: this.options.noRecords,
			dataBinding: function(e)
			{
				// mini grid in DE form, when tab keypress:
				// 1. columns can not focus
				// 2. filter button can not focus
				// 3. grid table can not focus
				// 4. filter input can not focus
				if (self.options.removeTabIndex)
				{
					self.$container.find("a").attr("tabindex", "-1");
					self.$container.find("span[tabindex]").attr("tabindex", "-1");
					self.$container.find("table[tabindex]").attr("tabindex", "-1");
					self.$container.find("input").attr("tabindex", "-1");
				}

				// add following block to cover following case:
				// 1. set filter as equal, add filter value
				// 2. set filter type as custom, the code will set filter operator type as 'eq'
				// 3. after send request, the filter type reset as 'custom' by kendogrid inner code
				// 4. call following code, force to set filter operator as 'custom' again
				if (self.kendoGrid && self.kendoGrid.dataSource)
				{
					var rootFilter = self.kendoGrid.dataSource.filter();
					if (rootFilter)
					{
						var customFilters = rootFilter.filters.filter(function(filter)
						{
							return filter.operator === 'custom';
						});
						customFilters.map(function(customFilter)
						{
							self.kendoGridFilterHelper.setEmptyCustomFilterCommon(customFilter.field, null, this.kendoGrid, this.obHeaderFilterSets);
						});
					}
				}

				if (self.result)
				{
					self.allIds = [];

					self._resetPageInfo();
					self._resetPageInfoSelect();
					self.obFilteredRecordCount(self.result.FilteredRecordCount);
					self.obTotalRecordCount(self.result.TotalRecordCount);

					if (self.isBigGrid)
					{
						//self.getIdsWithCurrentFiltering();
					}
					if (self.suspendRefresh)
					{
						e.preventDefault();
					}
				}
			},
			filterMenuInit: self.kendoGridFilterHelper.filterMenuInit.bind(self)
		};

		if (this.options.dataSource)
		{
			kendoGridOption.dataSource = {
				type: "odata",
				pageSize: this.options.pageSize,
				data: this.options.dataSource,
				schema: {
					model: {
						fields: self.getKendoField(),
						id: self.getIdName()
					}
				}
			};
		}

		if (self.options.filterable === false)
		{
			kendoGridOption.filterable = false;
		}
		else
		{
			kendoGridOption.filterable = {
				extra: true,
				mode: "menu row",
				operators: TF.Grid.LightKendoGrid.DefaultOperator
			};
		}

		kendoGridOption = $.extend(true, kendoGridOption, this.options.kendoGridOption);
		this.filterClearButtonInUnLockedArea = this.options.kendoGridOption.filterClearButtonInUnLockedArea;

		kendoGridOption.dataSource.sort = this.getKendoSortColumn();

		this.$container.data("lightKendoGrid", this);
		this.$container.kendoGrid(kendoGridOption);
		this.kendoGrid = this.$container.data("kendoGrid");

		this.disableBulkMenuColumnResize();
		TF.CustomFilterHelper.initCustomFilterBtn(this.$container);

		if (this.options.withoutData)
		{
			return;
		}

		this.bindGridFilter();
		this.sortModel = this.kendoGrid.getOptions().sortable.mode;
		this._initFilterDropDownListTimer();

		TF.CustomFilterHelper.initCustomFilterBtn(this.$container);
		TF.ListFilterHelper.initListFilterBtn(this.$container);
		this.kendoGridFilterHelper.addFilterClass(this.$container, this.kendoGrid);
		this.kendoGridFilterHelper.setColumnCurrentFilterIcon();
		this.bindFilterIconClick();
		this.createFilterClearAll();
		this.bindDoubleClickEvent();
		this.bindAltClickEvent();
		this.autoLoadDataOnScrollBottom();
		this.blurFilterInputWhenClickGrid();
		this.handleClickClearQuickFilterBtn();
		this.bindCalendarButton();
		this.lightKendoGridDetail && this.lightKendoGridDetail.onCreateGrid();
		this.options.canDragDelete && this.createDragDelete();
		this.options.onCreateGrid && this.options.onCreateGrid();
	};

	LightKendoGrid.prototype.FormatFilterClause = function(requestOption)
	{
		if (!requestOption || !requestOption.data || !requestOption.data.filterClause) return;

		switch (this._gridType)
		{
			case "studentattendanceschedule":
				this.replaceLocalTimeToUTCForFilterClause(requestOption);
				break;
			case "mapincident":
				this.replaceLocalDateTimeToUTCForFilterClause(requestOption);
				break;
			case "gpsevent":
				this.replaceTimeForFilterClause(requestOption);
				break;
			default:
				break;
		}
	}

	LightKendoGrid.prototype.disableBulkMenuColumnResize = function()
	{
		// To disable the resize from bulk_menu column. Solution from https://docs.telerik.com/kendo-ui/knowledge-base/disable-column-resize-for-specific-columns
		if (this.kendoGrid?.resizable)
		{
			this.kendoGrid.resizable.bind("start", e =>
			{
				if ($(e.currentTarget).data("th").data("kendoField") == "bulk_menu")
				{
					e.preventDefault();
					setTimeout(() =>
					{
						this.kendoGrid.wrapper.removeClass("k-grid-column-resizing");
						$(document.body)
							.add(".k-grid th")
							.add(".k-grid th .k-link")
							.css("cursor", "");
					});
				}
			});
		}
	}

	LightKendoGrid.prototype.replaceLocalTimeToUTCForFilterClause = function(requestOption)
	{
		let regex1 = /\'(.+?)\'/g;
		var matchResult = requestOption.data.filterClause.match(regex1);
		if (matchResult)
		{
			let filter = matchResult.filter(x => x.length === 10 && x.split(":").length === 3);
			let date = tf.storageManager.get(this._gridType + "Date") ? moment(tf.storageManager.get(this._gridType + "Date")).format(dataFormat) : moment(new Date()).format(dataFormat);
			filter.forEach(item =>
			{
				requestOption.data.filterClause = requestOption.data.filterClause.replace(item, `'${moment(date + " " + item.replace(/\'/g, '')).add(-tf.timezonetotalminutes, 'm').format("HH:mm:ss")}'`);
			});
		}
	}

	LightKendoGrid.prototype.replaceLocalDateTimeToUTCForFilterClause = function(requestOption)
	{
		var regexArray = [
			/\[CreatedOn\]?\s*[<>=]+\s*\'(.+?)\'/gi,
			/\[ActiveFrom\]?\s*[<>=]+\s*\'(.+?)\'/gi,
			/\[ActiveTo\]?\s*[<>=]+\s*\'(.+?)\'/gi
		];

		regexArray.forEach(regex =>
		{
			var matchResult = requestOption.data.filterClause.match(regex);
			if (matchResult)
			{
				let dateTime = matchResult.map(x => /\'(.+?)\'/gi.exec(x)[1]);
				dateTime.forEach(item =>
				{
					if (moment(item).isValid())
					{
						requestOption.data.filterClause = requestOption.data.filterClause.replace(item, `${moment(item).add(-tf.timezonetotalminutes, 'm').format("YYYY-MM-DDTHH:mm:ss")}`);
					}
				});
				return;
			}
		});
	}

	LightKendoGrid.prototype.replaceTimeForFilterClause = function(requestOption)
	{
		requestOption.data.filterClause.replace(/[TIME]/gi, '[Time]');
		let regex = /\[Time\]?\s*[<>=]+\s*\'(.+?)\'/gi;
		let matchResult = requestOption.data.filterClause.match(regex);
		if (matchResult)
		{
			matchResult.forEach(item =>
			{
				let time = /\'(.+?)\'/.exec(item)[1];
				let replaceTime = time;
				let matched = item.match(/\[Time\]?\s*\<=+\s*\'(.+?)\'/gi);
				let needReplace = item.indexOf('12/30/1899') === -1;
				if (matched)
				{
					replaceTime = matched.map(x => /\'(.+?)\'/.exec(x)[1]);
					replaceTime = needReplace ? moment(`12/30/1899 ${replaceTime}`).add(1, 'm').format("12/30/1899 hh:mm:ss A")
						: moment(replaceTime).add(1, 'm').format("12/30/1899 hh:mm:ss A");
				}
				else
				{
					replaceTime = needReplace ? `12/30/1899 ${replaceTime}` : replaceTime;
				}

				if (replaceTime != time)
				{
					requestOption.data.filterClause = requestOption.data.filterClause.replace(time, replaceTime);
				}
			})
		}
	}

	LightKendoGrid.prototype.speedUpHScroll = function()
	{
		setTimeout(function()
		{
			if (this.kendoGrid && this.kendoGrid.wrapper)
			{
				this.kendoGrid.wrapper.children(".k-grid-header").find(".k-grid-header-wrap").off("scroll");
			}
		}.bind(this));
	};

	LightKendoGrid.prototype.setFilterIconByKendoDSFilter = function()
	{
		var self = this;
		var currentFilters = self.kendoGrid.dataSource.filter() ? self.kendoGrid.dataSource.filter().filters : [];
		var columns = self.kendoGrid.columns.filter(function(column) { return column.filterable !== false; });

		this.$container.children(".k-grid-header").find(".k-filtercell .k-dropdownlist .k-input-value-text").each(function(i, item)
		{
			var $item = $(item);
			var field = columns[i].field;
			currentFilters.map(function(currentFilter)
			{
				var $filterBtn = $item.closest('.k-dropdownlist').find(self.filterClass);
				if (currentFilter.field === field && currentFilter.operator === 'list')
				{
					self.kendoGridFilterHelper.removeFilterBtnIcon($filterBtn);
					$filterBtn.addClass('list');
					self.kendoGridFilterHelper.visibleListFilterBtn2($item, field);
					self.kendoGridFilterHelper.bindListFilterBtnEvent($item, field);
				}
				else if (currentFilter.filters && currentFilter.filters.length > 0 &&
					currentFilter.filters[0].field === field)
				{
					self.kendoGridFilterHelper.removeFilterBtnIcon($filterBtn);
					$filterBtn.addClass('custom');
					self.kendoGridFilterHelper.visibleCustomFilterBtn($item, i);
				}
			});
		});
	};

	LightKendoGrid.prototype.handleClickClearQuickFilterBtn = function()
	{
		var self = this;
		this.$container.delegate('>.k-grid-header button', 'click', function(e)
		{
			var $button = $(this);
			self.clearDateNilFilterCell($button);
			if ($button.children(".k-i-close").length === 0)
				return;

			$button.parent().find('input:text').focus();
			// self._showCannotSupportSelectAllModal();

		});
	};

	LightKendoGrid.prototype.clearDateNilFilterCell = function(button)
	{
		var cellContainer = button.closest("span.k-filtercell"),
			operator = cellContainer.find("input.k-dropdown-operator").val();
		if ((button.children(".k-svg-i-filter-clear").length === 0) ||
			TF.FilterHelper.dateTimeNilFiltersOperator.indexOf(operator) === -1) return;

		cellContainer.find("input.date-number").val("");
	};

	LightKendoGrid.prototype.bindCalendarButton = function()
	{
		var self = this;

		self.$container.find(".k-i-calendar,.k-i-clock").parent().on("click", function(e)
		{
			var $input = $(e.currentTarget).prev();

			var rect = $input.closest(".k-filtercell[data-kendo-field]")[0].getBoundingClientRect();

			var $popup;
			if ($input.attr("aria-activedescendant"))
			{
				var id = $input.attr("aria-activedescendant").split("_")[0];
				var $popup = $(`#${id}`).closest(".k-animation-container");
			}
			else if ($input.data("DateTimePicker"))
			{
				var $popup = $("body>.bootstrap-datetimepicker-overlay>.bootstrap-datetimepicker-widget:last");
				$popup.css({ margin: 0, padding: 0 })
			}

			if ($popup)
			{
				var popLeft = rect.left;
				var popTop = rect.top + rect.height + 1;
				var docSelectorHeight = $(".doc-selector").height();

				if ($popup.width() + rect.left > $(window).width())
				{
					popLeft = $(window).width() - $popup.width() - 1;
				}

				if ($popup.height() + rect.top > $(window).height() - docSelectorHeight)
				{
					popTop = rect.top - $popup.height() - 1;
				}

				$popup.css({ left: popLeft, top: popTop });

			}
		});

		self.$container.find(".k-i-calendar").parent().prev().on("blur", function(e)
		{
			self.isOpen = false;
		});
	};

	LightKendoGrid.prototype.triggerRefreshClick = function()
	{
		var self = this;
		TF.Grid.LightKendoGrid.prototype.refreshClick.apply(self);
	};

	LightKendoGrid.prototype.getIdsWithCurrentFiltering = function(isCopyRequest, forceSendRequest)
	{
		if (!this.searchOption || (this.kendoGrid.dataSource.options.serverPaging == false
			|| (this.options.kendoGridOption && this.options.kendoGridOption.dataSource && this.options.kendoGridOption.dataSource.serverPaging == false)))
		{
			var datasource = this.kendoGrid.dataSource;
			if (datasource.options.serverFiltering == false && datasource.sort())
			{
				var data = (new kendo.data.Query(datasource.data()).filter(datasource.filter())).data;
				data = (new kendo.data.Query(data).sort(datasource.sort())).data;
			}
			else
			{
				data = datasource.data();
			}
			var allIds = data.map(function(item) { return item.Id; })
			this.obAllIds(allIds);
			return Promise.resolve(allIds);
		}

		if ((this.options.isMiniGrid && this.options.gridType === "DocumentGrid") || this.options.gridType === "auditlog")
		{
			var allIds = this.kendoGrid.dataSource.data().map(item => item.Id);
			this.obAllIds(allIds);
			return Promise.resolve(allIds);
		}

		if (!forceSendRequest)
		{
			var filteredIds;
			if (this.allIds && this.allIds.length > 0)
			{
				filteredIds = this.allIds;
			} else if (this.result && this.result.FilteredRecordIds)
			{
				filteredIds = this.result.FilteredRecordIds;
			}

			if (filteredIds)
			{
				this.obAllIds(filteredIds);
				this.allIds = filteredIds;
				return Promise.resolve(filteredIds);
			}
		}

		this.searchOption.data.filterSet = this.searchOption.data.filterSet ||
		{
			FilterItems: [],
			FilterSets: [],
			LogicalOperator: "and"
		};


		return tf.promiseAjax.post(pathCombine(this.getApiRequestURL(this.options.url), "id"),
			{
				paramData: this.searchOption.paramData,
				data: this.searchOption.data
			},
			{ isCopyRequest: isCopyRequest ? true : false, overlay: this.options.showLoadingForIds ? true : false }
		)
			.then(function(apiResponse)
			{
				this.allIds = apiResponse.Items;
				if (this.options && this.options.onAllIdBounded)
				{
					this.options.onAllIdBounded();
				}
				this.obAllIds(this.allIds);
				return this.allIds.slice(0);
			}.bind(this))
			.catch(function()
			{

			});
	};

	LightKendoGrid.prototype.blurFilterInputWhenClickGrid = function()
	{
		if (TF.isMobileDevice)
		{
			// this is a bug when use virtual scroll on mobile device , when click filter and then click the grid, the filter will not blur
			// this code relate to kendo.all.js at line 26596 and line 26748
			this.$container.delegate(".k-grid-content,.k-grid-content-locked", "touchstart", function(e)
			{
				var $filterInputs = $(".k-filter-row .k-filtercell input");
				$filterInputs.blur();
			}.bind(this));
		}
	};

	LightKendoGrid.prototype.autoLoadDataOnScrollBottom = function()
	{
		var self = this,
			pagerWrap = this.$container.children(".k-pager"),
			$pageInfo = pagerWrap.children(".pageInfo"),
			$pageInfoSelect = pagerWrap.children(".pageInfoSelect");
		pagerWrap.children().hide();
		if ($pageInfo.length === 0)
		{
			$pageInfo = $("<span class='pageInfo' style='float:left'></span>");
			pagerWrap.append($pageInfo);
		}
		$pageInfo.show();
		if ($pageInfoSelect.length === 0)
		{
			$pageInfoSelect = $("<span class='pageInfoSelect' style='float:left'></span>");
			pagerWrap.append($pageInfoSelect);
		}
		$pageInfoSelect.show();
		var $scrollbar = this.$container.find(".k-scrollbar-vertical");

		$scrollbar.unbind("scroll.scrollToBottom").bind("scroll.scrollToBottom", function(e)
		{
			var height = $scrollbar.height(),
				scrollHeight = $scrollbar[0].scrollHeight,
				scrollTop = $scrollbar.scrollTop();
			if (scrollTop >= (scrollHeight - height))
			{
				self.obIsScrollAtBottom(true);
			}
		});
	};

	LightKendoGrid.prototype.createFilterClearAll = function()
	{
		var self = this, tr;
		if (self.filterClearButtonInUnLockedArea)
		{
			tr = self.$container.children("div.k-grid-header").find("tr.k-filter-row");
		}
		else
		{
			tr = self.$container.children(".k-grid-header").find("div.k-grid-header-locked").find("tr.k-filter-row");
		}

		// No point to continue if no element is found.
		if (tr && tr.length > 0)
		{
			var td = tr.children("td:first"),
				div = $('<div class="grid-filter-clear-all"></div>');

			td.text("");
			td.append(div);

			div.mousedown(function(e)
			{
				e.stopPropagation();
				var buttons = tr.find("button.k-button:visible");
				if (buttons !== undefined)
				{
					buttons.trigger("click");
				}

				function forceClearListFilter()
				{
					self.listFilters = {};
					self.onClearFilter.notify();
					self.obHeaderFilters([]);
					self.getSelectedIds([]);
					self.obTempOmitExcludeAnyIds([]);
					self.clearKendoGridQuickFilter(true);
					self.rebuildGrid();
				}

				forceClearListFilter();
			});
		}
	};

	LightKendoGrid.prototype.clearKendoGridQuickFilter = function(lazyRebuildGrid)
	{
		var self = this;
		self.listFilters = TF.ListFilterHelper.initListFilters();
		TF.CustomFilterHelper.clearCustomFilter();
		self.kendoGrid.dataSource.filter({}, lazyRebuildGrid);
		return Promise.resolve(true);
	};

	LightKendoGrid.prototype._isFilterDropDownListPopup = function($dropDownList)
	{
		return $dropDownList.parent().attr("aria-expanded") === "true";
	};

	LightKendoGrid.prototype._hideFilterDropDownList = function($dropDownList, e)
	{
		var kendoDropDownList = $dropDownList.data("kendoDropDownList");
		if (this._isFilterDropDownListPopup($dropDownList))
			kendoDropDownList._wrapperClick(e);
	};

	LightKendoGrid.prototype._clearFilterDropDownListTimer = function()
	{
		var self = this;
		$("input.k-dropdown-operator").each(function(i, item)
		{
			var $dropDownList = $(item);
			if (self._isFilterDropDownListPopup($dropDownList))
				clearTimeout(self.filterDropDownListTimer);
		});
	};

	LightKendoGrid.prototype._setFilterDropDownListTimer = function(e)
	{
		var self = this;
		$("input.k-dropdown-operator").each(function(i, item)
		{
			var $dropDownList = $(item);
			if (self._isFilterDropDownListPopup($dropDownList))
				self.filterDropDownListTimer = setTimeout(function() { self._hideFilterDropDownList($dropDownList, e); }, 500);
		});
	};

	LightKendoGrid.prototype._initFilterDropDownListTimer = function()
	{
		var self = this;

		var $filterBtn = $("span.k-dropdown.k-header.k-dropdown-operator");
		$filterBtn.on("mouseover", function(e) { self._clearFilterDropDownListTimer(); });
		$filterBtn.on("mouseleave", function(e) { self._clearFilterDropDownListTimer(); self._setFilterDropDownListTimer(e); });

		var $filterListContainer = $(".k-list-container");
		$filterListContainer.on("mouseover", function(e) { self._clearFilterDropDownListTimer(); });
		$filterListContainer.on("mouseleave", function(e) { self._clearFilterDropDownListTimer(); self._setFilterDropDownListTimer(e); });
	};

	LightKendoGrid.prototype.handleDateTimeNilFilterToKendoDataSource = function(value, fieldName, operator)
	{
		var self = this;

		var kendoFilters = self.kendoGrid.dataSource.filter();
		var filters = [],
			filter = { field: fieldName, operator: operator, value: value };

		if (kendoFilters)
		{
			for (var i = 0; i < kendoFilters.filters.length; i++)
			{
				if (kendoFilters.filters[i].field !== fieldName)
				{
					if (kendoFilters.filters[i].filters)
					{
						if (kendoFilters.filters[i].filters.length > 0 &&
							kendoFilters.filters[i].filters[0].field !== fieldName)
							filters.push(kendoFilters.filters[i]);
					}
					else
					{
						filters.push(kendoFilters.filters[i]);
					}
				}
			}
		}

		filters.push(filter);

		//filters.push(nilFilter);
		self.kendoGrid.dataSource.filter({ logic: 'and', filters: filters });
	};

	LightKendoGrid.prototype.afterhandleListFilterResult = function(selectedFilterItems, fieldName, originalSelectedFilterItemsCnt, currentlySelectedFilterItemsCnt, selectedIds)
	{
		var self = this;

		self._setListFilterFilterCellInput(selectedFilterItems, fieldName);

		self.kendoGridFilterHelper.addListFilterToKendoDataSource(selectedFilterItems, fieldName, selectedIds, this.kendoGrid);

		if (currentlySelectedFilterItemsCnt < originalSelectedFilterItemsCnt)
			self._showCannotSupportSelectAllModal();

		// now in form result grid, do not send the request twice: the follwoing code is 2016, we cannot find the reason why need call twice
		// test in form result, the list filter can work after adding the condition
		if (!self.options.isMiniGrid && self.options.gridType !== 'form') // RW-44827 Reading datasource here can cause mini Grid broken.
		{
			self.kendoGrid.dataSource.read(); // Paul: will call search twice, not found soluntion yet
		}
	};

	LightKendoGrid.prototype._setListFilterFilterCellInput = function(selectedFilterItems, fieldName)
	{
		var self = this;
		var displayInputVal = selectedFilterItems.length ? selectedFilterItems.join(',') : '';

		var wrappers = $('.doc.wrapper');
		var $filterCellsAll;
		if (wrappers)
		{
			var visableWrappers = wrappers.filter(function() { return $(this).css('visibility') != 'hidden' && $(this).css('display') != 'none' });
			if (self.options.isMiniGrid || self.options.customGridType === "dashboardwidget")
			{
				$filterCellsAll = self.$container.find('.k-filtercell');
			}
			else
			{
				$filterCellsAll = visableWrappers.find('.k-filtercell');
			}
		}
		else
		{
			$filterCellsAll = $('.k-filtercell');
		}

		var $filterCells = $filterCellsAll.filter(function(idx, filterCell)
		{
			return $(filterCell).data('kendo-field') === fieldName;
		});
		$filterCells.map(function(idx, filterCell)
		{
			self.kendoGridFilterHelper.setKendoFilterCellInputValue($(filterCell), displayInputVal, 'listFilter');
		});
	};

	LightKendoGrid.prototype._refillAllListFilterFilterCellInput = function()
	{
		var self = this;
		var listFilterFieldNames = Object.keys(self.listFilters);
		listFilterFieldNames.forEach(fieldName =>
		{
			var selectedFilterItems = self.listFilters[fieldName].selectedFilterItems;
			self._setListFilterFilterCellInput(selectedFilterItems, fieldName);
		});
	};

	LightKendoGrid.prototype.setListFilterRequestOption = function(options)
	{
		var self = this;

		options = self.removeUnusedListFilterRequestOption(options);

		if (!self.listFilters || self.listFilters.length === 0)
			return options;

		var fieldNames = Object.keys(self.listFilters);
		if (fieldNames.length === 0)
			return options;

		options.data.filterSet = options.data.filterSet || { FilterSets: [], LogicalOperator: "and" };
		options.data.filterSet.FilterItems = options.data.filterSet.FilterItems || [];

		fieldNames.forEach(fieldName =>
		{
			if (self.listFilters[fieldName] &&
				self.listFilters[fieldName].selectedFilterItems &&
				self.listFilters[fieldName].selectedFilterItems.length &&
				self.listFilters[fieldName].selectedFilterItems.length > 0)
			{
				self.deleteListFilterItemsByFieldName(options, fieldName);

				if (self.listFilters[fieldName].selectedFilterItems.length > 0)
				{
					// add the empty value, include the "" and null
					let selectedFilterItems = [...self.listFilters[fieldName].selectedFilterItems];
					let hasEmptyValue = selectedFilterItems.filter(item => !item);
					if (hasEmptyValue.length > 0)
					{
						!hasEmptyValue.includes("") && selectedFilterItems.push("");
						!hasEmptyValue.includes(null) && selectedFilterItems.push(null);
					}

					var dsListFilterItem = TF.ListFilterHelper.buildDsListFilterItem(fieldName,
						selectedFilterItems.join(','),
						JSON.stringify(selectedFilterItems),
						self.listFilters[fieldName].ids
					);
					options.data.filterSet.FilterItems.push(dsListFilterItem);
				}
			}
		});

		return options;
	};

	// moveToFilterHelper
	LightKendoGrid.prototype.deleteListFilterItemsByFieldName = function(options, fieldName)
	{
		var deletedItems = options.data.filterSet.FilterItems.filter(function(item)
		{
			return (item.Operator === "In" || item.IsListFilter) && item.FieldName === fieldName;
		});

		if (deletedItems[0])
		{
			var idx = options.data.filterSet.FilterItems.indexOf(deletedItems[0]);
			options.data.filterSet.FilterItems.splice(idx, 1);
		}

		return options;
	};

	LightKendoGrid.prototype.clearQuickFilterAndRefresh = function()
	{
		this.obTempOmitExcludeAnyIds([]);

		this.overlayShow = true;
		this.getSelectedIds([]);
		this.allIds = [];
		if (this.options.showOverlay && this.options.customGridType !== "dashboardwidget")
		{
			tf.loadingIndicator.showImmediately();
			//console.log("LightKendoGrid +1");
		}

		if (this.kendoGrid && this.kendoGrid.dataSource)
		{
			this.kendoGrid.dataSource.filter({});

		}
		if (this.options.showOverlay)
		{
			setTimeout(function()
			{
				this.overlayShow = false;
				tf.loadingIndicator.tryHide();
				//console.log("LightKendoGrid -1");
			}.bind(this), 1500);
		}

		//this.refreshClick();
	};

	LightKendoGrid.prototype.bindFilterIconClick = function()
	{
		var self = this;
		// ":not(.hidden)" to avoid adding event listener when custom filter is not enabled.
		this.$container.children(".k-grid-header").delegate("a.k-grid-filter:not(.hidden)", "mousedown", function(e)
		{
			var index = $(e.currentTarget).closest("tr").find("th").index($(e.currentTarget).closest("th"));
			var title = $(e.currentTarget).closest("thead").find("tr").eq(1).find("th").eq(index).find("input").attr("title")
			var inputType = $(e.currentTarget).closest("thead").find("tr").eq(1).find("th").eq(index).find("input").attr("data-kendo-role")
			if (inputType == "datepicker")
			{
				if (title)
				{
					var matches = [...title.matchAll(/Is Within\s+(-?\d*)/g)];
					self.filterConditionMathces = matches;
				}
				else
					self.filterConditionMathces = null;
			}
		})
	};

	LightKendoGrid.prototype.bindGridFilter = function()
	{
		var self = this;
		self.kendoGrid.dataSource.originalFilter = self.kendoGrid.dataSource.filter;
		self.kendoGrid.dataSource.filter = function(val, lazyRebuildGrid)
		{
			if (val === undefined)
			{
				return this._filter;
			}

			if (lazyRebuildGrid)
			{
				this._filter = val;
				return this._filter;
			}

			return self.kendoGrid.dataSource.originalFilter.call(this, val);
		};
	}

	LightKendoGrid.prototype.disableFilterCellWhenEmptyOrNotEmptyApplied = function()    //User should not type in values when "empty" or "not empty" filters are applied
	{
		var self = this;

		self.$container.children(".k-grid-header").find(".k-filtercell .k-dropdownlist .k-input-inner").each(function(i, item)
		{
			let $item = $(item),
				text = $item.text();

			// sub grid's filter input won't update, so use the dropdownlist tool to get the text.
			if ($item.closest(".detail-grid").length > 0)
			{
				text = $item.parent().find("input")?.data("kendoDropDownList")?.text() || text;
			}
			for (var key in self.filterNames)
			{
				if (text === key)
				{
					let keyType = key;
					if (TF.FilterHelper.dateTimeNonParamFiltersName.indexOf(key) > -1)
					{
						keyType = "Date Non Param";
					}
					switch (keyType)
					{
						case "Date Non Param":
						case "Empty":
						case "Not Empty":
							var input = $item.parent().parent().parent().find('input');
							TF.FilterHelper.disableFilterCellInput(input);
							input.parent().parent().parent().addClass("hide-cross-button");
							input.val("");
							input.data("isempty", true);
							break;
						default:
							break;
					}
				}
			}
		});
	};

	LightKendoGrid.prototype.setColumnCurrentFilterInput = function()
	{
		var self = this;
		this.disableFilterCellWhenEmptyOrNotEmptyApplied();
		if (!self.kendoGrid)
		{
			return;
		}
		this.$container.children(".k-grid-header").find(".k-filtercell .k-dropdownlist .k-input-inner").each(function(i, item)
		{
			var $item = $(item),
				text = $item.text();

			var $filterItem = $item.parent().parent().parent();
			var fieldName = $filterItem.data('kendo-field');
			if (!self.kendoGrid.dataSource.filter())
			{
				return;
			}
			self.kendoGrid.dataSource.filter().filters.map(function(filter)
			{
				if (fieldName === filter.FieldName || fieldName === filter.field)
				{
					// FieldName is for filter sticky; field is for change the column index
					self.kendoGrid.columns.map(function(column)
					{
						if (column.FieldName === fieldName)
						{
							switch (column.type)
							{
								case "boolean":
									var kendoDropDwonList = $filterItem.find('input').data("kendoDropDownList");
									kendoDropDwonList.dataItems().map(function(dataItem, idx)
									{
										if (String(dataItem.valueField) === filter.Value)
										{
											kendoDropDwonList.select(idx);
											//kendoDropDwonList.trigger("change");
										}
									});
									break;
								case "date":
								case "datetime":
									if (TF.FilterHelper.dateTimeNilFiltersOperator.indexOf(filter.operator) > -1)
									{
										let dateCellClass = column.type === 'date' ? '.k-datepicker' : '.input-group.tf-filter';
										let operatorName = self.kendoGridFilterHelper.getOpetatorName(filter.operator);
										let formatStr = TF.FilterHelper.getNilFiltersFormat(operatorName);
										$filterItem.find("span.date-number").show(); // show the input
										$($filterItem.find("input.date-number")[1]).data("kendoNumericTextBox").setOptions({
											format: formatStr.replace("X", "0")
										});
										$($filterItem.find("input.date-number")[1]).data("kendoNumericTextBox").value(filter.value);
										$filterItem.find(dateCellClass).hide();

										// hide the clear button
										setTimeout(function()
										{
											if (filter.value === '' || filter.value === null)
											{
												self.hideClearBtn($filterItem);
											}
										}, 200);
									}
									break;
								default:
									break;
							}
						}
					});
				}
			});
		});
	};

	LightKendoGrid.prototype.hideClearBtn = function($filterItem)
	{
		var clearBtn = $filterItem.find(".k-svg-i-filter-clear").parent();
		if (clearBtn)
		{
			clearBtn.hide();
		}
	};

	LightKendoGrid.prototype.getKendoSortColumn = function()
	{
		return [];
	};

	LightKendoGrid.prototype.getKendoColumn = function()
	{
		var currentColumns, columnsdefalultColumnWidth = '150px';
		const selectedColumns = this._obSelectedColumns();

		if (selectedColumns && selectedColumns.length > 0)
		{
			currentColumns = selectedColumns;
		}
		else
		{
			currentColumns = this._gridDefinition.Columns;
		}

		currentColumns = this.handleUDFColumns(currentColumns);

		var columns = this.getKendoColumnsExtend(currentColumns, columnsdefalultColumnWidth);

		if (this.obShowEyeColumn())
		{
			var menuColumns = [
				{
					field: "map_checkbox",
					title: "<div></div>",
					width: '30px',
					sortable: false,
					filterable: false,
					locked: true,
					type: "boolean",
					hidden: !this.options.eyeColumnVisible,
					headerTemplate: "<div class='iconbutton eye-show'></div>",
					template: "<input class='eyecolumn' type='checkbox' #: tf.georegionGridDefinition.gridDefinition().booleanToCheckboxFormatter(true)# data-Id=#: typeof(Id)=='undefined'?0:Id# />"
				}];
			columns = menuColumns.concat(columns);
		}

		if (this.options.showLockedColumn)
		{
			const showArrowToToggleSubGrid = this.lightKendoGridDetail && this.lightKendoGridDetail.detail && this.lightKendoGridDetail.detail.getAvailabilityOnGrid();
			menuColumns = [
				{
					field: "bulk_menu",
					title: "<div></div>",
					width: '30px',
					sortable: false,
					filterable: false,
					locked: true,
					template: showArrowToToggleSubGrid ? "<div class='k-hierarchy-cell'><a class='k-icon k-i-expand' style='display:none' aria-label='Expand'></a></div>" : ""
				}
			];
			if (this.options.lockColumnTemplate)
			{
				menuColumns = this.options.lockColumnTemplate;
			}
			columns = menuColumns.concat(columns);
		}
		if (this.options.expandColumns)
		{
			columns = columns.concat(this.options.expandColumns);
		}
		let isDetailGrid = this.options.isDetailGrid;
		if (tf.isViewfinder)
		{
			isDetailGrid = !this.options.hasDetail && this.options.isDetailGrid; // isDetailGrid is always true, so using hasDetail to judge it is detail grid or not
		}
		let noDateSpecialFilterGridTypes = ['gpsevent', 'eta'];
		var isNoDateSpecialFilter = noDateSpecialFilterGridTypes.includes(this._gridType) || isDetailGrid;
		var supportListFilterColumns = this._gridDefinition.Columns.filter(function(column) { return column.ListFilterTemplate; });
		var supportDateTimeFilterColumns = isNoDateSpecialFilter ? [] : this._gridDefinition.Columns.filter(function(column) { return column.type === 'datetime' && !column.hideExtendedFilterOperator; });
		var supportDateFilterColumns = isNoDateSpecialFilter ? [] : this._gridDefinition.Columns.filter(function(column) { return column.type === 'date'; });
		var supportNumberFilterColumns = this._gridDefinition.Columns.filter(function(column) { return column.type === 'number' || column.type === 'integer'; });
		var supportTimeFilterColumns = this._gridDefinition.Columns.filter(function(column) { return column.type === 'time' });
		columns.forEach(column =>
		{
			if (supportDateTimeFilterColumns.some(function(sc)
			{
				return sc.field === column.field || (sc.UDFId != null && sc.UDFId === column.UDFId);
			}))
			{
				column.filterable.operators = tf.stopfinderUtil.isStopfinderType(this.options.gridType) ?
					TF.Grid.LightKendoGrid.OperatorWithDateTimeForStopfinder : TF.Grid.LightKendoGrid.OperatorWithDateTime;
			}
			if (supportDateFilterColumns.some(function(sc)
			{
				return sc.field === column.field || (sc.UDFId != null && sc.UDFId === column.UDFId);
			}))
			{
				column.filterable.operators = tf.stopfinderUtil.isStopfinderType(this.options.gridType) ?
					TF.Grid.LightKendoGrid.OperatorWithDateForStopfinder : TF.Grid.LightKendoGrid.OperatorWithDate;
			}

			if (supportListFilterColumns.some(function(sc)
			{
				return sc.field === column.field || (sc.UDFId != null && sc.UDFId === column.UDFId);
			}))
			{
				column.filterable.operators = TF.Grid.LightKendoGrid.OperatorWithList;
			}

			if (supportNumberFilterColumns.some(function(sc)
			{
				return sc.field === column.field || (sc.UDFId != null && sc.UDFId === column.UDFId);
			}))
			{
				column.filterable.operators = TF.Grid.LightKendoGrid.OperatorWithNumber;
			}

			if (supportTimeFilterColumns.some(function(sc)
			{
				return sc.field === column.field || (sc.UDFId != null && sc.UDFId === column.UDFId);
			}))
			{
				column.filterable.operators = TF.Grid.LightKendoGrid.OperatorWithTime;
			}

		});

		this.currentDisplayColumns(columns);
		columns = tf.measurementUnitConverter.handleUnitOfMeasure(columns);
		this.handleUnitMeasurementFormatting(columns);
		return columns;
	};

	LightKendoGrid.prototype.handleUnitMeasurementFormatting = function(columns)
	{
		const isNumberType = function(col)
		{
			return ['number', 'currency', 'float'].includes(col.type.toLowerCase());
		}

		columns.forEach(col =>
		{
			if (col.UnitOfMeasureSupported && isNumberType(col) && col.template)
			{
				const oldTemplate = col.template;
				const precision = isNumber(col.Precision) ? col.Precision : 2;
				let newTemplate = (val) => tf.dataFormatHelper.numberFormatter(oldTemplate(val), precision);
				col.template = newTemplate;
			}
		});
	}

	LightKendoGrid.prototype.handleInvisibleUDFColumns = function(columns)
	{
		var self = this,
			needHandleTypes = tf.dataTypeHelper.getAvailableDataTypes()
				.map(function(item) { return item.key; });

		if (columns
			&& columns instanceof Array
			&& needHandleTypes.some(function(type) { return type === self._gridType; }))
		{
			var invisibleUDFs = tf.UDFDefinition.getInvisibleUDFs(self._gridType);

			return _.intersectionBy(columns, invisibleUDFs, function(item) { return item.UDFId; }).map(function(i)
			{
				var result = $.extend(true, {}, i);

				result.FieldName = i.FieldName;
				result.OriginalName = i.OriginalName;
				result.DisplayName = i.DisplayName;
				return result;
			});
		}

		return [];
	};

	LightKendoGrid.prototype.handleUDFColumns = function(columns)
	{
		var self = this,
			needHandleTypes = tf.dataTypeHelper.getAvailableDataTypes().map(function(item)
			{
				return item.key;
			});

		if (columns
			&& columns instanceof Array
			&& needHandleTypes.some(function(type) { return type === self._gridType; }))
		{
			var udfs = tf.UDFDefinition.getAvailableWithCurrentDataSource(self._gridType);
			return columns.map(function(c)
			{
				if (!c.UDFId) return c;

				var matched = udfs.filter(function(u) { return u.UDFId == c.UDFId; });

				if (matched.length == 0)
				{
					return "";
				}
				else
				{
					var result = $.extend(true, {}, c);
					result.FieldName = matched[0].FieldName;
					result.OriginalName = matched[0].OriginalName;
					result.DisplayName = matched[0].DisplayName;
					return result;
				}
			}).filter(function(item)
			{
				return !!item;
			});
		}

		return columns;
	};

	LightKendoGrid.prototype.setColumnFilterableCell = function(column, definition, source)
	{
		const self = this;
		if (column && column.filterable !== false)
		{
			switch (definition.type)
			{
				case "string":
				case "select":
				case "hyperlink":
				case "in boundary set":
					column.filterable = {
						cell: {
							operator: "contains",
							template: function(args)
							{
								var filterOption = {
									dataTextField: column.field,
									filter: "contains",
									delay: 500,
									minLength: 1,
									valuePrimitive: true,
									dataSource: {
										type: "odata",
										pageSize: 200,
										serverPaging: true,
										serverFiltering: true,
										serverSorting: true,
										transport: {
											read: async function(kendoOption)
											{
												var closebutton = args.element.parent().nextAll(".k-button.k-icon-button"),
													loadingElement = args.element.nextAll(".k-i-loading");
												if (closebutton.css("display") != "none")
												{
													loadingElement.addClass("right-button");
												}
												else
												{
													loadingElement.removeClass("right-button");
												}
												!!this.options.disableAutoComplete && loadingElement.hide();

												kendoOption.data.sort = [];
												kendoOption.data.isFromAutoComplete = !this.options.disableAutoComplete;
												kendoOption.data.disableTriggerDashboardLiveFilter = true;
												if (self.isListMoverWithSearch && column.field && column.field.length > 1 && /^[0-9a-f]{32}$/i.test(column.field.substr(1)))
												{
													kendoOption.data.autoCompleteSelectedColumn = tf.UDFDefinition.getOriginalName(column.field);
												}
												else
												{
													kendoOption.data.autoCompleteSelectedColumn = column.field;
												}
												var kendoSuccess = kendoOption.success;
												if (Enumerable.From(bigGridTypes).Contains(this._gridType))
												{
													var options = await this.getApiRequestOption(kendoOption);
													options.paramData = { FieldName: tf.UDFDefinition.getOriginalName(column.field), AggregateOperator: 'Distinct100' };

													if (this._gridType == 'mapincident')
													{
														options.paramData['databaseId'] = tf.datasourceManager.databaseId;
													}

													options.success = function(result)
													{
														result.Items = LightKendoGrid.normalizeResultItem(result.Items, this._gridType, this.options);
														result.Items = Enumerable.From(result.Items).Select(function(item)
														{
															var obj = {};
															obj[column.field] = column.formatType?.toLowerCase() === "phone" ||
																(column.UDFType && column.UDFType === "phone number") ?
																$.trim(tf.dataFormatHelper.phoneFormatter(item)) :
																$.trim(item);
															return obj;
														}).Distinct("$." + column.field).OrderBy("$." + column.field).Take(10).ToArray();

														kendoSuccess({
															d: {
																__count: result.FilteredRecordCount,
																results: result.Items
															}
														});
													};

													const hasDBID = tf.dataTypeHelper.getDataTypeConfig(this._gridType).hasDBID !== false;
													var url = pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(this._gridType));
													if (this.options.aggregateSearchDataSource && hasDBID)
													{
														url = pathCombine(tf.api.apiPrefixWithoutDatabase(), this.options.aggregateSearchDataSource, "search", tf.dataTypeHelper.getEndpoint(this._gridType));
													}
													else if (this.options.setRequestURL)
													{
														url = this.options.setRequestURL(url);
													}
													tf.dataFormatHelper.clearPhoneNumberFormat(options, self);
													!this.options.disableAutoComplete && this.postRequestData(pathCombine(url, "aggregate"), options);
												}
												else if (this._gridType === "busfinderhistorical")
												{
													var url = pathCombine(tf.api.apiPrefix(), "search/gpsevents/getFilterValue");

													var options = await this.getApiRequestOption(kendoOption);
													options.data.fields = [column.field];

													this.postRequestData(url, options);
												}
												else if (this._gridType === "Form")
												{
													const url = this.getApiRequestURL(this.options.url);
													const udGridID = this.options.udGridID;
													let options = await this.getApiRequestOption(kendoOption);
													const defaultFilter = tf.udgHelper.getUDGridIdFilter(udGridID);
													if (options.data.filterSet)
													{
														options.data.filterSet["FilterItems"].push(...defaultFilter);
													} else
													{
														const filterSet = {};
														filterSet["FilterItems"] = [];
														filterSet["FilterItems"].push(...defaultFilter);
														filterSet["FilterSets"] = [];
														filterSet["LogicalOperator"] = "and";
														options.data.filterSet = filterSet;
													}
													options.data.filterSet.UDGridID = udGridID;
													this.postRequestData(url, options);
												}
												else if (["GPSEventType", "StaffTypes", "Tags", "StudentTags", "LocationEventOnTime"].includes(this._gridType))
												{
													//front-end simulated aggregation
													const filter = kendoOption && kendoOption.data && kendoOption.data.filter && kendoOption.data.filter.filters && kendoOption.data.filter.filters[0];
													const filterValue = filter && filter.value || "";
													const viewModel = ko.dataFor(args.element.closest(".list-mover-mover")[0]);
													const availableRecords = viewModel.allRecords.filter(x => !viewModel.selectedData.some(y => y.ID === x.ID));

													if (filterValue)
													{
														const filteredRecords = availableRecords.filter(x => (x[filter.field] || "").toLowerCase().includes(filterValue));
														kendoSuccess({
															d: {
																__count: filteredRecords.length,
																results: filteredRecords
															}
														});
													}
													else
													{
														kendoSuccess({
															d: {
																__count: availableRecords.length,
																results: availableRecords
															}
														});
													}
												}
												else
												{
													this.postRequestData(this.getApiRequestURL(this.options.url), await this.getApiRequestOption(kendoOption));
												}
											}.bind(this)
										}
									}
								};
								if (this.options.dataSource)
								{
									filterOption.dataSource = args.dataSource;
								}
								args.element.kendoAutoComplete(filterOption);
							}.bind(this)
						}
					};
					break;
				case "number":
					if (column.UDFType === 'number')
					{
						column.format = TF.DetailView.UserDefinedFieldHelper.getNumberFormat(column);
					}
					else
					{
						column.format = column.format || (!!column.UDFId ? (_.isNumber(column.Precision) && Number(column.Precision) >= 0 ? String.format("{0:n{0}}", column.Precision) : "{0:0}") : "{0:n2}");
					}
					column.filterable = {
						cell: {
							operator: "eq",
							template: function(args)
							{
								args.element.kendoNumericTextBox({
									decimals: isNumber(definition.Precision) ? definition.Precision : 2,
									format: definition.format || "{0:n2}"
								});
							}
						}
					};
					break;
				case "integer":
					column.format = definition.format || "{0:n0}";
					column.filterable = {
						cell: {
							operator: "eq",
							template: function(args)
							{
								args.element.kendoNumericTextBox({ format: "n0" });
								$(args.element[0]).on("keypress", function(event, e)
								{
									if (event.which == 46)
									{
										args.element[0].value = args.element[0].value.replace('.', '');
										event.preventDefault();
									}
								});
							}
						}
					};
					break;
				case "time":
					column.format = definition.format || "{0:h:mm tt}";
					column.filterable = {
						cell: {
							template: function(args)
							{
								var span = $(args.element[0].parentElement);
								span.empty();
								var $dateTimeBtn = $("<span class='input-group tf-filter' data-kendo-bind='value: value' data-kendo-role='customizedtimepicker'></span>");

								if (source === "listmover")
									$dateTimeBtn.attr('Width', '150px');

								span.append($dateTimeBtn);
							}
						},
						ui: function(element)
						{
							var $timePickerBtn = $('<span style="width: 100%" class="input-group tf-filter" data-kendo-role="customizedtimepicker"></span>');

							var kendoDataBindStr = element.data('kendo-bind');
							$timePickerBtn.attr('data-kendo-bind', kendoDataBindStr);

							element.replaceWith($timePickerBtn);
							element.kendoCustomizedTimePicker({});
						}
					};
					break;
				case "date":
					column.format = "{0:MM/dd/yyyy}";
					column.filterable = {
						cell: {
							operator: "eq",
							template: function(args)
							{
								args.element.kendoDatePicker();
								args.element.on("keypress", function(e)
								{
									if ((e.which < 45 || e.which > 57) && TF.Input.BaseBox.notSpecialKey(e))
									{
										e.preventDefault();
									}
								});
								var datePicker = args.element.data("kendoDatePicker");
								var dateHelper = new TF.DateBoxHelper(datePicker);
								args.element.on("focus", (e) =>
								{
									// for recover the raw date value string
									let datePicker = args.element.data("kendoDatePicker");
									let span = $(args.element[0].parentElement).parent().parent();
									let operator = span.find("input.k-dropdown-operator").val();
									if (TF.FilterHelper.dateTimeDateParamFiltersOperator.includes(operator))
									{
										let oldText = datePicker._oldText;
										if (oldText)
										{
											$(args.element).val(oldText);
										}
									}
								});
								args.element.on("blur", (e) =>
								{
									// for setting the "ON","On or After", "On or Before" label for date filter cell
									let cellValue = $(args.element).val();
									let span = $(args.element[0].parentElement).parent().parent();
									let operator = span.find("input.k-dropdown-operator").val();
									if (TF.FilterHelper.dateTimeDateParamFiltersOperator.includes(operator))
									{
										if (cellValue && cellValue !== '')
										{
											let operatorName = span.find('input.k-dropdown-operator').attr("aria-label");
											if (operatorName)
											{
												operatorName = operatorName.replace("X", "");
											}
											setTimeout(() =>
											{
												if (moment(cellValue).isValid())
												{
													$(args.element).val(operatorName + cellValue);
												}
												else
												{
													$(args.element).val('');
												}
											});
										}
									} else if (TF.FilterHelper.dateTimeParamFiltersOperator.includes(operator))
									{	// clear the invalid the input date/time value
										let filterCellValue = $(args.element).val();
										if (!kendo.parseDate(filterCellValue))
										{
											$(args.element).val('');
										}
									}
								});
								dateHelper.trigger = function()
								{
									this.trigger("change");
								}.bind(datePicker);

								// insert number for integer paramber
								var span = $(args.element[0].parentElement).parent().parent()
								self.createDateIntegerFilterCell(span, 'date');
							}
						}
					};
					break;
				case "datetime":
					column.format = "{0:MM/dd/yyyy hh:mm tt}";
					column.filterable = {
						cell: {
							operator: "eq",
							template: function(args)
							{
								var span = $(args.element[0].parentElement);
								span.empty();
								span.append($("<span class='input-group tf-filter' data-kendo-bind='value: value' data-kendo-role='customizeddatetimepicker'></span>"));
								setTimeout(() =>
								{	// clear the invalid input date/time value
									let dateInput = span.find(".datepickerinput");
									dateInput.attr('title', column.title);
									dateInput.on("blur", (e) =>
									{
										let filterOperator = span.find("input.k-dropdown-operator").val();
										let isDateTimeParamFiltersOperator = TF.FilterHelper.dateTimeParamFiltersOperator.includes(filterOperator);
										let isDateTimeDateParamFiltersOperator = TF.FilterHelper.dateTimeDateParamFiltersOperator.includes(filterOperator);
										let filterCellValue = dateInput.val();
										if (isDateTimeParamFiltersOperator || isDateTimeDateParamFiltersOperator)
										{
											if (isDateTimeDateParamFiltersOperator)
											{
												// the blur will be triggered twice (one is dropdown closing; another is kendo grid clicking);
												// the second blur: input value has added prefix; so need to remove
												let operatorPrefix = TF.FilterHelper.dateTimeDateParamFilterMapping[filterOperator];
												filterCellValue = filterCellValue.replace(operatorPrefix, '');
											}

										}
										if (!kendo.parseDate(filterCellValue))
										{
											dateInput.val('');
										}
										else
										{
											let momentDate = moment(filterCellValue);
											if (momentDate.year() > 2099) // currently, the maximum year of kendo is 2099, so if the year exceeds 2099, the date is invalid
											{
												dateInput.val('');
											}
											else
											{
												dateInput.val(momentDate.format("MM/DD/YYYY hh:mm A"));
											}
										}
									})
								})
								// insert number for integer paramber
								self.createDateIntegerFilterCell(span, 'datetime');
							}
						},
						ui: function(element)
						{
							var $datetimePickerBtn = $('<span style="width: 100%" class="input-group tf-filter" data-kendo-role="customizeddatetimepicker"></span>');

							var kendoDataBindStr = element.data('kendo-bind');
							$datetimePickerBtn.attr('data-kendo-bind', kendoDataBindStr);

							element.replaceWith($datetimePickerBtn);
							element.kendoCustomizedDateTimePicker({});
						}
					};
					break;
				case "boolean":
					// udf filterable definition is in UDFDefinition.js
					if (column.UDFId == null)
					{
						column.filterable = {
							positiveLabel: column.filterablePositiveLabel || "True",
							negativeLabel: column.filterableNegativeLabel || "False",
							cell: {
								template: function(args)
								{
									args.element.kendoDropDownList({
										dataSource: new kendo.data.DataSource({
											data: [
												{ someField: "(not specified)", valueField: "null" },
												{ someField: column.filterable.positiveLabel, valueField: "true" },
												{ someField: column.filterable.negativeLabel, valueField: "false" }
											]
										}),
										dataTextField: "someField",
										dataValueField: "valueField",
										valuePrimitive: true
									});
								},
								showOperators: false
							}
						};
					}
					break;
				case "image":
					column.filterable = {
						cell: {
							template: function(args)
							{
								args.element.kendoDropDownList({
									dataSource: {
										data: this.getImageFilterableDataSource(definition.FieldName)
									},
									dataTextField: "someField",
									dataValueField: "valueField",
									valuePrimitive: true,
									valueTemplate: '<span class="icon-select-item #:data.someField#"></span>',
									template: '<span class="icon-select-item #:data.someField#"></span>'
								});
							}.bind(this),
							showOperators: false
						}
					};
					break;
				case "nofilter":
					break;
			}
		}
	};


	LightKendoGrid.prototype.createDateIntegerFilterCell = function(span, columnType)
	{
		let self = this,
			numberInput = $("<input type='text' class='date-number'>");
		span.append(numberInput);
		numberInput.kendoNumericTextBox({
			format: "{0:0}",
			decimals: 0,
			min: 1,
			change: function(e)
			{
				//this.trigger('change');
				var fieldName = numberInput.closest("[data-kendo-field]").attr("data-kendo-field"),
					value = this.value(),
					operator = span.find("input.k-dropdown-operator").val();

				if (value !== null)
				{
					var validateResult = self.validateDateTimeInteger(operator, value);
					if (validateResult !== null)
					{
						e.preventDefault();

						setTimeout(() =>
						{
							tf.promiseBootbox.alert('The filter value must be range: ' + validateResult + '.').then(function()
							{
								numberInput.parent().find('.k-formatted-value').focus();
							});
						})

						return false;
					}
				}

				if (value !== null && value !== '')
				{
					self.handleDateTimeNilFilterToKendoDataSource(numberInput.val(), fieldName, operator);
				} else
				{
					let filterCell = span.closest(".k-filtercell").data('kendoFilterCell');
					if (filterCell)
					{
						filterCell.clearFilter();
					}
				}
			}
		});
		numberInput.parent().hide();
	}

	LightKendoGrid.prototype.validateDateTimeInteger = function(operator, value)
	{
		let allowRange = 0;

		if (operator === 'onyearx')
		{
			return value > 2040 || value < 1970 ? '1970 ~ 2040' : null;
		}

		switch (operator)
		{
			case "lastxdays":
			case "olderthanxdays":
			case "nextxdays":
				allowRange = 30 * 12 * 365;
				break;
			case "lastxhours":
			case "nextxhours":
				allowRange = 30 * 12 * 365 * 24;
				break;
			case "lastxmonths":
			case "nextxmonths":
				allowRange = 30 * 12;
				break;
			case "lastxweeks":
			case "nextxweeks":
				allowRange = 30 * 12 * 4;
				break;
			case "nextxyears":
			case "lastxyears":
				allowRange = 30;
				break;
			case "olderthanxmonths":
				allowRange = 30 * 12;
				break;
			case "olderthanxyears":
				allowRange = 30 * 2;
				break;
			case "lastxhours":
				allowRange = 30 * 12 * 365 * 24;
				break;
		}
		return value > allowRange ? '1 ~ ' + allowRange : null;

	}

	LightKendoGrid.prototype.postRequestData = function(url, requestOption)
	{
		let promise;
		if (this.options.getAsyncRequestOption)
		{
			promise = this.options.getAsyncRequestOption(requestOption);
		}
		else
		{
			promise = Promise.resolve(requestOption);
		}
		promise.then(response =>
		{
			tf.ajax["post"](url, response, { overlay: false });
		})
	}

	//LightKendoGrid.prototype.dateChange = function(e)
	//{
	//	var strValue = this.element.val();
	//	if (!strValue)
	//	{
	//		return true;
	//	}
	//	var dateTime = moment(TF.DateBoxHelper.convertToDateFormat(strValue));
	//	if (dateTime.isValid())
	//	{
	//		this.value(toISOStringWithoutTimeZone(dateTime));
	//		this.trigger("change");
	//	}
	//}

	LightKendoGrid.prototype.getKendoColumnsExtend = function(currentColumns, defalultColumnWidth)
	{
		var self = this;
		var columns = currentColumns.map(function(definition)
		{
			var column = definition;
			column.field = definition.FieldName;
			column.title = definition.DisplayName;
			column.headerTemplate = `<span class="column-title">${kendo.htmlEncode(definition.DisplayName ?? definition.FieldName ?? "")}</span>`;
			if (!column.width)
				column.width = definition.Width || defalultColumnWidth;
			else
				definition.Width = column.width;
			if (definition.filterable == null)
			{
				column.filterable = {
					cell: {}
				};
			}

			column.hidden = false; // Overwrite the value of hidden attribute which setting in api.
			column.locked = false;
			if (self.tobeLockedColumns.length > 0)
			{
				var lockColumn = self.tobeLockedColumns.filter(function(c)
				{
					return c.field === column.field || (column.UDFId && c.udfId === column.UDFId);
				});
				column.locked = lockColumn && lockColumn.length > 0;
			}
			if (JSON.stringify(column.filterable.cell) === '{}')
			{
				self.setColumnFilterableCell(column, definition, "lightKendoGrid");
			}

			if (definition.AllowFiltering === false)
			{
				column.filterable = false;
			}
			if (definition.AllowSorting === false)
			{
				column.sortable = false;
			}
			if (definition.template !== undefined)
			{
				column.template = definition.template;
			}
			if (!self.options.showOperators)
			{
				if (column.filterable)
				{
					column.filterable.cell.showOperators = false;
				}
			}
			return column;
		});

		return columns;
	}

	LightKendoGrid.prototype.getImageFilterableDataSource = function(fieldName)
	{
		var dataSource = [];
		switch (fieldName)
		{
			case "Ampmschedule":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmschedule("14"), valueField: "14" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmschedule("15"), valueField: "15" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmschedule("16"), valueField: "16" });
				break;
			case "Ampmtransportation":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmtransportation("10"), valueField: "10" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmtransportation("11"), valueField: "11" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Ampmtransportation("12"), valueField: "12" });
				break;
			case "RidershipStatus":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_RidershipStatus("37"), valueField: "37" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_RidershipStatus("39"), valueField: "39" });
				break;
			case "PolicyDeviation":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_PolicyDeviation("37"), valueField: "37" });
				break;
			case "Notes":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.studentGridDefinition.gridDefinition().getIconUrl_Notes("5"), valueField: "5" });
				break;
			case "IsLocked":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_IsLocked("6"), valueField: "6" });
				dataSource.push({ someField: tf.tripGridDefinition.gridDefinition().getIconUrl_IsLocked(""), valueField: "neq" });
				break;
			case "Error":
				dataSource.push({ someField: "", valueField: "" });
				dataSource.push({ someField: TF.GridDefinition.FormGridDefinition.getIconUrl_IsLogError(true), valueField: "true" });
				dataSource.push({ someField: TF.GridDefinition.FormGridDefinition.getIconUrl_IsLogError(false), valueField: "false" });
				break;
		}
		return dataSource;
	}

	LightKendoGrid.prototype.getImageFilterReverseSelection = function(filter)
	{
		switch (filter.field)
		{
			case "IsLocked":
				if (filter.value == "neq")
				{
					filter.operator = filter.value;
					filter.value = "6";
				}
				break;
		}
		return filter;
	}

	LightKendoGrid.prototype.getKendoField = function()
	{
		var fields = {};
		this._gridDefinition.Columns.forEach(function(definition)
		{
			var field = {};
			switch (definition.type)
			{
				case "string":
					field.parse = definition.parse || kendoStringTypeParser;
					field.type = "string";
					break;
				case "boolean":
					field.type = "string";
					break;
				case "integer":
				case "number":
					field.type = "number";
					break;
				case "time":
					field.parse = function(v)
					{
						if (!v) return "";

						var formatStr = "YYYY/MM/DD HH:mm:ss";
						var format = moment(v).format(formatStr);
						if (format === "Invalid date")
						{
							formatStr = "HH:mm:ss";
							format = moment(v).format(formatStr);
						}
						else
						{
							formatStr = "yyyy/MM/dd HH:mm:ss";
						}

						v = format !== "Invalid date" ? format : v;
						if (typeof v === 'string' && (v.toLocaleLowerCase().indexOf('pm') > 0 || v.toLocaleLowerCase().indexOf('am') > 0))
							return kendo.parseDate(v, formatStr + " t", kendo.getCulture())
						else
							return kendo.parseDate(v, formatStr, kendo.getCulture())
					};
					field.type = "date";
					break;
				case "datetime":
					field.type = "datetime";
					break;
				case "date":
					field.type = "date";
					break;
			}
			field.validation = definition.validation;
			fields[definition.FieldName] = field;
		});
		return fields;
	};

	LightKendoGrid.prototype.getApiRequestURL = function(url)
	{
		if (this.options.setRequestURL)
		{
			url = this.options.setRequestURL(url);
		}

		return url;
	};

	LightKendoGrid.prototype.getIdName = function()
	{
		if (this.options.Id) return this.options.Id;

		if (this._gridType === 'attendance')
		{
			return "AttendanceId";
		}

		return "Id";
	};

	function _isPhoneColumn(self, autoCompleteSelectedColumn)
	{
		let columnField = self.options.gridDefinition.Columns?.find(x => x.FieldName === autoCompleteSelectedColumn);
		return tf.dataFormatHelper.isPhoneColumn(columnField);
	}

	function updateFilterItemsForEmptyType(filterSet)
	{
		if (!filterSet)
		{
			return filterSet;
		}

		filterSet.FilterItems.forEach(function(item)
		{
			if (["isnull", "isnotnull"].includes(item.Operator.toLowerCase()))
			{
				item.value = '';
			}
		});

		return filterSet;
	}

	LightKendoGrid.prototype.getApiRequestOption = async function(kendoOptions, beforeGridInit = false)
	{
		if (beforeGridInit && !(this instanceof TF.Grid.KendoGrid && this.isBigGrid))
		{
			return null;
		}

		var self = this,
			includeOnlyIds = self.getIncludeOnlyIds(),
			excludeAnyIds = self.getExcludeAnyIds(),
			isStringId = !!(self.options && self.options.idSeparator),
			sortItems = [];

		if (beforeGridInit)
		{
			sortItems = this.getKendoSortColumn();
		}
		else
		{
			sortItems = kendoOptions.data.sort ? kendoOptions.data.sort : [];
		}
		sortItems = sortItems.map(function(item)
		{
			return {
				Name: tf.UDFDefinition.getOriginalName(item.field),
				isAscending: function()
				{
					return item.dir === "asc";
				},
				Direction: item.dir === "asc" ? "Ascending" : "Descending"
			};
		});

		var options = {
			paramData: {
				databaseId: this.options.dataSourceId || tf.datasourceManager.databaseId,
				take: beforeGridInit ? 200 : (kendoOptions.data.take ? kendoOptions.data.take : 100),
				skip: beforeGridInit ? 0 : kendoOptions.data.skip ? kendoOptions.data.skip : 0,
				getCount: self.options.getCount == false ? false : true
			},
			data: {
				sortItems: sortItems,
				idFilter: (includeOnlyIds || excludeAnyIds) ? {
					IncludeOnly: isStringId ? null : includeOnlyIds,
					ExcludeAny: isStringId ? [] : excludeAnyIds,
					IncludeOnlyIdsString: isStringId ? includeOnlyIds : null,
					ExcludeAnyIdsString: isStringId ? excludeAnyIds : null,
				} : null,
				filterSet: (self._gridState && self._gridState.filterSet) ? self._gridState.filterSet : null,
				filterClause: ""
			},
			traditional: true
		};

		if (!beforeGridInit)
		{
			options.success = function(result)
			{
				if (self.checkFilteredResponse(result))
				{
					return;
				}
				if (self.kendoGrid == null)
				{
					return;
				}

				let autoCompleteSelectedColumn = kendoOptions.data.autoCompleteSelectedColumn;
				result.Items = LightKendoGrid.normalizeResultItem(result.Items, self._gridType, self.options);
				if (kendoOptions.data.isFromAutoComplete && autoCompleteSelectedColumn)
				{
					result.Items = Enumerable.From(result.Items).Select(function(item)
					{
						let columnValue = _isPhoneColumn(self, autoCompleteSelectedColumn) ?
							tf.dataFormatHelper.phoneFormatter(item[autoCompleteSelectedColumn]) :
							item[autoCompleteSelectedColumn];
						item[autoCompleteSelectedColumn] = $.trim(columnValue);
						return item;
					}).Where(function(item)
					{
						return item[autoCompleteSelectedColumn];
					}).Distinct(`x=>x["${autoCompleteSelectedColumn}"]`).OrderBy(`\$["${autoCompleteSelectedColumn}"]`).ToArray();
				}

				if (result.Items && result.Items instanceof Array)
				{
					const udfs = self.getUdfs();
					result.Items = result.Items.map(function(item)
					{
						udfs.forEach(function(udf)
						{
							item[udf.FieldName] = item[udf.OriginalName];
						});

						return item;
					});
					self.resultChangedIds = result.Items.map(x => x.Id);
				}

				//verify ajax by filter control or real ajax request
				self.result = result;

				kendoOptions.success({
					d: {
						__count: result.FilteredRecordCount,
						results: result.Items
					}
				});
				// self.$container.find(".k-loading-mask").remove();
			},
				options.error = function(result)
				{
					if (result && result.Message)
					{
						if (self instanceof TF.Grid.KendoGrid && result.StatusCode !== 403)
						{
							self.gridAlert.show({
								alert: "Danger",
								title: "Error",
								message: result.Message
							});
							kendoOptions.error(result);
						}
					}
				}
		}

		if (tf.stopfinderUtil.isStopfinderType(self.options.gridType))
		{
			options.paramData.timeOffset = tf.timezonetotalminutes;
		}
		if (self.obClassicFilterSet())
		{
			options.data.filterSet = self.obClassicFilterSet();
		}
		if (self._gridState && self._gridState.filterClause)
		{
			options.data.filterClause = self._gridState.filterClause;
			if (self.obSelectedGridFilterClause())
			{
				options.data.filterClause += " and " + self.obSelectedGridFilterClause();
			}
		}
		else
		{
			options.data.filterClause = self.obSelectedGridFilterClause() || "";
		}

		if (self.obSelectedGridFilterType && ![undefined, null].includes(self.obSelectedGridFilterType()))
		{
			options.data.isQuickSearch = self.obSelectedGridFilterType();
		}

		if (self.options.kendoGridOption.dataSource && self.options.kendoGridOption.dataSource.serverPaging === false)
		{
			options.paramData = {};
		}

		self.overlay = kendoOptions ? !kendoOptions.data.disableOverlay : null;

		const selectedColumns = self._obSelectedColumns();
		var fields = Array.from(selectedColumns && selectedColumns.length > 0 ? selectedColumns : self._gridDefinition.Columns)
			.filter(function(c) { return !c.hidden; })
			.map(function(c) { return c.FieldName });

		fields = (fields || []).map(function(field)
		{
			return tf.UDFDefinition.getOriginalName(field);
		});

		if (self.isBigGrid && self.options.loadAllFields === false)
		{
			options.data.sortItems = options.data.sortItems.concat(tf.helpers.kendoGridHelper.getDefaultSortItems(self._gridType, self.options.Id));
			if (!Enumerable.From(fields).Contains(self.options.Id))
			{
				fields = fields.concat([self.options.Id]);
			}
			fields = self.bindNeedFileds(self._gridType, fields);
			options.data.fields = fields;
		}

		if (self.options.sort)
		{
			options.data.sortItems = options.data.sortItems.concat([self.options.sort]);
		}
		let defaultNameSortGridTypes = ['scheduledreport', 'dashboards', 'scheduledDashboard'];
		if (options.data.sortItems.length === 0 && defaultNameSortGridTypes.includes(self._gridType))
		{
			options.data.sortItems = [{ "Name": "Name", "isAscending": "asc", "Direction": "Ascending" }];
		}

		(options.data.sortItems || []).forEach(function(item)
		{
			item.Name = tf.UDFDefinition.getOriginalName(item.Name);
		});

		if (self.obSelectedGridFilterParameterFilterSet && self.obSelectedGridFilterParameterFilterSet())
		{
			options.data.filterSet = self.obSelectedGridFilterParameterFilterSet();
		}

		if (beforeGridInit)
		{
			var filterData = self.getQuickFilter().data;
			if ((filterData.filterSet && filterData.filterSet.FilterItems && filterData.filterSet.FilterItems.length > 0) ||
				(filterData.filterSet && filterData.filterSet.FilterSets && filterData.filterSet.FilterSets.length > 0))
			{
				var kendoFilters = {
					filters: self.convertRequest2KendoFilterSet(filterData.filterSet),
					logic: "and"
				};
				options.data.filterSet = self.convertKendo2RequestFilterSet(options.data.filterSet, kendoFilters);
			}
		}
		else
		{
			if (kendoOptions.data.filter)
			{
				options.data.filterSet = self.convertKendo2RequestFilterSet(options.data.filterSet, kendoOptions.data.filter);
			}
		}

		if (tf.stopfinderUtil.isStopfinderType(self.options.gridType) && self.options.gridType === "invitationsent")
		{
			fields = self.bindNeedFileds(self.options.gridType, fields);
			options.data.fields = fields;
		}

		//verify ajax by filter control or real ajax request
		if (!beforeGridInit && kendoOptions.data.isFromAutoComplete !== true)
		{
			if (JSON.stringify(self._obSortedItems()) != JSON.stringify(sortItems ? sortItems : []))
			{
				self._obSortedItems(sortItems ? sortItems : []);
			}

			//filter
			if (self.obHeaderFilters && self.obHeaderFilters().length > 0 && !(options.data.filterSet && options.data.filterSet.FilterItems && options.data.filterSet.FilterItems.length > 0))
			{
				self.obHeaderFilters.removeAll();
			}
			else if (!(options.data.filterSet && options.data.filterSet.FilterItems && options.data.filterSet.FilterItems.length > 0) && self.obHeaderFilters().length > 0)
			{
				self.obHeaderFilters([]);
			}

			if (self.obHeaderFilterSets && self.obHeaderFilterSets().length > 0)
			{
				self.obHeaderFilterSets([]);
			}

			//verify is scroll paging or not，scroll paging will not clear slect ids and no overlay
			var newFilterString = JSON.stringify(kendoOptions.data.filter),
				newSortString = JSON.stringify(kendoOptions.data.sort),
				newFilterId = self.obSelectedGridFilterClause(),
				newFields = JSON.stringify(fields),
				newIdIncludeOnlyFilter = JSON.stringify(options.data.idFilter.IncludeOnly),
				newIdExcludeAnyFilter = JSON.stringify(options.data.idFilter.ExcludeAny);
			if ((newIdIncludeOnlyFilter !== self._oldIdIncludeOnlyFilter || newFilterId !== self._oldFilterId || newFilterString !== self._oldFilterString || newFields !== self._oldFields))
			{
				self.getSelectedIds([]);
				self.allIds = [];
			}
			// else if (newIdExcludeAnyFilter == this._oldIdExcludeAnyFilter && this.overlayShow !== true)  // comment this block for fix issue of apply busfinder/gps events reduce count filter, overlay not display
			// {
			// 	this.overlay = false;
			// }
			self._oldFilterString = newFilterString;
			self._oldSortString = newSortString;
			self._oldFilterId = newFilterId;
			self._oldFields = newFields;
			self._oldIdIncludeOnlyFilter = newIdIncludeOnlyFilter;
			self._oldIdExcludeAnyFilter = newIdExcludeAnyFilter;
		}

		if (!beforeGridInit && (kendoOptions.data.filter || self._gridState.filterSet))
		{
			if (self.isEmptyFilterSet(options.data.filterSet))
			{
				delete options.data.filterSet;
			}
			else if (kendoOptions.data.isFromAutoComplete !== true)
			{
				var oldInitialFilter = self.initialFilter, change = false;
				self.initialFilter = true;
				if (options.data.filterSet.FilterItems && options.data.filterSet.FilterItems.length > 0)
				{
					self.obHeaderFilters(JSON.parse(JSON.stringify(options.data.filterSet.FilterItems)));
					change = true;
				}

				if (options.data.filterSet.FilterSets && options.data.filterSet.FilterSets.length > 0)
				{
					self.obHeaderFilterSets(JSON.parse(JSON.stringify(options.data.filterSet.FilterSets)));
					change = true;
				}
				self.initialFilter = oldInitialFilter;
				if (change && self._currentFilterChange)
				{
					self._currentFilterChange();
				}
			}
		}

		let sortColumns = TF.FilterHelper.getSortColumns(self._gridDefinition.Columns);
		if (self.isListMoverWithSearch)
		{
			sortColumns = sortColumns.map(col =>
			{
				const newCol = _.cloneDeep(col);
				if (newCol.FieldName && newCol.FieldName.length > 1 && /^[0-9a-f]{32}$/i.test(newCol.FieldName.substr(1)))
				{
					newCol.FieldName = tf.UDFDefinition.getOriginalName(newCol.FieldName);
				}
				return newCol;
			})
		}

		options = TF.FilterHelper.setSortItems(options, sortColumns);

		self._removeInvisibleListFilterItems(self.getKendoColumn());
		options = self.setListFilterRequestOption.bind(self)(options);
		removeEmptyFilterItems(options);

		if (!beforeGridInit)
		{
			options.isFromAutoComplete = kendoOptions.data.isFromAutoComplete;
			options.disableTriggerDashboardLiveFilter = kendoOptions.data.disableTriggerDashboardLiveFilter;
		}

		if (self.options.isFromDashboardActiveFilter)
		{
			options.disableTriggerDashboardLiveFilter = true;
			self.options.isFromDashboardActiveFilter = false;
		}

		if (self.options.setRequestOption)
		{
			if (!beforeGridInit)
			{
				options.isOnlyDefaultSort = kendoOptions.data.sort ? kendoOptions.data.sort.length === 0 : false;
			}
			else
			{
				options.isOnlyDefaultSort = sortItems ? sortItems.length === 0 : false;
			}
			options = self.options.setRequestOption(options);
		}

		if (options && options.data.filterSet && self.isEmptyFilterSet(options.data.filterSet))
			delete options.data.filterSet;

		if (options && options.data.filterSet && options.data.filterSet.FilterItems && options.data.filterSet.FilterItems.length > 0)
		{
			updateFilterItemsUDFFilterName(options.data.filterSet);
			updateFilterItemsForEmptyType(options.data.filterSet);
		}

		if (options && options.data.filterSet && options.data.filterSet.FilterSets && options.data.filterSet.FilterSets.length > 0)
		{
			var filterSets = options.data.filterSet.FilterSets;
			filterSets.forEach(function(filterSet)
			{
				updateFilterItemsUDFFilterName(filterSet);
				updateFilterItemsForEmptyType(filterSet);
			});
		}

		if (self.options.getAsyncRequestOption)
		{
			options = await this.options.getAsyncRequestOption(options);
		}

		if (!beforeGridInit && kendoOptions.data.isFromAutoComplete !== true)
		{
			self.searchOption = options;
		}
		else
		{
			if (self.options.gridType === 'Role' && !options.data.idFilter.ExcludeAny.includes(-999))
			{
				options.data.idFilter.ExcludeAny = options.data.idFilter.ExcludeAny.concat([-999]);
			}
		}

		if (this.options.kendoGridOption.filterSet)
		{
			if (options.data.filterSet)
			{
				options.data.filterSet.FilterItems = [
					...this.options.kendoGridOption.filterSet.FilterItems,
					...options.data.filterSet.FilterItems
				];
			} else
			{
				options.data.filterSet = this.options.kendoGridOption.filterSet;
			}
		}

		self.FormatFilterClause(options);
		self.onFilterChanged && (self.onFilterChanged.notify(options));

		if (self._gridType === "tripstopschedule" && options && options.isFromAutoComplete !== true && options.data)
		{
			if (self.preRequestData
				&& (self.preRequestData.filterClause || "") === (options.data.filterClause || "")
				&& _.isEqual(self.preRequestData.filterSet.FilterItems || [], options.data.filterSet.FilterItems || [])
				&& _.isEqual(self.preRequestData.filterSet.FilterSets || [], options.data.filterSet.FilterSets || [])
				&& !self._gridState?.clearUseCache)
			{
				options.data.filterSet.IsUseCache = true;
			}

			self.preRequestData = $.extend(true, {}, options.data);
		}
		options && (options.IncludeOnly = await self.bindNeedIncludedIds(self._gridType, options));
		return options;

		function updateFilterItemsUDFFilterName(filterSet)
		{
			filterSet.FilterItems = processVehicleExternalName(filterSet.FilterItems);
			filterSet.FilterItems = (filterSet.FilterItems || []).map(function(item)
			{
				item.FieldName = tf.UDFDefinition.getOriginalName(item.FieldName);
				return item;
			});

			self.setBooleanNotSpecifiedFilterItem(filterSet.FilterItems);
		}

		function processVehicleExternalName(filterItems)
		{
			filterItems = filterItems.map(function(item)
			{
				if (item.Operator === 'In' &&
					item.FieldName == 'VehicleExternalName')
				{
					var tmp = JSON.parse(item.ValueList);
					tmp = tmp.map(function(t)
					{
						t = t.trim();
						return t;
					})
					item.ValueList = JSON.stringify(tmp);
				}

				return item;
			});

			return filterItems;
		}
	};

	LightKendoGrid.prototype.getUdfs = function()
	{
		const currentColumns = [].concat(this._obSelectedColumns()).concat(this.getKendoColumn());
		return _.uniqBy(currentColumns.filter(c => !!c.OriginalName), "UDFId");
	}

	LightKendoGrid.prototype.checkFilteredResponse = function(response)
	{
		// handle the results question columns
		if (this._gridType === "stopfindersentformresult")
		{
			response.Items.forEach(function(item)
			{
				if (item.questionAnswers.length > 0)
				{
					item.questionAnswers.forEach(function(question)
					{
						item["question" + question.questionId] = question.value;
					})
				}
			});
		}
		if ([400, 412].includes(response.StatusCode) && tf.dataTypeHelper.getNameByType(this.options.gridType))
		{
			return this.resetFilterForCurrentInvalidFilter(response.Message);
		}
	};

	LightKendoGrid.prototype.resetFilterForCurrentInvalidFilter = function(errorMessage)
	{
		var self = this;
		if (self._isShowingInvalidFilterAlert || !self.obSelectedGridFilterId)
		{
			return;
		}

		var filterId = self.obSelectedGridFilterId();
		var quickFilterItems = self.getQuickFilter()?.data?.filterSet?.FilterItems;
		if (!filterId && (!quickFilterItems || quickFilterItems.length === 0))
		{
			return;
		}

		self.isFilterReset = true;
		self._isShowingInvalidFilterAlert = true;
		var filterObj = self.obGridFilterDataModels().find(o => o.id() === filterId),
			filterName = filterObj ? filterObj.name() : "None",
			message = filterId ? `The applied filter "${kendo.htmlEncode(filterName)}" is invalid.` : "The applied quick filter is invalid.";

		let preUpdateFilterPromise;
		if (self.options.isMiniGrid)
		{
			preUpdateFilterPromise = Promise.resolve();
		}
		else
		{
			preUpdateFilterPromise = tf.promiseBootbox.alert(message);
		}
		return preUpdateFilterPromise.then(function()
		{
			self.gridSuccessResponse = false;
			self._isShowingInvalidFilterAlert = false;
			var updateFilterPromise = Promise.resolve();

			if (filterId)
			{
				// use error message to distinguish deleted udf and invisible udf.
				updateFilterPromise = tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters", filterId), {
					data: [{ "op": "replace", "path": "/IsValid", "value": false }]
				}).then(function()
				{
					var filters = self.obGridFilterDataModels().filter(function(filter)
					{
						return filter.id() === filterId;
					});

					if (filters && filters.length === 1)
					{
						filters[0].isValid(false);
					}
				})
			}

			updateFilterPromise.then(() =>
			{
				self.resetLayoutClick();
			})
		});
	};

	function removeEmptyFilterItems(options)
	{
		if (options && options.data &&
			options.data.filterSet && options.data.filterSet.FilterItems)
			options.data.filterSet.FilterItems = TF.CustomFilterHelper.removeEmptyFilterItems(options.data.filterSet.FilterItems);
	}

	LightKendoGrid.prototype.setBooleanNotSpecifiedFilterItem = function(filterItems = [])
	{
		var self = this;
		var columns = self.kendoGrid?.columns || self.getKendoColumn();
		filterItems.forEach(function(filterItem)
		{
			var filterBooleanColumns = columns.filter(function(col)
			{
				var colFieldName = tf.UDFDefinition.getOriginalName(col.field);
				return col.type === "boolean" && colFieldName === filterItem.FieldName;
			});

			if (filterBooleanColumns.length > 0 && filterItem.Value === "null")
			{
				filterItem.Operator = "IsNull";
				filterItem.Value = "";
			}
		});
	};

	LightKendoGrid.prototype.removeUnusedListFilterRequestOption = function(options)
	{
		var unUsedFilter = function(filterItem)
		{
			return (filterItem.FieldName != null) && ((filterItem.Operator !== 'In') ||
				(filterItem.Operator === 'In' && filterItem.ValueList));
		};

		if (options.data.filterSet && options.data.filterSet.FilterItems && options.data.filterSet.FilterItems.length > 0)
		{
			options.data.filterSet.FilterItems = options.data.filterSet.FilterItems.filter(function(filterItem)
			{
				return unUsedFilter(filterItem);
			});
		}

		if (options.data.filterSet && options.data.filterSet.FilterSets && options.data.filterSet.FilterSets.length > 0)
		{
			var filterSets = options.data.filterSet.FilterSets;
			filterSets.forEach(function(filterSet)
			{
				filterSet.FilterItems = filterSet.FilterItems.filter(function(filterItem)
				{
					return unUsedFilter(filterItem);
				});
			});
		}
		return options;
	};

	LightKendoGrid.prototype.isEmptyFilterSet = function(filterSet)
	{
		return (
			!filterSet.FilterSets ||
			filterSet.FilterSets.length === 0
		) && (
				!filterSet.FilterItems ||
				filterSet.FilterItems.length === 0 ||
				filterSet.FilterItems[0] === undefined
			);
	};

	LightKendoGrid.prototype.convertKendo2RequestFilterSet = function(optionFilterSet, kendofilter)
	{
		optionFilterSet = optionFilterSet || {};
		var self = this;

		var kendofilterFilters = kendofilter.filters?.filter(x => x.FieldName !== "ParameterDateRange");
		if (!kendofilterFilters || kendofilterFilters.length === 0)
			return optionFilterSet;

		var newFilterSets = [];
		var newFilterItems = [];

		if (self.isOnlyApplyOneMenuFilterAndFillTwoInputType(kendofilterFilters))
		{
			var tmpFilterSet = {};
			tmpFilterSet = self.buildMenuFilterSet(kendofilter);

			if (tmpFilterSet.LogicalOperator) // ignore empty filter set
				newFilterSets.push(tmpFilterSet);
		}
		else
		{
			var fieldNamsApplyiedMenuFilter = self.getFieldNamesAppliedMenuFilter();

			kendofilterFilters.map(function(kendofilterFilter)
			{
				if (kendofilterFilter.filters && kendofilterFilter.filters.length === 2)
				{
					kendofilterFilter.isMenuFilter = true;
				} else if (self.options.isMiniGrid && kendofilterFilter.filters && kendofilterFilter.filters.some(filter => filter.isMenuFilter == true))
				{
					kendofilterFilter.isMenuFilter = true;
				}
				else
				{
					var field = kendofilterFilter.field;
					if (!field && kendofilterFilter.filters && kendofilterFilter.filters.length > 0 && kendofilterFilter.filters[0].field)
						field = kendofilterFilter.filters[0].field;
					kendofilterFilter.isMenuFilter = Array.contain(fieldNamsApplyiedMenuFilter, field);
				}
			});

			var menuFilters = kendofilterFilters.filter(function(kendofilterFilter) { return kendofilterFilter.isMenuFilter; });
			if (menuFilters.length > 0)
			{
				menuFilters.map(function(menuFilter)
				{
					var tmpFilterSet = {};

					if (!menuFilter.filters)
					{
						tmpFilterSet.FilterItems = [];
						tmpFilterSet.FilterItems.push(self.buildFilterItem(menuFilter));
						tmpFilterSet.LogicalOperator = 'and';
						tmpFilterSet.FilterSets = [];
					}
					else
					{
						tmpFilterSet = self.buildMenuFilterSet(menuFilter);
					}

					if (tmpFilterSet.LogicalOperator) // ignore empty filter set
						newFilterSets.push(tmpFilterSet);
				});
			}

			var itemFilters = kendofilterFilters.filter(function(kendofilterFilter) { return !kendofilterFilter.isMenuFilter; });
			if (itemFilters.length)
			{
				itemFilters.forEach(function(itemFilter)
				{
					if ((itemFilter.filters?.length ?? 0) === 1 && (itemFilter.filters[0].filters?.length ?? 0) > 0)
					{
						// it should be a filter set
						newFilterSets.push(self.buildMenuFilterSet(itemFilter.filters[0]));
					}
					else
					{
						var tmpItemFilter = self.buildFilterItem(itemFilter);
						newFilterItems.push(tmpItemFilter);
					}
				});
			}
		}

		if (optionFilterSet && optionFilterSet.FilterSets && optionFilterSet.FilterSets.length > 0)
			newFilterItems = newFilterItems.concat(optionFilterSet.FilterSets);

		if (optionFilterSet && optionFilterSet.FilterItems && optionFilterSet.FilterItems.length > 0)
			newFilterItems = newFilterItems.concat(optionFilterSet.FilterItems);

		optionFilterSet = {
			FilterItems: newFilterItems,
			FilterSets: newFilterSets,
			LogicalOperator: 'and'
		};
		return optionFilterSet;
	}

	LightKendoGrid.prototype.getFieldNamesAppliedMenuFilter = function()
	{
		if (!this.kendoGrid || !this.kendoGrid.wrapper)
			return [];

		var wrapper = this.kendoGrid.wrapper;
		var $filterBtnParents = wrapper.find('.k-filter-custom-btn:not(.hidden)').closest(".k-table-th.k-header.k-filterable");
		var fieldNamsApplyiedMenuFilter = $filterBtnParents.map(function(idx, filterBtnParent)
		{
			return $(filterBtnParent).data('kendo-field');
		});
		return fieldNamsApplyiedMenuFilter.toArray();
	};

	LightKendoGrid.prototype.isOnlyApplyOneMenuFilterAndFillTwoInputType = function(kendofilterFilters)
	{
		return (kendofilterFilters.length === 2 &&
			kendofilterFilters[0].field !== undefined &&
			kendofilterFilters[0].field === kendofilterFilters[1].field);
	};

	LightKendoGrid.prototype.buildMenuFilterSet = function(menuFilter)
	{
		var self = this;
		var filterItems = menuFilter.filters.map(function(item)
		{
			return self.buildFilterItem(item);
		});

		filterItems = TF.CustomFilterHelper.removeEmptyFilterItems(filterItems);
		if (filterItems && filterItems.length)
		{
			customFilterFilterSet = {
				LogicalOperator: menuFilter.logic,
				FilterItems: filterItems,
				FilterSets: []
			};
		}
		else
		{
			customFilterFilterSet = {};
		}

		return customFilterFilterSet;
	};

	LightKendoGrid.prototype.buildFilterItem = function(item)
	{
		var self = this;

		if (item.operator === "empty")
		{
			return;
		}

		item = self.getImageFilterReverseSelection(item);
		var filter = {
			FieldName: item.field,
			Operator: this.operatorKendoMapTF[item.operator],
			Value: item.value
		};

		if (item.ValueList || item.valueList)
		{
			if (self.isJsonFormat(item.ValueList) || self.isJsonFormat(item.valueList))
			{
				filter.ValueList = item.valueList || item.ValueList;
				if (self.options.isMiniGrid && item.selectedIds)
				{
					filter.ListFilterIds = item.selectedIds;
				}
			}
			else
			{
				filter.ValueList = JSON.stringify(item.valueList || item.ValueList);
				if (self.options.isMiniGrid && item.selectedIds)
				{
					filter.ListFilterIds = JSON.stringify(item.selectedIds);
					filter.selectedIds = JSON.stringify(item.selectedIds);
				}
			}
		}

		// todo: build list valuelist here
		//init type hint
		var columnConfig = self._gridDefinition.Columns.find(d => d.FieldName === item.field);
		if (columnConfig)
		{
			filter.TypeHint = "String";
			if (columnConfig.type === "number")
			{
				filter.Precision = isNumber(columnConfig.Precision) ? columnConfig.Precision : 2;
			}
			else if (columnConfig.type === "time")
			{
				filter.TypeHint = "Time";
				if (moment(filter.Value).isValid())
				{
					filter.Value = toISOStringWithoutTimeZone(moment(filter.Value));
				}
				else if (moment(kendo.parseDate(filter.Value)).isValid())
				{
					var format1 = 'h:m tt';
					var timeValue = kendo.parseDate(filter.Value, format1) || kendo.parseDate(filter.Value);
					filter.Value = toISOStringWithoutTimeZone(moment(timeValue));
				}
			}
			else if (columnConfig.type === "datetime")
			{
				filter.TypeHint = "DateTime";
				if (TF.FilterHelper.dateTimeNilFiltersOperator.indexOf(item.operator) >= 0)
				{
					filter.Operator = this.operatorKendoMapTF[item.operator];
					if (columnConfig.isUTC)
					{
						filter.ConvertedToUTC = true;
					}

				} else
				{
					//if it is of 'onx' related operators and UTC column
					if (columnConfig.isUTC && TF.FilterHelper.dateTimeDateParamFiltersOperator.indexOf(item.operator) < 0)
					{
						filter.Value = toISOStringWithoutTimeZone(clientTimeZoneToUtc(moment(filter.Value).format("YYYY-MM-DDTHH:mm:ss")));
						filter.ConvertedToUTC = true;
					}
					else
					{
						filter.Value = toISOStringWithoutTimeZone(moment(filter.Value));
					}
				}

			}
			else if (columnConfig.type === "date")
			{
				filter.TypeHint = "Date";
				if (columnConfig.isUTC)
				{
					filter.ConvertedToUTC = true;
				}
				if (TF.FilterHelper.dateTimeNilFiltersOperator.indexOf(item.operator) >= 0)
				{
					filter.Operator = this.operatorKendoMapTF[item.operator];
				}
				else if (TF.FilterHelper.dateTimeDateParamFiltersOperator.indexOf(item.operator) > -1)
				{
					filter.Value = toISOStringWithoutTimeZone(moment(filter.Value));
				}
				else
				{
					if (columnConfig.isUTC)
					{
						filter.Value = toISOStringWithoutTimeZone(clientTimeZoneToUtc(moment(filter.Value).format("YYYY-MM-DDTHH:mm:ss")));
						filter.ExactHint = "utc";
					}
					else if (filter.Operator !== "IsWithIn")
					{
						filter.Value = toISOStringWithoutTimeZone(moment(filter.Value));
					}
					else
					{
						// Nothing to do
					}
				}
			}
			else if (columnConfig.type === "boolean")
			{
				// Some boolean type column have special positive value rather than true.
				if (columnConfig.positiveValue)
				{
					filter = tf.helpers.kendoGridHelper.booleanFilterConvert(filter, columnConfig.positiveValue);
				}
			}

			if (columnConfig.TypeHint)
			{
				filter.TypeHint = columnConfig.TypeHint;
			}

			if (columnConfig.type === "integer" || columnConfig.TypeHint === "integer")
			{
				if (filter.Value && filter.Value.toFixed)
				{
					filter.Value = filter.Value.toFixed();
				}
			}
		}

		if (self.options.customGridType === "dashboardwidget" && (self.options.gridType === "gpsevent" || self.options.gridType === "locationevent"))
		{
			filter.isGridFilter = true;
		}
		return filter;
	};

	LightKendoGrid.prototype.resetRequestOption = function(requestOption)
	{
		let options = $.extend(true, {}, requestOption);
		tf.helpers.kendoGridHelper.resetTimeFilterSet(options?.data?.filterSet?.FilterItems);

		// tag block change, first lazyload grid, update the includes ids
		if (this.options.gridType === "TagGrid")
		{
			const key = this.options.gridType + this.$container?.data("uniqueClassName");
			const includeIds = this.lazyLoadScrollContainer?.cacheOnIncludeIds && this.lazyLoadScrollContainer?.cacheOnIncludeIds[key];
			if (includeIds)
			{
				options.data.idFilter.IncludeOnly = includeIds;
			}
		}
		return options;
	}

	LightKendoGrid.prototype.isJsonFormat = function(obj)
	{
		var isjson = typeof (obj) === "string" && obj.indexOf('[\"') >= 0;
		return isjson;
	};

	LightKendoGrid.prototype.bindNeedIncludedIds = async function(type, options)
	{
		if (type === "tags")
		{
			const dataTypeId = tf.dataTypeHelper.getId(this.options.baseRecordType)
			const availableTagIds = (await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `tags/ids?dataType=${dataTypeId}`)))?.Items || [];
			this.options.totalRecordCount = availableTagIds.length;
			if (options.data.idFilter?.IncludeOnly)
			{
				options.data.idFilter.IncludeOnly = options.data.idFilter.IncludeOnly.filter(n => availableTagIds.includes(n));
			}
			else
			{
				options.data.idFilter.IncludeOnly = availableTagIds;
			}
		}
	}

	LightKendoGrid.prototype.bindNeedFileds = function(type, fields)
	{
		fields = LightKendoGrid.bindNeedFileds(type, fields);

		if (type === 'tripstop')
		{
			if (this.options && this.options.kendoGridOption && this.options.kendoGridOption.entityId)
			{
				if (this.options.kendoGridOption.entityId == 1 || this.options.kendoGridOption.entityId == 2)
				{
					fields = fields.concat(['Tripstopid']);
				}
			}
		} else if (type === "fieldtripinvoice" && fields.includes("FieldTripStageName"))
		{
			fields = fields.concat(["FieldTripStageID"]);
		}
		else if (type === TF.Helper.DataTypeHelper.FormResultGridDataType)
		{
			fields = fields.concat(['UDGridID']);
		}

		return fields;
	};

	function addNeedFields(fields, needFields)
	{
		var needToAddFields = [];
		needFields.forEach(function(needField)
		{
			if (!Enumerable.From(fields).Contains(needField))
			{
				needToAddFields.push(needField);
			}
		});

		return fields.concat(needToAddFields);
	}

	LightKendoGrid.bindNeedFileds = function(type, fields)
	{
		switch (type)
		{
			case 'school':
				fields = addNeedFields(fields, ["School"]);
				break;
			case 'trip':
				fields = addNeedFields(fields, ["Schools", "Name", "AideId", "DriverId", "VehicleId"]);
				break;
			case 'vehicle':
				fields = addNeedFields(fields, ["BusNum"]);
				break;
			case 'altsite':
			case 'georegion':
			case 'district':
			case 'contractor':
			case 'fieldtriptemplate':
				fields = addNeedFields(fields, ["Name"]);
				break;
			case 'student':
				fields = addNeedFields(fields, ["School", "FirstName", "LastName", "Grade"]);
				break;
			case 'tripstop':
				fields = addNeedFields(fields, ["TripId", "Street", "Sequence"]);
				break;
			case 'staff':
				fields = addNeedFields(fields, ["FirstName", "LastName", "StaffTypes", "ContractorId", "UserID"]);
				break;
			case 'fieldtrip':
				fields = addNeedFields(fields, ["School", "Name", "DepartDateTime", "FieldTripStageId", "RequestedBy"]);
				break;
			case 'document':
				fields = addNeedFields(fields, ["FileName", "MimeType"]);
				break;
			case 'report':
				fields = addNeedFields(fields, ["IsSystem", "Type"]);
				break;
			case 'reportlibrary':
				fields = addNeedFields(fields, ["IsPreviewAvailable", "Status"]);
				break;
			case 'route':
				fields = fields.concat(['HasTrip']);
				break;
			case 'fieldtripinvoice':
				fields = addNeedFields(fields, ["FieldTripID"]);
				break;
			case 'invitationsent':
				fields = addNeedFields(fields, ['Id']);
				break;
			case 'tripstopschedule':
				if (fields.some(field => ["ETAInMinutes", "LastUpdatedOn", "OnTimeStatus", "ETATimeStamp", "ActualSequence", "ActualStopTime", "ActualAttendance"].includes(field)))
				{
					fields = addNeedFields(fields, ['TripStopId', 'TripId', 'AttendanceDate', 'OnTime', 'ActualStopTime']);
				}
				break;
			case 'tags':
				fields = addNeedFields(fields, ["Color", "Description"]);
				break;
			case 'studentattendanceschedule':
				fields = addNeedFields(fields, ["ActualTripHistoryId", "PlannedTripId", "ActualPUStopHistoryId", "ActualDOStopHistoryId", "PlannedPUStopHistoryId", "PlannedDOStopHistoryId", "ActualVehicleId", "School", "StudID"]);
				break;
			default:
				break;
		}

		return fields;
	};

	LightKendoGrid.prototype._selectedIdsChange = function()
	{
		var self = this;
		var idsHash = {};
		if (self.kendoGrid && self.kendoGrid.element)
		{
			var selected = $.map(self.kendoGrid.items(), function(item)
			{
				var row = $(item).closest("tr");
				var dataItem = self.kendoGrid.dataItem(row);
				var selectedId = Enumerable.From(self.getSelectedIds());

				if (dataItem && ($.isNumeric(dataItem[self.options.Id]) || self.options.idSeparator) && selectedId.Contains(dataItem[self.options.Id]))
				{
					return item;
				}
			});
			if (this.kendoGrid.options.selectable)
			{
				// only dispose grid own menu.
				if (tf.contextMenuManager.isGridMenu)
				{
					tf.contextMenuManager.dispose();
				}
				this.kendoGrid.clearSelection();
				this.kendoGrid.select(selected);
			}

			self._refreshGridBlank(true);

			var ids = self.getSelectedIds(),
				records = $.map(ids, function(item)
				{
					return self.kendoGrid.dataSource.get(item);
				});

			self.getSelectedRecords(records);
			self.onRowsChanged.notify(records);

			var allIds = self.obAllIds();
			if (allIds && allIds.length)
			{
				self.obSelectedIndex(allIds.indexOf(ids[0]));
			}
			else
			{
				self.obSelectedIndex(self.kendoGrid.dataSource.indexOf(records[0]));
			}
		}

		self.getSelectedIds().forEach(function(id)
		{
			idsHash[id] = id;
		});

		this.idsHash = idsHash;
		this._resetPageInfoSelect();
	};

	LightKendoGrid.prototype._omitIdsChange = function()
	{
		this._resetPageInfoSelect();
	};

	LightKendoGrid.prototype._resetPageInfo = function()
	{
		var self = this,
			result = self.result,
			total = result.TotalRecordCount;
		if (["tags", "gpsevent", "tripstop", "vehicle", "school", "busfinderhistoricalvehicle", "role", "staff", "locationevent", "recordcontact", "auditlog"].includes((self._gridType || "").toLowerCase()))
		{
			if (!isNaN(this.options.totalRecordCount))
			{
				total = this.options.totalRecordCount;
			} else if (result.FilteredRecordCount > result.TotalRecordCount)
			{
				total = result.FilteredRecordCount;
			}
		}
		var pageInfo = result.FilteredRecordCount + " of " + total;
		self.$container.children(".k-pager").find('.pageInfo').html(pageInfo);
	};

	LightKendoGrid.prototype._resetPageInfoSelect = function()
	{
		if (!this.options.showOmittedCount && !this.options.showSelectedCount && this.options.gridTypeInPageInfo === "") { return; }

		var pageInfoList = [];
		if (this.options.showSelectedCount && this.getSelectedIds().length > 0)
		{
			pageInfoList.push(this.getSelectedIds().length + " selected");
		}

		if (this.options.showOmittedCount)
		{
			var omittedRecordsCount;
			if (this.obFilteredExcludeAnyIds() != null)
			{
				omittedRecordsCount = this.obFilteredExcludeAnyIds().concat(this.obTempOmitExcludeAnyIds()).length;
			}
			else
			{
				omittedRecordsCount = this.obTempOmitExcludeAnyIds().length;
			}
			if (omittedRecordsCount > 0)
			{
				pageInfoList.push((omittedRecordsCount + " omitted"));
			}
		}
		var pageInfo = "";
		if (this.options.gridTypeInPageInfo)
		{
			pageInfo = "&nbsp;" + this.options.gridTypeInPageInfo;
		}
		if ((this.options.showSelectedCount || this.options.showOmittedCount) && pageInfoList.length > 0)
		{
			pageInfo = pageInfo + "&nbsp;(" + pageInfoList.join(", ") + ")";
		}

		this.$container.children(".k-pager").find(".pageInfoSelect")
			.html(pageInfo);
	};

	LightKendoGrid.prototype.columnResizeEvent = function(e)
	{
		if (this.options.onColumnResizeEvent)
		{
			this.options.onColumnResizeEvent(e);
		}
		else
		{
			this.invalidateFitContainer();
		}
	};

	LightKendoGrid.prototype.columnReorderEvent = function(e)
	{
		if (this.options.columnReorder)
		{
			this.options.columnReorder(e);
		}
	};

	LightKendoGrid.prototype.columnHideEvent = function(e)
	{
		this.invalidateFitContainer();
	};

	LightKendoGrid.prototype.columnShowEvent = function(e)
	{
		this.invalidateFitContainer();
	};

	LightKendoGrid.prototype.saveState = function()
	{
		const self = this;
		const isDashboardWidget = self.options.customGridType && self.options.customGridType.toLowerCase() == "dashboardwidget";
		const permanentLockCount = isDashboardWidget ? 0 : self.permanentLockCount();
		var columns = self.kendoGrid.getOptions().columns.slice(permanentLockCount);
		columns = Enumerable.From(columns).Where("$.field!='bulk_menu'").ToArray();
		self._obSelectedColumns(columns);
	};

	LightKendoGrid.prototype.getGridState = function()
	{
		return new TF.Grid.GridState({ gridFilterId: this.obSelectedGridFilterId(), filteredIds: this._filteredIds, filteredExcludeAnyIds: this.obFilteredExcludeAnyIds() });
	};

	/**
	 * Get the includeOnly filter id list.
	 * @return {Array}
	 */
	LightKendoGrid.prototype.getIncludeOnlyIds = function(isIncludeAddition = true)
	{
		var self = this,
			gridFilterIds = (self._gridState && self._gridState.filteredIds) ? self._gridState.filteredIds.slice() : null;

		if (isIncludeAddition)
		{
			if (self.additionalFilterIds && self.shouldIncludeAdditionFilterIds)
			{
				gridFilterIds = self.mergeTwoFilterIds(true, gridFilterIds, self.additionalFilterIds);
			}

			if (self.additionalGridFilterIds)
			{
				gridFilterIds = self.mergeTwoFilterIds(true, gridFilterIds, self.additionalGridFilterIds);
			}
		}

		return gridFilterIds;
	};

	LightKendoGrid.prototype.getExcludeAnyIds = function()
	{
		return this.obFilteredExcludeAnyIds() ? this.obFilteredExcludeAnyIds().concat(this.obTempOmitExcludeAnyIds()) : this.obTempOmitExcludeAnyIds();
	};

	/**
	 * Merge two filter id lists.
	 * @param {boolean} isInclude Whether they are includeOnly id lists.
	 * @param {Array} idList1 The first id list.
	 * @param {Array} idList2 The second id list.
	 * @return {Array} The merged id list.
	 */
	LightKendoGrid.prototype.mergeTwoFilterIds = function(isInclude, idList1, idList2)
	{
		// have not considered efficiency, could be optimized if got time.
		if (idList1 && idList2)
		{
			var result = [];
			if (isInclude)
			{
				idList1.forEach(id =>
				{
					if (idList2.indexOf(id) !== -1)
					{
						result.push(id);
					}
				});
			}
			else
			{
				var appendUniqueValue = function(id)
				{
					if (result.indexOf(id) === -1)
					{
						result.push(id);
					}
				};
				idList1.forEach(element =>
				{
					appendUniqueValue(element);
				});
				idList2.forEach(element =>
				{
					appendUniqueValue(element);
				});
			}
			return result;
		}
		else
		{
			return idList1 || idList2;
		}
	};

	LightKendoGrid.prototype.getSelectedIdsWithWarning = function()
	{
		var selectedIds = this.getSelectedIds();
		if (selectedIds.length === 0)
		{
			this.gridAlert.show({
				alert: this.gridAlert.alertOption.danger,
				message: "please select one or more items"
			});
		}
		return selectedIds;
	};

	LightKendoGrid.prototype.invertSelection = function()
	{
		var self = this;
		this.getIdsWithCurrentFiltering().then(function(data)
		{
			var ids = data;
			var selectedIds = ids.filter(function(id)
			{
				return self.idsHash[id] == null;
			});
			this.getSelectedIds([...selectedIds]);
		}.bind(this));
	};

	LightKendoGrid.prototype.omitSelection = function()
	{
		this.obTempOmitExcludeAnyIds(this.getSelectedIds().concat(this.obTempOmitExcludeAnyIds()));
		this.refresh();
	};

	LightKendoGrid.prototype.allSelection = function()
	{
		this.getIdsWithCurrentFiltering().then(function(data)
		{
			this.getSelectedIds([...data]);
		}.bind(this));
	};

	LightKendoGrid.prototype.onCtrlIPress = function(e, keyCombination)
	{
		e.preventDefault();
		var self = this;
		self.baseKeyPress();
		if (self.isDisableShortCutKey())
		{
			return;
		}
		self.invertSelection();
	};

	LightKendoGrid.prototype.onCtrlOPress = function(e, keyCombination)
	{
		e.stopPropagation();
		e.preventDefault(); // Prevent open new file by chrome
		var self = this;
		self.baseKeyPress();
		if (self.isDisableShortCutKey())
		{
			return;
		}
		setTimeout(function()
		{
			self.omitSelection();
		}, 0);
		setTimeout(function()
		{
			e.stopPropagation();
			e.preventDefault(); // Prevent open new file by chrome
		}, 1000);
	};

	LightKendoGrid.prototype.onCtrlAPress = function(e, keyCombination)
	{
		e.preventDefault();
		var self = this;
		self.baseKeyPress();
		if (self.isDisableShortCutKey() || self.options.isMiniGrid == true)
		{
			return;
		}
		// self._showCannotSupportSelectAllModal();
		self.allSelection();
	};

	/**
	 * Both all key press will be doing some thing on ViewFinder.
	 * @returns {void}
	 */
	LightKendoGrid.prototype.baseKeyPress = function()
	{
		//To do some thing on ViewFinder.
	};

	LightKendoGrid.prototype._showCannotSupportSelectAllModal = function()
	{
		// if (this.options.showCannotSupportSelectAllModal)
		// 	this.options.showCannotSupportSelectAllModal();
	};

	LightKendoGrid.prototype.onShiftDown = function(e, keyCombination)
	{
		if (this.kendoGrid.options.sortable.mode != "multiple")
		{
			this.kendoGrid.setOptions({ sortable: { mode: "multiple" } });
		}
	};

	LightKendoGrid.prototype.onShiftUp = function(e, keyCombination)
	{
		this.kendoGrid.setOptions({ sortable: { mode: "single" } });
	};

	LightKendoGrid.prototype.beforeSendFirstRequest = function()
	{
		var self = this;

		if (this.options.beforeSendFirstRequest)
		{
			return this.options.beforeSendFirstRequest.bind(this)()
				.then(function(result)
				{
					return Promise.resolve(result);
				});
		}

		return Promise.resolve(true);
	};

	LightKendoGrid.prototype._loadIdsWhenOnDataBound = function()
	{
		if (this.options.miniGridEditMode || (this.searchOption?.data?.idFilter?.IncludeOnly?.indexOf(-1) >= 0 && this.options.isAddMode))
		{
			return Promise.resolve();
		}
		return this.getIdsWithCurrentFiltering().then(
			function()
			{
				return true;
			},
			function()
			{
				// catch exception thrown by reject.
			}
		);
	}

	LightKendoGrid.prototype.onDataBound = function()
	{
		var self = this,
			kendoOptions = null;
		self.kendoGrid = self.$container.data("kendoGrid");
		if (self.kendoGrid === undefined)
		{
			return;
		}

		kendoOptions = self.kendoGrid.options;
		self.bindScrollXMoveSummayBarEvent();
		if (kendoOptions && kendoOptions.selectable && self.getSelectedIds().length > 0)
		{
			self._selectedIdsChange();
		}

		// remove all detail row event
		$(`tr.${SUB_ROW_CLASS}`).unbind().click(function(event)
		{
			event.stopPropagation();
			event.preventDefault();
		});

		var clickSelector = `.k-grid-content-locked table.k-selectable >tbody >tr:not('.${SUB_ROW_CLASS}'), .k-grid-content table.k-selectable >tbody >tr:not('.${SUB_ROW_CLASS}')`;
		if (TF.isMobileDevice)
		{
			var isClick = true;
			self.$container.on('touchstart', clickSelector, function(e)
			{
				isClick = true;
			});
			self.$container.on('touchmove', clickSelector, function(e)
			{
				isClick = false;
			});
			self.$container.on('touchend', clickSelector, function(e)
			{
				if (isClick)
				{
					onKendoGridTRClickEvent.call(this, e, self);
				}
			});
			if (this.options.supportMobileMultipleSelect)
			{
				self.$container.on('change', ">.k-grid-container>.k-grid-content-locked tr input[type='checkbox'].multi-selectable, >.k-grid-container>.k-grid-content>.k-virtual-scrollable-wrap>table>tbody>tr input[type='checkbox'].multi-selectable", function(e)
				{
					var currentElement = $(e.target),
						currentId = +currentElement.attr("value"),
						checked = currentElement.prop("checked"),
						existedIndex = self.getSelectedIds.indexOf(currentId);
					if (checked)
					{
						if (existedIndex === -1)
						{
							self.getSelectedIds.push(currentId);
						}
					}
					else
					{
						if (existedIndex > -1)
						{
							self.getSelectedIds.remove(currentId);
						}
					}
					return;
				});
			}
		} else
		{
			self.$container.off('click', clickSelector).on('click', clickSelector, function(e)
			{
				onKendoGridTRClickEvent.call(this, e, self);
			});
		}

		if (self.options.isDataRowHover)
		{
			switch (self.options.gridType)
			{
				case "student":
					self.$container.find(">.k-grid-container>.k-grid-content>div>table>tbody>tr").Popover(new TF.Popover.StudentPopoverViewModel(self.kendoGrid));
					break;
				case "tripstop":
					self.$container.find(">.k-grid-container>.k-grid-content>div>table>tbody>tr").Popover(new TF.Popover.TripStopPopoverViewModel(self.kendoGrid));
					break;
				case "trip":
					self.$container.find(">.k-grid-container>.k-grid-content>div>table>tbody>tr").Popover(new TF.Popover.TripPopoverViewModel(self.kendoGrid));
					break;
			}
		}

		if (self.options.onDataBound)
		{
			self.options.onDataBound();
		}

		if (kendoOptions && kendoOptions.selectable)
		{
			TF.LightKendoGridHelper._cancelKendoGridSelectedArea.bind(self)(self.kendoGrid);
		}

		if (this.fullfillGridBlankTimer)
		{
			clearTimeout(this.fullfillGridBlankTimer);
		}
		this.fullfillGridBlankTimer = setTimeout(() =>
		{
			this.fullfillGridBlankTimer = null;
			self._fullfillGridBlank.bind(self)();
		}, 200);

		self._initLinkTd();
		self._setCustomizetimePickerborderradius();
		self.kendoGridFilterHelper.setFilterDropDownListSize();

		if (!self.kendoGrid._data || self.kendoGrid._data.length === 0) //RW-850 Message should display when there are no associated documents
		{
			var $nomatching = self.$container.find(".no-matching-records");
			if ($nomatching.length === 0)
			{
				var $parent = self.$container.children(".k-grid-container").children(".k-grid-content").children(".k-virtual-scrollable-wrap");
				if (self.options.isMiniGrid && self.options.hasPermission === false && !self.options.miniGridEditMode)
				{
					$parent.append("<div class='col-md-20 no-matching-records'>You don't have permission to view data.</div>");
				}
				else if (!self.options.withoutData && !self.options.miniGridEditMode)
				{
					$parent.append("<div class='col-md-20 no-matching-records'>There are no matching records.</div>");
				}
				$parent.find("table").css("display", "none");
				$parent.find(".kendogrid-blank-fullfill").css("display", "none");
			}
		}
		else
		{
			var $nomatching = self.$container.find(".no-matching-records");
			if ($nomatching.length > 0)
			{
				$nomatching.remove();
			}
			var $parent = self.$container.children(".k-grid-container").children(".k-grid-content").children(".k-virtual-scrollable-wrap");
			$parent.find("table").css("display", "");
			$parent.find(".kendogrid-blank-fullfill").css("display", "");

			self.showLoadingForLazyLoad($parent);
		}

		//VIEW-1299 Grid columns do not move with grid header when tabbing through Quick Filter bar
		self.$container.children(".k-grid-header").find(".k-grid-header-wrap input,.k-grid-header-wrap span[tabindex]")
			.off('focus.autoScrollOnFocus').on('focus.autoScrollOnFocus', function(e)
			{
				var gridBody = self.$container.children(".k-grid-container").children(".k-grid-content").children(".k-virtual-scrollable-wrap");
				var $target = $(e.target).closest(".k-grid-header-wrap");
				if ($target.length > 0)
				{
					gridBody.scrollLeft($target.scrollLeft());
					if (self.$summaryContainer)
					{
						self.$summaryContainer.children(".k-grid-content").scrollLeft($target.scrollLeft());
					}
				}
			});
		//VIEW-1244 Cursor appears outside of inputs
		if (TF.isMobileDevice)
		{
			var header = self.$container.children(".k-grid-header").find(".k-grid-header-wrap");
			header.find("input:text").unbind('scroll.autoScrollOnFocus').bind('focus.autoScrollOnFocus', function(e)
			{
				var input = $(e.currentTarget),
					headerOffset = header.offset(),
					inputOffset = input.offset();

				if (headerOffset.left > inputOffset.left)
				{
					var scrollLeft = header.scrollLeft() - (headerOffset.left - inputOffset.left) - 32;
					self.$container.children(".k-grid-container").children(".k-grid-content").children(".k-virtual-scrollable-wrap").scrollLeft(scrollLeft);
					if (self.$summaryContainer)
					{
						self.$summaryContainer.find(".k-grid-content").scrollLeft(scrollLeft);
					}
				}
			});
		}

		//RW-997 once staff grid and the record without type dirver and bus aide, then no drag.
		if (self._gridType == "staff")
		{
			self._staffGridDraggable();
		}

		var showLoading = ((self.geoFields && self.geoFields.length > 0) || self._gridType === "trip")
			&& self.options.customGridType !== "dashboardwidget" && !self.options.isMiniGrid && !self.options.isSmallGrid && !this.updateLazyloadData
		if (showLoading)
		{
			tf.loadingIndicator.showImmediately();
		}

		var oldIds = self.obAllIds().slice(), newIds;
		self._loadIdsWhenOnDataBound()
			.then(function()
			{
				newIds = self.obAllIds().slice();
				let idChanged = oldIds.sort().join(',') !== newIds.sort().join(',') ||
					newIds.length === 0;
				if (idChanged)
				{
					self.onIdsChanged.notify();
				}

				self.onDataBoundEvent.notify(idChanged);
				showLoading && tf.loadingIndicator.tryHide();
			});

		self.speedUpHScroll();
		self.shortcutExtender = self.shortcutExtender || new TF.KendoGridNavigator({ grid: self.kendoGrid, tfGrid: self });

		if (typeof self.pendingTaskOnNextDataBound === "function")
		{
			self.pendingTaskOnNextDataBound();

			self.pendingTaskOnNextDataBound = null;
		}

		if (this.lazyloadFields.udf.length > 0)
		{
			self.lazyLoadUdf();
		}

		if (this.lazyloadFields.general.length > 0)
		{
			self.lazyLoadGeneral();
		}

		if (self.options.delayShortCutKeysBinding)
		{
			self.bindCtrlAAndCtrlI();
		}
	};

	LightKendoGrid.prototype.showLoadingForLazyLoad = function($o)
	{
		if (this.lazyloadFields.udf.length === 0 && this.lazyloadFields.general.length === 0 || this.kendoGrid.tbody == null)
		{
			return;
		}

		this.kendoGrid.items().each((index, item) =>
		{
			const row = $(item).closest('tr');
			this.lazyloadFields.udf.forEach(f =>
			{
				const $ele = row.find(`[data-kendo-field='${f.FieldName}']`);
				if ($ele.length > 0)
				{
					$ele.html('<span class="k-icon k-i-loading rollup-loading"></span>');
				}
			});

			this.lazyloadFields.general.forEach(f =>
			{
				const $ele = row.find(`[data-kendo-field='${f.FieldName}']`);
				if ($ele.length > 0)
				{
					$ele.html('<span class="k-icon k-i-loading lazy-loading"></span>');
				}
			});
		});
	}

	/**
	 * Run specified task on next data bound.
	 *
	 * @param {Function} task
	 */
	LightKendoGrid.prototype.runOnNextDataBound = function(task)
	{
		if (!!this.pendingTaskOnNextDataBound)
		{
			console.log("Pending task overriden.");
		}
		this.pendingTaskOnNextDataBound = task;
	}

	LightKendoGrid.prototype.setSelectedIndex = function(value)
	{
		var length = this.obFilteredRecordCount();
		if (!length) return;

		value = Math.max(Math.min(length - 1, value), 0);
		if (value == -1)
		{
			this.getSelectedIds([]);
			return;
		}

		var allIds = this.obAllIds(), id;
		if (allIds && allIds.length)
		{
			id = allIds[value];
		}
		else
		{
			id = this.kendoGrid.dataSource.at(value)[this.options.Id];
		}

		this.getSelectedIds(id == null ? [] : [id]);
	};

	LightKendoGrid.prototype.lazyLoadGrid = function(requestOption)
	{
		// if grid only include -1 as id and it is creating entity from detail view, means it doesn't need to send the request to get data.
		if (requestOption?.data?.idFilter?.IncludeOnly?.indexOf(-1) >= 0 && this.options.isAddMode)
		{
			return;
		}
		const Func = () => this._readGrid(requestOption);
		tf.helpers.kendoGridHelper.lazyLoadGrid(this.$container, this.lazyLoadScrollContainer, Func);
	}

	LightKendoGrid.prototype._readGrid = function(requestOption)
	{
		var self = this;

		tf.dataFormatHelper.clearPhoneNumberFormat(requestOption, self);

		typeof self.options.showLoadingBeforeSendRequest === "function" && self.options.showLoadingBeforeSendRequest();

		this.setLazyLoadFields(requestOption.data);
		this.updateLazyloadData = false;

		if (!self.checkFilterFieldsPermission(self._gridType, requestOption.data.filterClause, requestOption.data.filterSet))
		{
			self.resetFilterForCurrentInvalidFilter();
			typeof self.options.hideLoadingAfterSendRequest === "function" && self.options.hideLoadingAfterSendRequest(true);
			self.options.customGridType === "dashboardwidget" && self.showInvalidFilterMessage();

			return Promise.resolve(true);
		}

		var promise;
		if (self.firstRequestPromise)
		{
			promise = self.firstRequestPromise.then(result =>
			{
				requestOption.success(result);
			}).catch(result =>
			{
				requestOption.error(result);
			}).finally(() =>
			{
				self.firstRequestPromise = null;
			});
		}
		else
		{
			promise = self.fetchData(requestOption);
		}

		return promise
			.then(function()
			{
				self.gridSuccessResponse = true;
				//the count of request callback in the process of change filter
				if (!self.kendoDataSourceTransportRequestBackCount) self.kendoDataSourceTransportRequestBackCount = 0;
				self.kendoDataSourceTransportRequestBackCount = self.kendoDataSourceTransportRequestBackCount + 1;
				//check the filter Whether custom or not
				if (self.kendoGridFilterHelper.$customFilterBtn)
				{
					//when user change the custom filter, show the custom filter menu now
					//check the last request callback, if this back is the last request callback, show the custom filter menu and reset data
					if (self.kendoDataSourceTransportRequestBackCount == self.kendoDataSourceTransportReadCount)
					{
						self.kendoGridFilterHelper.$customFilterBtn.click();
						self.kendoDataSourceTransportRequestBackCount = 0;
						self.kendoDataSourceTransportReadCount = 0;
						self.kendoGridFilterHelper.$customFilterBtn = undefined;
					}
				}
				else
				{
					//reset the request count, request callback count and custom button
					self.kendoDataSourceTransportRequestBackCount = 0;
					self.kendoDataSourceTransportReadCount = 0;
					self.kendoGridFilterHelper.$customFilterBtn = undefined;
				}

				if (self.addHotLinkTimer)
				{
					clearTimeout(self.addHotLinkTimer);
				}
				self.addHotLinkTimer = setTimeout(() =>
				{
					self.addHotLinkTimer = null;
					self.addHotLink();
				}, 200);

				typeof self.options.hideLoadingAfterSendRequest === "function" && self.options.hideLoadingAfterSendRequest();
			}).catch(function(ex)
			{
				if (self.overlay && self.options.showOverlay)
				{
					tf.loadingIndicator.tryHide();
				}
				typeof self.options.hideLoadingAfterSendRequest === "function" && self.options.hideLoadingAfterSendRequest(true);
				if ([400, 412].includes(ex.StatusCode))
				{
					self.resetFilterForCurrentInvalidFilter(ex.Message);
				}
				else if (ex.StatusCode == 408) //display Timeout message
				{
					tf.promiseBootbox.alert("Timeout expired, please try again.");
				}
				else if (ex.StatusCode === 403 && self instanceof TF.Grid.KendoGrid)
				{
					tf.promiseBootbox.alert("The roles of this user doesn't have the right to access this grid.").then(() =>
					{
						tf.documentManagerViewModel.closeDocument(tf.documentManagerViewModel.obCurrentDocument());
					});
				}
			});
	};

	LightKendoGrid.prototype.checkFilterFieldsPermission = function(gridType, filterClause, filterSet)
	{
		let hasPermission = true;
		if (gridType === "tripstopschedule" && !tf.helpers.accessHelper.hasETAPermission())
		{
			let columns = tf.tripStopScheduleGridDefinition.getCheckPermissionColumns();

			function getFieldsFromFilterSet(filterSet)
			{
				let filterItems = filterSet.FilterItems || [];

				(filterSet.FilterSets || []).forEach(fs =>
				{
					filterItems = filterItems.concat(getFieldsFromFilterSet(fs));
				})

				return filterItems;
			}

			let filterFields = getFieldsFromFilterSet(filterSet || {}).map(f => f.FieldName.toLowerCase());

			columns.forEach(column =>
			{
				if (filterFields.includes(column.toLowerCase()) || (filterClause || "").toLowerCase().indexOf(column.toLowerCase()) >= 0)
				{
					hasPermission = false;
				}
			});
		}

		return hasPermission;
	}

	LightKendoGrid.prototype.showInvalidFilterMessage = function()
	{
		let messageElement = "<div class='tip-message'>The applied quick filter is invalid.</div>";
		this.kendoGrid.element.find(".k-virtual-scrollable-wrap").append(messageElement);
	}

	LightKendoGrid.prototype.setLazyLoadFields = function(data)
	{
		this.lazyloadFields = { general: [], udf: [] };
		this.setLazyLoadGeneralFields(data);
		this.setLazyLoadUdfFields(data);
	}

	LightKendoGrid.prototype.setLazyLoadGeneralFields = function(data)
	{
		const currentColumns = [].concat(this._obSelectedColumns()).concat(this.getKendoColumn());
		let fields = _.uniqBy(currentColumns.filter(c => c.lazyLoad), "FieldName");
		if (!fields.length)
		{
			return false;
		}

		fields = fields.filter(x => data.filterClause.indexOf(x.FieldName) < 0);
		if (!fields.length)
		{
			return false;
		}

		if (data.filterSet)
		{
			fields = fields.filter(x => !data.filterSet.FilterItems.some(y => y.FieldName === x.FieldName));
		}

		if (!fields.length)
		{
			return false;
		}

		if (data.sortItems)
		{
			fields = fields.filter(x => !data.sortItems.some(y => y.Name === x.FieldName));
		}

		if (!fields.length)
		{
			return false;
		}

		this.lazyloadFields.general = fields;
		data.fields = (data.fields || []).filter(x => !fields.some(y => y.field === x));
		return true;
	}

	LightKendoGrid.prototype.setLazyLoadUdfFields = function(data)
	{
		let fields = (this.getUdfs() || []).filter(x => udfLazyLoadType.includes(x.UDFType));
		if (fields.length === 0)
		{
			return false;
		}

		fields = fields.filter(x => data.filterClause.indexOf(x.OriginalName) < 0);
		if (fields.length === 0)
		{
			return false;
		}

		if (data.filterSet)
		{
			fields = fields.filter(x => !data.filterSet.FilterItems.some(y => y.FieldName === x.OriginalName));
		}

		if (fields.length === 0)
		{
			return false;
		}

		if (data.sortItems)
		{
			fields = fields.filter(x => !data.sortItems.some(y => y.Name === x.OriginalName));
		}

		if (fields.length === 0)
		{
			return false;
		}

		this.lazyloadFields.udf = fields;
		if (data.fields)
		{
			data.fields = data.fields.filter(x => !fields.some(y => y.OriginalName === x));
		}
		return true;
	}

	LightKendoGrid.prototype.lazyLoadUdf = async function()
	{
		let entityIds = this.kendoGrid.dataSource.data().map(x => x.Id);
		const rollupUdfIds = this.lazyloadFields.udf.map(x => x.UDFId);
		let rollupResult, shouldApplyThematic = true;
		if (this.alreadyLoadId && this.alreadyLoadId.udf && Object.keys(this.alreadyLoadId.udf).length > 0)
		{
			entityIds = entityIds.filter(id =>
			{
				if (this.alreadyLoadId.udf[id])
				{
					return rollupUdfIds.some(x => !Object.keys(this.alreadyLoadId.udf[id]).includes(x.toString()));
				}
				return id;

			});
		}

		if (!this.hasRollUpFieldsInThematicConfigs())
		{
			shouldApplyThematic = false;
			this.applyGridThematicConfigs && this.applyGridThematicConfigs();
		}

		if (entityIds.length > 0)
		{
			// update the data source id when from the map viewer layer call out
			let dbid = tf.datasourceManager.databaseId;
			if (tf.helpers.kendoGridHelper.isDetailView(this.lazyLoadScrollContainer))
			{
				dbid = tf.helpers.detailViewHelper.getDataSourceId(this.lazyLoadScrollContainer);
			}
			const res = await tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "rollupudfs"), {
				data: {
					dataSourceId: dbid,
					udfIds: rollupUdfIds,
					entityIds: entityIds
				}
			}, { overlay: false });
			rollupResult = res && res.Items && res.Items[0];
			this.alreadyLoadId.udf = {
				...this.alreadyLoadId.udf,
				...rollupResult
			};
		}
		if ((!this.alreadyLoadId || !Object.keys(this.alreadyLoadId.udf).length) && shouldApplyThematic)
		{
			this.applyGridThematicConfigs && this.applyGridThematicConfigs();
			return;
		}

		this.updateLazyloadData = true;
		this.updateGridForUdfLazyLoad();
		if (shouldApplyThematic)
		{
			this.applyGridThematicConfigs && this.applyGridThematicConfigs();
		}
	}

	LightKendoGrid.prototype.lazyLoadGeneral = async function()
	{
		if (this.lazyloadFields.general.length === 0)
		{
			return;
		}

		let entityIds = this.kendoGrid.dataSource.data().map(x => x.Id);
		if (this.alreadyLoadId && this.alreadyLoadId.general)
		{
			entityIds = entityIds.filter(id =>
			{
				return !this.alreadyLoadId.general[id];
			});
		}

		if (!entityIds.length)
		{
			this.updateLazyloadData = true;
			this.updateGridForGeneralLazyLoad();
			return;
		}

		const chunkSize = 20;
		for (let i = 0; i < entityIds.length; i += chunkSize)
		{
			const chunk = entityIds.slice(i, i + chunkSize);
			if (chunk.length > 0)
			{
				const res = await tf.promiseAjax.post(pathCombine(this.getApiRequestURL(this.options.url)), {
					data: {
						"idFilter": {
							"IncludeOnly": chunk,
							"ExcludeAny": [],
						},
						"fields": ["Id", ...this.lazyloadFields.general.map(r => r.FieldName)],
					}
				}, { overlay: false });
				let result = res && res.Items;
				result.forEach(r =>
				{
					this.alreadyLoadId.general[r.Id] = r;
				});
			}
			if (!this.alreadyLoadId || !Object.keys(this.alreadyLoadId.general).length)
			{
				return;
			}

			this.updateLazyloadData = true;
			this.updateGridForGeneralLazyLoad();
		}
	}

	LightKendoGrid.prototype.updateGridForUdfLazyLoad = function()
	{
		if (this.kendoGrid.tbody == null)
		{
			return;
		}
		this.kendoGrid.items().each((index, item) =>
		{
			const row = $(item).closest('tr');
			const dataItem = this.kendoGrid.dataItem(row);
			this.lazyloadFields.udf.forEach((udf) =>
			{
				const value = this.alreadyLoadId.udf[dataItem.Id] && this.alreadyLoadId.udf[dataItem.Id][udf.UDFId];
				const $ele = row.find(`[data-kendo-field='${udf.FieldName}']`);
				if ($ele.length > 0)
				{
					var formattedValue = this.udfFormatValue(udf, value);
					$ele.html(formattedValue);
					dataItem[udf.DisplayName] = formattedValue;
				}
			});
		});
	}

	LightKendoGrid.prototype.updateGridForGeneralLazyLoad = function()
	{
		if (this.kendoGrid?.tbody == null)
		{
			return;
		}
		this.kendoGrid.items().each((index, rowItem) =>
		{
			const row = $(rowItem).closest('tr');
			const dataItem = this.kendoGrid.dataItem(row);
			const item = this.alreadyLoadId.general[dataItem.Id];
			if (!item)
			{
				return;
			}

			this.lazyloadFields.general.forEach((f) =>
			{
				const $ele = row.find(`[data-kendo-field='${f.FieldName}']`);
				const fieldVal = item[f.FieldName];
				if ($ele.length > 0)
				{
					var display = fieldVal;
					if (kendo && f.template)
					{
						display = f.template(item);
					}
					else if (kendo && f.format)
					{
						display = kendo.toString(fieldVal, kendo._extractFormat(f.format));
					}

					$ele.html(display);
				}
			});
		});
	}

	LightKendoGrid.prototype.udfFormatValue = function(udf, value)
	{
		if (value === null || value === undefined)
		{
			return value;
		}

		const _udf = Enumerable.From(this.kendoGrid.columns).FirstOrDefault(null, function(x)
		{
			return x.UDFId === udf.UDFId
		});
		if (_udf && _udf.format)
		{
			switch (udf.type)
			{
				case 'time':
					value = new Date(`${moment(new Date()).format(dataFormat)} ${value}`);
					break;
				case 'datetime':
				case 'date':
					value = new Date(value);
					break;
				default:
					break;
			}
			value = kendo.format(_udf.format, value);
		}
		return value;
	}

	LightKendoGrid.prototype._onGridItemClick = function(dataItem, e)
	{
		let currentId = dataItem ? dataItem[this.options.Id] : null;
		if (!$.isNumeric(currentId) && !this.options.idSeparator)
		{
			this._resetPageInfoSelect();
			return;
		}

		if (TF.isMobileDevice && this.options.supportMobileMultipleSelect)
		{
			var currentElement = $(e.target);
			if (currentElement.attr("type") === "checkbox" && currentElement.hasClass("multi-selectable"))
			{
				return;
			}
			if (currentElement.is("label.chk_label"))
			{
				return;
			}
		}

		if (this.options.selectable == "row")
		{
			this.getSelectedIds([currentId]);
			return;
		}

		const isMacCMDkey = this.isMacOs && e.metaKey;
		if ((e.ctrlKey || isMacCMDkey) && this.kendoGrid.options.selectable != "row")
		{
			if (Array.contain(this.getSelectedIds(), currentId))
			{
				this.getSelectedIds.remove(function(id) { return id === currentId; });
			}
			else
			{
				this.getSelectedIds.push(currentId);
			}

			return;
		}

		if (!e.shiftKey || this.kendoGrid.options.selectable == "row")
		{
			this.getSelectedIds([currentId]);
			return;
		}

		var data = this.obAllIds();
		if (!data || !data.length)
		{
			var selectedData = this.kendoGrid.getSelectedData();
			if (selectedData.length > 0)
			{
				this.getSelectedIds(selectedData.map(s => this.kendoGrid.dataSource.getByUid(s.uid).Id));
			}

			return;
		}

		let selectedIndex = this.obSelectedIndex() == -1 ? 0 : this.obSelectedIndex(),
			currentIndex = data.indexOf(currentId),
			newSelectedIds = [data[selectedIndex]], start, end;
		if (selectedIndex > currentIndex)
		{
			start = currentIndex;
			end = selectedIndex;
		}
		else
		{
			start = selectedIndex + 1;
			end = currentIndex + 1;
		}

		if (this.kendoGrid.virtualScroll?.rows)
		{
			newSelectedIds = newSelectedIds.concat(data.slice(start, end));
			this.getSelectedIds(newSelectedIds);
			return;
		}

		for (var index = start; index < end; index++)
		{
			let row = this.kendoGrid.table.find("tr:eq(" + index + ")");
			if (this._isRowSelectable(row))
			{
				var dataItem = this.kendoGrid.dataItem(row);
				newSelectedIds.push(dataItem.Id);
			}
		}

		this.getSelectedIds(newSelectedIds);
	};

	LightKendoGrid.prototype._fullfillGridBlank = function()
	{
		const self = this;
		var $canver = this.$container.children(".k-grid-container").children(".k-grid-content").children('.k-virtual-scrollable-wrap');
		$canver.children('.kendogrid-blank-fullfill').remove();

		var $blankFiller = $('<table class="kendogrid-blank-fullfill"><tbody></tbody></table>');
		var $trs = $canver.children('table').children('tbody').children('tr:visible');
		let strHtml = '';
		$trs.map(function(idx, tr)
		{
			const $tr = $(tr);
			const uid = $tr.data('kendo-uid') || "";
			const fillItemColor = self._getFillItemColor($tr);
			const fillItemHeight = self._caculateFillItemHeight($tr);
			let classList = [
				"fillItem",
				$tr.hasClass("k-alt") ? 'l-alt' : 'l',
				$tr.hasClass(MAIN_ROW_CLASS) ? MAIN_ROW_CLASS : SUB_ROW_CLASS,
			];

			if ($tr.hasClass(SELECTED_ROW_CLASS))
			{
				classList.push(SELECTED_ROW_CLASS);
			}

			strHtml += `<tr data-id="${uid}" class="${classList.join(" ")}"
				style="height:${fillItemHeight}px;background-color:${fillItemColor};">
				<td></td></tr>`;
		});

		$blankFiller.find("tbody").append(strHtml);
		$blankFiller.find(`tr.fillItem.${MAIN_ROW_CLASS}`)
			.dblclick(function(e)
			{
				self.onGridDoubleClick(e);
			})
			.click(function(e)
			{
				var uid = $(this).data('id');
				var dataItem = self.kendoGrid.dataSource.getByUid(uid);
				self._onGridItemClick(dataItem, e, true);
			});

		$canver.prepend($blankFiller).children('table.k-grid-table').addClass('table-blank-fullfill');
	};

	LightKendoGrid.prototype.adoptRowStyle = function($tr, $fill)
	{
		const bkgColor = this._getFillItemColor($tr);
		const rowHeight = this._caculateFillItemHeight($tr);

		$fill.css({ 'background-color': bkgColor, 'height': rowHeight });
	}

	LightKendoGrid.prototype.adoptRowSelection = function($tr, $fill)
	{
		const isSelected = $tr.hasClass(SELECTED_ROW_CLASS);
		const curIsSelected = $fill.hasClass(SELECTED_ROW_CLASS);
		if (isSelected !== curIsSelected)
		{
			$fill.toggleClass(SELECTED_ROW_CLASS, isSelected);
		}
	}

	LightKendoGrid.prototype._refreshGridBlank = function(onlyForSelection)
	{
		const self = this;
		var $content = self.$container.children(".k-grid-container").children(".k-grid-content").children('.k-virtual-scrollable-wrap');
		var $blankFiller = $content.children('.kendogrid-blank-fullfill');
		var $trs = $content.children('table.k-grid-table').find(">tbody>tr.k-master-row");
		var $fillItems = $blankFiller.find('tbody>tr.fillItem.k-master-row');

		$trs.map(function(idx, tr)
		{
			const $fillItem = $fillItems.eq(idx);
			const $tr = $(tr);

			if (!onlyForSelection)
			{
				self.adoptRowStyle($tr, $fillItem);
			}

			self.adoptRowSelection($tr, $fillItem);
		});
	};

	LightKendoGrid.prototype._caculateFillItemHeight = function($tr)
	{
		if (!$tr.is(":visible"))
		{
			return 0;
		}

		return $tr.find('td').eq(0).outerHeight();
	};

	LightKendoGrid.prototype._getFillItemColor = function($tr)
	{
		let color = $tr.attr("custom-bkg-color");
		if (!color)
		{
			color = $tr.hasClass("k-alt") ? GRID_ROW_COLOR.EVEN : GRID_ROW_COLOR.ODD;
		}

		return color;
	};

	LightKendoGrid.prototype.clearSelection = function()
	{
		this.getSelectedIds([]);
	};

	LightKendoGrid.prototype.extendAdditionGridDefinition = function(gridDefinition, additionGridDefinition)
	{
		for (var i = 0; i < gridDefinition.Columns.length; i++)
		{
			var column = gridDefinition.Columns[i],
				additionColumn = Enumerable.From(additionGridDefinition.Columns).
					Where(function(x) { return x.FieldName === column.FieldName }).SingleOrDefault();
			column = $.extend(column, additionColumn);
			if (!this.options.isMiniGrid)
			{
				this.gridDefinitionHelper.updateGridDefinitionWidth(column);
			}

			this.gridDefinitionHelper.updateGridDefinitionDisplayNameFromTerm(column);
		}

		return gridDefinition;
	};

	LightKendoGrid.prototype.resizeHeightOnWindowResize = function()
	{
		var self = this;
		self._onWindowResize = function()
		{
			self.invalidateFitContainer();
		};

		// VIEW-7199: When resizing the browser, the record count cannot be displayed.
		// Originally, only the main grid used lightkendoGrid. However the detailView mini-grid also uses lightkendoGrid now.
		// This means that if you open a student grid with a detailView that includes a document mini-grid,
		// it will bind ResizeEvent twice. 1st time, bind the studentGridResizeEvent. After that it would unbind the studentGridResizeEvent and bind the documentGridResizeEvent.
		// In Plus, the studentGridResizeEvent is triggered by other way. So only add code to handle the case in viewfinder.
		const toBindResizeEvent = !tf.isViewfinder || (tf.pageManager.getPageId(this._gridType) == tf.pageManager.pageType());
		if (toBindResizeEvent)
		{
			TF.LightKendoGridHelper.rebindResizeEvent(self._onWindowResize);
		}
	};

	LightKendoGrid.prototype.invalidateFitContainer = function()
	{
		if (this.fitContainerTimer != null)
		{
			clearTimeout(this.fitContainerTimer);
		}

		this.fitContainerTimer = setTimeout(function()
		{
			this.fitContainer();
			this.fitContainerTimer = null;
		}.bind(this), 50);
	};

	LightKendoGrid.prototype.fitContainer = function()
	{
		var self = this, height = self.getGridFullHeight(), pagerHeight = 0;
		if (self.options.isSmallGrid)
		{
			return;
		}
		if (!self.options.kendoGridOption.pageable && self.$container.children(".k-pager.k-grid-pager").length == 1)
		{
			// some grid don't have pager, need set pager height in content height either.
			pagerHeight = self.$container.children(".k-pager.k-grid-pager").outerHeight();
		}

		var contentHeight = height - self.$container.children(".k-grid-header").outerHeight() - pagerHeight,
			$content = self.$container.children(".k-grid-container").children(".k-grid-content"),
			$lockedContent = self.$container.children(".k-grid-container").children(".k-grid-content-locked"),
			$table = $content.find('table');

		// In the dashboard grid, not need to reset the content height. The dashboard has its own calculations function
		if (self.options.customGridType !== "dashboardwidget")
		{
			self.$container.height(height);
			$content.height(contentHeight);
			self.$container.next(".kendo-summarygrid-container").find(".k-grid-content-locked,.k-grid-content").height(self.summaryHeight);
		}

		if (self.kendoGrid && self.kendoGrid.virtualScrollable)
		{
			self.kendoGrid._rowHeight = null;
			self.kendoGrid.virtualScrollable.repaintScrollbar();
		}

		self.resetGridContainerHorizontalLayout();
		self.changeLockedColumnHeight();
		self._refreshGridBlank();
	};

	/** */
	LightKendoGrid.prototype.resetGridContainerHorizontalLayout = function()
	{
		var self = this,
			$summaryGrid = self.$container.next(),
			warpWidth = self.$container.width(),
			lockHeaderWidth = self.$container.children(".k-grid-header").children('.k-grid-header-locked').width(),
			remainedWidth = warpWidth - lockHeaderWidth,
			paddingRight = parseInt(self.$container.children(".k-grid-container").children(".k-grid-content").css("padding-right"));

		self.$container.children(".k-grid-container").children(".k-grid-content").css("width", remainedWidth - paddingRight);
		self.$container.children(".k-grid-header").children(".k-auto-scrollable").css("width", remainedWidth - paddingRight);
		$summaryGrid.find(".k-grid-content").css("width", remainedWidth - paddingRight);
		$summaryGrid.find(".k-auto-scrollable").css("width", remainedWidth - paddingRight);
	};

	LightKendoGrid.prototype.changeLockedColumnHeight = function()
	{
		let $lockedContent = this.$container.find(".k-grid-content-locked"),
			$gridContent = this.$container.find('.k-virtual-scrollable-wrap:first'),
			$content = this.$container.children(".k-grid-container").children(".k-grid-content"),
			$contentWidth = ($content.length > 0 && $content[0].clientWidth) || 0,
			$table = this.$container.find('.k-auto-scrollable > table'),
			hasScroll = $table.length > 0 && $table[0].offsetWidth > $contentWidth;

		if ($lockedContent.find('.k-i-collapse').length === 0 && !hasScroll)
		{
			$lockedContent.height($gridContent.height());
		}
		else
		{
			$lockedContent.height($gridContent[0].clientHeight);
		}
		const rightScroll = this.$container.find(">.k-auto-scrollable >.k-scrollbar-vertical"),
			scrollTop = rightScroll.scrollTop(),
			scrollHeight = rightScroll.length > 0 && rightScroll[0].scrollHeight,
			lockedContentScroll = $lockedContent.scrollTop();
		if (scrollTop !== lockedContentScroll && (scrollTop + rightScroll.height()) === scrollHeight)
		{
			$lockedContent.scrollTop(scrollTop);
		}
	}

	LightKendoGrid.prototype.getGridFullHeight = function()
	{
		var height = 0;
		if (this.options.height !== "auto")
		{
			height = this.options.height;
		}
		else
		{
			height = this.$container.parent().height();//  $("body").height() - 172;

			if (this.options.containerHeight !== undefined)
			{//used in view page grids
				height = this.options.containerHeight;
			}
		}

		if (this.obSummaryGridVisible && this.obSummaryGridVisible())
		{
			height = height - this.summaryHeight;
		}
		return height;
	};

	LightKendoGrid.prototype.bindScrollXMoveSummayBarEvent = function()
	{
		if (!isMobileDevice())
		{
			return;
		}
		var self = this,
			timeoutEvent,
			gridContent = this.$container.children(".k-grid-container").children(".k-grid-content").children(".k-virtual-scrollable-wrap").length > 0 ? this.$container.children(".k-grid-container").children(".k-grid-content").children(".k-virtual-scrollable-wrap") : this.$container.children(".k-grid-container").children(".k-grid-content");
		this.$container.children(".k-grid-container").children(".k-grid-content").addClass("overflow-hidden");
		if (self.$summaryContainer)
		{
			self.$summaryContainer.children(".k-grid-content").off("scroll.summarybar");
		}
		gridContent.bind("touchmove", function(e)
		{
			//e.preventDefault();
			if (self.$summaryContainer)
			{
				var $summaryGrid = self.$summaryContainer.find(".k-grid-content");
				$summaryGrid.scrollLeft(gridContent.scrollLeft());
				clearTimeout(timeoutEvent);
				timeoutEvent = setTimeout(function()
				{
					$summaryGrid.scrollLeft(gridContent.scrollLeft());
				}, 50);
			}
		});
	};

	LightKendoGrid.prototype.convertRequest2KendoFilterSet = function(filterSet)
	{
		var kendoFilterSet = [];
		if (!filterSet)
			return kendoFilterSet;

		var self = this;

		kendoFilterSet = self._convertToKendoFilterItems(filterSet.FilterItems);
		kendoFilterSet = kendoFilterSet.concat(self._convertToKendoFilterSets(filterSet.FilterSets));

		return kendoFilterSet;
	};

	// Kendogrid's filterSet data struct is different api request's filterSet, need covert here
	LightKendoGrid.prototype._convertToKendoFilterSets = function(filterSets)
	{
		var self = this;
		if (!filterSets || filterSets.length === 0)
			return [];

		return filterSets.map(function(filterSet)
		{
			var tmp = {
				logic: filterSet.LogicalOperator,
				filters: self._convertToKendoFilterItems(filterSet.FilterItems)
			};

			// tmp.filters[0].operator = 'custom';
			return tmp;
		});
	};

	LightKendoGrid.prototype._convertToKendoFilterItems = function(filterItems)
	{
		if (!filterItems || filterItems.length === 0)
		{
			return [];
		}

		var columns = this.kendoGrid?.columns || this.getKendoColumn();
		return filterItems.map((filterItem) =>
		{
			const match = columns.find(c => c.FieldName === filterItem.FieldName);
			if (match)
			{
				switch (match.type)
				{
					case "date":
						if (filterItem.ExactHint == "utc")
						{
							filterItem.value = moment(filterItem.Value).add(tf.clientTimeZone.HoursDiff, 'hours').format("YYYY-MM-DDTHH:mm:ss")
						}
						break;
					case "time":
						filterItem.Value = moment(filterItem.Value).format('h:mm A');
						break;
					case "boolean":
						// If there is positive value and matches with filter value, we should check the value by filter operator.
						if (match.hasOwnProperty("positiveValue") && filterItem.Value === match.positiveValue)
						{
							filterItem.Value = filterItem.Operator === TF.Grid.LightKendoGrid.prototype.operatorKendoMapTF['eq'];;
						}
						break;
					default:
						break;
				}
			}

			var kendoFilterItem = {
				operator: this.operatorKendoMapTF.reverseKeyValue()[filterItem.Operator],
				field: filterItem.FieldName,
				value: filterItem.Value
			};
			kendoFilterItem = $.extend(kendoFilterItem, filterItem);
			return kendoFilterItem;
		});
	};

	LightKendoGrid.prototype._staffGridDraggable = function()
	{//RW-997 once staff grid and the record without type dirver and bus aide, then no drag.
		//self.kendoGrid = this.$container.data("kendoGrid");
		//self.bindScrollXMoveSummayBarEvent();
		var self = this;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "stafftypes"))
			.then(function(staffTypes)
			{
				var BusAideId = 0, DriverId = 0;
				staffTypes.Items.forEach(function(staffType)
				{
					if (staffType.StaffTypeName == "Bus Aide")
					{
						BusAideId = staffType.StaffTypeId;
					}

					if (staffType.StaffTypeName == "Driver")
					{
						DriverId = staffType.StaffTypeId;
					}
				});

				if (!self.kendoGrid)
				{
					return;
				}

				$.map(self.kendoGrid.items(), function(item)
				{
					var row = $(item).closest("tr");
					var dataItem = self.kendoGrid.dataItem(row);
					const containsBusOrDriver = dataItem.StaffTypes && $.type(dataItem.StaffTypes) === 'string' && dataItem.StaffTypes.split(",").some(function(staffType)
					{
						return staffType == BusAideId || staffType == DriverId;
					});

					if (containsBusOrDriver)
					{
						row.addClass("draggable");
					}
				});
			});
	};

	/**
	 * Scroll to a row by id.
	 *
	 * @param {number} id
	 */
	LightKendoGrid.prototype.scrollToRowById = function(id)
	{
		var self = this,
			index = self.obAllIds().indexOf(id);

		if (index > -1)
		{
			var $scroll = self.$container.children(".k-grid-container").children(".k-grid-content").children(".k-scrollbar.k-scrollbar-vertical"),
				containerHeight = $scroll.height(),
				totalHeight = $scroll.contents().height(),
				unitHeight = self.$container.children(".k-grid-container").children(".k-grid-content").find(".k-virtual-scrollable-wrap .fillItem").height(),
				unitHeightNofillItem = self.$container.children(".k-grid-container").children(".k-grid-content").find(`.k-virtual-scrollable-wrap .k-master-row`).height(),
				scrollTop = (unitHeight ? unitHeight : unitHeightNofillItem) * index,
				lastPageTop = totalHeight - containerHeight;

			scrollTop = Math.min(scrollTop, lastPageTop);
			$scroll.scrollTop(scrollTop);
		}
	};

	LightKendoGrid.prototype.createDragDelete = function()
	{
		var that = this,
			deleteColumn = function(e)
			{
				$(window).off("mouseup");
				$("body").off("mousemove");
				var $dragEle = e.draggable.hint;
				if (!$dragEle.hasClass("dragIn")) return;
				$dragEle.remove();
				var childColumns = that._obSelectedColumns().filter(function(column)
				{
					return column.ParentField;
				});
				if (that._obSelectedColumns().length <= 1 || (that._obSelectedColumns().length === 2 && childColumns && childColumns.length > 0))
				{
					return that.gridAlert.show(
						{
							alert: "Warning",
							title: "Warning",
							message: "There should be at least one non locked column",
							key: that._columnsUnLockedTimesKey
						});
				}

				if (that._removingOnlyOneUnLockColumn && that._removingOnlyOneUnLockColumn(e.draggable.hint.text()))
				{
					return;
				}
				//All visible columns to the left of this column are locked. Removing this column will unlock those columns. Are you sure you want to remove this column?
				return tf.promiseBootbox.confirm(
					{
						message: "Are you sure you want to remove the column?",
						title: "Remove Confirmation"
					})
					.then(function(result)
					{
						if (result)
						{
							var name = e.draggable.hint.text();
							if (that.options.onDragRemoveColumn)
							{
								that.options.onDragRemoveColumn(name);
							} else
							{
								that._removeColumn(name);
								that.rebuildGrid();
							}
						}
					}.bind(that));
			};

		var docRoot = $("body");
		docRoot.kendoDropTarget(
			{
				group: that.kendoGrid._draggableInstance.options.group,
				dragenter: function(e)
				{
					var $fromEle = e.draggable.currentTarget;
					var $dragEle = e.draggable.hint;
					$(window).off(".removecolumn");
					$("body").on(this.isMobileDevice ? "touchmove.removecolumn" : "mousemove.removecolumn", function()
					{
						var grid = $fromEle.closest(".kendo-grid"), dragOffset = 40,
							fromEleTop = $fromEle.offset().top,
							dragEleTop = $dragEle.offset().top,
							dragEleLeft = $dragEle.offset().left;
						if (grid && (fromEleTop > dragEleTop + dragOffset || fromEleTop < dragEleTop - dragOffset ||
							grid.offset().left - dragOffset > dragEleLeft || grid.offset().left + grid.outerWidth() + dragOffset < dragEleLeft))
						{
							$dragEle.addClass("dragIn");
						}
						else
						{
							$dragEle.removeClass("dragIn");
						}
					});
				}.bind(this),
				dragleave: function(e)
				{
					var $dragEle = e.draggable.hint;
					if (e.pageX < 0 || e.pageX >= $(window).width() || e.pageY < 0 || e.pageY >= $(window).height())
					{
						$(window).off(".removecolumn").on(this.isMobileDevice ? "touchend.removecolumn" : "mouseup.removecolumn", function()
						{
							$("body").off(".removecolumn");
							$(window).off(".removecolumn");
							deleteColumn(e);
							$dragEle.remove();
						}.bind(this));
						return;
					}
					$("body").off(".removecolumn");
					$dragEle.removeClass("dragIn");
				}.bind(this),
				drop: function(e)
				{
					deleteColumn(e);
					e.draggable.hint.remove();
					$(window).off(".removecolumn");
					$("body").off(".removecolumn");
				}
			});
	};

	LightKendoGrid.prototype._removeColumn = function(columnDisplayName)
	{
		var columns = this.kendoGrid.columns;
		var avaColumns = this._availableColumns;
		for (var idx = 0; idx < columns.length; idx++)
		{
			if (columns[idx].DisplayName === columnDisplayName)
			{
				var subColumn = columns.filter(function(column)
				{
					return column.ParentField === columns[idx].FieldName;
				});
				avaColumns.push(columns[idx]);
				this.clearCustomFilterByFieldName(columns[idx].FieldName);
				columns.splice(idx, 1);
				if (subColumn && subColumn.length > 0)
				{
					avaColumns.push(subColumn[0]);
					this.clearCustomFilterByFieldName(subColumn[0].FieldName);
					subColumn.forEach(function(sColumn)
					{
						columns.splice(columns.indexOf(sColumn), 1);
					});
				}
				break;
			}
		}
		this._availableColumns = avaColumns;
	};

	LightKendoGrid.prototype.clearCustomFilterByFieldName = function(fieldName)
	{
		this.kendoGridFilterHelper?.clearCustomFilterByFieldName(fieldName);
	};

	LightKendoGrid.prototype.getSelectedRecordsFromServer = function()
	{
		var self = this, options = {
			paramData: { getCount: false, databaseId: tf.datasourceManager.databaseId },
			data: {
				fields: self._obSelectedColumns().map(function(column) { return column.FieldName }),
				filterClause: "",
				filterSet: null,
				idFilter: {
					ExcludeAny: [],
					IncludeOnly: self.getSelectedIds()
				},
				sortItems: self.searchOption.data.sortItems
			}
		};

		if (self.options.setRequestOption)
		{
			self.options.setRequestOption(options);

			// reset selected ids.
			const includeIds = self.getSelectedIds();
			options.data.idFilter.IncludeOnly = includeIds;
		}

		return tf.ajax.post(self.getApiRequestURL(self.options.url), options);
	};

	LightKendoGrid.prototype.setColumnCurrentFilterIcon = function()
	{
		return this.kendoGridFilterHelper.setColumnCurrentFilterIcon();
	};

	LightKendoGrid.prototype.addFilterClass = function($container, kendoGrid)
	{
		return this.kendoGridFilterHelper.addFilterClass($container, kendoGrid);
	};

	LightKendoGrid.prototype.dispose = function()
	{
		this.$container.off('click');
		this.onDoubleClick.unsubscribeAll();
		this.onRowsChangeCheck.unsubscribeAll();
		this.onRowsChanged.unsubscribeAll();
		this.onDataBoundEvent.unsubscribeAll();
		this.onClearFilter.unsubscribeAll();
		this.onAltPressClick.unsubscribeAll();
		if (this.onClearGridFilterClickEvent)
		{
			this.onClearGridFilterClickEvent.unsubscribeAll();
		}

		if (this.onFilterChanged)
		{
			this.onFilterChanged.unsubscribeAll();
		}
		this._onWindowResize = null;
		this.shortcutExtender && this.shortcutExtender.dispose();
		for (var i in this.subscriptions)
		{
			this.subscriptions[i]?.dispose();
			this.subscriptions[i] = null;
		}
		this.$container.removeData("lightKendoGrid");
		if (this.kendoGrid)
		{
			this.kendoGrid.dataSource = null;
			this.kendoGrid.destroy();
			this.kendoGrid = null;
		}
		this.$container.find('[data-kendo-role="autocomplete"]').each(function()
		{
			if ($(this).data("kendoAutoComplete"))
			{
				$(this).data("kendoAutoComplete").destroy();
			}
		});
		if (this.fullfillGridBlankTimer)
		{
			clearTimeout(this.fullfillGridBlankTimer);
			this.fullfillGridBlankTimer = null;
		}
		this.lightKendoGridDetail && this.lightKendoGridDetail.dispose();
	};

	LightKendoGrid.AllFilterTypes = ['contains', 'isequalto', 'isnotequalto', 'startswith',
		'doesnotcontain', 'endswith', 'islessthanorequalto', 'isgreaterthanorequalto',
		'isgreaterthan', 'islessthan', 'isempty', 'isnotempty', 'custom', 'list',
		'lastxdays', 'lastxhours', 'lastxmonths', 'lastxweeks', 'lastxyears',
		'nextxdays', 'nextxhours', 'nextxmonths', 'nextxweeks', 'nextxyears', 'olderthanxdays',
		'olderthanxmonths', 'olderthanxyears', 'onyearx', 'all', 'lastmonth', 'lastweek', 'lastyear',
		'nextbusinessday', 'nextmonth', 'nextweek', 'nextyear', 'thismonth', 'thisweek', 'thisyear',
		'today', 'tomorrow', 'yesterday', 'onx', 'onorafterx', 'onorbeforex'];

	const filterNames = {
		'Equal To': 'isequalto',
		'Not Equal To': 'isnotequalto',
		'Does Not Contain': 'doesnotcontain',
		'Contains': 'contains',
		'Starts With': 'startswith',
		'Ends With': 'endswith',
		'Greater Than': 'isgreaterthan',
		'Greater Than or Equal To': 'isgreaterthanorequalto',
		'Less Than': 'islessthan',
		'Less Than or Equal To': 'islessthanorequalto',
		'Empty': 'isempty',
		'Not Empty': 'isnotempty',
		'Custom': 'custom',
		'List': 'list',
		'Last X Days': 'lastxdays',
		'Last X Hours': 'lastxhours',
		'Last X Months': 'lastxmonths',
		'Last X Weeks': 'lastxweeks',
		'Last X Years': 'lastxyears',
		'Next X Days': 'nextxdays',
		'Next X Hours': 'nextxhours',
		'Next X Months': 'nextxmonths',
		'Next X Weeks': 'nextxweeks',
		'Next X Years': 'nextxyears',
		'Older than X Days': 'olderthanxdays',
		'Older than X Months': 'olderthanxmonths',
		'Older than X Years': 'olderthanxyears',
		'On Year X': 'onyearx',
		'All': 'all',
		'Last Month': 'lastmonth',
		'Last Week': 'lastweek',
		'Last Year': 'lastyear',
		'Next Business Day': 'nextbusinessday',
		'Next Month': 'nextmonth',
		'Next Week': 'nextweek',
		'Next Year': 'nextyear',
		'This Month': 'thismonth',
		'This Week': 'thisweek',
		'This Year': 'thisyear',
		'Today': 'today',
		'Tomorrow': 'tomorrow',
		'Yesterday': 'yesterday',
		'On X': 'onx',
		'On or After X': 'onorafterx',
		'On or Before X': 'onorbeforex'
	};

	LightKendoGrid.prototype.filterNames = filterNames;

	LightKendoGrid.FilterNames = filterNames;

	LightKendoGrid.prototype.operatorKendoMapFilterNameValue = {
		'eq': 'isequalto',
		'neq': 'isnotequalto',
		'doesnotcontain': 'doesnotcontain',
		'contains': 'contains',
		'startswith': 'startswith',
		'endswith': 'endswith',
		'gt': 'isgreaterthan',
		'gte': 'isgreaterthanorequalto',
		'lt': 'islessthan',
		'lte': 'islessthanorequalto',
		'isempty': 'isempty',
		'isnotempty': 'isnotempty',
		'custom': 'custom',
		'list': 'list',
		'lastxdays': 'lastxdays',
		'lastxhours': 'lastxhours',
		'lastxmonths': 'lastxmonths',
		'lastxweeks': 'lastxweeks',
		'lastxyears': 'lastxyears',
		'nextxdays': 'nextxdays',
		'nextxhours': 'nextxhours',
		'nextxmonths': 'nextxmonths',
		'nextxweeks': 'nextxweeks',
		'nextxyears': 'nextxyears',
		'olderthanxdays': 'olderthanxdays',
		'olderthanxmonths': 'olderthanxmonths',
		'olderthanxyears': 'olderthanxyears',
		'onyearx': 'onyearx',
		'all': 'all',
		'lastmonth': 'lastmonth',
		'lastweek': 'lastweek',
		'lastyear': 'lastyear',
		'nextbusinessday': 'nextbusinessday',
		'nextmonth': 'nextmonth',
		'nextweek': 'nextweek',
		'nextyear': 'nextyear',
		'thismonth': 'thismonth',
		'thisweek': 'thisweek',
		'thisyear': 'thisyear',
		'today': 'today',
		'tomorrow': 'tomorrow',
		'yesterday': 'yesterday',
		'onx': 'onx',
		'onorafterx': 'onorafterx',
		'onorbeforex': 'onorbeforex'
	}

	var operatorMapping = {
		'eq': 'EqualTo',
		'neq': 'NotEqualTo',
		'doesnotcontain': 'DoesNotContain',
		'contains': 'Contains',
		'startswith': 'StartsWith',
		'endswith': 'EndsWith',
		'gt': 'GreaterThan',
		'gte': 'GreaterThanOrEqualTo',
		'lt': 'LessThan',
		'lte': 'LessThanOrEqualTo',
		'isempty': 'IsNull',
		'isnotempty': 'IsNotNull',
		'custom': 'Custom',
		'list': 'In',
		'wi': 'IsWithIn',
		'lastxdays': 'LastXDays',
		'lastxhours': 'LastXHours',
		'lastxmonths': 'LastXMonths',
		'lastxweeks': 'LastXWeeks',
		'lastxyears': 'LastXYears',
		'nextxdays': 'NextXDays',
		'nextxhours': 'NextXHours',
		'nextxmonths': 'NextXMonths',
		'nextxweeks': 'NextXWeeks',
		'nextxyears': 'NextXYears',
		'olderthanxdays': 'OlderthanXDays',
		'olderthanxmonths': 'OlderthanXMonths',
		'olderthanxyears': 'OlderthanXYears',
		'onyearx': 'OnYearX',
		'all': 'All',
		'lastmonth': 'LastMonth',
		'lastweek': 'LastWeek',
		'lastyear': 'LastYear',
		'nextbusinessday': 'NextBusinessDay',
		'nextmonth': 'NextMonth',
		'nextweek': 'NextWeek',
		'nextyear': 'NextYear',
		'thismonth': 'ThisMonth',
		'thisweek': 'ThisWeek',
		'thisyear': 'ThisYear',
		'today': 'Today',
		'tomorrow': 'Tomorrow',
		'yesterday': 'Yesterday',
		'onx': 'OnX',
		'onorafterx': 'OnOrAfterX',
		'onorbeforex': 'OnOrBeforeX'
	};

	Object.setPrototypeOf(operatorMapping, {
		reverseKeyValue: function()
		{
			var obj = Object.create(null);

			for (var [k, v] of Object.entries(this))
			{
				obj[v] = k;
			}

			return obj;
		}
	});

	LightKendoGrid.prototype.operatorKendoMapTF = operatorMapping;

	LightKendoGrid.normalizeResultItem = function(items)
	{
		if (items.length === 1 && $.isArray(items[0]))
		{
			return items[0];
		}
		return items;
	};

	LightKendoGrid.prototype.addHotLink = function()
	{
		const self = this,
			hotLinkConfig = {
				studentschedule: ["Name", { targetGrid: "trip", fields: ["TripName"], }]
			}
		const $table = self.$container.find("div[class^='k-grid'] table");
		TF.Grid.GridHotLinkHelper && TF.Grid.GridHotLinkHelper.setHotLink(self.options.gridType, $table, hotLinkConfig, self.kendoGrid);
	};

	/**
	 * Check is this grid used as dashboard widget.
	 *
	 * @return {*}
	 */
	LightKendoGrid.prototype.isDashboardWidget = function()
	{
		return this.options.customGridType === "dashboardwidget"
	};

	LightKendoGrid.prototype.hasRollUpFieldsInThematicConfigs = function()
	{
		var hasRollUpField = false;
		if (!this.selectedGridThematicConfigs)
		{
			return hasRollUpField;
		}

		this.selectedGridThematicConfigs.forEach(configs =>
		{
			if (hasRollUpField) return;

			configs.forEach(config =>
			{
				if (hasRollUpField) return;

				hasRollUpField = config.typeId === TF.DetailView.UserDefinedFieldHelper.DataTypeId.RollUp ||
					config.typeId === TF.DetailView.UserDefinedFieldHelper.DataTypeId.Case;
			});
		});

		return hasRollUpField;
	};

	LightKendoGrid.prototype.isDisableShortCutKey = function()
	{
		return this.disableShortCutKey || !this.$container.is(":visible");
	};
})();

(function()
{
	var FilterHelper = function() { };
	createNamespace("TF").FilterHelper = FilterHelper;

	FilterHelper.dateTimeNilFiltersName = ['Last X Days', 'Last X Hours', 'Last X Months', 'Last X Weeks', 'Last X Years',
		'Next X Days', 'Next X Hours', 'Next X Months', 'Next X Weeks', 'Next X Years', 'Older than X Days',
		'Older than X Months', 'Older than X Years', 'On Year X'];

	FilterHelper.dateTimeNilFiltersOperator = ['lastxdays', 'lastxhours', 'lastxmonths', 'lastxweeks', 'lastxyears',
		'nextxdays', 'nextxhours', 'nextxmonths', 'nextxweeks', 'nextxyears', 'olderthanxdays',
		'olderthanxmonths', 'olderthanxyears', 'onyearx'];

	FilterHelper.dateTimeNonParamFiltersName = ['All', 'Last Month', 'Last Week', 'Last Year',
		'Next Business Day', 'Next Month', 'Next Week',
		'Next Year', 'This Month', 'This Week', 'This Year', 'Today', 'Tomorrow', 'Yesterday'];

	FilterHelper.dateTimeNonParamFiltersOperator = ['all', 'lastmonth', 'lastweek', 'lastyear',
		'nextbusinessday', 'nextmonth', 'nextweek',
		'nextyear', 'thismonth', 'thisweek', 'thisyear', 'today', 'tomorrow', 'yesterday'];

	FilterHelper.dateTimeDateParamFiltersOperator = ['onx', 'onorafterx', 'onorbeforex'];

	FilterHelper.dateTimeDateParamFiltersNames = ['On X', 'On or After X', 'On or Before X'];

	FilterHelper.dateTimeDateParamFilterMapping = {
		'onx': 'On ',
		'onorafterx': 'On or After ',
		'onorbeforex': 'On or Before '
	};

	FilterHelper.dateTimeParamFiltersOperator = ['eq', 'neq', 'lt', 'lte', 'gt', 'gte'];

	FilterHelper.getNilFiltersFormat = function(filter)
	{
		if (filter === 'On Year X') return filter;

		let formatStr = filter.slice(0, -1);
		formatStr = formatStr + '(s)';
		return formatStr;
	}

	FilterHelper.getSortColumns = function(columns)
	{
		return columns.filter(function(column) { return column.isSortItem; });
	}

	FilterHelper.setSortItems = function(requestOptions, sortColumns)
	{
		if (sortColumns.length === 0)
			return requestOptions;

		var sortItems = requestOptions.data.sortItems || [];
		sortColumns.map(function(sortColumn)
		{
			let sortColumnName = sortColumn.FieldName;
			let direction = "Ascending";
			let isAscending = true;
			if (sortColumn.isAscendingSort === false)
			{
				direction = "Descending";
				isAscending = false;
			}

			let item = sortItems.filter(function(sortItem)
			{
				if (sortItem.Name === sortColumnName)
					return true;
				return false;
			});

			if (item.length === 0)
			{
				sortItems.push({ Name: sortColumnName, Direction: direction, isAscending: isAscending });
			}
		});

		requestOptions.data.sortItems = sortItems;
		return requestOptions;
	}

	FilterHelper.buildEmptyDSFilterSet = function()
	{
		return {
			FilterSets: [],
			FilterItems: [],
			LogicalOperator: "and"
		}
	}

	FilterHelper.isDateOrDateTimeFilterType = function(filterType)
	{
		return filterType === 'datetime' || filterType === 'date';
	}

	FilterHelper.disableFilterCellInput = function($input, isNormalInput)
	{
		var kendoAutoComplete = $($input[0]).data('kendoAutoComplete');
		if (kendoAutoComplete !== undefined)
			kendoAutoComplete.enable(false);

		var kendoNumericTextBox = $($input[1]).data('kendoNumericTextBox');
		if (kendoNumericTextBox !== undefined)
			kendoNumericTextBox.enable(false);

		var kendoDatePicker = $($input[0]).data('kendoDatePicker');
		if (kendoDatePicker !== undefined)
			kendoDatePicker.enable(false);

		var DateTimePicker = $($input[0]).data('DateTimePicker');
		if (DateTimePicker !== undefined)
			DateTimePicker.disable(true);

		var kendoDateTimePicker = $($input[0]).data('kendoDateTimePicker');
		if (kendoDateTimePicker !== undefined)
		{
			kendoDateTimePicker.enable(false);
			kendoDateTimePicker.element.parent().find('.datepickerbutton').addClass(TF.KendoClasses.STATE.DISABLED);
		}

		if (isNormalInput)
			$input.attr('disabled', true).addClass('is-disabled-text-input');
	};

	FilterHelper.hideDatetimeNumberCell = function($input, cellWidget)
	{
		let dateTimeFilterCell = $input.closest('.k-filtercell'); //hide the number box when select nil filter
		let dateTimeNumberFilterCell = dateTimeFilterCell.find("span.date-number");
		let cellClass = cellWidget === "datetimepicker" ? ".tf-filter" : ".k-datepicker";
		if (dateTimeNumberFilterCell)
		{
			dateTimeNumberFilterCell.hide();
			dateTimeNumberFilterCell.closest(".k-filtercell").find(cellClass).show();
		}
	}

	FilterHelper.enableFilterCellInput = function($input, isNormalInput)
	{
		var kendoAutoComplete = $($input[0]).data('kendoAutoComplete');
		if (kendoAutoComplete !== undefined)
			kendoAutoComplete.enable(true);

		var kendoNumericTextBox = $($input[1]).data('kendoNumericTextBox');
		if (kendoNumericTextBox !== undefined)
			kendoNumericTextBox.enable(true);

		var kendoDatePicker = $($input[0]).data('kendoDatePicker');
		if (kendoDatePicker !== undefined)
			kendoDatePicker.enable(true);

		var DateTimePicker = $($input[0]).data('DateTimePicker');
		if (DateTimePicker !== undefined)
			DateTimePicker.enable(true); // DateTimePicker.disable(false);

		var kendoDateTimePicker = $($input[0]).data('kendoDateTimePicker');
		if (kendoDateTimePicker !== undefined)
		{
			kendoDateTimePicker.enable(true);
			kendoDateTimePicker.element.parent().find('.datepickerbutton').removeClass(TF.KendoClasses.STATE.DISABLED);
		}

		if (isNormalInput)
			$input.attr('disabled', false).removeClass('is-disabled-text-input');
	};

	FilterHelper.formatFilterCellInputValue = function(val, columnType)
	{
		var format = "";

		switch (columnType)
		{
			case "time":
				format = "h:mm A";
				break;
			case "date":
				format = "l";
				break;
			case "datetime":
				format = "MM/DD/YYYY hh:mm A";
				break;
			default:
				return val;
		}

		var str = moment(val).format(format);
		return (str === "Invalid date") ? val : str;
	};

	FilterHelper.clearFilterCellInput = function($input)
	{
		$input.val('');
	};
})();

(function()
{
	var CustomFilterHelper = function() { };
	createNamespace("TF").CustomFilterHelper = CustomFilterHelper;

	CustomFilterHelper.removeEmptyFilterItems = function(filterItems)
	{
		if (!filterItems || !filterItems.length)
			return filterItems;

		for (var i = filterItems.length - 1; i >= 0; i--)
		{
			var filterItem = filterItems[i];
			if (TF.CustomFilterHelper.isEmptyFilterItemOfTimeTypeColumn(filterItem) ||
				TF.CustomFilterHelper.isEmptyFilterItemOfDateTypeColumn(filterItem) ||
				TF.CustomFilterHelper.isEmptyFilterItemOfDateTimeTypeColumn(filterItem) ||
				TF.CustomFilterHelper.isEmptyFilterItemOfOtherTypeColumn(filterItem))
			{
				filterItems.pop();
			}
		}
		return filterItems;
	}


	CustomFilterHelper.isEmptyFilterItemOfTimeTypeColumn = function(filterItem)
	{
		return (filterItem.TypeHint === 'Time'
			&& filterItem.Operator !== 'Empty'
			&& filterItem.Operator !== 'IsNotNull' && filterItem.Operator !== 'IsNull'
			&& (filterItem.Value === '' || filterItem.Value === "Invalid date"));
	}

	CustomFilterHelper.isEmptyFilterItemOfDateTypeColumn = function(filterItem)
	{
		return (filterItem.TypeHint === 'Date'
			&& filterItem.Operator !== 'Empty'
			&& filterItem.Operator !== 'IsNotNull' && filterItem.Operator !== 'IsNull'
			&& TF.FilterHelper.dateTimeNonParamFiltersOperator.indexOf(filterItem.Operator.toLowerCase()) === -1
			&& (filterItem.Value === '' || filterItem.Value === "Invalid date"));
	}

	CustomFilterHelper.isEmptyFilterItemOfDateTimeTypeColumn = function(filterItem)
	{
		return (filterItem.TypeHint === 'DateTime'
			&& filterItem.Operator !== 'Empty'
			&& filterItem.Operator !== 'IsNotNull' && filterItem.Operator !== 'IsNull'
			&& TF.FilterHelper.dateTimeNonParamFiltersOperator.indexOf(filterItem.Operator.toLowerCase()) === -1
			&& (filterItem.Value === '' || filterItem.Value === "Invalid date"));
	}

	CustomFilterHelper.isEmptyFilterItemOfOtherTypeColumn = function(filterItem)
	{
		return (
			filterItem.TypeHint !== 'Date' &&
			filterItem.TypeHint !== 'Time' &&
			filterItem.TypeHint !== 'DateTime' &&
			filterItem.TypeHint !== 'BoolToChar' && //used for georegion geo column
			filterItem.Operator !== 'Empty' && filterItem.Operator !== 'IsNotNull' && filterItem.Operator !== 'IsNull' &&
			(filterItem.Value === '' || filterItem.Value === "Invalid date")
		);
	}


	CustomFilterHelper.removeSpecialDDLItem = function(dropdownList)
	{
		var specialItems = dropdownList.dataItems().filter(function(item)
		{
			return item.value === 'custom' || item.value === 'list' ||
				TF.FilterHelper.dateTimeNilFiltersOperator.indexOf(item.value) > -1 ||
				TF.FilterHelper.dateTimeNonParamFiltersOperator.indexOf(item.value) > -1 ||
				TF.FilterHelper.dateTimeDateParamFiltersOperator.indexOf(item.value) > -1;
		});
		specialItems.map(function(specialItem)
		{
			dropdownList.dataSource.remove(specialItem);
		});
	};

	CustomFilterHelper.addCustomFilterEllipsisClass = function(input)
	{
		input.addClass('text-ellipsis');
	}

	CustomFilterHelper.removeCustomFilterEllipsisClass = function(input)
	{
		input.removeClass('text-ellipsis');
	}

	CustomFilterHelper.initCustomFilterBtn = function($gridContainer)
	{
		$gridContainer.children('.k-grid-header').find('thead tr:first-child').css('height', 33); // reset header height changed by filter menu
		$gridContainer.children('.k-grid-header').find('.k-grid-filter-menu').addClass('k-filter-custom-btn').addClass('hidden');
	};

	CustomFilterHelper.clearCustomFilter = function()
	{
		var $customInput = $(".clear-custom-filter-menu-btn").parent().find('input');
		TF.CustomFilterHelper.removeCustomFilterEllipsisClass($customInput);
		$(".clear-custom-filter-menu-btn").remove(); //remove custom filter close button
	};
})();

(function()
{
	var ListFilterHelper = function() { };
	createNamespace("TF").ListFilterHelper = ListFilterHelper;

	ListFilterHelper.initListFilters = function()
	{
		return {}
	};

	ListFilterHelper.getDefaultListFilterOption = function(displayFilterTypeName)
	{
		return {
			title: 'Filter ' + displayFilterTypeName,
			description: 'Select the ' + displayFilterTypeName + ' that you would like to view.'
		}
	};

	ListFilterHelper.initListFilterBtn = function($gridContainer)
	{
		var filterListBtn = '<div class="k-filter-list-btn hidden btn btn-default btn-sharp">' +
			'<span class="glyphicon glyphicon-option-horizontal"></span>' +
			'</div>';
		$gridContainer.children('.k-grid-header').find('thead tr:first-child th').append(filterListBtn);
	};

	ListFilterHelper.addSelectedIdsIntoFilterItems = function(filterItems, cachedListFilters)
	{
		if (!cachedListFilters || !Object.keys(cachedListFilters))
			return filterItems;

		filterItems.map(function(filterItem)
		{
			var fieldName = filterItem.FieldName;
			if (filterItem.Operator === 'In' &&
				cachedListFilters[fieldName] // removed blank list filter from quick filter
			)
			{
				var selectedIds = cachedListFilters[fieldName].ids || [];
				filterItem.ListFilterIds = JSON.stringify(selectedIds);
			}
		});
		return filterItems;
	};

	ListFilterHelper.buildDsListFilterItem = function(fieldName, value, valueList, listfilterIds)
	{
		return {
			Operator: 'In',
			IsListFilter: true,
			FieldName: fieldName,
			Value: value || '',
			ValueList: valueList || [],
			ListFilterIds: listfilterIds || []
		}
	}

	ListFilterHelper.buildListFilterItemBySelectedData = function(option)
	{
		var fieldName = option.fieldName;
		var filterField = option.filterField;
		var selectedData = option.selectedData;

		var selectedItems = selectedData.map(function(item) { return item[filterField]; });
		var selectedIds = selectedData.map(function(item) { return item.Id; });
		return ListFilterHelper.buildDsListFilterItem(
			fieldName,
			selectedItems.join(','),
			JSON.stringify(selectedItems),
			selectedIds
		);
	}

	ListFilterHelper.initListFilterIdsByQuickFilter = function(quickFilterData, cachedListFilters, columns)
	{
		if (!quickFilterData ||
			!quickFilterData.filterSet ||
			!quickFilterData.filterSet.FilterItems ||
			!quickFilterData.filterSet.FilterItems.length)
			return cachedListFilters;

		quickFilterData.filterSet.FilterItems.map(function(filterItem)
		{
			if (filterItem.Operator === 'In')
			{
				var field = filterItem.FieldName;
				var column = columns.filter(function(column) { return column.FieldName === field; });

				if (column && column.length > 0 && column[0].ListFilterTemplate)
				{
					TF.ListFilterHelper.initListFilterItem(cachedListFilters, column[0].ListFilterTemplate, filterItem, field);
				}
			}
		});

		return cachedListFilters;
	};

	ListFilterHelper.initListFilterItem = function(cachedListFilters, listFilterTemplate, dsFilterItem, field)
	{
		cachedListFilters[field] = cachedListFilters[field] || {};
		var rawListFilterIds = dsFilterItem.ListFilterIds || '[]';
		var selectedIds = JSON.parse(rawListFilterIds);
		cachedListFilters[field].ids = selectedIds;
		cachedListFilters[field].selectedFilterItems = dsFilterItem.Value?.split(',') || [];

		if (listFilterTemplate.isDistinctListTemplate) // it will be removed when get filter data from list data table but not from grid column
		{
			if (dsFilterItem.Value)
			{
				cachedListFilters[field].selectedFilterItems = dsFilterItem.Value.split(',');
				cachedListFilters[field].selectedItems = dsFilterItem.Value.split(',');
			}
		}
		else if (listFilterTemplate.listFilterType === 'Enum')
		{
			if (dsFilterItem.Value)
				cachedListFilters[field].selectedFilterItems = dsFilterItem.Value.split(',');
		}
		else if (listFilterTemplate.listFilterType === 'WithSearchGrid' && selectedIds.length > 0)
		{
			var requestUrl = listFilterTemplate.getUrl();
			var requestOption = {
				data: {
					FilterClause: "",
					IdFilter: { IncludeOnly: selectedIds }
				}
			}

			if (listFilterTemplate.GridType === "BusfinderHistoricalTrip" && listFilterTemplate.DisplayFilterTypeName === "Trips")
			{
				requestOption.paramData = requestOption.paramData || {};
				requestOption.paramData.time = toISOStringWithoutTimeZone(moment().currentTimeZoneTime());
			}
			else if (listFilterTemplate.setLeftGridRequestOption)
				requestOption = listFilterTemplate.setLeftGridRequestOption(requestOption);

			if (listFilterTemplate.UDFId)
			{
				requestOption.data.fields = [listFilterTemplate.OriginalName];
			}

			function processFilterWithField(selectedItems, listFilterTemplate, cachedListFilters, field)
			{
				selectedItems = TF.ListMoverForListFilterHelper.processSelectedData(selectedItems, listFilterTemplate.filterField);
				selectedItems.sort(function(a, b) { return a.FilterItem.localeCompare(b.FilterItem); });
				cachedListFilters[field].selectedItems = selectedItems;
				var tmp = TF.ListMoverForListFilterHelper.processSelectedData(selectedItems, listFilterTemplate.filterField);
				cachedListFilters[field].selectedFilterItems = tmp.map(function(item) { return item.FilterItem; });
			}

			function getData(requestUrl, idField)
			{
				tf.promiseAjax.get(requestUrl)
					.then(function(response)
					{
						var selectedItems = response.Items.filter(x => selectedIds.some(y => y === x[idField]));
						selectedItems.forEach(x => x.Id = x[idField]);
						processFilterWithField(selectedItems, listFilterTemplate, cachedListFilters, field);
					});
			}

			const getApiFilterId = TF.ListFilterHelper.filterId(listFilterTemplate.GridType);
			if (getApiFilterId)
			{
				getData(requestUrl, getApiFilterId);
			}
			else
			{
				tf.promiseAjax.post(requestUrl, requestOption)
					.then(function(response)
					{
						var selectedItems = response.Items || [];

						if (listFilterTemplate.UDFId)
						{
							/**
						 * preprocess response to map UDF
						 */
							selectedItems = selectedItems.map(function(item)
							{
								if (Object.keys(item).some(function(key)
								{
									return key == listFilterTemplate.OriginalName;
								}))
								{
									item[listFilterTemplate.filterField] = item[listFilterTemplate.OriginalName];
								}

								return item;
							});
						}

						processFilterWithField(selectedItems, listFilterTemplate, cachedListFilters, field);
					});
			}
		}
		else if (listFilterTemplate.listFilterType === 'MapData' && selectedIds.length > 0)
		{
			var requestUrl = listFilterTemplate.getUrl();
			var requestMethod = listFilterTemplate.requestMethod ? listFilterTemplate.requestMethod : 'get';
			tf.promiseAjax[requestMethod](requestUrl)
				.then(function(response)
				{
					var allItems = TF.ListFilterHelper.processMapData(response, listFilterTemplate.modifySource);
					var selectedItems = [];
					if (cachedListFilters[field].ids)
					{
						selectedItems = allItems.filter(function(item)
						{
							return Array.contain(cachedListFilters[field].ids, item.Id);
						});
					}
					cachedListFilters[field].selectedItems = selectedItems;
					//cachedListFilters[field].selectedFilterItems = TF.ListMoverForListFilterHelper.processSelectedData(selectedItems, listFilterTemplate.filterField);
					var tmp = TF.ListMoverForListFilterHelper.processSelectedData(selectedItems, listFilterTemplate.filterField)
					cachedListFilters[field].selectedFilterItems = tmp.map(function(item) { return item.FilterItem; });;
				});
		}
	};

	ListFilterHelper.filterId = function(gridType)
	{
		switch (gridType)
		{
			case "Genders":
			case "GPSEventType":
				return 'ID';
			case "Grades":
				return 'Id';
			case "StaffTypes":
				return 'StaffTypeId';
			case "Tags":
				return 'Id';
			default:
				return null;
		}
	}

	ListFilterHelper.processMapData = function(response, modifySource)
	{
		var data = $.isArray(response.Items[0]) ? response.Items[0] : response.Items;
		var allItems = data;
		if (modifySource)
			allItems = modifySource(allItems);

		return allItems;
	}

	ListFilterHelper.getSelectedFilterItemsForWithSearchGridType = function(cachedListFilters, listFilterTemplate, fieldName)
	{
		var selectedItems = cachedListFilters[fieldName].selectedItems || [];
		return selectedItems;
	}

	ListFilterHelper.getSelectedFilterItemsForDefaultType = function(cachedListFilters, listFilterTemplate, fieldName)
	{
		var allItems = listFilterTemplate.AllItems;
		if (cachedListFilters[fieldName] && cachedListFilters[fieldName].selectedFilterItems)
		{
			return cachedListFilters[fieldName].selectedFilterItems;
		}

		var selectedIds = cachedListFilters[fieldName].ids || [];

		var selectedFilterItems = listFilterTemplate.AllItems.filter(function(item, idx) { return selectedIds.indexOf(idx) >= 0; });
		return selectedFilterItems;
	}

	ListFilterHelper.handleWithSearchGridListFilterResult = function(cachedListFilters, selectedItems, fieldName, data)
	{
		var caller = data;

		cachedListFilters[fieldName].selectedFilterItems = cachedListFilters[fieldName].selectedFilterItems || [];
		var originalSelectedFilterItemsCnt = cachedListFilters[fieldName].selectedFilterItems.length;
		cachedListFilters[fieldName].selectedItems = cachedListFilters[fieldName].selectedItems || [];

		var selectedFilterItems = [], selectedIds = undefined;
		if (selectedItems === false)
		{
			selectedFilterItems = cachedListFilters[fieldName].selectedFilterItems;
		}
		else if (selectedItems.length > 0)
		{
			selectedFilterItems = selectedItems.map(function(item) { return item.FilterItem == null ? item : item.FilterItem; });
			cachedListFilters[fieldName].selectedFilterItems = selectedFilterItems;
			cachedListFilters[fieldName].selectedItems = selectedItems;
			cachedListFilters[fieldName].ids = selectedItems.map(function(item) { return item.Id });
			selectedIds = cachedListFilters[fieldName].ids;
		}
		else if (selectedItems.length === 0)
		{
			delete cachedListFilters[fieldName];
		}

		var currentlySelectedFilterItemsCnt = selectedFilterItems.length;

		caller.afterhandleListFilterResult(selectedFilterItems, fieldName, originalSelectedFilterItemsCnt, currentlySelectedFilterItemsCnt, selectedIds);
	};

	ListFilterHelper.handleDefaultListFilterResult = function(cachedListFilters, selectedFilterItems, fieldName, listFilterTemplate)
	{
		var caller = this;

		var allItems = listFilterTemplate.AllItems;
		cachedListFilters[fieldName].selectedFilterItems = cachedListFilters[fieldName].selectedFilterItems || [];
		var originalSelectedFilterItemsCnt = cachedListFilters[fieldName].selectedFilterItems.length;

		if (selectedFilterItems !== false)
		{
			cachedListFilters[fieldName].selectedFilterItems = selectedFilterItems;

			var ids = [];
			allItems.map(function(item, idx)
			{
				selectedFilterItems.map(function(selectedFilterItem)
				{
					if (selectedFilterItem === item)
						ids.push(idx);
				});
			});
			cachedListFilters[fieldName].ids = ids;
		}

		selectedFilterItems = cachedListFilters[fieldName].selectedFilterItems;
		let selectedIds = cachedListFilters[fieldName].ids;

		var currentlySelectedFilterItemsCnt = selectedFilterItems.length;

		caller.afterhandleListFilterResult(selectedFilterItems, fieldName, originalSelectedFilterItemsCnt, currentlySelectedFilterItemsCnt, selectedIds);
	};
})();

(function()
{
	var LightKendoGridHelper = function() { };
	createNamespace("TF").LightKendoGridHelper = LightKendoGridHelper;

	LightKendoGridHelper._cancelKendoGridSelectedArea = function(kendoGrid)
	{
		kendoGrid.selectable.userEvents.unbind("start");
		kendoGrid.selectable.userEvents.unbind("move");
		kendoGrid.selectable.userEvents.unbind("end");
	};

	LightKendoGridHelper.isHotLinkNode = function($node)
	{
		if (!$node)
			return false;

		return $node.closest('td').hasClass('has-link');
	};

	const RESIZE_EVENT_KEY = 'resize.lightKendoGrid';
	LightKendoGridHelper.rebindResizeEvent = function(event)
	{
		$(window).off(RESIZE_EVENT_KEY).on(RESIZE_EVENT_KEY, event);
	}

	LightKendoGridHelper.unbindResizeEvent = function()
	{
		$(window).off(RESIZE_EVENT_KEY);
	}
})()
