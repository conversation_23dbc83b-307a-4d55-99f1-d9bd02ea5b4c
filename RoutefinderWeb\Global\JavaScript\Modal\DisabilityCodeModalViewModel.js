﻿(function()
{
	createNamespace('TF.Modal').DisabilityCodeModalViewModel = DisabilityCodeModalViewModel;

	function DisabilityCodeModalViewModel(fieldName, id)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/disabilitycodecontrol');
		this.buttonTemplate('modal/positivenegative');
		this.disabilityCodeViewModel = new TF.Control.DisabilityCodeViewModel(fieldName, id);
		this.data(this.disabilityCodeViewModel);
		this.sizeCss = "modal-dialog-sm";

		var viewTitle;

		///this is going to check if the popup form is add new records or edit an existing record
		if (id)
		{
			viewTitle = tf.applicationTerm.getApplicationTermSingularByName("Edit Disability Code");
		}
		else
		{
			viewTitle = tf.applicationTerm.getApplicationTermSingularByName("Add Disability Code");
			this.buttonTemplate('modal/positivenegativeextend');
		}

		this.title(viewTitle);

		this.containerLoaded = ko.observable(false);
	}

	DisabilityCodeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	DisabilityCodeModalViewModel.prototype.constructor = DisabilityCodeModalViewModel;

	DisabilityCodeModalViewModel.prototype.positiveClick = function()
	{
		this.disabilityCodeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	DisabilityCodeModalViewModel.prototype.saveAndNewClick = function()
	{
		this.disabilityCodeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.disabilityCodeViewModel.obEntityDataModel(new TF.DataModel.DisabilityCodeDataModel());
				this.newDataList.push(result);
				if ($("input[name=code]") && $("input[name=code]").length > 0)
				{
					$("input[name=code]").focus();
				}
				PubSub.publish(topicCombine(pb.DATA_CHANGE, "listmover"));
			}
		}.bind(this));
	};

	DisabilityCodeModalViewModel.prototype.dispose = function()
	{
		this.disabilityCodeViewModel.dispose();
	};

})();
