﻿(function()
{
	// the base class of map
	createNamespace("TF.Map").BaseMap = BaseMap;

	BaseMap.longJobInterval = 20000; // 20s
	BaseMap.middleJobInterval = 10000; // 10s
	BaseMap.shortJobInterval = 5000; // 5s

	function BaseMap()
	{
		var self = this;
		self.ArcGIS = tf.map ? tf.map.ArcGIS : null;  // the reference of esri api for js
		self.map = null;
		self._status = {
			SUCCESS: "SUCCESS",
			CALCELLED: "CALCELLED"
		};

		this.usingArcGISComplete = new TF.Map.MapBehaviorSubject();
		this.startupLoadArcgisUrlsComplete = new TF.Map.MapBehaviorSubject();
	}

	/**
	* Loading esri js api.
	*/
	BaseMap.prototype.usingArcGIS = function(extrasLocation, callback)
	{
		var self = this;
		require({
			packages: [{
				name: "extras",
				location: extrasLocation
			}]
		}, [
			"esri/Map",
			"esri/Basemap",
			"esri/views/MapView",
			"esri/views/2d/layers/TileLayerView2D",
			"esri/layers/GraphicsLayer",
			"esri/layers/MapImageLayer",
			"esri/Graphic",
			"esri/geometry/SpatialReference",
			"esri/layers/FeatureLayer",
			"esri/layers/MediaLayer",
			"esri/request",
			"esri/config",
			"esri/Color",
			"esri/symbols/Font",
			"esri/symbols/PictureMarkerSymbol",
			"esri/symbols/SimpleFillSymbol",
			"esri/symbols/SimpleLineSymbol",
			"esri/symbols/SimpleMarkerSymbol",
			"esri/symbols/TextSymbol",
			"esri/widgets/Directions",
			"esri/widgets/Search",
			"esri/widgets/Search/SearchViewModel",
			"esri/geometry/Extent",
			"esri/geometry/Point",
			"esri/geometry/Polygon",
			"esri/geometry/Circle",
			"esri/geometry/Polyline",
			"esri/geometry/Multipoint",
			"esri/geometry/support/webMercatorUtils",
			"esri/views/draw/Draw",
			"esri/rest/geoprocessor",
			"esri/rest/support/JobInfo",
			"esri/rest/support/FeatureSet",
			"esri/rest/serviceArea",
			"esri/rest/support/ServiceAreaParameters",
			"esri/rest/locator",
			"esri/rest/geometryService",
			"esri/rest/support/ProjectParameters",
			"esri/core/reactiveUtils",
			"esri/rest/support/Query",
			"esri/geometry/Geometry",
			"esri/geometry/geometryEngine",
			"esri/widgets/Sketch/SketchViewModel",
			"esri/core/lang",
			"esri/core/maybe",
			"esri/identity/IdentityManager",
			"esri/rest/route",
			"esri/rest/support/RouteParameters",
			"esri/layers/TileLayer",
			"esri/layers/WMSLayer",
			"esri/layers/WMTSLayer",
			"esri/layers/VectorTileLayer",
			"esri/layers/CSVLayer",
			"esri/layers/KMLLayer",
			"esri/layers/GeoRSSLayer",
			"esri/layers/ImageryLayer",
			"esri/rest/query",
			"esri/views/draw/support/GraphicMover",
			"esri/views/draw/support/drawUtils",
			"esri/views/draw/support/input/GraphicMoverEvents",
			"esri/widgets/Popup",
			"esri/layers/support/TileInfo",
			"esri/layers/support/ImageElement",
			"esri/layers/support/CornersGeoreference",
			"esri/renderers/SimpleRenderer",
			"esri/renderers/ClassBreaksRenderer",
			"esri/layers/support/FeatureFilter",
			"esri/geometry/support/geodesicUtils",
			"esri/widgets/Legend",
			"esri/views/support/screenUtils",
			"extras/FlareClusterLayer",
			"esri/views/draw/DrawOperation",
			"esri/rest/print",
			"esri/rest/support/PrintTemplate",
			"esri/rest/support/PrintParameters",
			"esri/views/webgl/RenderingContext",
			"esri/views/webgl/BufferObject",
			"esri/views/webgl/enums",
			"esri/views/webgl/FramebufferObject",
			"esri/views/webgl/Texture",
			"esri/views/webgl/TextureDescriptor",
			"esri/views/webgl/VertexArrayObject",
			"esri/views/webgl/VertexElementDescriptor",
			"esri/renderers/UniqueValueRenderer",
			"esri/geometry/projection"
		], function(
			Map,
			Basemap,
			MapView,
			TileLayerView2D,
			GraphicsLayer,
			MapImageLayer,
			Graphic,
			SpatialReference,
			FeatureLayer,
			MediaLayer,
			esriRequest,
			esriConfig,
			Color,
			Font,
			PictureMarkerSymbol,
			SimpleFillSymbol,
			SimpleLineSymbol,
			SimpleMarkerSymbol,
			TextSymbol,
			Directions,
			Search,
			SearchViewModel,
			Extent,
			Point,
			Polygon,
			Circle,
			Polyline,
			Multipoint,
			webMercatorUtils,
			Draw,
			geoprocessor,
			JobInfo,
			FeatureSet,
			serviceArea,
			ServiceAreaParameters,
			locator,
			GeometryService,
			ProjectParameters,
			reactiveUtils,
			Query,
			Geometry,
			geometryEngine,
			SketchViewModel,
			coreLang,
			maybe,
			IdentityManager,
			route,
			RouteParameters,
			TileLayer,
			WMSLayer,
			WMTSLayer,
			VectorTileLayer,
			CSVLayer,
			KMLLayer,
			GeoRSSLayer,
			ImageryLayer,
			query,
			GraphicMover,
			drawUtils,
			GraphicMoverEvents,
			Popup,
			TileInfo,
			ImageElement,
			CornersGeoreference,
			SimpleRenderer,
			ClassBreaksRenderer,
			FeatureFilter,
			geodesicUtils,
			Legend,
			screenUtils,
			FlareClusterLayer,
			DrawOperation,
			print,
			PrintTemplate,
			PrintParameters,
			RenderingContext,
			BufferObject,
			webglEnums,
			FramebufferObject,
			Texture,
			TextureDescriptor,
			VertexArrayObject,
			VertexElementDescriptor,
			UniqueValueRenderer,
			projection
		)
		{
			GraphicsLayer.prototype.originalRemove = GraphicsLayer.prototype.remove;
			GraphicsLayer.prototype.remove = function(graphic)
			{
				const result = this.originalRemove(graphic);
				tfdispose(graphic);
				return result;
			}

			GraphicsLayer.prototype.originalRemoveAll = GraphicsLayer.prototype.removeAll;
			GraphicsLayer.prototype.removeAll = function()
			{
				const result = this.originalRemoveAll();
				(this.graphics && this.graphics.items || []).forEach(tfdispose);
				return result;
			}

			GraphicsLayer.prototype.originalRemoveMany = GraphicsLayer.prototype.removeMany;
			GraphicsLayer.prototype.removeMany = function(graphics)
			{
				const result = this.originalRemoveMany(graphics);
				Array.from(graphics || []).forEach(tfdispose);
				return result;
			}

			TileLayerView2D.prototype.requestUpdate = function()
			{
				this.updateRequested || (this.updateRequested = !0, this.suspended || (this.view && this.view.requestUpdate && this.view.requestUpdate()));
			}

			self.ArcGIS = {
				Color: Color,
				Map: Map,
				Basemap: Basemap,
				MapView: MapView,
				TileLayerView2D: TileLayerView2D,
				FeatureLayer: FeatureLayer,
				MediaLayer: MediaLayer,
				SimpleMarkerSymbol: SimpleMarkerSymbol,
				SimpleLineSymbol: SimpleLineSymbol,
				GraphicsLayer: GraphicsLayer,
				MapImageLayer: MapImageLayer,
				SpatialReference: SpatialReference,
				Graphic: Graphic,
				Font: Font,
				PictureMarkerSymbol: PictureMarkerSymbol,
				SimpleFillSymbol: SimpleFillSymbol,
				TextSymbol: TextSymbol,
				Directions: Directions,
				Search: Search,
				Extent: Extent,
				Point: Point,
				Polygon: Polygon,
				Circle: Circle,
				Polyline: Polyline,
				Multipoint: Multipoint,
				webMercatorUtils: webMercatorUtils,
				Draw: Draw,
				geoprocessor: geoprocessor,
				JobInfo: JobInfo,
				FeatureSet: FeatureSet,
				serviceArea: serviceArea,
				ServiceAreaParameters: ServiceAreaParameters,
				locator: locator,
				ProjectParameters: ProjectParameters,
				GeometryService: GeometryService,
				reactiveUtils: reactiveUtils,
				Query: Query,
				Geometry: Geometry,
				geometryEngine: geometryEngine,
				SketchViewModel: SketchViewModel,
				coreLang: coreLang,
				route: route,
				RouteParameters: RouteParameters,
				esriRequest: esriRequest,
				esriConfig: esriConfig,
				TileLayer: TileLayer,
				WMSLayer: WMSLayer,
				WMTSLayer: WMTSLayer,
				VectorTileLayer: VectorTileLayer,
				CSVLayer: CSVLayer,
				KMLLayer: KMLLayer,
				GeoRSSLayer: GeoRSSLayer,
				ImageryLayer: ImageryLayer,
				query: query,
				IdentityManager: IdentityManager,
				Popup: Popup,
				TileInfo: TileInfo,
				ImageElement: ImageElement,
				CornersGeoreference: CornersGeoreference,
				FlareClusterLayer: FlareClusterLayer,
				SimpleRenderer: SimpleRenderer,
				ClassBreaksRenderer: ClassBreaksRenderer,
				FeatureFilter: FeatureFilter,
				geodesicUtils: geodesicUtils,
				Legend: Legend,
				screenUtils: screenUtils,
				print,
				PrintTemplate,
				PrintParameters,
				UniqueValueRenderer,
				projection: projection
			};
			self.ArcGIS.esriConfig.request.timeout = 480000;//milliseconds

			this._hackDragHandler(GraphicMover, GraphicMoverEvents, drawUtils, coreLang);
			this._hackSuggest(SearchViewModel);
			this._hackSearch(Search);
			hackGeoprocessor(geoprocessor);
			hackGeometryEngine(geometryEngine);
			this._hackFlareClusterLayer(FlareClusterLayer);
			hackSnap(DrawOperation);
			hackRoute(route, FeatureSet);
			hackRenderingContext({ RenderingContext, BufferObject, webglEnums, FramebufferObject, Texture, TextureDescriptor, VertexArrayObject, VertexElementDescriptor, maybe })
			hackIdentityManager(IdentityManager)

			this.usingArcGISComplete.setProgressComplete();
			callback && callback();
		}.bind(self));
	};

	// When using 4.18, we hack IdentityManager.registerToken by making it do nothing, we modify the Arcgis js API source code in RW-20133.
	// In 4.30, we keep the previous process
	function hackIdentityManager(IdentityManager)
	{
		IdentityManager.registerToken = function()
		{
			// do nothing
		};
	}

	let webglChecked = false;
	function hackRenderingContext(imports)
	{
		TF.smartOverride(imports.RenderingContext.RenderingContext.prototype, 'configure', function(base)
		{
			base.apply(this, arguments);

			if (webglChecked)
			{
				return;
			}

			let result = testSamplerPrecision(this, imports);
			webglChecked = true;
			if (result)
			{
				tf.promiseBootbox.alert('A problem was detected with your graphics driver. Some of map features might not work. Please contact Transfinder Support at <a target="_blank" href="mailto: <EMAIL>"><EMAIL></a> or 888-427-2403.');
			}
		});
	}

	// fix RW-50403
	// Copy these codes from ArcGIS JS 4.29 testSamplerPrecision class
	function testSamplerPrecision(rctx, { BufferObject, webglEnums, FramebufferObject, Texture, TextureDescriptor, VertexArrayObject, VertexElementDescriptor, maybe })
	{
		let program = rctx.programCache.acquire("\n      precision highp float;\n      attribute vec2 a_pos;\n      uniform highp sampler2D u_texture;\n      varying vec4 v_color;\n\n      float getBit(in float bitset, in int bitIndex) {\n        float offset \x3d pow(2.0, float(bitIndex));\n        return mod(floor(bitset / offset), 2.0);\n      }\n\n      void main() {\n        vec4 value \x3d texture2D(u_texture, vec2(0.0));\n        float bit \x3d getBit(value.x * 255.0, 1);\n\n        v_color \x3d bit * vec4(1.0);\n        gl_Position \x3d vec4(a_pos * 2.0 - 1.0, 0.0, 1.0);\n      }\n      ", "\n      precision highp float;\n      varying vec4 v_color;\n\n      void main() {\n        gl_FragColor \x3d v_color;\n      }\n      ", new Map([["a_pos", 0]]))
		var textureDescriptor = new TextureDescriptor.TextureDescriptor(1);
		textureDescriptor.wrapMode = webglEnums.TextureWrapMode.CLAMP_TO_EDGE;
		textureDescriptor.samplingMode = webglEnums.TextureSamplingMode.NEAREST;
		const framebufferObject = new FramebufferObject.FramebufferObject(rctx, textureDescriptor)
			, uint8Array = new Uint8Array(4);
		let vertex = BufferObject.BufferObject.createVertex(rctx, webglEnums.Usage.STATIC_DRAW, new Uint16Array([0, 0, 1, 0, 0, 1, 1, 1]));
		vertex = new VertexArrayObject.VertexArrayObject(rctx, new Map([["a_position", 0]]), {
			geometry: [new VertexElementDescriptor.VertexElementDescriptor("a_position", 2, webglEnums.DataType.SHORT, 0, 4)]
		}, {
			geometry: vertex
		});
		rctx.useProgram(program);
		textureDescriptor = new Texture.Texture(rctx, textureDescriptor, new Uint8Array([2, 255, 0, 0]));
		program.setUniform1i("u_texture", 0);
		rctx.bindTexture(textureDescriptor, 0);
		textureDescriptor = rctx.getBoundFramebufferObject();
		rctx.bindFramebuffer(framebufferObject);
		rctx.useProgram(program);
		const { x: v, y: w, width: x, height: y } = rctx.getViewport();
		rctx.setViewport(0, 0, 1, 1);
		rctx.bindVAO(vertex);
		rctx.drawArrays(webglEnums.PrimitiveType.TRIANGLE_STRIP, 0, 4);
		rctx.setViewport(v, w, x, y);
		framebufferObject.readPixels(0, 0, 1, 1, webglEnums.PixelFormat.RGBA, webglEnums.PixelType.UNSIGNED_BYTE, uint8Array);
		vertex.dispose();
		framebufferObject.dispose();
		let result = 255 !== uint8Array[0] || 255 !== uint8Array[1] || 255 !== uint8Array[2] || 255 !== uint8Array[3];
		rctx.bindFramebuffer(textureDescriptor);
		maybe.disposeMaybe(program);
		return result;
	}

	function hackRoute(route, FeatureSet)
	{
		TF.smartOverride(route, 'solve', function(base)
		{
			arguments = Array.from(arguments);
			arguments.splice(0, 1);
			let params = arguments[1],
				originPolygonBarriers = params.polygonBarriers?.features,
				filtered = filterPolygonBarriers(params),
				changed = originPolygonBarriers?.length !== filtered?.length;
			if (changed)
			{
				params.polygonBarriers = new FeatureSet({ features: filtered });
			}

			return base.apply(this, arguments).then(result =>
			{
				if (!changed)
				{
					return result;
				}

				let routeGeometry = result.routeResults[0]?.route?.geometry;
				if (!routeGeometry)
				{
					return result;
				}

				let intersected = originPolygonBarriers.some(i =>
				{
					if (filtered.indexOf(i) == -1)
					{
						return tf.map.ArcGIS.geometryEngine.intersects(tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(i.geometry), routeGeometry);
					}

					return false;
				});

				if (intersected)
				{
					params.polygonBarriers.features = originPolygonBarriers;
					return base.apply(this, arguments);
				}

				return result;
			});
		});
	}

	function filterPolygonBarriers(params, nofilterMaxCount = 10, bufferSize = 4000)
	{
		let polygonBarriers = params.polygonBarriers?.features,
			stops = params.stops.features;
		var uniqueDic = {};
		var uniquePaths = [];
		stops.forEach(stop => 
		{
			var key = [stop.geometry.x, stop.geometry.y].toString();
			if (!uniqueDic[key])
			{
				uniquePaths.push([stop.geometry.x, stop.geometry.y]);
				uniqueDic[key] = true;
			}
		});

		if (uniquePaths.length <= 1 || !polygonBarriers || polygonBarriers.length <= nofilterMaxCount)
		{
			return polygonBarriers;
		}

		let spatialReference = stops[0].geometry.spatialReference,
			stopsShape;
		if (uniquePaths.length == 2)
		{
			stopsShape = new tf.map.ArcGIS.Polyline({
				paths: uniquePaths,
				spatialReference: spatialReference
			});
		}
		else
		{
			stopsShape = new tf.map.ArcGIS.Polygon({
				rings: [uniquePaths],
				spatialReference: spatialReference
			});
		}

		if (spatialReference.wkid == 4326)
		{
			stopsShape = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(stopsShape);
		}

		var stopsPolygonBuffer = new tf.map.ArcGIS.geometryEngine.buffer(stopsShape, bufferSize, "meters");
		stopsPolygonBuffer = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(stopsPolygonBuffer);
		return polygonBarriers.filter(barrier => tf.map.ArcGIS.geometryEngine.intersects(barrier.geometry, stopsPolygonBuffer));
	}

	function hackGeoprocessor(geoprocessor)
	{
		TF.smartOverride(geoprocessor, 'submitJob', function(base)
		{
			if (TF.RoutingMap.MapEditSaveHelper.isHandledService(this.url))
			{
				TF.RoutingMap.MapEditSaveHelper.setSaving(true);
			}

			arguments = Array.from(arguments);
			arguments.splice(0, 1);
			return base.apply(this, arguments);
		});
	}

	BaseMap.prototype._hackdeclared = function(declared)
	{
		declared.declared = function(d)
		{
			for (var f = [], h = 1; h < arguments.length; h++)
				f[h - 1] = arguments[h];
			h = function()
			{
				if (this.initss)
				{
					this.initss()
				}
				return this || {}
			};
			h.__bases__ = [d].concat(f);
			return h
		}
	};

	/**
	* hack search to fix reverse geocode N\A bug for ES API 4.14
	*/
	BaseMap.prototype._hackSearch = function(Search)
	{
		Search.prototype.search = function(a)
		{
			var b = this;
			this.activeMenu = "none";
			this._cancelSuggest();
			return this.viewModel.search(a).catch(function(a)
			{
				b.activeMenu = "none";
				return a
			}).then(function(a)
			{
				b.activeMenu = a.numResults ? "none" : "warning";
				return a
			})
		}
	}

	function hackGeometryEngine(geometryEngine)
	{
		TF.smartOverride(geometryEngine, 'union', function(base)
		{
			arguments = Array.from(arguments);
			arguments.splice(0, 1);
			if ($.isArray(arguments[0]))
			{
				if (arguments[0].length == 0)
				{
					return null;
				}
			} else
			{
				if (!arguments[0] && !arguments[1])
				{
					return null
				}
			}
			return base.apply(this, arguments);
		});
		TF.smartOverride(geometryEngine, 'intersect', function(base)
		{
			arguments = Array.from(arguments);
			arguments.splice(0, 1);
			if ($.isArray(arguments[0]))
			{
				if (arguments[0].length == 0)
				{
					return [];
				}
			} else
			{
				if (!arguments[0] || !arguments[1])
				{
					return [null]
				}
			}
			return base.apply(this, arguments);
		});

		//this method is called frequently, so not use TF.smartOverride for improving performance
		let originIntersects = geometryEngine.intersects;
		geometryEngine.intersects = function(geometry1, geometry2)
		{
			if (!geometry1 || !geometry2)
			{
				return false;
			}

			return originIntersects.call(this, geometry1, geometry2);
		};
	}

	/**
	* hack flare cluster layer bug for ES API 4.14
	*/
	BaseMap.prototype._hackFlareClusterLayer = function(FlareClusterLayer)
	{
		FlareClusterLayer.prototype._viewPointerMove = function(evt) 
		{
			// Hack code, viewfinder don't need these events.
			var _this = this;
			var mousePos = this._getMousePos(evt);
			// if there's an active cluster and the current screen pos is within the bounds of that cluster's group container, don't do anything more. 
			// TODO: would probably be better to check if the point is in the actual circle of the cluster group and it's flares instead of using the rectanglar bounding box.
			if (this._activeCluster && this._activeCluster.clusterGroup)
			{
				var bbox = this._activeCluster.clusterGroup.getBoundingClientRect();
				if (bbox)
				{
					if (mousePos.x >= bbox.left && mousePos.x <= bbox.right && mousePos.y >= bbox.top && mousePos.y <= bbox.bottom)
						return;
				}
			}
			if (!this._activeView.ready)
				return;
			var hitTest = this._activeView.hitTest(mousePos);
			if (!hitTest)
				return;
			hitTest.then(function(response)
			{
				var graphics = response.results;
				if (graphics.length === 0)
				{
					_this._deactivateCluster();
					return;
				}
				for (var i = 0, len = graphics.length; i < len; i++)
				{
					var g = graphics[i].graphic;
					// The code 'g.layer.id == _this.id' was added code
					if (g && g.layer && g.layer.id == _this.id && (g.attributes.clusterId != null && !g.attributes.isClusterArea))
					{
						var cluster = _this._clusters[g.attributes.clusterId];
						_this._activateCluster(cluster);
						return;
					}
					else
					{
						_this._deactivateCluster();
					}
				}
			});
		}

		FlareClusterLayer.prototype._createSingle = function(obj)
		{
			var point = new tf.map.ArcGIS.Point({
				// Hack code, fixed the code bug, the point maybe different spatial reference
				x: obj[this.xPropertyName], y: obj[this.yPropertyName], z: obj[this.zPropertyName], spatialReference: this.spatialReference
			});
			if (!point.spatialReference.isWebMercator)
			{
				point = webMercatorUtils.geographicToWebMercator(point);
			}
			var graphic = new tf.map.ArcGIS.Graphic({
				geometry: point,
				attributes: obj
			});
			graphic.popupTemplate = this.singlePopupTemplate;
			if (this.singleRenderer)
			{
				var symbol = this.singleRenderer.getSymbol(graphic, this._activeView);
				graphic.symbol = symbol;
			}
			else if (this.singleSymbol)
			{
				graphic.symbol = this.singleSymbol;
			}
			else
			{
				// no symbology for singles defined, use the default symbol from the cluster renderer
				graphic.symbol = this.clusterRenderer.defaultSymbol;
			}
			this.add(graphic);
		};

		// Hack code, new cluster symbol logic, merge the text symbol and old cluster symbol to new cluster symbol(CIMSymbol).
		FlareClusterLayer.prototype._createClusterSymbol = function(clusterSymbol, textSymbol, clusterCount)
		{
			let circle = {
				frame: { xmin: 0.0, ymin: 0.0, xmax: 17.0, ymax: 17.0 },
				rings: [
					[
						[8.5, 0.2],
						[7.06, 0.33],
						[5.66, 0.7],
						[4.35, 1.31],
						[3.16, 2.14],
						[2.14, 3.16],
						[1.31, 4.35],
						[0.7, 5.66],
						[0.33, 7.06],
						[0.2, 8.5],
						[0.33, 9.94],
						[0.7, 11.34],
						[1.31, 12.65],
						[2.14, 13.84],
						[3.16, 14.86],
						[4.35, 15.69],
						[5.66, 16.3],
						[7.06, 16.67],
						[8.5, 16.8],
						[9.94, 16.67],
						[11.34, 16.3],
						[12.65, 15.69],
						[13.84, 14.86],
						[14.86, 13.84],
						[15.69, 12.65],
						[16.3, 11.34],
						[16.67, 9.94],
						[16.8, 8.5],
						[16.67, 7.06],
						[16.3, 5.66],
						[15.69, 4.35],
						[14.86, 3.16],
						[13.84, 2.14],
						[12.65, 1.31],
						[11.34, 0.7],
						[9.94, 0.33],
						[8.5, 0.2]
					]
				]
			};
			let color = clusterSymbol.color, outlineColor = clusterSymbol.outline.color, textFont = textSymbol.font, textColor = textSymbol.color;
			return {
				type: "cim",
				data: {
					type: "CIMSymbolReference",
					symbol: {
						type: "CIMPointSymbol",
						symbolLayers: [
							{
								type: "CIMVectorMarker",
								enable: true,
								size: textFont.size,
								frame: { xmin: -5, ymin: -5, xmax: 5, ymax: 5 },
								markerGraphics: [
									{
										type: "CIMMarkerGraphic",
										geometry: { x: 0, y: 0 },
										symbol: {
											type: "CIMTextSymbol",
											fontFamilyName: textFont.family,
											fontStyleName: textFont.weight,
											height: 8,
											horizontalAlignment: "Center",
											offsetX: 0,
											offsetY: 0,
											symbol: {
												type: "CIMPolygonSymbol",
												symbolLayers: [
													{
														type: "CIMSolidFill",
														enable: true,
														color: [textColor.r, textColor.g, textColor.b, textColor.a * 255]
													}
												]
											},
											verticalAlignment: "Center"
										},
										textString: clusterCount
									}
								],
								scaleSymbolsProportionally: true,
								respectFrame: true
							},
							{
								type: "CIMVectorMarker",
								enable: true,
								size: clusterSymbol.size,
								frame: circle.frame,
								markerGraphics: [
									{
										type: "CIMMarkerGraphic",
										geometry: {
											rings: circle.rings
										},
										symbol: {
											type: "CIMPolygonSymbol",
											symbolLayers: [
												{
													type: "CIMSolidFill",
													enable: true,
													width: clusterSymbol.size,
													color: [color.r, color.g, color.b, color.a * 255]
												},
												{
													type: "CIMSolidStroke",
													width: clusterSymbol.outline.width * 1.7,
													color: [outlineColor.r, outlineColor.g, outlineColor.b, outlineColor.a * 255]
												}
											]
										}
									}
								],
								scaleSymbolsProportionally: true,
								respectFrame: true
							}
						]
					}
				}
			};
		};

		FlareClusterLayer.prototype._createCluster = function(gridCluster)
		{
			return __awaiter(this, void 0, void 0, function()
			{
				var cluster, point, attributes, cbi, textSymbol, mp, area, areaAttr, areaPoly, _a;
				var _this = this;
				return __generator(this, function(_b)
				{
					switch (_b.label)
					{
						case 0:
							cluster = {};
							cluster.gridCluster = gridCluster;
							// Hack code, fixed the code bug, the point maybe different spatial reference
							point = new tf.map.ArcGIS.Point({ x: gridCluster.x, y: gridCluster.y, spatialReference: this.spatialReference });
							if (!point.spatialReference.isWebMercator)
							{
								point = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(point);
							}
							attributes = {
								x: gridCluster.x,
								y: gridCluster.y,
								clusterCount: gridCluster.clusterCount,
								isCluster: true,
								clusterObject: gridCluster
							};
							cluster.clusterGraphic = new tf.map.ArcGIS.Graphic({
								attributes: attributes,
								geometry: point
							});
							return [4 /*yield*/, this.clusterRenderer.getClassBreakInfo(cluster.clusterGraphic)];
						case 1:
							cbi = _b.sent();
							// Hack code, merge the text symbol and cluster symbol to CIMSymbol
							cluster.clusterGraphic.symbol = this._createClusterSymbol(cbi.symbol, this.textSymbol, gridCluster.clusterCount.toString());
							if (this._is2d && this._activeView.rotation)
							{
								cluster.clusterGraphic.symbol["angle"] = 360 - this._activeView.rotation;
							}
							else
							{
								cluster.clusterGraphic.symbol["angle"] = 0;
							}
							cluster.clusterId = cluster.clusterGraphic["uid"];
							cluster.clusterGraphic.attributes.clusterId = cluster.clusterId;
							if (!(this.clusterAreaDisplay && gridCluster.points && gridCluster.points.length > 0)) return [3 /*break*/, 3];
							// Hack code, fixed the code bug, the point maybe different spatial reference
							mp = new tf.map.ArcGIS.Multipoint({ spatialReference: this.spatialReference });
							mp.points = gridCluster.points;
							area = tf.map.ArcGIS.geometryEngine.convexHull(mp, true);
							areaAttr = {
								x: gridCluster.x,
								y: gridCluster.y,
								clusterCount: gridCluster.clusterCount,
								clusterId: cluster.clusterId,
								isClusterArea: true
							};
							if (!(area.rings && area.rings.length > 0)) return [3 /*break*/, 3];
							// Hack code, fixed the code bug, the point maybe different spatial reference
							areaPoly = new tf.map.ArcGIS.Polygon({ spatialReference: this.spatialReference });
							areaPoly = areaPoly.addRing(area.rings[0]);
							if (!areaPoly.spatialReference.isWebMercator)
							{
								areaPoly = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(areaPoly);
							}
							cluster.areaGraphic = new tf.map.ArcGIS.Graphic({ geometry: areaPoly, attributes: areaAttr });
							_a = cluster.areaGraphic;
							return [4 /*yield*/, this.areaRenderer.getClassBreakInfo(cluster.areaGraphic)];
						case 2:
							_a.symbol = (_b.sent()).symbol;
							_b.label = 3;
						case 3:
							// add the graphics in order        
							if (cluster.areaGraphic && this.clusterAreaDisplay === "always")
							{
								this.add(cluster.areaGraphic);
							}
							this.add(cluster.clusterGraphic);
							// Hack code, remove the textGraphic
							this._clusters[cluster.clusterId] = cluster;
							return [2 /*return*/];
					}
				});
			});
		};

		FlareClusterLayer.prototype._scale = function()
		{
			// Hack code, if the map view zoom is 22 or 23, don't cluster.
			if (this._activeView.zoom === 22 || this._activeView.zoom === 23)
			{
				return undefined;
			}

			return this._activeView ? this._activeView.scale : undefined;
		};

		// Hack code, new calculate cluster logic
		FlareClusterLayer.prototype._calculateCluster = function(point, cluster)
		{
			let distance = (
				Math.sqrt(
					Math.pow((cluster.x - point.x), 2) + Math.pow((cluster.y - point.y), 2)
				) / this._clusterResolution
			);

			return (distance <= this.clusterRatio);
		};

		// Hack code, new calculate cluster logic
		FlareClusterLayer.prototype._isSamePoint = function(point, cluster)
		{
			let clusterX = Math.round(cluster.x * 10000) / 10000, clusterY = Math.round(cluster.y * 10000) / 10000,
				pointX = Math.round(point.x * 10000) / 10000, pointY = Math.round(point.y * 10000) / 10000;

			return clusterX === pointX && clusterY === pointY;
		};

		FlareClusterLayer.prototype.draw = function(activeView, drawData)
		{
			var _this = this;
			if (activeView)
			{
				// if we're swapping views from the currently active one, clear the surface object so it get's recreated fresh after the first draw
				if (this._activeView && activeView !== this._activeView)
				{
					this._activeView.fclSurface = null;
				}
				this._activeView = activeView;
			}
			// Not ready to draw yet so queue one up
			if (!this._readyToDraw)
			{
				this._queuedInitialDraw = true;
				return;
			}

			// Hack code, don't redraw the layer when drag the map
			if (!drawData && (this._PrevViewZoom && this._PrevViewZoom === this._activeView.zoom))
			{
				return;
			}
			else
			{
				this._PrevViewZoom = this._activeView.zoom;
				// Get the map resolution in current zoom
				this._clusterResolution = this._activeView.extent.width / this._activeView.width;
			}

			var currentExtent = this._extent();
			if (!this._activeView || !this._data || !currentExtent)
				return;
			this._is2d = this._activeView.type === "2d";
			// check for required renderer
			if (!this.clusterRenderer)
			{
				console.error("FlareClusterLayer: clusterRenderer must be set.");
			}
			// check to make sure we have an area renderer set if one needs to be
			if (this.clusterAreaDisplay && !this.areaRenderer)
			{
				console.error("FlareClusterLayer: areaRenderer must be set if clusterAreaDisplay is set.");
				return;
			}
			this.clear();

			let dataLength = this._data.length

			// console.time("draw-data-" + this._activeView.type);
			this._isClustered = this.clusterToScale < this._scale() && this.clusterMinCount <= dataLength;

			// Hack code, change the logic for draw cluster graphic and single graphic.
			// Step 1. Check point is near the exsit clusters.
			// Step 2. If yes, add point into exsit cluster, re-calculate the cluster x, y and extent; if no, create a new cluster then add point in it.
			// Step 3. Darw these cluster graphic and single graphic
			// TODO, use feature layer not graphic layer
			let gridClusters = [];
			let web, obj, xVal, yVal;
			for (let i = 0; i < dataLength; i++)
			{
				obj = this._data[i];
				// check if filters are specified and continue if this object doesn't pass
				if (!this._passesFilter(obj))
				{
					continue;
				}

				if (this._isClustered)
				{
					if (this.spatialReference.isWebMercator)
					{
						web = [obj[this.xPropertyName], obj[this.yPropertyName]];
					}
					else
					{
						web = tf.map.ArcGIS.webMercatorUtils.lngLatToXY([obj[this.xPropertyName], obj[this.yPropertyName]]);
					}

					xVal = web[0];
					yVal = web[1];

					let cluster;
					let clusterCount = gridClusters.length;
					for (let j = 0; j < clusterCount; j++)
					{
						cluster = gridClusters[j];
						if (this._calculateCluster({ x: xVal, y: yVal }, cluster))
						{
							break;
						}

						cluster = null;
					}

					if (!cluster)
					{
						cluster = {
							extent: {
								xmin: xVal,
								xmax: xVal,
								ymin: yVal,
								ymax: yVal
							},
							clusterCount: 0,
							subTypeCounts: [],
							singles: [],
							points: [],
							x: xVal,
							y: yVal,
							isSamePoint: true
						};

						gridClusters.push(cluster);
					}
					else
					{
						// recalc the x and y of the cluster by averaging the points again
						cluster.x = (xVal + (cluster.x * cluster.clusterCount)) / (cluster.clusterCount + 1);
						cluster.y = (yVal + (cluster.y * cluster.clusterCount)) / (cluster.clusterCount + 1);
						cluster.extent.xmin = xVal < cluster.extent.xmin ? xVal : cluster.extent.xmin;
						cluster.extent.xmax = xVal > cluster.extent.xmax ? xVal : cluster.extent.xmax;
						cluster.extent.ymin = yVal < cluster.extent.ymin ? yVal : cluster.extent.ymin;
						cluster.extent.ymax = yVal > cluster.extent.ymax ? yVal : cluster.extent.ymax;
						cluster.isSamePoint = cluster.isSamePoint ? this._isSamePoint({ x: xVal, y: yVal }, cluster) : cluster.isSamePoint;
					}

					// push every point into the cluster so we have it for area display if required. This could be omitted if never checking areas, or on demand at least
					if (this.clusterAreaDisplay)
					{
						cluster.points.push([xVal, yVal]);
					}
					cluster.clusterCount++;

					var subTypeExists = false;
					for (var s = 0, sLen = cluster.subTypeCounts.length; s < sLen; s++)
					{
						if (cluster.subTypeCounts[s].name === obj[this.subTypeFlareProperty])
						{
							cluster.subTypeCounts[s].count++;
							subTypeExists = true;
							break;
						}
					}
					if (!subTypeExists)
					{
						cluster.subTypeCounts.push({ name: obj[this.subTypeFlareProperty], count: 1 });
					}

					cluster.singles.push(obj);
				}
				else
				{
					// not clustered so just add every obj
					this._createSingle(obj);
				}
			}

			if (this._isClustered)
			{
				for (let i in gridClusters)
				{
					if (gridClusters[i].clusterCount === 1 || gridClusters[i].isSamePoint)
					{
						this._createSingle(gridClusters[i].singles[0]);
					}
					else if (gridClusters[i].clusterCount > 1)
					{
						this._createCluster(gridClusters[i]);
					}
				}
			}

			// emit an event to signal drawing is complete.
			this.emit("draw-complete", {});
			// console.timeEnd("draw-data-" + this._activeView.type);
			if (!this._activeView.fclSurface)
			{
				setTimeout(function()
				{
					_this._createSurface();
				}, 10);
			}
		};

		FlareClusterLayer.prototype.setData = function(data, drawData)
		{
			if (drawData === void 0) { drawData = true; }
			this._data = data;
			if (drawData)
			{
				this.draw(undefined, true);
			}
		};
	}


	/**
	* hack suggest to change suggest result
	*/
	BaseMap.prototype._hackSuggest = function(SearchViewModel)
	{
		SearchViewModel.prototype._getSuggestionsFromSource = function(a)
		{
			var self = this;
			var geoSearch = new TF.RoutingMap.RoutingPalette.GeoSearch(tf.map.ArcGIS, self.view.map, true);
			var d = this.suggestionsEnabled
				, f = a.trim().length
				, k = this.minSuggestCharacters;
			if (d && f >= k)
			{
				self._timeKey = (new Date()).getTime().toString();
				return (function(timeKey)
				{	//RW - 15914 Geocode: Address Point (if no match)→ Street
					return geoSearch.suggestAddressPoint(a).then(function(suggestions)
					{
						if (suggestions.length < 1)
						{
							return geoSearch.suggest(a).then(function(streetSuggestions)
							{
								if (timeKey == self._timeKey)
								{
									return streetSuggestions.map(function(suggest, i)
									{
										return {
											text: suggest.address,
											key: (new Date()).getTime().toString() + i,
											sourceIndex: 0,
											location: suggest.location,
											extent: suggest.extent
										};
									});
								}
								return [];

							});
						}
						else
						{
							if (timeKey == self._timeKey)
							{
								return suggestions.map(function(suggest, i)
								{
									return {
										text: suggest.address,
										key: (new Date()).getTime().toString() + i,
										sourceIndex: 0,
										location: suggest.location,
										extent: suggest.extent
									};
								});
							}
							return [];
						}
					});
				})(self._timeKey);
			}
			return Promise.resolve();
		};

		var oldGetResultsFromSources = SearchViewModel.prototype._getResultsFromSources;
		SearchViewModel.prototype._getResultsFromSources = function(e)
		{
			if (e.text && e.extent)
			{
				return Promise.resolve([{
					value: [{
						feature: {
							geometry: new tf.map.ArcGIS.Point({ x: e.location.x, y: e.location.y, spatialReference: { wkid: 102100 } })
						},
						extent: e.extent,
						key: e.key,
						name: e.text,
						sourceIndex: e.sourceIndex
					}]
				}]);
			} else if (e.text)
			{
				var geoSearch = new TF.RoutingMap.RoutingPalette.GeoSearch(tf.map.ArcGIS, this.view.map, true);
				geoSearch.initSuggest();
				return geoSearch.suggestResults([{ text: e.text }], e.text).then(function(suggestions)
				{
					return [{
						value: suggestions.map(function(suggest)
						{
							return {
								feature: {
									geometry: new tf.map.ArcGIS.Point({ x: suggest.location.x, y: suggest.location.y, spatialReference: { wkid: 102100 } })
								},
								extent: suggest.extent,
								key: (new Date()).getTime().toString(),
								name: suggest.address,
								sourceIndex: 0
							};
						})
					}];
				});
			} else
			{
				return oldGetResultsFromSources.call(this, e);
			}
		};
	};

	/**
	* add snap function for reshape
	*/
	BaseMap.prototype._hackDragHandler = function(GraphicMover, GraphicMoverEvents, drawUtils, lang)
	{
		GraphicMover.prototype._dragHandler = function(a)
		{
			var b = this;
			if (("start" === a.action || this._dragEvent) && this._activeGraphic && this._activeGraphic.geometry)
			{
				a.stopPropagation();
				var c = a.x
					, d = a.y
					, h = this.graphics.indexOf(this._activeGraphic)
					, f = this._activeGraphic.geometry
					, e = this._dragEvent ? c - this._dragEvent.x : 0
					, g = this._dragEvent ? d - this._dragEvent.y : 0
					, l = c - a.origin.x
					, m = d - a.origin.y;
				// get snap graphic
				var snapPoint = getSnapPoint(this.view.map);
				if (snapPoint && f.type == "point")
				{
					this._activeGraphic.geometry = snapPoint;
				} else
				{
					this._activeGraphic.geometry = this._dragEvent && f.type == "point" ? this.view.toMap(this._dragEvent) : drawUtils.cloneMove(f, e, g, this.view);
				}
				this.enableMoveAllGraphics && this.graphics.forEach(function(a)
				{
					a !== b._activeGraphic && (a.geometry = drawUtils.cloneMove(a.geometry, e, g, b.view))
				});
				this._dragEvent = a;
				"start" === a.action ? (this._initialDragGeometry = lang.clone(f),
					a = new GraphicMoverEvents.GraphicMoveStartEvent(this._activeGraphic, this.graphics, h, c, d, e, g, l, m, a),
					this.emit("graphic-move-start", a),
					this.callbacks.onGraphicMoveStart && this.callbacks.onGraphicMoveStart(a),
					a.defaultPrevented && this._activeGraphic.set("geometry", f)) : "update" === a.action ? (a = new GraphicMoverEvents.GraphicMoveEvent(this._activeGraphic, this.graphics, h, c, d, e, g, l, m, a),
						this.emit("graphic-move", a),
						this.callbacks.onGraphicMove && this.callbacks.onGraphicMove(a),
						a.defaultPrevented && this._activeGraphic.set("geometry", f)) : (this._dragEvent = null,
							a = new GraphicMoverEvents.GraphicMoveStopEvent(this._activeGraphic, this.graphics, h, c, d, e, g, l, m, a),
							this.emit("graphic-move-stop", a),
							this.callbacks.onGraphicMoveStop && this.callbacks.onGraphicMoveStop(a),
							a.defaultPrevented && this.graphics[h].set("geometry", this._initialDragGeometry),
							this._initialDragGeometry = null)
			}
		};
	};

	function hackSnap(DrawOperation)
	{
		TF.smartOverride(DrawOperation.DrawOperation.prototype, '_processCursor', function(base, point)
		{
			let snapPoint = getSnapPoint(this.view.map) ?? point;
			base.call(this, snapPoint);
		});
	}

	function getSnapPoint(map)
	{
		var snapSymbolLayer = map.findLayerById("snapSymbolLayerId");
		if (snapSymbolLayer && snapSymbolLayer.graphics.items.length > 0 && snapSymbolLayer.graphics.items[0].visible && snapSymbolLayer.graphics.items[0].geometry)
		{
			return snapSymbolLayer.graphics.items[0].geometry;
		}
	}

	BaseMap.prototype._hackGraphicProcessingQueue = function(RenderingCore2D)
	{
		var GraphicProcessingQueue = RenderingCore2D.GraphicProcessingQueue;
		GraphicProcessingQueue.prototype._queueArray = [];

		GraphicProcessingQueue.prototype._next = function()
		{
			if (null == this._scheduledNextHandle || 0 === this._queue.size || this._onGoingGraphic)
				this._scheduledNextHandle = null;
			else
			{
				this._scheduledNextHandle = null;
				var a = this._peek()
					, b = a.graphic
					, c = a.addOrUpdate;
				this._queue.delete(b);
				if (this._queueArray.length > 0) this._queueArray.length = this._queueArray.length - 1;
				this._onGoingGraphic = a;
				this._onGoingPromise = this.process(b, c, this._timestamp);
				this._onGoingPromise.then(this._finalize, this._finalize);
				this.notifyChange("updating")
			}
		}
		GraphicProcessingQueue.prototype._peek = function()
		{
			var c = this._queueArray[this._queueArray.length - 1];
			return c;
		}
		GraphicProcessingQueue.prototype.push = function(a, b, c)
		{
			this._queue.has(a) || (this._queue.set(a, {
				graphic: a,
				addOrUpdate: b,
				timestamp: c || this._timestamp
			}), this._queueArray.push({
				graphic: a,
				addOrUpdate: b,
				timestamp: c || this._timestamp
			}),
				this._scheduleNext(),
				this.notifyChange("updating"))
		}
		GraphicProcessingQueue.prototype.initss = function()
		{
			this._queueArray = [];
		}
	};

	BaseMap.prototype.onLayersAddResult = function(callback, errback)
	{
		this.map.on("layers-add-result", callback, errback);
	};

	/**
	* Release resources.
	*/
	BaseMap.prototype.dispose = function()
	{
		this.map.removeAllLayers();
		this.map.destroy();

		// release the objects
		for (var i in this)
		{
			this[i] = null;
		}
	};

})();

(function()
{
	createNamespace("TF.Map").MapBehaviorSubject = MapBehaviorSubject;

	function MapBehaviorSubject()
	{
		this._progressComplete = false;
		this._isNotTimeout = true;
		this._progressCompleteCallBack = [];
	}

	MapBehaviorSubject.prototype.setProgressComplete = function()
	{
		this._progressComplete = true;
		this._progressCompleteCallBack.forEach(callBack => callBack(true));
		this._progressCompleteCallBack = [];
	}

	MapBehaviorSubject.prototype.isProgressComplete = function()
	{
		return this._progressComplete;
	}

	MapBehaviorSubject.prototype.subscribe = function(callBack, showLoading)
	{
		let callBackFunc = callBack;
		if (showLoading)
		{
			tf.loadingIndicator.show();
			callBackFunc = () =>
			{
				tf.loadingIndicator.tryHide();
				callBack && callBack();
			};
		}

		if (this._progressComplete)
		{
			callBackFunc();
		}
		else
		{
			this._progressCompleteCallBack.push(callBackFunc);
		}
	}

	MapBehaviorSubject.prototype.waitComplete = function(showLoading, timeout = 0)
	{
		showLoading && tf.loadingIndicator.show();
		const promises = [new Promise((resolve) =>
		{
			const completeFunc = function()
			{
				resolve(true);
			};

			if (this._progressComplete)
			{
				completeFunc();
			}
			else
			{
				this._progressCompleteCallBack.push(completeFunc);
			}
		})];

		if (timeout > 0 && this._progressComplete === false)
		{
			promises.push(this._isNotTimeout
				? new Promise((resolve, _) => setTimeout(() =>
				{
					this._isNotTimeout = false;
					resolve(false);
				}, timeout))
				: new Promise((resolve, _) => resolve(false))
			);

		}

		return Promise.race(promises).finally(() => showLoading && tf.loadingIndicator.tryHide());
	}

})();