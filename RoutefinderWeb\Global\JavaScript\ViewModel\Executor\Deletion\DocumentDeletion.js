﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DocumentDeletion = DocumentDeletion;

	function DocumentDeletion()
	{
		this.type = 'document';
		namespace.BaseDeletion.apply(this, arguments);
	}

	DocumentDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	DocumentDeletion.prototype.constructor = DocumentDeletion;

	DocumentDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), { 
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@filter": "in(DocumentID," + ids.toString() + ")"
			}
		}).then(function(response)
		{
			for (var key in Array.groupBy(response.Items, "AttachedToType"))
			{
				if (response.Items[key].length > 0)
				{
					associatedDatas.push({
						type: tf.dataTypeHelper.getKeyById(key),
						items: response.Items[key]
					});
				}
			}
		});
		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DocumentDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		var p0 = this.getDataPermission(ids, "document", "documentrelationship");

		return Promise.all([p0]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	}
})();