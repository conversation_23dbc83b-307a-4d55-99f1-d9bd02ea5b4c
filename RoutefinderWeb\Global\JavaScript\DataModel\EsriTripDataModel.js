﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.EsriTripDataModel = function(tripEntity)
	{
		namespace.BaseDataModel.call(this, tripEntity);
	}

	namespace.EsriTripDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.EsriTripDataModel.prototype.constructor = namespace.EsriTripDataModel;

	namespace.EsriTripDataModel.prototype.mapping = [
		{ from: "ActivityTrip", default: false },
		{ from: "AideId", default: 0 },
		{ from: "Comments", default: "" },
		{ from: "Cost", default: 0 },
		{ from: "Day", default: 0 },
		{ from: "Description", default: "" },
		{ from: "Dhdistance", to: "deadheadDistance", default: 0 },
		{ from: "Disabled", default: false },
		{ from: "Distance", default: 0 },
		{ from: "DriverId", default: 0 },
		{ from: "FilterName", default: "" },
		{ from: "FilterSpec", default: "" },
		{ from: "FinishTime", default: "1970-01-01T00:00:00" },
		{ from: "GPSEnabledFlag", default: false },
		{ from: "Guid", default: "" },
		{ from: "HasBusAide", default: false },
		{ from: "HomeSchl", default: true },
		{ from: "HomeTrans", default: false },
		{ from: "Id", default: 0, required: true },
		{ from: "IDescription", default: "" },
		{ from: "IName", default: "" },
		{ from: "IntGratChar1", default: "" },
		{ from: "IntGratChar2", default: "" },
		{ from: "IntGratDate1", default: null },
		{ from: "IntGratDate2", default: null },
		{ from: "IntGratNum1", default: 0 },
		{ from: "IntGratNum2", default: 0 },
		{ from: "IShow", default: false },
		{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
		{ from: "LastUpdatedId", default: 0 },
		{ from: "LastUpdatedName", default: "" },
		{ from: "LastUpdatedType", default: 0 },
		{ from: "MaxOnBus", default: 0 },
		{ from: "Name", default: "" },
		{ from: "NonDisabled", default: true },
		{ from: "NumTransport", default: 0 },
		{ from: "Rsyslockid", default: 0 },
		{ from: "Schools", default: "" },
		{ from: "Session", default: 0 },
		{ from: "Shuttle", default: false },
		{ from: "Sifchanged", default: 0 },
		{ from: "StartTime", default: "1970-01-01T00:00:00" },
		{ from: "System1", default: "" },
		{ from: "System2", default: "" },
		{ from: "System3", default: "" },
		{ from: "System4", default: "" },
		{ from: "TripAlias", default: "" },
		{ from: "VehicleId", default: 0 },
		{ from: "TripStops", default: [], subDataModelType: namespace.EsriTripStopDataModel },
		{ from: "TripId", default: -1 },
		{ from: 'StopId', default: -1 }
	];
})();
