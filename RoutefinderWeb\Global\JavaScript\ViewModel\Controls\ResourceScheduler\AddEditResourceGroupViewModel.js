﻿(function()
{
	createNamespace('TF.Control.ResourceScheduler').AddEditResourceGroupViewModel = AddEditResourceGroupViewModel;

	function AddEditResourceGroupViewModel(resourceGroupEntity, drivers, aides, vehicles, fieldTripId, assignedResGroups)
	{
		this.obDrivers = ko.observable([]);
		this.obAides = ko.observable([]);
		this.obVehicles = ko.observable([]);
		this.conflictAideIds = [];
		this.conflictDriverIds = [];
		this.conflictVehicleIds = [];
		this.resourceGroupEntity = resourceGroupEntity;
		this.afterAidesSelect = this.afterAidesSelect.bind(this);
		this.afterDriversSelect = this.afterDriversSelect.bind(this);
		this.afterVehiclesSelect = this.afterVehiclesSelect.bind(this);
		if (Array.isArray(resourceGroupEntity))
		{
			this.initForMulti(resourceGroupEntity, drivers, aides, vehicles);
		} else
		{
			this.initForSingle(resourceGroupEntity, drivers, aides, vehicles, fieldTripId, assignedResGroups);
		}
	}
	AddEditResourceGroupViewModel.prototype.apply = function()
	{
		return null;
	};
	AddEditResourceGroupViewModel.prototype.initForSingle = function(resourceGroupEntity, drivers, aides, vehicles, fieldTripId, assignedResGroups)
	{
		let assignedDriverIds = [], assignedAideIds = [], assignedVehicleIds = [];
		if (Array.isArray(assignedResGroups))
		{
			assignedResGroups.forEach(x =>
			{
				assignedDriverIds.push(x.DriverId);
				assignedAideIds.push(x.AideId);
				assignedVehicleIds.push(x.VehicleId);
			});
		}
		let selectedDriverId = parseInt(resourceGroupEntity && resourceGroupEntity.DriverId) || 0,
			selectedAideId = parseInt(resourceGroupEntity && resourceGroupEntity.AideId) || 0,
			selectedVehicleId = parseInt(resourceGroupEntity && resourceGroupEntity.VehicleId) || 0;
		this.resourceGroupEntity = resourceGroupEntity;
		this.setSelectedItem(selectedDriverId, selectedAideId, selectedVehicleId, drivers, aides, vehicles);
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'conflictresschls'), {
			paramData: {
				databaseId: tf.api.datasourceManager.databaseId,
				id: fieldTripId,
				type: "FieldTrip"
			}
		}).then(function(conflictRes)
		{
			var conflictDriverIds = [];
			var conflictAideIds = [];
			var conflictVehicleIds = [];
			conflictRes.Items.forEach(function(item)
			{
				conflictDriverIds.push(item.DriverId);
				conflictDriverIds.push(item.AideId);
				conflictAideIds.push(item.AideId);
				conflictAideIds.push(item.DriverId);
				conflictVehicleIds.push(item.VehicleId);
			});
			conflictDriverIds.push(selectedAideId);
			conflictAideIds.push(selectedDriverId);
			this.conflictAideIds = conflictAideIds;
			this.conflictDriverIds = conflictDriverIds;
			this.conflictVehicleIds = conflictVehicleIds;
			this.obDrivers(this.ProduceDatasourceArr(drivers.filter(d => d.DriverId == selectedDriverId || assignedDriverIds.indexOf(d.DriverId) == -1), "DriverId", "DriverName", conflictDriverIds));
			this.obAides(this.ProduceDatasourceArr(aides.filter(d => d.AideId == selectedAideId || assignedAideIds.indexOf(d.AideId) === -1), "AideId", "AideName", conflictAideIds));
			this.obVehicles(this.ProduceDatasourceArr(vehicles.filter(d => d.VehicleId == selectedVehicleId || assignedVehicleIds.indexOf(d.VehicleId) === -1), "VehicleId", "VehicleName", conflictVehicleIds));
		}.bind(this))
			.catch(function()
			{
				this.obDrivers(this.ProduceDatasourceArr(drivers, "DriverId", "DriverName", []));
				this.obAides(this.ProduceDatasourceArr(aides, "AideId", "AideName", []));
				this.obVehicles(this.ProduceDatasourceArr(vehicles, "VehicleId", "VehicleName", []));
			}.bind(this));
		this.apply = function()
		{
			let updatedEntity = $.extend({}, this.resourceGroupEntity, {
				DriverId: this.obSelectedDriver() ? this.obSelectedDriver().DriverId : null,
				AideId: this.obSelectedAide() ? this.obSelectedAide().AideId : null,
				VehicleId: this.obSelectedVehicle() ? this.obSelectedVehicle().VehicleId : null
			});
			return Promise.resolve(updatedEntity);
		}.bind(this);
	}
	AddEditResourceGroupViewModel.prototype.initForMulti = function(resourceGroupEntities, drivers, aides, vehicles)
	{
		var curDrivers = [], curAides = [], curVehicles = [];
		resourceGroupEntities.forEach(function(entity)
		{
			if (curDrivers.indexOf(entity.DriverId) === -1)
			{
				curDrivers.push(entity.DriverId);
			}
			if (curAides.indexOf(entity.AideId) === -1)
			{
				curAides.push(entity.AideId);
			}
			if (curVehicles.indexOf(entity.VehicleId) === -1)
			{
				curVehicles.push(entity.VehicleId);
			}
		});

		var driverDs = [], aideDs = [], vehicleDs = [], selectedDriverId = 0, selectedAiderId = 0, selectedVehicleId = 0;
		if (curDrivers.length === 1)
		{
			selectedDriverId = curDrivers[0] || 0;
		} else if (curDrivers.length > 1)
		{
			selectedDriverId = -1;
			driverDs.push({ DriverId: -1, DriverName: '(Multiple Values Exist)' });
		}
		driverDs.push({ DriverId: 0, DriverName: '(none)' });
		Array.prototype.push.apply(driverDs, drivers);
		if (curAides.length === 1)
		{
			selectedAiderId = curAides[0] || 0;
		} else if (curAides.length > 1)
		{
			selectedAiderId = -1;
			aideDs.push({ AideId: -1, AideName: '(Multiple Values Exist)' });
		}
		aideDs.push({ AideId: 0, AideName: '(none)' });
		Array.prototype.push.apply(aideDs, aides);

		if (curVehicles.length === 1)
		{
			selectedVehicleId = curVehicles[0] || 0;
		} else if (curVehicles.length > 1)
		{
			selectedVehicleId = -1;
			vehicleDs.push({ VehicleId: -1, VehicleName: '(Multiple Values Exist)' });
		}
		vehicleDs.push({ VehicleId: 0, VehicleName: '(none)' });
		Array.prototype.push.apply(vehicleDs, vehicles);

		this.obDrivers = ko.observable(driverDs);
		this.obAides = ko.observable(aideDs);
		this.obVehicles = ko.observable(vehicleDs);
		this.setSelectedItem(selectedDriverId, selectedAiderId, selectedVehicleId, driverDs, aideDs, vehicleDs);
		var self = this;
		this.apply = function()
		{
			return Promise.resolve(resourceGroupEntities.map(function(entity)
			{
				entity.DriverId = self.obSelectedDriver() && (self.obSelectedDriver().DriverId || null) || null;
				entity.AideId = self.obSelectedAide() && (self.obSelectedAide().AideId || null) || null;
				entity.VehicleId = self.obSelectedVehicle() && (self.obSelectedVehicle().VehicleId || null) || null;

				return entity;
			}));
		};
	}

	AddEditResourceGroupViewModel.prototype.afterAidesSelect = function(selectValue)
	{
		const self = this, selectedAide = self.obSelectedAide();
		if (selectedAide.AideId !== 0 && self.conflictAideIds.indexOf(selectedAide.AideId) > -1)
		{			
			 tf.promiseBootbox.yesNo({
				message: "This Bus Aide is already scheduled for a Trip or Field Trip. Are you sure you want to assign this resource to this Field Trip?",
				title: "Confirmation"
			}).then(function(result)
			{
				if (!result)
				{
					self.obSelectedAide(null);
				}
			});
		}
	};

	AddEditResourceGroupViewModel.prototype.afterDriversSelect = function(selectValue)
	{
		const self = this, selectedDriver = self.obSelectedDriver();
		if (selectedDriver.DriverId !== 0 && self.conflictDriverIds.indexOf(selectedDriver.DriverId) > -1)
		{			
			 tf.promiseBootbox.yesNo({
				message: "This Driver is already scheduled for a Trip or Field Trip. Are you sure you want to assign this resource to this Field Trip?",
				title: "Confirmation"
			}).then(function(result)
			{
				if (!result)
				{
					self.obSelectedDriver(null);
				}
			});
		}
	};

	AddEditResourceGroupViewModel.prototype.afterVehiclesSelect = function(selectValue)
	{
		const self = this, selectedVehicle= self.obSelectedVehicle();
		if (selectedVehicle.VehicleId !== 0 && self.conflictVehicleIds.indexOf(selectedVehicle.VehicleId) > -1)
		{			
			 tf.promiseBootbox.yesNo({
				message: "This Vehicle is already scheduled for a Trip or Field Trip. Are you sure you want to assign this resource to this Field Trip?",
				title: "Confirmation"
			}).then(function(result)
			{
				if (!result)
				{
					self.obSelectedVehicle(null);
				}
			});
		}
	};

	AddEditResourceGroupViewModel.prototype.setSelectedItem = function(driverId, aideId, vehicleId, drivers, aides, vehicles)
	{
		var selectedDrivers, selectedAides, selectedVehicles;
		selectedDrivers = drivers.filter(function(d) { return d.DriverId === driverId; });
		this.obSelectedDriver = ko.observable(selectedDrivers && selectedDrivers.length > 0 ? selectedDrivers[0] : null);
		this.obSelectedDriverText = ko.computed(function() { return this.obSelectedDriver() == null ? "" : this.obSelectedDriver().DriverName; }.bind(this));

		selectedAides = aides.filter(function(d) { return d.AideId === aideId; });
		this.obSelectedAide = ko.observable(selectedAides && selectedAides.length > 0 ? selectedAides[0] : null);
		this.obSelectedAideText = ko.computed(function() { return this.obSelectedAide() == null ? "" : this.obSelectedAide().AideName; }.bind(this));

		selectedVehicles = vehicles.filter(function(d) { return d.VehicleId === vehicleId; });
		this.obSelectedVehicle = ko.observable(selectedVehicles && selectedVehicles.length > 0 ? selectedVehicles[0] : null);
		this.obSelectedVehicleText = ko.computed(function() { return this.obSelectedVehicle() == null ? "" : this.obSelectedVehicle().VehicleName; }.bind(this));
	}

	AddEditResourceGroupViewModel.prototype.ProduceDatasourceArr = function(sourceArr, keyField, displayField, excludeIds)
	{
		var res = this.SeperateDatasourceArr(sourceArr, keyField, excludeIds, conflictArray, conflictFreeArray, keyField),
			conflictArray = res[0], conflictFreeArray = res[1];

		var tempItem = {};
		tempItem[keyField] = 0;
		tempItem[displayField] = "(none)";
		var newArr = [];
		newArr.push(tempItem);
		if (conflictFreeArray.length > 0)
		{
			var tempItem = {};
			tempItem[keyField] = null;
			tempItem[displayField] = "[disable]-------No Conflicts-------";
			newArr.push(tempItem);
			Array.sortBy(conflictFreeArray, displayField);
			Array.prototype.push.apply(newArr, conflictFreeArray);
		}
		if (conflictArray.length > 0)
		{
			var tempItem = {};
			tempItem[keyField] = null;
			tempItem[displayField] = "[disable]-------Conflicts-------";
			newArr.push(tempItem);
			Array.sortBy(conflictArray, displayField);
			Array.prototype.push.apply(newArr, conflictArray);
		}
		return newArr;
	}
	AddEditResourceGroupViewModel.prototype.SeperateDatasourceArr = function(sourceArr, keyField, excludeIds)
	{
		var conflictArray = [];
		var conflictFreeArray = [];
		if (!Array.isArray(sourceArr) || !Array.isArray(excludeIds))
		{
			return [];
		}
		sourceArr.sort(function(a, b)
		{
			var aField = a[keyField], bField = b[keyField];
			if (aField == null && bField == null) return 0;

			if (aField == null) return -1;
			if (bField == null) return 1;
			if (aField == bField)
			{
				return 0;
			}
			return aField > bField ? 1 : -1;
		});
		excludeIds.sort((x, y) => x > y ? 1 : -1);
		for (var i = 0, j = 0; i < sourceArr.length;)
		{
			if (j == excludeIds.length)
			{
				conflictFreeArray.push(sourceArr[i]);
				i++;
				continue;
			}
			if (sourceArr[i][keyField] == excludeIds[j])
			{
				conflictArray.push(sourceArr[i]);
				i++;
				j++;
			} else if (sourceArr[i][keyField] < excludeIds[j])
			{
				conflictFreeArray.push(sourceArr[i]);
				i++;
			} else
			{
				j++;
			}
		}
		return [conflictArray, conflictFreeArray];
	}
})();
