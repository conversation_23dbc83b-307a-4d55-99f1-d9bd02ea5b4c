@ThemeColor: #D74B3C;

.rotate-animation {
	animation: rotate 0.5s infinite linear;
	-webkit-animation: rotate 0.5s infinite linear;
	-moz-animation: rotate 0.5s infinite linear;
	-ms-animation: rotate 0.5s infinite linear;
	-o-animation: rotate 0.5s infinite linear;

	@keyframes rotate {
		from {
			transform: rotate(0deg);
			-webkit-transform: rotate(0deg);
			-moz-transform: rotate(0deg);
			-ms-transform: rotate(0deg);
			-o-transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
			-webkit-transform: rotate(360deg);
			-moz-transform: rotate(360deg);
			-ms-transform: rotate(360deg);
			-o-transform: rotate(360deg);
		}
	}

	@-webkit-keyframes rotate {
		from {
			transform: rotate(0deg);
			-webkit-transform: rotate(0deg);
			-moz-transform: rotate(0deg);
			-ms-transform: rotate(0deg);
			-o-transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
			-webkit-transform: rotate(360deg);
			-moz-transform: rotate(360deg);
			-ms-transform: rotate(360deg);
			-o-transform: rotate(360deg);
		}
	}

	@-moz-keyframes rotate {
		from {
			transform: rotate(0deg);
			-webkit-transform: rotate(0deg);
			-moz-transform: rotate(0deg);
			-ms-transform: rotate(0deg);
			-o-transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
			-webkit-transform: rotate(360deg);
			-moz-transform: rotate(360deg);
			-ms-transform: rotate(360deg);
			-o-transform: rotate(360deg);
		}
	}

	@-ms-keyframes rotate {
		from {
			transform: rotate(0deg);
			-webkit-transform: rotate(0deg);
			-moz-transform: rotate(0deg);
			-ms-transform: rotate(0deg);
			-o-transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
			-webkit-transform: rotate(360deg);
			-moz-transform: rotate(360deg);
			-ms-transform: rotate(360deg);
			-o-transform: rotate(360deg);
		}
	}

	@-o-keyframes rotate {
		from {
			transform: rotate(0deg);
			-webkit-transform: rotate(0deg);
			-moz-transform: rotate(0deg);
			-ms-transform: rotate(0deg);
			-o-transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
			-webkit-transform: rotate(360deg);
			-moz-transform: rotate(360deg);
			-ms-transform: rotate(360deg);
			-o-transform: rotate(360deg);
		}
	}
}

.tabstrip-resourcescheduler {
	padding: 30px 20px 20px 21px;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	display: flex;
	flex-direction: column;
	font-family: "SourceSansPro-Regular";

	table th {
		font-weight: normal;
		border: none;
	}

	.scheduler-tf-layout {
		width: 100%;

		.scheduler-tf-row {
			td:first-child {
				width: 240px;
			}
		}

		.scheduler-main-row {
			>td:last-child {
				position: relative;
			}
		}
	}

	.scheduler {
		border: none;
		position: relative;

		.scheduler-tf-table {
			&.top-right-table {
				th {
					.title-container {
						position: absolute;
						top: 0;
						right: 0;
						bottom: 0;
						left: 0;

						.title-wrapper {
							width: 180px;
							height: 42px;
							margin-left: auto;
							margin-right: auto;
							display: flex;

							.title-text {
								font-size: 13px;
								font-family: "SourceSansPro-SemiBold";
								line-height: 42px;
							}
						}
					}
				}
			}
		}

		tfoot {
			line-height: 32px;

			tr {
				th.scheduler-count-label {
					text-align: left;
					border-bottom-color: white;
					border-right: none;
					cursor: pointer;
					max-width: 224px;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					background: #f2f2f2;

					div {
						border-top: 1px solid #e6e6e6;
						line-height: 31px;
						background: #fff;
						margin-right: 16px;
						padding-left: 8px;
						font-size: 14px;
					}
				}

				div.countcell-wrapper {
					border-top: 1px solid #e6e6e6;
					position: relative;
					height: 32px;
					box-sizing: border-box;
					border-right: 1px solid #e6e6e6;

					.count-cell {
						position: absolute;
						border-right: 1px solid #e6e6e6;
						box-sizing: border-box;
						text-align: center;

						&:last-child {
							border-right: none;
						}
					}
				}
			}
		}

		&.assigned {
			.scheduler-tf-table {
				&.top-right-table {
					tr:nth-child(2) {
						height: 44px !important;

						th {
							border-width: 1px 0px 1px 0px;
							border-style: solid;
							border-color: #e6e6e6;
							position: relative;

							.title-container {

								.title-wrapper {
									width: 50px;
									cursor: pointer;

									.icon {
										&.bottom-caret {
											margin: 18px 0 0 8px;

											&:after {
												border-top-color: white;
											}
										}
									}
								}
							}
						}
					}

					.timeline {
						height: 32px;
						cursor: pointer;
						box-sizing: border-box;
						text-align: center;

						.timeline-item {
							height: 100%;
							display: inline-block;
							box-sizing: border-box;
							padding-top: 5px;
							border-top: 2px solid transparent;
							border-bottom: 2px solid transparent;
							text-overflow: ellipsis;
							white-space: nowrap;
							overflow: hidden;

							&:first-child {
								border-left: 2px solid transparent;
							}

							&:last-child {
								border-right: 2px solid transparent;
							}
						}

						&:hover,
						&.dragging {
							.timeline-item {
								border-color: #4A90E2;
							}
						}
					}
				}
			}

			.filter-menu-container {
				top: 39px;
			}
		}

		&.unassigned {
			z-index: 2;

			.scheduler-tf-table {
				&.top-left-table {
					tr:first-child {
						height: 44px !important;

						td {
							padding: 14px 0 0 12px;
							font-size: 13px;
							font-family: "SourceSansPro-SemiBold";
							border-color: #e6e6e6;
							border-style: solid;
							border-width: 1px 0px 1px 0px;
						}
					}

					tr:nth-child(1) {
						height: 44px !important;

						th {
							padding: 0;
							border-width: 1px 0px 1px 0px;
							border-style: solid;
							border-color: #e6e6e6;
						}
					}
				}

				&.top-right-table {
					tr:first-child {
						height: 44px !important;

						th {
							border-color: #e6e6e6;
							border-style: solid;
							border-width: 1px 0px 1px 0px;
						}
					}
				}
			}

			.filter-menu-container {
				top: 7px;
			}

			tfoot {
				tr {
					div:not(.count-cell) {
						border-bottom: 1px solid #e6e6e6;
					}
				}
			}
		}

		&.existing-scroll {

			.top-right-table,
			.scheduler-tf-content,
			.countcell-wrapper {
				width: calc(~"100% - 17px");
			}

			&.scroll {
				.scheduler-tf-content {
					width: 100%;
					overflow-y: scroll;

					.bottom-right-grid {
						.scheduler-grid-row.bottom {
							border-bottom: none !important;
						}
					}
				}

				.bottom-left-table tr th.scheduler-resource-cell.bottom {
					border-bottom: none !important;
				}
			}
		}

		.scheduler-tf-resources {
			border-bottom: none;
			background: #f2f2f2;
			padding-right: 16px;
			position: relative;
			overflow: hidden;
			border-width: 0;

			table {
				background: #fff;
			}
		}

		.scheduler-tf-header {
			padding-right: 0px !important;
			position: relative;
		}

		.scheduler-tf-table {
			width: 100%;
			border-collapse: separate;

			tr {

				td,
				th {
					padding: 0;
					height: 32px;
					box-sizing: border-box;
				}
			}

			tr:not(.fill) {
				height: 32px !important;
			}

			tr.fill {
				th {
					border: none;

					span {
						display: block;
						box-sizing: border-box;
					}
				}
			}

			&.top-left-table {
				width: 240px;

				th {
					border: none;
				}

				tr:nth-child(2) {
					height: 44px !important;

					th {
						padding: 0;
						border-width: 1px 0px 1px 0px;
						border-style: solid;
						border-color: #e6e6e6;
					}
				}

				.settings-wrapper {
					display: flex;
					width: calc(~"100% - 10px");
					padding-left: 6px;

					.date-picker {
						width: calc(~"100% - 40px");
						height: 30px;
						position: relative;
						background: #4A90E2;
						border-radius: 5px;

						.date-time-text {
							position: absolute;
							left: 12px;
							right: 12px;
							top: 0;
							bottom: 0;
							text-align: center;

							span {
								display: inline-block;
								margin-top: 6px;
								color: white;
								font-size: 11pt;
							}
						}

						.previous-day {
							position: absolute;
							top: 0;
							bottom: 0px;
							left: 0px;
							width: 12px;
						}

						.next-day {
							position: absolute;
							top: 0;
							bottom: 0px;
							right: 0px;
							width: 12px;
						}
					}

					.customview-setting-btn {
						width: 32px;
						height: 32px;
						background-image: url('../img/ResourceScheduler/icon-Adjust.svg');
						background-size: 16px 16px;
						background-repeat: no-repeat;
						background-position: center center;
						cursor: pointer;
					}

					.customview-refresh-btn {
						width: 32px;
						height: 32px;
						background-image: url(../img/ResourceScheduler/icon-Refresh.svg);
						background-size: 16px 16px;
						background-repeat: no-repeat;
						background-position: center center;
						cursor: pointer;
					}
				}

				.move-left {
					background: #4A90E2;
					position: absolute;
					top: 0;
					bottom: 44px;
					right: 0px;
					width: 16px;
					cursor: pointer;

					.left {
						left: 5px;
					}
				}
			}

			&.bottom-left-table {
				th {
					color: #262626;
					font-family: "SourceSansPro-Regular";
					font-size: 14px;
				}

				tr {
					&:nth-of-type(even):not(.fill) {
						background: #F0F7FE;
					}

					th.scheduler-resource-cell {
						line-height: 31px;
						text-align: left;
						padding-left: 8px;
						border-bottom-color: white;
						border-right: none;
						cursor: pointer;
						max-width: 224px;
						text-overflow: ellipsis;
						white-space: nowrap;
						overflow: hidden;

						&.bottom {
							border-bottom: 1px solid #e6e6e6;
						}
					}

					&:not(.fill) {
						th {
							&:hover {
								background-color: #FFFFCE;
							}
						}
					}
				}
			}
		}

		.trangle {
			display: block;
			width: 0;
			height: 0;
			border-style: solid;
			position: absolute;
			top: 9px;
			left: 3px;
			cursor: pointer;

			&.left {
				border-width: 6px 6px 6px 0;
				border-color: transparent #fff transparent transparent;
			}

			&.right {
				border-width: 6px 0 6px 6px;
				border-color: transparent transparent transparent #fff;
			}
		}

		span.move-right {
			position: absolute;
			background: #4A90E2;
			top: 0;
			bottom: 44px;
			right: 0px;
			width: 17px;
			cursor: pointer;

			.right {
				left: 6px;
			}
		}

		.scheduler-tf-content {
			overflow: hidden;
			position: relative;
			border-right: 1px solid #e6e6e6;

			.events-container {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 0px;
				z-index: 1;
			}

			.bottom-right-grid {
				width: 100%;
				position: relative;

				.scheduler-grid-row {
					height: 32px;
					box-sizing: border-box;

					&.bottom {
						border-bottom: 1px solid #e6e6e6;
					}

					&:nth-of-type(even) {
						background: #F0F7FE;
					}

					&.hover {
						background-color: #FFFFCE !important;
					}
				}

				.scheduler-grid-column {
					position: absolute;
					top: 0;
					border-right: 1px solid #e6e6e6;
					box-sizing: border-box;
					height: 100%;
					pointer-events: none;

					&:last-child {
						border-right: none;
					}
				}

				.scheduler-grid-empty-placeholder {
					height: 100%;
					width: 100%;
					padding: 20px;
					color: #848484;
					font-size: 16px;
				}
			}

			.area-selection {
				border: 2px black dashed;
				box-sizing: border-box;
				position: absolute;
				width: 10px;
				height: 10px;
				z-index: 2;
			}
		}

		.school-container,
		.current-time-container {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			pointer-events: none;
			overflow: hidden;

			.school-events-container,
			.current-time-bar-container {
				position: absolute;
				top: 0;
				height: 100%;
				width: 100%;

				.scheduler-event-wrapper {
					pointer-events: auto;
					z-index: 0;

					&.current-time {
						top: 0px;
						bottom: 0px;
						width: 0%;
						z-index: 2;

						.scheduler-event {
							background-color: #ffff00;
						}
					}

					.school-conflict {
						.color-blend-container {
							position: absolute;
							left: 0;
							top: 0;

							.color-blend {
								width: 10px;
							}
						}
					}
				}
			}
		}

		.quick-search-container {
			border-bottom: 1px solid #4a4a4a;
			margin: 0 10px 5px 0;

			&.on-quick-search {
				.clear-btn {
					display: block;
				}
			}
		}

		.search-header {
			display: block;
			height: 35px;
			position: relative;

			.item-icon {
				position: absolute;
				background-repeat: no-repeat;
				background-position: center;
				cursor: pointer;

				&.search-btn {
					background-image: none;
					height: 40px;
					width: 59px;
					left: -20px;

					&:before {
						content: '';
						background-image: url(../img/ResourceScheduler/icon-Search.svg);
						top: 3px;
						left: 17px;
						width: 32px;
						height: 32px;
						position: absolute;
						background-repeat: no-repeat;
						background-size: 24px;
						background-position: center;
						z-index: 2;
					}
				}

				&.quick-search-close {
					background-image: url("../img/ResourceScheduler/icon-Search Close.svg");
				}
			}

			.search-text {
				position: absolute;
				top: 7px;
				left: 30px;
				width: calc(~"100% - 61px");
				font-size: 16px;
				border: 0;
				outline: 0;

				&::-webkit-input-placeholder {
					opacity: 0.5;
				}

				&:-moz-placeholder {
					opacity: 0.5;
				}

				&::-moz-placeholder {
					opacity: 0.5;
				}

				&:-ms-input-placeholder {
					opacity: 0.5;
				}

				&::-ms-input-placeholder {
					opacity: 0.5;
				}

				&.font14 {
					&::-webkit-input-placeholder {
						font-size: 14px;
					}

					&:-moz-placeholder {
						font-size: 14px;
					}

					&::-moz-placeholder {
						font-size: 14px;
					}

					&:-ms-input-placeholder {
						font-size: 14px;
					}

					&::-ms-input-placeholder {
						font-size: 14px;
					}
				}

				&.font15 {
					&::-webkit-input-placeholder {
						font-size: 15px;
					}

					&:-moz-placeholder {
						font-size: 15px;
					}

					&::-moz-placeholder {
						font-size: 15px;
					}

					&:-ms-input-placeholder {
						font-size: 15px;
					}

					&::-ms-input-placeholder {
						font-size: 15px;
					}
				}

				&.font16 {
					&::-webkit-input-placeholder {
						font-size: 16px;
					}

					&:-moz-placeholder {
						font-size: 16px;
					}

					&::-moz-placeholder {
						font-size: 16px;
					}

					&:-ms-input-placeholder {
						font-size: 16px;
					}

					&::-ms-input-placeholder {
						font-size: 16px;
					}

					&.mobile {
						&::-webkit-input-placeholder {
							color: #fff !important;
							opacity: 1;
							font-family: "SourceSansPro-Regular", Arial;
						}

						&:-moz-placeholder {
							color: #ffffff !important;
							opacity: 1;
							font-family: "SourceSansPro-Regular", Arial;
						}

						&::-moz-placeholder {
							color: #ffffff !important;
							opacity: 1;
							font-family: "SourceSansPro-Regular", Arial;
						}

						&:-ms-input-placeholder {
							color: #ffffff !important;
							opacity: 1;
							font-family: "SourceSansPro-Regular", Arial;
						}

						&::-ms-input-placeholder {
							color: #ffffff !important;
							opacity: 1;
							font-family: "SourceSansPro-Regular", Arial;
						}
					}
				}
			}

			.clear-btn {
				top: 7px;
				right: 0px;
				height: 24px;
				width: 13px;
				display: none;
				background-size: 13px;
				cursor: pointer;
			}

			.quick-search-spinner {
				display: none;
				position: absolute;
				top: 19px;
				left: 2px;
				width: 13px;
				height: 13px;
				background-size: 13px;
				.rotate-animation;
			}

			&.searching {
				.item-icon {
					opacity: 0.3;
				}

				.quick-search-spinner {
					display: block;
				}
			}
		}
	}

	.scheduler-event-wrapper {
		position: absolute;
		z-index: 1;
		outline: none;

		&.selected {
			.scheduler-event {
				border-width: 2px;
				padding: 3px 1px 0px 1px;
				border-color: #fff700;
			}
		}

		&.placeholder {
			.scheduler-event {
				border: 1px dashed black;
				opacity: 0.5;
			}

			.time-preview {
				position: absolute;
				left: -20px;
				top: -20px;
			}
		}

		&.ui-draggable-dragging {
			z-index: 2;
		}

		.scheduler-event {
			position: absolute;
			left: 1px;
			right: 1px;
			top: 1px;
			bottom: 1px;
			border: 1px solid black;
			cursor: pointer;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			font-size: 14px;
			box-shadow: rgba(0, 0, 0, .3) 0 2px 6px 0;
			padding: 4px 2px 1px 2px;
			border-radius: 3px;
			font-family: "SourceSansPro-SemiBold";
			color: white;

			.event-trip-time {
				padding-left: 10px;
			}

			span {
				cursor: pointer;
			}

			.fieldtripstage {
				position: absolute;
				bottom: 0;
				height: 5px;
				width: 100%;
				margin-left: -2px;
			}
		}

		.event-trip-ETA {
			position: absolute;
			right: 0;
			top: 1px;
			bottom: 1px;
			background: linear-gradient(-45deg, #333 25%, #fff 0, #fff 50%, #333 0, #333 75%, #fff 0);
			background-size: 4px 4px;
			border-top-right-radius: 3px;
			border-bottom-right-radius: 3px;
			border-left: none;
			box-shadow: rgba(0, 0, 0, .3) 0 2px 6px 0;
			border: 1px solid black;
			cursor: pointer;
		}
	}

	.multi-scheduler-events-wrapper {
		position: absolute;
		display: flex;
		flex-direction: column;
		border: 1px dashed black;
		opacity: 0.5;
		box-shadow: rgba(0, 0, 0, .3) 0 2px 6px 0;
		border-radius: 3px;
		z-index: 2;

		.scheduler-event-wrapper {
			position: relative;

			.scheduler-event {
				border: none;
				box-shadow: none;
				height: 100%;
			}
		}
	}

	.filter-container {
		text-align: left;

		.filter-item {
			position: relative;
			width: 25%;
			float: left;

			.iconbutton.filter {
				display: inline-block;
				height: 24px;
				width: 25px;
				background-position-x: 4px;

				&.contextmenu-open {
					border: none;
				}
			}

			.name {
				position: absolute;
				top: 3px;
				left: 20px;
				text-align: left;
				width: calc(~"100% - 50px");
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				padding-left: 8px;

				&.no-filter {
					font-style: italic;
				}
			}
		}
	}

	.filter-menu-container {
		background: transparent;
		position: absolute;
		z-index: 3;
		left: 256px;

		.filter-icon-mask {
			width: 25px;
			height: 25px;
			cursor: pointer;
		}

		.scheduler-filters {
			padding: 0px;
			list-style: none;
			background: #fff;
			border: 1px solid #4b4b4b;
			margin: 0px;
			overflow-y: auto;

			li {
				.menu-icon {
					width: 24px;
					height: 24px;
					float: left;
					background: #4B4B4B;
					cursor: pointer;
				}

				.filter-text {
					cursor: pointer;
					height: 22px;
					line-height: 22px;
					display: inline-block;
					padding: 0px 15px 0px 5px;
					margin-top: 2px;
					font-size: 14px;
					width: calc(~"100% - 24px");
					box-sizing: border-box;
				}

				&:hover {
					background: #DDEDFB;
				}

				&.selected {
					font-weight: 600;

					.menu-icon {
						background-image: url("../../global/img/grid/green_check.png");
						background-repeat: no-repeat;
						background-position: 3px 5px;
					}
				}
			}

			li.divider {
				border-bottom: 1px solid #4B4B4B;
			}

			li.clear-filter {
				.filter-text {
					border-bottom: 1px solid #4B4B4B;
				}
			}
		}
	}

	.page-spliter {
		height: 0px;
		box-sizing: border-box;
		position: absolute;
		bottom: 0px;
		width: calc(~"100% - 41px");
		border: none;
		z-index: 99;

		.draggable-wrapper-holder {
			position: absolute;
			top: -11px;
			height: 46px;
			width: 200px;
			left: calc(~"50% + 20px");

			.draggable-wrapper {
				position: absolute;
				top: 5px;
				left: 90px;
				height: 11px;
				width: 20px;
				border-width: 1px 0px;
				border-style: solid;
				border-color: #4A90E2;
				cursor: pointer;
				display: none;

				div {
					border-top: 1px solid #4A90E2;
					width: 160%;
					left: -30%;
					position: absolute;

					&:first-child {
						top: 2px;
					}

					&:last-child {
						top: 7px;
					}
				}
			}
		}

		&.active {
			border-bottom: 1px solid #4A90E2;

			.draggable-wrapper {
				display: block;
			}
		}

		&.dragging {
			border-bottom: 1px solid #4A90E2;
			z-index: 5;

			.draggable-wrapper {
				display: block;
			}
		}
	}

	.scheduler.existing-scroll+.page-spliter {
		width: calc(~"100% - 57px");
	}
}

.scheduler-popup-arrow {
	position: absolute;
	height: 32px;
	width: 32px;
	transform: rotate(45deg);
	background-color: #fff;
	box-shadow: 0 2px 16px rgba(0, 0, 0, .3);

	&.left {
		left: -16px;
	}

	&.right {
		right: -16px;
	}

	&.top {
		top: -16px;
		left: 246px;
		background-color: #fddad7
	}

	&.bottom {
		bottom: -16px;
		left: 246px;
	}
}

.event-details {
	float: left;
	width: 520px;
	position: absolute;
	pointer-events: auto;

	>.wrapper {
		background-color: #fff;
		box-shadow: 0 2px 16px rgba(0, 0, 0, .3);

		.header {
			padding: 10px 20px;
			font-size: 18px;
			line-height: 22px;
			height: 42px;
			font-weight: bold;
			background-color: #fddad7; // rgba(215, 75, 60, .1);
			color: @ThemeColor;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			position: relative;
		}

		.content-body {
			position: relative;
			max-height: 500px;
			overflow: auto;

			.wrapper {
				display: flex;
				flex-direction: column;
				flex-wrap: wrap;
				justify-content: start;
				padding: 10px 20px;
				background-color: #fff;
				min-height: 100px;

				.info-header {
					width: 100%;
					height: 24px;
					color: @ThemeColor;
					font-size: 15px;
					font-weight: 700;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}

				.info-container {
					width: 100%;
					margin-bottom: 15px;

					&:last-child {
						margin-bottom: 5px;
					}

					.info-item {
						min-width: 40%;
						float: left;
						line-height: 22px;

						&.full-length {
							width: 100%;
						}

						label {
							margin-right: 5px;
							float: left;
						}

						div {
							float: left;
						}
					}
				}
			}
		}
	}
}

.customview-settings {
	position: absolute;
	background-color: white;
	z-index: 2;

	.customview-settings-wrapper {
		box-shadow: 0 2px 16px rgba(0, 0, 0, .3);

		.scheduler-popup-arrow {
			&.top {
				background-color: #fff;
			}
		}

		.content-body {
			position: relative;
			width: 367px;
			padding: 20px;
			background-color: white;
			max-height: calc(~"100vh - 130px");
			overflow: auto;

			.customview-settings-item {

				.config-item {
					&>span {
						font-weight: bold;
					}

					&.inactive {
						opacity: 0.5;
						pointer-events: none;
					}
				}
			}

			.time-range-container {
				.time-range-settings {
					display: flex;
					margin-top: 10px;

					.input-group {
						width: calc(~"50% - 10px");

						input {
							width: 100%;
						}
					}

					.to-text {
						width: 20px;
						padding: 2px 0px 0px 5px;
					}
				}
			}

			.interval-container {
				.input-group {
					margin-top: 10px;
				}
			}

			.error {
				color: red;
				margin: 3px 0 0 0;
				position: absolute;
			}
		}
	}
}

.control-checkbox {
	display: block;
	position: relative;
	height: 16px;
	width: 16px;
	margin-left: 5px;
	cursor: pointer;
	font-size: 22px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	&:hover input~.checkmark {
		background-color: #e4e4e4;
	}

	input:checked~.checkmark {
		&:after {
			display: block;
		}
	}

	input {
		display: none;
	}

	.checkmark {
		position: absolute;
		top: 0;
		left: 0;
		height: 16px;
		width: 16px;
		background: #fff;
		border: 1px solid #999;
		cursor: pointer;

		&:after {
			content: "";
			position: absolute;
			display: none;
			left: 5px;
			top: 2px;
			width: 5px;
			height: 10px;
			border: solid #333;
			border-width: 0 3px 3px 0;
			-webkit-transform: rotate(45deg);
			-ms-transform: rotate(45deg);
			transform: rotate(45deg);
		}
	}
}

.select-edit-items {
	font-family: "SourceSansPro-Regular";

	.item-list {
		border: 1px solid #e6e6e6;
		margin-top: 5px;
		overflow: auto;
		max-height: 300px;

		ul {
			list-style: none;
			margin: 0;
			padding: 0;
			font-size: 15px;

			li {
				&:hover {
					background-color: rgba(0, 0, 0, 0.05);
				}

				&.selected {
					background-color: #FFFFCE;
				}

				.item {
					height: 32px;
					padding: 5px;
				}
			}
		}
	}
}

.modal-body {
	.edit-date-range-container {
		border-top: 1px solid #ccc;

		.col-xs-4 {
			padding-right: 0;
			width: 19%;

			.form-group {
				margin-bottom: 10px;
			}
		}

		.from-date,
		.to-date {
			height: 56px;
			margin-bottom: 0;

			.error {
				color: red;
				margin: 3px 0 0 0;
				position: absolute;
			}
		}

		.recurs-period
		{
			height: 60px;
		}

		.checkbox.disable {
			label {
				opacity: 0.5;
				cursor: not-allowed;
			}
		}
	}

	// will be removed.
	.trip-edit-level-modal {
		font-family: "SourceSansPro-Regular";

		.radio-group {
			cursor: pointer;

			div.radio {
				display: inline-block;
				padding-right: 20px;
			}
		}
	}

	.resource-substitution {
		.fromTo-container {
			border-top: 1px solid #ccc;
			padding-top: 15px;
		}

		.restriction-container {
			border-top: 1px solid #ccc;
			padding-top: 15px;
		}
	}

	.edit-calendar-record-modal {
		.description.duration {
			margin-top: 0px;
		}

		.edit-date-range-container {
			border-top: none;

			&.disableDateRange .row {
				&.recurs-period,
				&.edit-date,
				&.edit-day {
					opacity: 0.5;
					pointer-events: none;
				}
			}

			input[type=radio] {
				cursor: pointer;
			}

			.radio-group {
				div.radio {
					display: inline-block;
					padding-right: 40px;
				}
			}
		}
	}

	.edit-modal {
		.description {
			margin-top: 5px;
			margin-bottom: 0px;

			&.duration {
				width: 200%;
			}
		}

		&.fieldtrip {
			.resourceGroupGrid {
				height: 150px;

				.k-grid-container {
					.k-grid-table {
						.k-table-row {
							.k-command-cell {
								button:focus {
									top: 0;
									right: 0;
									height: 16px;
									border: none;
									margin: .16em;
									margin-inline-end: var(--kendo-spacing-1, 0.25rem);
								}
							}
						}
					}
				}
			}
		}

		&.restriction {
			.all-day-container.checkbox {
				margin: 0px;

				label {
					font-weight: bold;
				}
			}

			.input-control {
				.label {
					width: 40%;
					float: left;
					padding: .3em 0 0 0;
					font-size: 12px;
					text-align: left;
				}

				.label+.customInput,
				.label+.input-group {
					width: 60%;
				}
			}

			.line {
				border: 1px dashed black;
				opacity: 0.3;
				margin: 15px 10px 10px 10px;
			}

			.date-container {
				border-top: 1px solid #ccc;
				padding-top: 15px;
				margin-top: 20px;

				.date-input-container {
					padding-top: 10px;
				}
			}

			.error {
				margin: 0;

				&.date,
				&.time {
					width: 100%;
					padding-left: 35%;
				}
			}
		}

		&.resource-schedule {
			.info-container {
				width: 100%;
				margin-bottom: 15px;
				float: left;

				.info-item {
					min-width: 40%;
					float: left;
					line-height: 22px;

					label {
						margin-right: 5px;
						float: left;
					}

					div {
						float: left;
					}
				}
			}

			.schedule-items-container {
				width: 100%;
				border-top: 1px solid #ccc;
				padding-top: 15px;

				table {
					table-layout: fixed;
					width: 100%;

					thead {
						th {
							width: 16.66%;

							&.type {
								width: 10.66%;
							}

							&.name {
								width: 31.33%;
							}

							&.starttime {
								width: 10.33%;

								&:hover {
									cursor: pointer;
								}
							}

							&.endtime {
								width: 10.33%;

								&:hover {
									cursor: pointer;
								}
							}

							&.descr1,
							&.descr2 {
								width: 18.66%;
							}
						}
					}

					tr {
						height: 24px;

						td {
							overflow: hidden;
							text-overflow: ellipsis;
							word-break: normal;
							white-space: nowrap;
							padding-right: 2px;
						}
					}
				}
			}
		}

		.error {
			color: red;
			margin: 3px 0 0 0;
			position: absolute;
		}

		.calendar-grid-title {
			label {
				display: inline-block;
			}

			button {
				float: right;
				margin-top: -8px;
				background: none;
				border: 1px solid #262626;
			}
		}

		.calendar-grid {
			border: 1px solid #e6e6e6;
			max-height: 250px;
			overflow-y: auto;

			table {
				width: 100%;

				tr.k-grid-header th {
					background: #4b4b4b;
					color: #fff;
				}

				tbody {
					tr.k-alt td {
						background: #EFEFEF;
					}

					tr[aria-selected="true"] td {
						background: #FFFFCE !important;
						color: #262626;
					}
				}
			}
		}
	}
	
	.trip-display-note {
		margin-bottom: 10px;
	}
	.trip-display-custom-setting {
		display: inline-block;
		padding-top: 10px;
		label {
			padding: 0 0 5px 20px;
			position: relative;
			line-height: 22px;
			cursor: pointer;
	
			&:has(input[type=checkbox]) {
				font-weight: 500;
			}
		}
		.separator {
			width: 70px;
			padding-left: 4px;
			margin-left: 2px;
			display: inline-block;

			&:focus {
				border-color: rgba(0, 0, 0, 0.4);
			}
		}

		.note {
			margin-top: -5px;
			padding-left: 20px;
		}
	}
}

.resourcetype-menu {
	.tf-contextmenu {
		border: 1px solid #f2f2f2;

		.menu-item {
			.menu-label {
				line-height: 42px;
				margin-left: 0;
				padding-left: 12px;
				text-align: center;
				font-size: 13px;
				font-family: "SourceSansPro-SemiBold";
				max-width: none;
			}
		}
	}
}

.substitute-resources-modal {
	width: 500px;

	.date-range-input {
		height: 0;
		padding: 0 !important;
		border: none;
	}

	.panel-heading {
		padding-left: 20px;
	}

	.modal-body {
		overflow-x: hidden;
		padding: 20px;
	}

	.col-xs-12.padding-left {
		padding-left: 40px;
	}

	.substitute-resources {
		.description {
			margin-top: 0;
			margin-bottom: 0;
		}

		.row.disabled {
			pointer-events: none;
			opacity: 0.5;
		}

		.section-header {
			font-size: 13px;
			font-weight: bold;
			color: #333;

			&.requirestar::after {
				content: "*";
				color: red;
				margin-left: 3px;
			}
		}

		.left-padding {
			padding-left: 25px;
		}

		.new {
			margin-top: 5px;
		}

		.trip-time {
			margin-bottom: 20px;
		}

		.form-group {
			margin-bottom: 5px;
		}

		.help-block {
			margin-top: 5px;
		}

		.date-time {
			padding-left: 0px;
			padding-right: 0px;
			margin-right: 10px;
			position: relative;
			width: 50%;
			float: left;
		}

		.adjust-time-type {
			padding-left: 0px;
			padding-right: 0px;
			float: left;
			width: 32%
		}

		.adjust-time-unit {
			padding-left: 10px;
			line-height: 22px;
			padding-right: 0px;
		}

		.disable-mask {
			position: absolute;
			inset: 0;
			opacity: 0.1;
			background-color: gray;
			z-index: 9999;
		}

		.adjust-time-number-box {
			padding-left: 0px;
			padding-right: 0px;
			position: relative;
			width: 90px;
			float: left;

			input {
				width: 45px;
				float: left;
			}

			.number-box-button {
				float: left;
				width: 22px;
				height: 22px;
				padding: 2px;
			}
		}


		.days-of-the-week {
			display: flex;
			flex-direction: row;
			padding-left: 36px;
			padding-right: 15px;
			width: 500px;


			&.weekdays_only {
				width: 140px;
			}

			label {
				flex: 1;
				padding-bottom: 0.5em;
				padding-left: 0px;
				font-size: 12px;
			}

			input[type=checkbox] {
				margin-left: -15px;
			}
		}

		.checkbox-inline {
			padding-left: 20px;
			line-height: 1.8;
			font-size: 13px;
			color: #333;

			input[type=checkbox] {
				margin-top: 4px;
				cursor: pointer;
			}
		}
	}
}