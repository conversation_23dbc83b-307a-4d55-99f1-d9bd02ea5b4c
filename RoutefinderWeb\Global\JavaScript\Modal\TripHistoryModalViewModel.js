﻿(function()
{
	createNamespace('TF.Modal').TripHistoryModalViewModel = TripHistoryModalViewModel;

	function TripHistoryModalViewModel(tripId, tripName, tripHistoryId)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/triphistorycontrol');
		this.buttonTemplate('modal/positivenegative');
		this.obEnableEnter(false);
		this.tripHistoryViewModel = new TF.Control.TripHistoryViewModel(tripHistoryId, tripId, tripName);
		this.data(this.tripHistoryViewModel);
		this.sizeCss = "modal-dialog-lg";
		this.tripHistoryId = tripHistoryId;
		var title = "Calendar Event - [" + tripName + "]";
		if (tripHistoryId && tripHistoryId > 0)
		{
			this.title("Edit " + title);
		}
		else
		{
			this.title("Add " + title);
		}
		this.obSaveAndNewButtonLabel = ko.observable("Save and Copy");
		this.controlTabKey = true;
	}

	TripHistoryModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	TripHistoryModalViewModel.prototype.constructor = TripHistoryModalViewModel;

	TripHistoryModalViewModel.prototype.positiveClick = function()
	{
		return this.tripHistoryViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	TripHistoryModalViewModel.prototype.negativeClose = function(returnData)
	{
		this.tripHistoryViewModel.close().then(function(result)
		{
			if (result)
			{
				this.positiveClick();
			}
			else
			{
				this.hide();
				this.resolve(result);
			}
		}.bind(this));
	};

	// TripHistoryModalViewModel.prototype.saveAndNewClick = function()
	// {
	// 	this.tripHistoryViewModel.apply().then(function(result)
	// 	{
	// 		if (result)
	// 		{
	// 			this.tripHistoryViewModel.createNewTripHistory();
	// 		}
	// 	}.bind(this));
	// };

	// TripHistoryModalViewModel.prototype.generateFunction = function(fn)
	// {
	// 	return fn.bind(this, Array.prototype.slice.call(arguments, 1));
	// };

	TripHistoryModalViewModel.prototype.dispose = function()
	{
		this.tripHistoryViewModel.dispose();
	};

})();
