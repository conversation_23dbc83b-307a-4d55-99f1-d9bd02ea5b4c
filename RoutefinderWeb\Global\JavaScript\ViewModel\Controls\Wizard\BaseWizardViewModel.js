(function()
{
	createNamespace("TF.Control").BaseWizardViewModel = BaseWizardViewModel;

	function BaseWizardViewModel()
	{
		var self = this;
		self.data = ko.observable();
		self.currentStep = ko.observable();
		self.name = ko.observable("");
		self.title = ko.computed(function()
		{
			var step = self.currentStep(), name = self.name();
			if (step && step.name())
			{
				return name + ": " + step.name();
			}

			return name;
		});

		self.nextStepType = ko.computed(self.getNextStepType, self);
		self.stepHistory = ko.observableArray([]);
		self.canNext = ko.pureComputed(function()
		{
			var nextType = self.nextStepType();
			return !!nextType;
		});

		this.canPrevious = ko.pureComputed(function()
		{
			return !!self.stepHistory().length;
		});
	};

	BaseWizardViewModel.prototype.init = function()
	{
		this.next();
	};

	BaseWizardViewModel.prototype.apply = function()
	{
		return this.applyCurrentStep();
	};

	BaseWizardViewModel.prototype.getNextStepType = function()
	{
		throw "It's an abstract method, please implement it.";
	};

	BaseWizardViewModel.prototype.applyCurrentStep = function()
	{
		return this.currentStep().apply();
	};

	BaseWizardViewModel.prototype.next = function()
	{
		var self = this;
		if (!self.currentStep())
		{
			var nextStepType = self.nextStepType();
			if (nextStepType)
			{
				self.currentStep(new nextStepType(self.data(), self));
			}

			return Promise.resolve(!!nextStepType);
		}

		return self.applyCurrentStep().then(function(result)
		{
			if (!result)
			{
				return false;
			}

			var nextStepType = self.nextStepType();
			if (!nextStepType)
			{
				self.disposeCurrentStep();
				return false;
			}

			self.stepHistory.push(self.currentStep().constructor);
			var stepViewModel = new nextStepType(self.data(), self);
			self.disposeCurrentStep();
			self.currentStep(stepViewModel);
			return true;
		});
	};

	BaseWizardViewModel.prototype.turnToSpecialPage = function()
	{
		return false;
	};

	BaseWizardViewModel.prototype.disposeCurrentStep = function()
	{
		var step = this.currentStep();
		if (step)
		{
			step.dispose();
		}
	};

	BaseWizardViewModel.prototype.previous = function()
	{
		var self = this;
		if (!self.canPrevious())
		{
			return Promise.reject();
		}

		return self.currentStep().back().then(function()
		{
			var stepViewModelType = self.stepHistory.pop();
			var stepViewModel = new stepViewModelType(self.data());
			self.disposeCurrentStep();
			self.currentStep(stepViewModel);
			return Promise.resolve(stepViewModel);
		});
	};
})();