﻿(function()
{
	createNamespace("TF.Modal.Navigation").NewDataSourceModalViewModel = NewDataSourceModalViewModel;

	function NewDataSourceModalViewModel(title, text, options, copyAsNew)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-sm";
		this.title(title);
		this.contentTemplate('Navigation/NewDataSource');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel(title.indexOf("Info") >= 0 ? "Apply" : "Create");
		this.openDataSourceViewModel = new TF.Navigation.NewKendoDataSourceViewModel(text, options, copyAsNew);
		this.data(this.openDataSourceViewModel);
	}

	NewDataSourceModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	NewDataSourceModalViewModel.prototype.constructor = NewDataSourceModalViewModel;

	NewDataSourceModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	NewDataSourceModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.cancel()
			.then(function()
			{
				this.negativeClose();
			}.bind(this));
	};

})();