(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.ThematicConfigurationDataModel = function(ThematicConfigurationEntity)
	{
		namespace.BaseDataModel.call(this, ThematicConfigurationEntity);
		this.dashboardThematicNames = ko.computed(function()
		{
			const dashboardThematic = this.dashboardThematic();
			if (!dashboardThematic || !dashboardThematic.length)
			{
				return "";
			}

			var names = dashboardThematic.map(e => e.Name);
			if (names.length === 1)
			{
				return names[0];
			}

			var lastOne = names.pop();
			return `${names.join(", ")} and ${lastOne}`;
		}, this);
	}

	namespace.ThematicConfigurationDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ThematicConfigurationDataModel.constructor = namespace.ThematicConfigurationDataModel;

	namespace.ThematicConfigurationDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "DataTypeID", default: "" },
		{ from: "QuickFilters", default: "" },
		{ from: "SortInfo", default: "" },
		{ from: "CustomDisplaySetting", default: [] },
		{ from: "DashboardThematicExists", default: false },
		{ from: "DashboardThematic", default: [] },
	];
})();