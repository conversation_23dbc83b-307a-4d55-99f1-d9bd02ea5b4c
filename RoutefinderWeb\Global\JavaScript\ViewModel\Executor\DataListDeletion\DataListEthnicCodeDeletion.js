﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListEthnicCodeDeletion = DataListEthnicCodeDeletion;

	function DataListEthnicCodeDeletion()
	{
		this.type = 'ethniccode';
		this.deleteType = 'Code';
		this.deleteRecordName = 'Ethnic Code';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListEthnicCodeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListEthnicCodeDeletion.prototype.constructor = DataListEthnicCodeDeletion;

	DataListEthnicCodeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();