﻿.document-controlpanel {

	@title-template-margin-left: 4px;

	.path {
		padding-top: 15px;
		margin-left: @title-template-margin-left;
	}

	.controlpanel-title-group {
		line-height: 38px;
		height: 38px;

		.controlpanel-title {
			font-family: Arial;
			font-weight: bold;
			color: #D0503C;
			font-size: 24px;
			margin: 0 6px 0 @title-template-margin-left;
		}
	}

	.controlpanel-description {
		margin-left: @title-template-margin-left;
		color: #4A4A4A;
		max-width: 920px;
	}

	.controlpanel-paginator {
		position: absolute;
		right: 34px;
		top: 35px;
		font-size: 10px;

		&>div {
			float: left;
			display: block;
			margin-left: 10px;
		}

		.iconbutton {
			background-size: 100% 100%;
			width: 20px;
			height: 20px;

			&:hover {
				background-color: transparent;
			}
		}
	}

	.controlpanel-container {
		overflow: auto;
		padding-bottom: 20px;
		margin-top: 30px;
	}

	.edit-bottombar {
		background-color: #f2f2f2;
		line-height: 37px;
		height: 45px;
		border-top: 1px solid #C4C4C4;
	}

	.edit-error {
		width: 100%;
		border: 2px solid #9e3e0e;
		border-radius: 2px;
		line-height: 20px;
		padding: 5px;
		margin: 5px -15px 0 -9px;
		height: 95px;
		padding-top: 4px;
		padding-bottom: 4px;

		div {
			margin-left: 40px;

			&:first-child {
				margin-left: 10px;
				float: left;
			}
		}

		ul {
			width: 100%;
			height: 40px;
			overflow: auto;

			a {
				cursor: pointer;
				text-decoration: underline;
				color: #0069D2;
			}
		}

		.error-title {
			color: #9e3e0e;
			font-weight: bolder;
			font-size: 14px;
		}
	}


	.edit-success {
		width: 100%;
		height: 50px;
		border: 2px solid #3c9d0b;
		border-radius: 2px;
		line-height: 20px;
		padding: 5px;
		margin-top: 10px;
		padding-top: 4px;
		padding-bottom: 4px;

		div {
			float: left;
			display: inherit;
			margin-left: 10px;
		}

		.success-title {
			color: #3c9d0b;
			font-weight: bolder;
			font-size: 14px;
		}
	}

	.btn-link {
		color: #333;
		outline: none !important;
	}

	.tf-btn-black {
		color: #ffffff;
		padding: 0px;
		height: 26px;
		width: 108px;
		background-color: #333333;
		border-color: #444444;
		outline: none !important;
	}

	.tf-btn-black:hover,
	.tf-btn-black:focus,
	.tf-btn-black:active,
	.open>.dropdown-toggle.tf-btn-black {
		color: #ffffff;
		background-color: #444444;
		border-color: #333333;
	}
}

.applicationterms {
	margin-left: -5px;

	.kendo-grid {
		table {
			width: 100%;
		}

		td.k-group-cell {
			background-color: #ffffff;
		}

		.k-alt td {
			background-color: #ecf2f9;
		}

		div.not-applicable {
			color: #999999;
		}
	}
}

.tabstrip-userdefinedlabel {
	margin-left: -15px;
	margin-right: -15px;
	background-color: #FFFFFF;
	border: 0;

	&:focus {
		-webkit-box-shadow: none;
		box-shadow: none;
	}

	.tabstrip-items {
		margin-left: 10px;

		.k-active {
			span {
				&:first-child {
					font-weight: bold;
				}
			}
		}

		.tabstrip-item {
			margin-left: 5px;
			background-image: none;
			border-color: #9f9f9f;
			background-color: #F2F2F2;

			.k-complete {
				width: 0;
			}
		}

		.k-active {
			background-color: #ffffff;
		}
	}

	.tabstrip-content {
		border-width: 1px 0px 0px 0px;
		padding: 30px 20px 0px 20px;

		.kendo-grid {
			table {
				width: 100%;
			}
		}

		.k-dirty {
			display: none;
		}
	}
}

.restore-button {
	float: right;
	margin-right: 0px;
	margin-top: 5px;
	padding: 0px;
	height: 24px;
	width: 120px;
	background-color: #f2f2f2;
	outline: none !important;
	font-weight: normal;
	text-align: center;
	border: 1px solid #999;
}

.dataentry-container-panel.document-dataentry {
	.controlpanel-container {
		height: calc(~"100% - 120px");
	}

	.description {
		margin-top: -20px;
		color: #666666;
	}
}

.district-policies-grid {
	height: 250px;
}

.controlpanel-container.district-policies-container {
	height: calc(~"100% - 155px");
	overflow: auto;

	.tabstrip-userdefinedlabel {
		.controlpanel-description {
			.kendo-grid {
				.k-grid-container {
					.k-grid-content tbody {
						.k-input.k-numerictextbox {
							width: auto;
							height: 22px;

							.k-input-spinner.k-spin-button {
								height: 22px;

								button.k-button {
									min-width: 17px;
									margin: 0;
								}
							}
						}
					}
				}
			}
		}
	}
}

.controlpanel-container.wayfinder-configurations-container {
	height: calc(~"100% - 103px");
	overflow: auto;
	margin-top: 0px;
}

.controlpanel-container.wayfinder-alerts-container {
	height: calc(~"100% - 103px");
	width: calc(~"100% + 3px");
	margin-top: 0px;
}

.systemconfigurationversion {
	padding: 16px;
	color: #777;
	font-size: 14px;
	font-family: SourceSansPro-Regular;
	height: 50px;
	position: absolute;
	bottom: 40px;
	right: 0
}

@media screen and (max-width:992px) {
	.group-wrapper {
		flex-basis: 46.5% !important;
	}
}

.wayfinderConfigTitle {
	&.row {
		margin-left: 2px;
	}

	&.container {
		width: 100%;
		padding: 15px 32px 0px 27px;
	}
}

.wayfinderConfigPage {
	&.row {
		margin-left: 2px;
	}

	&.container {
		width: 100%;
		padding: 0px 32px 0px 27px;

		&.content-padding {
			padding-top: 0px;
		}

		.tripColor-section {
			padding-left: 15px;

			.tripcolor-grid {
				height: 38px;
				width: 127px;
			}
		}

		>* h4 {
			margin-top: 0px;
		}

		.list-mover-mover {
			.move-button-column {
				max-width: none;
				display: grid;
				place-items: center;

				a {
					max-width: 38px;
				}
			}

			.move-button-column-2 {
				max-width: none;
				margin-left: 0;

				a {
					max-width: 38px;
				}
			}
		}

		.group-wrapper {
			display: flex;
			flex-direction: column;
			flex-basis: 25%;

			.checkbox-title {
				margin-top: 10px;
			}

			.checkbox-group {
				padding: 0px;
				display: flex;
				flex-direction: row;
				align-items: center;

				.seperator {
					flex-basis: 45px;
				}
			}

			.checkbox {
				margin: 10px 0px;

				label {
					padding-left: 15px;

					input {
						vertical-align: top;
					}
				}
			}

			.way-validator {
				color: red;
				padding-left: 15px;
				font-family: "SourceSansPro-Regular", Arial;
				font-size: 11.9px;
			}
		}
	}

	.map-area {
		.map-area-settings {
			position: relative;

			min-height: 322px;

			li.map-tool-bar-item {
				margin-right: 10px;
			}

			.map-expand-button {
				top: calc(100% - 50px);
				left: 15px;
			}
		}
	}
}

.map-area-settings.map-page {

	/* make sure the context menu is above tool bar layer*/
	.right-click-menu {
		z-index: 12032;
	}
}

.wayfinderAlertPage {
	width: 100%;

	.controlpanel-title-group .controlpanel-title {
		font-size: 27px;
		font-family: 'SourceSansPro-SemiBold';
	}

	.grid-wrapper {
		padding: 5px 15px;
		display: flex;
		flex-direction: column;
		font-family: 'SourceSansPro-SemiBold';
	}

	.kendo-treelist {
		table {
			table-layout: fixed;
		}

		.k-selectable {
			margin-left: -0.7px;
		}

		.k-alt {
			background-color: #ecf2f9;
		}

		.item-drag-icon {
			background-image: url(../img/map/thematics/UpDn.svg);
			background-repeat: no-repeat;
			background-position: center center;
			background-size: 8px;
			height: 20px;
			width: 30px;
			cursor: move;
		}

		.k-icon.k-i-none {
			display: none;
		}

		.drop-hint-start {
			border-left-color: #F35800;
			margin-right: -1px;
		}

		.drop-hint-end {
			border-right-color: #F35800;
			margin-left: -1px;
		}

		.drop-hint-line {
			background-color: #F35800;
			height: 2px;
			width: 80%;
		}
	}

	.kendo-treeList-footer {
		text-align: end;
		padding-right: 17px;
		color: #666;
		line-height: 30px
	}
}

.eta-container {
	float: left;
	width: 100%;
	height: 40px;

	.early-title,
	.late-title {
		float: left;
		width: 100px;
		margin-top: 2px;
	}

	.early-container,
	.late-container {
		float: left;

		button {
			background-color: #ffffff;
			background-position: center;
			background-repeat: no-repeat;
			height: 25px;
			border: 1px solid #ccc;
			width: 25px;
			float: left;

			&.plus-button {
				background-image: url(../../Global/img/detail-screen/plus.svg);
				border-left: none;
			}

			&.minus-button {
				background-image: url(../../Global/img/detail-screen/minus.svg);
				border-right: none;
			}
		}

		input {
			width: 150px;
			padding-left: 4px;
			border-radius: 0;
			height: 25px;
			text-align: center;
			float: left;

			&::-webkit-outer-spin-button,
			&::-webkit-inner-spin-button {
				-webkit-appearance: none;
				margin: 0;
			}

			&[type=number] {
				-moz-appearance: textfield;
			}
		}
	}
}