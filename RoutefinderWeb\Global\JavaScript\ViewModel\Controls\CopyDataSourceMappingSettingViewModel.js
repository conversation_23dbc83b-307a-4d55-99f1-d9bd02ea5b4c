(function()
{
	createNamespace("TF.Control").CopyDataSourceMappingSettingViewModel = CopyDataSourceMappingSettingViewModel;

	function CopyDataSourceMappingSettingViewModel(options)
	{
		var self = this;
		self.dataSources = options.dataSources;
		self.toCopyDBIDs = [];
	}

	CopyDataSourceMappingSettingViewModel.prototype.init = function(viewModel, element)
	{
		let self = this;
		self.$element = $(element);
		const UI_ELEMENT_ID = "#CopyDataSourceDDL";
		const $CopyDataSourceDDL = self.$element.find(UI_ELEMENT_ID);

		const setCopyDataSourceDropDownListItem = (kendoMultiSelectDataElement) =>
		{
			const elements = kendoMultiSelectDataElement.ul.find("li");

			elements.each(index =>
			{
				const element = $(elements[index]);
				element.css("background-color", "transparent");
				const input = element.find("input[type='checkbox']");
				input.prop("checked", element.hasClass(TF.KendoClasses.STATE.SELECTED));
			});
		}

		self.kendoMultiSelectData = null;
		const options = {
			dataTextField: "text",
			dataValueField: "value",
			itemTemplate: '<input type="checkbox" style="margin-right: 5px"/> #= text #',
			downArrow: true,
			autoClose: false,
			dataSource: self.dataSources,
			value: self.toCopyDBIDs,
			select: function(e)
			{
				e.preventDefault();
				// to prevent list to auto scroll
				var offset = this.list.offset().top - this.ul.offset().top + 1;
				var dataItem = e.dataItem;
				self.toCopyDBIDs = self.kendoMultiSelectData.value();
				self.toCopyDBIDs.push(dataItem.value);
				self.kendoMultiSelectData.value(self.toCopyDBIDs);
				this.list.find(".k-list-scroller").scrollTop(offset);

				setCopyDataSourceDropDownListItem(self.kendoMultiSelectData);
			},
			deselect: function(e)
			{
				e.preventDefault();
				// to prevent list to auto scroll
				var offset = this.list.offset().top - this.ul.offset().top + 1;
				var dataItem = e.dataItem;
				this.list.find(".k-list-scroller").scrollTop(offset);

				self.toCopyDBIDs = self.kendoMultiSelectData.value();
				self.toCopyDBIDs = self.toCopyDBIDs.filter(x => x !== dataItem.value);
				self.kendoMultiSelectData.value(self.toCopyDBIDs);

				setCopyDataSourceDropDownListItem(self.kendoMultiSelectData);
			},
			close: function()
			{
				self.kendoMultiSelectData.isOpen = false;
			},
			dataBound: function()
			{
				self.kendoMultiSelectData && setCopyDataSourceDropDownListItem(self.kendoMultiSelectData);
			}
		};

		$CopyDataSourceDDL.kendoMultiSelect(options);
		self.kendoMultiSelectData = $CopyDataSourceDDL.data("kendoMultiSelect");
		
		setCopyDataSourceDropDownListItem(self.kendoMultiSelectData);
	};

	CopyDataSourceMappingSettingViewModel.prototype.apply = function()
	{
		if (!this.toCopyDBIDs.length)
		{
			return tf.promiseBootbox.alert({
				message: "Please select at least one data source.",
				title: "No Data Source Selected"
			}).then(() =>
			{
				return false;
			});
		}

		return Promise.resolve(this.toCopyDBIDs);
	}

	CopyDataSourceMappingSettingViewModel.prototype.cancel = function()
	{
		return Promise.resolve(false);
	}

	CopyDataSourceMappingSettingViewModel.prototype.dispose = function()
	{
		if (this.kendoMultiSelectData)
		{
			this.kendoMultiSelectData.destroy();
		}
	};
})();