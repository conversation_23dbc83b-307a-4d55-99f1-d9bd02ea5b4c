﻿(function()
{
	createNamespace('TF.Control').MessageViewModel = MessageViewModel;

	function MessageViewModel(modalViewModel, messages)
	{
		this.modalViewModel = modalViewModel;
		this.messages = messages;
	}

	MessageViewModel.prototype.init = function(viewModel, e)
	{
		var self = this;
		this.element = $(e);
		var gridElement = this.element.find(".kendo-grid").kendoGrid({
			dataSource: {
				data: this.messages
			},
			height: 400,
			scrollable: true,
			selectable: true,
			columns: [
				// {
				// 	width: '30px',
				// 	type: "image",
				// 	template: function(abc)
				// 	{
				// 		return abc.read ? "" : '<div class="unread-marker"></div>';
				// 	},
				// 	attributes: {
				// 		"class": "text-center"
				// 	}
				// },
				{ field: "Description", title: "Description" },
				{ field: "StartDate", title: "Start Date", width: '150px' }
			]
		});

		var grid = gridElement.data("kendoGrid");

		var $gridContent = this.element.find(".k-grid-content");
		$gridContent.css({
			"overflow-y": "auto"
		});

		if ($gridContent.children('table').height() <= $gridContent.height())
		{
			this.element.find('.k-grid-header').css({
				'padding-right': '0'
			});
		}

		$gridContent.delegate("table tr", "dblclick", function()
		{
			var dataItem = grid.dataItem(grid.select()[0]);
			self.modalViewModel.negativeClick();
			tf.modalManager.showModal(new TF.Modal.MessageViewModalViewModel(dataItem, self.messages));
		});
	};

})();