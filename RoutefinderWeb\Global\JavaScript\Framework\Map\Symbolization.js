﻿// Map Symbol Library
(function()
{
	createNamespace("TF.Map").Symbolization2 = Symbolization2;

	function Symbolization2()
	{
		this.defaultColor = "#FF5500";
		this.selectionColor = "#B0FFFF";
		this.colorList = ["#FF0800", "#8800FF", "#3333FF", "#FF6700", "#FF00FF", "#00FFFF", "#73D952", "#FFFF00", "#AA0000", "#0000A2", "#CC5200", "#E10087", "#00CCCC", "#006600", "#FFCC00", "#D47F7F", "#7F7FD0", "#E5A87F", "#F07FC3", "#7FE5E5", "#7FB27F", "#FFE57F"];

	};

	Symbolization2.prototype.DirectionsWalking = function(hexColor)
	{
		var symbol;
		require(["esri/symbols/SimpleLineSymbol"],
			function(SimpleLineSymbol)
			{
				hexColor = hexColor ? hexColor : "#ADADAD";
				symbol = new SimpleLineSymbol().setWidth(4).setColor(hexColor).setStyle(SimpleLineSymbol.STYLE_SHORTDASH);
			});
		return symbol;
	};

	Symbolization2.prototype.DirectionsDestination = function()
	{
		var symbol;
		require(["esri/symbols/SimpleLineSymbol", "esri/Color"],
			function(SimpleMarkerSymbol, Color)
			{
				symbol = new SimpleMarkerSymbol().setSize(20).setColor("#FF0000").setPath("M16,3.5c-4.142,0-7.5,3.358-7.5,7.5c0,4.143,7.5,18.121,7.5,18.121S23.5,15.143,23.5,11C23.5,6.858,20.143,3.5,16,3.5z M16,14.584c-1.979,0-3.584-1.604-3.584-3.584S14.021,7.416,16,7.416S19.584,9.021,19.584,11S17.979,14.584,16,14.584z").setStyle(SimpleMarkerSymbol.STYLE_PATH);
			});
		return symbol;
	};

	Symbolization2.prototype.DirectionsVertex = function()
	{
		var symbol;
		require(["esri/symbols/SimpleMarkerSymbol", "esri/symbols/SimpleLineSymbol", "esri/Color"],
			function(SimpleMarkerSymbol, SimpleLineSymbol, Color)
			{
				symbol = new SimpleMarkerSymbol().setSize(5).setColor("#FFFFFF").setOutline(new SimpleLineSymbol().setColor("#00A2E8").setWidth(1));
			});
		return symbol;
	};

	Symbolization2.prototype.DirectionsAdditionalStop = function()
	{
		var symbol;
		require(["esri/symbols/SimpleMarkerSymbol"],
			function(SimpleMarkerSymbol)
			{
				symbol = new SimpleMarkerSymbol().setSize(8).setColor("#FFFFFF");
			});
		return symbol;
	};

	Symbolization2.prototype.DirectionsTips = function()
	{
		var symbol;
		require(["esri/symbols/Font", "esri/symbols/TextSymbol"],
			function(Font, TextSymbol)
			{
				var font = new Font();
				font.setFamily("Microsoft Yahei");
				font.setSize(13);
				font.setWeight(Font.WEIGHT_BOLD);

				symbol = new TextSymbol().setFont(new Font().setFamily("Calibri Light")).setText("Click to change route").setOffset(-60, 10);

			});
		return symbol;
	};

	Symbolization2.prototype.DirectionsGhost = function()
	{
		var symbol;
		require(["esri/symbols/SimpleMarkerSymbol", "esri/symbols/SimpleLineSymbol"],
			function(SimpleMarkerSymbol, SimpleLineSymbol)
			{
				symbol = new SimpleMarkerSymbol().setSize(10).setColor("#FFFFFF").setOutline(new SimpleLineSymbol().setColor("#C0C0C0").setWidth(3));
			});
		return symbol;
	};
})();