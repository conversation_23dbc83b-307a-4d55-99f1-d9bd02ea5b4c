﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.StudentExceptionDataModel = function(studentExceptionEntity)
	{
		namespace.BaseDataModel.call(this, studentExceptionEntity);
	}

	namespace.StudentExceptionDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.StudentExceptionDataModel.prototype.constructor = namespace.StudentExceptionDataModel;

	namespace.StudentExceptionDataModel.prototype.mapping = [
		{ from: "StudentExceptionId", default: 0 },
		{ from: "StudId", default: -1 },
		{ from: "AltSiteId", default: -1 },
		{ from: "TripId", default: -1 },
		{ from: "Tripstopid", default: null },
		{ from: "TripStopStreet", default: "" },
		{ from: "SessionId", default: -1 },
		{ from: "RouteLegId", default: -1 },
		{ from: "ExceptionType", default: -1 },
		{ from: "FromSchool", default: "" },
		{ from: "ToSchool", default: "" },
		{ from: "Comment", default: "" },
		{ from: "Day1Flag", default: false },
		{ from: "Day2Flag", default: false },
		{ from: "Day3Flag", default: false },
		{ from: "Day4Flag", default: false },
		{ from: "Day5Flag", default: false },
		{ from: "Day6Flag", default: false },
		{ from: "Day7Flag", default: false },
		{ from: "Day8Flag", default: false },
		{ from: "StartDate", default: null },
		{ from: "EndDate", default: null },
		{ from: "ActionFlag", default: 0 },
		{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
		{ from: "LastUpdatedId", default: 0 },
		{ from: "LastUpdatedName", default: "" },
		{ from: "LastUpdatedType", default: 0 },
		{ from: "AltSite", default: null },
		{ from: "Trip", default: null },
		{ from: "TripStop", default: null }
	];
})();