﻿(function()
{
	var valueHandler = ko.bindingHandlers.value.update;
	ko.bindingHandlers.value.update = function(element, valueAccessor, allBindings, viewModel, bindingContext)
	{
		if (allBindings().fireChangeEvent)
		{
			valueHandler.apply(this, arguments);
			ko.utils.unwrapObservable(valueAccessor());
			$(element).trigger("change");
		}
	}
	ko.bindingHandlers.integer = {
		init: function(element, valueAccessor)
		{
			$(element).on("keydown", function(event)
			{
				// Allow: backspace, delete, tab, escape, and enter
				if (event.keyCode == 46 || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 27 || event.keyCode == 13 ||
					// Allow: Ctrl+A
					(event.keyCode == 65 && event.ctrlKey === true) ||
					// Allow: home, end, left, right
					(event.keyCode >= 35 && event.keyCode <= 39))
				{
					// let it happen, don't do anything
					return;
				} else
				{
					// Ensure that it is a number and stop the keypress
					if (event.shiftKey || (event.keyCode < 48 || event.keyCode > 57) && (event.keyCode < 96 || event.keyCode > 105))
					{
						event.preventDefault();
					}
				}
			});
			//.on("keyup", function(event)
			//{
			//	if (this.value === "")
			//	{
			//		return;
			//	}
			//	if (parseInt(this.value) < 1 || parseInt(this.value) > 31)
			//		this.value = this.value.substring(0, this.value.length - 1);
			//});
		}
	};
})();
