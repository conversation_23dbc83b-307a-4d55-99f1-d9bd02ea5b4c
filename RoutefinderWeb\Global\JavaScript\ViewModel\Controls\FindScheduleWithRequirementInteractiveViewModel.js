(function()
{
	createNamespace("TF.Control").FindScheduleWithRequirementInteractiveViewModel = FindScheduleWithRequirementInteractiveViewModel;

	var midDaySession = 3;

	function WeekDaysGroup(data)
	{
		var self = this;
		self.disable = ko.observable();
		self.weekDayButtons = [];
		TF.DayOfWeek.allValues.forEach(function(v)
		{
			self.weekDayButtons.push(new WeekDayButton({
				dayOfWeek: v,
				dateRangeHandler: data.dateRangeHandlers[v],
				dataContext: data.dataContext,
				dateRanges: data.dateRanges[v]
			}));
		});

		self.checked = ko.computed(function()
		{
			return self.weekDayButtons.some(function(weekDayButton)
			{
				return weekDayButton.checked();
			});
		});
	}

	WeekDaysGroup.template = "<div data-bind='stopBinding:true'><div class='studentScheduleDaysPicker' style='width:200px'>"
		+ '<!-- ko foreach:weekDayButtons -->'
		+ "<span data-bind='text:text,css:{checked:checked,disable:(disable() || $parent.disable())}, click:(disable() || $parent.disable()) ? null : toggle'></span>"
		+ '<!--/ko-->'
		+ "</div></div>";

	function WeekDayButton(data)
	{
		var self = this;
		self.dayOfWeek = data.dayOfWeek;
		self._dataContext = data.dataContext;
		self._dateRangeHandler = data.dateRangeHandler;
		self.text = TF.DayOfWeek.toString(data.dayOfWeek, 2);
		self.checked = ko.observable(false);
		self._dateRanges = data.dateRanges;
		self.disable = ko.observable(!self._dateRangeHandler.canOccupy(self._dateRanges));
		self._dateRangeHandler.availableDateRangesChanged.subscribe(function()
		{
			self.disable(!self.checked() && !self._dateRangeHandler.canOccupy(self._dateRanges));
		});

		self.checked.subscribe(function()
		{
			if (self.checked())
			{
				self._dateRangeHandler.occupy(self._dateRanges, self._dataContext);
				return;
			}

			if (!self.checked())
			{
				self._dateRangeHandler.release(self._dateRanges);
				return;
			}
		});
	}

	WeekDayButton.prototype.toggle = function()
	{
		if (this.disable())
		{
			return;
		}

		this.checked(!this.checked());
	};

	function DateRangeHandler(data)
	{
		this._dateRanges = data.dateRanges;
		this.occupiedDateRangesList = [];
		this.occupiedDataContexts = [];
		this._availableDateRanges = null;
		this.availableDateRangesChanged = new TF.Events.Event();
	}

	DateRangeHandler.prototype.occupy = function(dateRanges, dataContext)
	{
		var index = this.occupiedDateRangesList.indexOf(dateRanges);
		if (index > -1)
		{
			return;
		}

		this.occupiedDateRangesList.push(dateRanges);
		this.occupiedDataContexts.push(dataContext);
		this._availableDateRanges = null;
		this.availableDateRangesChanged.notify({ sender: this, occupy: true, dateRanges: dateRanges, dataContext: dataContext });
	};

	DateRangeHandler.prototype.release = function(dateRanges)
	{
		var index = this.occupiedDateRangesList.indexOf(dateRanges);
		if (index > -1)
		{
			this.occupiedDateRangesList.splice(index, 1);
			var dataContexts = this.occupiedDataContexts.splice(index, 1);
			this._availableDateRanges = null;
			this.availableDateRangesChanged.notify({ sender: this, release: true, dateRanges: dateRanges, dataContext: dataContexts[0] });
		}
	};

	DateRangeHandler.prototype.getAvailableDateRanges = function()
	{
		if (this._availableDateRanges != null)
		{
			return this._availableDateRanges;
		}

		var occupiedDateRanges = [];
		this.occupiedDateRangesList.forEach(function(dateRanges)
		{
			occupiedDateRanges = occupiedDateRanges.concat(dateRanges);
		});

		this._availableDateRanges = TF.Helper.FindStudentScheduleHelper.truncateDateRanges(this._dateRanges, occupiedDateRanges);
		return this._availableDateRanges;
	};

	DateRangeHandler.prototype.canOccupy = function(dateRanges)
	{
		if (!dateRanges)
		{
			return false;
		}

		var availableDateRanges = this.getAvailableDateRanges();
		return availableDateRanges.some(function(availableDateRange)
		{
			return dateRanges.some(function(dateRange)
			{
				return !!TF.Helper.FindStudentScheduleHelper.getIntersectedDate(availableDateRange, dateRange);
			});
		});
	};

	function FindScheduleWithRequirementInteractiveViewModel(modalViewModel, data)
	{
		this.modalViewModel = modalViewModel;
		this.student = data.student;
		this.requirements = data.requirements;
		this.selectedRequirement = ko.observable(this.requirements[0]);
		this.initData();
	}

	FindScheduleWithRequirementInteractiveViewModel.prototype.initData = function()
	{
		var self = this;
		self.requirementData = {};
		self.tripStopData = {};
		self.requirements.forEach(function(r)
		{
			r.startDate = r.requirementEntity.StartDate ? moment(r.requirementEntity.StartDate).format("YYYY/MM/DD") : r.requirementEntity.StartDate;
			r.endDate = r.requirementEntity.EndDate ? moment(r.requirementEntity.EndDate).format("YYYY/MM/DD") : r.requirementEntity.EndDate;
			self.requirementData[r.id] = r.requirementEntity;
			r.sessionId = r.requirementEntity.SessionID;
			delete r.requirementEntity;
			r.dateRangeHandlers = {};
			TF.DayOfWeek.allValues.forEach(function(v)
			{
				r.dateRangeHandlers[v] = new DateRangeHandler({ dateRanges: r.availableDateRanges[v] });
			});

			r.validStops.forEach(function(s)
			{
				s.tripInfo.session = s.tripStopEntity.trip.Session;
				self.tripStopData[s.id] = s.tripStopEntity;
				delete s.tripStopEntity;
				s.weekDaysGroup = new WeekDaysGroup({ dateRanges: s.availableDateRanges, dateRangeHandlers: r.dateRangeHandlers, dataContext: s });
			});

			var dateRange = r.startDate && r.endDate ? r.startDate + " - " + r.endDate : (!r.startDate && !r.endDate ? "All" : (r.startDate ? "Starting " + r.startDate : "Until " + r.endDate));
			r.displayField = r.session + " - " + r.location + " - (" + dateRange + ")";
		});

		self.currentSelectedStops = ko.computed(function()
		{
			return self.selectedRequirement().validStops.filter(function(s)
			{
				return s.weekDaysGroup.checked();
			});
		});

		self.selectedStops = ko.computed(function()
		{
			var selectedStops = [];
			self.requirements.forEach(function(r)
			{
				selectedStops = selectedStops.concat(r.validStops.filter(function(s)
				{
					return s.weekDaysGroup.checked();
				}));
			});

			return selectedStops;
		});

		self.assignedMidDayTripSessions = ko.computed(function()
		{
			var result = {}, selectedStops = self.selectedStops();
			selectedStops.forEach(function(stop)
			{
				if (stop.tripInfo.session == midDaySession)
				{
					result[stop.tripInfo.id] = self.selectedRequirement().sessionId;
				}
			});

			return result;
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.init = function(viewModel, element)
	{
		this.$element = $(element);
		this.initDropDown();
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.initGrid = function()
	{
		var self = this;
		if (self.kendoGrid)
		{
			self.$element.find(".kendo-grid").kendoGrid('destroy').empty();
			self.kendoGrid = null;
		}

		this.$element.find(".kendo-grid").kendoGrid({
			dataSource: new kendo.data.DataSource({
				data: this.selectedRequirement().validStops
			}),
			columns: [
				{
					title: "Trip Name",
					width: 150,
					template: "<div>#: tripInfo.name #</div>",
				}, {
					title: "Trip Stop",
					field: "name",
					width: 150,
					template: "<div>#: name # #:(type=='door to door' || type=='stop pool') ? '('+type+')':'' #</div>",
				}, {
					title: "Days",
					width: 200,
					field: "_availableDays",
					template: WeekDaysGroup.template
				}, {
					title: "Trip Time",
					template: "<div>#: tripInfo.startTime #  -  #: tripInfo.finishTime#</div>",
				}, {
					title: tf.authManager.hasTripDates() ? "Date Intervals" : "Effective Dates",
					width: 400,
					template: dateItem => TF.Helper.KendoGridHelper.renderTripDateIntervalWithoutPast(dateItem.tripInfo)
				}, {
					title: "Max Studs",
					width: 100,
					template: "<div>#: tripInfo.maxStudents # </div>",
				}, {
					title: "Capacity",
					width: 100,
					template: "<div>#: tripInfo.capacity ==null?'66':tripInfo.capacity # </div>",
				}, {
					title: "Stop Count",
					width: 100,
					template: "<div>#: tripInfo.stopCount # </div>",
				}, {
					title: "Stop Time",
					width: 100,
					field: "stopTime"
				}, {
					command: [
						{
							name: "view",
							template: "<a class=\"k-button k-button-icontext k-grid-view\" title=\"View\"></a>",
							click: self.detailClick.bind(self)
						}],
					title: "Details",
					attributes: {
						"class": "text-center"
					},
					width: 70,
				}
			],
			height: 250,
			selectable: "multiple",
			sortable: false,
			pageable: {
				pageSize: 5000,
				messages: {
					display: ""
				},
				alwaysVisible: false
			},
			scrollable: true,
			resizable: true,
			dataBound: function(e)
			{
				self.kendoGrid = self.kendoGrid || e.sender;
				self.initWeekDaysGroups();
				refreshSelection(e);
				self.initGridFooter();
			},
			change: function(e)
			{
				if (!e.target || e.target.type != "checkbox")
				{
					refreshSelection(e);
				}
			}
		});

		function refreshSelection(e)
		{
			e.preventDefault();
			e.stopPropagation && e.stopPropagation();
			var allRows = e.sender.content.find("tr");
			for (var i = 0; i < allRows.length; i++)
			{
				if (e.sender.dataItem(allRows[i]).selected)
				{
					$(allRows[i]).addClass(TF.KendoClasses.STATE.SELECTED)
				} else
				{
					$(allRows[i]).removeClass(TF.KendoClasses.STATE.SELECTED)
				}
			}
		}

		this.kendoGrid = this.$element.find(".kendo-grid").data("kendoGrid");
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.initGridFooter = function()
	{
		var self = this, footer = self.kendoGrid.pager.element;
		footer.html("<div data-bind='stopBinding:true'><div class='custom-footer' data-bind=\"text:selectedCount()+' of '+count()+' selected'\"></div></div>");
		var element = footer.find('.custom-footer')[0];
		ko.cleanNode(element);
		ko.applyBindings({
			selectedCount: ko.computed(function()
			{
				return self.currentSelectedStops().length;
			}),
			count: ko.computed(function()
			{
				return self.selectedRequirement().validStops.length;
			})
		}, element);
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.initWeekDaysGroups = function()
	{
		var self = this;
		self.kendoGrid.items().each(function(index, item)
		{
			var dataItem = self.kendoGrid.dataItem(item);
			var trip = dataItem.tripInfo;
			var disable = false;
			if (trip.session == midDaySession)
			{
				var assignedSession = self.assignedMidDayTripSessions()[trip.id];
				if (assignedSession != null && assignedSession != self.selectedRequirement().sessionID)
				{
					disable = true;
				}
			}

			ko.cleanNode(item);
			dataItem.weekDaysGroup.disable(disable);
			ko.applyBindings(dataItem.weekDaysGroup, $(item).find('.studentScheduleDaysPicker')[0]);
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.initDropDown = function()
	{
		var self = this;
		var data = self.requirements;
		this.$element.find("#dropdownlist").kendoDropDownList({
			dataSource: data,
			dataTextField: "displayField",
			dataValueField: "id",
			headerTemplate: '<table style="margin: 0 4px 0 4px;"><tr>' +
				'<td style="width:166px" class="schedule-hd-td">Session</td>' +
				'<td style="width:166px" class="schedule-hd-td">Location</td>' +
				'<td style="width:166px" class="schedule-hd-td">Days</td>' +
				'<td style="width:167px" class="schedule-hd-td">Effective Dates</td></tr></table>',
			template: '<table><tr>' +
				'<td style="width:166px" class="schedule-td">${session}</td>' +
				'<td style="width:166px" class="schedule-td">${location}</td>' +
				'<td style="width:166px" class="schedule-td">${daysString}</td>' +
				'<td style="width:167px" class="schedule-td">#:TF.Helper.KendoGridHelper.renderDateRange(startDate, endDate)#</td></tr></table>',
			dataBound: function()
			{
				this.select(0);
				self.selectedRequirement(data[0]);
				self.initGrid();
			},
			change: function()
			{
				var id = this.value(), req = Enumerable.From(data).FirstOrDefault(null, function(s) { return s.id == id });
				self.selectedRequirement(req);
				self.initGrid();
			}
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.detailClick = function(event)
	{
		var selectStop = this.getSelectedFullData(event);
		var stopEntity = this.tripStopData[selectStop.id];
		stopEntity.tripInfo = selectStop.tripInfo;
		tf.modalManager.showModal(new TF.Modal.Grid.TripDetailModalViewModel(this.student, selectStop.tripInfo.id, stopEntity));
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.getSelectedFullData = function(event)
	{
		var row = $(event.target.closest("tr"));
		var allRows = row.parent().find("tr");
		var currentIndex = -1;
		for (var i = 0; i < allRows.length; i++)
		{
			var element = allRows[i];
			if (element == row[0])
			{
				currentIndex = i;
				break;
			}
		}

		return this.kendoGrid.dataItem(allRows[currentIndex]);
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.attachAssignedData = function()
	{
		var self = this;
		self.requirements.forEach(function(r)
		{
			r.requirementEntity = self.requirementData[r.id];
			r.validStops.forEach(function(s)
			{
				s.tripStopEntity = self.tripStopData[s.id];
			});

			var restDateRanges = r.availableDateRanges;
			$.each(r.dateRangeHandlers, function(k, dateRangeHandler)
			{
				var currentRestDateRanges = restDateRanges[k];
				dateRangeHandler.occupiedDataContexts.forEach(function(tripStop)
				{
					var intersection = {};
					currentRestDateRanges = TF.Helper.FindStudentScheduleHelper.truncateDateRanges(currentRestDateRanges, tripStop.availableDateRanges[k], intersection);
					if (intersection.dateRanges && intersection.dateRanges.length)
					{
						tripStop.assignedDateBlocks = tripStop.assignedDateBlocks || {};
						tripStop.assignedDateBlocks[k] = intersection.dateRanges;
					}
				});

				var isFullSchedule = !currentRestDateRanges.length;
				r.isFullSchedule = r.isFullSchedule == null ? isFullSchedule : (r.isFullSchedule && isFullSchedule);
			});
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.acceptClick = function()
	{
		this.modalViewModel.hide();
		this.attachAssignedData();
		this.modalViewModel.resolve({
			requirements: this.requirements,
			status: "accept"
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.skipClick = function()
	{
		this.modalViewModel.hide();
		this.modalViewModel.resolve({
			status: "skip"
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.finishClick = function()
	{
		this.modalViewModel.hide();
		this.attachAssignedData();
		this.modalViewModel.resolve({
			requirements: this.requirements,
			status: "finish"
		});
	};

	FindScheduleWithRequirementInteractiveViewModel.prototype.cancelClick = function()
	{
		this.modalViewModel.hide();
		this.modalViewModel.resolve({
			status: "cancel"
		});
	};
})();