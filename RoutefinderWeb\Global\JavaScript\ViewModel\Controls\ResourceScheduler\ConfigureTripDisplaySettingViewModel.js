(function()
{
	createNamespace('TF.Control.ResourceScheduler').ConfigureTripDisplaySettingViewModel = ConfigureTripDisplaySettingViewModel;
	const DISPLAY_SETTING_KEY = "rfweb.resourceScheduler.tripDisplaySetting";

	function ConfigureTripDisplaySettingViewModel(shortCutKeyHashMapKeyName, obDisableControl)
	{
		this.initComponent();
		this.obDisableControl = obDisableControl;
		// parameters: availableColumns, selectedColumns, defaultLayoutColumns, shortCutKeyHashMapKeyName, isMiniGrid, noLoadingIndicator, disableControl
		TF.Grid.EditKendoColumnViewModel.call(this, this.availableFields, this.savedSelectedColumns, null, shortCutKeyHashMapKeyName, true, false, this.obDisableControl);
	}

	ConfigureTripDisplaySettingViewModel.prototype = Object.create(TF.Grid.EditKendoColumnViewModel.prototype);

	ConfigureTripDisplaySettingViewModel.prototype.constructor = ConfigureTripDisplaySettingViewModel;

	ConfigureTripDisplaySettingViewModel.prototype.initComponent = function()
	{
		this.allFields = tf.tripGridDefinition.gridDefinition().Columns.filter(col =>
			!col.lazyLoad && (!col.UDFId || !["roll-up", "case"].includes(col.UDFType)) && !col.onlyForFilter && !col.DisplayName?.startsWith("PDE") && col.FieldName !== "DateIntervals" && col.type !== "image");
		this.allFields.filter(f => f.ParentField).forEach(f => delete f.ParentField); // field with parent will filter out in EditKendoColumnViewModel
		const defaultColNames = ["Name", "StartTime", "FinishTime"];
		this.defaultColumns = this.allFields.filter(f => defaultColNames.includes(f.FieldName));
		const savedSetting = tf.userPreferenceManager.get(DISPLAY_SETTING_KEY);

		const savedSelectedColumns = savedSetting?.selectedColumns || [];
		let availableColumns = savedSelectedColumns.map(c =>
			this.allFields.find(f => f.FieldName === c.fieldName && (!c.isUdf || f.UDFDataSources.some(d => d.DBID === tf.datasourceManager.databaseId)))
		).filter(c => !!c);
		availableColumns.length === 0 && (availableColumns = this.defaultColumns);
		this.savedSelectedColumns = availableColumns;

		this.obDisplayMode = ko.observable(savedSetting?.displayMode || 0); //Default Display
		this.obSeparator = ko.observable(savedSetting?.separator || ""); //Default separator is "-"
		this.obIncludeFieldLabel = ko.observable(savedSetting?.includeFieldLabel || false); // Include field labels
		this.availableFields = this.allFields.filter(f => !this.savedSelectedColumns.some(c => c.FieldName === f.FieldName));
	}

	ConfigureTripDisplaySettingViewModel.prototype.init = function(viewModel, el)
	{
		TF.Grid.EditKendoColumnViewModel.prototype.init.call(this, viewModel, el);
		[this.obSeparator, this.obIncludeFieldLabel, this.obselectedColumns].forEach(obVariable =>
		{
			obVariable.subscribe(() => this.obDisplayMode(1));
		});
	};

	ConfigureTripDisplaySettingViewModel.prototype._moveItemUpDown = function(targetIdx)
	{
		TF.Grid.EditKendoColumnViewModel.prototype._moveItemUpDown.call(this, targetIdx);
		this.obDisplayMode(1);
	}

	ConfigureTripDisplaySettingViewModel.prototype._setCurrentDisplaySetting = async function()
	{
		const setting = {
			displayMode: this.obDisplayMode(),
			separator: this.obSeparator(),
			includeFieldLabel: this.obIncludeFieldLabel(),
			selectedColumns: this.selectedColumns.map(c => ({ fieldName: c.FieldName, isUdf: !!c.UDFId })),
		}

		tf.userPreferenceManager.save(DISPLAY_SETTING_KEY, setting);
	}


	ConfigureTripDisplaySettingViewModel.prototype.apply = async function()
	{
		await TF.Grid.EditKendoColumnViewModel.prototype.apply.call(this); // adjust the sequence
		await this._setCurrentDisplaySetting();
		return true;
	}


	ConfigureTripDisplaySettingViewModel.prototype.reset = function()
	{
		this.toAllLeftClick();
		this.obIncludeFieldLabel(false);
		this.obSeparator("");
		this.toRightWithSpecifiedItems(this.defaultColumns, "FieldName");
		this.obDisplayMode(0);
	}
})();
