﻿(function()
{
	createNamespace('TF.Modal').GeoFinderSettingModalViewModel = GeoFinderSettingModalViewModel;

	function GeoFinderSettingModalViewModel(findType)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/GeoFinderSettingControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Apply");
		//this.GeoFinderSettingViewModel = new TF.Control.GeoFinderSettingViewModel();
		//this.data(this.GeoFinderSettingViewModel);
		this.sizeCss = "modal-dialog-md";
		var type = findType == "walkout" ? "Walkout" : "Drive To";
		this.title("Geofinder " + type + " Options");
		//this.obPositiveButtonLabel("Create Trip");
		this.viewModel = new TF.Control.GeoFinderSettingViewModel(findType);
		this.data(this.viewModel);
	}

	GeoFinderSettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	GeoFinderSettingModalViewModel.prototype.constructor = GeoFinderSettingModalViewModel;

	GeoFinderSettingModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	GeoFinderSettingModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};

})();
