﻿(function()
{
	createNamespace("TF.Grid").ModifyReminderFilterViewModel = ModifyReminderFilterViewModel;

	ModifyReminderFilterViewModel.prototype = Object.create(TF.Control.BaseControl.prototype);
	ModifyReminderFilterViewModel.prototype.constructor = ModifyReminderFilterViewModel;

	function ModifyReminderFilterViewModel(gridType, isNew, gridFilterDataModel, headerFilters)
	{
		//createNamespace("tf.debug").ModifyReminderFilterViewModel = this;

		this.obGridTypes = ko.observableArray();
		this.obGridTypes(TF.ReminderHelper.getGridTypeList());
		this.obSelectedGridType = ko.observable(this.obGridTypes()[0]);
		this.obGridDefinition = ko.observable();
		this.obGridDefinitionComputer = ko.computed(this._gridDefinitionComputer, this);

		this.isNew = isNew;

		if (!gridFilterDataModel)
		{
			gridFilterDataModel = new TF.DataModel.GridFilterDataModel();
			gridFilterDataModel.gridType(this.obSelectedGridType().GridType);
		}
		else
		{
			gridFilterDataModel = gridFilterDataModel.clone();
			if (isNew)
			{
				gridFilterDataModel.id(0);
				gridFilterDataModel.apiIsNew(true);
				gridFilterDataModel.apiIsDirty(false);
			}
		}
		this.gridFilterDataModel = gridFilterDataModel;
		this.obGridDefinitionColumns = ko.computed(this._excludePersistenceNameIsNullComputer, this);
		this.textAreaElement = ko.observable(null);
		this.obValueFieldType = ko.observable("Disabled");
		this.obValueFieldValue = ko.observable("");
		this.obApplyOnSave = ko.observable(false);
		this.selectFieldOpen = false;
		this.selectOperatorOpen = false;
		this.selectLogicalOperatorOpen = false;
		//this._gridType = gridType;

		this.obStatementVerify = ko.observable(false);
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obSuccessMessageDivIsShow = ko.observable(false);//useless for now, may use later
		this.obValidationErrors = ko.observableArray([]);
		this.obSucessmessage = ko.observable();

		if (headerFilters)
		{
			var searchData = new TF.SearchParameters(null, null, null, headerFilters, null, null, null);
			tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(this.obSelectedGridType().GridType), "RawFilterClause"), {
				data: searchData.data.filterSet
			})
				.then(function(apiResponse)
				{
					this.gridFilterDataModel.whereClause((this.gridFilterDataModel.whereClause() ? this.gridFilterDataModel.whereClause() + " AND " : "") + apiResponse.Items[0]);
				}.bind(this))
				.catch(function()
				{
					this.pageLevelViewModel.obErrorMessageDivIsShow(true);
					this.pageLevelViewModel.obValidationErrors([{ name: "error", message: "failed to convert header filters to SQL" }]);
				}.bind(this));
		}

		//drop down list
		this.obOperatorSource = ko.observable([" ", "=", "<>", ">", "<", ">=", "<=", "LIKE"]);
		this.obLogicalOperatorSource = ko.observable([" ", "AND", "OR"]);
		this.selectedOperator = ko.observable("");
		this.selectedOperator.subscribe(this.selectOperatorClick.bind(this));
		this.selectedLogicalOperator = ko.observable("");
		this.selectedLogicalOperator.subscribe(this.selectLogicalOperatorClick.bind(this));

		this.obSelectedField = ko.observable();
		this.obSelectedFieldText = ko.observable();
		this.obSelectedFieldText.subscribe(this.selectFieldClick.bind(this));

		this.obSelectedGridTypeText = ko.observable(this.obSelectedGridType() ? this.obSelectedGridType().GridTypeName : "");
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	ModifyReminderFilterViewModel.prototype._gridDefinitionComputer = function()
	{
		if (this.obSelectedGridType() && this.obSelectedGridType().GridType)
		{
			var gridDefinition = (new TF.ReminderHelper.gridDefinitionConstructorTable[this.obSelectedGridType().GridType]).gridDefinition();
			gridDefinition.Columns.map(function(column)
			{
				column.TypeCode = column.type.replace(/\b(\w)|\s(\w)/g, function(m) { return m.toUpperCase() }),
					column.PersistenceName = column.FieldName;
				return column;
			});
			gridDefinition.Columns.unshift({ DisplayName: " ", PersistenceName: " " });
			this.obGridDefinition(gridDefinition || {});
		}
	};

	ModifyReminderFilterViewModel.prototype._excludePersistenceNameIsNullComputer = function()
	{
		if (this.obGridDefinition())
		{
			return this.obGridDefinition().Columns.filter(function(column) { return column.PersistenceName });
		}
		else
		{
			return [];
		}
	};

	ModifyReminderFilterViewModel.prototype.verifyClick = function(viewModel, e)
	{
		var self = this;
		self.obStatementVerify(true);
		self.verify()
			.then(function(count)
			{
				self.pageLevelViewModel.popupSuccessMessage("SQL syntax is correct. " + count + " record in total.");
			})
			.catch(function()
			{
				var validationErrors = [],
					$field = $("textarea[name='sqlStatement']");
				validationErrors.push({ name: "sqlStatement", message: "SQL syntax is incorrect", field: $field });
				self.pageLevelViewModel.obErrorMessageDivIsShow(true);
				self.pageLevelViewModel.obValidationErrorsSpecifed(validationErrors);
			})
	};

	ModifyReminderFilterViewModel.prototype.verify = function()
	{
		var skip = 0, take = 0,
			searchData = new TF.SearchParameters(skip, take, self.sorts, self.filters, this.gridFilterDataModel.whereClause(), null, null);
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(this.obSelectedGridType().GridType)),
			{
				paramData: searchData.paramData,
				data: searchData.data
			},
			{ overlay: false }
		)
			.then(function(response)
			{
				return response.FilteredRecordCount;
			})
			.catch(function()
			{
				throw false;
			})
	};

	ModifyReminderFilterViewModel.prototype.save = function()
	{
		this.obStatementVerify(false);
		return this.saveValidate()
			.then(function(result)
			{
				if (result)
				{
					return this._save();
				}
				return Promise.reject();
			}.bind(this));
	};

	ModifyReminderFilterViewModel.prototype.saveValidate = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
				{
					return false;
				}
				else
				{
					return true;
				}
			}.bind(this));
	};

	ModifyReminderFilterViewModel.prototype._save = function()
	{
		if (this.gridFilterDataModel.apiIsDirty())
		{
			var data = this.gridFilterDataModel.toData();
			return tf.promiseAjax[this.isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"), {
				data: [this.gridFilterDataModel.toData()]
			})
				.then(function(apiResponse)
				{
					this.gridFilterDataModel.update(apiResponse.Items[0]);
					return this.gridFilterDataModel;
				}.bind(this))
				.catch(function(apiResponse)
				{
					this.pageLevelViewModel.obErrorMessageDivIsShow(true);
					this.pageLevelViewModel.obValidationErrors([{ name: "error", message: data.Message }]);
					throw apiResponse;
				}.bind(this))
		}
		else
		{
			return Promise.resolve(this.gridFilterDataModel);
		}
	}

	ModifyReminderFilterViewModel.prototype.selectFieldClick = function(viewModel, e)
	{
		//if (!this.selectFieldOpen)
		//{
		//	this.selectFieldOpen = true;
		//	return;
		//}
		//this.selectFieldOpen = false;

		var columnDefinition = this.obSelectedField();
		if (!columnDefinition || !columnDefinition.FieldName)
		{
			this.obValueFieldType("Disabled");
			return;
		}
		this.insertFragmentToCurrentCursorPostion("[" + columnDefinition.PersistenceName + "]");
		this.obValueFieldType(columnDefinition.TypeCode);
	};

	ModifyReminderFilterViewModel.prototype.selectFieldBlur = function(viewModel, e)
	{
		this.selectFieldOpen = false;
	};

	ModifyReminderFilterViewModel.prototype.selectOperatorClick = function(viewModel, e)
	{
		//if (!this.selectOperatorOpen)
		//{
		//	this.selectOperatorOpen = true;
		//	return;
		//}
		//this.selectOperatorOpen = false;
		var value = this.selectedOperator();
		if (value != " ")
		{
			this.insertFragmentToCurrentCursorPostion(value);
		}
	};

	//ModifyReminderFilterViewModel.prototype.selectOperatorBlur = function(viewModel, e)
	//{
	//	this.selectOperatorOpen = false;
	//};

	ModifyReminderFilterViewModel.prototype.selectLogicalOperatorClick = function(viewModel, e)
	{
		//if (!this.selectLogicalOperatorOpen)
		//{
		//	this.selectLogicalOperatorOpen = true;
		//	return;
		//}
		//this.selectLogicalOperatorOpen = false;
		var value = this.selectedLogicalOperator();
		if (value != " ")
		{
			this.insertFragmentToCurrentCursorPostion(value);
		}
	};

	//ModifyReminderFilterViewModel.prototype.selectLogicalOperatorBlur = function(viewModel, e)
	//{
	//	this.selectLogicalOperatorOpen = false;
	//};

	ModifyReminderFilterViewModel.prototype.valueKeypress = function(viewModel, e)
	{
		if (e.keyCode == 13)
		{
			console.log("enterkeypressed");
			var baseBox = ko.dataFor(e.target);
			var value = baseBox.value();
			this.insertFragmentToCurrentCursorPostion(valueToSQL(baseBox.getType(), value));
		}
		return true;
	};

	ModifyReminderFilterViewModel.prototype.insertFragmentToCurrentCursorPostion = function(fragment)
	{
		var cursorPosition = $(this.textAreaElement()).prop("selectionStart");
		var whereClause = this.gridFilterDataModel.whereClause();
		var firstPart = whereClause.substring(0, cursorPosition);
		var secondPart = whereClause.substring(cursorPosition, whereClause.length);
		var pad = 0;
		if (firstPart[firstPart.length - 1] != " " && firstPart.length != 0)
		{
			firstPart = firstPart + " ";
			pad++;
		}
		if (secondPart[0] != " " && secondPart.length != 0)
		{
			secondPart = " " + secondPart;
			pad++;
		}
		whereClause = firstPart + fragment + secondPart;
		this.gridFilterDataModel.whereClause(whereClause);
		$(this.textAreaElement()).prop("selectionStart", cursorPosition + fragment.length + pad);
		$(this.textAreaElement()).prop("selectionEnd", cursorPosition + fragment.length + pad);
		this._$form.data('bootstrapValidator').revalidateField("sqlStatement");
	};

	ModifyReminderFilterViewModel.prototype.initialize = function(viewModel, el)
	{
		this._$form = $(el);
		setTimeout(function()
		{
			var isValidating = false, self = this,
				updateErrors = function($field, errorInfo)
				{
					var errors = [];
					$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
					{
						if ($field[0] === item.field[0])
						{
							if (item.rightMessage.indexOf(errorInfo) >= 0)
							{
								return true;
							}
						}
						errors.push(item);
					});
					self.pageLevelViewModel.obValidationErrors(errors);
				};
			this._$form
				.bootstrapValidator({
					excluded: [':hidden', ':not(:visible)'],
					live: 'enabled',
					message: 'This value is not valid',
					fields: {
						filterName: {
							trigger: "blur change",
							validators: {
								notEmpty: {
									message: "required"
								},
								callback: {
									message: "must be unique",
									callback: function(value, validator, $field)
									{
										if (!value)
										{
											updateErrors($field, "unique");
											return true;
										}
										else
										{
											updateErrors($field, "required");
										}
										var data = this.gridFilterDataModel.toData();
										return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"), {
											paramData: {
												"@filter": String.format("(eq(dbid,{0})|isnull(dbid,))&eq(datatypeId,{1})&eq(name,{2})",
													tf.datasourceManager.databaseId, tf.dataTypeHelper.getId(data.DataType), data.Name)
											},
											data: this.gridFilterDataModel.toData()
										})
											.then(function(apiResponse)
											{
												return apiResponse.Items.length === 0;
											})
									}.bind(this)
								}
							}
						},
						sqlStatement: {
							trigger: "blur change",
							validators: {
								notEmpty: {
									message: "required"
								},
								callback: {
									message: " is invalid",
									callback: function(value, validator, $field)
									{
										if (!value)
										{
											updateErrors($field, "invalid");
											return true;
										}
										else
										{
											updateErrors($field, "required");
										}
										var searchData = new TF.SearchParameters(null, 1, self.sorts, self.filters, this.gridFilterDataModel.whereClause(), null, null);
										return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(this.obSelectedGridType().GridType)),
											{
												paramData: searchData.paramData,
												data: searchData.data
											},
											{ overlay: false }
										)
											.then(function(response)
											{
												if (response.StatusCode === 400)
												{
													setTimeout(function()
													{ 	//put data source name into message.
														$('textarea[name=sqlStatement]').closest('.form-group').find(".help-block").text("Invalid Syntax");
													}.bind(this));
													return false;
												}
												return true;
											})
											.catch(function()
											{
												setTimeout(function()
												{ 	//put data source name into message.
													$('textarea[name=sqlStatement]').closest('.form-group').find(".help-block").text("Invalid Syntax");
												}.bind(this));
												return false;
											});
									}.bind(this)
								}
							}
						}
					}
				}).on('success.field.bv', function(e, data)
				{
					if (!isValidating)
					{
						isValidating = true;
						self.pageLevelViewModel.saveValidate(data.element);
						isValidating = false;
					}
				});
			this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
		}.bind(this), 0);
	};

	ModifyReminderFilterViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(gridFilterDataModel)
			{
				return {
					applyOnSave: this.obApplyOnSave(),
					savedGridFilterDataModel: gridFilterDataModel
				}
			}.bind(this));
	};

	ModifyReminderFilterViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose()
	};

	/////////////

	function valueToSQL(type, value)
	{
		switch (type)
		{
			case "Disabled":
				return "";
			case "Boolean":
				return (value == "true" ? 1 : 0).toString();
			case "String":
			case "DateTime":
			case "Date":
			case "Time":
				return "'" + value + "'";
			case "Integer":
			case "Decimal":
				return value;
		}
	}
})();

