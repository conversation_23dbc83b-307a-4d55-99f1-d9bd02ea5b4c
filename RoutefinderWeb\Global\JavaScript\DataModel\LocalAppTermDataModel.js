(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.LocalAppTermDataModel = function(LocalAppTermEntity)
	{
		namespace.BaseDataModel.call(this, LocalAppTermEntity);
	}

	namespace.LocalAppTermDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.LocalAppTermDataModel.prototype.constructor = namespace.LocalAppTermDataModel;

	namespace.LocalAppTermDataModel.prototype.mapping = [
		{ from: "LocalAppTermID", default: 0 },
		{ from: "LocalSettingID", default: 0 },
		{ from: "Term", default: "" },
		{ from: "Singular", default: "" },
		{ from: "Plural", default: "" },
    { from: "Abbreviation", default: "" }
	];

})();
