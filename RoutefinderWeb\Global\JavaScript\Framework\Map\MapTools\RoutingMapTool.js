(function()
{

	const DRAW_GEO_REGION_BOUNDARY = "Draw GeoRegion Boundary";
	const ACTIVATE_CONFIRM_SETTINGS = {
		measurementTool: {
			geoSearchTool: {
				title: 'Confirmation Message',
				message: 'Are you sure you want to exit Geo Search?',
				enable: (mapTool) => mapTool.geoSearchTool.needClearResult()
			}
		}
	};

	createNamespace("TF.Map").RoutingMapTool = RoutingMapTool;

	function RoutingMapTool(routingMapDocumentViewModel, options)
	{
		var self = this;
		self.options = $.extend({
			buildPalettes: function()
			{
				return [];
			},
			thematicLayerId: 'candidateStudentFeatureLayer',
			baseMapSaveKey: "rfweb.baseMapId",
			geoSearchAvailable: false,
			thematicAvailable: true,
			mapLayersAvailable: false,
			questionLayersAvailable: false,
			geoFinderAvailable: true,
			homeToSchoolPathAvailable: false,
			measurementAvailable: true,
			manuallyPinAvailable: false,
			drawBoundaryAvailable: false,
			baseMapAvailable: true,
			trafficMapAvailable: (tf && tf.isViewfinder) ? false : true,
			myMapAvailable: true,
			zoomAvailable: true,
			playbackAvailable: false,
			printAvailable: false,
			compareMapAvailable: false,
			isDetailView: false,
			thematicInfo: null,
			GoogleStreet: true,
			legendStatus: null,
			onThematicChanged: null,
			onLegendStatusChanged: null,
			obTrips: ko.observable([])
		}, options);
		self.options.trafficMapAvailable = self.options.trafficMapAvailable && tf.authManager.hasTraffic();
		self.routingMapDocumentViewModel = routingMapDocumentViewModel;
		self.$container = routingMapDocumentViewModel.element;
		TF.Map.BaseMapTool.call(
			self,
			routingMapDocumentViewModel.element,
			{
				isReadMode: options.isReadMode,
				isDetailView: options.isDetailView,
				isLandscape: options.isLandscape,
				mapToolOptions: options.mapToolOptions,
				top: options.top,
				right: options.right,
			}
		);
		self.onCandidatesStudentsChangeEvent = self.onCandidatesStudentsChangeEvent.bind(self);
		self.highlightChangedEvent = self.highlightChangedEvent.bind(self);
		self.geoFinderTool = TF.Map.GeoFinderTool && new TF.Map.GeoFinderTool(self);
		self.baseMapTools = null;
	}

	RoutingMapTool.prototype = Object.create(TF.Map.BaseMapTool.prototype);
	RoutingMapTool.prototype.constructor = RoutingMapTool;

	RoutingMapTool.prototype.init = function()
	{
		TF.Map.BaseMapTool.prototype.init.call(this);
		this.initThematicsTool();
	};

	RoutingMapTool.prototype.confirmBeforeActivate = function(toolName)
	{
		const confirmInfo = ACTIVATE_CONFIRM_SETTINGS[toolName] && ACTIVATE_CONFIRM_SETTINGS[toolName][this._currentToolName];
		return confirmInfo?.enable(this) ? tf.promiseBootbox.yesNo(confirmInfo.message, confirmInfo.title) : Promise.resolve(true);
	}

	RoutingMapTool.prototype.startSketch = function(toolName)
	{
		this.routingMapDocumentViewModel.sketchTool && this.routingMapDocumentViewModel.sketchTool.stop();
		this.inactiveOtherBy(toolName);
	};

	RoutingMapTool.prototype.inactiveOtherBy = function(toolName)
	{
		var previousToolName = this._currentToolName;
		this._currentToolName = toolName;

		if (toolName != "measurementTool" && previousToolName == "measurementTool")
		{
			this.measurementTool && this.measurementTool.deactivate();
		}
		if (toolName != "geoSearchTool" && previousToolName == "geoSearchTool")
		{
			this.geoSearchTool && this.geoSearchTool.forceExit();
		}
		if (toolName != "geoFinderTool" && previousToolName == "geoFinderTool")
		{
			this.geoFinderTool && this.geoFinderTool.endGeoFinder();
		}
		if (toolName != "manuallyPinTool" && previousToolName == "manuallyPinTool")
		{
			this.manuallyPinTool && this.manuallyPinTool.stopPin();
		}
		if (toolName != "drawBoundaryTool" && previousToolName == "drawBoundaryTool")
		{
			this.drawBoundaryTool && this.drawBoundaryTool.stopDraw();
		}
		if (toolName != "directionsTool" && previousToolName == "directionsTool")
		{
			this.routingMapDocumentViewModel._directionsTool && this.routingMapDocumentViewModel._directionsTool._stopDropMode();
		}
		if (toolName != "googleStreetTool" && previousToolName == "googleStreetTool")
		{
			this.googleStreetTool && this.googleStreetTool.deactivate();
		}
		if (toolName != "routingChangePath" && previousToolName == "routingChangePath")
		{
			this.routingMapDocumentViewModel.routingPaletteViewModel?.tripViewModel?.routingChangePath?.stopDrawingMultiplePoints();
		}
		if (toolName != "routingSequenceStop" && previousToolName == "routingSequenceStop")
		{
			this.routingMapDocumentViewModel.routingPaletteViewModel?.tripViewModel?.routingSequenceStop?.stop();
		}

		const hasPopups = this.routingMapDocumentViewModel.gridMapPopup || (this.routingMapDocumentViewModel.hasMapPopups && this.routingMapDocumentViewModel.hasMapPopups());
		if (hasPopups && this.routingMapDocumentViewModel.disableMouseEvent)
		{
			this.routingMapDocumentViewModel.disableMouseEvent();
		}
	};

	RoutingMapTool.prototype.stopSketch = function(toolName)
	{
		if (toolName == this._currentToolName)
		{
			const hasPopups = this.routingMapDocumentViewModel.gridMapPopup || (this.routingMapDocumentViewModel.hasMapPopups && this.routingMapDocumentViewModel.hasMapPopups());
			if (hasPopups)
			{
				this.routingMapDocumentViewModel.enableMouseEvent();
			}
		}
	};

	RoutingMapTool.prototype.GetLocationMarkerToolbarItems = function()
	{
		if (!this.options.locationMarkerList) return;
		if (this.options.locationMarkerAvailable)
		{
			let toolbarItems = [];

			if (this.options.trashAvailable)
			{
				let trashIcon = 'trash';
				if (this.options.disableTrashBtn)
				{
					trashIcon += ' disable';
				}

				toolbarItems.push(new TF.RoutingMap.MenuItem({
					header: 'Clear',
					icon: trashIcon,
					closable: true,
					click: () => this.trashClick()
				}));
			}

			let locationMarkerToolItems = this.options.locationMarkerList.map(marker => new TF.RoutingMap.MenuItem({
				toggleStatus: ko.observable(false),
				header: marker,
				closable: true,
				type: marker.toLowerCase(),
				click: () => this.locationMarkerClick(marker)
			}));

			this.toolbarItems = toolbarItems.concat(locationMarkerToolItems);
		}
	}

	RoutingMapTool.prototype.GetMenuItems = function()
	{
		var self = this;
		this.rootMenuItem = new TF.RoutingMap.MenuItem(
			{
				header: 'root',
				icon: null,
				parent: null,
				children: []
			}
		);

		if (this.options.baseMapAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Basemap',
				icon: 'basemap',
				click: self.baseMapBtnClick.bind(self)
			}));
		}

		if (this.options.questionLayersAvailable)
		{
			let children = self.options.buildQuestionLayersMenuItems();
			if (children.length > 0)
			{
				self.mapLayersMenu = new TF.RoutingMap.MenuItem({
					header: 'Map Layers',
					icon: 'questionlayers',
					click: self.questionLayersBtnClick.bind(self),
					children: children
				});
				self.rootMenuItem.addChild(self.mapLayersMenu);
			}
		}

		if (this.options.zoomAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Zoom to Layers',
				icon: 'zoom',
				closable: true,
				click: self.zoomToLayersExtent.bind(self)
			}));
		}

		var children = this.options.buildPalettes().sort(function(a, b)
		{
			if (a.header > b.header) return 1;
			else if (a.header == b.header) return 0;
			else return -1;
		});
		if (children.length > 0)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				helpKey: "palettes",
				header: 'Palettes',
				icon: 'palettes',
				click: function() { },
				children: children
			}));
		}

		if (this.options.manuallyPinAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Manually Pin',
				icon: 'manuallypin',
				closable: true,
				click: self.manuallyPinClick.bind(self)
			}));
		}

		if (this.options.homeLocationPinAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Home Location',
				icon: 'home',
				closable: true,
				click: self.homeLocationPinClick.bind(self)
			}));
		}

		if (this.options.drawBoundaryAvailable)
		{
			var drawBoundaryMenuItem = self.getDrawBoundaryMenuItem();
			this.rootMenuItem.addChild(drawBoundaryMenuItem);
		}

		if (this.options.thematicAvailable && tf.authManager.isAuthorizedFor("thematics", "read"))
		{
			if (this.addThematicsMenuItem)
			{
				this.addThematicsMenuItem();
			} else
			{
				this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
					header: 'Thematics',
					icon: 'thematics',
					click: self.thematicsToolClick.bind(self)
				}));
			}
		}

		if (this.options.compareMapAvailable)
		{
			this.compareMapCanvasTool = new TF.Map.CompareMapCanvasTool(this);
			this.compareMapCanvasTool.buildMenu();
		}

		if (this.options.mapLayersAvailable)
		{
			this.mapLayerTool = new TF.Map.LayerTool({ map: self.routingMapDocumentViewModel._map, uid: this.options.uid });
			this.rootMenuItem.addChild(this.mapLayerTool.initialize());
		}

		if (this.options.GoogleStreet)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Google Street View',
				icon: 'googlestreet',
				closable: true,
				click: self.googleStreetClick.bind(self)
			}));
		}

		if (this.options.geoFinderAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				helpKey: "geofinder",
				header: 'Geofinder',
				icon: 'geofinder',
				click: function() { },
				children: [
					new TF.RoutingMap.MenuItem({
						toggleStatus: ko.observable(false),
						header: 'Find in Polygon',
						closable: true,
						type: "polygon",
						click: self.geoFindClick
					}),
					new TF.RoutingMap.MenuItem({
						toggleStatus: ko.observable(false),
						header: 'Find in Walkout',
						closable: true,
						type: "walkout",
						click: self.geoFindClick
					}),
					new TF.RoutingMap.MenuItem({
						toggleStatus: ko.observable(false),
						header: 'Find in Drive To',
						closable: true,
						type: "driveto",
						click: self.geoFindClick
					})
				]
			}));
		}

		if (this.options.geoSearchAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Geo Search',
				icon: 'geosearch',
				closable: true,
				click: self.geoSearchToolClick.bind(self)
			}));
		}

		if (this.options.homeToSchoolPathAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Home to School Path',
				icon: 'studentWalk',
				closable: true,
				click: self.homeToSchoolPathClick.bind(self)
			}));
		}

		if (this.options.clearGeoSearchAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Clear Shapes',
				icon: 'clear-geosearch',
				closable: true,
				click: self.clearGeoSearchToolClick.bind(self)
			}));
		}

		if (this.options.measurementAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Measurement',
				icon: 'measurement',
				closable: true,
				click: self.measurementToolClick.bind(self)
			}));
		}

		if (this.options.playbackAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Playback',
				icon: 'playback',
				closable: true,
				click: self.playbackClick.bind(self)
			}));
		}

		if (this.options.printAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Print',
				icon: 'print',
				closable: true,
				click: self.showPrintTypeDialog.bind(self)
			}));
		}

		if (this.options.homeAvailable)
		{
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'Home',
				icon: 'home',
				closable: true,
				click: () => this.homeClick()
			}));
		}

		if (this.options.myLocationAvailable)
		{
			let iconClass = 'location-arrow';
			if (this.options.disableMyLocationBtn)
			{
				iconClass += " disable";
			}
			this.rootMenuItem.addChild(new TF.RoutingMap.MenuItem({
				header: 'My Location',
				icon: iconClass,
				closable: true,
				click: () => this.myLocationClick()
			}));
		}
	};

	var letterSize = { width: 8.5, height: 11 }, minMagin = 0.2;

	RoutingMapTool.prototype.showPrintTypeDialog = function()
	{
		tf.promiseBootbox.dialog(
			{
				closeButton: true,
				size: "small",
				title: "Print As",
				css: "abc",
				message: "Select the type that you would like to print." +
					"<div class='col-xs-24'>" +
					"<div class='remove-col-default-left-padding-15'>" +
					"<div class='radio'><label><input id='printAsConfirmWebPrintRadio' checked='checked' name='type' type='radio' value='web' />Send to Printer</label></div>" +
					"<div class='radio' style='margin-bottom:0'><label><input name='type' type='radio' value='server' />Layered PDF</label></div>" +
					"</div>" +
					"</div>",
				buttons:
				{
					save:
					{
						label: "OK",
						className: "btn tf-btn-black btn-sm",
						callback: () =>
						{
							if ($("#printAsConfirmWebPrintRadio").is(':checked'))
							{
								return this.printByWeb();
							}
							return this.printByServer();
						}
					},
					cancel:
					{
						label: "Cancel",
						className: "btn btn-link btn-sm"
					}
				}
			})
	}

	RoutingMapTool.prototype.printByServer = function()
	{
		const url = arcgisUrls.ServiceDirectory + "/Utilities/PrintingTools/GPServer/Export%20Web%20Map%20Task";

		const template = new tf.map.ArcGIS.PrintTemplate({
			format: "pdf",
			exportOptions: {
				width: this.routingMapDocumentViewModel._map.mapView.width,
				height: this.routingMapDocumentViewModel._map.mapView.height
			},
			layout: "map-only",
		});

		const params = new tf.map.ArcGIS.PrintParameters({
			view: this.routingMapDocumentViewModel._map.mapView,
			template: template
		});

		tf.loadingIndicator.show();
		this.routingMapDocumentViewModel._map.allLayers.forEach(layer =>
		{
			if (!layer.title)
			{
				layer.title = TF.Helper.MapHelper.getLayerTitleById(layer.id);
			}
		});
		tf.map.ArcGIS.print.execute(url, params).then(printResult).catch(printError);

		function printResult(result)
		{
			tf.loadingIndicator.tryHide();
			console.log(result.url);
			window.open(result.url);
		}

		function printError(err)
		{
			tf.loadingIndicator.tryHide();
			console.log("Something broke: ", err);
		}
	}

	RoutingMapTool.prototype.printByWeb = function(menuItem, e)
	{
		tf.loadingIndicator.show();
		var endPrint;
		this.routingMapDocumentViewModel._map.mapView.takeScreenshot({
			'format': 'png'
		}).then(screenshot =>
		{
			var src = screenshot.dataUrl,
				width = screenshot.data.width,
				height = screenshot.data.height,
				imgWidthInch = width / 96,
				imgHeightInch = height / 96,
				landscape = imgWidthInch > imgHeightInch,
				pageWidth = landscape ? letterSize.height : letterSize.width,
				pageHeight = landscape ? letterSize.width : letterSize.height,
				scale = Math.min((pageWidth - minMagin * 2) / imgWidthInch, (pageHeight - minMagin * 2) / imgHeightInch);

			imgWidthInch = imgWidthInch * scale;
			imgHeightInch = imgHeightInch * scale;
			width = Math.floor(imgWidthInch * 96);
			height = Math.floor(imgHeightInch * 96);

			var horizontalMargin = Math.max((pageWidth - imgWidthInch) / 2, minMagin),
				verticalMargin = Math.max((pageHeight - imgHeightInch) / 2, minMagin),
				img = `<img width='${width}' height='${height}' src='${src}' class='printable' />)`,
				pageCss = `@media print {@page {margin: ${verticalMargin.toFixed(2)}in ${horizontalMargin.toFixed(2)}in; size: ${pageWidth}in ${pageHeight}in;}}`,
				styleHtml = `<style>${pageCss}</style>`,
				styleEle = $(styleHtml).appendTo($(document).find("head")),
				imgElement = $(img).on('load', () =>
				{
					window.focus();
					window.print();
				});

			var measurementPanelCopy;
			endPrint = () =>
			{
				if (imgElement)
				{
					imgElement.remove();
					imgElement = null;
					styleEle.remove();
					styleEle = null;
					if (measurementPanelCopy)
					{
						measurementPanelCopy.remove();
						measurementPanelCopy = null;
					}
					tf.loadingIndicator.tryHide();
				}
			};

			window.onafterprint = endPrint;

			$(document.body).append(imgElement);
			if (this.measurementTool && this.measurementTool.isActive)
			{
				var measurementPanel = this.measurementTool.$infoPanel.find(".active.measurement-panel"),
					measurementPanelMargin = 5,
					measurementPanelWidth = measurementPanel.width(),
					measurementPanelHeight = measurementPanel.height();
				measurementPanelCopy = $(`<div id='measurementInfoPanel' class='measurement-info-panel active'><div class='measurement-panel-container'>${measurementPanel[0].outerHTML}</div></div>`)
					.addClass("printable")
					.width(measurementPanelWidth)
					.height(measurementPanelHeight)
					.css({
						position: "absolute",
						left: (width - measurementPanelWidth - measurementPanelMargin) + "px",
						top: (height - measurementPanelHeight - measurementPanelMargin) + "px"
					});
				$(document.body).append(measurementPanelCopy);
				measurementPanelCopy.find(".location-track").remove();
			}
		}).catch(() =>
		{
			if (endPrint)
			{
				endPrint();
			}
		});
	};

	RoutingMapTool.buildMenuItem = function(header, icon, viewModel, click)
	{
		return new TF.RoutingMap.MenuItem({
			helpKey: viewModel.helpKey,
			header: header,
			icon: icon,
			children: [],
			isToggled: true,
			disable: click ? false : true,
			toggleStatus: viewModel.obShow,
			click: function()
			{
				click && click(viewModel);
			}
		});
	};

	RoutingMapTool.prototype.onCandidatesStudentsChangeEvent = function(allStudents, highlightedStudents, trips)
	{
		let thematicTool = this.thematicTools?.student || this.thematicTool;
		if (thematicTool)
		{
			thematicTool.trips = trips;
		}
		if (thematicTool && thematicTool.thematicMenu.obSelectThematicId() > 0)
		{
			this.reloadThematicsTool(allStudents, highlightedStudents);
		}
	};

	RoutingMapTool.prototype.highlightChangedEvent = function(highlightedStudents)
	{
		var self = this;

		if (self.thematicTool && self.thematicTool.grid)
		{
			self.thematicTool.grid.highLightedData = highlightedStudents;
			if (self.thematicTool.thematicMenu.obSelectThematicId() != null && highlightedStudents.length > 0)
			{
				self.thematicTool.setHighLightedDataSymbol();
			}
		}
	};

	RoutingMapTool.prototype.hasApplyThematic = function()
	{
		return (this.thematicTool.thematicMenu && this.thematicTool.thematicMenu.obAppliedThematic()) != null;
	};

	/**
	* Initialize the thematics tool
	* @return {void}
	*/
	RoutingMapTool.prototype.reloadThematicsTool = function(allStudents, highlightedStudents)
	{
		let self = this;
		allStudents = allStudents ? allStudents : [];
		highlightedStudents = highlightedStudents ? highlightedStudents : [];
		let thematicTool = self.thematicTool || self.thematicTools?.student;
		if (thematicTool)
		{
			allStudents = allStudents.length > 0 ? allStudents.filter(function(item)
			{
				if (item.geometry) return item.geometry;
				if (item.XCoord) return item.XCoord;
				if (item.Xcoord) return item.Xcoord;
			}) : allStudents;
			thematicTool.grid.result = { TotalRecordCount: thematicTool.grid._gridType == "gpsevent" ? (thematicTool.grid.obTotalCount ? thematicTool.grid.obTotalCount() : 0) : allStudents.length };
			thematicTool.grid.allIds = allStudents.map(function(s)
			{
				if (s.entityId && (thematicTool.grid._gridType == "district" || thematicTool.grid._gridType == "trip" || thematicTool.grid._gridType == "route"))
				{
					return s.entityId;
				}
				else if (s.id)
				{
					return s.id;
				}
				else if (s.Id)
				{
					return s.Id;
				}

			});
			thematicTool.grid.allData = allStudents;
			thematicTool.grid.highLightedData = highlightedStudents.length > 0 ? highlightedStudents.filter(function(item)
			{
				if (item.geometry) return item.geometry;
				if (item.XCoord) return item.XCoord;
				if (item.Xcoord) return item.Xcoord;
			}) : highlightedStudents;
			if (thematicTool.thematicInfo != null)
			{
				thematicTool.thematicMenu.refreshThematic();
			}
		}
	};

	/**
	* Initialize the thematics tool
	* @return {void}
	*/
	RoutingMapTool.prototype.initThematicsTool = function()
	{
		var self = this;
		self.thematicsToolTimer = setTimeout(function()
		{
			var grid = {
				_gridType: self.options.mapToolOptions != undefined ? self.options.mapToolOptions.gridType : 'student',
				result: { TotalRecordCount: 0 },
				allIds: [],
				allData: [],
				highLightedData: [],
				dataType: 'unassignedStudents',
				isMapCanvas: self.options.mapToolOptions == undefined
			};
			var document;
			if (self.routingMapDocumentViewModel.parentDocument)
			{
				document = self.routingMapDocumentViewModel.parentDocument;
			}
			else if (self.routingMapDocumentViewModel.routeState && !(tf && tf.isViewfinder))
			{
				document = tf.documentManagerViewModel._findDocument(self.routingMapDocumentViewModel.routeState);
			}
			if (document && document.gridViewModel)
			{
				grid = document.gridViewModel.searchGrid;
			}
			else if (self.routingMapDocumentViewModel && self.routingMapDocumentViewModel._grid)
			{
				grid = self.routingMapDocumentViewModel._grid;
			}

			grid.requestOptions = document && document.gridViewModel && document.gridViewModel.obFilterOptions;
			grid.obTotalCount = document && document.gridViewModel && document.gridViewModel.obTotalCount;
			self.thematicTool = new TF.Map.RoutingThematicTool(
				grid,
				self.routingMapDocumentViewModel,
				self.$mapTool,
				self.$offMapTool,
				self.options.isDetailView,
				self.studentIds,
				self.options.isReadMode,
				self.options.thematicInfo,
				self.options.legendStatus,
				self.options.thematicLayerId,
				self.options.mapToolOptions);

			self.$thematicTool = self.$offMapTool.find('.thematic-menu');
			self.thematicTool.thematicMenu.onMenuOptionClick.subscribe(self.onThematicMenuOptionClick.bind(self));

			if (self.options.onThematicChanged)
			{
				self.thematicTool.thematicMenu.onThematicChanged.subscribe(self.options.onThematicChanged);
			}

			if (self.options.onLegendStatusChanged)
			{
				self.thematicTool.thematicMenu.onLegendStatusChanged.subscribe(self.options.onLegendStatusChanged);
			}
		});
	};

	RoutingMapTool.prototype.onThematicMenuOptionClick = function()
	{
		this.toolkitBtnClick();
		this.hideSubMenu();
	};

	RoutingMapTool.prototype.thematicsToolClick = function(menuItem)
	{
		var self = this;
		if (self.thematicTool && menuItem.isActive)
		{
			self.thematicTool.thematicMenu.activate().then(function()
			{
				var $caret = $('<div class="caret"></div>');
				self.setMenuPosition(menuItem, self.$thematicTool, $caret);
			});
		}
	};

	RoutingMapTool.prototype.getDrawBoundaryMenuItem = function()
	{
		const self = this;
		let header = "Draw Boundary";
		if (self.routingMapDocumentViewModel.type === "georegion")
		{
			header = DRAW_GEO_REGION_BOUNDARY;
		}
		else if (self.routingMapDocumentViewModel.type === "parceladdresspoint")
		{
			header = 'Draw Parcel Boundary';
		}


		return new TF.RoutingMap.MenuItem({
			header,
			icon: 'geosearch',
			click: function() { },
			children: [
				new TF.RoutingMap.MenuItem({
					toggleStatus: ko.observable(false),
					header: 'Polygon',
					closable: true,
					type: "polygon",
					click: self.drawBoundaryClick
				}),
				new TF.RoutingMap.MenuItem({
					toggleStatus: ko.observable(false),
					header: 'Rectangle',
					closable: true,
					type: "rectangle",
					click: self.drawBoundaryClick
				}),
				new TF.RoutingMap.MenuItem({
					toggleStatus: ko.observable(false),
					header: 'Draw',
					closable: true,
					type: "draw",
					click: self.drawBoundaryClick
				}),
				new TF.RoutingMap.MenuItem({
					toggleStatus: ko.observable(false),
					header: 'Circle',
					closable: true,
					type: "circle",
					click: self.drawBoundaryClick
				})
			]
		})
	};

	RoutingMapTool.prototype.addDrawBoundaryTool = function()
	{
		var self = this;
		var menuItem = self.getDrawBoundaryMenuItem();
		const drawGeoregionTool = Enumerable.From(this.rootMenuItem.children).FirstOrDefault(null, function(x)
		{
			return x.header === DRAW_GEO_REGION_BOUNDARY;
		});
		if (!drawGeoregionTool)
		{
			self.rootMenuItem.addChild(menuItem);
		}
		menuItem.onclick = menuItem.onclick.createInterceptor(self.openSubMenu.bind(self)).bind(this, menuItem);
		self.insertTool(menuItem.icon, menuItem.header, menuItem.onclick, menuItem, this.getDrawBoundaryMenuSequence());
	};

	RoutingMapTool.prototype.removeDrawBoundaryTool = function()
	{
		this.removeTool(this.getDrawBoundaryMenuSequence());
	};

	RoutingMapTool.prototype.getDrawBoundaryMenuSequence = function()
	{
		const defaultValue = 5;
		const drawGeoregionTool = Enumerable.From(this.rootMenuItem.children).FirstOrDefault(null, function(x)
		{
			return x.header === DRAW_GEO_REGION_BOUNDARY;
		});
		if (!drawGeoregionTool)
		{
			return defaultValue;
		}

		const $e = $(drawGeoregionTool.target).attr('class');
		if (!$e)
		{
			return defaultValue;
		}
		const classNames = $e.split(' ');
		const sequenceClass = classNames && Enumerable.From(classNames).FirstOrDefault(null, function(x)
		{
			return x.indexOf("sequence-") > -1;
		})
		if (!sequenceClass)
		{
			return defaultValue;
		}

		const items = sequenceClass.split("-");
		const sequence = items && items[1];
		if (!sequence)
		{
			return defaultValue;
		}

		return sequence * 1;
	}

	RoutingMapTool.prototype.drawBoundaryClick = function(e, data)
	{
		var self = this;
		if (!self.drawBoundaryTool)
		{
			self.drawBoundaryTool = new TF.Map.DrawBoundaryTool(self);
		}

		if (self.routingMapDocumentViewModel.type !== "parceladdresspoint"
			&& self.routingMapDocumentViewModel._map.findLayerById(`${self.routingMapDocumentViewModel.type}PointLayer`).graphics.items.length === 0)
		{
			tf.promiseBootbox.alert(`Please manually pin a Geo Location.`);
			return;
		}

		self.drawBoundaryTool.startDraw(e.config.type);
	};

	RoutingMapTool.prototype.geoFindClick = function(e, data)
	{
		var self = this;
		this.routingMapDocumentViewModel.gridMapPopup && this.routingMapDocumentViewModel.gridMapPopup.close();
		self.geoFinderTool.startGeoFinder(e.config.type);
	};

	RoutingMapTool.prototype.googleStreetClick = function(e, data)
	{
		var self = this;
		if (!self.googleStreetTool)
		{
			self.googleStreetTool = new TF.Map.GoogleStreetTool(self.routingMapDocumentViewModel._map, tf.map.ArcGIS, self.getRouteState(), this);
		}

		var isActive = self.googleStreetTool.isMeasurementActive();
		if (isActive)
		{
			self.googleStreetTool.deactivate();
		}
		else
		{
			self.googleStreetTool.activate();
		}
	};

	RoutingMapTool.prototype.questionLayersBtnClick = function(menuItem)
	{
		var self = this;
		if (self.questionLayerTool && menuItem.isActive)
		{
			self.questionLayerTool.show();
			var $caret = $('<div class="caret"></div>');
			self.setMenuPosition(menuItem, self.$thematicTool, $caret);
		}
	};

	RoutingMapTool.prototype.baseMapBtnClick = function(menuItem)
	{
		this.toggleBaseMapGalleryDisplayStatus(menuItem);
	};

	RoutingMapTool.prototype.toggleBaseMapGalleryDisplayStatus = function(menuItem, status)
	{
		var self = this;
		var $basemapGallery = self.initBaseMapTool();
		var $menu = $basemapGallery,
			$icon = self.$mapToolContainer.find(".tool-icon.basemap"),
			currentStatus = (status !== undefined) ? status : !$menu.hasClass("active");

		if (currentStatus)
		{
			if (TF.isPhoneDevice)
			{
				self.$offMapTool.css({
					'display': 'none'
				});

				self.$mapToolContainer.find(".tool-icon").addClass("active");
				self.$toolkitButton.addClass("active");
				TF.Map.ExpandMapTool.moveMobileFullScreenBaseMapBehind(self.$offMapTool);

				if (_.isEmpty(self.baseMapModel)) self.baseMapModel = new TF.Modal.SelectMapModalViewModel(self);

				tf.modalManager.showModal(self.baseMapModel);
			}
			else
			{
				var $caret = $('<div class="caret"></div>');
				self.setMenuPosition(menuItem, $basemapGallery, $caret);
			}
		}
		else
		{
			if (TF.isPhoneDevice)
			{
				self.baseMapModel && self.baseMapModel.positiveClick();
				self.$offMapTool.css({
					'display': 'block'
				});
			}
			else
			{
				$icon.removeClass("active");
				$menu.removeClass("active");
				self.$offMapTool.removeClass("basemap");
			}
		}
	};

	RoutingMapTool.prototype.manuallyPinClick = function()
	{
		var self = this;
		if (!self.manuallyPinTool)
		{
			self.manuallyPinTool = new TF.Map.ManuallyPinTool(self);
		}
		if (!self.manuallyPinTool._manuallyPinActive)
		{
			self.manuallyPinTool.startPin();
		}
		else
		{
			self.manuallyPinTool.stopPin();
		}

	};

	RoutingMapTool.prototype.homeLocationPinClick = function()
	{
		var self = this;
		if (!self.homeLocationPinTool)
		{
			self.homeLocationPinTool = new TF.Map.HomeLocationPinTool(self);
		}
		if (!self._homeLocationPinActive)
		{
			self.homeLocationPinTool.startPin();
			self._homeLocationPinActive = true;
		}
		else
		{
			self.homeLocationPinTool.stopPin();
			self._homeLocationPinActive = false;
		}

	};

	/**
	* Initialize the arcgis basemap gallery.
	* @return {void}
	*/
	RoutingMapTool.prototype.initBaseMapTool = function(externalId)
	{
		//this.options.mapToolOptions.urlWithoutPrefix = true when viewfinder
		let imageJsonUrlPrefix;
		if (TF.URLHelper.isDashboard())
		{
			imageJsonUrlPrefix = "./"
		}
		else
		{
			imageJsonUrlPrefix = this.options && this.options.mapToolOptions && this.options.mapToolOptions.urlWithoutPrefix ? "./" : "../../";
		}

		var self = this;
		var basemapSelectedClass = "esri-basemap-gallery__item--selected";
		var id = externalId || "basemap-menu-" + self.getRouteState();
		var $basemapGallery = $("<div></div>", { id: id });
		$basemapGallery.addClass("esri-basemap-gallery esri-widget esri-widget--panel-height-only tool-menu routing-sub-item");

		var $ul = $('<ul class="esri-basemap-gallery__item-container" role="menu"></ul>');
		let baseMapOption = {
			myMapAvailable: self.options.myMapAvailable
		};
		TF.Helper.MapHelper.baseMaps(imageJsonUrlPrefix, baseMapOption).forEach(function(baseMap)
		{
			var $li = $('<li class="esri-basemap-gallery__item" role="menuitem" tabindex="0" ><img  alt = "" class= "esri-basemap-gallery__item-thumbnail" src="' + baseMap.thumbnail + '" ><div class="esri-basemap-gallery__item-title">' + baseMap.title + '</div></li>');
			if (self.routingMapDocumentViewModel._map.basemap.id == baseMap.id)
			{
				$li.addClass(basemapSelectedClass);
			}
			$ul.append($li);
			$li.on("click", function()
			{
				if (!self.options.notStickyBaseMap)
				{
					tf.userPreferenceManager.save(self.options.baseMapSaveKey, baseMap.id);
				}
				$(self.routingMapDocumentViewModel._map.mapView.container).css("background-color", "white");
				if (baseMap.id == "my-maps")
				{
					if (self.routingMapDocumentViewModel.mapLayersPaletteViewModel)
					{
						self.routingMapDocumentViewModel.mapLayersPaletteViewModel.show();
					}
					else
					{
						self.layersViewModel = self.layersViewModel || new TF.RoutingMap.MapLayersPaletteViewModel(self.routingMapDocumentViewModel, false, self.getRouteState());
						self.routingMapDocumentViewModel.onMapLoad.notify();
						setTimeout(() =>
						{
							self.layersViewModel.show();
						}, 20);
					}
				}
				else
				{
					let parcelPaletteViewModel = self.routingMapDocumentViewModel.parcelPaletteViewModel || self.layersViewModel?.parcelDisplaySetting?.relateViewModel;
					if (self.routingMapDocumentViewModel._map.basemap.id == "my-maps" && parcelPaletteViewModel)
					{
						//hide parcel point layer if change basemap.
						parcelPaletteViewModel.minusShowCount();
						if (parcelPaletteViewModel.showCount == 0)
						{
							parcelPaletteViewModel.close();
						}
					}

					if (baseMap.basemap)
					{
						self.routingMapDocumentViewModel._map.basemap = baseMap.basemap;
					}
					else
					{
						self.routingMapDocumentViewModel._map.basemap = baseMap.id;
					}
				}

				self.toolkitBtnClick();
				self.hideSubMenu();
				if (TF.isPhoneDevice)
				{
					self.baseMapModel && self.baseMapModel.positiveClick();
					self.$offMapTool.css({
						'display': 'block'
					});
				}
			});
		});
		this.initTrafficMap($ul);
		$basemapGallery.append($ul);
		self.$basemapGallery = $basemapGallery;
		if (!TF.isPhoneDevice)
		{
			self.$offMapTool.append($basemapGallery);
			$basemapGallery.show();
		}
		else
		{
			self.baseMapTools = self.$basemapGallery.detach();
		}
		return $basemapGallery;
	};

	RoutingMapTool.prototype.initTrafficMap = function($ul)
	{
		if (this.options.trafficMapAvailable)
		{
			if (!this.trafficMapCheckbox)
			{
				this.trafficMapCheckbox = $(`<li>
			<div class="spinner-circle-wrapper" style='margin-top:20px;margin-bottom:10px;'>
				<div class="border"><span class='time'>300</span></div>
				<div class="wrapper">
					<div class="spinner pie"></div>
					<div class="filler pie"></div>
					<div class="mask"></div>
				</div>
			</div>
			<div class="checkbox" >
				<label>
					<input type="checkbox" style="margin-top:2px;">
					<span>Traffic Data</span>
				</label>
			</div></li>`);
			}

			this.trafficMapCheckbox.find("input")
				.off("change").on("change", () =>
				{
					this.trafficMapOn = this.trafficMapCheckbox.find("input").prop("checked");
					if (!this.trafficMap)
					{
						this.trafficMap = new TF.Map.TrafficMap(this.routingMapDocumentViewModel._map, this);
					}
					this.trafficMap.toggleTrafficMap(() =>
					{
						// count down
						if (this.trafficMapOn)
						{
							var trafficMapCheckbox = this.trafficMapCheckbox;
							trafficMapCheckbox.find(".spinner-circle-wrapper").css("visibility", "visible");
							var totalSecond = TF.Map.TrafficMap.RefreshInterval * 60;
							var currentSecond = totalSecond;
							drawCurrentStatus();
							this.trafficTimer = setInterval(() =>
							{
								currentSecond = currentSecond - 1;
								if (currentSecond < 0)
								{
									currentSecond = totalSecond;
								}
								drawCurrentStatus();
							}, 1000);

							function drawCurrentStatus()
							{
								trafficMapCheckbox.find(".time").text(currentSecond);
								var rotate = -360 / totalSecond * currentSecond + 360;
								trafficMapCheckbox.find(".spinner").css("transform", "rotate(" + rotate + "deg)");
								if (currentSecond < totalSecond / 2)
								{
									trafficMapCheckbox.find(".mask").hide();
									trafficMapCheckbox.find(".filler").show();
								}
								else
								{
									trafficMapCheckbox.find(".mask").show();
									trafficMapCheckbox.find(".filler").hide();
								}
							}
						}
						else
						{
							this.trafficMapCheckbox.find(".spinner-circle-wrapper").css("visibility", "hidden");
							clearInterval(this.trafficTimer);
						}
					});
				});
			this.trafficMapCheckbox.off("click.stopPro").on("click.stopPro", (e) =>
			{
				e.stopPropagation();
			});
			$ul.append(this.trafficMapCheckbox);
		}
	};

	RoutingMapTool.prototype.getRouteState = function()
	{
		return this.routingMapDocumentViewModel.routeState || (tf.documentManagerViewModel && tf.documentManagerViewModel.obCurrentDocument && tf.documentManagerViewModel.obCurrentDocument().routeState) || "";
	};

	RoutingMapTool.prototype.zoomToLayersExtent = function()
	{
		var map = this.routingMapDocumentViewModel._map;
		let centerLayers = () =>
		{
			var promises = [];
			var query = new tf.map.ArcGIS.Query();
			query.where = "1=1";
			query.returnGeometry = true;
			query.outSpatialReference = map.mapView.spatialReference;
			const layerIdsToIgnore = this?.routingMapDocumentViewModel?.getLayersToIgnoreZoom?.()?.map(l => l.id) || []
			map.allLayers.forEach(function(layer)
			{
				if (layerIdsToIgnore.includes(layer.id))
				{
					return;
				}

				if (layer.graphics && layer.id != "tripArrowLayer")
				{
					promises.push(Promise.resolve(layer.graphics.items));
				}
				else if (layer.type == "feature")
				{
					var layerView = map.mapView.allLayerViews.items.filter(item => item.layer.id === layer.id)[0];
					if (layerView && layerView.filter && layerView.filter.where != null)
					{
						// Considering FeatureFilter
						query.where = layerView.filter.where;
					}
					promises.push(layer.queryFeatures(query).then(function(featureSet)
					{
						return featureSet.features;
					}));
				}
			});

			Promise.all(promises).then(function(data)
			{
				var allGraphics = _.flatten(data);
				if (allGraphics.length > 0)
				{
					var finalGraphics = (tf && tf.isViewfinder) ? [] : allGraphics;
					if (tf && tf.isViewfinder)
					{
						allGraphics.forEach(function(item)
						{
							if (item.geometry && item.geometry.longitude && item.geometry.latitude)
							{
								finalGraphics.push({
									geometry: TF.xyToGeometry(item.geometry.longitude, item.geometry.latitude),
									id: item.Id
								});
							}
							else
							{
								finalGraphics.push(item);
							}
						});
					}
					TF.RoutingMap.EsriTool.centerMultipleItem(map, finalGraphics);
				}
			});
		}

		if (tf.isViewfinder)
		{
			centerLayers();
			return;
		}

		// if does not have too much layer, zoom to all graphics
		if (map.allLayers.length < 18)
		{
			centerLayers();
		}
		else
		{
			map.mapView.extent = TF.createDefaultMapExtent();
		}
	};

	RoutingMapTool.prototype.measurementToolClick = function()
	{
		if (!this.measurementTool)
		{
			var routeState = this.getRouteState();
			this.measurementTool = new TF.Map.RoutingMapMeasureTool(this.routingMapDocumentViewModel._map, tf.map.ArcGIS, this.routingMapDocumentViewModel.element, routeState, this);
			this.measurementTool.onMeasureEnd.subscribe(e =>
			{
				let tool = this.routingMapDocumentViewModel._directionsTool;
				if (tool && tool._stopLayer)
				{
					tool.startDraggingMode(tool._routeGeometry);
				}
			});
			this.measurementTool.onMeasureStart.subscribe(e =>
			{
				let tool = this.routingMapDocumentViewModel._directionsTool;
				if (tool && tool._stopLayer)
				{
					tool.stopDraggingMode();
				}
			});
		}

		var isActive = this.measurementTool.isMeasurementActive();
		if (isActive)
		{
			this.measurementTool.deactivate();
		} else
		{
			this.confirmBeforeActivate('measurementTool').then(res =>
			{
				if (res)
				{
					this.routingMapDocumentViewModel.gridMapPopup && this.routingMapDocumentViewModel.gridMapPopup.close();
					this.measurementTool.activate();
				}
			});
		}
	};

	// click on geo search
	RoutingMapTool.prototype.geoSearchToolClick = function()
	{
		var self = this;
		if (!self.geoSearchTool)
		{
			self.initGeoSearchTool();
		}

		var isActive = self.geoSearchTool.isGeoSearching();

		let mapToolButton = self.$appliedGeoSearchIcon.parent().find(".map-tool-btn");
		if (isActive)
		{
			if (isMobileDevice())
			{
				self.$appliedGeoSearchIcon.show();
				self.$appliedGeoSearchIcon.css("top", mapToolButton.css("top"));
				self.geoSearchTool.activateDrawTool();
			}
			else
			{
				self.geoSearchTool._exitBtnClick();
			}
		}
		else
		{
			self.routingMapDocumentViewModel.gridMapPopup && self.routingMapDocumentViewModel.gridMapPopup.close();
			self.routingMapDocumentViewModel.revertGeoSearch();
			self.geoSearchTool.startGeoSearch();
			if (isMobileDevice())
			{
				self.$appliedGeoSearchIcon.show();
				self.$appliedGeoSearchIcon.css("top", mapToolButton.css("top"));
			}
		}
	};

	RoutingMapTool.prototype.initGeoSearchTool = function()
	{
		const self = this;
		self.geoSearchTool = new TF.Map.GeoSearchTool(self.routingMapDocumentViewModel, self, self.options.searchGrid);
		self.geoSearchTool.drawCompleted.subscribe(function()
		{
			if (isMobileDevice())
			{
				self.$appliedGeoSearchIcon.hide();
			}
		});
		self.geoSearchTool.clearGeoSearch.subscribe(function()
		{
			self.routingMapDocumentViewModel.revertGeoSearch();
		});
	}

	RoutingMapTool.prototype.clearGeoSearchToolClick = function()
	{
		var self = this;
		if (self.geoSearchTool)
		{
			self.geoSearchTool.clearAllBtnClick();
		}
	};

	RoutingMapTool.prototype.playbackClick = function()
	{
		if (!this.playbackTool)
		{
			this.playbackTool = new TF.Map.PlaybackTool(this);
		}
		this.playbackTool.toggleDisplay();
	};

	RoutingMapTool.prototype.homeToSchoolPathClick = function()
	{
		if (!this.homeToSchoolPathTool)
		{
			this.homeToSchoolPathTool = new TF.Map.HomeToSchoolPathTool(this);
		}
		this.homeToSchoolPathTool.toggleDisplay();
	};

	// only for form, move to sperate file in free time
	RoutingMapTool.prototype.homeClick = function()
	{
		if (!this.homeTool)
		{
			this.homeTool = new TF.Form.Map.HomeTool(this);
		}
		this.homeTool.jumpToHome();
	};

	// only for form, click to show current location
	RoutingMapTool.prototype.myLocationClick = function()
	{
		if (!this.myLocationTool)
		{
			this.myLocationTool = new TF.Form.Map.MyLocationTool(this);
		}
		this.myLocationTool.drawMyLocation();
	}

	// only for form, move to sperate file in free time
	RoutingMapTool.prototype.locationMarkerClick = function(locatonMarker)
	{
		if (!this.locationMarkerTool)
		{
			this.locationMarkerTool = new TF.Form.Map.LocationMarkerTool(this);
		}
		this.locationMarkerTool.drawMarker(locatonMarker);
	}

	// only for form, move to sperate file in free time
	RoutingMapTool.prototype.trashClick = function(isSilent)
	{
		let $trashButton = this.$mapToolBar.find("li.trash");
		if (($trashButton.length > 0) && $trashButton.hasClass("disable"))
		{
			return;
		}
		if (!this.deleteShapeTool)
		{
			this.deleteShapeTool = new TF.Form.Map.DeleteShapeTool(this);
		}
		this.deleteShapeTool.removeShape(isSilent);
	}

	RoutingMapTool.prototype.dispose = function()
	{
		clearTimeout(this.thematicsToolTimer);
		this.layersViewModel && this.layersViewModel.dispose();
		this.thematicTool && this.thematicTool.dispose();
		this.measurementTool && this.measurementTool.dispose();
		this.geoSearchTool && this.geoSearchTool.dispose();
		TF.Map.BaseMapTool.prototype.dispose.call(this);
		this.manuallyPinTool && this.manuallyPinTool.dispose();
		this.drawBoundaryTool && this.drawBoundaryTool.dispose();
		this.geoFinderTool && this.geoFinderTool.dispose();
		this.playbackTool && this.playbackTool.dispose();
		this.googleStreetTool && this.googleStreetTool.dispose();
		this.locationMarkerTool && this.locationMarkerTool.dispose();
		this.mapLayerTool && this.mapLayerTool.dispose();
		this.compareMapCanvasTool && this.compareMapCanvasTool.dispose();
	};
})();