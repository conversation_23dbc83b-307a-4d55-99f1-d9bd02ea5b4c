﻿(function()
{
	createNamespace("TF.Grid").MergeTemplateMenuViewModel = MergeTemplateMenuViewModel;

	function MergeTemplateMenuViewModel()
	{

	}

	MergeTemplateMenuViewModel.prototype = Object.create(TF.ContextMenu.BaseGeneralMenuViewModel.prototype);

	MergeTemplateMenuViewModel.prototype.constructor = MergeTemplateMenuViewModel;

	MergeTemplateMenuViewModel.prototype.openCreateNewTemplateModalClick = function()
	{
		tf.modalManager.showModal(
			new TF.Modal.EditMergeTemplateTypeModalViewModel(
				{
					hideRolesAccess: true,
					...TF.MergeTemplateTypeModalHelper.NewTemplateModalOptions()
				},
				new TF.DataModel.MergeTemplateTypeModel()
			))
			.then(TF.MergeTemplateTypeModalHelper.NewTemplateModalCallback);
	}

	MergeTemplateMenuViewModel.prototype.openManageTemplatesModalClick = function()
	{
		if (!tf.authManager.authorizationInfo.isAdmin)
			return;

		tf.modalManager.showModal(new TF.Modal.ManageMergeTemplateTypeModalViewModel())
	}

})();