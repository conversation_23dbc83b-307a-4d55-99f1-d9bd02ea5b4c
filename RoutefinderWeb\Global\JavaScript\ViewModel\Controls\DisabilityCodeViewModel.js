﻿(function()
{
	createNamespace('TF.Control').DisabilityCodeViewModel = DisabilityCodeViewModel;

	function DisabilityCodeViewModel(fieldName, id)
	{
		this.fieldName = fieldName;
		this.endpoint = tf.dataTypeHelper.getEndpoint(fieldName);
		this.obEntityDataModel = ko.observable(new TF.DataModel.DisabilityCodeDataModel());
		this.obEntityDataModel().id(id);

		this.pageLevelViewModel = new TF.PageLevel.DisabilityCodePageLevelViewModel();

		//this._studentsCount = 0;
		//this._studentsManuallyAdjustedCount = 0;
		//this._tripStopsCount = 0;
		//this._tripStopsManuallyAdjustedCount = 0;
	}

	DisabilityCodeViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(result)
			{
				if (result)
				{
					return this.checkAffectedStudents()
						.then(function(affected)
						{
							if (affected && affected[0] && affected[0].TotalStudentCount)
							{// if affected count >0, need check if there is any reset functions
								return tf.modalManager.showModal(new TF.Modal.ResetManuallyAdjustedLoadTimesModalViewModel(affected[0].TotalStudentCount, affected[0].ManualStudentCount, affected[0].TotalStopsCount, affected[0].ManualStopsCount))
									.then(function(result)
									{
										if (result)
										{
											return tf.promiseBootbox.confirm({ message: this.getConfirmMessage(result, affected), title: "Confirmation Message" })
												.then(function(confirmResult)
												{
													if (confirmResult)
													{
														//if click OK, then implemented save function, or ignore save.
														return this.basicSave({
															LoadTimes: result.checkStudentLoadTimes,
															StopTimes: result.checkTotalStopTimes
														});
													}
													return false;
												}.bind(this));
										}
										else
										{
											return false;
										}
									}.bind(this))
							}
							else
							{
								return this.basicSave();
							}
						}.bind(this));
				}
				else
				{
					return false;
				}
			}.bind(this));
	}

	DisabilityCodeViewModel.prototype.getConfirmMessage = function(result, affected)
	{
		var message = "";
		if (result.checkStudentLoadTimes && result.checkTotalStopTimes)
		{
			message = "Resetting the manually adjusted " + tf.applicationTerm.getApplicationTermSingularByName("Load Time") +
				" for a student and the Total " + tf.applicationTerm.getApplicationTermSingularByName("Stop Time") +
				" for a trip stop will reset the automatic calculation of these values.  Are you sure you want to reset the "
				+ tf.applicationTerm.getApplicationTermSingularByName("Load Time") +
				" for " + (affected[0].ManualStudentCount > 1 ? "these students" : "this student") + " and the associated trip stop Total " +
				tf.applicationTerm.getApplicationTermPluralByName("Stop Time") + "?";
		}
		else if (result.checkStudentLoadTimes)
		{
			message = "Resetting the manually adjusted " + tf.applicationTerm.getApplicationTermSingularByName("Load Time") +
				" for a student will reset the automatic calculation of the " + tf.applicationTerm.getApplicationTermSingularByName("Load Time") +
				" for that student.  Are you sure you want to reset the " + tf.applicationTerm.getApplicationTermPluralByName("Load Time") +
				" for " + (affected[0].ManualStudentCount > 1 ? "these students" : "this student");
		}
		else if (result.checkTotalStopTimes)
		{
			message = "Resetting the manually adjusted Total " + tf.applicationTerm.getApplicationTermSingularByName("Stop Time") +
				" for a trip stop will reset the automatic calculation of this value.  Are you sure you want to reset these Total "
				+ tf.applicationTerm.getApplicationTermPluralByName("Stop Time") + "?";
		}
		else
		{
			message = tf.applicationTerm.getApplicationTermSingularByName("Student") + " " +
				tf.applicationTerm.getApplicationTermPluralByName("Load Time") + " and the associated trip stop Total "
				+ tf.applicationTerm.getApplicationTermPluralByName("Stop Time") +
				" that have not been manually adjusted will be recalculated.  Are you sure you do not want to reset manually adjusted values?";
		}
		return message
	}

	DisabilityCodeViewModel.prototype.basicSave = function()
	{
		//save district policy
		var entity = this.obEntityDataModel().toData();

		//if no related students and stops, no need to reset.
		var isNew = this.obEntityDataModel().id() ? false : true;

		delete entity["APIIsDirty"];

		return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefix(), this.endpoint), { data: [entity] })
			.then(function()
			{
				PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
				return entity;
			}.bind(this))
	}

	DisabilityCodeViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.$form = $(el);

		var validatorFields = {}, isValidating = false,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.code = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Code is required"
				},
				callback: {
					message: "Code must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), self.endpoint), {
							paramData: {
								"@filter": "eq(Code," + value + ")"
							}
						}, { overlay: false })
							.then(function(apiResponse)
							{
								return !apiResponse.Items.some(function(item)
								{
									return item.Code.toLowerCase() == value.toLowerCase() && item.Id != self.obEntityDataModel().id();
								}.bind(this));
							}.bind(this));
					}
				}
			}
		};

		validatorFields.loadTime = {
			trigger: "blur change",
			validators: {
				callback: {
					message: "must be >= 0",
					callback: function(value, validator)
					{
						return !value || /^\d+$/.test(value);// Expect empty string or non-negative integer value
					}.bind(this)
				}
			}
		}


		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.$form.find("input[name=name]").focus();
		this.load();
	};

	DisabilityCodeViewModel.prototype.load = function()
	{
		var self = this,
			disabilityCodeId = self.obEntityDataModel().id();

		if (disabilityCodeId)
		{
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), self.endpoint),
				{
					paramData: {
						DBID: tf.datasourceManager.databaseId,
						Id: disabilityCodeId
					}
				})
				.then(function(data)
				{
					var item = data.Items[0];
					self.obEntityDataModel(new TF.DataModel.DisabilityCodeDataModel(item));
				});
		}

		self.pageLevelViewModel.load(self.$form.data("bootstrapValidator"));
	};

	DisabilityCodeViewModel.prototype.apply = function()
	{
		return this.save();
	};

	DisabilityCodeViewModel.prototype.generateFunction = function(fn)
	{
		return fn.bind(this, Array.prototype.slice.call(arguments, 1));
	}

	DisabilityCodeViewModel.prototype.checkAffectedStudents = function()
	{
		// the function of check affects based on the data change
		var self = this,
			disabilityCodeId = self.obEntityDataModel().id(),
			studentEndpoint = tf.dataTypeHelper.getEndpoint("student"),
			tripStopEndpoint = tf.dataTypeHelper.getEndpoint("tripstop");

		// if new record	
		if (!disabilityCodeId) { return Promise.resolve(false); }

		var getTotalStudentCount = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), studentEndpoint),
			{
				paramData: {
					"disabilityCodeId": disabilityCodeId,
					"@count": true
				}
			}),
			getManualStudentCount = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), studentEndpoint),
				{
					paramData: {
						"disabilityCodeId": disabilityCodeId,
						"LoadTimeManuallyChanged": true,
						"@count": true
					}
				}),
			getTotalTripStopCount = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tripStopEndpoint),
				{
					paramData: {
						"disabilityCodeId": disabilityCodeId,
						"@count": true
					}
				}),
			getManualTripStopCount = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tripStopEndpoint),
				{
					paramData: {
						"disabilityCodeId": disabilityCodeId,
						"LoadTimeManuallyChanged": true,
						"@count": true
					}
				});

		return Promise.all([getTotalStudentCount, getManualStudentCount, getTotalTripStopCount, getManualTripStopCount])
			.then(function(res)
			{
				if (!Array.isArray(res)) return;

				return {
					"TotalStudentCount": res[0],
					"ManualStudentCount": res[1],
					"TotalStopsCount": res[2],
					"ManualStopsCount": res[3]
				};
			});
	}


	DisabilityCodeViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

