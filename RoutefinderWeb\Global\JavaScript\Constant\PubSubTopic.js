﻿var namespace = createNamespace("pb");
pb.DATA_CHANGE = "datachange";
pb.ADD = "add";
pb.EDIT = "edit";
pb.DELETE = "delete";
pb.UI_CHANGE = "uichange";
pb.RESIZE = "resize";
pb.TABS_SORTED = "tabssorted";
pb.REQUIRED_FIELDS_CHANGED = "requiredfieldschanged";
pb.REQUIRED_UDF_FIELDS_CHANGED = "requiredudffieldschanged";
pb.UDF_FIELD_CHANGED = "udffieldchanged";

function topicCombine()
{
	var args = Array.prototype.slice.call(arguments);
	if (Enumerable.From(args).Where(function(c) { return c == null || c == undefined }).ToArray().length != 0)
	{
		throw "some arguments are null or undefined";
	}
	var topic = Array.prototype.slice.call(arguments).join(".");
	if (topic != "")
	{
		return topic;
	}
	return null;
}

pb.getActionType = function(actionEnum)
{
	switch (actionEnum)
	{
		case 0:
			return pb.ADD;
		case 1:
			return pb.EDIT;
		case 2:
			return pb.DELETE;
		default:
			console.warn("Unsupported update type.");
			break;
	}

	return null;
}