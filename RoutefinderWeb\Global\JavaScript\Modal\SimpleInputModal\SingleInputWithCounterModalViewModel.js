(function()
{
	createNamespace("TF.Modal").SingleInputWithCounterModalViewModel = SingleInputWithCounterModalViewModel;

	function SingleInputWithCounterModalViewModel(options)
	{
		TF.Modal.SingleInputModalViewModel.call(this, options);
		this.contentTemplate('modal/SimpleInput/SingleInputWithCounter');
		this.viewModel = new TF.Control.SingleInputWithCounterViewModel(options);
		this.data(this.viewModel);
	}

	SingleInputWithCounterModalViewModel.prototype = Object.create(TF.Modal.SingleInputModalViewModel.prototype);

	SingleInputWithCounterModalViewModel.prototype.constructor = SingleInputWithCounterModalViewModel;
})();