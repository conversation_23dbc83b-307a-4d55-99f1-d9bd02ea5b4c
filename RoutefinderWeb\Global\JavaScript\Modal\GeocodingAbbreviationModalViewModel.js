﻿(function()
{
	createNamespace('TF.Modal').GeocodingAbbreviationModalViewModel = GeocodingAbbreviationModalViewModel;

	function GeocodingAbbreviationModalViewModel(fieldName, modal, uniqueChecking)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/GeocodingAbbreviationcontrol');
		this.buttonTemplate('modal/positivenegative');
		this.GeocodingAbbreviationViewModel = new TF.Control.GeocodingAbbreviationViewModel(fieldName, modal, uniqueChecking);
		this.data(this.GeocodingAbbreviationViewModel);
		this.sizeCss = "modal-dialog-lg";
		var viewTitle;
		switch (fieldName)
		{
			case 'geocodingabbreviation':
				viewTitle = " Geocoding Abbreviation";
				if (!modal)
					this.buttonTemplate('modal/positivenegativeextend');
				break;
			default:
				viewTitle = " Two Fields";
				break;
		}

		///this is going to check if the popup form is add new records or edit an existing record
		if (modal)
		{
			viewTitle = "Edit" + viewTitle;
		}
		else
		{
			viewTitle = "Add" + viewTitle;
		}

		this.title(viewTitle);
	}

	GeocodingAbbreviationModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	GeocodingAbbreviationModalViewModel.prototype.constructor = GeocodingAbbreviationModalViewModel;

	GeocodingAbbreviationModalViewModel.prototype.positiveClick = function()
	{
		this.GeocodingAbbreviationViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	GeocodingAbbreviationModalViewModel.prototype.negativeClose = function(returnData)
	{
		if (this.GeocodingAbbreviationViewModel.obEntityDataModel().apiIsDirty())
		{
			return tf.promiseBootbox.yesNo({ message: "You have unsaved changes.  Would you like to save your changes prior to closing?", backdrop: true, title: "Unsaved Changes", closeButton: true })
				.then(function(result)
				{
					if (result == true)
					{
						return this.positiveClick();
					}
					if (result == false)
					{
						return TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
					}
				}.bind(this));
		}
		else
		{
			TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
		}
	};

	GeocodingAbbreviationModalViewModel.prototype.saveAndNewClick = function()
	{
		this.GeocodingAbbreviationViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.GeocodingAbbreviationViewModel.obEntityDataModel(new this.GeocodingAbbreviationViewModel.entityDataModel());
				this.newDataList.push(result);
			}
		}.bind(this));
	};

	GeocodingAbbreviationModalViewModel.prototype.dispose = function()
	{
		this.GeocodingAbbreviationViewModel.dispose();
	};

})();
