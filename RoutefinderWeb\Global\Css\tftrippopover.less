﻿/* TF Trip Popover Styles */

.tf-trippopover-wrapper {
	position: absolute;
	margin-bottom: 13px;
	width: 450px;
}

.tf-trippopover-wrapper .header {
	border-bottom: 1px solid;
}

.tf-trippopover-wrapper .indent {
	margin: 0;
}


.tf-trippopover-wrapper .header .iconbutton {
	padding: 12pt !important;
}

.tf-trippopover-wrapper .title {
	font-weight: bold;
	margin: 6px 0 0 0;
	color: #333333;
}

.tf-trippopover-wrapper .title .time {
	color: #333333;
	font-weight: normal;
}

.tf-trippopover-wrapper .subtitle {
	font-size: small;
	color: gray;
}

.tf-trippopover-wrapper .buttons {
	margin: 6px 0 -13px 0;
}

.tf-trippopover-wrapper .buttons .button {
	padding: 5px;
	font-size: 12pt;
	opacity: 0.8;
}

.tf-trippopover-wrapper .buttons .button:hover {
	cursor: pointer;
	border: none;
}

.tf-trippopover-wrapper .trip {
	margin-top: 5px;
	height: 80px;
}

.tf-trippopover-wrapper .trip .row {
	margin-bottom: 5px;
	/* margin-right: 15px; */
}

.tf-trippopover-wrapper .trip .row > div:first-child {
	margin-left: 15px;
	margin-bottom: 5px;
}

.tf-trippopover-wrapper .trip .info {
}

.tf-trippopover-wrapper .trip .trip-label {
	font-weight: bold;
}

.tf-trippopover-wrapper .trip .trip-value {
	color: grey;
	font-size: small;
}

.arrow_box_top {
	position: relative;
	background: #ffffff;
	border: 1px solid #000000;
}

.arrow_box_top:after, .arrow_box_top:before {
	bottom: 100%;
	left: 80%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.arrow_box_top:after {
	border-color: rgba(255, 255, 255, 0);
	border-bottom-color: #ffffff;
	border-width: 10px;
	margin-left: -10px;
}

.arrow_box_top:before {
	border-color: rgba(0, 0, 0, 0);
	border-bottom-color: #000000;
	border-width: 11px;
	margin-left: -11px;
}
