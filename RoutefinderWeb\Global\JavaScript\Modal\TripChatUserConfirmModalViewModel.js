
(function()
{
	createNamespace("TF.Modal.Grid").TripChatUserConfirmModalViewModel = TripChatUserConfirmModalViewModel;

	function TripChatUserConfirmModalViewModel(selectRecordIds)
	{
		const self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-md";
		self.title("Select Trip Associated Staff Types");
		self.contentTemplate("Modal/TripChatUserConfirm");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel("Chat");
		self.obNegativeButtonLabel("Cancel");
		self.focusInFirstElement = false;

		self.model = new TF.Control.TripChatUserConfirmViewModel(selectRecordIds, self);
		self.data(self.model);
	}

	TripChatUserConfirmModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	TripChatUserConfirmModalViewModel.prototype.constructor = TripChatUserConfirmModalViewModel;

	TripChatUserConfirmModalViewModel.prototype.positiveClose = function()
	{
		const self = this, result = self.model.apply();
		result.then(items =>
		{
			if (items)
			{
				self.hide();
				self.resolve(items);
			}
			else
			{
				self.hide();
				tf.promiseBootbox.alert("No users are associated with the selected Staff record(s).");
			}
		});
	};
})();
