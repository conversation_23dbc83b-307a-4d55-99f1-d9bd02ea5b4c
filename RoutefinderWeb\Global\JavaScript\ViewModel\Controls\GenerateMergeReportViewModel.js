﻿(function()
{
	createNamespace('TF.Control').GenerateMergeReportViewModel = GenerateMergeReportViewModel;

	function GenerateMergeReportViewModel(udReport, output, options)
	{
		this.udReport = udReport;
		this.isEditing = udReport.isEditing;
		this.modalData = udReport.modalData;

		this.recordContactListmoverDefaultOptions = {
			title: "Select Records ",
			description: "You may select one or more specific records to create the document for. At least one record must be selected.",
			availableTitle: 'Available',
			selectedTitle: 'Selected',
			mustSelect: true,
			gridOptions:
			{
				forceFitColumns: true,
				enableColumnReorder: true
			}
		};
		this.options = options;
		this.output = output;
		this.specificRecordFormatter = this.specificRecordFormatter.bind(this);
		this.obReportDataSourceModels = ko.observableArray();
		this.obUDFEmails = ko.observableArray();
		this.showUDFEmails = ko.observable(true);

		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);

		this.obShowGPSEvents = ko.observable(true);
		this.Document = udReport.Document;
		this.defaultSpecifyRecordOption = options?.specifyRecordOption || "All Records";

		if (tf.permissions.filtersRead)
		{
			this.obSpecifyRecords = ko.observableArray([
				{
					id: 1,
					name: "All Records"
				},
				{
					id: 2,
					name: "Filter"
				},
				{
					id: 3,
					name: "Specific Records"
				}]);
		}
		else
		{
			this.obSpecifyRecords = ko.observableArray([
				{
					id: 1,
					name: "All Records"
				},
				{
					id: 3,
					name: "Specific Records"
				}]);
		}
		this.obOutputTo = ko.observable(GenerateMergeReportViewModel.outputTypes[output]);
		this.obOutputToText = ko.computed(function()
		{
			return this.obOutputTo() ? this.obOutputTo().name : " ";
		}, this);
		this.obSelectedSpecificRecord = ko.observableArray();
		this.obFilterDataModels = ko.observableArray();
		this.obEntityDataModel = ko.observable(new TF.DataModel.GenerateMergeReportDataModel());
		this.dataType = "";
		this.obFilter = ko.observable();
		this.obSpecifyRecordOption = ko.observable();
		this.IsNotUpgradedDataSource = ko.observable(false);
		this.reportUser = null;
		this.IsFirstLoading = true;
		this.obSpecifyRecordOptionText = ko.computed(function()
		{
			return this.obSpecifyRecordOption() ? this.obSpecifyRecordOption().name : " "
		}, this);

		this.obFilterVisible = ko.observable(false);
		this.obSpecificRecordsVisible = ko.observable(false);
		this.isInit = true;
		this.obFilterText = ko.computed(function()
		{
			return this.obFilter() ? this.obFilter().name() : " "
		}, this)
		this.obFilterText.subscribe(this.filterDataSourceSelectChange, this);

		this.obShowIncludeInActiveRecords = ko.computed(function()
		{
			return this.dataType == "student";
		}.bind(this));
		this.obDisabledFilteRecords = ko.computed(function()
		{
			var type = this.dataType;
			return type == "other";
		}.bind(this));
		this.obDisabledSpecifyRecords = ko.computed(function()
		{
			return this.IsNotUpgradedDataSource() || this.obDisabledFilteRecords();
		}.bind(this));
		this.obDisabledFilteName = ko.computed(function()
		{
			return this.dataType === "other" ||
				this.obEntityDataModel().specifyRecordOption() != 2 || this.IsNotUpgradedDataSource() || this.obDisabledFilteRecords();
		}.bind(this));
		this.obDisabledSpecificRecord = ko.computed(function()
		{
			return this.dataType == "other" ||
				this.obEntityDataModel().specifyRecordOption() != 3 || this.IsNotUpgradedDataSource();
		}.bind(this));
		this.obSpecificRecordStringForValidation = ko.computed(function()
		{

			if (this.obDisabledFilteRecords() || this.obDisabledSpecificRecord() || this.obSelectedSpecificRecord().length > 0)
			{
				return "1";
			}
			else
			{
				return "";
			}
		}.bind(this));
		this.obFilterNameStringForValidation = ko.computed(function()
		{
			if (this.obDisabledFilteRecords() || this.obDisabledFilteName())
			{
				return "1";
			}
			else
			{
				return this.obEntityDataModel().filterName();
			}
		}.bind(this));

		this.obDataTypeName = ko.observable("");
		this.obSpecifyRecordsLabel = ko.computed(function()
		{
			return `Specify ${this.obDataTypeName()} Records`;
		}, this);
		ko.computed(this.specificRecordSelectComputed, this);
		ko.computed(this.filterNameComputed, this);
		ko.computed(this.outputToComputed, this);
		this.Id = udReport.Id;

		this.obIsNotContactDataType = ko.observable(!((udReport.Document && udReport.Document.DataTypeName === 'Contact') || this.dataType.toLowerCase() === 'contact'));
		this.datasources = ko.observableArray([]);
		this.selectedDatasource = ko.observable();
		this.selectedDatasourceText = ko.computed(function()
		{
			let name = " ";
			let ds = this.selectedDatasource();
			if (ds)
			{
				if (ds.Name)
				{
					name = ds.Name;
				}
				else
				{
					let dsFound = this.datasources().find((d) => d.DBID === ds.DBID);
					if (dsFound)
					{
						name = dsFound.Name;
					}
				}
			}
			return name;
		}, this);
		if (udReport.entityType)
		{
			this.dataType = tf.dataTypeHelper.getKeyById(udReport.entityType);
		}

		this.load().then(async () =>
		{
			this.dataLoaded = true;
			var defaultDatasources = this.datasources().filter(function(ds)
			{
				return ds.DBID === tf.datasourceManager.databaseId;
			});
			var defaultDatasource = defaultDatasources.length > 0 ? defaultDatasources[0] : null;
			this.selectedDatasource(this.isEditing ? this.modalData.selectedDatasource : defaultDatasource);
			this.selectedDatasourceText.subscribe(this.selectedDataSourceUpdated, this);
			this.obSpecifyRecordOptionText.subscribe(this.specificRecordSelectChange, this);
			if (udReport.entityType)
			{
				this.getEmailUDFs();
			}

			await this.bindDefaultReportInfo(udReport);
			await this.initSpecificRecord(udReport);
			var dataTypeName = tf.dataTypeHelper.getNameByType(this.dataType);
			this.obDataTypeName(dataTypeName);
			this.emailDesc("Select how to send the email, then select " + dataTypeName + " records to merge and the associated contacts to receive the email.");
			this.recordsDesc("You may filter the " + dataTypeName + " records using a Filter or by selecting Specific Records. Unless a Filter or Specific Records are selected, all records will be used.");
			this.obSpecifyRecordOption.subscribe(() => { this.isInit = false; }, this);
			this.obEntityDataModel().selectedRecordIds.subscribe(() => { this.isInit = false; }, this);

			let initData = udReport.modalData;

			this.backupEntity = {
				destinationType: initData ? initData.destinationType : this.destinationType(),
				selectedDatasource: initData ? initData.selectedDatasource.DBID : this.selectedDatasource().DBID,
				selectedSpecifyRecordOption: initData ? initData.selectedSpecifyRecordOption.id : this.obSpecifyRecordOption().id,
				selectedSpecificRecords: (initData && initData.selectedSpecificRecords) ? initData.selectedSpecificRecords.map(a => a.Id) : (this.options?.selectedRecordIds || []),
				selectedEmailUDFs: (initData && initData.selectedEmailUDFs) ? initData.selectedEmailUDFs.map(a => a.Id) : [],
				filterId: this.options?.FilterId ?? undefined,
				checkedUDFs: (initData && initData.selectedEmailUDFs) ? initData.selectedEmailUDFs.map(a => a.UDFEmail_EmailField) : []
			}
			if (this.dataType !== 'contact')
			{
				this.backupEntity.selectedRecordContactIds = initData ? initData.selectedRecordContactIds : [];
			}
		});

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		this.to = ko.observable("");
		this.cc = ko.observable("");
		this.bcc = ko.observable("");
		this.destinationType = ko.observable((this.output === "email" && this.isEditing) ? this.modalData.destinationType : "Bcc");
		this.originalDestinationType = this.destinationType();
		this.emailDesc = ko.observable("");
		this.recordsDesc = ko.observable("");
		this.recordContactListMoverViewModel = ko.observable();
		this.emailUDFListMoverViewModel = ko.observable();
		this.obEntityDataModel().selectedRecordIds.subscribe(this.refreshRecordContactListMover, this);
	}

	GenerateMergeReportViewModel.outputTypes = {
		view: {
			id: 0,
			name: "View",
			action: "Generate",
			title: "Generate Merge Document",
			titleWithDocName: true
		},
		email: {
			id: 1,
			name: "Email",
			action: "Apply",
			title: "Add Recipient",
			titleWithDocName: false
		},
		sendemail: {
			id: 2,
			name: "Email",
			action: "Send",
			title: "Send Merge Email Message",
			titleWithDocName: true
		},
		preview: {
			id: 3,
			name: "View",
			action: "Preview",
			title: "Preview Merge Document",
			titleWithDocName: true
		},
		previewEmail: {
			id: 4,
			name: "View",
			action: "Preview",
			title: "Preview Merge Email Message",
			titleWithDocName: true
		},
		viewEmail: {
			id: 5,
			name: "View",
			action: "Generate",
			title: "Generate Merge Email Message",
			titleWithDocName: true
		}
	};

	GenerateMergeReportViewModel.prototype = Object.create(TF.Control.BaseControl.prototype);
	GenerateMergeReportViewModel.prototype.constructor = GenerateMergeReportViewModel;

	GenerateMergeReportViewModel.prototype.selectedDataSourceUpdated = function()
	{
		this.specificRecordSelectChange();
		this.obSelectedSpecificRecord([]);
		this.obEntityDataModel().selectedRecordIds([]);

		this.getEmailUDFs();
	};

	GenerateMergeReportViewModel.prototype.refreshRecordContactListMover = function()
	{
		this.buildRecordContactListMoverWidget().then(widget =>
		{
			this.recordContactListMoverViewModel(widget);
		});
		this.emailUDFListMoverViewModel(this.buildEmailUDFListMoverWidget())
	};

	GenerateMergeReportViewModel.prototype.getRecordContactListMoverWidgetOption = function()
	{
		let requestOptions = {
			data: {
				filterSet: {
					FilterItems: [],
					FilterSets: [],
					LogicalOperator: "and"
				}
			}
		};
		let dataTypeFilter = {
			FieldName: tf.recordContactGridDefinition.gridDefinition().Columns.filter(function(c) { return c.FieldName === "DataTypeId" })[0].DBName,
			Operator: "EqualTo",
			Value: tf.dataTypeHelper.getId(this.dataType)
		};
		let contactEmailFilter = {
			FieldName: "ContactEmail",
			Operator: "IsNotNull",
			Value: ""
		};
		requestOptions.data.filterSet.FilterItems.push(dataTypeFilter);
		requestOptions.data.filterSet.FilterItems.push(contactEmailFilter);
		var options = $.extend(
			{},
			this.recordContactListmoverDefaultOptions,
			{
				type: "recordcontact",
				dataSource: this.selectedDatasource().DBID,
				showRemoveColumnButton: true,
				gridOptions: {
					filter:
						[
							dataTypeFilter
						],
					contactEmailFilter: contactEmailFilter,
				},
				hideTotalFromLeftGrid: true,
				noPermission: !tf.authManager.isAuthorizedForDataType('contact', "read")
					&& "You don't have permission to view data.",
			});

		if (this.obSpecifyRecordOption().id === 2 || this.obSpecifyRecordOption().id === 3)
		{
			var RecordIdsFilter =
			{
				FieldName: tf.recordContactGridDefinition.gridDefinition().Columns.filter(function(c) { return c.FieldName === "RecordId" })[0].DBName,
				IsListFilter: true,
				Operator: "In",
				Value: ",",
				ValueList: "[" + this.obEntityDataModel().selectedRecordIds().join(",") + "]"
			};
			options.gridOptions.filter.push(RecordIdsFilter);
			requestOptions.data.filterSet.FilterItems.push(RecordIdsFilter);
		}

		return { options, requestOptions };
	};

	GenerateMergeReportViewModel.prototype.buildRecordContactListMoverWidget = function()
	{
		let widgetOption = this.getRecordContactListMoverWidgetOption();
		let options = widgetOption.options;
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), options.dataSource, 'search', tf.dataTypeHelper.getEndpoint(options.type), "count"), widgetOption.requestOptions).then(result =>
		{
			options.totalRecordCount = (result.Items || []).length === 1 ? result.Items[0] : 0;
			var widgetViewModel = new TF.Modal.ListMoverSelectRecordControlWidgetViewModel(
				(this.isEditing && this.output === "email" && this.isInit) ? this.modalData.selectedRecordContactRecords : [],
				options
			);

			return widgetViewModel;
		});
	};

	GenerateMergeReportViewModel.prototype.getEmailUDFListMoverWidgetOption = function()
	{
		var options = $.extend(
			{},
			this.recordContactListmoverDefaultOptions,
			{
				nosticky: true,
				type: this.dataType,
				dataSource: this.selectedDatasource().DBID,
				showRemoveColumnButton: false,
				gridOptions: {
				},
				disableAutoComplete: true,
				hideTotalFromLeftGrid: true,
				idSeparator: true
			});

		if (this.obSpecifyRecordOption().id === 2 || this.obSpecifyRecordOption().id === 3)
		{
			var RecordIdsFilter =
			{
				FieldName: "Id",
				IsListFilter: true,
				Operator: "In",
				Value: ",",
				ValueList: "[" + this.obEntityDataModel().selectedRecordIds().join(",") + "]"
			};
			options.gridOptions.filter = [RecordIdsFilter];
		}

		options.emailUDFs = this.obUDFEmails;

		return options;
	};

	GenerateMergeReportViewModel.prototype.buildEmailUDFListMoverWidget = function()
	{
		var options = this.getEmailUDFListMoverWidgetOption();
		var widgetViewModel = new TF.Modal.ListMoverSelectEmailUDFRecordControlWidgetViewModel(
			(this.isEditing && this.output === "email" && this.modalData.selectedEmailUDFs || []),
			options
		);

		return widgetViewModel;
	}

	GenerateMergeReportViewModel.prototype.bindFilterDropdown = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"),
			{
				paramData: {
					"@filter": String.format("(eq(dbid, {0})|isnull(dbid,))&eq(datatypeId,{1})", this.selectedDatasource().DBID, tf.dataTypeHelper.getId(this.dataType)),
				}
			})
			.then(function(data)
			{
				var filterList = data.Items;
				filterList = filterList.filter(function(ele)
				{
					return ele.IsValid != null;
				});
				filterList = sortFilter(filterList);
				if (!filterList.length)
				{
					filterList.unshift(
						{
							Name: " ",
							Id: undefined
						});
				}
				this.obFilterDataModels(TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, filterList));
			}.bind(this));
	};

	GenerateMergeReportViewModel.prototype.initSpecificRecord = async function(report)
	{
		if (!this.dataLoaded || !this.uiInitialized)
		{
			return;
		}

		if (this.isEditing && (this.obSpecifyRecordOption().id === 3)) //3:Specific Records type
		{
			await this._setupSpecificRecordIds(this.modalData.selectedRecordIds, true);
			this.obEntityDataModel().selectedRecordIds(this.modalData.selectedRecordIds);
		}
	};

	GenerateMergeReportViewModel.prototype.init = function(viewModel, el)
	{
		this._$form = $(el);
		this.initValidation();
		this.uiInitialized = true;
		this.initSpecificRecord(this.udReport);
		this._$form.find("[name=selReportType]").focus();
	};

	GenerateMergeReportViewModel.prototype.initValidation = function()
	{
		var self = this,
			isValidating = false;
		if (!self._$form)
		{
			return;
		}

		var validatorFields = {};
		validatorFields["filterName"] = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "is required"
				}
			}
		};

		validatorFields["specificRecords"] = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: " At least one record must be selected"
				}
			}
		};

		if (self.output == "email")
		{
			validatorFields["to"] = validatorFields["cc"] = validatorFields["bcc"] = {
				trigger: "blur change",
				validators:
				{
					callback:
					{
						callback: function(value, validator, $field)
						{
							value = (value || "").trim();
							var testResult = TF.testEmail(value);
							var errorEmails = testResult.invalidList;
							var msg = null;
							if (errorEmails.length == 1)
							{
								msg = errorEmails[0] + ' is not a valid email.';
							}
							else if (errorEmails.length > 1)
							{
								msg = errorEmails.length + ' emails are invalid.';
							}

							return { valid: errorEmails.length == 0, message: msg };
						}
					}
				}
			};
		}

		self._$form.bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			})
			.on('success.field.bv', function(e, data)
			{
				var $parent = data.element.closest('.form-group');
				$parent.removeClass('has-success');
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

		self.pageLevelViewModel.load(self._$form.data("bootstrapValidator"));
	};

	GenerateMergeReportViewModel.prototype.normalizTime = function(time)
	{
		time = moment(time);
		time.set('year', 1900);
		time.set('month', 1);
		time.set('date', 1);
		return time;
	};

	GenerateMergeReportViewModel.prototype.convertHtmlTagToOptionText = function(option, item)
	{
		//return ko.applyBindingsToNode(option, { disable: item.DatabaseName === '<hr>', text: item.DatabaseName === '<hr>' ? '------------' : item.DatabaseName }, item);
		var $optionNdoe = $('#filterDataSource').find(option);
		if ($optionNdoe.text() === '<hr>')
		{
			$optionNdoe.text('------------');
		}
	};

	GenerateMergeReportViewModel.prototype.handleReportSource = function(Items)
	{
		this.reportDataSourceModels = Items;
		var reportDataSourceModels = [];
		for (var i = 0; i < Items.length; i++)
		{
			if (Items[i].BaseDataType === 0)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'student', tf.applicationTerm.getApplicationTermSingularByName('Student'));
			}
			if (Items[i].BaseDataType === 1)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'school', tf.applicationTerm.getApplicationTermSingularByName('School'));
			}
			if (Items[i].BaseDataType === 2)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'district', tf.applicationTerm.getApplicationTermSingularByName('District'));
			}
			if (Items[i].BaseDataType === 3)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'contractor', tf.applicationTerm.getApplicationTermSingularByName('Contractor'));
			}
			if (Items[i].BaseDataType === 4)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'vehicle', tf.applicationTerm.getApplicationTermSingularByName('Vehicle'));
			}
			if (Items[i].BaseDataType === 5)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'staff', tf.applicationTerm.getApplicationTermSingularByName('Staff'));
			}
			if (Items[i].BaseDataType === 6)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'altsite', tf.applicationTerm.getApplicationTermSingularByName('Alternate Site'));
			}
			if (Items[i].BaseDataType === 7)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'trip', tf.applicationTerm.getApplicationTermSingularByName('Trip'));
			}
			if (Items[i].BaseDataType === 9)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'other', 'Other');
			}
			if (Items[i].BaseDataType === 10)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'fieldtrip', tf.applicationTerm.getApplicationTermSingularByName('Field Trip'));
			}
			if (Items[i].BaseDataType === 13 ||
				Items[i].BaseDataType === 14 ||
				Items[i].BaseDataType === 15)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'busfinder', 'Busfinder');
			}
			if (Items[i].BaseDataType === 16)
			{
				buildReportDataSource(Items[i], reportDataSourceModels, 'custom', 'Custom');
			}
		}

		// sort each section
		reportDataSourceModels.forEach(function(reportGroup)
		{
			reportGroup.source = Enumerable.From(reportGroup.source).OrderBy(function(report)
			{
				return report.Name;
			}).ToArray();
		});
		var xxx = Enumerable.From(reportDataSourceModels).OrderBy(function(x)
		{
			return x.displayName;
		}).ToArray();
		this.obReportDataSourceModels(xxx);
	}

	function buildReportDataSource(item, reportDataSourceModels, type, label)
	{
		var datas = Enumerable.From(reportDataSourceModels).Where(function(x)
		{
			return x.displayName == label;
		}).ToArray();
		var data;
		if (datas.length == 0)
		{
			data = {
				displayName: label,
				source: []
			};
			reportDataSourceModels.push(data);
		}
		else
		{
			data = datas[0];
		}
		data.source.push($.extend(item,
			{
				type: type,
				label: label,
				groupName: label
			}));
	};

	GenerateMergeReportViewModel.prototype.load = function()
	{
		let promiseOfMerges = Promise.resolve();
		if (!this.dataType)
		{
			promiseOfMerges = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocuments", this.Id))
				.then(function(data)
				{
					this.dataType = tf.dataTypeHelper.getKeyById(data.Items[0].DataTypeId);
					this.obIsNotContactDataType(this.dataType !== 'contact');
					this.getEmailUDFs();
				}.bind(this));
		}

		const promiseOfGetAuthedDatasources = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "databases?authedOnly=true&@sort=Name")).then(res =>
			this.datasources(res.Items));

		return Promise.all([promiseOfMerges, promiseOfGetAuthedDatasources]);
	};

	GenerateMergeReportViewModel.prototype.getEmailUDFs = function()
	{
		if (this.dataType != 'contactor' && this.udReport.Document.Content)
		{
			let $content = $("<div>").append(this.udReport.Document.Content),
				contactorFields = $content.find('[data-field-contact]');

			contactorFields = contactorFields.filter(function()
			{
				let isFieldContact = $(this).data('field-contact');
				return isFieldContact === true;
			});
			this.showUDFEmails(contactorFields.length === 0);
		}
		tf.UDFDefinition.RetrieveByType(this.dataType).then(() =>
		{
			const selectedDBID = this.selectedDatasource().DBID;
			let emailUDFs = tf.UDFDefinition.get(this.dataType).userDefinedFields.filter(item =>
			{
				return item.UDFType === 'email' && item.UDFDataSources.findIndex(dataSource => dataSource.DBID === selectedDBID) > -1;
			});

			let emailSelectedData = this.emailUDFListMoverViewModel().selectedData;
			let emailFields = emailUDFs.map(udf =>
			{
				let hasSelectedData = emailSelectedData.findIndex(data => data.UDFEmail_EmailField === udf.OriginalName) > -1;
				udf.obFieldChecked = ko.observable(hasSelectedData);
				udf.obFieldChecked.subscribe(() =>
				{
					this.updateUDFEmailsListMover();
				});

				return udf;
			}).sort((a, b) => a.DisplayName.toLowerCase() > b.DisplayName.toLowerCase() ? 1 : -1);
			this.obUDFEmails(emailFields);
		});
	}

	GenerateMergeReportViewModel.prototype.updateUDFEmailsListMover = function()
	{
		let emailUDFListMoverWidget = this.emailUDFListMoverViewModel();
		emailUDFListMoverWidget.reloadListMover();
	}

	GenerateMergeReportViewModel.prototype.filterDataSourceSelectChange = function()
	{
		this.obSelectedSpecificRecord([]);
		if (this.output === 'email')
		{
			this.getIdsByFilter();
		} else
		{
			this.obEntityDataModel().selectedRecordIds([]);
		}

		this.removeBootstrapValidationError();
	};

	GenerateMergeReportViewModel.prototype.specificRecordSelectChange = function()
	{
		if (this.obSpecifyRecordOption())
		{
			this.obFilterVisible(this.obSpecifyRecordOption().id === 2);
			this.obSpecificRecordsVisible(this.obSpecifyRecordOption().id === 3);
			if (this.obSpecifyRecordOption().id === 2)
			{
				var self = this;
				this.bindFilterDropdown().then(function()
				{
					if (self.options && self.options.FilterId)
					{
						let filter = self.obFilterDataModels().filter(x => x.id() === self.options.FilterId);
						if (filter.length > 0)
						{
							self.obFilter(filter[0]);
						}
					}
					else
					{
						self.obFilter(self.obFilterDataModels()[0]);
					}
				});
				this.refreshRecordContactListMover();
			} else
			{
				this.obFilter(null);
			}

			this.obSelectedSpecificRecord([]);
			this.obEntityDataModel().selectedRecordIds([]);
		}
		else
		{
			this.obFilter(this.obFilterDataModels()[0]);
		}
		this.removeBootstrapValidationError();
	};

	GenerateMergeReportViewModel.prototype.loadSpecificRecords = function(ids)
	{
		var self = this;
		if (!Array.isArray(ids) || ids.length === 0)
		{
			self.obSpecifyRecords([]);
			return;
		}

		var dataType = self.dataType;
		let isDistrictType = dataType === 'district';
		// basic columns of 'district'(getBasicColumnsByDataType) is "Name" field not "District"
		// so config the needed field("District") for 'district' type
		let districtBasicColumn = ["District"];
		let columns = isDistrictType ?
			districtBasicColumn :
			tf.dataTypeHelper.getBasicColumnsByDataType(dataType).map(function(item)
			{
				return item.FieldName;
			});

		let sortField = columns && columns.length > 0 ? [columns[0]] : [];

		return tf.dataTypeHelper.getRecordByIdsAndColumns(self.selectedDatasource().DBID, dataType, ids, columns, sortField)
			.then(function(selectedRecords)
			{
				self.obSelectedSpecificRecord(selectedRecords);
				self.obEntityDataModel().selectedRecordIds(selectedRecords.map(function(record) { return record.Id; }));
			});
	};

	GenerateMergeReportViewModel.prototype.bindDefaultReportInfo = async function(report)
	{
		if (this.isEditing)
		{
			this.obSpecifyRecordOption(this.modalData.selectedSpecifyRecordOption);
			return;
		}

		if (this.options && this.options.selectedRecordIds)
		{
			this.obSpecifyRecordOption(this.obSpecifyRecords()[2]);
			this.selectedDatasource(this.datasources().filter(function(datasource) { return datasource.DBID == this.options.selectedDataSourceId; }.bind(this))[0]);
			await this.loadSpecificRecords(this.options.selectedRecordIds);
		}
		else
		{
			var specifyRecordId = this.obSpecifyRecords().filter(e =>
			{
				return e.name === this.defaultSpecifyRecordOption;
			})[0]?.id;
			var theDatas = Enumerable.From(this.obSpecifyRecords()).Where(function(x)
			{
				return x.id == specifyRecordId;
			}.bind(this)).ToArray();
			if (theDatas)
			{
				this.obSpecifyRecordOption(theDatas[0]);
			}
		}
	};

	GenerateMergeReportViewModel.prototype.specificRecordSelectComputed = function()
	{
		if (this.obSpecifyRecordOption())
		{
			this.obEntityDataModel().specifyRecordOption(this.obSpecifyRecordOption().id);

		}
		else
		{
			this.obEntityDataModel().specifyRecordOption(0);
		}
	};

	GenerateMergeReportViewModel.prototype.filterNameComputed = function()
	{
		if (this.obFilter())
		{
			this.obEntityDataModel().filterName(this.obFilter().name());
			this.obEntityDataModel().filterSpec(this.obFilter().whereClause());
		}
		else
		{
			this.obEntityDataModel().filterName("");
			this.obEntityDataModel().filterSpec("");
		}
	};

	GenerateMergeReportViewModel.prototype.outputToComputed = function()
	{
		var output = "";
		if (this.obOutputTo())
		{

			switch (this.obOutputTo().id)
			{
				case 0:
				case 3:
				case 4:
				case 5:
					output = "view";
					break;
				case 1:
				case 2:
					output = "email";
					break;
			}
		}

		this.output = output;
		this.obEntityDataModel().outputTo(output);
	};

	GenerateMergeReportViewModel.prototype.specificRecordFormatter = function(specificRecordDataModel)
	{
		var name;
		switch (this.dataType)
		{
			case "student":
				name = specificRecordDataModel.FullName || this._getFullName(specificRecordDataModel);
				break;
			case "staff":
				name = specificRecordDataModel.FullName || this._getFullNameWitnMiddleName(specificRecordDataModel);
				break;
			case "vehicle":
				name = specificRecordDataModel.BusNum;
				break;
			case "school":
				name = specificRecordDataModel.Name;
				break;
			case "district":
				name = specificRecordDataModel.District || specificRecordDataModel.IdString;
				break;
			case "busfinderDriver":
				name = specificRecordDataModel.DriverName;
				break;
			case "busfinderVehicle":
				name = specificRecordDataModel.ExternalName;
				break;
			case "contact":
				name = this._getFullName(specificRecordDataModel);
				break;
			case "tripstop":
				name = specificRecordDataModel.Street;
				break;
			default:
				name = specificRecordDataModel.Name;
				break;
		}
		return name;
	};

	GenerateMergeReportViewModel.prototype._getSelectRecordSort = function()
	{
		switch (this.dataType)
		{
			case "student":
				return "LastName,FirstName,FullName";
			case "staff":
				return "LastName,FirstName,MiddleName,FullName";
			case "vehicle":
				return "BusNum";
			case "school":
				return "Name";
			case "district":
				return "District,IdString";
			case "busfinderDriver":
				return "DriverName";
			case "busfinderVehicle":
				return "ExternalName";
			case "contact":
				return "LastName,FirstName";
			case "tripstop":
				return "Street";
			default:
				return "Name";
		}
	}

	GenerateMergeReportViewModel.prototype._getFullName = function(nameEntity)
	{
		var tmpNameEntity = [];
		if (nameEntity.LastName) tmpNameEntity.push(nameEntity.LastName);
		if (nameEntity.FirstName) tmpNameEntity.push(nameEntity.FirstName);

		return tmpNameEntity.join(', ');
	};

	GenerateMergeReportViewModel.prototype._getFullNameWitnMiddleName = function(nameEntity)
	{
		var fullName = this._getFullName(nameEntity);
		if (nameEntity.MiddleName)
		{
			fullName = fullName + ' ' + tmpNameEntity.MiddleName;
		}
		return fullName;
	};

	GenerateMergeReportViewModel.prototype._setupSpecificRecordIds = async function(selectedRecord, isIds)
	{
		var self = this;
		if (selectedRecord && $.isArray(selectedRecord))
		{
			if (selectedRecord.length === 0)
			{
				this.obSelectedSpecificRecord([]);
				this.obEntityDataModel().selectedRecordIds([]);
				return;
			}
			var validator = this._$form.data("bootstrapValidator");
			if (isIds)
			{//student need paging, because it is too big.
				var apiBatches = [],
					dataTypeConfig = tf.dataTypeHelper.getDataTypeConfig(this.dataType),
					dbid = dataTypeConfig && dataTypeConfig.hasDBID ? this.selectedDatasource().DBID : "",
					baseUrl = pathCombine(tf.api.apiPrefixWithoutDatabase(), dbid, tf.dataTypeHelper.getEndpoint(this.dataType)),
					splittedListGroups = TF.Helper.ApiSplitter.split(baseUrl, selectedRecord);

				splittedListGroups.forEach(list =>
				{
					if (self.dataType === 'tripstop')
					{
						var tripStopUrl = pathCombine(tf.api.apiPrefixWithoutDatabase(), dbid, "search", tf.dataTypeHelper.getEndpoint(self.dataType));
						var requestOptions = {
							data: {
								fields: ["Name", "Street", "Id"],
								idFilter: { IncludeOnly: list }
							}
						};
						var tripStopPromise = tf.promiseAjax.post(tripStopUrl, requestOptions);
						apiBatches.push(tripStopPromise);
					}
					else
					{
						var p = tf.promiseAjax.get(baseUrl, {
							paramData: { "@filter": "in(Id," + list.join(",") + ")", "@sort": self._getSelectRecordSort() }
						});
						apiBatches.push(p);
					}
				});
				await Promise.all(apiBatches)
					.then(function(data)
					{
						var items = [];
						data.forEach(function(response)
						{
							items = items.concat(response.Items);
						});
						this.obSelectedSpecificRecord(items);
						this.obEntityDataModel().selectedRecordIds(selectedRecord);
						validator.validate();
					}.bind(this));
			}
			else
			{
				this.obSelectedSpecificRecord(selectedRecord);
				this.obEntityDataModel().selectedRecordIds(selectedRecord.map(function(item)
				{
					return item.Id;
				}));

				validator.validate();
			}
		}
	};

	GenerateMergeReportViewModel.prototype.selectRecordClick = function()
	{
		var type = this.dataType;
		if (type != undefined && type != "")
		{
			tf.modalManager.showModal(
				new TF.Modal.ListMoverSelectRecordControlModalViewModel(
					this.obSelectedSpecificRecord(),
					$.extend(
						{},
						this.recordContactListmoverDefaultOptions,
						{
							type: type,
							dataSource: this.selectedDatasource().DBID,
							showRemoveColumnButton: true,
							allowApplyZeroRecord: true,
							onlyReturnId: true
						})
				)
			).then(function(result) { this._setupSpecificRecordIds(result, true) }.bind(this));
		}
	};

	GenerateMergeReportViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(data)
			{
				return data;
			});
	};

	GenerateMergeReportViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
				{
					return Promise.reject();
				}
				else
				{
					switch (this.obOutputTo().name)
					{
						case "View":
							return this.generateMergeDocument();
						case "Email":
							return this.applySendEmailBatch();
						default:
					}
				}
			}.bind(this));
	};

	GenerateMergeReportViewModel.prototype.postMergeDocumentRuntime = async function()
	{
		let data = await this.getPostData();
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocumentruntimes"), {
			data: data
		});
	};

	GenerateMergeReportViewModel.prototype.getEmailUDFsMapping = function(emailMapping)
	{
		let selectedRecords = this.emailUDFListMoverViewModel().selectedData;

		if (selectedRecords.length == 0)
		{
			return emailMapping;
		}

		selectedRecords.forEach(rc =>
		{
			var recordMapping =
			{
				RecordId: parseInt(rc.originalId),
				To: "",
				Cc: "",
				Bcc: "",
				ToContact: [],
				CcContact: [],
				BccContact: [],
				IsEmailUDF: true
			};

			let emailField = rc['UDFEmail_EmailField'];
			let email = rc[emailField];

			if (email)
			{
				let concatItem = { "Name": null, "Id": null, "Email": email, "AssociatedRecord": rc.Name || "", IsEmailUDF: true };
				switch (this.destinationType())
				{
					case "To":
						recordMapping.To = email;
						recordMapping.ToContact.push(concatItem);
						break;
					case "Cc":
						recordMapping.Cc = email;
						recordMapping.CcContact.push(concatItem);
						break;
					case "Bcc":
						recordMapping.Bcc = email;
						recordMapping.BccContact.push(concatItem);
						break;
				}
			}

			if (recordMapping.To || recordMapping.Cc || recordMapping.Bcc)
			{
				emailMapping.push(recordMapping);
			}

			return emailMapping;
		});
	}

	GenerateMergeReportViewModel.prototype.getContactDataTypeEmailMapping = async function()
	{
		let selectedRecordIds = this.obEntityDataModel().selectedRecordIds();
		let includeOnly = this.obSpecifyRecordOption().id === 1 ? null : (selectedRecordIds.length > 0 ? selectedRecordIds : []);

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix('v2', this.selectedDatasource().DBID), 'search', 'contacts'), {
			paramData: {
				'databaseId': this.selectedDatasource().DBID,
			},
			data: {
				fields: ['Id', 'FirstName', 'LastName', 'Email'],
				filterClause: '',
				filterSet: null,
				idFilter: {
					ExcludeAny: [],
					ExcludeAnyIdsString: null,
					IncludeOnly: includeOnly,
					IncludeOnlyIdsString: includeOnly && includeOnly.join(','),
				},
				sortItems: [{
					Direction: 'Ascending',
					Name: 'LastName',
					isAscending: 'asc'
				}, {
					Direction: 'Ascending',
					Name: 'FirstName',
					isAscending: 'asc'
				}]
			}
		}).then(response =>
		{
			let emailMapping = [],
				selectedRecords = response.Items;

			selectedRecords.forEach(record =>
			{
				let email = record.Email,
					recordMapping =
					{
						RecordId: parseInt(record.Id),
						To: "",
						Cc: "",
						Bcc: "",
						ToContact: [],
						CcContact: [],
						BccContact: [],
					};
				let testEmailList = TF.testEmail(email).validList;
				if (email && testEmailList.length > 0)
				{
					let associatedRecord = "";
					if (!!record.FirstName && !!record.LastName)
					{
						associatedRecord = `${record.LastName}, ${record.FirstName}`;
					} else if (!!record.FirstName || !!record.LastName)
					{
						associatedRecord = record.FirstName || record.LastName
					}

					let item = {
						"Name": record.FirstName + record.LastName,
						"Id": record.Id,
						"Email": email,
						"AssociatedRecord": associatedRecord,
					};
					switch (this.destinationType())
					{
						case "To":
							recordMapping.To = email;
							recordMapping.ToContact.push(item);
							break;
						case "Cc":
							recordMapping.Cc = email;
							recordMapping.CcContact.push(item);
							break;
						case "Bcc":
							recordMapping.Bcc = email;
							recordMapping.BccContact.push(item);
							break;
					}
					recordMapping.item = item;
				}

				if (recordMapping.To || recordMapping.Cc || recordMapping.Bcc)
				{
					emailMapping.push(recordMapping);
				}
			});

			this.contactDataTypeEmailMapping = emailMapping;

			return emailMapping;
		});
	}

	GenerateMergeReportViewModel.prototype.getEmailMapping = function()
	{
		let emailMapping = [];
		let selectedRecordIds = this.recordContactListMoverViewModel().selectedData;

		if (selectedRecordIds.length == 0)
		{
			return emailMapping;
		}

		const options = { data: {} };
		this.recordContactListMoverViewModel()._addSortItemIntoRequest(options);

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix('v2', this.selectedDatasource().DBID), 'search', 'recordcontacts'), {
			paramData: {
				'databaseId': this.selectedDatasource().DBID,
			},
			data: {
				fields: this.recordContactListMoverViewModel().getFields(),
				filterClause: '',
				filterSet: null,
				idFilter: {
					ExcludeAny: [],
					IncludeOnly: selectedRecordIds.length > 0 ? selectedRecordIds : null,
				},
				sortItems: options.sortItems
			}
		}).then(response =>
		{
			const selectedRecords = response.Items;
			this.recordContactListMoverViewModel().selectedData = response.Items;
			selectedRecords.forEach(rc =>
			{
				var recordMapping =
				{
					RecordId: parseInt(rc.RecordID),
					To: "",
					Cc: "",
					Bcc: "",
					ToContact: [],
					CcContact: [],
					BccContact: [],
				};

				if (rc.ContactEmail)
				{
					switch (this.destinationType())
					{
						case "To":
							recordMapping.To = rc.ContactEmail;
							recordMapping.ToContact.push({ "Name": rc.ContactName, "Id": rc.Id, "Email": rc.ContactEmail, "AssociatedRecord": rc.AssociatedRecord || "" });
							break;
						case "Cc":
							recordMapping.Cc = rc.ContactEmail;
							recordMapping.CcContact.push({ "Name": rc.ContactName, "Id": rc.Id, "Email": rc.ContactEmail, "AssociatedRecord": rc.AssociatedRecord || "" });
							break;
						case "Bcc":
							recordMapping.Bcc = rc.ContactEmail;
							recordMapping.BccContact.push({ "Name": rc.ContactName, "Id": rc.Id, "Email": rc.ContactEmail, "AssociatedRecord": rc.AssociatedRecord || "" });
							break;
					}
				}

				if (recordMapping.To || recordMapping.Cc || recordMapping.Bcc)
				{
					emailMapping.push(recordMapping);
				}
			});

			return emailMapping;
		});
	};

	GenerateMergeReportViewModel.prototype.getPostData = async function()
	{
		var data = {
			SpecificRecordIds: this.obEntityDataModel().selectedRecordIds(),
			FilterId: this.obFilter() ? this.obFilter().id() : null,
			DatabaseId: this.selectedDatasource().DBID
		};

		var isMergeDocumentGridEntity = this.Document && !this.Document.DataTypeId;
		if (this.Document && !isMergeDocumentGridEntity)
		{
			if (this.Document.CellWidth)
			{
				const cellWidth = this.Document.CellWidth;
				const cellPadding = this.Document.CellPadding || 0;
				const adjustedWidthRate = (cellWidth - cellPadding) / cellWidth;
				this.Document.Content = this.Document.Content?.replace('"><tbody>', `width: ${Math.floor(adjustedWidthRate * 100)}%;"><tbody>`);
			}
			data.Document = this.Document;
		}
		else
		{
			data.DocumentId = this.Id;
		}

		if (this.dataType === 'contact')
		{
			data.EmailMapping = await this.getContactDataTypeEmailMapping();
		}
		else
		{
			data.EmailMapping = await this.getEmailMapping();
		}
		this.getEmailUDFsMapping(data.EmailMapping);

		return data;
	};

	GenerateMergeReportViewModel.prototype.applySendEmailBatch = async function()
	{
		var self = this,
			data = await self.getPostData(),
			summary = null;
		if (self.isAllRecordWithoutContact(data.EmailMapping))
		{
			return self.pageLevelViewModel.popupErrorMessage("At least one recipient is required(To, Cc or Bcc).")
				.then(function()
				{
					return false;
				});
		}

		let selectedRecordContactIds;
		if (this.dataType === 'contact')
		{
			selectedRecordContactIds = data.EmailMapping.filter(item => !item.IsEmailUDF).map(item => item.RecordId);
		}
		else
		{
			selectedRecordContactIds = self.recordContactListMoverViewModel().selectedData.map(function(rc) { return rc.Id; })
		}

		let selectedEmailUDFs = this.emailUDFListMoverViewModel().selectedData;

		self.postData = data;
		self.modalData = {
			destinationType: self.destinationType(),
			selectedDatasource: self.selectedDatasource(),
			selectedSpecifyRecordOption: self.obSpecifyRecordOption(),
			selectedSpecificRecords: self.obSelectedSpecificRecord(),
			selectedRecordIds: self.obEntityDataModel().selectedRecordIds(),
			selectedRecordContactRecords: self.recordContactListMoverViewModel().selectedData,
			selectedRecordContactIds: selectedRecordContactIds,
			selectedEmailUDFs: selectedEmailUDFs
		}
		summary = {
			postData: self.postData,
			modalData: self.modalData
		};
		return summary;
	};

	GenerateMergeReportViewModel.prototype.sendEmail = async function()
	{
		var self = this;
		var data = await self.getPostData();
		if (self.isAllRecordWithoutContact(data.EmailMapping))
		{
			return self.pageLevelViewModel.popupErrorMessage("At least one recipient is required(To, Cc or Bcc).")
				.then(function()
				{
					return false;
				});
		}

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocumentemails"), {
			data: data
		}).then(function(response)
		{
			return tf.modalManager.showModal(new TF.Modal.ResultModalViewModel({ title: "Send Email Result", content: response.Items[0].Message }));
		}).catch(function()
		{
			return tf.promiseBootbox.alert("The email could not be sent.", "Unable to Send Email")
				.then(function()
				{
					return false;
				});
		});
	};

	GenerateMergeReportViewModel.prototype.generateMergeDocument = function()
	{
		var windowName = "_generateMergeDocumentlatest" + Math.random().toString(36).substring(2, 15);
		var win = window.open(location.origin + location.pathname + "loading.html", windowName);
		return this.postMergeDocumentRuntime().then(function(data)
		{
			var key = data.Items[0];
			var path = pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocumentruntimes", key) + "?@VendorId=Transfinder";
			window.open(path, windowName);
			return true;
		}.bind(this))
			.catch(function(e)
			{
				win.close();
				this.pageLevelViewModel.popupErrorMessage("mergedocumentruntimes cannot be executed for the following error: \n" + e.ExceptionType);
			}.bind(this));
	};

	GenerateMergeReportViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			let data = {
				destinationType: this.destinationType(),
				selectedDatasource: this.selectedDatasource().DBID,
				selectedSpecifyRecordOption: this.obSpecifyRecordOption().id,
				selectedSpecificRecords: this.obSelectedSpecificRecord().map(a => a.Id),
				selectedEmailUDFs: this.emailUDFListMoverViewModel().selectedData.map(a => a.Id),
				filterId: this.obFilter()?.id(),
				checkedUDFs: this.obUDFEmails().filter(i => i.obFieldChecked()).map(i => i.DisplayName)
			}
			if (this.dataType !== 'contact')
			{
				data.selectedRecordContactIds = this.recordContactListMoverViewModel().selectedData;
			}

			if (this.isDirty(this.backupEntity, data))
			{
				resolve(tf.promiseBootbox.yesNo("Are you sure you want to cancel?", "Unsaved Changes"));
			}
			else
			{
				resolve(true);
			}
		}.bind(this));
	};

	GenerateMergeReportViewModel.prototype.isDirty = function(backupData, data)
	{
		let valueEquals = (value1, value2) => { return value1 === value2; };
		let arrayEquals = (array1, array2) =>
		{
			if (array1.length !== array2.length)
			{
				return false;
			}
			return JSON.stringify(array1.sort()) === JSON.stringify(array2.sort());
		};

		for (let key in backupData)
		{
			let isDirty;
			if (Array.isArray(backupData[key]))
			{
				isDirty = !arrayEquals(backupData[key], data[key]);
			}
			else
			{
				isDirty = !valueEquals(backupData[key], data[key]);
			}
			if (isDirty)
			{
				return true;
			}
		}
		return false;
	}

	GenerateMergeReportViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

	GenerateMergeReportViewModel.prototype.onClickSendAs = function()
	{
		var self = this;
		const newDestinationType = self.destinationType();
		if (self.originalDestinationType === newDestinationType)
		{
			return;
		}

		if (newDestinationType !== "Bcc")
		{
			tf.promiseBootbox.confirm({
				message: "We recommend you send this as a blind carbon copy (Bcc) email to avoid disclosing email addresses publicly. Are you sure you want to Send As \"" + newDestinationType + "\"?",
				title: "Confirmation"
			})
				.then(function(result)
				{
					if (!result)
					{
						self.destinationType(self.originalDestinationType);
					} else
					{
						self.originalDestinationType = newDestinationType;
						$("#destinationType" + newDestinationType).prop("checked", true);
					}
				});
		}
		else
		{
			self.originalDestinationType = newDestinationType;
			setTimeout(() => $("#destinationType" + newDestinationType).prop("checked", true));
		}
	};

	GenerateMergeReportViewModel.prototype.isAllRecordWithoutContact = function(emailMappingArray)
	{
		var self = this;
		var noneValid = false;
		if (emailMappingArray.length > 0)
		{
			var invalidRecords = emailMappingArray.filter(checkCount);

			noneValid = (invalidRecords.length == emailMappingArray.length);
		}
		else
		{
			var to = self.to(), cc = self.cc(), bcc = self.bcc();
			var toList = TF.testEmail(to).emailList, ccList = TF.testEmail(cc).emailList, bccList = TF.testEmail(bcc).emailList;
			noneValid = (toList.length + ccList.length + bccList.length == 0);
		}

		return noneValid;
	};

	function checkCount(oneEmail)
	{
		return TF.testEmail(oneEmail.To).emailList.length == 0 && TF.testEmail(oneEmail.Cc).emailList.length == 0 && TF.testEmail(oneEmail.Bcc).emailList.length == 0;
	}

	GenerateMergeReportViewModel.prototype.removeBootstrapValidationError = function()
	{
		this._$form && this._$form.find("small[data-bv-result='INVALID']").attr("data-bv-result", "NOT_VALIDATED").css("display", "none");
	}

	GenerateMergeReportViewModel.prototype.getIdsByFilter = function()
	{
		var self = this;
		var url = tf.dataTypeHelper.getSearchUrl(this.selectedDatasource().DBID, this.dataType)
		if (this.obFilter())
		{
			return tf.promiseAjax.post(`${url}/id`, {
				data: {
					sortItem: [],
					idFilter: {},
					filterSet: null,
					filterClause: this.obFilter().whereClause(),
					isQuickSearch: false,
					fields: []
				},
			}).then(function(result)
			{
				self.obEntityDataModel().selectedRecordIds(result.Items);
				//first time load a merge document with filter.
				if (self.options.FilterId && self.isInit)
				{
					self.isInit = false;
				}
			});
		}
	}
})();
