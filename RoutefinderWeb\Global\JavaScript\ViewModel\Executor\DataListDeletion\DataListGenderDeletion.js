﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListGenderDeletion = DataListGenderDeletion;

	function DataListGenderDeletion()
	{
		this.type = 'genders';
		this.deleteType = 'Gender';
		this.deleteRecordName = 'Gender';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListGenderDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListGenderDeletion.prototype.constructor = DataListGenderDeletion;

	DataListGenderDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
	DataListGenderDeletion.prototype.getAssociatedData = function(ids)
	{
		const self = this;

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `genders/${ids[0]}/status`))
			.then(function(response)
			{
				if (response && response.InUse)
				{
					return [{
						type: 'genders',
						items: [1]
					}];
				}

				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `genders`))
					.then(function(response)
					{
						const genders = response.Items;
						const isFeedToGender = genders.findIndex(g => ids.includes(g.FeedTo)) >= 0;
						if (!isFeedToGender)
						{
							return []; // Next step would check the response is array OR not, if it is empty array, it would prompt a delete confirm message.
						}

						var confirmMessage = "This gender feeds to another genders, are you sure you want to delete it?";
						return tf.promiseBootbox.yesNo(confirmMessage, "Delete Confirmation")
							.then(function(ans)
							{
								//if confirm no , delete nothing
								if (!ans)
								{
									self.deleteIds = [];
								}

								return; // Next step would check the response is array OR not, if it is undefined, it would prompt nothing.
							});
					});
			});
	};
})();