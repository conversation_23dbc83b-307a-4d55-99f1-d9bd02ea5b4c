﻿(function()
{
	createNamespace('TF.Control').EditCalendarEventsTypeViewModel = EditCalendarEventsTypeViewModel;

	function EditCalendarEventsTypeViewModel(fieldName, id)
	{
		this.fieldName = fieldName;
		this._isSaving = false;
		this.obName = ko.observable();
		this.obDescriptionVisible = ko.observable(true);
		this.obDescription = ko.observable("Description");
		this.obDescriptionRequired = ko.observable(false);
		this.entityDataModel = TF.DataModel.CalendarEventTypeDataModel;
		this.obEditEnabled = ko.observable(true);
		this.obName("Type");
		let entityData = new this.entityDataModel();
		this.obEntityDataModel = ko.observable(entityData);
		this.obEntityDataModel().id(id);
		this.obEntityDataModel().apiIsDirty(false);
		this.entitySubscriptions = [];
		this.subscribeEvents(entityData);
		this.pageLevelViewModel = new TF.PageLevel.AddTwoFieldsPageViewModel();
		this.obEntityDataModel.subscribe(() => this.subscribeEvents(this.obEntityDataModel()));
	}

	EditCalendarEventsTypeViewModel.prototype.save = function()
	{
		if (this._isSaving)
		{
			return Promise.resolve(false);
		}

		this._isSaving = true;
		return this.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(result)
			{
				if (result)
				{
					let isNew = this.obEntityDataModel().id() ? false : true;

					let calendarEventTypeData = this.obEntityDataModel().toData();
					let inSession = false, closed = false;
					if (calendarEventTypeData.Insession !== undefined)
					{
						inSession = calendarEventTypeData.Insession;
						closed = !inSession;
					}

					return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName),
						{
							data: [{
								ID: isNew ? 0 : this.obEntityDataModel().id(),
								Description: calendarEventTypeData.Description,
								System: calendarEventTypeData.System,
								Insession: inSession,
								Closed: closed,
								Color: calendarEventTypeData.Color
							}]
						})
						.then(function(data)
						{
							this._isSaving = false;
							PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
							return data.Items[0];
						}.bind(this))
						.catch(function()
						{
							this._isSaving = false;
							return;
						}.bind(this));
				}
				this._isSaving = false;
				return;
			}.bind(this))
			.catch(function()
			{
				this._isSaving = false;
				return;
			}.bind(this));
	}

	EditCalendarEventsTypeViewModel.prototype.afterRender = function(el)
	{
		var fieldName = this.fieldName;
		this.$form = $(el);
		var validatorFields = {}, isValidating = false, self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.name = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Type is required"
				},
				callback: {
					message: "Type must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), fieldName), {
							paramData: {
								"@filter": "eq(Description," + value + ")",
								"@fields": "EventTypeID"
							}
						}, { overlay: false })
							.then(function(apiResponse)
							{
								return !(apiResponse.Items[0] && apiResponse.Items[0].ID && apiResponse.Items[0].ID != self.obEntityDataModel().id());
							});
					}.bind(this)
				}
			}
		}

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.$form.find("input[name=name]").focus();
		this.load();

		this.LimitInput();
	};

	EditCalendarEventsTypeViewModel.prototype.load = function()
	{
		if (this.obEntityDataModel().id() != null)
		{
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.fieldName),
				{
					paramData: {
						ID: this.obEntityDataModel().id()
					}
				})
				.then((data) =>
				{
					var item = data.Items[0];
					if (!item.Insession && !item.Closed)
					{
						item.Insession = undefined;
						item.Closed = undefined;
					}
					let entityData = new this.entityDataModel(item);
					this.subscribeEvents(entityData);
					this.obEntityDataModel(entityData);
					if (item.System)
					{
						this.obEditEnabled(false);
					}
				});
		}

		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	};

	EditCalendarEventsTypeViewModel.prototype.apply = function()
	{
		return this.save()
			.then(function(data)
			{
				return data;
			}, function()
			{
			});
	};

	EditCalendarEventsTypeViewModel.prototype.LimitInput = function()
	{
		switch (this.fieldName)
		{
			case 'calendareventtypes':
				var $name = this.$form.find("input[name=name]");
				$name.attr("maxlength", 200);
				break;
		}
	};

	EditCalendarEventsTypeViewModel.prototype.subscribeEvents = function(entity)
	{
		this.disposeEntitySubscriptions();
		if (!entity)
		{
			return;
		}

		this.entitySubscriptions = [
			entity.closed.subscribe((v) =>
			{
				if (v)
				{
					this.obEntityDataModel().insession(!v);
				}
			}),
			entity.insession.subscribe((v) =>
			{
				if (v)
				{
					this.obEntityDataModel().closed(!v);
				}
			})
		];
	};

	EditCalendarEventsTypeViewModel.prototype.dispose = function()
	{
		this.disposeEntitySubscriptions();
		this.pageLevelViewModel.dispose();
	};

	EditCalendarEventsTypeViewModel.prototype.disposeEntitySubscriptions = function()
	{
		this.entitySubscriptions.forEach(i => i.dispose());
		this.entitySubscriptions = [];
	};
})();

