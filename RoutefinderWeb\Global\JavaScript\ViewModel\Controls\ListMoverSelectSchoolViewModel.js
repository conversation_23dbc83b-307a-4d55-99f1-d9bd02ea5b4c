(function()
{
	createNamespace('TF.Control').ListMoverSelectSchoolViewModel = ListMoverSelectSchoolViewModel;

	function ListMoverSelectSchoolViewModel(selectedData, options)
	{
		options.getUrl = function()
		{
			return pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint("school"));
		};

		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
	}

	ListMoverSelectSchoolViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectSchoolViewModel.prototype.constructor = ListMoverSelectSchoolViewModel;

	ListMoverSelectSchoolViewModel.prototype.columnSources = {
		school: [
			{
				FieldName: 'Name',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('Name'),
				Width: '150px',
				type: 'string'//,
				// isSortItem: true
			},
			{
				FieldName: 'School',
				DisplayName: 'Code',
				Width: '120px',
				type: 'string'//,
				// isSortItem: true
			},
			{
				FieldName: 'GradeRange',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('Grade'),
				Width: '60px',
				type: 'string'//,
				// isSortItem: true
			}
		]
	};

	ListMoverSelectSchoolViewModel.prototype.getFields = function()
	{
		return this.columns.map((item) => item.FieldName).concat(['Id', "ArrivalTime", "DepartTime"]);
	};

	ListMoverSelectSchoolViewModel.prototype.initGridScrollBar = function(container)
	{//need check soon
		var $gridContent = container.find(".k-grid-content");
		$gridContent.css({
			"overflow-y": "auto"
		});

		if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
		{
			$gridContent.find("colgroup col:last").css({
				width: 117
			});
		}
		else
		{
			$gridContent.find("colgroup col:last").css({
				width: 100
			});
		}
	};

	ListMoverSelectSchoolViewModel.prototype.setRightRequestOption = function(requestOptions)
	{
		requestOptions = TF.Control.KendoListMoverWithSearchControlViewModel.prototype.setRightRequestOption.call(this, requestOptions);
		// requestOptions.paramData.time = toISOStringWithoutTimeZone(moment().currentTimeZoneTime());
		return requestOptions;
	};

	ListMoverSelectSchoolViewModel.prototype.apply = function()
	{
		return TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this).then(function(selectedData)
		{
			return selectedData;
		});
	};

	ListMoverSelectSchoolViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			resolve(false);
		});
	};
})();
