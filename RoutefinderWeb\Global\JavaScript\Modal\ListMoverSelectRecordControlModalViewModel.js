﻿(function()
{
	createNamespace('TF.Modal').ListMoverSelectRecordControlModalViewModel = ListMoverSelectRecordControlModalViewModel;

	function ListMoverSelectRecordControlModalViewModel(selectedData, options)
	{
		options.displayCheckbox = false;
		if (options.showRemoveColumnButton === undefined)
		{
			options.showRemoveColumnButton = true;
		}
		options.contentTemplate = "modal/ListMoverSelectRecordControl";
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.inheritChildrenShortCutKey = false;
		this.ListMoverSelectRecordControlViewModel = new TF.Control.ListMoverSelectRecordControlViewModel(selectedData, options);
		this.data(this.ListMoverSelectRecordControlViewModel);
		this.data().isWidget = ko.observable(false);
	}

	ListMoverSelectRecordControlModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectRecordControlModalViewModel.prototype.constructor = ListMoverSelectRecordControlModalViewModel;

	ListMoverSelectRecordControlModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverSelectRecordControlViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this)).catch(function() { });
	};

	ListMoverSelectRecordControlModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverSelectRecordControlViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
			else
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};

})();
