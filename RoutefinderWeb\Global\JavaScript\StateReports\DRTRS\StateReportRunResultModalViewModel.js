(function()
{
	createNamespace('TF.Modal').StateReportRunResultModalViewModel = StateReportRunResultModalViewModel;
	function StateReportRunResultModalViewModel(summaryOption)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.contentTemplate('modal/StateReport/StateReportRunResult');
		self.buttonTemplate('modal/positivenegative');
		self.sizeCss = "modal-dialog-sm";
		self.title("Run Result");
		self.resultViewModel = new TF.Control.StateReportRunResultViewModel(summaryOption);
		self.data(self.resultViewModel);
		self.obPositiveButtonLabel('Download Files');
		self.obCustomizeCss("auto-width");
		self.obNegativeButtonLabel('Close');
	}
	StateReportRunResultModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	StateReportRunResultModalViewModel.prototype.constructor = StateReportRunResultModalViewModel;
	StateReportRunResultModalViewModel.prototype.positiveClick = function(e, viewModel)
	{
		this.resultViewModel.downloadzip();
	}
})()