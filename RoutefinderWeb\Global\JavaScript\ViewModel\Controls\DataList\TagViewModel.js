(function()
{
	createNamespace('TF.Control').TagViewModel = TagViewModel;
	function TagViewModel(recordEntity)
	{
		this._isSaving = false;
		this.obEntityDataModel = ko.observable(new TF.DataModel.TagDataModel(recordEntity));
		this.availableSource = tf.dataTypeHelper.getAvailableDataTypes().filter(x => x.enableTag).map(x =>
		{
			return { value: x.id, text: x.label };
		});
		this.obSelectedDataTypeDisplay = ko.computed(this.computeDatatypeDisplay, this);
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	TagViewModel.prototype.computeDatatypeDisplay = function()
	{
		var dataTypes = this.obEntityDataModel().tagDataTypes();
		if (!dataTypes || !dataTypes.length)
		{
			return 'All';
		}
		return dataTypes.map(x => this.availableSource.find(c => c.value == x.DataTypeId).text).join(', ');
	}

	TagViewModel.prototype.selectDataTypes = function()
	{
		var selectedDataTypeIds = (this.obEntityDataModel().tagDataTypes() || []).map(x => x.DataTypeId);
		var options = {
			resizable: true,
			modalWidth: 850,
			gridHeight: 400,
			title: "Data Types",
			allowNullValue: true,
			availableSource: this.availableSource.filter(c => !selectedDataTypeIds.includes(c.value)),
			selectedSource: this.availableSource.filter(c => selectedDataTypeIds.includes(c.value)),
		};

		tf.modalManager.showModal(new TF.DetailView.ListMoverFieldEditorModalViewModel(options)).then((result) =>
		{
			if (Array.isArray(result))
			{
				this.obEntityDataModel().tagDataTypes(this.availableSource.filter(c => result.includes(c.value)).map(c => ({ DataTypeId: c.value })));
			}
		});
	}

	TagViewModel.prototype.apply = async function()
	{
		if (this._isSaving)
		{
			return false;
		}

		this._isSaving = true;
		const validateResult = await this.pageLevelViewModel.saveValidate(null, { hideToast: true });
		if (validateResult)
		{
			const isNew = !this.obEntityDataModel().id();
			const record = this.obEntityDataModel().toData();
			const apiResponse = await tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), "tags?@relationships=TagDataTypes"),
				{ data: [record] }).catch((err) =>
				{
					if (err.Message)
					{
						tf.promiseBootbox.alert(err.Message);
					}
					return false;
				});
			this._isSaving = false;
			if (!apiResponse)
			{
				return false;
			}
			this.updateTagCache();
			return apiResponse.Items[0];
		}

		this._isSaving = false;
		return validateResult;
	}

	TagViewModel.prototype.updateTagCache = function()
	{
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tags?@relationships=TagDataTypes")).then(function(response)
		{
			tf.tags = response.Items;
		})
	}

	TagViewModel.prototype.afterRender = function(element)
	{
		this.$form = $(element);
		const validatorFields = {}, self = this;
		let isValidating = false;

		validatorFields.name = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Name is required"
				},
				regexp: {
					regexp: "^[^,]*$",
					message: "Name should not include comma"
				},
				callback: {
					message: "Name must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							return true;
						}

						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tags"), {
							paramData: {
								"@filter": `eq(Name,${value})&ne(Id,${self.obEntityDataModel().id()})`,
								"@fields": "Id,Name"
							}
						}, { overlay: false }).then(apiResponse => apiResponse?.Items?.length === 0);
					}
				}
			}
		}

		$(element).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.pageLevelViewModel.load($(element).data("bootstrapValidator"));
		this.$form.find("input[name=name]").focus();
	}

	TagViewModel.prototype.dispose = function()
	{
		tfdispose(this);
	}
})();
