(function()
{
	var ns = createNamespace("TF.ImportAndMergeData");

	ns.ImportAndMergeDataModel = ImportAndMergeDataModel;

	var ImportAndMergeDataType = ns.ImportAndMergeDataType = {
		PredefinedImport: 0,
		TransfinderDataSource: 1,
		ExternalSource: 2
	};

	var ImportAndMergeOperationType = ns.ImportAndMergeOperationType = {
		Back: 0,
		Next: 1
	};

	var LookupAction = ns.LookupAction = TF.createEnum([
		"Create If Not Found",
		"Ignore If Not Found",
		"Reject If Not Found"
	], 1);

	const dataTypes = [
		"Alternate Site",
		"Contractor",
		"District",
		"Field Trip",
		"Field Trip Account",
		"Field Trip Activity",
		"Field Trip Billing Classification",
		"Field Trip Classification",
		"Field Trip Destination",
		"Field Trip Equipment",
		"Field Trip Template",
		"Filter",
		"Geo Region",
		"Geo Region Type",
		"Redistrict",
		"School",
		"Staff",
		"$Mailing City",
		"$Mailing Postal Code",
		"Student",
		"Student Disability Codes",
		"Student Ethnic Codes",
		"Route",
		"Trip",
		"Trip Alias",
		"Vehicle",
		"Vehicle Body Type",
		"Vehicle Brake Type",
		"Vehicle Category",
		"Vehicle Equipment",
		"Vehicle Fuel Type",
		"Vehicle Make",
		"Vehicle Make Of Body",
		"Vehicle Model",
		"$District Department",
		"$StopPoolCategory",
		"$MAP_RSPOOLPT",
		"$MAP_RSPOOLBY",
		"$TripStop",
		"$MAP_TRIPPATH",
		"$MAP_TRIPSTOPBOUNDARY",
		"$SchoolLocation",
		"$SchoolGrade",
		"$MAP_NEZ",
		"$MAP_GEOREGIONPOLYGON",
		"$StudentRequirement",
		"Contact",
		"$RecordContact",
		"$Document",
		"$DocumentRelationShip",
		"$RecordPicture",
		"$Grade",
		"$StudentRequirementDays",
		"$StudentSchedule",
		"$Gender",
		"$MailingState",
		"Address Point",
		"$Tag",
		"Field Trip Travel Scenario",
	];

	const dataTypeWithId = { "Address Point": 65, "$Tag": 66, "Field Trip Travel Scenario": 67};
	var ImportDataType = ns.ImportDataType = TF.createEnum(dataTypes, Array.from(dataTypes, (oldVal, i) => dataTypeWithId[oldVal] ?? i));
	ns.avaliableDataTypes = ImportDataType.all.filter(function(i) { return i.name[0] != "$" }).sort(function(a, b) { return a.displayName > b.displayName ? 1 : -1; });

	function ImportAndMergeDataModel()
	{
		this.type = ko.observable(ImportAndMergeDataType.ExternalSource);
		this.performArchive = ko.observable(true);

		this.targetDBID = tf.datasourceManager.databaseId;
		this.tableConfigs = [this.createTableConfig()];

		// Existing TF data source
		this.sourceDBID = ko.observable();

		// Predefined import
		this.predefinedData = ko.observable();

		// External Source
		this.externalSourceDataPath = ko.observable();
		this.externalSourceDataFile = ko.observable();
		this.externalSourceDataFile.subscribe(function()
		{
			this.externalSourceDataPath((this.externalSourceDataFile() || {}).name || "");
			resolveTableConfig(this.tableConfigs[0]);
		}, this);

		this.externalSourceTemplatePath = ko.observable();
		this.externalSourceTemplateFile = ko.observable();
		this.externalSourceTemplateFile.subscribe(function()
		{
			this.externalSourceTemplatePath((this.externalSourceTemplateFile() || {}).name || "");
			resolveTableConfig(this.tableConfigs[0]);
		}, this);

		this.selectedExternalTableName = ko.observable();
		this.externalSource = null;
		this.tfxData = ko.observable();

		function resolveTableConfig(tableConfig)
		{
			if (!tableConfig)
			{
				return;
			}

			tableConfig.columnConfigs = tableConfig.columnConfigs.filter(function(c)
			{
				return !!c.Source;
			});
		}
	}

	ImportAndMergeDataModel.prototype.createTableConfig = function()
	{
		return {
			dataType: ko.observable(ImportDataType.AlternateSite),
			columnConfigs: [],
			isUpdateExisting: ko.observable(false),
			isDeleteNonexisting: ko.observable(false),
			includeIds: [],
			excludeIds: [],
			needFindSchedule: ko.observable(false),
			useStopPool: ko.observable(false),
			stopPoolId: ko.observable(),
			createDoorToDoor: ko.observable(false),
			includeStopPool: ko.observable(false),
			needResetLoadTimes: ko.observable(false),
			needFindSchoolResidence: ko.observable(false),
			residenceIds: ko.observableArray([]),
			isGeocode: ko.observable(false),
			geocodeSource: ko.observable('Street Address Range'),
			needFindPopulationRegion: ko.observable(false),
			ungeocodeClearsSchoolOfResidence: ko.observable(false),
			doNotAddCardEndDates: ko.observable(false),
		};
	};

	ImportAndMergeDataModel.prototype.getDataView = function(tableName, maxCount)
	{
		var self = this;
		if (self.type() == ImportAndMergeDataType.ExternalSource)
		{
			var sheet = self.externalSource.Sheets[tableName];
			if (!sheet)
			{
				return {};
			}

			return TF.getSheetInfo(sheet, maxCount, true);
		}

		return {};
	};

	ImportAndMergeDataModel.prototype.readExternalExcel = function(workbook)
	{
		this.externalSource = workbook;
		this.selectedExternalTableName(workbook.SheetNames[0]);
	};

	ImportAndMergeDataModel.prototype.readTFDAndTFX = function(data)
	{
		this.predefinedData(data);
	}

	ImportAndMergeDataModel.prototype.readTemplateFile = function(xmlDoc)
	{
		var self = this, doc = $(xmlDoc), tablespec = doc.find('tablespec');
		if (!tablespec.length)
		{
			return false;
		}

		var name = tablespec.attr("name");
		if (!name)
		{
			return false;
		}

		var dataType = ImportDataType.find(name);
		if (dataType == null)
		{
			return false;
		}

		var tableConfig = self.createTableConfig();
		self.tableConfigs = [tableConfig];
		tableConfig.dataType(dataType.id);
		var fieldspecs = tablespec.find("fieldspec");
		fieldspecs.each(function()
		{
			var fieldspec = $(this),
				fieldspecName = fieldspec.attr("name").toLowerCase(),
				sourcefield = fieldspec.find("sourcefield").text(),
				matchon = (fieldspec.find("matchon").text() || "").toLowerCase() == "true",
				fkmatchfield = fieldspec.find("fkmatchfield").text(),
				fkmatchaction = parseInt(fieldspec.find("fkmatchaction").text()) || 0;

			if (fieldspecName)
			{
				tableConfig.columnConfigs.push({ Target: fieldspecName, Source: sourcefield, MatchOn: matchon, LookupField: fkmatchfield, LookupAction: fkmatchaction });
			}
		});

		return true;
	};

	ImportAndMergeDataModel.prototype.toData = function()
	{
		var externalData = [];
		// TODO: now importing TFDB is using a different model. Should make it use current model
		if (this.type() == ImportAndMergeDataType.TransfinderDataSource)
		{
			return this.tfdbImportData;
		}

		if (this.type() == ImportAndMergeDataType.ExternalSource)
		{
			var dataView = this.getDataView(this.selectedExternalTableName());
			externalData = dataView.rows;
		}

		var tableConfigs = this.tableConfigs.map(function(t)
		{
			t.columnConfigs = t.columnConfigs.filter(function(i) { return !!i.Source });
			return ko.mapping.toJS(t);
		})

		return {
			TargetDBID: this.targetDBID,
			Type: this.type(),
			SourceDBID: this.sourceDBID(),
			TableConfigs: tableConfigs,
			ExternalData: externalData,
		};
	};

	ImportAndMergeDataModel.prototype.saveTemplate = function()
	{
		var fieldspecs = [],
			fields = { $tag: "fields", $children: fieldspecs },
			tableConfig = this.tableConfigs[0],
			tablespec = { $tag: "tablespec", $children: [fields], name: TF.ImportAndMergeData.ImportDataType.findById(tableConfig.dataType()).name },
			template = { $tag: "tfimporttemplate", $children: [tablespec] };
		tableConfig.columnConfigs.forEach(function(item)
		{
			if (item.Source || item.MatchOn || item.LookupField || item.LookupAction)
			{
				var sourcefield = { $tag: "sourcefield", $text: (item.Source || "").toUpperCase() },
					matchon = { $tag: "matchon", $text: item.MatchOn ? "True" : "False" },
					fkmatchfield = { $tag: "fkmatchfield", $text: item.LookupField || "" },
					fkmatchaction = { $tag: "fkmatchaction", $text: item.LookupAction || 0 },
					fieldspec = { $tag: "fieldspec", name: item.Target.toLowerCase(), $children: [sourcefield, matchon, fkmatchfield, fkmatchaction] };
				fieldspecs.push(fieldspec);
			}
		});

		return template;
	};
})();