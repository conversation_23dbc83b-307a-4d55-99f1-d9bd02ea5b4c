(function()
{
	createNamespace("TF.Modal").FormEmailRuleSelectRecipientsModalViewModel = FormEmailRuleSelectRecipientsModalViewModel;

	function FormEmailRuleSelectRecipientsModalViewModel(selectedData, formConfig)
	{
		var self = this,
			defaultOptions = {
				'title': 'Select Recipients',
				'contentTemplate': "modal/FormEmailRuleSelectRecipients",
				'type': 'user',
				'modalViewModel': self
			};
		options = $.extend({}, defaultOptions);
		TF.Modal.ListMoverSelectRecipientControlModalViewModel.call(self, selectedData, options);
		self.formEmailRuleSelectRecipientsViewModel = new TF.Control.FormEmailRuleSelectRecipientsViewModel(selectedData, options, formConfig);
		self.data(self.formEmailRuleSelectRecipientsViewModel);
	};

	FormEmailRuleSelectRecipientsModalViewModel.prototype = Object.create(TF.Modal.ListMoverSelectRecipientControlModalViewModel.prototype);
	FormEmailRuleSelectRecipientsModalViewModel.prototype.constructor = FormEmailRuleSelectRecipientsModalViewModel;

	FormEmailRuleSelectRecipientsModalViewModel.prototype.positiveClick = function()
	{
		var self = this;

		self.data().apply().then((result) =>
		{
			if (result)
			{
				this.positiveClose(result);
			}
		});
	};

	FormEmailRuleSelectRecipientsModalViewModel.prototype.negativeClick = function()
	{
		this.data().cancel().then((result) =>
		{
			if (result)
			{
				this.negativeClose();
			}
		}).catch(() => null);
	};

	FormEmailRuleSelectRecipientsModalViewModel.prototype.dispose = function()
	{
		this.formEmailRuleSelectRecipientsViewModel.dispose();
	};
})();