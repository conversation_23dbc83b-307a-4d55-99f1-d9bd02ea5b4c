﻿(function()
{
	createNamespace("TF").EntStorageManager = EntStorageManager;

	function EntStorageManager()
	{
		this.prefix = "ent.";
		this.storageManager = new TF.StorageManager("ent");
	};

	EntStorageManager.prototype.save = function(key, value)
	{
		var self = this;
		if (TF.useEntToken)
		{
			self.storageManager.save(key, value, true);
		}
		else
		{
			tf.storageManager.save(key, value, true);
		}
	};

	EntStorageManager.prototype.get = function(key)
	{
		var self = this;
		if (TF.useEntToken)
		{
			return self.storageManager.get(key, true);
		}
		else
		{
			return tf.storageManager.get(key, true);
		}
	};
})();
