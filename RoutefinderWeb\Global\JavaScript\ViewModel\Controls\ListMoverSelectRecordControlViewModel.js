﻿(function()
{
	createNamespace('TF.Control').ListMoverSelectRecordControlViewModel = ListMoverSelectRecordControlViewModel;
	function ListMoverSelectRecordControlViewModel(selectedData, options)
	{
		options.getUrl = function(gridType, options)
		{
			var prefix = tf.api.apiPrefix();
			if (options.dataSource)
			{
				prefix = pathCombine(tf.api.apiPrefixWithoutDatabase(), options.dataSource);
			}

			if (gridType === "mapincident")
			{
				prefix = tf.api.apiPrefixWithoutDatabase();
			}

			if (options.endpoint)
			{
				return pathCombine(prefix, options.serverPaging ? "search" : "", options.endpoint);
			}
			//if (gridType === "vehicle")
			//{
			//	return pathCombine(tf.api.apiPrefix(), "vehicle", "vehiclegrid");
			//} else
			//{
			return pathCombine(prefix, "search", tf.dataTypeHelper.getEndpoint(gridType));
			//}
		};

		if (options.repeatStudent)
		{
			this.columnSources['student'] = options.nameColumns;
		}

		if (options.serverPaging === undefined)
		{
			options.serverPaging = true;
		}

		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
		this.pageLevelViewModel = new TF.PageLevel.ListMoverPageLevelViewModel(this);
		ko.computed(function()
		{
			if (this.obSelectedData().length > 0)
			{
				this.pageLevelViewModel.obValidationErrorsSpecifed([]);
			}
		}, this);

		this.udfHelper = new TF.DetailView.UserDefinedFieldHelper();
	}

	ListMoverSelectRecordControlViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectRecordControlViewModel.prototype.constructor = ListMoverSelectRecordControlViewModel;

	ListMoverSelectRecordControlViewModel.prototype.columnSources = {
		student: [
			{
				FieldName: "FullName",
				DisplayName: "Name",
				Width: "120px",
				type: "string",
				isSortItem: true
			}
		],
		school: [
			{
				FieldName: "School",
				DisplayName: "Code",
				Width: "100px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "Name",
				DisplayName: "Name",
				Width: "260px",
				type: "string"

			}
		],
		altsite: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		contractor: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		district: [
			{
				FieldName: "District",
				Width: "180px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		fieldtrip: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		staff: [
			{
				FieldName: "FullName",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		trip: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		route: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		vehicle: [
			{
				FieldName: "BusNum",
				DisplayName: "BusNum",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		busfinderDriver: [
			{
				FieldName: "DriverName",
				DisplayName: "Driver",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		busfinderVehicle: [
			{
				FieldName: "ExternalName",
				DisplayName: "Vehicle",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		georegion: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		tripstop: [
			{
				FieldName: "Name",
				DisplayName: "Trip Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "Street",
				DisplayName: "Street",
				Width: "260px",
				type: "string"
			}
		],
		contact: [
			{
				FieldName: "FirstName",
				DisplayName: "First Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "LastName",
				DisplayName: "Last Name",
				Width: "180px",
				type: "string"
			}
		],
		recordcontact: [
			{
				FieldName: "ContactName",
				DisplayName: "Name",
				Width: "120px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "ContactTypeName",
				DisplayName: "Contact Type",
				Width: "100px",
				type: "string"
			}, {
				FieldName: "AssociatedRecord",
				DisplayName: "Associated Record",
				Width: "120px",
				type: "string"
			}
		],
		nez: [
			{
				FieldName: "Name",
				DisplayName: "NEZ Name",
				Width: "150px",
				type: "string",
				isSortItem: true
			},
			{
				FieldName: "SchoolName",
				DisplayName: "School Name",
				Width: "150px",
				type: "string"
			},
			{
				FieldName: "Comments",
				DisplayName: "Comments",
				Width: '150px',
				type: "string"
			},
			{
				FieldName: "SchoolCode",
				DisplayName: "School Code",
				Width: "100px",
				type: "string"
			}
		],
		boundaryset: [
			{
				FieldName: "Name",
				Width: '180px',
				type: "string",
				isSortItem: true
			},
			{
				FieldName: "Schools",
				Width: '150px',
				type: "string",
				template: "#:  !data.Schools ? '<All Schools>' : tf.tripGridDefinition.gridDefinition().formatter(data.Schools)#"
			},
			{
				FieldName: "FilterName",
				DisplayName: "Filter",
				Width: '150px',
				type: "string",
			},
		],
		populationregion: [
			{
				FieldName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		mapincident: [
			{
				FieldName: "IncidentTypeName",
				DisplayName: "Incident Type",
				Width: "180px",
				type: "string",
				isSortItem: true,
			},
			{
				FieldName: "ActiveFrom",
				Width: "180px",
				type: "string",
				isSortItem: true,
				template: "#: utcToClientTimeZone(data.ActiveFrom).format(\"MM/DD/YYYY hh:mm A\")#",
			},
			{
				FieldName: "ActiveTo",
				Width: "180px",
				type: "string",
				isSortItem: true,
				template: "#: data.ActiveTo?utcToClientTimeZone(data.ActiveTo).format(\"MM/DD/YYYY hh:mm A\"):''#",
			},
			{
				FieldName: "Source",
				DisplayName: "Source",
				Width: "180px",
				type: "integer",
				isSortItem: true,
				template: "#: data.Source===1?'Routefinder PLUS':data.Source===2?'Wayfinder':''#",
			},
			{
				FieldName: "CreatedInDatasourceName",
				DisplayName: "Data Source",
				Width: "180px",
				type: "string",
				isSortItem: true,
			},
			{
				FieldName: "CreatedInTripName",
				DisplayName: "Trip Name",
				Width: "180px",
				type: "string",
				isSortItem: true,
			},
		]
	};

	ListMoverSelectRecordControlViewModel.prototype.getFields = function()
	{
		var fields = this.columns.map(function(item) { return item.FieldName; }).concat(['Id']);
		if (this.options.type === "staff")
		{
			fields = fields.concat(["StaffTypes"]);
		}

		if (this.options.type === "recordcontact")
		{
			fields = fields.concat(["RecordID"]);
			var contactEmailFieldStr = "ContactEmail";
			if (!fields.some(function(f) { return f === contactEmailFieldStr }))
			{
				fields = fields.concat([contactEmailFieldStr]);
			}
		}

		var nameColumns = this.getColumnSources(this.options.type, true);
		if (nameColumns)
		{
			nameColumns.forEach(function(item)
			{
				if (fields.indexOf(item.FieldName) < 0)
				{
					fields.push(item.FieldName);
				}
			});
		}

		return fields;
	};

	ListMoverSelectRecordControlViewModel.prototype.init = function(viewModel, el)
	{
		setTimeout(async function()
		{
			this.$form = $(el);
			this.availableColGridContainer = this.$form.find(".availablecolumngrid-container");
			this.selectedColGridContainer = this.$form.find(".selectedcolumngrid-container");
			if (!this.options.nosticky)
			{
				var stickyColumns = this.getCurrentSelectedColumns(this.options.type);
				if (stickyColumns)
				{
					this.columns = stickyColumns;
				}
			}

			// check udf availability. filter out of the columns if udf does not exist anymore.
			if (this.columns.some(i => i.UDFGuid))
			{
				let currentUDFs = await this.udfHelper.getCurrentDBUDFs(this.options.type).then((items) => items);
				const currentUDFGuids = currentUDFs.map((i) => i.Guid);
				this.columns = currentUDFs.length === 0 ?
					this.columns.filter(i => !i.UDFGuid) :
					this.columns.filter(i => !i.UDFGuid || currentUDFGuids.includes(i.UDFGuid));
			}

			this.columns.map(function(item)
			{
				if (item.FieldName == "RawImage")
				{
					item.template = function(arg)
					{
						var url = "data:image/jpeg;base64," + arg.RawImage;
						return '<img style="width:20px; height:20px;" src="' + url + '" class="img-circle"/>';
					};
				}
			});
			this.originalColumns = this.columns.map(function(item)
			{
				return $.extend(
					{}, item);
			});
			if (this.options.type === "recordcontact")
			{
				this.defaultColumns = [{
					DisplayName: "Associated Record",
					FieldName: "AssociatedRecord",
					Width: "120px",
					type: "string"
				}, {
					DisplayName: "Name",
					FieldName: "ContactName",
					Width: "120px",
					type: "string"
				}, {
					DisplayName: "Contact Email",
					FieldName: "ContactEmail",
					Width: "150px",
					type: "string"
				}];
			}

			this.getAllRecords().then(function()
			{
				this.initRightGrid();
				this.initLeftGrid();
				this.setDataSource();
				this.afterInit();
			}.bind(this));
			$(el).bootstrapValidator();
			this.pageLevelViewModel.load($(el).data("bootstrapValidator"));

		}.bind(this), 1000);
	};

	ListMoverSelectRecordControlViewModel.prototype.getSelectDataCount = function()
	{
		return this.selectedData.length;
	};

	ListMoverSelectRecordControlViewModel.prototype.apply = function()
	{
		return TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this).then(function(selectedData)
		{
			if (this.options.mustSelect)
			{
				return this.pageLevelViewModel.saveValidate().then(function(result)
				{
					if (result)
					{
						return selectedData;
					}
					else
					{
						return null;
					}
				}.bind(this));
			}
			else
			{
				return selectedData;
			}
		}.bind(this));
	};

	ListMoverSelectRecordControlViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (!isArraySame(this.oldData, this.selectedData))
			{
				return tf.promiseBootbox.yesNo("You have unsaved changes.  Would you like to save your changes prior to canceling?", "Unsaved Changes").then(function(result)
				{
					if (result)
					{
						resolve(this.selectedData);
					}
					else
					{
						resolve(false);
					}
				}.bind(this));
			} else
			{
				resolve(true);
			}
		}.bind(this));
	};

	ListMoverSelectRecordControlViewModel.prototype.dispose = function()
	{
		TF.Control.KendoListMoverWithSearchControlViewModel.prototype.dispose.call(this);
		this.pageLevelViewModel.dispose();
	};

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var oldIds = oldData.map(function(item)
		{
			return item.Id;
		});
		var newIds = newData.map(function(item)
		{
			return item.Id;
		});
		var diffData1 = Enumerable.From(newIds).Where(function(x)
		{
			return !Array.contain(oldIds, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldIds).Where(function(x)
		{
			return !Array.contain(newIds, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();
