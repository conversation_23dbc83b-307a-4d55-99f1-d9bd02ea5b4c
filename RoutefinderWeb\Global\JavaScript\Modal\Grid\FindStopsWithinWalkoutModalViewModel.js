(function()
{
	createNamespace("TF.Modal.Grid").FindStopsWithinWalkoutModalViewModel = FindStopsWithinWalkoutModalViewModel;

	/**
	 * Constructor
	 * @returns {void} 
	 */
	function FindStopsWithinWalkoutModalViewModel(data)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.sizeCss = "modal-dialog-sm";
		self.title("Find Stops Within Walkout");
		self.contentTemplate("Modal/FindStopsWithinWalkout");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel("Apply");
		self.obNegativeButtonLabel("Close");

		self.model = new TF.Control.FindStopsWithinWalkoutViewModel(data);
		self.data(self.model);
	}

	FindStopsWithinWalkoutModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	FindStopsWithinWalkoutModalViewModel.prototype.constructor = FindStopsWithinWalkoutModalViewModel;

	FindStopsWithinWalkoutModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		var result = self.model.apply();
		self.positiveClose(result);
	};
})();