﻿(function()
{
	createNamespace('TF.Control').ReminderViewViewModel = ReminderViewViewModel;

	function ReminderViewViewModel(reminder, modalViewModel)
	{
		this.reminderId = reminder.Id;
		this.modalViewModel = modalViewModel;
		reminder.Name = "";// reminder.Name || reminder.FilterName;
		reminder.FilterID = reminder.FilterID <= 0 ? null : reminder.FilterID;
		reminder.NonEligibleZoneId = reminder.NonEligibleZoneId === 0 ? null : reminder.NonEligibleZoneId;
		this.emptyReminder = new TF.DataModel.ReminderDataModel(reminder);
		this.emptyReminder.userID(tf.authManager.authorizationInfo.authorizationTree.userId);
		this.obEntityDataModel = ko.observable(this.emptyReminder);
	}

	ReminderViewViewModel.prototype.init = function(model, element)
	{
		this.element = $(element);
		this.loadData();
		this.initValidation(element);
	};

	// load data
	ReminderViewViewModel.prototype.loadData = function()
	{
		if (this.reminderId > 0)
		{
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "reminders"), {
				paramData: {
					"@filter": String.format("eq(Id,{0})", this.reminderId)
				}
			}).then(function(response)
			{
				if (response && response.StatusCode === 404)
				{
					return Promise.reject(response);
				}
				if (response.Items.length == 1)
				{
					var item = response.Items[0];
					this.obEntityDataModel(new TF.DataModel.ReminderDataModel(item));
				} else
				{
					this.modalViewModel.title("New Reminder");
				}
			}.bind(this));
		}
	};

	// validation section
	ReminderViewViewModel.prototype.initValidation = function(el)
	{
		var self = this,
			validatorFields = {};
		this.$form = $(el);
		var isValidating = false;
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		setTimeout(function()
		{
			function updateErrors($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			}

			validatorFields.name = {
				trigger: "blur",
				validators:
				{
					notEmpty:
					{
						message: "required"
					},
					callback:
					{
						message: " must be unique",
						callback: function(value, validator, $field)
						{

							if (!value)
							{
								updateErrors($field, "unique");
								return true;
							}

							updateErrors($field, "required");

							return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "reminders"), {
								paramData: {
									Name: value,
									"@fields": "Id"
								}
							}, { overlay: false })
								.then(apiResponse =>
								{
									if (apiResponse.Items.length)
									{
										if (!self.reminderId)
										{
											return false;
										}

										if (apiResponse.Items[0].Id != self.reminderId)
										{
											return false;
										}
									}

									return true;
								});

						}.bind(this)
					}
				}
			};
			validatorFields.filterName = {
				trigger: "change blur",
				validators:
				{
					notEmpty:
					{
						message: "required"
					}
				}
			};
			validatorFields.dataTypeName = {
				trigger: "change",
				validators:
				{
					notEmpty:
					{
						message: "required"
					}
				}
			};
			$(el).bootstrapValidator(
				{
					excluded: [':hidden', ':not(:visible)'],
					live: 'enabled',
					message: 'This value is not valid',
					fields: validatorFields
				}).on('success.field.bv', function(e, data)
				{
					if (!isValidating)
					{
						isValidating = true;
						self.pageLevelViewModel.saveValidate(data.element);
						isValidating = false;
					}
				});

			this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
		}.bind(this));
	};

	ReminderViewViewModel.prototype.apply = function()
	{
		return this.pageLevelViewModel.saveValidate().then(valid =>
		{
			if (valid)
			{
				var entity = this.obEntityDataModel().toData();
				entity.GeoRegionIds = this.emptyReminder.geoRegionIds();
				return TF.ReminderHelper.save(entity);
			}
		});
	};

	ReminderViewViewModel.prototype.cancel = function()
	{
		this.pageLevelViewModel.clearError();
		return new Promise(function(resolve)
		{
			if (this.obEntityDataModel().apiIsDirty())
			{
				resolve(tf.promiseBootbox.yesNo("You have unsaved changes.  Would you like to save your changes prior to canceling?", "Unsaved Changes"));
			}
			else
			{
				resolve(false);
			}
		}.bind(this));
	};

	ReminderViewViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};
})();
