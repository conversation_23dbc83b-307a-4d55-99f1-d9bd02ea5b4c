(function()
{
	createNamespace("TF.Control").BaseEditImageViewModel = BaseEditImageViewModel;

	BaseEditImageViewModel.prototype = Object.create(TF.Control.BaseControl.prototype);
	BaseEditImageViewModel.prototype.constructor = BaseEditImageViewModel;

	function BaseEditImageViewModel()
	{
		this.obImageLabel = ko.observable("Photo");
		this.obImageFitMode = ko.observable();
		this.obIsPreviewModel = ko.observable(true);
		this.obEditable = ko.observable(false);
	}

	BaseEditImageViewModel.prototype.imageChange = function()
	{
		var files = this.$uploadedPhotoFileInput[0].files,
			url = window.URL || window.webkitURL,
			file,
			reader = new FileReader(),
			acceptFileExtensions = ['jpeg', 'jpg', 'ico', 'gif', 'svg', 'jfif', 'webp', 'bmp', 'png'],
			fileTypes = acceptFileExtensions.map(x =>
			{
				if (x === 'ico')
				{
					return 'image/x-icon';
				}
				else if (x === 'svg')
				{
					return 'image/svg+xml';
				}
				else
				{
					return `image/${x}`;
				}
			});


		if (files && files.length)
		{
			file = files[0];

			if (!fileTypes.includes(file.type))
			{
				this.oberrormessage(`Choose ${acceptFileExtensions.join(', ')}.`);
			}
			else if (file.size >= 1024 * 1024 * 10)
			{
				this.oberrormessage('File is too large');
			}
			else
			{
				this.oberrormessage(null);
				this.$overlay.show();
				reader.onload = function(event)
				{
					this.filePostData.fileName = file.name;
					this.filePostData.fileType = file.type;
					this.filePostData.fileData = event.target.result;

					this.$overlay.hide();
				}.bind(this);
				reader.readAsDataURL(file);
				this.imageChangeInner(file, url);
			}
		}
	};

	BaseEditImageViewModel.prototype.imageChangeInner = function(file, url)
	{
		var doNotRespectRatio = this.imageType == "udfimage";
		var blobUrl = url.createObjectURL(file);
		this.obEditable(true);
		this.$uploadedPhoto.attr('src', blobUrl).cropper(
			{
				background: false,
				guides: false,
				autoCrop: true,
				aspectRatio: doNotRespectRatio ? null : 1,
				minContainerWidth: this.imageWidth,
				preview: '.img-preview',
				autoCropArea: 0.8,
				crop: function(data)
				{
					$.extend(this.filePostData, data);
					this.imageUpdated = true;
				}.bind(this)
			}).cropper('replace', blobUrl);
		this.imageUpdated = true;
	};

	BaseEditImageViewModel.prototype.rotateLeft = function()
	{
		this.rotateImage(-90);
	};

	BaseEditImageViewModel.prototype.rotateRight = function()
	{
		this.rotateImage(90);
	};

	BaseEditImageViewModel.prototype.rotateImage = function(rotate)
	{
		if (this.$uploadedPhoto.data('cropper'))
		{
			this.$uploadedPhoto.cropper('rotate', rotate);
			this.imageUpdated = true;
		}
	};
})();

(function()
{
	createNamespace("TF.Control").EditPhotoViewModel = EditPhotoViewModel;

	EditPhotoViewModel.prototype = Object.create(TF.Control.BaseEditImageViewModel.prototype);
	EditPhotoViewModel.prototype.constructor = EditPhotoViewModel;

	function checkImageExist(imageType, imageData)
	{
		if (imageType === 'userprofile')
		{
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `users/${tf.userEntity.UserID}`))
				.then(function(response)
				{
					return response.Items[0].Avatar;
				});
		}
		return Promise.resolve(imageData);

	}

	EditPhotoViewModel.prototype.getImage = function(imageType, imageId, placeholderImage, apiPrefix, hideOverlay)
	{
		apiPrefix = typeof (apiPrefix) !== "undefined" ? apiPrefix : tf.api.apiPrefix();
		var paramData = {
		};
		if (placeholderImage)
		{
			paramData = {
				placeholderImage: placeholderImage
			};
		}
		return tf.promiseAjax.get(pathCombine(apiPrefix, "image", imageType, imageId),
			{
				paramData: paramData
			},
			{
				overlay: !hideOverlay
			})
			.then(function(response)
			{
				return response;
			});
	};

	function EditPhotoViewModel(imageType, imageId, recordName, databaseId, imageData)
	{
		TF.Control.BaseEditImageViewModel.call(this);
		this.recordName = recordName;
		this.imageWidth = 146;
		this.imageType = imageType;
		this.imageId = imageId;
		this.filePostData = {};
		this.obImageExist = ko.observable(false);
		this.oberrormessage = ko.observable(null);
		this.imageUpdated = false;
		this.obOriginalImage = ko.observable(null);
		this.apiPrefix = typeof (databaseId) !== "undefined" ? (tf.api.apiPrefixWithoutDatabase() + "/" + databaseId) : tf.api.apiPrefix();
		this.imageData = imageData;
	}

	EditPhotoViewModel.prototype.init = function(viewModel, el)
	{
		var $el = $(el);
		var $modalElement = $(el.parentElement);
		this.$form = $el.find('form');
		this.$overlay = this.$form.find("#open-datasouce-loading");
		this.$uploadedPhoto = $el.find('img.uploadedPhoto');
		this.$uploadedPhotoFileInput = $el.find('input[type=file]');

		var $uploadHint = $("<div class='upload-file-container'>\
										<div class='inner-mask' style='position:absolute;top:0;right:0;bottom:0;left:0;'>\
										<label class='input-label'>Drop to upload photo </label>\
										</div>\
									</div>");
		var $dragoverMask = $("<div class='dragover-mask'></div>");
		let isBase64 = (str) =>
		{
			const regex = /^(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/;
			return regex.test(str);
		}
		$dragoverMask.append($uploadHint)
		$modalElement.append($dragoverMask);

		var self = this;
		$modalElement.on('dragover', function(e)
		{
			$dragoverMask.css("z-index", "99999");
			e.preventDefault();
		})
		$dragoverMask.on("drop", function(e)
		{
			$dragoverMask.css("z-index", "-1");
			self.$uploadedPhotoFileInput[0].files = e.originalEvent.dataTransfer.files
			self.imageChange();
			e.preventDefault();
		})

		$dragoverMask.on("dragleave", function(e)
		{
			$dragoverMask.css("z-index", "-1");
			e.preventDefault();
		})

		checkImageExist(this.imageType, this.imageData).then(function(imageBase64)
		{
			this.obImageExist(imageBase64 != null)
			this.obOriginalImage(imageBase64);
			var current = $el.find('img.current');
			var src = '../../Global/img/photo.png';
			if (imageBase64 != null)
			{
				if (isBase64(imageBase64))
				{
					src = 'data:image/jpeg;base64,' + imageBase64;
				}
				else
				{
					src = imageBase64;
				}
			}

			current.attr("src", src);
		}.bind(this));
	};

	EditPhotoViewModel.prototype.deletePhoto = function()
	{
		var alterMessage = "Are you sure you want to delete the photo for this record" + (this.recordName ? " (" + this.recordName + ")" : "") + "?";
		return tf.promiseBootbox.yesNo(alterMessage, "Delete Photo").then(function(result)
		{
			if (this.imageType == "udfimage" || this.imageType == "questionimage")
			{
				return result;
			}

			if (result)
			{
				return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase() + '/users'),
					{
						data: [
							{
								Id: tf.userEntity.UserID,
								op: "remove",
								path: "Avatar"
							}
						]
					})
					.then(function()
					{
						return true;
					}.bind(this));
			}
			return false;
		}.bind(this));
	};

	function blobToBase64(blob)
	{
		return new Promise((resolve, _) =>
		{
			const reader = new FileReader();
			reader.onloadend = () => resolve(reader.result);
			reader.readAsDataURL(blob);
		});
	}

	function getMaxSizeOption(cropper)
	{
		var option = {}, maxSize = 3000;
		if (cropper.image.naturalWidth > maxSize)
		{
			option.width = maxSize;
		}
		else if (cropper.image.naturalHeight > maxSize)
		{
			option.height = maxSize;
		}
		return option;
	}

	EditPhotoViewModel.prototype.apply = function()
	{
		var self = this;
		var notUserAvatar = this.imageType == "udfimage" || this.imageType == "questionimage";
		if (!self.imageUpdated)
		{
			return Promise.resolve(notUserAvatar ? {
				imageUpdated: self.imageUpdated
			} : this.obOriginalImage());
		}

		var cropper = this.$uploadedPhoto.data('cropper')
		var canvas = cropper.getCroppedCanvas(getMaxSizeOption(cropper));

		var isJpeg = self.filePostData.fileType == "image/jpeg";
		var fileType = isJpeg ? "image/jpeg" : "image/png";
		return new Promise(resolve => canvas.toBlob(resolve, fileType)).then(blobData =>
		{
			return blobToBase64(blobData).then(base64Result =>
			{
				if (notUserAvatar)
				{
					return Promise.resolve({
						fileName: isJpeg ? self.filePostData.fileName : (self.filePostData.fileName.split(".")[0] + ".png"),
						fileType: blobData.type,
						fileData: base64Result,
						imageUpdated: self.imageUpdated
					});
				}

				return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase() + '/users'),
					{
						data: [
							{
								Id: tf.userEntity.UserID,
								op: "replace",
								path: "Avatar",
								value: base64Result.replace('data:' + blobData.type + ';base64,', "")
							}
						]
					})
					.then(function(response)
					{
						return response.Items[0].Avatar;
					});
			})
		});
	};
})();


(function()
{
	createNamespace("TF.Control").EditImageViewModel = EditImageViewModel;

	const ImageFitMode = {
		Fill: "Fill",
		Tile: "Tile",
	};

	EditImageViewModel.prototype = Object.create(TF.Control.BaseEditImageViewModel.prototype);
	EditImageViewModel.prototype.constructor = EditImageViewModel;

	EditImageViewModel.prototype.getImage = function(imageString)
	{
		return Promise.resolve(imageString);
	};

	function EditImageViewModel(imageType, imageId, recordName, databaseId, imageData, options)
	{
		TF.Control.BaseEditImageViewModel.call(this);
		this.obImageLabel = ko.observable("Image");
		this.obImageFitMode = ko.observable(options.imageFitMode || ImageFitMode.Fill);
		this.obIsPreviewModel(false);
		this.imageId = imageId;
		this.recordName = recordName;
		this.imageWidth = 146;
		this.imageType = imageType;
		this.filePostData = {};
		this.obImageExist = ko.observable(false);
		this.oberrormessage = ko.observable(null);
		this.imageUpdated = false;
		this.apiPrefix = tf.api.apiPrefixWithoutDatabase() + "/" + 0;

		this.imageData = imageData;
	}

	EditImageViewModel.prototype.init = function(viewModel, el)
	{
		var $el = $(el);
		this.$form = $el.find('form');
		this.$overlay = this.$form.find("#open-datasouce-loading");
		this.$uploadedPhoto = $el.find('img.uploadedPhoto');
		this.$uploadedPhotoFileInput = $el.find('input[type=file]');

		this.obImageExist(!!this.imageData);
		this.getImage(this.imageData, null).then(function(image)
		{
			var current = $el.find('img.current');
			var src = '';
			if (this.obImageExist())
			{
				src = 'data:image/jpeg;base64,' + image.replace("data:image/png;base64,", "").replace("data:image/jpeg;base64,", "");
				TF.DataView.ImageDataViewModelHelper.resizeImageInContainer(
					src,
					'.img-container',
					'.img-container-canvas',
					'.img-container-canvas > img');
			}
			else
			{
				src = '../../Global/img/photo.png';
			}
			current.attr("src", src);
		}.bind(this));
	};

	EditImageViewModel.prototype.apply = function()
	{
		var self = this;
		self.$overlay.show();

		let imageUrl = '';
		if (self.imageUpdated)
		{
			imageUrl = self.filePostData.fileData;
		}
		else if (self.obImageExist())
		{
			imageUrl = self.imageData;
		}

		return Promise.resolve({
			imageUrl: imageUrl,
			imageFitMode: self.obImageFitMode(),
		});
	};

	EditImageViewModel.prototype.imageChangeInner = function(file, url)
	{
		let self = this;
		var blobUrl = url.createObjectURL(file);
		self.$uploadedPhoto.attr('src', blobUrl).cropper({
			background: false,
			guides: false,
			autoCrop: true,
			aspectRatio: 1 / 1,
			autoCropArea: 1,
			minContainerWidth: self.imageWidth,
		}).cropper('replace', blobUrl);
		let hideCropperPlugFun = function()
		{
			let $cropperDragBox = $('.cropper-drag-box');
			if ($cropperDragBox && $cropperDragBox.length && $cropperDragBox.css('display') == 'block')
			{
				$cropperDragBox.hide();
			}
			let $cropperCropBox = $('.cropper-crop-box').hide();
			if ($cropperCropBox && $cropperCropBox.length && $cropperCropBox.css('display') == 'block')
			{
				$cropperCropBox.hide();
			}
		};

		let validateFitModeFun = function()
		{
			let isTileSizeImage = self.isTileSizeImage();
			if (isTileSizeImage && self.obImageFitMode() === ImageFitMode.Fill)
			{
				tf.promiseBootbox.confirm(
					{
						message: "The image resolution is too low. Would you like to Tile the image instead?",
						title: "Fit Mode Change Confirmation"
					}).then(function(ans)
					{
						if (ans === true)
						{
							self.oberrormessage(null);
							self.obImageFitMode(ImageFitMode.Tile);
						}
					});
			}
			else if (!isTileSizeImage && self.obImageFitMode() === ImageFitMode.Tile)
			{
				tf.promiseBootbox.confirm(
					{
						message: "We recommend Fill for high resolution images. Would you like to change this to Fill?",
						title: "Fit Mode Change Confirmation"
					}).then(function(ans)
					{
						if (ans === true)
						{
							self.obImageFitMode(ImageFitMode.Fill);
						}
					});
			}
		};

		setTimeout(hideCropperPlugFun, 10);
		setTimeout(hideCropperPlugFun, 200);
		setTimeout(validateFitModeFun, 200);
		self.imageUpdated = true;
	};

	EditImageViewModel.prototype.isTileSizeImage = function()
	{
		let isTileSizeImage = false;
		let $newImage = $('.img-container .cropper-container img');
		if ($newImage.length && $newImage[0].naturalWidth > 0 && $newImage[0].naturalHeight > 0)
		{
			isTileSizeImage = $newImage[0].naturalWidth < 1024 || $newImage[0].naturalHeight < 768;
		}

		return isTileSizeImage;
	};

	EditImageViewModel.prototype.deletePhoto = function()
	{
		var alterMessage = "Are you sure you want to delete the background image for this Form?";
		return tf.promiseBootbox.yesNo(alterMessage, "Delete Background Image").then(function(result)
		{
			if (result)
			{
				return {
					imageUrl: null
				};
			}
			return false;
		}.bind(this));
	};
})();
