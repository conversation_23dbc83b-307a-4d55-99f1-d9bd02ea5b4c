﻿(function()
{
	createNamespace('TF.Modal').AddDocumentClassificationModalViewModel = AddDocumentClassificationModalViewModel;

	function AddDocumentClassificationModalViewModel(options)
	{
		var self = this,
			type = options.type,
			isEditing = options.isEditing || false;

		TF.Modal.BaseModalViewModel.call(self);
		self.contentTemplate('modal/adddocumentclassificationcontrol');
		self.buttonTemplate('modal/positivenegative');
		self.obCloseButtonVisible(false);
		self.sizeCss = "modal-sm";
		self.addDocumentClassificationViewModel = new TF.Control.AddDocumentClassificationViewModel(options);
		self.data(self.addDocumentClassificationViewModel);
		self.dataItem = options.dataItem;

		var viewTitle, prefix;

		switch (type)
		{
			case 'documentclassification':
				prefix = isEditing ? 'Edit ' : 'Add ';
				viewTitle = prefix + "Document " + tf.applicationTerm.getApplicationTermSingularByName("Classification");
				break;
			default:
				viewTitle = "Add Two Fields";
				break;
		}
		self.title(viewTitle);
	}

	AddDocumentClassificationModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	AddDocumentClassificationModalViewModel.prototype.constructor = AddDocumentClassificationModalViewModel;

	AddDocumentClassificationModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.addDocumentClassificationViewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					self.positiveClose(result);
				}
			})
			.catch(function() { });
	};

	AddDocumentClassificationModalViewModel.prototype.negativeClose = function(returnData)
	{
		var hasChange;
		if (this.dataItem)
		{
			hasChange = this.addDocumentClassificationViewModel.name() != this.dataItem.Name || this.addDocumentClassificationViewModel.description() != this.dataItem.Description;
		}
		else
		{
			hasChange = this.addDocumentClassificationViewModel.name() != "" || this.addDocumentClassificationViewModel.description() != "";
		}

		if (hasChange)
		{
			return tf.promiseBootbox.yesNo({ message: "You have unsaved changes.  Would you like to save your changes prior to closing?", backdrop: true, title: "Unsaved Changes", closeButton: true })
				.then(function(result)
				{
					if (result == true)
					{
						return this.positiveClick();
					}
					if (result == false)
					{
						return TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
					}
				}.bind(this));
		}
		else
		{
			TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
		}
	};

	AddDocumentClassificationModalViewModel.prototype.dispose = function()
	{
		this.addDocumentClassificationViewModel.dispose();
	};
})();