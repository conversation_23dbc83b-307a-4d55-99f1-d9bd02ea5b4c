(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.EventRuleActionEmailDataModal = function(eventRuleActionEmailEntity)
	{
		namespace.BaseDataModel.call(this, eventRuleActionEmailEntity);
	};

	namespace.EventRuleActionEmailDataModal.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.EventRuleActionEmailDataModal.prototype.constructor = namespace.EventRuleActionEmailDataModal;

	namespace.EventRuleActionEmailDataModal.prototype.mapping = [
		{ from: "ID", default: 0 },
		{ from: "To", default: [] },
		{ from: "Cc", default: [] },
		{ from: "Bcc", default: [] },
		{ from: "Subject", default: "" },
		{ from: "Message", default: "" },
		{ from: "EmailAddress", default: "" },
	];
})();

(function()
{
	createNamespace("TF.Control").SendEmailOfEventRuleViewModel = SendEmailOfEventRuleViewModel;
	const emailMessageSelector = 'input[name=emailMessage]';
	/*
	options structure:

	{
		"EmailAddress": "<EMAIL>",
		"To": ["<EMAIL>","<EMAIL>"],
		"Cc": ["<EMAIL>","<EMAIL>"],
		"Bcc":["<EMAIL>","<EMAIL>"],
		"Subject": "",
		"Message": "encoded string"
	}

	*/
	function SendEmailOfEventRuleViewModel(options, type)
	{
		options.EmailAddresses = options.EmailAddress ? [options.EmailAddress] : null;
		this.options = options;
		this.type = type;
		this.titleContent = "Send To";
		this.obRecipientList = ko.observableArray([]);
		this.obSearchRecipient = ko.observable("");
		this.obRecipientSearchResultIsEmpty = ko.observable(false);
		this.obNewEmail = ko.observable("");
		this.obIsNewEditing = ko.observable(false);
		this.selectRecipientToClick = this.selectRecipientToClick.bind(this);
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);
		this.obEntityDataModel = ko.observable(new TF.DataModel.EventRuleActionEmailDataModal(options));
		this.obEmails = ko.observableArray(options.EmailAddresses);
		this.obCcEnable = ko.observable(false);
		this.obBccEnable = ko.observable(false);
		this.obSelectEmailToList = ko.observableArray([]);
		this.obSelectEmailCcList = ko.observableArray([]);
		this.obSelectEmailBccList = ko.observableArray([]);
		this.changePattern = this.changePattern.bind(this);
		this.initFromEmailSource(options).then(function()
		{
			this.obEntityDataModel().emailAddress(options.EmailAddresses[0] || '');
		}.bind(this));
		this.obEntityDataModel().emailAddress.subscribe(function()
		{
			tf.storageManager.save("fromEmailAddress", this.obEntityDataModel().emailAddress());
		}.bind(this));
		this.obEmailToList = ko.observableArray([]);
		this.obEmailCcList = ko.observableArray([]);
		this.obEmailBccList = ko.observableArray([]);
		if (this.obEntityDataModel().to() && this.obEntityDataModel().to().length > 0)
		{
			const mailCcList = this.convertEmailsToList(this.obEntityDataModel().to())
			this.obEmailToList(mailCcList);
			this.obSelectEmailToList(mailCcList);
		}

		if (this.obEntityDataModel().cc() && this.obEntityDataModel().cc().length > 0)
		{
			const mailCcList = this.convertEmailsToList(this.obEntityDataModel().cc());
			this.obEmailCcList(mailCcList);
			this.obSelectEmailCcList(mailCcList);
			this.obCcEnable(true);
		}

		if (this.obEntityDataModel().bcc() && this.obEntityDataModel().bcc().length > 0)
		{
			const mailBccList = this.convertEmailsToList(this.obEntityDataModel().bcc());
			this.obEmailBccList(mailBccList);
			this.obSelectEmailBccList(mailBccList);
			this.obBccEnable(true);
		}
		this.obEmailToErrorList = ko.observableArray([]);
		this.isNewEmailValid = ko.observable(true);
		this.obEmailDoneClickable = ko.observable(true);
		this.obEmailToString = ko.computed(function()
		{
			return this.convertEmailListToString(this.obEmailToList());
		}.bind(this));
		this.obEmailCcString = ko.computed(function()
		{
			return this.convertEmailListToString(this.obEmailCcList());
		}.bind(this));
		this.obEmailBccString = ko.computed(function()
		{
			return this.convertEmailListToString(this.obEmailBccList());
		}.bind(this));


		if (this.obEmailCcList().length > 0)
		{
			this.obCcEnable(true);
		}
		if (this.obEmailBccList().length > 0)
		{
			this.obBccEnable(true);
		}
		this.setEmailSubject();
		this.pageLevelViewModel = new TF.PageLevel.EmailPageLevelViewModel(this);
	}

	SendEmailOfEventRuleViewModel.prototype._initMessageEditor = function(viewModel, el)
	{
		var self = this, editorId = "#EmailMessageEditor",
			$editorWrapper = self._$form.find(".editor-wrapper");

		$editorWrapper.css("visibility", "visible");
		if (self.messageBodyEditor)
		{
			self.messageBodyEditor.destroy();
		}
		self.messageBodyEditor = self._$form.find(editorId).kendoEditor({
			resizable: {
				toolbar: false,
				content: false
			},
			tools: ["formatting", "fontName", "fontSize", "foreColor", "backColor", "bold", "italic", "underline",
				{
					name: "Undo",
					tooltip: "Undo",
				},
				{
					name: "Redo",
					tooltip: "Redo",
				},
				"justifyLeft",
				"justifyCenter", "justifyRight", "insertUnorderedList", "insertOrderedList", "indent", "createLink", "unlink", "createTable",
				"addRowAbove", "addRowBelow", "addColumnLeft", "addColumnRight", "deleteRow", "deleteColumn",
				{
					name: "createfield",
					tooltip: "Create field",
					exec: (e) =>
					{
						self.createField();
					}
				}],
			messages:
			{
				fontNameInherit: 'Default Font',
				fontSizeInherit: 'Default Font Size'
			},
			stylesheets:
				[
					"../../Global/ThirdParty/bootstrap/css/bootstrap.min.css",
					"../../Global/Css/KendoEditor.css"
				],
			select: function()
			{
				const $clearCssIcon = self.messageBodyEditor.toolbar.element.find("span.k-svg-i-clear-css");
				const hasSelectedText = self.messageBodyEditor.selectedHtml().length > 0;
				if (!hasSelectedText)
				{
					$clearCssIcon.addClass("disabled");
				}
				else
				{
					$clearCssIcon.removeClass("disabled");
				}
			}
		}).data("kendoEditor");

		setTimeout(function()
		{
			$(self.messageBodyEditor.body).blur(function()
			{
				self.obEntityDataModel().message(self.messageBodyEditor.value());
				self._$form.find(emailMessageSelector).change();
			});
		}, 300);
		let $MessageBodyHtmlEditor = self._$form.find('#MessageBodyHtmlEditor');
		$MessageBodyHtmlEditor.blur(function()
		{
			self._$form.find(emailMessageSelector).change();
		});
		$editorWrapper.find(".k-insertImage").closest(".k-tool").hide();
		self.messageBodyEditor.refresh();
		$(self.messageBodyEditor.body).addClass("merge-doc-body");

		const head = self.messageBodyEditor.wrapper.find("iframe").contents().find("head");
		const css = '<style type="text/css">' +
			'body,html{overflow:auto}; ' +
			'</style>';
		head.append(css);
		self.bindBodyEvents();
		self._$form.find(".editor-options-wrap .design").addClass("selected");
		self._$form.find(".editor-options-wrap .html").removeClass("selected");
		self.messageBodyEditor.toolbar.element.find("span.k-svg-i-clear-css").addClass("disabled");
		self.messageBodyEditor.value(self.options.Message || '');
	};

	SendEmailOfEventRuleViewModel.prototype.bindBodyEvents = function()
	{
		var self = this, editor = self.messageBodyEditor;
		$(editor.body).on("input", function()
		{
			if (editor.currentVariableHtml)
			{
				var currentVariableHtml = editor.currentVariableHtml;
				setTimeout(function()
				{
					var range = editor.getRange();
					editor.selectRange(range);
					self.insertHtml(currentVariableHtml);
				}, 1);

				editor.currentVariableHtml = null;
				return;
			}
		}).on("keyup", function(e)
		{
			if (e.keyCode === $.ui.keyCode.ENTER)
			{
				var dumpElement = $(editor.body).find("span[contenteditable=false][data-field]");
				dumpElement.each(function()
				{
					var ele = $(this);
					if (!ele.text())
					{
						var parent = ele.parent();
						ele.remove();
						var range = editor.createRange();
						range.selectNode(parent[0]);
						range.collapse();
						editor.selectRange(range);
					}
				});
			}
		}).off("dragstart.kendoEditor").on("dragstart", "*[data-field]", function(ev)
		{
			var dt = ev.originalEvent.dataTransfer, target = ev.target, tableTarget = $(target).closest("table[data-field]");
			if (tableTarget.length) target = tableTarget[0];
			var currentVariableHtml = target.outerHTML;
			dt.setData("text", "&nbsp;");
			dt.setData("text/html", "&nbsp;");
			dt.setData("application/html", currentVariableHtml);
		}).on("drop", function(ev)
		{
			var dt = ev.originalEvent.dataTransfer;
			var currentVariableHtml = (dt.getData("application/html") || "").trim();
			editor.currentVariableHtml = currentVariableHtml;
		}).on("click", 'span[data-field], table[data-field]', function(ev)
		{
			var target = $(ev.currentTarget), removeIconWidth = 16, documentScrollLeft = $(ev.currentTarget.ownerDocument).scrollLeft();
			if (ev.clientX > target.offset().left + target.width() - removeIconWidth - documentScrollLeft)
			{
				target.remove();
				return;
			}
			else
			{
				self.editField(ev.currentTarget);
			}
		});
	};

	SendEmailOfEventRuleViewModel.prototype.createField = function()
	{
		const options = {
		};
		return tf.modalManager.showModal(
			new TF.Modal.Event.EventFieldControl(options, this.type)
		).then((result) =>
		{
			if (!result)
			{
				return;
			}

			const id = kendo.guid();
			let html = `<span contenteditable='false' data-id=${id} data-field>${result.text}</span>&nbsp;`,
				editor = this.messageBodyEditor;

			var range = editor.getRange();
			editor.selectRange(range);
			this.insertHtml(html);
			this.setAttr($(editor.body).find(`span[data-field][data-id=${id}]`), result);
			this.resolveFocus();
		});
	};

	SendEmailOfEventRuleViewModel.prototype.resolveFocus = function()
	{
		var editor = this.messageBodyEditor,
			focusEle = $(editor.getSelection().focusNode).parent(),
			focusContainer = focusEle.parent(),
			index = getIndexOfParent(focusEle);
		this.resolveNewLineBeforeUneditableSpan(focusContainer);
		var range = editor.createRange();
		range.setStart(focusContainer[0], index + 1);
		range.setEnd(focusContainer[0], index + 1);
		editor.selectRange(range);
		editor.focus();
	};

	SendEmailOfEventRuleViewModel.prototype.resolveNewLineBeforeUneditableSpan = function(focusContainer)
	{
		var editor = this.messageBodyEditor;
		if (editor.beforeDropRootNodesCount == null)
		{
			return;
		}

		var rootNodesCount = editor.body.childNodes.length;
		if (rootNodesCount > editor.beforeDropRootNodesCount)
		{
			var newLine = focusContainer.next();
			if (newLine.length)
			{
				var children = newLine.children();
				newLine.remove();
				var last = focusContainer.children().last();
				if (isBr(last[0]))
				{
					children.each(function()
					{
						if (isBr(this))
						{
							return;
						}
						$(this).insertBefore(last);
					});
				}
				else
				{
					children.each(function()
					{
						if (isBr(this))
						{
							return;
						}
						focusContainer.append(this);
					});
				}
			}
		}

		editor.beforeDropRootNodesCount = null;
	};

	function isBr(ele)
	{
		return ele && ele.nodeName.toLowerCase() == "br";
	}

	function getIndexOfParent(element)
	{
		var $element = $(element),
			parent = $element.parent(),
			index = 0;
		$.each(parent[0].childNodes, function(i, item)
		{
			if (item == $element[0])
			{
				index = i;
				return false;
			}
		});

		return index;
	}

	SendEmailOfEventRuleViewModel.prototype.setAttr = function($element, options)
	{
		$element.attr("data-field", options.value);
	};

	SendEmailOfEventRuleViewModel.prototype.insertHtml = function(html)
	{
		var editor = this.messageBodyEditor,
			range = editor.getRange(),
			focusEle = range.startContainer;
		if (focusEle.nodeName == "#text")
		{
			focusEle = focusEle.parentElement;
		}
		editor.exec("insertHtml", { value: html });
	};

	SendEmailOfEventRuleViewModel.prototype.editField = function(element)
	{
		const $element = $(element);
		const options = {
			dataId: $element.attr("data-id"),
			widgetName: this.widgetName,
		};
		const dataField = $element.attr("data-field");
		options.dataField = dataField || '';
		return tf.modalManager.showModal(
			new TF.Modal.Event.EventFieldControl(options, this)
		).then((result) =>
		{
			if (!result)
			{
				return;
			}
			this.setAttr($element, result);
			if (result.text)
			{
				$element.html(result.text);
			}
		});
	};

	SendEmailOfEventRuleViewModel.prototype.changePattern = function(viewModel, e)
	{
		var self = this, htmlEditorId = "#MessageBodyHtmlEditor",
			$MessageBodyHtmlEditor = self._$form.find(htmlEditorId), $optionBtn = $(e.target).closest(".option");
		if ($optionBtn.hasClass("selected"))
		{
			return;
		}

		var $container = $optionBtn.closest(".editor-wrapper");
		$container.find(".option").removeClass("selected");
		$optionBtn.addClass("selected");

		if ($optionBtn.hasClass("design"))
		{
			$container.find(".text-editor-wrapper").show();
			$container.find(".html-editor-wrapper").hide();
			self.messageBodyEditor.value($MessageBodyHtmlEditor.val());
		}
		else
		{
			$container.find(".html-editor-wrapper").show();
			$container.find(".text-editor-wrapper").hide();
			$MessageBodyHtmlEditor.val(self.messageBodyEditor.value());
		}
	};

	SendEmailOfEventRuleViewModel.prototype.convertEmailListToString = function(emailList)
	{
		var list = _.uniq((emailList || []).map(function(i)
		{
			return (i.emailAddress() || "").toLowerCase();
		}).filter(Boolean));
		return list.join(";");
	};

	SendEmailOfEventRuleViewModel.prototype.setEmailSubject = function()
	{
		this.obEntityDataModel().subject(this.options.Subject);
	};

	SendEmailOfEventRuleViewModel.prototype.EmailFormatter = function(item)
	{
		return item.emailAddress();
	};

	SendEmailOfEventRuleViewModel.prototype.initFromEmailSource = function(options)
	{
		if (options.EmailAddresses && options.EmailAddresses.length > 0)
		{
			return Promise.resolve();
		}
		options.EmailAddresses = [];
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "clientconfigs"),
			{
				paramData:
				{
					clientId: tf.authManager.clientKey
				}
			}, { overlay: false })
			.then(function(data)
			{
				var config = data.Items[0];
				if (config?.IsTransfinderEmail)
				{
					options.EmailAddresses.push(tf.defaultTransfinderEmailAddress);
				}
				else if (config?.EmailAddress)
				{
					options.EmailAddresses.push(config.EmailAddress);
				}
			}.bind(this)).then(function()
			{
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "userprofiles?dbid=" + tf.datasourceManager.databaseId), {}, { overlay: false })
					.then(function(response)
					{
						if (!!response.Items[0].Email)
						{
							if (options.EmailAddresses.length <= 0 || options.EmailAddresses[0] !== response.Items[0].Email)
							{
								options.EmailAddresses.push(response.Items[0].Email);
							}
						}
					}.bind(this));
			}.bind(this)).then(function()
			{
				this.obEmails(options.EmailAddresses);
			}.bind(this));
	};

	SendEmailOfEventRuleViewModel.prototype.initModel = function(viewModel, el)
	{
		this._$form = $(el);
		var validatorFields = {},
			isValidating = false,
			self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		if (!TF.isPhoneDevice)
		{
			this._$form.find("input[name='from']").focus();
		}
		validatorFields.FromAddress = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				},
				callback:
				{
					message: "invalid email",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "email");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						if (!testEmail(value).valid)
						{
							return false;
						}
						return true;
					}
				}
			}
		};
		validatorFields["emailSubject"] = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				}
			}
		};

		validatorFields["mailToList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						return self.validateEmail("To", value, $field, updateErrors);
					}
				}
			}
		};

		validatorFields["mailCcList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						return self.validateEmail("Cc", value, $field, updateErrors);
					}
				}
			}
		};

		validatorFields["mailBccList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						return self.validateEmail("Bcc", value, $field, updateErrors);
					}
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [],
				live: "enabled",
				message: "This value is not valid",
				fields: validatorFields
			}).on("success.field.bv", function(e, data)
			{
				var $parent = data.element.closest(".form-group");
				$parent.removeClass("has-success");
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});
		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
		this.obEntityDataModel().apiIsDirty(false);
		this._initMessageEditor();
	};

	SendEmailOfEventRuleViewModel.prototype.validateEmail = function(emailType, value, $field, updateErrors)
	{
		var self = this,
			obEmailList = self[String.format("obEmail{0}List", emailType)];
		if (value)
		{
			updateErrors($field, "required");
		}
		value = value.trim();

		if (!value)
		{
			obEmailList([]);
			return true;
		}

		var result = true,
			reg = /[,;]/,
			errorEmails = [],
			oldList = obEmailList(),
			emptyEmailList = obEmailList().filter(function(item) { return !self.EmailFormatter(item); }),
			newList = [];

		_.uniq(value.split(reg).map(function(i) { return i.trim(); })).forEach(function(item)
		{
			item = item.trim();
			if (!item)
			{
				return;
			}

			if (!testEmail(item).valid)
			{
				errorEmails.push(item);
				result = false;
			}

			var matched = oldList.filter(function(c)
			{
				return (self.EmailFormatter(c) || "").trim().toLowerCase() == item.trim().toLowerCase();
			})

			if (matched.length > 0)
			{
				newList = newList.concat(matched);
			}
			else
			{
				newList.push(new TF.DataModel.ScheduledReportReceiptDataModel({
					SelectedUserId: 0,
					EmailAddress: item
				}));
			}
		});

		obEmailList(newList.concat(emptyEmailList));
		var message = errorEmails.length == 1 ? errorEmails[0] + " is not a valid email." : errorEmails.length + " emails are invalid.";
		self._$form.find(String.format("small[data-bv-for=mail{0}List][data-bv-validator=callback]", emailType)).text(message);

		if (result)
		{
			self.pageLevelViewModel.obValidationErrorsSpecifed([]);
		}

		return result;
	};

	SendEmailOfEventRuleViewModel.prototype.CcEnableClick = function()
	{
		this.obCcEnable(true);
	};

	SendEmailOfEventRuleViewModel.prototype.BccEnableClick = function()
	{
		this.obBccEnable(true);
	};

	SendEmailOfEventRuleViewModel.prototype.selectRecipientToClick = function(viewModel, e)
	{
		var self = this,
			sendType = $(e.currentTarget).data("send-type"),
			addressList = self[String.format("obEmail{0}List", sendType)],
			selectAddressList = self[String.format("obSelectEmail{0}List", sendType)];
		self.getSelectedItems(addressList()).then(function(selectedItems)
		{
			const emailAddressList = selectedItems.map(x => x.Email.toLowerCase());
			var options = {};
			tf.modalManager.showModal(new TF.Modal.ListMoverSelectRecipientControlModalViewModel(selectedItems, options)).then(function(result)
			{
				if (!result)
				{
					return;
				}
				var list = result.map(function(item)
				{
					const name = [item.FirstName, item.LastName].filter(x => x).join(" ") || item.LoginId;
					emailAddressList.push(item.Email.toLowerCase());
					return new TF.DataModel.ScheduledReportReceiptDataModel(
						{
							SelectedUserId: item.Id,
							EmailAddress: item.Email,
							UserName: name
						});
				});
				list = list.concat(addressList().filter(function(i) { return i.selectedUserId() == 0 && !emailAddressList.includes(i.emailAddress().toLowerCase()) }));
				addressList(list);
				selectAddressList(list);
			});
		});
	};

	SendEmailOfEventRuleViewModel.prototype.convertEmailsToList = function(emailList)
	{
		return emailList.map(function(email)
		{
			return new TF.DataModel.ScheduledReportReceiptDataModel({
				SelectedUserId: 0,
				EmailAddress: email
			});
		});
	};

	SendEmailOfEventRuleViewModel.prototype.getSelectedItems = function(recipients)
	{
		var self = this;
		return self.getSelectedItemsForSystemUser(recipients);
	};

	SendEmailOfEventRuleViewModel.prototype.getSelectedItemsForSystemUser = function(recipients)
	{
		var self = this,
			emails = recipients.map(function(item)
			{
				return (item.emailAddress() || "").trim().toLowerCase();
			}).filter(Boolean);

		return self.getSelectedItemsByEmails(emails);
	};

	SendEmailOfEventRuleViewModel.prototype.getSelectedItemsByEmails = function(emails)
	{
		var self = this;
		return !emails.length ? Promise.resolve([]) : Promise.all(tf.urlHelper.chunk(emails, 100).map(function(emailChunk)
		{
			var filterSyntax = emailChunk.join(","),
				paramData = { "@filter": String.format("in(Email,{0})", filterSyntax) };

			return tf.promiseAjax.get(self.getRequestUrl(), { paramData: paramData }).then(function(r)
			{
				return r.Items;
			}, function()
			{
				return [];
			});
		})).then(function(values)
		{
			return _.flattenDeep(values);
		});
	};

	SendEmailOfEventRuleViewModel.prototype.getRequestUrl = function()
	{
		return pathCombine(tf.api.apiPrefixWithoutDatabase(), "users");
	};

	SendEmailOfEventRuleViewModel.prototype.apply = function()
	{
		return this.trysave().catch(function(ex) { console.log(ex) });
	};

	SendEmailOfEventRuleViewModel.prototype.trysave = function()
	{
		var validator = this._$form.data("bootstrapValidator");
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
				{

					var messages = validator.getMessages(validator.getInvalidFields());
					var $fields = validator.getInvalidFields();
					var validationErrors = [];
					$fields.each(function(i, fielddata)
					{
						if (i == 0)
						{
							$(fielddata).focus();
						}
						validationErrors.push(
							{
								name: ($(fielddata).attr("data-bv-error-name") ? $(fielddata).attr("data-bv-error-name") : $(fielddata).closest("div.form-group").find("strong").text()),
								message: messages[i].replace("&lt;", "<").replace("&gt;", ">"),
								field: $(fielddata)
							});

					}.bind(this));
					this.obErrorMessageDivIsShow(true);
					this.obValidationErrors(validationErrors);

					return Promise.reject();
				}
				else
				{
					let sub = this.obEntityDataModel().subject(), msg = this.obEntityDataModel().message();
					let emptySub = sub === "" || sub == undefined || sub === null;
					let emptyMsg = msg === "" || msg == undefined || msg === null;
					if (emptySub || emptyMsg)
					{
						var info = "The Subject or Message has";
						if (emptySub && !emptyMsg)
						{
							info = "The Subject has";
						}
						if (!emptySub && emptyMsg)
						{
							info = "The Message has";
						}
						return tf.promiseBootbox.yesNo(info + " not been specified.  Are you sure you want to save this email config?", "Confirmation Message")
							.then(function(ans)
							{
								if (!ans)
								{
									return Promise.reject();
								}
								else
								{
									return this.save();
								}
							}.bind(this));
					}
					return this.save();
				}
			}.bind(this));
	};

	SendEmailOfEventRuleViewModel.prototype.save = function()
	{
		var self = this;
		return Promise.resolve().then(function()
		{
			const isDeign = self._$form.find(".editor-options-wrap .option.selected").hasClass("design");
			const textBoxContent = isDeign ? self.messageBodyEditor.value() : self._$form.find("textarea#MessageBodyHtmlEditor").val();
			return {
				EmailAddress: self.obEntityDataModel().emailAddress(),
				To: self.obEmailToList().map(x => x.emailAddress()),
				Cc: self.obEmailCcList().map(x => x.emailAddress()),
				Bcc: self.obEmailBccList().map(x => x.emailAddress()),
				Subject: self.obEntityDataModel().subject(),
				Message: textBoxContent
			};
		});
	};

	SendEmailOfEventRuleViewModel.prototype.close = function()
	{
		return new Promise(function(resolve, reject)
		{
			resolve(true);
		}.bind(this));
	};

	SendEmailOfEventRuleViewModel.prototype.selectRecipientsCancel = function()
	{
		if (!this.isNewEmailValid())
		{
			this.obIsNewEditing(false);
			this.obNewEmail("");
		}
		this.status("sendEmail");
	};

	SendEmailOfEventRuleViewModel.prototype.selectRecipientsDone = function()
	{
		if (!this.obEmailDoneClickable())
		{
			return;
		}
		var addressList = this.obRecipientList().filter(function(item)
		{
			return item.obSelected();
		}).map(function(item)
		{
			return new TF.DataModel.ScheduledReportReceiptDataModel(
				{
					SelectedUserId: item.Id,
					EmailAddress: item.Email,
					UserName: item.FirstName + " " + item.LastName
				});
		});
		if (this.status() === "selectToRecipients")
		{
			this.obEmailToList(addressList);
		}
		else if (this.status() === "selectCcRecipients")
		{
			this.obEmailCcList(addressList);
		}
		else
		{
			this.obEmailBccList(addressList);
		}
		this.status("sendEmail");
		this._$form.data("bootstrapValidator").validate();
	};

	SendEmailOfEventRuleViewModel.prototype.emptySearchRecipient = function()
	{
		this.obSearchRecipient("");
	};

	SendEmailOfEventRuleViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
		if (this.documentEntities)
		{
			this.documentEntities.length = 0;
		}

		return Promise.resolve(true);
	};
})();
