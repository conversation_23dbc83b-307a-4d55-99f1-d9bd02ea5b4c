﻿(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.IntegerBox = IntegerBox;

	function IntegerBox(initialValue, attributes, className, events)
	{
		var self = this;
		this.inputEnable = attributes && attributes.inputEnable == false ? false : true;
		namespace.StringBox.call(this, initialValue, attributes, className, undefined, undefined, events);
		this.getElement().on("keypress keyup blur", function(event)
		{
			var key = event.which || event.keyCode || 0;
			if (self.inputEnable && ((key == 64 && ($(this).val() === '' || $(this).val() === '@')) || /^@@/.test($(this).val())))
			{
				return;
			}

			if ((key < 48 || key > 57)
				&& (key != 45 || ($(this).val().indexOf('-') !== -1 ? this.selectionStart > 0 || this.selectionEnd === 0 : false) || this.selectionStart > 0)
				&& TF.Input.BaseBox.notSpecialKey(event))
			{
				event.preventDefault();
				event.stopPropagation();
			}
		});
	}

	IntegerBox.prototype = Object.create(namespace.StringBox.prototype);

	IntegerBox.constructor = IntegerBox;

	IntegerBox.prototype.type = "Integer";

	IntegerBox.prototype.rawValueChange = function(value)
	{
		if (!this.updating)
		{
			if (value === "")
			{
				return this.value(value);
			}
			let intValue = parseInt(value + "");
			if (isNaN(intValue))
			{
				return;
			}
			else if (this.value() !== intValue)
			{
				this.value(intValue);
			}
		}
	};
})();