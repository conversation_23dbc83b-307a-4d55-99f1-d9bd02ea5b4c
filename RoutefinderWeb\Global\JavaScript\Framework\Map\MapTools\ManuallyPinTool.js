(function()
{
	createNamespace("TF.Map").ManuallyPinTool = ManuallyPinTool;

	function ManuallyPinTool(routingMapTool)
	{
		var self = this;
		self.symbol = new TF.Map.Symbol();
		self.routingMapTool = routingMapTool;
		self.map = self.routingMapTool.routingMapDocumentViewModel._map;
		self.routeState = self.routingMapTool.routingMapDocumentViewModel.routeState;
		self.record = self.routingMapTool.routingMapDocumentViewModel.data;
		self.type = self.routingMapTool.routingMapDocumentViewModel.type;
		self.layer = self.routingMapTool.routingMapDocumentViewModel.layer;
		self.detailView = self.routingMapTool.routingMapDocumentViewModel.options.detailView;
		self.stopTool = new TF.RoutingMap.RoutingPalette.StopTool(null, self.map, null);

		self._manuallyPinActive = false;
	}

	ManuallyPinTool.prototype.startPin = function()
	{
		this.routingMapTool.$container.find(".manuallypin").addClass("on");
		this.routingMapTool.startSketch("manuallyPinTool");
		this.cursorToPin();
		this.bindClickEvent();
		this.bindEscEvent();

		this._manuallyPinActive = true;
	};

	ManuallyPinTool.prototype.stopPin = function()
	{
		this.routingMapTool && this.routingMapTool.$container.find(".manuallypin").removeClass("on");
		this.routingMapTool && this.routingMapTool.stopSketch("manuallyPinTool");
		this.map && this.cursorToDefault();
		tf.documentEvent.unbind("keydown.pin", this.routeState);
		this.mapClickEvent && this.mapClickEvent.remove();
		this.mapClickEvent = null;

		this._manuallyPinActive = false;
	};

	ManuallyPinTool.prototype.modifyRecordToNewLocation = async function(geometry, reverseGeocodeResult)
	{
		var self = this;
		var geoGraphic = self.type !== "parceladdresspoint" ? tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(geometry) : geometry;
		var dataModel = tf.dataTypeHelper.getDataModelByGridType(self.type)?.toData();
		var coordName = typeof dataModel?.XCoord !== "undefined" ? "Coord" : "coord";
		self._updateField("X" + coordName, geoGraphic.x);
		self._updateField("Y" + coordName, geoGraphic.y);
		self._updateField("GeoConfidence", TF.Grid.GeocodeTool.getGeoConfidence("ManuallyPin"));
		if (self.detailView)
		{
			self.detailView.obEditing(true);
		}

		if (self.type === "parceladdresspoint")
		{
			const addressInfo = await TF.RoutingMap.GeocodeHelper.getAddressInfoAsync(geometry);

			self._updateField("City", addressInfo?.City);
			self._updateField("State", addressInfo?.State);
			self._updateField("PostalCode", addressInfo?.Zip);
			if (self.map.findLayerById("parceladdresspointPolygonLayer").graphics.items.length === 0)
			{
				self._updateField("Type", "Address Point");
			}

			return;
		}

		if (!reverseGeocodeResult)
		{
			return;
		}
		var gridType = self.type === "geocodeInteractive" ? "student" : self.type;
		if (reverseGeocodeResult.Street)
		{
			self._updateField(TF.Grid.GeocodeTool.getAddressFieldNameByGridType("street", gridType), reverseGeocodeResult.Street);
		}
		if (reverseGeocodeResult.City)
		{
			self._updateField(TF.Grid.GeocodeTool.getAddressFieldNameByGridType("city", gridType), reverseGeocodeResult.City);
		}
		if (reverseGeocodeResult.Postal)
		{
			self._updateField(TF.Grid.GeocodeTool.getAddressFieldNameByGridType("zip", gridType), reverseGeocodeResult.Postal);
		}
		if (reverseGeocodeResult.Region)
		{
			self._updateField(TF.Grid.GeocodeTool.getAddressFieldNameByGridType("state", gridType), reverseGeocodeResult.Region);
		}
	};

	ManuallyPinTool.prototype._updateField = function(fieldName, value)
	{
		if (!this.detailView?.fieldEditorHelper) { return }
		this.detailView.fieldEditorHelper && this.detailView.fieldEditorHelper._onNonUDFEditorApplied({
			lockName: fieldName,
			errorMessages: null,
			fieldName: fieldName,
			recordValue: value,
			relationshipKey: undefined,
			text: undefined,
		});

		this.detailView.fieldEditorHelper._updateGeneralFieldsContent(fieldName, value, { updateAll: true });
	}

	ManuallyPinTool.prototype.bindClickEvent = function()
	{
		var self = this;
		self.mapClickEvent && self.mapClickEvent.remove();
		self.mapClickEvent = self.map.mapView.on("click", (event) =>
		{
			if (self.detailView && self.detailView.beforeManuallyPin)
			{
				self.detailView.beforeManuallyPin().then(result =>
				{
					if (result)
					{
						self.proceedPinEvent(event);
					}
				});
				return;
			}

			if (self.detailView && self.detailView.recordEntity && self.detailView.recordEntity.Geocoded)
			{
				Promise.resolve().then(() =>
				{
					if (self.detailView.isAvailableToRepin)
					{
						return self.detailView.isAvailableToRepin();
					}
					return Promise.resolve(true);
				}).then(canRepin =>
				{
					if (canRepin)
					{
						const dataTypeName = tf.dataTypeHelper.getDisplayNameByDataType(self.type);
						tf.promiseBootbox.yesNo({
							message: `Are you sure you want to repin this ${dataTypeName}?`,
							title: "Confirmation Message"
						}).then((res) =>
						{
							if (!res) return;

							self.proceedPinEvent(event);
						});
					}
				})
			}
			else
			{
				self.proceedPinEvent(event);
			}
		});
	};

	ManuallyPinTool.prototype.proceedPinEvent = function(event, isRevert)
	{
		var self = this;
		const hasPopups = self.routingMapTool.routingMapDocumentViewModel.gridMapPopup || (self.routingMapTool.routingMapDocumentViewModel.hasMapPopups && self.routingMapTool.routingMapDocumentViewModel.hasMapPopups());
		if (hasPopups && self.routingMapTool.routingMapDocumentViewModel.gridMapPopup.map)
		{
			self.routingMapTool.routingMapDocumentViewModel.gridMapPopup.closePopup();
		}

		if (self.type === "mapincident" && self.detailView)
		{
			let snapToStreet = false;
			if (self.detailView.isCreateGridNewRecord && self.detailView.fieldEditorHelper.editFieldList && !self.detailView.fieldEditorHelper.editFieldList.SnapToStreet)
			{
				snapToStreet = true
			}
			else if (self.detailView.fieldEditorHelper.editFieldList && self.detailView.fieldEditorHelper.editFieldList.SnapToStreet)
			{
				snapToStreet = self.detailView.fieldEditorHelper.editFieldList.SnapToStreet.value
			}
			else
			{
				snapToStreet = self.routingMapTool.routingMapDocumentViewModel.mapIncidentType.SnapToStreet
			}
			if (snapToStreet)
			{
				const nearestStreet = this._getNearestStreet(event.mapPoint, self.routingMapTool.routingMapDocumentViewModel._mapViewExtentCache);
				if (nearestStreet) event.mapPoint = tf.map.ArcGIS.geometryEngine.nearestCoordinate(nearestStreet, event.mapPoint).coordinate;
			}
		}

		TF.locationToAddress(event.mapPoint).then(async function(result)
		{
			self.modifyGraphicOnMap(event.mapPoint);
			self.modifyGeocodeInteractiveResult(result, event.mapPoint);
			await self.modifyRecordToNewLocation(event.mapPoint, result);
			self.modifyBoundaryGraphic(event.mapPoint);
			if (!isRevert)
			{
				self.showCreateParcelAddressPointModal();
			}
		})
	};

	ManuallyPinTool.prototype.bindEscEvent = function()
	{
		var self = this;
		tf.documentEvent.bind("keydown.pin", self.routeState, function(e)
		{
			if (e.key === "Escape")
			{
				self.stopPin();
			}
		});
	};

	ManuallyPinTool.prototype.modifyGraphicOnMap = function(geometry)
	{
		var self = this;
		self.graphic = self.findGraphic();
		if (self.graphic)
		{
			self.graphic.geometry = geometry;
		} else
		{
			self.addGraphic(geometry);
		}
	};

	ManuallyPinTool.prototype.findGraphic = function()
	{
		var self = this;
		switch (this.type)
		{
			case 'school':
				self.layer = self.map.findLayerById("schoolLayer");
				break;
			case 'georegion':
				self.layer = self.map.findLayerById("georegionPointLayer");
				break;
			case 'parceladdresspoint':
				self.layer = self.map.findLayerById("parceladdresspointPointLayer");
				break;
			case 'altsite':
				self.layer = self.map.findLayerById("altsiteLayer");
				break;
			case 'mapincident':
				self.layer = self.map.findLayerById("mapincidentLayer");
				break;
			default:
				break;
		}

		return Enumerable.From(self.layer.graphics.items).FirstOrDefault(null, function(c)
		{
			return c.attributes.type === self.type;
		});
	};

	ManuallyPinTool.prototype.addGraphic = function(geometry)
	{
		var graphic = new tf.map.ArcGIS.Graphic({
			geometry: geometry,
			symbol: this.getSymbol(),
			attributes: {
				id: this.record ? this.record.Id : TF.createId(),
				type: this.type
			}
		});
		this.layer.add(graphic);
		this.graphic = graphic;
	};

	ManuallyPinTool.prototype.getSymbol = function()
	{
		switch (this.type)
		{
			case "georegion":
				return this.symbol.georegionPoint();
			case "parceladdresspoint":
				return {
					...this.symbol.getAddressPointSymbol(), outline: {
						color: [255, 255, 0, 1],
						width: 3
					}
				};
			case "school":
				return this.symbol.school("#FF00FF");
			case "student":
				return this.symbol.student();
			case "altsite":
				return this.symbol.student();
			default:
				return this.symbol.student();
		}
	};

	ManuallyPinTool.prototype.cursorToPin = function()
	{
		TF.Helper.MapHelper.setMapCursor(this.map, "pin");
		this.map.mapView.pining = true;
	};

	ManuallyPinTool.prototype.cursorToDefault = function()
	{
		TF.Helper.MapHelper.setMapCursor(this.map, "default");
		this.map.mapView.pining = false;
	};

	ManuallyPinTool.prototype.modifyGeocodeInteractiveResult = function(result, geometry)
	{
		var self = this;
		if (self.type == "geocodeInteractive")
		{
			result ||= {};//RW-37473
			result.location = geometry;
			self.routingMapTool.routingMapDocumentViewModel.updateAddressFromPin(result, self.stopTool);
		}
	};

	ManuallyPinTool.prototype.modifyBoundaryGraphic = function(point)
	{
		var self = this;
		switch (self.type)
		{
			case "georegion":
				self._handleGeoRegionPoint(point);
				break;
			case "parceladdresspoint":
				self._handleParcelAddressPointPoint(point);
				break;
			default:
				break;
		}
	};

	ManuallyPinTool.prototype._handleParcelAddressPointPoint = function(pointGeometry)
	{
		const self = this;
		var parcelGraphic = self.map.findLayerById("parceladdresspointPolygonLayer").graphics.items[0];
		if (parcelGraphic && parcelGraphic.geometry && !tf.map.ArcGIS.geometryEngine.intersects(parcelGraphic.geometry, pointGeometry))
		{
			parcelGraphic.geometry = self._createFinger(pointGeometry, parcelGraphic.geometry);
			self.detailView.obEditing(true);
			self._updateField("Boundary", parcelGraphic.geometry);
		}
	}

	ManuallyPinTool.prototype._handleGeoRegionPoint = function(point)
	{
		const self = this;
		const georegionTypeId = self.detailView.getGeoRegionTypeId();
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "georegiontypes?Id=" + georegionTypeId)).then(function(response)
		{
			var georegionType = response.Items[0];
			if (georegionType && georegionType.Boundary === "User Defined")
			{
				var georegionBoundaryGraphic = self.map.findLayerById("georegionPolygonLayer").graphics.items[0];
				if (georegionBoundaryGraphic && georegionBoundaryGraphic.geometry)
				{
					georegionBoundaryGraphic.geometry = self._createFinger(point, georegionBoundaryGraphic.geometry);
					self.detailView.obEditing(true);
					self.detailView && self.detailView.fieldEditorHelper && self.detailView.fieldEditorHelper._onNonUDFEditorApplied(
						{
							lockName: "Shape",
							errorMessages: null,
							fieldName: "Shape",
							recordValue: georegionBoundaryGraphic.geometry,
							relationshipKey: undefined,
							text: undefined,
							type: "Shape"
						}
					);
				}
			}
		});
	}

	ManuallyPinTool.prototype._handleMapIncidentSnapping = function()
	{
		const self = this;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), 'mapincidenttypes?Id=' + self.detailView))
	}

	ManuallyPinTool.prototype._createFinger = function(point, polygon)
	{
		var self = this;
		var nearestPoint = tf.map.ArcGIS.geometryEngine.nearestCoordinate(polygon, point).coordinate,
			line = new tf.map.ArcGIS.Polyline({ spatialReference: self.map.mapView.spatialReference, paths: [[[point.x, point.y], [nearestPoint.x, nearestPoint.y]]] }),
			distance = TF.Helper.MapHelper.convertPxToDistance(self.map, tf.map.ArcGIS, 5),
			buffer = tf.map.ArcGIS.geometryEngine.buffer(line, distance, "meters");

		return tf.map.ArcGIS.geometryEngine.union([tf.map.ArcGIS.geometryEngine.simplify(buffer), tf.map.ArcGIS.geometryEngine.simplify(polygon)]);
	};

	ManuallyPinTool.prototype._getNearestStreet = function(point, streets)
	{
		let nearStreet = null;
		if (streets.length > 0)
		{
			let distance = TF.geocodeTolerance;
			for (let i = 0; i < streets.length; i++)
			{
				const streetGeometry = streets[i].geometry;
				const d = tf.map.ArcGIS.geometryEngine.distance(streetGeometry, point, "meters");
				if (d < distance)
				{
					nearStreet = streetGeometry;
					distance = d;
				}
			}
		}
		return nearStreet;
	}

	/**
	 * create parcel and address point when user manually pin on student map
	 */
	ManuallyPinTool.prototype.showCreateParcelAddressPointModal = async function()
	{
		if ((this.type !== "student" && this.routingMapTool?.routingMapDocumentViewModel?.gridViewModel?.type !== "student") || !tf.authManager.isAuthorizedFor("parceladdresspoint", "add"))
		{
			return;
		}

		let student;
		if (this.type === "student")
		{
			student = this.detailView.recordEntity ? $.extend({}, this.detailView.recordEntity) : tf.dataTypeHelper.getDataModelByGridType(this.type).toData();
			this.detailView.fieldEditorHelper.applyModificationsToEntity(student)
		}
		else if (this.routingMapTool?.routingMapDocumentViewModel?.gridViewModel?.type === "student")
		{
			let viewModel = this.routingMapTool.routingMapDocumentViewModel;
			student = viewModel.targetEntity;
			student.Xcoord = viewModel.result?.Xcoord;
			student.Ycoord = viewModel.result?.Ycoord;
		}

		if (!student.Xcoord)
		{
			return;
		}
		try
		{
			var confirmResult = await tf.promiseBootbox.yesNo({ message: "Do you want to create a new Address Point at this student’s geocode location?", title: "Confirm Message", closeButton: true })
			if (!confirmResult) { return; }
			const layout = await getParcelAddressPointDetailScreen();
			const parentDocument = this.detailView.parentDocument;
			const modal = new TF.DetailView.ParcelAddressPointQuickAddModalViewModel({ student: student, layout: layout, parentDocument: parentDocument});
			return tf.modalManager.showModal(modal).then((result) =>
			{
				if (result)
				{
					TF.RoutingMap.RoutingPalette.GeoSearch.setCachedAddressPoint(student.GeoStreet, result.entity);
				}
			});
		} catch (e)
		{
			tf.promiseBootbox.alert(e.message);
		}

		async function getParcelAddressPointDetailScreen()
		{
			let type = "parceladdresspoint";
			let layoutResponses = await tf.helpers.detailViewHelper.getLayouts(type);
			let layouts = layoutResponses.Items || [];
			let preferenceLayoutId = tf.storageManager.get("grid.detailscreenlayoutid." + type) || layouts[0]?.Id;
			var layout = layouts.find(layout => layout.Id === preferenceLayoutId);
			if (!layout)
			{
				throw new Error("Can't open create parcel & address point modal, no layout found.");
			}

			let layoutDetailResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "detailscreens", layout.Id));
			layout = layoutDetailResponse.Items[0];
			let missingFields = tf.helpers.detailViewHelper.validateRequiredFields(JSON.parse(layout.Layout), type);
			if (missingFields.length > 0)
			{
				throw new Error("Can't open create parcel & address point modal, layout is missing required fields.");
			}
			return layout;
		}
	}

	ManuallyPinTool.prototype.dispose = function()
	{
		this.stopPin();
		this.routingMapTool = null;
		this.map = null;
		this.routeState = null;
		this.record = null;
		this.type = null;
		this.layer = null;
		this.detailView = null;
	};
})();