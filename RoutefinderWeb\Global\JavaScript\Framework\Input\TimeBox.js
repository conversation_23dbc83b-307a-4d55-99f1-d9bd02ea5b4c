﻿// Bootstrap Time Picker
(function()
{
	createNamespace("TF.Input").TimeBox = TimeBox;

	function TimeBox(initialValue, attributes, disable, noWrap, delayChange, element)
	{
		this.showClearIcon = attributes && attributes.showClearIcon;
		if (attributes && attributes.onKeypress)
		{
			this.keypress = attributes.onKeypress.bind(this);
			delete attributes.onKeypress;
		}
		else
		{
			this.keypress = this.keypress.bind(this);
		}
		this.blurIntercept = attributes && attributes.hasOwnProperty("blurintercept") ? attributes.blurintercept : null;
		if (attributes && attributes.adjustPopupPosition)
		{
			this.adjustPopupPosition = attributes.adjustPopupPosition;
			delete attributes.adjustPopupPosition;
		}

		TF.Input.BaseBox.call(this, initialValue, attributes, disable);
		this.keepInvalid = true;
		this._noWrap = noWrap;
		this.delayChange = delayChange;
		this._dateTimePicker = null;
		this.$parent = $(element);
		if (attributes)
		{
			if (attributes.format)
			{
				this.formatString = attributes.format;
			}

			this.keepInvalid = attributes.hasOwnProperty("keepInvalid") ? attributes.keepInvalid : true;
			this.minDate = attributes.min;
			this.maxDate = attributes.max;
			this.disableWeekend = attributes.disableWeekend;
			this.inputEnable = attributes.inputEnable;
			this.ignoreReadonly = attributes.ignoreReadonly;
			this.stepping = attributes.stepping;
			this._exactFormat = attributes.exactFormat;
		}
		this.initialize.call(this);

		this.height = 158;
	}

	TimeBox.prototype = Object.create(TF.Input.BaseBox.prototype);

	TimeBox.constructor = TimeBox;

	TimeBox.prototype.getInvalidCharacterRegex = function()
	{
		return /[^0-9A-Za-z|\:| ]/g;
	};

	TimeBox.prototype.type = "Time";

	TimeBox.prototype.formatString = "LT";

	TimeBox.prototype.pickerIconClass = "k-i-clock";

	TimeBox.prototype.convertToDateFormat = function(strValue)
	{
		if (strValue.indexOf(".") > 0)
		{
			if (strValue.trim().split(".").length == 3)
			{
				strValue = strValue.replace(/\./g, "-");
			}
			else
			{
				return strValue;
			}
		}
		if (strValue.indexOf("-") > 0)
		{
			var strArr = strValue.trim().split("-");
			if (strArr.length == 3)
			{
				if (strArr[0].length == 4)
				{
					return strArr[1] + "/" + strArr[2] + "/" + strArr[0];
				}
				return strArr[1] + "/" + strArr[0] + "/" + strArr[2];
			}
			return strValue;
		}
		return strValue;
	};

	TimeBox.prototype.initialize = function()
	{
		var self = this;

		let $clearX = null;
		if (self.showClearIcon)
		{
			$clearX = $(`<span class="clear-x" title="clear">&times;</span>`);
		}

		this.value.subscribe(function(value)
		{
			var datetime = moment.utc(this.value(), [this.formatString, moment.ISO_8601]);
			if (value == "Empty" || value == 'Not empty' || !value || !datetime.isValid())
			{
				this._dateTimePicker.date(null);
				if (this.showClearIcon)
				{
					$clearX.addClass("empty");
				}
			}
			else
			{
				this._dateTimePicker.date(datetime);
				if (this.showClearIcon)
				{
					$clearX.removeClass("empty");
				}
			}
		}, this);
		var $input = $('<input ' + this.type + ' type="text" class="form-control datepickerinput" data-tf-input-type="' + this.type + 
			'" data-bind="disable:disable, css:{disabled:disable},event: {blur:updateValue,keypress:keypress}" />');
		this.applyAttribute($input, this.attributes);
		var $button = $('<div class="input-group-addon glyphicon datepickerbutton"><span unselectable="on" class="k-icon ' + this.pickerIconClass + '">select</span></div>');
		if (this.attributes)
		{
			this.applyAttribute($button, this.attributes.picker);
		}

		if (TF.isPhoneDevice) //VIEW-1252 Date Control is not visible when focus is still set to input
		{
			$button.click(function()
			{
				$(":focus").blur();
			});
		}
		var $element = null;
		if (self.showClearIcon)
		{
			$element = $input.add($clearX).add($button);
		} else
		{
			$element = $input.add($button);
		}

		var $container = "body";

		if (this.$parent.closest(".document-dataentry").length > 0)
		{
			$container = $('<div style="position:relative;"></div>');
			this.$parent.parent().append($container);
		}

		$element.datetimepicker({
			widgetParent: $container,
			useCurrent: false,
			keepInvalid: this.keepInvalid,
			minDate: this.minDate,
			maxDate: this.maxDate,
			stepping: this.stepping,
			ignoreReadonly: self.ignoreReadonly !== undefined && self.ignoreReadonly !== null ? self.ignoreReadonly : false,
			daysOfWeekDisabled: this.disableWeekend ? [0, 6] : [],
			widgetPositioning:
			{
				horizontal: 'left'
			},
			format: this.formatString,
			locale: moment.locale(),
			keyBinds:
			{
				enter: null
			}
		});

		this._dateTimePicker = $element.data('DateTimePicker');
		let dateTimePicker = this._dateTimePicker;
		this.value.valueHasMutated();
		ko.applyBindings(this, $element[0]);
		if (self.showClearIcon)
		{
			$clearX.click(function()
			{
				self.value('');
				dateTimePicker.hide();
			});
		}
		this.$element = $element;

		function setDateTimeValue(event)
		{
			if (!event.date) return;
			if (!event.oldDate)
			{
				self.value(self.getTimeString(event.date));
				return;
			}
			var oldDate = moment(event.oldDate),
				newDate = moment(event.date);
			//RW-13267 check whether the date changed is result from time changed
			if (self.type == 'DateTime' && oldDate.date() !== newDate.date())	
			{
				var diff = newDate.diff(oldDate, 'minutes');

				if (diff == 1 || diff == -1 || diff == 60 || diff == -60)
				{
					newDate.subtract(diff > 0 ? 1 : -1, 'days');
				}
			}
			if (!newDate.isSame(oldDate, 'minutes'))
			{
				self.value(self.getTimeString(newDate));
			}
		}

		var delayTimeOut;
		$element.on('dp.change', function(event)
		{
			if (this.delayChange)
			{
				clearTimeout(delayTimeOut);
				delayTimeOut = setTimeout(function()
				{
					setDateTimeValue(event);
				}, 500);
			}
			else
			{
				setDateTimeValue(event);
			}
		}.bind(this));

		$element.on('dp.show', function(e)
		{
			var widgetParent = $(this._dateTimePicker.widgetParent());
			var $modalBody = widgetParent.closest(".modal-body");
			if (TF.isMobileDevice && $modalBody.length)
			{
				$modalBody.bind('mousewheel touchmove', lockScroll);
			}

			var widget = widgetParent.find(".bootstrap-datetimepicker-overlay>.bootstrap-datetimepicker-widget:last"),
				offsetParent = widget.offsetParent(),
				widgetWidth = widget.width(),
				bodyWidth = offsetParent.width();
			if (TF.isPhoneDevice)
			{
				var overlay = $("body>.bootstrap-datetimepicker-overlay");
				overlay.on("click", function()
				{
					this._dateTimePicker.hide();
				}.bind(this));
			}

			if (widget.length == 0)
			{
				widget = this.$element.parent().find(".bootstrap-datetimepicker-widget");
				if (widget)
				{
					var modal = this.$element.closest(".modal-dialog");
					if (modal.length > 0)
					{

						widget.css(
							{
								top: this.$element.outerHeight(), //+ this.$element.offset().top - modal.offset().top,
								bottom: 'auto',
								left: $button.closest(".input-group").outerWidth() - $button.outerWidth() / 2 - widget.outerWidth() / 2 //$button.offset().left - widget.outerWidth() / 2 + $button.outerWidth() / 2 - modal.offset().left
							});
					}
					else
					{
						var top = this.$element.outerHeight(),
							left = $button.closest(".input-group").outerWidth() - $button.outerWidth() / 2 - widget.outerWidth() / 2,
							widgetOffsetRight;
						if (($button.offset().top + $button.outerHeight() + this.height + 67) > document.body.offsetHeight)
						{
							top = -this.height;
						}
						widget.css(
							{
								top: top,
								bottom: 'auto',
								left: left
							});

						widgetOffsetRight = widget.offset().left + widget.outerWidth();
						if (widgetOffsetRight > document.body.offsetWidth)
						{
							left -= widgetOffsetRight - document.body.offsetWidth + 5;
							widget.css(
								{
									left: left
								});
						}
					}
					//widget.height(this.height - 10);
				}
			}
			else if (widget && widget.offset())
			{
				var preWightOffset = widget.offset();
				var wightOffsetLeft = $button.offset().left - widget.outerWidth() / 2 + $button.outerWidth() / 2;
				if (this.adjustPopupPosition && typeof this.adjustPopupPosition === "function")
				{
					this.adjustPopupPosition(this.$element, widget);
				}
				else 
				{
					var wightCss = {}, modal;
					if (TF.isMobileDevice)
					{
						modal = this.$element.closest(".modal-dialog");
						if (modal.length > 0)
						{
							bodyWidth = modal.width();
							if (modal && wightOffsetLeft > modal.offset().left + bodyWidth - widgetWidth && bodyWidth > widgetWidth)
							{
								wightCss['left'] = modal.offset().left + bodyWidth - widgetWidth - 10;
							}
							else if (modal && wightOffsetLeft < modal.offset().left + 5)
							{
								wightCss['left'] = modal.offset().left;
							}
							else
							{
								wightCss['left'] = wightOffsetLeft;
							}
						}
						else
						{
							wightCss['left'] = wightOffsetLeft;
						}
					}
					else
					{
						if (wightOffsetLeft > bodyWidth - widgetWidth && bodyWidth > widgetWidth)
						{
							wightCss['left'] = bodyWidth - widgetWidth - 10;
						}
						else if (wightOffsetLeft < 5)
						{
							wightCss['left'] = 10;
						} else
						{
							wightCss['left'] = wightOffsetLeft;
						}

					}
					var top = 0, currentTargetOffset = $(e.currentTarget).offset();

					if (preWightOffset.top <= 0)
					{
						top = currentTargetOffset.top - widget.outerHeight();
					}
					else if (preWightOffset.top !== currentTargetOffset.top + $(e.currentTarget).height())
					{
						top = currentTargetOffset.top + $(e.currentTarget).height();
						if (top + widget.outerHeight() > widgetParent.height() && widgetParent.height() > 0)
						{
							top = currentTargetOffset.top - widget.outerHeight();
						}
					}

					wightCss['top'] = top;
					wightCss['bottom'] = "auto";
					widget.css(wightCss);
				}
			}
			this._toggleScroll(false);
			this._toggleScroll(true);
			if (TF.isPhoneDevice && !this.adjustPopupPosition) //VIEW-1252 Date Control is not visible when focus is still set to input
			{
				if (widget.closest(".modal-dialog").length == 0)
				{
					setTimeout(function()
					{
						var windowHeight = (widget.parent().offset()?.top || 0) + widget.parent().height();
						var offset = this.$element.offset();
						if (offset.top + widget.height() * 1.5 >= $(window).height() + $(window).scrollTop() &&
							widget.height() + this.$element.outerHeight() < offset.top)
						{
							//top
							widget.css(
								{
									top: 'auto',
									bottom: windowHeight - this.$element.offset().top
								});
						}
						else
						{
							//bottom
							widget.css(
								{
									top: 'auto',
									bottom: windowHeight - this.$element.offset().top - widget.outerHeight() - this.$element.outerHeight()
								});
						}
					}.bind(this), 100);
				}
				$(window).on("resize.dateTime", function()
				{
					setTimeout(function()
					{
						this._dateTimePicker.hide();
					}.bind(this), 10);
				}.bind(this));
			}

		}.bind(this));
		$element.on('dp.hide', function()
		{
			var widgetParent = $(this._dateTimePicker.widgetParent());
			var $modalBody = widgetParent.closest(".modal-body");
			if (TF.isMobileDevice && $modalBody.length)
			{
				$modalBody.unbind('mousewheel touchmove', lockScroll);
			}

			this._toggleScroll(false);
			$(window).off("resize.dateTime");
		}.bind(this));

		var reg = this.getInvalidCharacterRegex();
		$element.on("input", function()
		{
			var text = $(this).val();
			if (self.inputEnable && text.indexOf('@') === 0)
			{
				$(this).val(text);
			}
			else
			{
				$(this).val(text.replace(reg, ""));
			}
		});
	};

	TimeBox.prototype.getInvalidCharacterRegex = function()
	{
		return /[^0-9A-Za-z|\/|\:| ]/g;
	};

	TimeBox.prototype.blurInterceptCheck = function(value)
	{
		if (this.blurIntercept)
		{
			if (value === "")
			{
				return;
			}

			const dt = new moment(value, 'h:mm A', true);
			if (!dt.isValid())
			{
				this.$element[0].value = null;
				this.value(null);
			}
		}
	}

	TimeBox.prototype.keypress = function(viewModel, e)
	{
		if (e.keyCode == 13)
		{
			setTimeout(function()
			{
				$(viewModel.$element[0]).blur();
			}.bind(this), 0);
		}
		else
			return true;
	};

	TimeBox.prototype.updateValue = function(self, e)
	{
		let text = e ? e.currentTarget.value : '';
		this.blurInterceptCheck(text);

		if (this.inputEnable && text.indexOf('@') === 0)
		{
			return true;
		}

		const timePattern = /^([01]?[0-9]):[0-5]?[0-9]$/;

		let dateTime = this.type == "Time" ? this._dateTimePicker.date() : moment(this.convertToDateFormat(text));

		if (!dateTime)
		{
			this.value(null);
			return true;
		}
		if (dateTime.isValid())
		{
			const isTimeColumn = TF.DateTimeBoxHelper.testIsTimeColumn($(e.currentTarget));
			this.value(this.getTimeString(dateTime));

			if (isTimeColumn)
				e ? (timePattern.test(text) ? null : (e.currentTarget.value = dateTime.format('h:mm') + ' ' + (dateTime.hours() < 12 ? 'AM' : 'PM'))) : null;
			else
				null;

			return true;
		}
		else
		{
			this.value(null);
			return false;
		}
	};

	TimeBox.prototype.getTimeString = function(dateTime)
	{
		if (this._exactFormat)
		{
			return `${dateTime.format('hh:mm')} ${(dateTime.hours() < 12 ? 'AM' : 'PM')}`;
		}
		return toISOStringWithoutTimeZone(dateTime);
	}

	TimeBox.prototype.getElement = function()
	{
		if (this._noWrap)
		{
			return this.$element;
		}
		else
		{
			return $("<div>").addClass("input-group").append(this.$element);
		}
	};

	TimeBox.prototype._toggleScroll = function(toggle)
	{
		var method = toggle ? "on" : "off";
		var scrollableParents = this._scrollableParents();
		if (method === "on")
		{
			scrollableParents.map(function(i, item)
			{
				$(item).data("scrollTop", item.scrollTop)
			});
			scrollableParents[method]("scroll.datatimebox", this._resizeProxy.bind(this));
			scrollableParents[method]("mousedown.datatimebox", this.$element.data("DateTimePicker").hide);
			$(window)[method]("scroll.datatimebox", this._resizeProxy.bind(this));
			if (!TF.isPhoneDevice) //VIEW-1252 Date Control is not visible when focus is still set to input
			{
				$(window)[method]("resize.datatimebox", this.$element.data("DateTimePicker").hide);
			}
		}
		else
		{
			scrollableParents[method]("scroll.datatimebox");
			scrollableParents[method]("mousedown.datatimebox");
			$(window)[method]("scroll.datatimebox");
			$(window)[method]("resize.datatimebox");
		}
	};

	TimeBox.prototype._scrollableParents = function()
	{
		var that = this;
		return that.$element
			.parentsUntil("body")
			.filter(function(index, element)
			{
				return that._isScrollable(element);
			});
	};

	TimeBox.prototype._isScrollable = function(element)
	{
		var overflow = $(element).css("overflow");
		return overflow == "auto" || overflow == "scroll";
	};

	TimeBox.prototype._resizeProxy = function(e)
	{
		var widget = $("body>.bootstrap-datetimepicker-overlay>.bootstrap-datetimepicker-widget:last");
		if (widget && widget.offset())
		{
			var scrollTop = $(e.currentTarget).data("scrollTop");
			widget.css(
				{
					top: widget.offset().top + scrollTop - e.currentTarget.scrollTop,
					height: this.height
				});
			$(e.currentTarget).data("scrollTop", e.currentTarget.scrollTop);
		}
	};

	TimeBox.prototype.dispose = function()
	{
		this._dateTimePicker.destroy();
		ko.removeNode(this.$element[0]);
		TF.Input.BaseBox.prototype.dispose.call(this);
	};

	function lockScroll(e)
	{
		e.preventDefault();
	}
})();