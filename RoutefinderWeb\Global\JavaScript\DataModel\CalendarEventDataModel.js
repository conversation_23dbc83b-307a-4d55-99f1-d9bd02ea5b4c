﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.CalendarEventDataModel = function(calendarEventEntity)
	{
		namespace.BaseDataModel.call(this, calendarEventEntity);
		this.editStatus = ko.observable(calendarEventEntity ? TF.Enums.EditStatus.NoChanged : TF.Enums.EditStatus.Created);
		this.extraOptions = {
			force: false,
			overwriteCalendarEventIds: null
		};
	}

	namespace.CalendarEventDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.CalendarEventDataModel.prototype.constructor = namespace.CalendarEventDataModel;

	namespace.CalendarEventDataModel.prototype.mapping = [
		{ from: "ObjectId", default: 0 },
		{ from: "Id", to: "id", default: 0 },
		{ from: "DBID", to: "dbid", default: 0 },
		{ from: "Description", default: "" },
		{ from: "End", to: "endTime", default: null },
		{ from: "EventTypeID", default: null },
		{ from: "EventType", default: "" },
		{ from: "School", default: "" },
		{ from: "Start", to: "startTime", default: null },
		{ from: "DistrictID", default: "" },
		{ from: "Summary", default: "" },
		{ from: "Sun", to: "sunday", default: false },
		{ from: "Mon", to: "monday", default: false },
		{ from: "Tues", to: "tuesday", default: false },
		{ from: "Wed", to: "wednesday", default: false },
		{ from: "Thurs", to: "thursday", default: false },
		{ from: "Fri", to: "friday", default: false },
		{ from: "Sat", to: "saturday", default: false },
		{ from: "DisplayDate", default: null }
	];

})();
