(function()
{
	createNamespace('TF.Modal.Report').CreateLogiReportModalViewModel = CreateLogiReportModalViewModel;

	function CreateLogiReportModalViewModel(options)
	{
		var self = this,
			modalTitle = "Create New Report (Logi)",
			positiveButtonLabel = "Create";

		TF.Modal.BaseModalViewModel.call(self);

		self.title(modalTitle);
		self.sizeCss = "modal-dialog-sm";
		self.contentTemplate("workspace/LogiReport/CreateLogiReport");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel(positiveButtonLabel);
		self.viewModel = new TF.Control.Report.CreateLogiReportViewModel(options);
		self.data(self.viewModel);
	}

	CreateLogiReportModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	CreateLogiReportModalViewModel.prototype.constructor = CreateLogiReportModalViewModel;

	CreateLogiReportModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.viewModel.validate().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};

	CreateLogiReportModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose();
	};

	CreateLogiReportModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};
})();

