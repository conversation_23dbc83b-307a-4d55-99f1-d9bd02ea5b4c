﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleBodyTypeDeletion = DataListVehicleBodyTypeDeletion;

	function DataListVehicleBodyTypeDeletion()
	{
		this.type = 'vehiclebodytype';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Body Type';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleBodyTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleBodyTypeDeletion.prototype.constructor = DataListVehicleBodyTypeDeletion;

	DataListVehicleBodyTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?BodyTypeId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleBodyTypeDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclebodytype", pb.DELETE), ids);
	}

	DataListVehicleBodyTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();