(function()
{
	createNamespace("TF.Map").HomeToSchoolPathTool = HomeToSchoolPathTool;

	function HomeToSchoolPathTool(routingMapTool)
	{
		var self = this;
		self.routingMapTool = routingMapTool;
		self.arcgis = tf.map.ArcGIS;
		self.map = self.routingMapTool.routingMapDocumentViewModel._map;
		self.detailView = self.routingMapTool.routingMapDocumentViewModel.options.detailView;
		self.visible = false;
		self.layer = null;
	}

	HomeToSchoolPathTool.prototype.toggleDisplay = function()
	{
		this.visible = !this.visible;
		if (this.visible)
		{
			this.draw();
		} else
		{
			this.clear();
		}
	};

	HomeToSchoolPathTool.prototype.draw = function()
	{
		var self = this;
		if (!this.layer)
		{
			this.layer = new tf.map.ArcGIS.GraphicsLayer({ id: "homeToSchoolPathLayer" });
			this.map.add(this.layer, 0);
		}
		this.clear();
		var data = this.getData();
		if (data.school && data.student)
		{
			var school = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(data.school.geometry);
			var student = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(data.student.geometry);
			tf.loadingIndicator.show();
			TF.solveWalk(student.x, student.y, school.x, school.y).then((result) =>
			{
				tf.loadingIndicator.tryHide();
				if (result && result.directions)
				{
					var path = new tf.map.ArcGIS.Graphic({
						geometry: result.directions.mergedGeometry,
						symbol: {
							type: "simple-line",
							style: "solid",
							color: "#D7483C",
							width: 3
						}
					});
					this.layer.add(path);
				}
			}).catch(() =>
			{
				this.visible = false;
				tf.loadingIndicator.tryHide();
			});
		}

		this.locationChangeSubscribe = function(e, data)
		{
			if (data.result.fieldName == "Xcoord")
			{
				self.draw();
			}
		};
		this.detailView.onFieldChange.subscribe(this.locationChangeSubscribe);
	};

	HomeToSchoolPathTool.prototype.clear = function()
	{
		this.layer && this.layer.removeAll();
		this.detailView.onFieldChange.unsubscribe(this.locationChangeSubscribe);
	};

	HomeToSchoolPathTool.prototype.getData = function()
	{
		var school,
			student,
			layer = this.map.findLayerById("studentLayer");
		layer.graphics.items.forEach(element =>
		{
			switch (element.attributes.type)
			{
				case "school":
					school = element;
					break;
				case "student":
					student = element;
					break;
			}
		});
		return { school: school, student: student };
	};

	HomeToSchoolPathTool.prototype.dispose = function()
	{
		this.clear();
	};
})();