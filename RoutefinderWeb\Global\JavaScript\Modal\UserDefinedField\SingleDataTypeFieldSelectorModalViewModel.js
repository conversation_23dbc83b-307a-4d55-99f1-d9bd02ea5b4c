(function()
{
	createNamespace("TF.Modal").SingleDataTypeFieldSelectorModalViewModel = SingleDataTypeFieldSelectorModalViewModel;

	function SingleDataTypeFieldSelectorModalViewModel(dataType, dataSourceViewModelType, fieldData)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-sm";
		this.title("Select Data");
		this.contentTemplate('modal/UserDefinedField/SingleDataTypeFieldSelector');
		this.buttonTemplate('modal/positiveNegative');
		this.obPositiveButtonLabel("Apply");
		this.openDataSourceViewModel = new dataSourceViewModelType(dataType, fieldData, this.obDisableControl);
		this.data(this.openDataSourceViewModel);
	}

	SingleDataTypeFieldSelectorModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SingleDataTypeFieldSelectorModalViewModel.prototype.constructor = SingleDataTypeFieldSelectorModalViewModel;

	SingleDataTypeFieldSelectorModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	SingleDataTypeFieldSelectorModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.cancel()
			.then(function()
			{
				this.negativeClose();
			}.bind(this));
	};
})();