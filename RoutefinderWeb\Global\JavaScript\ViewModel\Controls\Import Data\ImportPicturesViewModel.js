(function()
{
	createNamespace('TF.Control').ImportPicturesViewModel = ImportPicturesViewModel;

	//Define upload file types
	var UPLOAD_FILE_TYPE = {
		IMAGE: 'images',
		ZIP: 'zip',
		FOLDER: 'folder',
	}
	//Define update types
	var UPDATE_TYPE = {
		ALL: 'all',
		CHANGE: 'change'
	}
	//Define picture types
	var PICTURE_TYPES = [
		{ "value": 0, "text": "Contact" }, { "value": 1, "text": "Staff" }, { "value": 2, "text": "Student" }
	]

	function ImportPicturesViewModel()
	{
		var self = this;
		self.obFileType = ko.observable(UPLOAD_FILE_TYPE.IMAGE);
		self.obUpdateType = ko.observable(UPDATE_TYPE.CHANGE);
		self.files = null;
		self.onFileReadComplete = new TF.Events.Event();
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		self.obDataTypes = ko.observableArray(PICTURE_TYPES);
		self.obSelectedDataType = ko.observable();
		self.obSelectedDataTypeText = ko.observable(PICTURE_TYPES[0].text);
		self.obSelectedDataType.subscribe(self.dataTypeChange, this);
		self.obSelectedPictureHeader = ko.observable();
	}

	ImportPicturesViewModel.prototype.constructor = ImportPicturesViewModel;

	ImportPicturesViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.$element = $(el);
		$('[name="uploadFormat"]').change(function(e)
		{
			self.files = null;
			//disable the confirm button
			self.onFileReadComplete.notify(null);
			self.lazyCreateUploader();
		})
		self.lazyCreateUploader();
		self.obSelectedDataType(self.obDataTypes()[0]);
	};

	ImportPicturesViewModel.prototype.dataTypeChange = function()
	{
		var self = this;
		var text = self.obSelectedDataType().text;
		self.obSelectedPictureHeader("Select " + text + " Picture From:");
	}

	ImportPicturesViewModel.prototype.lazyCreateUploader = function()
	{
		var self = this;
		setTimeout(function()
		{
			$input = $('input[type="file"]');
			$inputResult = $('#input_result')
			$input.val(null)
			$inputResult.val("")
			if (self.obFileType() !== UPLOAD_FILE_TYPE.ZIP)
			{
				$input.attr('accept', 'image/*')
				$input.attr('multiple', 'multiple')
			} else
			{
				$input.removeAttr('multiple')
				$input.attr('accept', '.zip')
			}
		}, 100);
	};

	ImportPicturesViewModel.prototype.IsZipBasedFile = function(fileType)
	{
		if (!fileType || fileType.split('/') < 1)
		{
			return false;
		}
		else
		{
			var subType = fileType.split('/')[1];
			return subType.toLocaleLowerCase().indexOf("zip") >= 0
		}
	}

	ImportPicturesViewModel.prototype.UploadedFileChangeEvent = function(model, e)
	{
		var self = this,
			files = Array.from(e.target.files),
			filterFilesPromise;

		if (files.length === 0 && self.files !== null)
		{
			files = self.files;
		}
		else
		{
			self.files = null;
		}

		if (files.length > 0)
		{
			tf.loadingIndicator.showImmediately();
			switch (self.obFileType())
			{
				case UPLOAD_FILE_TYPE.ZIP:
					if (self.IsZipBasedFile(files[0].type))
					{
						filterFilesPromise = Promise.resolve(files.slice(0, 1));
					}
					else
					{
						filterFilesPromise = Promise.resolve(null);
					}
					break;
				case UPLOAD_FILE_TYPE.IMAGE:
				case UPLOAD_FILE_TYPE.FOLDER:
					filterFilesPromise = self.filterFiles(files);
					break;
			}

			filterFilesPromise.then(function(files)
			{
				var template = "\b" + files.length == 1 ? files[0].name : files.length + " file(s) selected";
				$('#input_result').val(template);
				if (files.length > 0)
				{
					self.files = files;
					self.onFileReadComplete.notify(files);
				}
				tf.loadingIndicator.tryHide();
			}).catch(ex =>
			{
				tf.promiseBootbox.alert("Unsupported file.");
				tf.loadingIndicator.tryHide();
			})
			return;
		}

		self.onFileReadComplete.notify(null);
	};

	ImportPicturesViewModel.prototype.filterFiles = function(files)
	{
		var self = this,
			fileList = files.filter(function(file)
			{
				return file.type.indexOf("image") >= 0;
			});

		return Promise.resolve(fileList);
	};

	ImportPicturesViewModel.prototype.uploadFile = function()
	{
		var self = this,
			sum = 0,
			files = self.files;
		const limitSize = 2 * 1024 * 1024 * 1024;

		if (files && files.length)
		{
			return new Promise(function(resolve, reject)
			{
				var fileData = new FormData();
				files.forEach(function(file, fileIndex)
				{
					sum += file.size;
					fileData.append(file.name, file);
				});

				if (sum > limitSize)
				{
					tf.promiseBootbox.alert("Your total upload size is too large. Please upload under 2GB.").then(function()
					{
						resolve(false);
					})
					return;
				}
				tf.loadingIndicator.show();
				$.ajax({
					url: pathCombine(tf.api.apiPrefixWithoutDatabase(), "RecordPictureFiles?updateType=" + self.obUpdateType() + "&picturetype=" + self.obSelectedDataType().text + '&dbid=' + tf.datasourceManager.databaseId),
					data: fileData,
					type: 'POST',
					cache: false,
					processData: false,
					contentType: false,
					headers: {
						"Token": tf.entStorageManager.get("token", true),
					},
				}).done(function(response)
				{
					var updateInfo = response.Items[0];
					if (!updateInfo.isFileValid)
					{
						tf.promiseBootbox.alert('Invalid file').then(function()
						{
							resolve(false);
						})
						return;
					}
					var recordsToAdd = updateInfo.addIds.length, recordsToUpdate = updateInfo.updateIds.length, recordsToRemove = updateInfo.deleteIds.length,
						duplicatedCount = updateInfo.duplicateRecord, unmatchedCount = updateInfo.unRecognisedRecord, invalidCount = updateInfo.invalidRecord,
						promptMsg = String.format(
							"There are {0} picture(s) to add, {1} picture(s) to update, {2} picture(s) to remove, {3} duplicated, {4} unmatched and {5} invalid picture(s) to ignore. Are you sure you want to apply these changes?",
							recordsToAdd, recordsToUpdate, recordsToRemove, duplicatedCount, unmatchedCount, invalidCount);

					tf.promiseBootbox.yesNo(promptMsg, "Confirm " + self.obSelectedDataType().text + " Picture Changes").then(function(result)
					{
						if (result)
						{
							resolve(result);
							self.updateRecords(updateInfo, false).then(function(res)
							{
								resolve(res);
							});
						}
						else
						{
							self.deleteUploadFiles().then(function(res)
							{
								resolve(false);
							});
						}
					});
				}).fail(function(err)
				{
					let errorInfo = err.responseJSON?.TransfinderMessage;
					errorInfo && tf.promiseBootbox.alert(errorInfo);
					resolve(false);
				}).always(function()
				{
					tf.loadingIndicator.tryHide();
				});
			});
		}

		return Promise.resolve(false);
	};
	ImportPicturesViewModel.prototype.deleteUploadFiles = function()
	{
		return tf.promiseAjax.delete(pathCombine(tf.api.apiPrefixWithoutDatabase(), "RecordPictureFiles"));
	}
	ImportPicturesViewModel.prototype.updateRecords = function(updateInfo, cancelled)
	{
		var self = this,
			updateType = cancelled === true ? "cancel" : self.obUpdateType(),
			databaseId = tf.datasourceManager.databaseId;
		return tf.promiseAjax.put(pathCombine(tf.api.apiPrefixWithoutDatabase(), "RecordPictureFiles?picturetype=" + self.obSelectedDataType().text), {
			paramData: {
				updateType: updateType,
				dbid: databaseId
			},
			data: updateInfo
		}).then(function(res)
		{
			if (cancelled === true) return false;

			if (res)
			{
				self.pageLevelViewModel.popupSuccessMessage(self.obSelectedDataType().text + " Pictures are updated succesfully");
			} else
			{
				self.pageLevelViewModel.popupErrorMessage("Failed to update " + self.obSelectedDataType().text + " Pictures");
			}
			return res;
		});
	};

})()