﻿(function()
{
	createNamespace("TF.Modal.Map").TripSymbolModalViewModel = TripSymbolModalViewModel;

	function TripSymbolModalViewModel(tripId, mapViewModel)
	{
		TF.Modal.BaseModalViewModel.call(this);

		this.title("Edit Trip Symbol");
		this.sizeCss = "modal-sm";
		this.contentTemplate("controls/EditTripSymbol");
		this.buttonTemplate("modal/positivenegative");
		this.tripId = tripId;
		this.mapViewModel = mapViewModel;

		this.editTripSymbolViewModel = new TF.Fragment.EditTripSymbolViewModel();
		this.data(this.editTripSymbolViewModel);
	};

	TripSymbolModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	TripSymbolModalViewModel.prototype.constructor = TripSymbolModalViewModel;

	TripSymbolModalViewModel.prototype.initialize = function() { };

	TripSymbolModalViewModel.prototype.positiveClick = function()
	{
		var tripSymbol = this.editTripSymbolViewModel;

		this.positiveClose();

		var data = {
			tripId: this.tripId,
			color: tripSymbol.tripColor,
			style: tripSymbol.tripStyle,
			width: tripSymbol.tripWidth,
			alpha: tripSymbol.tripAlpha
		};

		this.mapViewModel._setTripSymbol(data);
	};

	TripSymbolModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose();
	};

	TripSymbolModalViewModel.prototype.dispose = function() { };
})();