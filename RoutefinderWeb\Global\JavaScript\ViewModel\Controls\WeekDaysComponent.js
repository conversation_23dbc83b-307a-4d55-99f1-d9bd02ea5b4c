(function()
{
	function WeekdaysComponentViewModel(params)
	{
		this.params = params;
		this.weekdaysName = this.params.name || 'weekDays';
		this.initCss();
		this.initVariable();
		this.mondayToFriday = ko.pureComputed({
			read: () => this.monday() && this.tuesday() && this.wednesday() && this.thursday() && this.friday(),
			write: (v) =>
			{
				this.params.data.monday(v);
				this.params.data.tuesday(v);
				this.params.data.wednesday(v);
				this.params.data.thursday(v);
				this.params.data.friday(v);
			}
		});
		this.enableCheckbox();
		this.params.onCreated && this.params.onCreated();
	}

	WeekdaysComponentViewModel.prototype.constructor = WeekdaysComponentViewModel;

	WeekdaysComponentViewModel.prototype.hasValue = function(obj)
	{
		return typeof obj === 'undefined' || obj;
	}

	WeekdaysComponentViewModel.prototype.initCss = function()
	{
		this.col1 = this.params.col ? `col-xs-${this.params.col.span1}` : `col-xs-6`;
		this.col2 = this.params.col ? `col-xs-${this.params.col.span2}` : `col-xs-6`;
		this.col3 = this.params.col ? `col-xs-${this.params.col.span3}` : `col-xs-6`;
		this.col4 = this.params.col ? `col-xs-${this.params.col.span4}` : `col-xs-6`;
	}

	WeekdaysComponentViewModel.prototype.initVariable = function()
	{
		if (typeof this.params.data.monday === 'function')
		{
			this.params.data.monday = this.monday = this.params.data.Mon || this.params.data.monday;
			this.params.data.tuesday = this.tuesday = this.params.data.Tues || this.params.data.tuesday;
			this.params.data.wednesday = this.wednesday = this.params.data.Wed || this.params.data.wednesday;
			this.params.data.thursday = this.thursday = this.params.data.Thurs || this.params.data.thursday;
			this.params.data.friday = this.friday = this.params.data.Fri || this.params.data.friday;
			this.params.data.saturday = this.saturday = this.params.data.Sat || this.params.data.saturday;
			this.params.data.sunday = this.sunday = this.params.data.Sun || this.params.data.sunday;
		} else
		{
			this.params.data.monday = this.monday = ko.observable(!!this.hasValue(this.params.data.monday));
			this.params.data.tuesday = this.tuesday = ko.observable(!!this.hasValue(this.params.data.tuesday));
			this.params.data.wednesday = this.wednesday = ko.observable(!!this.hasValue(this.params.data.wednesday));
			this.params.data.thursday = this.thursday = ko.observable(!!this.hasValue(this.params.data.thursday));
			this.params.data.friday = this.friday = ko.observable(!!this.hasValue(this.params.data.friday));
			this.params.data.saturday = this.saturday = ko.observable(!!this.params.data.saturday);
			this.params.data.sunday = this.sunday = ko.observable(!!this.params.data.sunday);
		}
	}

	WeekdaysComponentViewModel.prototype.enableCheckbox = function()
	{
		if (this.params.enable !== undefined)
		{
			this.mondayToFridayEnable = ko.observable(this.params.enable);
			this.mondayEnable = ko.observable(this.params.enable);
			this.tuesdayEnable = ko.observable(this.params.enable);
			this.wednesdayEnable = ko.observable(this.params.enable);
			this.thursdayEnable = ko.observable(this.params.enable);
			this.fridayEnable = ko.observable(this.params.enable);
			this.saturdayEnable = ko.observable(this.params.enable);
			this.sundayEnable = ko.observable(this.params.enable);
		} else
		{
			TF.DayOfWeek.allSortByValue.forEach((item, index) =>
			{
				var dayOfWeek = index, key = item.toLowerCase();
				this[key + "Enable"] = ko.pureComputed(v =>
				{
					var start = this.params.data.startDate(),
						end = this.params.data.endDate();
					if (!start || !end)
					{
						return true;
					}

					start = new Date(start);
					end = new Date(end);
					start = new Date(start.getFullYear(), start.getMonth(), start.getDate());
					for (var i = 0; start <= end && i < 7; start.setDate(start.getDate() + 1))
					{
						i++;
						if (start.getDay() == dayOfWeek)
						{
							return true;
						}
					}

					return false;
				});

				this[key + "Enable"].subscribe(() =>
				{
					if (!this[key + "Enable"]())
					{
						this.params.data[key](false);
					} else if (key !== 'saturday' && key !== 'sunday')
					{
						this.params.data[key](true);
					}
				});

				if (key !== 'saturday' && key !== 'sunday')
				{
					this.params.data[key](this[key + "Enable"]() && this.params.data[key]());
				}
			});
			this.mondayToFridayEnable = ko.pureComputed(() =>
			{
				for (var i = 1; i <= 5; i++)
				{
					var key = TF.DayOfWeek.allSortByValue[i].toLowerCase();
					if (!this[key + 'Enable']())
					{
						return false;
					}
				}

				return true;
			});
		}
	}

	WeekdaysComponentViewModel.prototype.dispose = function()
	{
		this.mondayToFriday.dispose();
		this.mondayToFridayEnable.dispose && this.mondayToFridayEnable.dispose();
		this.mondayEnable.dispose && this.mondayEnable.dispose();
		this.tuesdayEnable.dispose && this.tuesdayEnable.dispose();
		this.wednesdayEnable.dispose && this.wednesdayEnable.dispose();
		this.thursdayEnable.dispose && this.thursdayEnable.dispose();
		this.fridayEnable.dispose && this.fridayEnable.dispose();
		this.sundayEnable.dispose && this.sundayEnable.dispose();
		this.saturdayEnable.dispose && this.saturdayEnable.dispose();
	}

	ko.components.register('week-days-component', {
		template: `
		<div class="week-days" data-bind="attr:{name:weekdaysName}">
			<div class="col-xs-24" style="padding:5px 0">
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col1">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:mondayToFriday,enable:mondayToFridayEnable" /> Mon-Fri
					</label>
				</div>
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col2">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:monday,enable:mondayEnable" /> Monday
					</label>
				</div>
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col3">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:tuesday,enable:tuesdayEnable" /> Tuesday
					</label>
				</div>
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col4">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:wednesday,enable:wednesdayEnable" /> Wednesday
					</label>
				</div>
			</div>
			<div class="col-xs-24" style="padding:0px;">
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col1">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:thursday,enable:thursdayEnable" /> Thursday
					</label>
				</div>
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col2">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:friday,enable:fridayEnable" /> Friday
					</label>
				</div>
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col3">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:saturday,enable:saturdayEnable" /> Saturday
					</label>
				</div>
				<div class="checkbox" style="padding:0px;margin:0px" data-bind="css:col4">
					<label>
						<input type="checkbox" name="weekdays" data-bind="checked:sunday,enable:sundayEnable" /> Sunday
					</label>
				</div>
			</div>
		<div>
		`,
		viewModel: WeekdaysComponentViewModel
	});
})();