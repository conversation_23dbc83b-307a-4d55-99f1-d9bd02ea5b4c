(function()
{
	createNamespace("TF").KendoHackHelper = KendoHackHelper;

	function KendoHackHelper()
	{
		var KendoMobile = kendo.mobile;
		var BaseKendoMobileViewEngineAppend = kendo.ViewEngine.prototype.append;
		kendo.ViewEngine.prototype.append = function(html, url)
		{
			html = html.replace(/data-role/g, "data-" + kendo.ns + "role");
			return BaseKendoMobileViewEngineAppend.call(this, html, url);
		};

		var BaseKendoMobilePaneWarp = kendo.Pane.wrap;
		kendo.Pane.wrap = function(element)
		{
			if (!element.is(kendo.roleSelector("view")))
			{
				element = element.wrap('<div data-' + kendo.ns + 'role="view" data-' + kendo.ns + 'stretch="true"></div>').parent();
			}
			return BaseKendoMobilePaneWarp.call(this, element);
		}

		kendo.date.isToday = function(date)
		{
			return utcToClientTimeZone(moment.utc()).format("YYYY/MM/DD") === moment(date).format("YYYY/MM/DD");
		}


		var KendoColorGradientPrototype = null,
			KendoFlatColorPickerPrototype = null,
			KendoColorPickerPrototype = null,
			KendoDropDownListPrototype = null,
			KendoUploadPrototype = null,
			KendoRangeSliderWidgetPrototype = null,
			KendoGridPrototype = null,
			kendoSchedulerPrototype = null;

		window.kendo.widgets.map(function(widget, idx)
		{
			if (widget.name === 'kendoColorGradient')
				KendoColorGradientPrototype = widget.widget.prototype;
			if (widget.name === 'kendoFlatColorPicker')
				KendoFlatColorPickerPrototype = widget.widget.prototype;
			if (widget.name === 'kendoColorPicker')
				KendoColorPickerPrototype = widget.widget.prototype;
			if (widget.name === 'kendoDropDownList')
				KendoDropDownListPrototype = widget.widget.prototype;
			if (widget.name === 'kendoUpload')
				KendoUploadPrototype = widget.widget.prototype;
			if (widget.name === 'kendoRangeSlider')
				KendoRangeSliderWidgetPrototype = widget.widget.prototype;
			if (widget.name === 'kendoGrid')
				KendoGridPrototype = widget.widget.prototype;
			if (widget.name === 'kendoScheduler')
				kendoSchedulerPrototype = widget.widget.prototype;
		});

		var Const = {
			recentTileWidth: TF.isPhoneDevice ? 46 : 24,
			recentTileHeight: TF.isPhoneDevice ? 46 : 24,
			recentWidth: 220,
			recentTileSpaceWidth: TF.isPhoneDevice ? 10 : 5,
			recentMaximumOfTileShow: 7,
			recentMaxColumns: 50,
			recentScrollStart: TF.isMobileDevice ? "touchstart" : "mousedown",
			recentScrollEnd: TF.isMobileDevice ? "touchend" : "mouseup"
		};

		var paletteColorArray = [
			"#ffffff", "#000000", "#d6ecff", "#4e5b6f", "#7fd13b", "#ea157a", "#feb80a", "#00addc", "#738ac8", "#1ab39f",
			"#f2f2f2", "#7f7f7f", "#a7d6ff", "#d9dde4", "#e5f5d7", "#fad0e4", "#fef0cd", "#c5f2ff", "#e2e7f4", "#c9f7f1",
			"#d8d8d8", "#595959", "#60b5ff", "#b3bcca", "#cbecb0", "#f6a1c9", "#fee29c", "#8be6ff", "#c7d0e9", "#94efe3",
			"#bfbfbf", "#3f3f3f", "#007dea", "#8d9baf", "#b2e389", "#f272af", "#fed46b", "#51d9ff", "#aab8de", "#5fe7d5",
			"#a5a5a5", "#262626", "#003e75", "#3a4453", "#5ea226", "#af0f5b", "#c58c00", "#0081a5", "#425ea9", "#138677",
			"#7f7f7f", "#0c0c0c", "#00192e", "#272d37", "#3f6c19", "#750a3d", "#835d00", "#00566e", "#2c3f71", "#0c594f"
		];

		KendoFlatColorPickerPrototype.setOptions({ extend: true, sticky: false, hack: true });
		KendoColorPickerPrototype.setOptions({ preview: false, extend: true, sticky: false, hack: true });
		function changeOpacity(length, maxLength, $previous, $next)
		{
			if (length <= 0 || maxLength <= 0)
			{
				$previous.css("opacity", "0.3")
			}
			else
			{
				$previous.css("opacity", "0.7")
			}
			if (length >= maxLength)
			{
				$next.css("opacity", "0.3")
			}
			else
			{
				$next.css("opacity", "0.7")
			}
		};

		function getValidColors(colors)
		{
			let inValidColors = [undefined, null, '#undefined', '#null'];
			return (colors || []).filter(c => !inValidColors.includes((c || "").toLowerCase()));
		}

		let _updateUI = KendoFlatColorPickerPrototype._updateUI;
		KendoFlatColorPickerPrototype._updateUI = function(color, dontChangeInput)
		{
			let self = this;
			_updateUI.call(self, color, dontChangeInput);
			self.element.find(".k-input.k-textbox").css("background-color", self.value() || self.element.find(".k-input.k-textbox").find("input").val());

			if (self.options.extend)
			{
				let input = $(self.element.find(".k-colorgradient-inputs input")[0]);

				if (self.options.treatWhiteAsTransparent)
				{
					if (self.value() === '#fffffe')
					{
						input.val('None');
						input.parent().addClass('no-border');
						$(".data-block-appearance-menu li.border").addClass('no-border');
						$(`[aria-controls=${self.element.closest(".k-colorpicker-popup").attr("id")}]`).find(".k-color-preview-mask").css("background-color", "");
					}
					else
					{
						input.parent().removeClass('no-border');
						$(".data-block-appearance-menu li.border").removeClass('no-border');
					}
				}

				if (self._recentPaletteColor && self.element.find(".recentPalette").data("kendoColorPalette") && !self.dontChangeRecent)
				{
					let rebuildRecentColors = !self.options.sticky;
					if (rebuildRecentColors)
					{
						rebuildRecentColors = false;
						if ($.cookie(self.options.cookieName) && JSON.parse($.cookie(self.options.cookieName)).colorArray)
						{
							let colorArray = JSON.parse($.cookie(self.options.cookieName)).colorArray;
							if (self._recentPaletteColor.length != colorArray.length)
							{
								self._recentPaletteColor = getValidColors(colorArray);
								rebuildRecentColors = true;
							}
						}
					}
					let colorStr = self.value(),
						index = self._recentPaletteColor.indexOf(colorStr),
						recentCount = self._recentPaletteColor.length,
						$previous = self.element.find(".image-previous"),
						$next = self.element.find(".image-next");
					if (index == -1 || rebuildRecentColors)
					{
						if (recentCount != Const.recentMaxColumns || rebuildRecentColors)
						{
							if (!self._recentScroll && recentCount >= Const.recentMaximumOfTileShow)
							{
								$previous.css("display", "block");
								$next.css("display", "block");
								self._recentScroll = true;
							}

							if (!rebuildRecentColors)
								self._recentPaletteColor.push(colorStr);

							if (self.options.treatWhiteAsTransparent)
							{
								self._recentPaletteColor = handleRecentPaletteColors(self._recentPaletteColor);
							}

							self.element.find(".recentPalette").data("kendoColorPalette").destroy();
							self.element.find(".recentPalette").html('')
							self.element.find(".recentPalette").kendoColorPalette({
								tileSize: {
									width: Const.recentTileWidth,
									height: Const.recentTileHeight
								},
								palette: self._recentPaletteColor,
								columns: Const.recentMaxColumns,
								change: function(e)
								{
									setTimeout(function()
									{
										let color = kendo.parseColor(e.value, true);
										if (color)
										{
											self.dontChangeRecent = true;
											self._updateUI(color, true);
											self.paletteClick();
										}
									}, 10);
								}
							});
							recentCount = self._recentPaletteColor.length;
							self.element.find(".recentPalette .k-colorpalette-table").css("width", recentCount * (Const.recentTileHeight + Const.recentTileSpaceWidth) + Const.recentTileSpaceWidth);
						}
						else
						{
							self.element.find(".recentPalette .k-colorpalette-table .k-colorpalette-tile").removeClass("k-selected");
							if (!self.options.sticky)
							{
								if (!$.cookie(self.options.cookieName))
								{
									setColorCookie(self.options.cookieName, null, [colorStr]);
								}
							}
							self.dontChangeRecent = false;
							return;
						}
					}
					index = index == -1 ? recentCount - 1 : index;
					self.element.find(".recentPalette .k-colorpalette-table .k-colorpalette-tile").removeClass("k-selected");
					$(self.element.find(".recentPalette .k-colorpalette-table .k-colorpalette-tile")[index]).addClass("k-selected");
					let recentPalette = self.element.find(".recentPalette")[0],
						length = (Const.recentTileHeight + Const.recentTileSpaceWidth) * index,
						maxLength = recentPalette.scrollWidth - Const.recentWidth;
					if (recentCount > Const.recentMaximumOfTileShow)
					{
						changeOpacity(length, maxLength, $previous, $next);
					}
					recentPalette.scrollLeft = length;
					if (!self.options.sticky)
					{
						setColorCookie(self.options.cookieName, null, self._recentPaletteColor);
					}
				}
				else if (self.dontChangeRecent)
				{
					let colorStr = self.value();
					$(self.element.find(".k-colorpalette")[0]).data("kendoColorPalette").value(colorStr);
				}

				if (self.options.treatWhiteAsTransparent)
				{
					self.element.addClass('replace-white-with-transparent');
				}

				self.dontChangeRecent = false;
			}
		}

		let _changeView = KendoFlatColorPickerPrototype._changeView;
		KendoFlatColorPickerPrototype._changeView = function(view)
		{
			let self = this;

			if (view === "palette" && JSON.stringify(self.options.views) === JSON.stringify(["palette"]))
			{
				self.element.addClass("only-palette");
				_changeView.call(this, view);
				return;
			}

			if (view === "palette")
			{
				if (self.options.treatWhiteAsTransparent)
				{
					paletteColorArray[0] = "#fffffe";
				}
				else
				{
					paletteColorArray[0] = "#ffffff";
				}

				self.setOptions({ palette: paletteColorArray });
			}

			_changeView.call(this, view);
			if (view === "gradient")
			{
				self.element.find(".k-colorgradient-inputs .k-colorgradient-toggle-mode").remove();
				let $label = $(self.element.find(".k-colorgradient-inputs .k-colorgradient-input-label")[0]);
				$label.text("Hex Color");
				$label.prependTo($label.parent());
				self.element.find(".k-input.k-textbox").css("background-color", self.value() || self.element.find(".k-input.k-textbox").find("input").val());
				if (self.options.treatWhiteAsTransparent)
				{
					let input = self.element.find(".k-input.k-textbox input");
					input.val("None");
					if (self.value() === '#fffffe')
					{
						self.element.find(".k-input.k-textbox").css("background-color", "none");
						input.val('None');
						input.parent().addClass('no-border');
						$(".data-block-appearance-menu li.border").addClass('no-border');
					}
					else
					{
						input.parent().removeClass('no-border');
						$(".data-block-appearance-menu li.border").removeClass('no-border');
					}
				}
			}
			else if (view === "palette")
			{
				if (!self.element.find(".recent-group").length)
				{
					self.element.find(".k-coloreditor-views").append(`
					<div class="form-group recent-group">
					<div class="color-header">Recent Colors</div>
					<div class="recent-colors">
					<div class="image-button image-previous"></div><div class="recentPalette"></div><div class="image-button image-next"></div>
					</div></div>`);
				}

				if (self.options.sticky)
				{
					self.options.stickyName = self.options.stickyName || "colorhistory";
					self._recentPaletteColor = [];
					var p1 = Promise.resolve();
				}
				else
				{
					self.options.cookieName = self.options.cookieName || "colorhistory";

					if ($.cookie(self.options.cookieName) && JSON.parse($.cookie(self.options.cookieName)).colorArray)
					{
						self._recentPaletteColor = getValidColors(JSON.parse($.cookie(self.options.cookieName)).colorArray);
						var p1 = Promise.resolve(true);
					}
					else
					{
						self._recentPaletteColor = [];
						var p1 = Promise.resolve(true);
					}
				}

				p1.then(function()
				{
					self._recentPaletteColor = self._recentPaletteColor || [self.value()];
					let preRecentPaletteColor = [];
					let colorNotChangeByOtherWay = false;
					$.each(self._recentPaletteColor, function(index, item)
					{
						if (item?.indexOf("#") < 0)
						{
							self._recentPaletteColor[index] = "#" + self._recentPaletteColor[index];
						}
					});
					if (self._recentPaletteColor.length < Const.recentMaxColumns)
					{
						self._recentPaletteColor.map(function(item)
						{
							if (item == self.value())
							{
								colorNotChangeByOtherWay = true;
							}
						});
						if (!colorNotChangeByOtherWay)
						{
							for (let recentColor of self._recentPaletteColor)
							{
								preRecentPaletteColor.push(recentColor);
							}
							self._recentPaletteColor = preRecentPaletteColor.concat([self.value()]);
							if (self.options.sticky)
							{
							}
							else
							{
								setColorCookie(self.options.cookieName, null, self._recentPaletteColor);
							}
						}
					}

					if (self.options.treatWhiteAsTransparent)
					{
						self._recentPaletteColor = handleRecentPaletteColors(self._recentPaletteColor);
					}

					self.element.find(".recentPalette").data("kendoColorPalette")?.destroy();
					self.element.find(".recentPalette").html('');
					self.element.find(".recentPalette").kendoColorPalette({
						tileSize: {
							width: Const.recentTileWidth,
							height: Const.recentTileHeight
						},
						palette: self._recentPaletteColor,
						columns: Const.recentMaxColumns,
						change: function(e)
						{
							setTimeout(function()
							{
								let color = kendo.parseColor(e.value, true);
								if (color)
								{
									self.dontChangeRecent = true;
									self._updateUI(color, true);
									self.paletteClick();
								}
							}, 10);
						}
					});

					let recentCount = self._recentPaletteColor.length;
					self.element.find(".recentPalette .k-colorpalette-table").css("width", (recentCount * (Const.recentTileHeight + Const.recentTileSpaceWidth) + Const.recentTileSpaceWidth) + "px");
					let recentPalette = self.element.find(".recentPalette")[0],
						$previous = self.element.find(".image-previous"),
						$next = self.element.find(".image-next");
					if (recentCount <= Const.recentMaximumOfTileShow)
					{
						$previous.css("display", "none");
						$next.css("display", "none");
					}
					else
					{
						self._recentScroll = true;
					}

					let previousInterval, nextInterval;
					$previous.on("click", function()
					{
						let length = recentPalette.scrollLeft - (Const.recentTileHeight + Const.recentTileSpaceWidth), maxLength = recentPalette.scrollWidth - Const.recentWidth;
						changeOpacity(length, maxLength, $previous, $next);
						recentPalette.scrollLeft = length;
					}).on(Const.recentScrollStart, function()
					{
						previousInterval = setInterval(function()
						{
							let length = recentPalette.scrollLeft - (Const.recentTileHeight + Const.recentTileSpaceWidth), maxLength = recentPalette.scrollWidth - Const.recentWidth;
							changeOpacity(length, maxLength, $previous, $next);
							recentPalette.scrollLeft = length;
						}, 100)
					}).on(Const.recentScrollEnd, function()
					{
						clearInterval(previousInterval);
					}).on("mouseout", function()
					{
						clearInterval(previousInterval);
					});
					$next.on("click", function()
					{
						let length = recentPalette.scrollLeft + (Const.recentTileHeight + Const.recentTileSpaceWidth), maxLength = recentPalette.scrollWidth - Const.recentWidth;
						if (length >= maxLength)
						{
							length = maxLength;
						}
						changeOpacity(length, maxLength, $previous, $next);
						recentPalette.scrollLeft = length;
					}).on(Const.recentScrollStart, function()
					{
						nextInterval = setInterval(function()
						{
							let length = recentPalette.scrollLeft + (Const.recentTileHeight + Const.recentTileSpaceWidth), maxLength = recentPalette.scrollWidth - Const.recentWidth;
							if (length >= maxLength)
							{
								length = maxLength;
							}
							changeOpacity(length, maxLength, $previous, $next);
							recentPalette.scrollLeft = length;
						}, 100)
					}).on(Const.recentScrollEnd, function()
					{
						clearInterval(nextInterval);
					}).on("mouseout", function()
					{
						clearInterval(nextInterval);
					});
					if (TF.isMobileDevice)
					{
						self.element.find(".recentPalette").data("kendoColorPalette").value(self.value());
						$(self.element.find(".k-colorpalette")[0]).data("kendoColorPalette").value(self.value());
					}
				});
			}
		};

		function setColorCookie(cookieName, activeColor, colorArray)
		{
			var colorCookie = {};
			if ($.cookie(cookieName))
			{
				colorCookie = JSON.parse($.cookie(cookieName));;
			}
			if (activeColor)
			{
				colorCookie.activeColor = activeColor;
			}
			if (colorArray)
			{
				colorCookie.colorArray = colorArray
			}
			$.cookie(cookieName, JSON.stringify(colorCookie));
		};

		function handleRecentPaletteColors(colors)
		{
			colors = colors.map(function(color)
			{
				return color && color.toLowerCase() === "#none" ? "#fffffe" : color;
			});

			var recentPaletteColorArray = [];
			colors.forEach(function(color)
			{
				if (recentPaletteColorArray.indexOf(color) == -1)
				{
					recentPaletteColorArray.push(color);
				}
			});

			return getValidColors(recentPaletteColorArray);
		}

		KendoDropDownListPrototype._keydown = KendoDropDownListPrototype._keydown.createSequence(function(e)
		{
			if (this.options.hack == false)
			{
				return;
			}

			e.stopPropagation();

			if (e.keyCode === $.ui.keyCode.LEFT ||
				e.keyCode === $.ui.keyCode.RIGHT ||
				e.keyCode === $.ui.keyCode.UP ||
				e.keyCode === $.ui.keyCode.DOWN)
			{
				this.listView.element.find(`.${TF.KendoClasses.STATE.HOVER}`).removeClass(TF.KendoClasses.STATE.HOVER);
			}
		});

		/**
		 * The event of color blocks which are in Swatch palette and Recent Colors palette click
		 * @return {void}
		 */
		KendoFlatColorPickerPrototype.paletteClick = function()
		{
		};

		KendoUploadPrototype._onInputChange = KendoUploadPrototype._onInputChange.createInterceptor(function()
		{
			this._module.postFormData = function(url, data, fileEntry, xhr)
			{
				var module = this;
				fileEntry.data('request', xhr);
				xhr.addEventListener('load', function(e)
				{
					module.onRequestSuccess.call(module, e, fileEntry);
				}, false);
				xhr.addEventListener('error', function(e)
				{
					module.onRequestError.call(module, e, fileEntry);
				}, false);
				xhr.upload.addEventListener('progress', function(e)
				{
					module.onRequestProgress.call(module, e, fileEntry);
				}, false);
				xhr.open('POST', url, true);
				xhr.withCredentials = this.upload.options.async.withCredentials;
				var accept = this.upload.options.async.accept;
				if (accept)
				{
					xhr.setRequestHeader('Accept', accept);
				}
				var token = this.upload.options.async.token;
				if (token)
				{
					xhr.setRequestHeader('Token', token);
				}
				xhr.send(data);
			}
		});

		function round(value, precision)
		{
			var power = pow(precision);
			return Math.round(value * power) / power;
		}

		function pow(p)
		{
			if (p)
			{
				return Math.pow(10, p);
			}
			return 1;
		}

		function removeFraction(value)
		{
			return value * 10000;
		}

		KendoRangeSliderWidgetPrototype._setItemsWidth = function(pixelWidths)
		{
			const TICK_SELECTOR = '.k-tick';
			var that = this, options = that.options, first = 0, last = pixelWidths.length - 1, items = that.wrapper.find(TICK_SELECTOR), i, paddingTop = 0, bordersWidth = 2, count = items.length, selection = 0;
			const wrapperWidth = that.wrapper.width();
			const cellWidth = Math.floor(wrapperWidth / (count - 1));

			for (i = 0; i < count; i++)
			{
				if (i === 0 || i === count - 1)
				{
					$(items[i]).css('width', `calc(100% / ${count - 1} / 2)`)
					if (i === count - 1)
					{
						$(items[i]).css('background-position-x', '90%');
					}
					//$(items[i]).width(Math.floor((wrapperWidth - cellWidth * (count - 2)) / 2));
				}
				else
				{
					$(items[i]).css('width', `calc(100% / ${count - 1})`)
					//$(items[i]).width(cellWidth);
				}
			}

			if (that._isHorizontal)
			{
				that.wrapper.find(TICK_SELECTOR + ':first').addClass('k-first');
				that.wrapper.find(TICK_SELECTOR + ':last').addClass('k-last');
			} else
			{
				$(items[last]).addClass('k-first')[that._sizeFn](pixelWidths[last]);
				$(items[first]).addClass('k-last')[that._sizeFn](pixelWidths[last - 1]);
			}
			if (that._distance() % options.smallStep !== 0 && !that._isHorizontal)
			{
				for (i = 0; i < pixelWidths.length; i++)
				{
					selection += pixelWidths[i];
				}
				paddingTop = that._maxSelection - selection;
				paddingTop += parseFloat(that._trackDiv.css(that._position), 10) + bordersWidth;
				that.wrapper.find('.k-slider-items').css('padding-top', paddingTop);
			}
		}

		KendoRangeSliderWidgetPrototype._sliderItemsInit = function()
		{
			var that = this, options = that.options;
			const previewOptions = JSON.parse(that._prevOptionString || '{}');
			if (that.element.closest("div.on-time-report-stack-item").length !== 0
				&& that._prevWrapperWidth === that.wrapper.width()
				&& previewOptions.max === options.max
				&& previewOptions.min === options.min)
			{
				return; // skip item re-init if no option changes to improve experience
			}

			that._prevOptionString = JSON.stringify(options);
			that._prevWrapperWidth = that.wrapper.width();
			var sizeBetweenTicks = that._maxSelection / ((options.max - options.min) / options.smallStep);
			var pixelWidths = that._calculateItemsWidth(Math.floor(removeFraction(that._distance()) / removeFraction(options.smallStep)));
			const scale = calculateScale.bind(that)();
			if (options.tickPlacement != 'none')
			{
				$(this.element).parent().find('.k-slider-items').remove();
				that._trackDiv.before(createSliderItems.bind(that)(scale[0], that._distance()));
				that._setItemsWidth(pixelWidths);
				// that._setItemsTitle();

			}
			that._calculateSteps(pixelWidths);
			if (options.tickPlacement != 'none' && options.largeStep >= options.smallStep)
			{
				that._setItemsLargeTickAndTitle(scale); // kendo only invoke method that._setItemsLargeTick
			}

			function calculateScale()
			{
				const wrapperWidth = this.wrapper.width()
				const observationWindow = Math.floor(this._distance() / 2);
				if (observationWindow <= 60) // handle 30/60/120
				{
					if (wrapperWidth >= 490)
					{
						return [1, 5];
					}
					else
					{
						return [5, 10];
					}
				}
				else // handle 240
				{
					if (wrapperWidth >= 600)
					{
						return [1, 10];
					}
					else
					{
						return [10, 20];
					}
				}
			}

			function createSliderItems(smallStep, distance)
			{
				var result = '<ul class=\'k-reset k-slider-items\'>', count = Math.floor(round(distance / smallStep)) + 1, i;
				for (i = 0; i < count; i++)
				{
					result += '<li class=\'k-tick\' role=\'presentation\'>&nbsp;</li>';
				}
				result += '</ul>';
				return result;
			}
		};

		KendoRangeSliderWidgetPrototype._setItemsLargeTickAndTitle = function(scale)
		{
			const TICK_SELECTOR = '.k-tick';
			var that = this, options = that.options, items = that.wrapper.find(TICK_SELECTOR), i = 0, item, value;
			const times = scale[1] / scale[0];
			const itemsLen = items.length;
			items.each((index, liItem) =>
			{
				const title = -((itemsLen - 1) / 2 * scale[0] - index * scale[0])
				$(liItem).attr('title', title);
				$(liItem).css('line-height', $(liItem)[that._sizeFn]() + 'px');
				if (index % times === 0)
				{
					$(liItem).addClass('k-tick-large').html(`<span class='k-label'>${title}</span>`);
				}
			});

			if (removeFraction(options.largeStep) % removeFraction(options.smallStep) === 0 || that._distance() / options.largeStep >= 3)
			{
				if (!that._isHorizontal && !that._isRtl)
				{
					items = $.makeArray(items).reverse();
				}

				for (i = 0; i < items.length; i++)
				{
					item = $(items[i]);
					value = that._values[i];
					var valueWithoutFraction = round(removeFraction(value - this.options.min));
					if (valueWithoutFraction % removeFraction(options.smallStep) === 0 && valueWithoutFraction % removeFraction(options.largeStep) === 0)
					{
						if (i % scale[1] === 0)
						{
							item.addClass('k-tick-large').html(`<span class='k-label'>${item.attr('title')}</span>`);
						}

						if (i !== 0 && i !== items.length - 1)
						{
							item.css('line-height', item[that._sizeFn]() + 'px');
						}
					}
				}
			}
		}

		kendoSchedulerPrototype._processHandlers = function(defaults)
		{
			var that = this;

			that._pdfClickHandler = (e) =>
			{
				e.preventDefault();
				that.saveAsPDF();
			};

			that._createClickHandler = (e) =>
			{
				e.preventDefault();
				that.addEvent();
			};

			that._calendarClickHandler = that._currentClickHandler = (e) =>
			{
				e.preventDefault();
				that._showCalendar(e.target);
			};

			that._todayClickHandler = (e) =>
			{
				e.preventDefault();

				var timezone = that.options.timezone,
					currentDate = new Date(),
					date;

				if (timezone)
				{
					var timezoneOffset = kendo.timezone.offset(currentDate, timezone);
					date = kendo.timezone.convert(currentDate, currentDate.getTimezoneOffset(), timezoneOffset);
				} else
				{
					date = currentDate;
				}

				const clientNow = moment(utcToClientTimeZone(moment.utc()).format("MM/DD/YYYY hh:mm:ss A")).toDate()
				if (!that.trigger("navigate", { view: that._selectedViewName, action: "today", date: clientNow }))
				{
					that.date(clientNow);
				}
			};

			that._previousClickHandler = (e) =>
			{
				e.preventDefault();

				var date = that.view().previousDate();

				if (!that.trigger("navigate", { view: that._selectedViewName, action: "previous", date: date }))
				{
					that.date(date);
				}
			};

			that._nextClickHandler = (e) =>
			{
				e.preventDefault();

				var date = that.view().nextDate();

				if (!that.trigger("navigate", { view: that._selectedViewName, action: "next", date: date }))
				{
					that.date(date);
				}
			};

			that._refreshClickHandler = (e) =>
			{
				e.preventDefault();

				var name = that.view().name;

				if (!that.trigger("navigate", { view: name, action: "changeView", date: that.date() }))
				{
					that.view(name);
				}
			};

			that._viewClickHandler = (e) =>
			{
				/*
				 * old code: var name = e.target.attr(kendo.attr("name")); 
				 * After kendo upgraded, seems kendo didn't use custom prefix defined in ko.ns to create element attribute, but kendo.attr("name") will append the suffix,
				 * they're not match so hack here use raw attribute name without suffix to match attribute.
				 */
				var name = e.target.attr("data-name");

				if (!that.trigger("navigate", { view: name, action: "changeView", date: that.date() }))
				{
					that.view(name);
				}
			};

			Object.values(defaults).map(t =>
			{
				if (t.name)
				{
					t.click = that["_" + t.name + "ClickHandler"];
				}
			});
		}

		kendoSchedulerPrototype._selectView = function(name)
		{
			var that = this;
			const MIN_SCREEN = "(min-width: 1024px)";
			if (name && that.views[name])
			{
				if (that._selectedView)
				{
					that._unbindView(that._selectedView);
				}

				that._selectedView = that._renderView(name);
				that._selectedViewName = name;

				if (this._initialSize)
				{
					this._initialSize = false;
					this._onMediaChange(window.matchMedia(MIN_SCREEN));
				}

				if (that._viewsCount > 1 && !that._isMobile())
				{
					// old code: var viewElementToSelect = that.toolbar.find("[" + kendo.attr("name") + "=" + name + "]");
					var viewElementToSelect = that.toolbar.find(`[data-name=${name}]`);
					var viewsDropdown = that.toolbar.find(".k-views-dropdown");
					var viewsGroupEl = viewElementToSelect.closest(".k-button-group");
					var viewsButtonGroup = viewsGroupEl.data("kendoButtonGroup");

					viewsDropdown.val(name);

					if (viewsButtonGroup)
					{
						viewsButtonGroup.select(viewElementToSelect);
					}
				} else if (that._viewsCount > 1)
				{
					var viewSelect = that.toolbar.find(".k-scheduler-mobile-views");

					viewSelect.find("[value=" + name.replace(/\./g, "\\.") + "]")
						.prop("selected", "selected");
				}
			}
		}

		kendo.ui.TreeView.prototype._keypress = kendo.ui.TreeView.prototype._keypress.createInterceptor(function(e)
		{
			var that = this;
			var delay = 300;
			var focusedNode = that.current().get(0);
			var matchToFocus;
			var key = e.key;
			var isPrintable = key.length === 1;
			if (!isPrintable)
			{
				return;
			}
			if (!that._match)
			{
				that._match = '';
			}
			that._match += key;
			clearTimeout(that._matchTimer);
			that._matchTimer = setTimeout(function()
			{
				that._match = '';
			}, delay);
			matchToFocus = focusedNode && that._matchNextByText(Array.prototype.indexOf.call(that.element.find('.k-item'), focusedNode), that._match);
			if (!matchToFocus.length)
			{
				matchToFocus = that._matchNextByText(-1, that._match);
			}
			if (matchToFocus.get(0))
			{
				that._trigger('navigate', matchToFocus);
				if (matchToFocus.get(0) !== focusedNode)
				{
					that.current(matchToFocus);
				}
			}
		});

		// Make the autofit columns behave like original KendoGrid: Grid should scroll left after auto fit column
		KendoGridPrototype._autoFitLeafColumn = KendoGridPrototype._autoFitLeafColumn.createSequence(function(e)
		{
			if (this._scrollLeft > 0)
			{
				let $scrollableWrap = this.element.find('.k-grid-content > .k-virtual-scrollable-wrap');
				kendo.scrollLeft($scrollableWrap, 0);
				kendo.scrollLeft($scrollableWrap, this._scrollLeft);
			}
		});

		kendo.ui.TreeView.prototype.toggle = kendo.ui.TreeView.prototype.toggle.createInterceptor(function(node, expand)
		{
			if ($(node[0]).attr('suspend') === 'true')
			{
				$(node[0]).attr('suspend', 'false');
				return false;
			}
			return true;
		});


		kendo.effects.promise = kendo.effects.promise.createInterceptor(function(element, options)
		{
			if ($('#routingtreeview').length > 0 &&
				$('#routingtreeview').data('kendoTreeView') &&
				$('#routingtreeview').data('kendoTreeView').dataItem(element[0]) &&
				$('#routingtreeview').data('kendoTreeView').dataItem(element[0]).customData.isTrip)
			{
				options.completeCallback = options.completeCallback.createSequence(function()
				{
					if (options.effects.expand != undefined && options.effects.expand.direction === 'vertical' && options.reverse)
					{
						$($(element)[0]).css('overflow', 'auto');
					}
				});
			}
		});

		kendo.ui.TreeView.prototype.append = function(nodeData, parentNode, success, expand)
		{
			function subGroup(node)
			{
				var result = node.children(".k-animation-container");

				if (!result.length)
				{
					result = node;
				}

				return result.children('.k-group');
			};
			var that = this;
			var group = that.root;
			if (parentNode && nodeData instanceof jQuery && parentNode[0] === nodeData[0])
			{
				return;
			}
			parentNode = parentNode && parentNode.length ? parentNode : null;
			if (parentNode)
			{
				group = subGroup(parentNode);
			}
			return that._dataSourceMove(nodeData, group, parentNode, function(dataSource, model, loadModel)
			{
				var inserted;
				function add()
				{
					if (parentNode)
					{
						that._expanded(parentNode, true, true);
					}
					var data = dataSource.data(), index = Math.max(data.length, 0);
					return that._insert(data, model, index);
				}
				loadModel.done(function()
				{
					inserted = add();
					success = success || $.noop;
					success(inserted);
				});
				return inserted || null;
			});
		};

		kendo.ui.Popup.prototype._resize = kendo.ui.Popup.prototype._resize.createInterceptor(function(e)
		{
			if (this && this.element && this.element.length && this.element[0])
			{
				var kendoRole = this.element[0].getAttribute("data-kendo-role");
				if (kendoRole && kendoRole == "colorpicker") return false;
			}
			return true;
		});

		kendo.ui.FilterMenu.fn._createForm = kendo.ui.FilterMenu.fn._createForm.createInterceptor(function(role)
		{
			var that = this, options = that.options, operators = that.operators || {}, type;
			if (that.options && that.options.dataSource && that.options.dataSource.options && Array.isArray(that.options.dataSource.options.fields))
			{
				var field = that.options.dataSource.options.fields.find(i => i.FieldName == that.field);
				if (field)
				{
					type = field.type;
				}
			}

			type = type || that.type;
			operators = operators[type] || options.operators[type];
			if (type === "date") 
			{
				operators.wi = "Is Within";
			}
		});

		kendo.ui.FilterMenu.fn._createForm = kendo.ui.FilterMenu.fn._createForm.createSequence(function(role)
		{
			var that = this, options = that.options, operators = that.operators || {}, type;
			if (that.options && that.options.dataSource && that.options.dataSource.options && Array.isArray(that.options.dataSource.options.fields))
			{
				var field = that.options.dataSource.options.fields.find(i => i.FieldName == that.field);
				if (field)
				{
					type = field.type;
				}
			}

			type = type || that.type;
			operators = operators[type] || options.operators[type];
			if (type === "date") 
			{
				delete operators.wi;
			}

			that.form.attr("fieldName", that.field);
		});

		TF.override ? TF.override(kendo.data.HierarchicalDataSource,
			function()
			{
				var result = [];
				function getAllById(data, id, fn)
				{
					for (var i = 0; i < data.length; i++)
					{
						if (data[i].id == id && (!fn || fn(data[i])))
						{
							result.push(data[i]);
						}
						if (data[i].children &&
							data[i].children._data &&
							data[i].children._data.length > 0)
						{
							getAllById(data[i].children._data, id, fn);
						}
						if (data[i].children &&
							data[i].children.options &&
							data[i].children.options.data &&
							data[i].children.options.data.items)
						{
							getAllById(data[i].children.options.data.items, id, fn);
						}
						if (data[i].items &&
							data[i].items.length > 0)
						{
							getAllById(data[i].items, id, fn);
						}
					}
				}
				function getFirstById(data, id, fn)
				{
					var node = null;
					for (var i = 0; i < data.length; i++)
					{
						if (data[i].children._data)
						{
							if (node == null) node = getFirstById(data[i].children._data, id, fn);
						}
						if (data[i].id == id && (!fn || fn(data[i])))
						{
							node = data[i];
							break;
						}
					}
					return node;
				}
				return {
					getAll: function(id, fn)
					{
						result = [];
						getAllById(this._data, id, fn);
						return result;
					},
					getFirst: function(id, fn)
					{
						return getFirstById(this._data, id, fn);
					}
				}
			}()) : null;

		var oldKendoGrid = $.fn.kendoGrid;
		$.fn.kendoGrid = function(options)
		{
			if (options && options.columns)
			{
				options.columns.filter((c) => c.type === "string" && !c.parse).forEach(function(c)
				{
					c.parse = kendoStringTypeParser;
				});
			}

			var grid = oldKendoGrid.apply(this, arguments);
			if (options.hideScrollNotOverflow)
			{
				var $gridContent = $(this).find(".k-grid-content");
				$gridContent.css({
					"overflow-y": "auto"
				});

				if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
				{
					$(this).find(".k-grid-header").css({
						"padding-right": 0
					});
				}
			}
			return grid;
		};

		kendo.ui.editor.InlineFormatter.prototype.consolidate = function(nodes)
		{
			var node, last;
			while (nodes.length > 1)
			{
				node = nodes.pop();
				last = nodes[nodes.length - 1];

				if (node.previousSibling && node.previousSibling.className == "k-marker")
				{
					last.appendChild(node.previousSibling);
				}

				if (node.tagName == last.tagName
					&& node.previousSibling == last
					&& node.style.cssText == last.style.cssText
					&& !node.hasAttribute("data-field")
					&& !last.hasAttribute("data-field")
					&& node.className === last.className)
				{
					while (node.firstChild)
					{
						last.appendChild(node.firstChild);
					}
					$(node).remove();
				}
			}
		};

		kendo.ui.editor.RestorePoint.prototype.index = function(node)
		{
			var result = 0;
			while (node = node.previousSibling)
			{
				result++;
			}

			return result;
		};

		kendo.ui.editor.RestorePoint.prototype.offset = function(node, value)
		{
			return value;
		};

		kendo.ui.editor.RestorePoint.prototype.toRangePoint = function(range, start, path, offset)
		{
			var node = this.rootNode,
				length = path.length;

			while (length-- && node)
			{
				node = node.childNodes[path[length]];
			}

			if (!node)
			{
				return;
			}

			if (node.nodeName == "#text")
			{
				offset = Math.min(offset, node.nodeValue.length);
			}

			if (offset >= 0)
			{
				range[start ? 'setStart' : 'setEnd'](node, offset);
			}
		};

		kendo.ui.editor.Serializer.toEditableHtml = function(html)
		{
			var br = '<br class="k-br">';

			html = html || "";

			return html
				.replace(/<!\[CDATA\[(.*)?\]\]>/g, "<!--[CDATA[$1]]-->")
				.replace(/<script([^>]*)>(.*)?<\/script>/ig, "<k:script$1>$2<\/k:script>")
				.replace(/^<(table|blockquote)/i, br + '<$1')
				.replace(/^[\s]*(&nbsp;|\u00a0)/i, '$1')
				.replace(/<\/(table|blockquote)>$/i, '</$1>' + br);
		};

		kendo.ui.editor.Serializer.htmlToDom = function(html, root, options)
		{
			var browser = kendo.support.browser;
			var msie = browser.msie;
			var o = options || {};
			var immutables = o.immutables;
			html = this.toEditableHtml(html);
			if (isFunction(o.custom))
			{
				html = o.custom(html) || html;
			}
			root.innerHTML = html;
			if (immutables)
			{
				immutables.deserialize(root);
			}
			if (msie)
			{
				kendo.ui.editor.Dom.normalize(root);
				this._resetOrderedLists(root);
			}
			this._fillEmptyElements(root);
			this._removeSystemElements(root);
			this._toEditableImmutables(root);
			$('table', root).addClass('k-table');
			return root;
		};

		let editorInitialize = kendo.ui.editor.FontTool.prototype.initialize;
		kendo.ui.editor.FontTool.prototype.initialize = function(ui, editor)
		{
			editorInitialize.call(this, ui, editor)
			let options = this.options,
				toolName = options.name,
				range,
				widget = ui.data("kendoComboBox");

			if (!widget)
			{
				return;
			}

			widget.unbind("change").bind("change", (e) =>
			{
				editor._range = range;
				kendo.ui.editor.Tool.exec(editor, toolName, e.sender.value());
			});
		};

		kendo.ui.Upload.prototype.selectFiles = function(files)
		{
			var that = this;
			var droppedFiles = files;
			var guidfiles = assignGuidToFiles(getAllFileInfo(droppedFiles), that._isAsyncNonBatch());

			if (droppedFiles.length > 0 && !that.wrapper.hasClass(TF.KendoClasses.STATE.DISABLED))
			{
				if (!that.multiple && guidfiles.length > 1)
				{
					guidfiles.splice(1, guidfiles.length - 1);
				}

				var prevented = that.trigger("select", { files: guidfiles });
				if (!prevented)
				{
					that._module.onSelect({ target: $(".k-dropzone", that.wrapper) }, guidfiles);
				}
			}
		};

		function getFileExtension(fileName)
		{
			var matches = fileName.match(/\.([^\.]+)$/);
			return matches ? matches[0] : "";
		}

		function getFileInfo(rawFile)
		{
			// Older Firefox versions (before 3.6) use fileName and fileSize
			var fileName = rawFile.name || rawFile.fileName;
			return {
				name: kendo.htmlEncode(fileName),
				extension: getFileExtension(fileName),
				size: rawFile.size || rawFile.fileSize,
				rawFile: rawFile
			};
		}

		function getAllFileInfo(rawFiles)
		{
			return $.map(rawFiles, function(file)
			{
				return getFileInfo(file);
			});
		}

		function assignGuidToFiles(files, unique)
		{
			var uid = kendo.guid();

			return $.map(files, function(file)
			{
				file.uid = unique ? kendo.guid() : uid;

				return file;
			});
		}


		var registerTool = kendo.ui.editor.EditorUtils.registerTool,
			registerFormat = kendo.ui.editor.EditorUtils.registerFormat;

		registerFormat("bold", [{ tags: ["span"], attr: { style: { fontWeight: "bold" } } }, { tags: ["strong", "b"] }]);
		registerTool("bold", new kendo.ui.editor.InlineFormatTool({
			key: "B", ctrl: true, format: kendo.ui.Editor.fn.options.formats.bold, template: new kendo.ui.editor.Tool({ template: kendo.ui.editor.EditorUtils.buttonTemplate, title: "Bold" }), ui: {
				togglable: true
			}
		}));

		registerFormat("italic", [{ tags: ["span"], attr: { style: { fontStyle: "italic" } } }, { tags: ["em", "i"] }]);
		registerTool("italic", new kendo.ui.editor.InlineFormatTool({
			key: "I", ctrl: true, format: kendo.ui.Editor.fn.options.formats.italic, template: new kendo.ui.editor.Tool({ template: kendo.ui.editor.EditorUtils.buttonTemplate, title: "Italic" }), ui: {
				togglable: true
			}
		}));

		function getContentEditbableAttr(element)
		{
			var value = $(element).attr("contenteditable");
			if (value === "" || value == null)
			{
				return null;
			}

			var value = value.toLowerCase();
			if (value === "true")
			{
				return true;
			}

			if (value === "false")
			{
				return false;
			}

			return null;
		}

		var handleBackspace = kendo.ui.editor.BackspaceHandler.prototype._handleBackspace;
		kendo.ui.editor.BackspaceHandler.prototype._handleBackspace = function(range)
		{
			if (!range.collapsed || range.startOffset !== 0)
			{
				return handleBackspace.call(this, range);
			}

			var node = $(range.startContainer);
			var removeUneditablePrev = function(node)
			{
				var prev = node.prev();
				if (prev.length)
				{
					if (getContentEditbableAttr(prev) !== false)
					{
						return false;
					}

					prev.remove();
					return true;
				}

				var parent = node.parent();
				if (!parent.length || node === parent)
				{
					return false;
				}

				return removeUneditablePrev(node.parent());
			};

			if (removeUneditablePrev(node))
			{
				return true;
			}

			return handleBackspace.call(this, range);
		};

		/* To fix RW-10007, prevent scroll when focus selected tab.
			Code is copied from kendo.all.js and add code to get preventScroll from options.
			For existing code, options.preventScroll is not set which means the default value is false, so this is not a breaking change.
			Changes:
				1. Add kendo.ui namespace to Widget.
				2. Add { preventScroll: options.preventScroll } to wr.focus();
		*/
		if (kendo.ui.TabStrip)
		{
			var tabStrip = (function()
			{
				return kendo.ui.TabStrip.extend({
					_itemClick: function(e)
					{
						var that = this, wr = that.wrapper[0];
						if (wr !== document.activeElement)
						{
							var msie = kendo.support.browser.msie,
								preventScroll = !!that.options.preventScroll;
							if (msie)
							{
								try
								{
									wr.setActive();
								} catch (j)
								{
									wr.focus({ preventScroll: preventScroll });
								}
							} else
							{
								wr.focus({ preventScroll: preventScroll });
							}
						}
						if (that._click($(e.currentTarget)))
						{
							e.preventDefault();
						}
					}
				});
			})();
			kendo.ui.plugin(tabStrip);
		}
	}

	TF.smartOverride(kendo.ui.VirtualList.prototype, "_prefetchByValue", function(origin)
	{
		arguments = Array.from(arguments);
		arguments.splice(0, 1);
		try
		{
			origin.apply(this, arguments);
		}
		catch (ex)
		{
			this._values = [];
			this._selectedIndexes = [];
			this._selectedDataItems = [];
			this.select([-1]);
		}
	});

	kendo.ui.Grid.prototype.reorderColumn = function(destIndex, column, before, isRunChild)
	{
		var that = this;
		if (!isRunChild)
		{
			if (!column)
			{
				return;
			}
			var childColumns = that.columns.filter(function(co, index)
			{
				return co.ParentField === column.FieldName;
			});
			if (childColumns && childColumns.length > 0)
			{
				return;
			}
		}

		// Check if the dest index inside parent and child field
		if (destIndex !== 1 && destIndex !== that.columns.filter(function(column) { return column.hidden === false }).length)
		{
			var sourceIndex = $.inArray(column, that.columns);
			if (sourceIndex > destIndex)
			{
				if (that.columns[destIndex] && !!that.columns[destIndex].ParentField && that.columns[destIndex].FieldName !== column.FieldName)
				{
					return;
				}
			}
			else
			{
				if (that.columns[destIndex + 1] && !!that.columns[destIndex + 1].ParentField && that.columns[destIndex + 1].FieldName !== column.FieldName)
				{
					return;
				}
			}
		}

		var parent = columnParent(column, that.columns), columns = parent ? parent.columns : that.columns, sourceIndex = $.inArray(column, columns), destColumn = columns[destIndex], lockChanged, isLocked = !!destColumn.locked, lockedCount = lockedColumns(that.columns).length, groupHeaderColumnTemplateColumns = $.grep(leafColumns(that.columns), function(column)
		{
			return column.groupHeaderColumnTemplate;
		});
		if (sourceIndex === destIndex)
		{
			return;
		}
		if (!column.locked && isLocked && nonLockedColumns(that.columns).length == 1)
		{
			return;
		}
		if (column.locked && !isLocked && lockedCount == 1)
		{
			return;
		}
		that._hideResizeHandle();
		if (before === undefined)
		{
			before = destIndex < sourceIndex;
		}
		var sourceColumns = [column];

		that._reorderHeader(sourceColumns, destColumn, before);
		if (that.lockedHeader)
		{
			removeEmptyRows(that.thead);
			removeEmptyRows(that.lockedHeader);
		}
		if (destColumn.columns)
		{
			destColumn = leafColumns(destColumn.columns);
			destColumn = destColumn[before ? 0 : destColumn.length - 1];
		}
		if (column.columns)
		{
			sourceColumns = leafColumns(column.columns);
		}
		that._reorderContent(sourceColumns, destColumn, before);
		lockChanged = !!column.locked;
		lockChanged = lockChanged != isLocked;
		column.locked = isLocked;
		columns.splice(before ? destIndex : destIndex + 1, 0, column);
		columns.splice(sourceIndex < destIndex ? sourceIndex : sourceIndex + 1, 1);
		that._updateLockedCols();
		that._updateCols();
		that._templates();
		that._updateColumnCellIndex();
		that._updateColumnSorters();
		if (groupHeaderColumnTemplateColumns.length > 0)
		{
			that._renderGroupRows();
		}
		that._updateTablesWidth();
		that._applyLockedContainersWidth();
		that._syncLockedHeaderHeight();
		that._syncLockedContentHeight();
		that._updateFirstColumnClass();
		if (!lockChanged)
		{
			return;
		}
		if (isLocked)
		{
			that.trigger("columnLock", { column: column });
		} else
		{
			that.trigger("columnUnlock", { column: column });
		}


		function columnParent(column, columns)
		{
			var parents = [];
			columnParents(column, columns, parents);
			return parents[parents.length - 1];
		}

		function columnParents(column, columns, parents)
		{
			parents = parents || [];
			for (var idx = 0; idx < columns.length; idx++)
			{
				if (column === columns[idx])
				{
					return true;
				} else if (columns[idx].columns)
				{
					var inserted = parents.length;
					parents.push(columns[idx]);
					if (!columnParents(column, columns[idx].columns, parents))
					{
						parents.splice(inserted, parents.length - inserted);
					} else
					{
						return true;
					}
				}
			}
			return false;
		}

		function nonLockedColumns(columns)
		{
			return $.grep(columns, function(column)
			{
				return !column.locked;
			});
		}

		function removeEmptyRows(container)
		{
			var rows = container.find('tr:not(.k-filter-row)');
			var emptyRowsCount = rows.filter(function()
			{
				return !$(this).children().length;
			}).remove().length;
			var cells = rows.find('th:not(.k-group-cell,.k-hierarchy-cell)');
			for (var idx = 0; idx < cells.length; idx++)
			{
				if (cells[idx].rowSpan > 1)
				{
					cells[idx].rowSpan -= emptyRowsCount;
				}
			}
			return rows.length - emptyRowsCount;
		}

		function leafColumns(columns)
		{
			var result = [];
			for (var idx = 0; idx < columns.length; idx++)
			{
				if (!columns[idx].columns)
				{
					result.push(columns[idx]);
					continue;
				}
				result = result.concat(leafColumns(columns[idx].columns));
			}
			return result;
		}

		function lockedColumns(columns)
		{
			return $.grep(columns, function(column)
			{
				return column.locked;
			});
		}
	};
	(function()
	{
		//RW-54402
		// those are copied from kendo.all.js to resolve sub-grid issue that tbody.empty() removes all pre-binded events ;
		kendo.ui.Grid.prototype._renderLockedContent = function(data, colspan, groups)
		{
			let html = "",
				idx,
				length,
				skipLastGroup,
				endlessAppend = null,
				flatViewLength,
				templates = {
					rowTemplate: this.lockedRowTemplate,
					altRowTemplate: this.lockedAltRowTemplate,
					groupFooterTemplate: this.lockedGroupFooterTemplate,
					groupHeaderColumnTemplate: this.lockedGroupHeaderColumnTemplate
				};

			if (this.lockedContent)
			{

				let table = this.lockedTable;
				endlessAppend = this._skipRerenderItemsCount > 0;

				if (groups > 0)
				{
					colspan = colspan - visibleColumns(leafColumns(nonLockedColumns(this.columns))).length;
					if (this.options.scrollable.endless)
					{
						flatViewLength = this.dataSource.flatView().length;
					}
					for (idx = 0, length = data.length; idx < length; idx++)
					{
						skipLastGroup = flatViewLength && idx === data.length - 1 && flatViewLength !== this.dataSource.total();
						html += this._groupRowHtml(data[idx], colspan, 0, groupRowBuilder, templates, false, skipLastGroup, true);
					}
				} else
				{
					html = this._rowsHtml(data, templates);
				}

				if (endlessAppend)
				{
					table.children("tbody").append(html);
				} else
				{
					appendContent(table.children("tbody"), table, html, this.options.size);
				}

				this._syncLockedContentHeight();
			}
		}
		kendo.ui.Grid.prototype._renderContent = function(data, colspan, groups)
		{
			let that = this,
				idx,
				length,
				html = "",
				isLocked = that.lockedContent != null,
				endlessAppend = null,
				skipLastGroup,
				flatViewLength,
				scrollable = that.options.scrollable,
				templates = {
					rowTemplate: that.rowTemplate,
					altRowTemplate: that.altRowTemplate,
					groupFooterTemplate: that.groupFooterTemplate,
					groupHeaderColumnTemplate: that.groupHeaderColumnTemplate
				};
			if (scrollable && scrollable.endless && !that.dataSource.options.endless)
			{
				that._skipRerenderItemsCount = 0;
				if (that.content)
				{
					that.content[0].scrollTop = 0;
				}
			}
			endlessAppend = that._skipRerenderItemsCount > 0;
			colspan = isLocked ? colspan - visibleLeafColumns(visibleLockedColumns(that.columns)).length : colspan;
			if (groups > 0)
			{

				colspan = isLocked ? colspan - groups : colspan;

				if (that.detailTemplate)
				{
					colspan++;
				}

				if (that.groupFooterTemplate)
				{
					that._groupAggregatesDefaultObject = that.dataSource.aggregates();
				}
				if (that.options.scrollable.endless)
				{
					flatViewLength = that.dataSource.flatView().length;
				}
				for (idx = 0, length = data.length; idx < length; idx++)
				{
					if (!that._skippedGroups)
					{
						that._skippedGroups = [];
					}
					skipLastGroup = flatViewLength && idx === data.length - 1 && flatViewLength !== that.dataSource.total();
					html += that._groupRowHtml(data[idx], colspan, 0, isLocked ? groupRowLockedContentBuilder : groupRowBuilder, templates, isLocked, skipLastGroup, false);
				}
			} else
			{
				html += that._rowsHtml(data, templates);
			}

			if (endlessAppend)
			{
				that.tbody.append(html);
				kendo.applyStylesFromKendoAttributes(that.tbody, ["display", "left", "right"]);
				clearTimeout(that._endlessFetchTimeOut);
				that._endlessFetchTimeOut = setTimeout(function()
				{
					if (that._groupToCollapse)
					{
						that.collapseGroup(that._groupToCollapse);
						that._groupToCollapse = null;
					}
				});
				that._endlessFetchInProgress = null;
			} else
			{
				that.tbody = appendContent(that.tbody, that.table, html, this.options.size);
			}
		}

		function visibleLockedColumns(columns)
		{
			return $.grep(columns, function(column)
			{
				return column.locked && isVisible(column);
			});
		}
		function isVisible(column)
		{
			return visibleColumns([column]).length > 0;
		}
		function visibleLeafColumns(columns)
		{
			let result = [];

			for (let idx = 0; idx < columns.length; idx++)
			{
				if (columns[idx].hidden)
				{
					continue;
				}

				if (columns[idx].columns)
				{
					result = result.concat(visibleLeafColumns(columns[idx].columns));
				} else
				{
					result.push(columns[idx]);
				}
			}

			return result;
		}
		function appendContent(tbody, table, html, size, supportInnerHtml)
		{
			let placeholder,
				tmp = tbody;

			let $html = $(html);
			kendo.applyStylesFromKendoAttributes($html, ["display", "left", "right"]);
			// if is true; tbody.empty() would cause remove events and cause issues;
			if (supportInnerHtml)
			{
				tbody.empty();
				$html.each((_, el) => tbody[0].appendChild(el));
			} else
			{
				let newHtml = $('<div>').append($html).html();
				placeholder = document.createElement("div");
				placeholder.innerHTML = "<table class='k-grid-table k-table'><tbody class='k-table-tbody'>" + newHtml + "</tbody></table>";
				$(placeholder).find("table").addClass(kendo.getValidCssClass("k-table-", "size", size));
				tbody = placeholder.firstChild.firstChild;
				table[0].replaceChild(tbody, tmp[0]);
				tbody = $(tbody);
			}
			return tbody;
		}
		function visibleColumns(columns)
		{
			return $.grep(columns, function(column)
			{
				let result = !column.hidden && column.matchesMedia !== false;

				if (result && column.columns)
				{
					result = visibleColumns(column.columns).length > 0;
				}
				return result;
			});
		}
		function leafColumns(columns)
		{
			var result = [];

			for (var idx = 0; idx < columns.length; idx++)
			{
				if (!columns[idx].columns)
				{
					result.push(columns[idx]);
					continue;
				}
				result = result.concat(leafColumns(columns[idx].columns));
			}

			return result;
		}
		function nonLockedColumns(columns)
		{
			return $.grep(columns, function(column)
			{
				return !column.locked;
			});
		}

		function groupRowBuilder(colspan, level, text, expanded, uid, includeAdditionalData, isRtl)
		{
			return '<tr ' + (includeAdditionalData ? 'data-group-uid="' + uid + '"' : '') + 'class="k-table-group-row k-grouping-row k-table-row">' + groupCells(level) +
				'<td class="k-table-td" colspan="' + colspan + '" aria-expanded="' + !!expanded + '">' +
				'<p class="k-reset">' +
				kendo.ui.icon($('<a href="#" tabindex="-1" ' + ARIA_LABEL + '="' + (expanded ? COLLAPSE : EXPAND) + '"></a>'), { icon: (expanded ? 'caret-alt-down' : `caret-alt-${isRtl ? 'left' : 'right'}`) }) + text +
				`</p></td>${new Array(colspan).join("<td hidden group-header-spanned-hidden></td>")}</tr>`;
		}
	})()
	kendo.ui.Reorderable.prototype._dropTargetAllowed = function(dropTarget)
	{
		var inSameContainer = this.options.inSameContainer, dragOverContainers = this.options.dragOverContainers, draggable = this._draggable;
		if (draggable[0] === dropTarget[0])
		{
			return false;
		}
		if (!inSameContainer || !dragOverContainers)
		{
			return true;
		}
		var allColumns = this._elements.closest(".kendo-grid").data("kendoGrid").columns;
		var allChildColumns = allColumns.filter(function(column)
		{
			return !!column.ParentField;
		});
		var allParentColumns = [];
		if (allChildColumns && allChildColumns.length > 0)
		{
			allParentColumns = allChildColumns.map(function(column)
			{
				return column.ParentField;
			});
			allChildColumns = allChildColumns.map(function(column)
			{
				return column.FieldName;
			});
			var targetItemName = dropTarget[0].getAttribute('data-kendo-field'), sourceIndex = this._index(draggable), targetIndex = this._index(dropTarget);
			if (allParentColumns.indexOf(targetItemName) > -1 && sourceIndex < targetIndex)
			{
				return false;
			}
			if (allChildColumns.indexOf(targetItemName) > -1 && sourceIndex > targetIndex)
			{
				return false;
			}
		}
		if (inSameContainer({
			source: draggable,
			target: dropTarget,
			sourceIndex: this._index(draggable),
			targetIndex: this._index(dropTarget)
		}))
		{
			return true;
		}
		return dragOverContainers(this._index(draggable), this._index(dropTarget));
	};

	kendo.ui.VirtualScrollable.prototype.repaintScrollbar = function(shouldScrollWrapper)
	{
		var that = this,
			html = '',
			maxHeight = that.options.maxScrollHeight,
			dataSource = that.dataSource,
			scrollbar = !kendo.support.kineticScrollNeeded ? kendo.support.scrollbar() : 0,
			wrapperElement = that.wrapper[0],
			totalHeight, idx, itemHeight;
		var wasScrolledToBottom = that._isScrolledToBottom();
		itemHeight = that.itemHeight = that.options.itemHeight() || 0;
		var addScrollBarHeight = wrapperElement.scrollWidth > wrapperElement.offsetWidth ? scrollbar : 0;
		totalHeight = (dataSource._isGroupPaged() ? dataSource.groupsTotal(true) : dataSource.total()) * itemHeight + addScrollBarHeight;
		var lightKendoGrid = $(that.element).closest(".kendo-grid").data("lightKendoGrid");

		if (lightKendoGrid && lightKendoGrid.lightKendoGridDetail)
		{
			for (var key in lightKendoGrid.lightKendoGridDetail.detailDomMap)
			{
				totalHeight += lightKendoGrid.lightKendoGridDetail.detailDomMap[key].show ? lightKendoGrid.lightKendoGridDetail.detailDomMap[key].height : 0;
			}
		}

		for (idx = 0; idx < Math.floor(totalHeight / maxHeight); idx++)
		{
			html += '<div style="width:1px;height:' + maxHeight + 'px"></div>';
		}

		if (totalHeight % maxHeight)
		{
			html += '<div style="width:1px;height:' + totalHeight % maxHeight + 'px"></div>';
		}
		that.verticalScrollbar.html(html);

		if (wasScrolledToBottom && !that._isScrolledToBottom() && !that.dataSource._isGroupPaged())
		{
			that.scrollToBottom();
		}

		if (typeof that._scrollTop !== 'undefined' && !!shouldScrollWrapper)
		{
			wrapperElement.scrollTop = that._scrollTop;
			that._scrollWrapperOnColumnResize();
		}
	};

	kendo.ui.VirtualScrollable.prototype._scroll = function(e)
	{
		var that = this,
			delayLoading = !that.options.prefetch,
			scrollTop = e.currentTarget.scrollTop,
			dataSource = that.dataSource,
			rowHeight = that.itemHeight,
			skip = dataSource.skip() || 0,
			start = that._rangeStart || skip,
			height = that.element.innerHeight(),
			isScrollingUp = !!(that._scrollbarTop && that._scrollbarTop > scrollTop),
			firstItemIndex = Math.max(Math.floor(scrollTop / rowHeight), 0),
			lastItemOffset = isScrollingUp ? Math.ceil(height / rowHeight) : Math.floor(height / rowHeight),
			lastItemIndex = Math.max(firstItemIndex + lastItemOffset, 0),
			SCROLL = 'scroll';
		if (that._preventScroll)
		{
			that._preventScroll = false;
			return;
		}
		that._prevScrollTop = that._scrollTop;
		that._scrollTop = scrollTop - start * rowHeight;
		that._scrollbarTop = scrollTop;
		that._scrolling = delayLoading;

		var lightKendoGrid = $(that.element).closest(".kendo-grid").data("lightKendoGrid");
		if (lightKendoGrid && lightKendoGrid.lightKendoGridDetail)
		{
			var totalHeight = 0;
			firstItemIndex = -1;
			lastItemIndex = -1;
			var topHeight = 0;
			lightKendoGrid.obAllIds().forEach(function(id, index)
			{
				totalHeight += rowHeight;
				if (lightKendoGrid.lightKendoGridDetail.detailDomMap[id])
				{
					totalHeight += lightKendoGrid.lightKendoGridDetail.detailDomMap[id].show ? lightKendoGrid.lightKendoGridDetail.detailDomMap[id].height : 0;
				}
				if (totalHeight > scrollTop && firstItemIndex == -1)
				{
					firstItemIndex = index;

				}
				if (totalHeight > (scrollTop + height) && lastItemIndex == -1)
				{
					lastItemIndex = index;
				}
				if (index < start)
				{
					topHeight = totalHeight;
				}
			});
			firstItemIndex = Math.max(firstItemIndex, 0);
			lastItemIndex = Math.max(lastItemIndex, 0);
			that._scrollTop = scrollTop - topHeight;
		}

		if (!that._fetch(firstItemIndex, lastItemIndex, isScrollingUp))
		{
			that.wrapper[0].scrollTop = that._scrollTop;
		}
		that.trigger(SCROLL);
		if (delayLoading)
		{
			if (that._scrollingTimeout)
			{
				clearTimeout(that._scrollingTimeout);
			}
			that._scrollingTimeout = setTimeout(function()
			{
				that._scrolling = false;
				that._page(that._rangeStart, that.dataSource.take());
			}, 100);
		}

	};

	kendo.ui.VirtualScrollable.prototype._fetch = function(firstItemIndex, lastItemIndex, scrollingUp)
	{
		var that = this,
			dataSource = that.dataSource,
			itemHeight = that.itemHeight,
			take = dataSource.take(),
			rangeStart = that._rangeStart || dataSource.skip() || 0,
			currentSkip = Math.floor(firstItemIndex / take) * take,
			fetching = false,
			prefetchAt = 0.19; //adjust prefetch factor from 0.33 to 0.19.
		var scrollbar = that.verticalScrollbar;
		var webkitCorrection = kendo.support.browser.webkit ? 1 : 0;
		var total = dataSource._isGroupPaged() ? dataSource.groupsTotal(true) : dataSource.total();
		if (firstItemIndex < rangeStart)
		{
			fetching = true;
			rangeStart = Math.max(0, lastItemIndex - take);
			that._scrollTop = scrollbar.scrollTop() - rangeStart * itemHeight;
			that._page(rangeStart, take);
		} else if (lastItemIndex >= rangeStart + take && !scrollingUp)
		{
			fetching = true;
			rangeStart = Math.min(firstItemIndex, total - take);
			if (scrollbar.scrollTop() >= scrollbar[0].scrollHeight - scrollbar[0].offsetHeight - webkitCorrection)
			{
				that._scrollTop = that.wrapper[0].scrollHeight - that.wrapper[0].offsetHeight;
			} else if (that.dataSource._isGroupPaged() && firstItemIndex >= total - take)
			{
				that._scrollTop = that.wrapper[0].scrollHeight - that.wrapper[0].offsetHeight - (that._scrollTop - that._prevScrollTop);
			} else
			{
				that._scrollTop = itemHeight;
			}
			that._page(rangeStart, take);
		} else if (!that._fetching && that.options.prefetch)
		{
			if (firstItemIndex < currentSkip + take - take * prefetchAt && firstItemIndex > take)
			{
				dataSource.prefetch(currentSkip - take, take, $.noop, true);
			}
			if (lastItemIndex > currentSkip + take * prefetchAt)
			{
				dataSource.prefetch(currentSkip + take, take, $.noop, true);
			}
		}
		return fetching;
	};

	kendo.data.DataSource.prototype.prefetch = function(skip, take, callback, disableOverlay)
	{
		var that = this, size = Math.min(skip + take, that.total()), options = {
			take: take,
			skip: skip,
			page: skip / take + 1,
			pageSize: take,
			sort: that._sort,
			filter: that._filter,
			group: that._group,
			aggregate: that._aggregate,
			disableOverlay: disableOverlay
		};
		if (that._isGroupPaged() && !that._isServerGrouped() && that._groupRangeExists(skip, size))
		{
			if (callback)
			{
				callback();
			}
			return;
		}
		if (that._isServerGroupPaged() && !that._groupRangeExists(skip, size) || !that._rangeExists(skip, size))
		{
			clearTimeout(that._timeout);
			that._timeout = setTimeout(function()
			{
				that._queueRequest(options, function()
				{
					if (!that.trigger('requestStart', { type: 'read' }))
					{
						if (that._omitPrefetch)
						{
							that.trigger('progress');
						}
						that.transport.read({
							data: that._params(options),
							success: that._prefetchSuccessHandler(skip, size, callback),
							error: function()
							{
								var args = slice.call(arguments);
								that.error.apply(that, args);
							}
						});
					} else
					{
						that._dequeueRequest();
					}
				});
			}, 100);
		} else if (callback)
		{
			callback();
		}
	};

	$.fn.bootstrapValidator.validators.regexp.validate = function(validator, $field, options)
	{
		var value = $field.val();
		if (value === '')
		{
			return true;
		}

		var regexp = ('string' === typeof options.regexp) ? new RegExp(options.regexp) : options.regexp;
		return options.revertTest ? (!regexp.test(value)) : regexp.test(value);
	}

	kendo.ui.editor.Clipboard.prototype._triggerPaste = function(html, options)
	{
		var args = { html: html || '' };
		args.html = args.html.replace(/\ufeff/g, '');
		this.editor.trigger('paste', args);
		Promise.resolve(args.handlePasteValueComplete).then(() =>
		{
			this.paste(args.html, options || {});
		});
	}

	kendo.ui.Editor.prototype._showElementResizeHandles = function()
	{
		var editor = this;
		var elementResizing = editor.elementResizing;
		if (elementResizing && elementResizing.element && elementResizing.element.parentNode)
		{
			elementResizing.showResizeHandles();
			this.trigger('elementResize', this);
		} else if (elementResizing && (!elementResizing.element || !elementResizing.element.parentNode))
		{
			editor._destroyElementResizing();
		}
	}

	kendo.ui.Editor.prototype.exec = function(name, params)
	{
		var that = this;
		var command = null;
		var range, tool, prevented;
		const Editor = kendo.ui.Editor;
		if (!name)
		{
			throw new Error('kendoEditor.exec(): `name` parameter cannot be empty');
		}
		if (that.body.getAttribute('contenteditable') !== 'true' && name !== 'print' && name !== 'pdf' && name !== 'exportAs')
		{
			return false;
		}
		name = name.toLowerCase();
		if (!that.keyboard.isTypingInProgress())
		{
			that._focusBody();
			that.selectRange(that._range || that.getRange());
		}
		tool =  that.toolbar.toolById && that.toolbar.toolById(name);
		if (!tool)
		{
			for (var id in Editor.defaultTools)
			{
				if (id.toLowerCase() == name)
				{
					tool = Editor.defaultTools[id];
					break;
				}
			}
		}
		if (tool)
		{
			range = that.getRange();
			if (tool.command)
			{
				command = tool.command($.extend({
					range: range,
					body: that.body,
					immutables: !!that.immutables
				}, params));
			}
			prevented = that.trigger('execute', {
				name: name,
				command: command
			});
			if (prevented)
			{
				return;
			}
			if (/^(undo|redo)$/i.test(name))
			{
				that.undoRedoStack[name]();
			} else if (command)
			{
				that.execCommand(command);
				if (command.async)
				{
					command.change = $.proxy(that._selectionChange, that);
					return;
				}
			}
			that._selectionChange();

			// Trigger the 'executed' event.
			that.trigger('executed', {
				name: name,
				command: command
			});
		}
	}

	kendo.ui.Grid.prototype._clipboard = function()
	{
		const NS = '.kendoGrid', NAVROW = 'tr:not(.k-footer-template):visible', NAVCELL = ':not(.k-group-cell):not(.k-detail-cell):not(.k-hierarchy-cell):visible', proxy = $.proxy;
		var options = this.options;
		var selectable = options.selectable;
		if (selectable && options.allowCopy)
		{
			var grid = this;
			if (!options.navigatable)
			{
				grid.table.add(grid.lockedTable).attr('tabindex', 0).on('mousedown' + NS + ' keydown' + NS, '.k-detail-cell', function(e)
				{
					if (e.target !== e.currentTarget)
					{
						e.stopImmediatePropagation();
					}
				}).on('mousedown' + NS, NAVROW + '>' + NAVCELL, proxy(tableClick, grid));
			}
			grid.copyHandler = proxy(grid.copySelection, grid);
			grid.updateClipBoardState = function()
			{
				if (grid.areaClipBoard)
				{
					grid.areaClipBoard.val(grid.getTSV()).focus().select();
				}
			};
			grid.bind('change', grid.updateClipBoardState);
			grid.wrapper.on('keydown', grid.copyHandler);
			grid.clearAreaHandler = proxy(grid.clearArea, grid);
			grid.wrapper.on('keyup', grid.clearAreaHandler);
		}
	}
	kendo.ui.NumericTextBox.prototype._inputHandler = function()
	{
		const POINT = '.', kendo = window.kendo, caret = kendo.caret;
		var that = this;
		var element = that.element;
		var value = element.val();
		var min = that.options.min;
		var numberFormat = that._format(that.options.format);
		var decimalSeparator = numberFormat[POINT];
		var minInvalid = (min !== null && min >= 0 && value.charAt(0) === '-');
		if (that._numPadDot && decimalSeparator !== POINT)
		{
			value = value.replace(POINT, decimalSeparator);
			that.element.val(value);
			that._numPadDot = false;
		}
		// error when past alpha, copy it from kendo source code v2013.1.716
		if (that._isPasted && that._parse(value))
		{
			value = that._parse(value).toString().replace(POINT, numberFormat[POINT]);
		}
		if (that._numericRegex(numberFormat).test(value) && !minInvalid)
		{
			that._oldText = value;
		} else
		{
			that._blinkInvalidState();
			that.element.val(that._oldText);
			if (that._cachedCaret)
			{
				caret(element, that._cachedCaret[0]);
				that._cachedCaret = null;
			}
		}
		that._isPasted = false;
	}

	kendo.ui.Grid.prototype._oldAutoFitLeafColumn = kendo.ui.Grid.prototype._autoFitLeafColumn;
	kendo.ui.Grid.prototype._autoFitLeafColumn = function(leafIndex)
	{
		var that = this;
		if (!that.table.children("tbody").children().length) { return; }
		kendo.ui.Grid.prototype._oldAutoFitLeafColumn.apply(this, arguments)
	}

	function isInputElement(element)
	{
		return $(element).is(':button,a,:input,a>.k-icon,textarea,span.k-select,span.k-icon,span.k-link,label.k-checkbox-label,.k-input,.k-multiselect-wrap,.k-picker-wrap,.k-picker-wrap>.k-selected-color,.k-tool-icon,.k-dropdown');
	}

	function focusTable(table, direct)
	{
		if (direct === true)
		{
			table = $(table);
			var scrollLeft = kendo.scrollLeft(table.parent());
			kendo.focusElement(table);
			kendo.scrollLeft(table.parent(), scrollLeft);
		} else
		{
			$(table).one('focusin', function(e)
			{
				e.preventDefault();
			}).focus();
		}
	}

	function tableClick(e)
	{
		//#region copy from kendo.ui.Grid
		const CHECKBOX = 'k-checkbox', CHECKBOXINPUT = 'input[data-role=\'checkbox\'].' + CHECKBOX;
		var currentTarget = $(e.currentTarget), isHeader = currentTarget.is('th'), table = this.table.add(this.lockedTable), headerTable = this.thead.parent().add($('>table', this.lockedHeader)), isInput = isInputElement(e.target), preventScroll = $(e.target).is('.k-checkbox'), target = $(e.target), currentTable = currentTarget.closest('table')[0];
		if (isInput && currentTarget.find(kendo.roleSelector('filtercell')).length)
		{
			this._setCurrent(currentTarget);
			return;
		}
		if (currentTable !== table[0] && currentTable !== table[1] && currentTable !== headerTable[0] && currentTable !== headerTable[1])
		{
			return;
		}
		if (target.is('a.k-i-expand, a.k-i-collapse'))
		{
			return;
		}
		if (this.options.navigatable)
		{
			this._setCurrent(currentTarget, false, preventScroll);
		}
		if (isHeader || !isInput)
		{
			setTimeout(function()
			{
				if ($(kendo._activeElement()).hasClass('k-widget'))
				{
					return;
				}
				if ($(kendo._activeElement()).is(CHECKBOXINPUT) || !isInputElement(kendo._activeElement()) || !$.contains(currentTable, kendo._activeElement()))
				{
					focusTable(currentTable, true);
				}
			});
		}
		if (isHeader && !kendo.support.touch)
		{
			e.preventDefault();
		}
		//#endregion

		// focus out filter input once select a row
		_.find(this.element.find('span.k-dropdown.k-dropdown-operator'), function(ele)
		{
			if ($(ele).children('span').hasClass(TF.KendoClasses.STATE.FOCUS)) 
			{
				$(ele).blur();
			}
		});
	}

	kendo.ui.MultiSelect.prototype._oldkeydown = kendo.ui.MultiSelect.prototype._keydown;

	kendo.ui.MultiSelect.prototype._keydown = function(e)
	{
		//RW-49925: Prevent events from bubbling
		e.stopPropagation && e.stopPropagation();
		kendo.ui.MultiSelect.prototype._oldkeydown.apply(this, arguments);
	}

	kendo.ui.Popup.prototype.close = function(skipEffects)
	{
		//RW-51482, copied from kendo.all.js
		let CLOSE = 'close',
			DOCUMENT_ELEMENT = $(document.documentElement),
			EFFECTS = 'effects',
			HIDDEN = 'hidden';

		let that = this, options = that.options, wrap, animation, openEffects, closeEffects;
		if (that.visible())
		{
			wrap = that.wrapper[0] ? that.wrapper : kendo.wrap(that.element).hide();
			that._toggleResize(false);
			if (that._closing || that._trigger(CLOSE))
			{
				that._toggleResize(true);
				return;
			}
			that.element.find('.k-popup').each(function()
			{
				let that = $(this)
					, popup = that.data('kendoPopup');
				if (popup)
				{
					popup.close(skipEffects);
				}
			});
			DOCUMENT_ELEMENT.off(that.downEvent, that._mousedownProxy);
			if (skipEffects)
			{
				animation = {
					hide: true,
					effects: {}
				};
			} else
			{
				animation = $.extend(true, {}, options.animation.close);
				openEffects = that.element.parent().data(EFFECTS);
				closeEffects = animation.effects;
				if (!closeEffects && !kendo.size(closeEffects) && openEffects && kendo.size(openEffects))
				{
					animation.effects = openEffects;
					animation.reverse = true;
				}
				that._closing = true;
			}
			// that.element.kendoStop(true).attr('aria-hidden', true);
			that.element.parent().kendoStop(true);
			if (that.element.attr('aria-hidden') != null)
			{
				that.element.removeAttr('aria-hidden');
			}
			// wrap.css({
			// 	overflow: HIDDEN
			// }).attr('aria-hidden', true);
			wrap.css({
				overflow: HIDDEN
			});
			if (wrap.attr('aria-hidden') != null)
			{
				wrap.removeAttr('aria-hidden');
			}
			that.element.parent().kendoAnimate(animation);
			if (skipEffects)
			{
				that._animationClose();
			}
		}
	};

	let dropdowntreeCloseHandler = kendo.ui.DropDownTree.prototype._closeHandler;
	kendo.ui.DropDownTree.prototype._closeHandler = function(e)
	{
		//RW-51482
		dropdowntreeCloseHandler.call(this, e);
		if (this.tree.attr('aria-hidden') != null)
		{
			this.tree.removeAttr('aria-hidden');
		}
	}

	let datetimePickerOpen = kendo.ui.DateTimePicker.prototype.open;
	kendo.ui.DateTimePicker.prototype.open = function(e)
	{
		datetimePickerOpen.call(this, e);
		if (typeof this.options.adjustPosition === "function")
		{
			this.options.adjustPosition(this);
		}
	}

	let treeViewFocus = kendo.ui.TreeView.prototype.focus;
	kendo.ui.TreeView.prototype.focus = function()
	{
		if (!this.wrapper)
		{
			return;
		}
		treeViewFocus.call(this);
	}

	let encode = kendo.htmlEncode;
	kendo.ui.Form.prototype._noLabelfieldTemplate = ({ styles, colSpan, hidden, field }) =>
		`<div class='${encode(styles.field)} ${colSpan ? `k-colspan-${encode(colSpan)} k-col-span-${encode(colSpan)}` : ''} ${hidden ? encode(styles.hidden) : ''}'>` +
		`<span class='${encode(styles.label)} ${encode(styles.emptyLabel)}'></span>` +
		`<div class='k-form-field-wrap' ${kendo.attr("container-for")}='${encode(field)}'></div>` +
		"</div>";

	kendo.ui.Form.prototype._fieldTemplate = ({ styles, colSpan, hidden, field, label, id, optional }) =>
		`<div class='${encode(styles.field)} ${colSpan ? `k-colspan-${encode(colSpan)} k-col-span-${encode(colSpan)}` : ''} ${hidden ? `${encode(styles.hidden)}` : ''}'>` +
		((label && !hidden) ?
			`<label class='${encode(styles.label)}' for='${encode(id)}' id='${encode(id)}-form-label'>` +
			((typeof label.encoded != 'undefined' && label.encoded === false) ?
				label.text || label
				: encode(label.text || label)) +
			(label.optional ? `<span class='${encode(styles.optional)}'>${encode(optional)}</span>` : '') +
			"</label>"
			: '') +
		`<div class='k-form-field-wrap' ${kendo.attr("container-for")}='${encode(field)}'></div>` +
		"</div>";

	kendo.dataviz.Note.prototype.renderVisual = function()
	{
		var this$1$1 = this;

		var options = this.options;
		var customVisual = options.visual;
		if (options.visible && customVisual)
		{
			//bind this to customVisual
			customVisual = customVisual.bind(this$1$1);
			this.visual = customVisual($.extend(this.fields, {
				sender: this.getSender(),
				rect: this.targetBox.toRect(),
				options: {
					background: options.background,
					border: options.background,
					icon: options.icon,
					label: options.label,
					line: options.line,
					position: options.position,
					visible: options.visible
				},
				createVisual: function()
				{
					this$1$1.createVisual();
					this$1$1.renderChildren();
					var defaultVisual = this$1$1.visual;
					delete this$1$1.visual;
					return defaultVisual;
				}
			}));
			this.addVisual();
		} else
		{
			kendo.dataviz.BoxElement.fn.renderVisual.call(this);
		}
	}
})();
