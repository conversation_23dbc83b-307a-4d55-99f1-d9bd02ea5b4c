﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListTripAliasDeletion = DataListTripAliasDeletion;

	function DataListTripAliasDeletion()
	{
		this.type = 'tripalias';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Trip Alias';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListTripAliasDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListTripAliasDeletion.prototype.constructor = DataListTripAliasDeletion;

	DataListTripAliasDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

	DataListTripAliasDeletion.prototype.publishData = function(ids)
	{//need to refresh the grid
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "tripalias", pb.DELETE), ids);
	};

})();