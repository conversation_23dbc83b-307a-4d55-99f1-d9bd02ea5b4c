.timeline-container{
	width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
}

.dashboard-designer {
	.grid-stack-item {
		.menu.withsub-menu {
			font-family: "SourceSansPro-Regular";
			font-size: 14px;
			z-index: 12034;
			&.closed {
				left:-1000px;
				top:-1000px;
			}
			.text{
				text-align: left;
			}
			background: linear-gradient(to right, #4b4b4b 34px, #ffffff 30px);
			> ul {
				padding-top: 0;
				li {
					&.title {
						pointer-events: none;
						.content {
							margin: 0 5px;
							height: 30px;
							padding: 5px;
							border-bottom: 1px solid #858585;
						}
					}
					> .text {
						border-left: 34px solid #4b4b4b;
						height: 30px;
					}
					.menuIcon {
						margin-left: 9px;
						&.check-white {
							background-image: url("../../global/img/Routing Map/check-white.png");
						}
					}
				}
			}
		}
	}
}

.timelinecontrol
{
	position: absolute;
	bottom: 0;
	width: 100%;
	padding: 5px 30px;
	float: left;
	height: 100%;
	z-index: 12031;
	min-width: 400px;

	&:hover {
		.toolbar
		{
			display: block;
			.toolbar-icon {
				&.active {
					display: inline-block;
				}
				display: none;
			}
		}
	}

	.timelinecontrol-container{
		float: left;
		position: relative;
		width: 100%;
		top: calc(50% + 8px);
		-ms-transform: translateY(-50%);
		transform: translateY(-50%);
	}
	&.setting-mode{
		background-color: rgba(244,244,244,.85);
		height: 200px;
	}

	.control-buttons-container{
		height: 36px;
		float: left;
		.control-button {
			height: 36px;
			width: 36px;
			margin-right: 5px;
			float: left;
			border: 1px solid #ddd;
			background-color: #fff;
			font-family: 'Glyphicons Halflings';
			font-style: normal;
			font-weight: normal;
			font-size: 15px;
			text-align: center;
			cursor: pointer;
			line-height: 35px;

			

			&.active .icon{
				color: #fff;
			}
			&.gear{
				margin-right: 20px;
			}
			&.current::before{
				content: "\e023";
			}
			&.play{
				&::before{
					content: "\e072";
				}
				&.stop::before{
					content: "\e073";
				}
			}
		}
	}

	.timeSlider-container{
		height: 26px;
		width: calc(100% - 60px);
		position: absolute;
		float: right;
		right: 8px;
		.timeline-value{
			position: absolute;
			top: -20px;
			white-space: nowrap;
		}
		.timeline-value span{
			height: 20px;
			line-height: 20px;
			padding: 0px 5px;
			text-align: center;
			background: #4a4a4a;
			color: #fff;
			font-size: 15px;
			font-family: 'SourceSansPro-SemiBold';
			display: block;
			position: absolute;
			left: 50%;
			transform: translate(-50%, 0);
			z-index: 101;
		}
		.timeline-value span:before{
			content: "";
			position: absolute;
			width: 0;
			height: 0;
			border-top: 8px solid #4a4a4a;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			top: 100%;
			left: 50%;
			margin-left: -11px;
			margin-top: -1px;
		}

		input{
			-webkit-appearance: none;
			width: 100%;
			height: 3px;
			background-color: #D8D8D8;
			margin-top: 16px;
			outline: none;
			&::-webkit-slider-thumb
			{
				-webkit-appearance: none;
				height: 20px;
				width: 20px;
				border-radius: 50%;
				background: #007cc0;
				cursor: pointer;
			}
			&::-moz-range-thumb
			{
				-webkit-appearance: none;
				height: 20px;
				width: 20px;
				border-radius: 50%;
				background: #007cc0;
				cursor: pointer;
			}
			&::-ms-thumb
			{
				-webkit-appearance: none;
				height: 20px;
				width: 20px;
				border-radius: 50%;
				background: #007cc0;
				cursor: pointer;
			}
			&::-webkit-slider-runnable-track
			{
				-webkit-appearance: none;
				box-shadow: none;
				border: none;
				background: transparent;
			}
			&::-moz-range-track
			{
				-webkit-appearance: none;
				box-shadow: none;
				border: none;
				background: transparent;
			}
			&::-ms-track
			{
				-webkit-appearance: none;
				box-shadow: none;
				border: none;
				background: transparent;
			}
		}
	}

	.setting-panel-container
	{
		overflow: hidden;
		height: calc(100% - 56px);
		margin-top: 56px;

		.settings-panel{
			padding: 15px 30px 15px 15px;
			padding-top: 20px;
			height: 100px;
			float: left;
			padding-bottom: 20px;
			border: 1px solid #ccc;
			background-color: #fff;

			.settings-date{
				width: 150px;
				float: left;
				.form-control.k-input
				{
					border-radius: 0;
				}
			}
			.settings-playspeed{
				width: 340px;
				float: left;
				margin-left: 35px;
				label{
					display: inline-block;
					margin-right: 5px;
				}
				.slider.slider-horizontal
				{
					width: 340px;
					margin-top: -4px;
					.slider-selection{
						background: transparent;
						box-shadow: none;
					}
				}
			}
		}
	}
}
.fake-slider{
	position: absolute;
	width: 20px;
	height: 20px;
	border-radius: 10px;
	background-color: #007cc0;
	z-index: 111;
}