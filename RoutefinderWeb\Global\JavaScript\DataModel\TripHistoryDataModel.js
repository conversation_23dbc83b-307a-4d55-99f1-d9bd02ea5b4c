﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.TripHistoryDataModel = function(documentEntity)
	{
		namespace.BaseDataModel.call(this, documentEntity);
	}

	namespace.TripHistoryDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.TripHistoryDataModel.prototype.constructor = namespace.TripHistoryDataModel;

	namespace.TripHistoryDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "TripStopHistories", default: [] },
		{ from: "TotalStudentsAssigned", default: 0 },
		{ from: "NumberOfStops", default: 0 },
		{ from: "TotalDistance", default: 0 },
		{ from: "TotalDeadheadDistance", default: 0 },
		{ from: "AttendanceDate" },
		{ from: "CreateDate", default: "1970-01-01T00:00:00" },
		{ from: "TripDuration", default: 0 },
		{ from: "DaysFor", default: 1 },
		{ from: "TripId", default: 0 },
		{ from: "AttendanceTakenBy", default: 0 },
		{ from: "AttendanceRecordSource", default: 0 },
		{ from: "AttendanceTakenDate" },
		{ from: "Level", default: 0 },
		{ from: "VehicleId", default: 0 },
		{ from: "DriverId", default: 0 },
		{ from: "AideId", default: 0 },
		{ from: "Session", default: 0 },
		{ from: "ActualAttendance", default: 0 },
		{ from: "TripStops", default: [], subDataModelType: namespace.AttendanceTripStopDataModel },
		{ from: "RouteID", default: 0 },
		{ from: "ActualOffPathEvents", default: null }
	];

})();
