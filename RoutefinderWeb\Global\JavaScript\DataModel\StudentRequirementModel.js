﻿(function()
{
	createNamespace("TF.DataModel").StudentRequirementModel = StudentRequirementModel;

	StudentRequirementModel.LocationType = {
		Home: 1,
		AltSite: 2,
		School: 3,
		NoTransportation: 4,
	};

	function StudentRequirementModel(entity, isEdit)
	{
		var self = this;
		TF.DataModel.BaseDataModel.call(self, entity);
		if (self.dbid() == null)
		{
			self.dbid(tf.datasourceManager.databaseId);
		}

		var locationType = self.noTransportationId() != null ? StudentRequirementModel.LocationType.NoTransportation : (self.locationSchoolCode() != null ? StudentRequirementModel.LocationType.School : (self.altSiteId() != null ? StudentRequirementModel.LocationType.AltSite : StudentRequirementModel.LocationType.Home));
		if (isEdit && !self.schoolCode())
		{
			locationType = StudentRequirementModel.LocationType.NoTransportation;
		}

		self.locationType = ko.observable(locationType);
		self.locationType.subscribe(function()
		{
			switch (self.locationType())
			{
				case StudentRequirementModel.LocationType.Home:
					self.locationSchoolCode(null);
					self.altSiteId(null);
					self.noTransportationId(null);
					break;
				case StudentRequirementModel.LocationType.AltSite:
					self.locationSchoolCode(null);
					self.noTransportationId(null);
					self.homeXCoord(null);
					self.homeYCoord(null);
					break;
				case StudentRequirementModel.LocationType.School:
					self.altSiteId(null);
					self.noTransportationId(null);
					self.homeXCoord(null);
					self.homeYCoord(null);
					break;
				case StudentRequirementModel.LocationType.NoTransportation:
					self.locationSchoolCode(null);
					self.altSiteId(null);
					self.homeXCoord(null);
					self.homeYCoord(null);
					break;
			}

			setTimeout(function()
			{
				self.validator && self.validator.clearExcludeErrors();
			});
		});

		self.isAltSite = ko.computed(function()
		{
			return self.locationType() == StudentRequirementModel.LocationType.AltSite;
		});

		self.isSchool = ko.computed(function()
		{
			return self.locationType() == StudentRequirementModel.LocationType.School;
		});

		self.isNoTransportation = ko.computed(function()
		{
			return self.locationType() == StudentRequirementModel.LocationType.NoTransportation;
		});

		self.locationTypeName = ko.pureComputed(function()
		{
			switch (self.locationType())
			{
				case StudentRequirementModel.LocationType.School:
					return "School"
				case StudentRequirementModel.LocationType.AltSite:
					return "Alternate Site"
				case StudentRequirementModel.LocationType.NoTransportation:
					return "No Transportation"
				case StudentRequirementModel.LocationType.Home:
				default:
					return "Home"
			}
		});
	}

	StudentRequirementModel.prototype = Object.create(TF.DataModel.BaseDataModel.prototype);

	StudentRequirementModel.prototype.constructor = StudentRequirementModel;

	StudentRequirementModel.prototype.applyDefaultHomeCoordinate = function(defaultX, defaultY)
	{
		if (this.locationType() != StudentRequirementModel.LocationType.Home)
		{
			return;
		}

		if (this.homeXCoord() == null) this.homeXCoord(defaultX);
		if (this.homeYCoord() == null) this.homeYCoord(defaultY);
	};

	StudentRequirementModel.prototype.isSameLocation = function(that)
	{
		return this.dbid() == that.DBID
			&& this.studentId() == that.StudentId
			&& this.altSiteId() == that.AltSiteID
			&& this.schoolCode() != null
			&& this.locationSchoolCode() == that.LocationSchoolCode;
	};

	StudentRequirementModel.isSameLocation = function(x, y)
	{
		return x.DBID == y.DBID
			&& x.StudentId == y.StudentId
			&& x.AltSiteID == y.AltSiteID
			&& x.SchoolCode != null
			&& x.LocationSchoolCode == y.LocationSchoolCode;
	};

	StudentRequirementModel.daysToString = function(entity)
	{
		return TF.DayOfWeek.all.filter(function(day)
		{
			return entity[day];
		}).map(function(day)
		{
			return day.substring(0, 2);
		}).join();
	};

	StudentRequirementModel.daysToArray = function(entity)
	{
		return TF.DayOfWeek.all.map(function(day)
		{
			return entity[day];
		});
	};

	var dateFromMapping = function(v) { return v ? new Date(v) : null; }, dateToMapping = function(v) { return v ? moment(v).format("YYYY-MM-DDT00:00:00.000[Z]") : null; };
	StudentRequirementModel.prototype.mapping = [
		{ from: "Id", to: "id", default: 0, notNull: true },
		{ from: "DBID", to: "dbid" },
		{ from: "StudentId", to: "studentId", default: 0, notNull: true },
		{ from: "SessionID", to: "sessionId", default: 0, notNull: true },
		{ from: "Sunday", to: "sunday", default: false, notNull: true },
		{ from: "Monday", to: "monday", default: true, notNull: true },
		{ from: "Tuesday", to: "tuesday", default: true, notNull: true },
		{ from: "Wednesday", to: "wednesday", default: true, notNull: true },
		{ from: "Thursday", to: "thursday", default: true, notNull: true },
		{ from: "Friday", to: "friday", default: true, notNull: true },
		{ from: "Saturday", to: "saturday", default: false, notNull: true },
		{ from: "StartDate", to: "startDate", fromMapping: dateFromMapping, toMapping: dateToMapping },
		{ from: "EndDate", to: "endDate", fromMapping: dateFromMapping, toMapping: dateToMapping },
		{ from: "SchoolCode", to: "schoolCode" },
		{ from: "Type", to: "type" },
		{ from: "AltSiteID", to: "altSiteId" },
		{ from: "HomeXCoord", to: "homeXCoord" },
		{ from: "HomeYCoord", to: "homeYCoord" },
		{ from: "LocationSchoolCode", to: "locationSchoolCode" },
		{ from: "ProhibitCross", to: "prohibitCross", default: false, notNull: true },
		{ from: "NoTransportationId", to: "noTransportationId" },
		{ from: "ApplySchoolCalendar", to: "applySchoolCalendar" },
		{ from: "RecurEvery", to: "recurEvery" },
		{ from: "RecurType", to: "recurType" },
		{ from: "SetRecurrence", to: "setRecurrence" }
	];
})();
