﻿(function()
{
	var commandTemplate = {
		MODE: 2,
		sets: {
			DIRDIST: true,
			ENDPOINTONLY: true,
			XMLDIR: true,
			UNITS: "mi",
			UTURNLIMIT: 2,
			DIRONLY: false,
			ONEWAY: true
		},
		internalSets: {
			pureThrough: true,
			searchpathmode: 0,
			SO: false,
			returnJson: true
		},
		points: [

		],
		action: {
			type: 0
		}
	}

	function RoutingManager()
	{
		this.createThroughPathBuilder = function()
		{
			var cmd = $.extend(true, {}, commandTemplate);
			cmd.internalSets.SO = true;
			return new RoutingCommandBuilder(cmd);
		}

		this.createPointToPointBuilder = function()
		{
			var cmd = $.extend(true, {}, commandTemplate);
			return new RoutingCommandBuilder(cmd);
		}
	}

	function RoutingCommandBuilder(cmdTemplate)
	{
		this.cmd = cmdTemplate;

		this.addStartPoint = function(point)
		{
			this.cmd.points.push(new GeoJSON.Feature(point, { pointType: "start" }));
			return this;
		};

		this.addEndPoint = function(point)
		{
			this.cmd.points.push(new GeoJSON.Feature(point, { pointType: "end" }));
			return this;
		};

		this.addPreviousPoint = function(point)
		{
			this.cmd.points.push(new GeoJSON.Feature(point, { pointType: "prev" }));
			return this;
		};

		this.addThroughPoint = function(point)
		{
			this.cmd.points.push(new GeoJSON.Feature(point, { pointType: "through" }));
			return this;
		};

		this.build = function()
		{
			return this.cmd;
		}
	}

	createNamespace('tf').routingManager = new RoutingManager();
})()