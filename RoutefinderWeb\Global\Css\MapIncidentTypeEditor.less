.map-incident-type-editor {
	#symbol-selector {
		padding-top: 10px;

		.symbol-container {
			height: 28px;
			border-bottom: 1px solid lightgray;
			padding: 2px 0 2px 5px;
			cursor: pointer;

			.currentSymbol {
				float: left
			}

			.dropDown {
				float: right;
				height: 100%;

				.caret {
					margin-right: 5px;
				}
			}
		}
	}

	.row .row {
		margin-right: 20px;
	}

	.item-group {
		padding: 0px;
		display: flex;
		flex-direction: row;
		align-items: center;

		.seperator {
			flex-basis: 45px;
		}

		.k-numerictextbox {
			width: 60px;
			margin-left: 0;

			.k-expand-padding {
				width: 62px;
			}
		}

		.input-group {
			width: 105px;
		}

		.text-padding {
			margin-left: 10px;
		}

		.input-size {
			width: 90px;
		}

		.radio-line {
			line-height: 22px;
		}

		.radio-position {
			margin-left: 10px;
		}
	}

	.radio-button {
		margin-top: 0px;
		line-height: 0px;
	}

	.col-xs-12 {
		&.no-right-padding {
			padding-right: 0px;
		}
	}

	.no-symbol {
		display: flex;
		align-items: center;

		.bold {
			font-weight: bold;
		}
	}

	.checkbox {
		.small-text {
			font-size: 12px;
		}
	}

	.color-input {
		padding-top: 12px;
	}

	.upload-symbol {
		.icon-button {
			line-height: 28px;
			color: #333;
			border: 1px solid #999;
			text-align: center;
			cursor: pointer;
			border-radius: 5px;
			font-size: 14px;
			width: 156px;
			margin-top: 9px;
		}
	}

	.icon-preview {
		height: 200px;
		background-image: url(../../Global/img/DisplayThumbnail.png);
		background-repeat: no-repeat;

		&.snapped {
			svg {
				left: 112px;
				top: 86px;
			}
		}

		svg {
			width: 36px;
			height: 36px;
			position: absolute;
			left: 124px;
			top: 66px;
		}
	}

	.symbols-panel {
		max-height: 240px;
		bottom: 0;
		left: 0;
		z-index: 10;
		position: absolute;
		left: 15px;
		top: 57px;
	}

	#slider-style {
		width: 100%;

		.slider-track {
			height: 2px;
			margin-top: 0px;

			.slider-handle {
				&.min-slider-handle {
					&.round {
						background: whitesmoke;
						margin-top: -9px;
						box-shadow: rgb(102, 102, 102) 0px 0px 12px;
					}
				}
			}
		}

		.tooltip {
			display: none;
		}
	}

	#border-slider {
		padding-top: 10px;

		#border-size-slider {
			#slider-style;
		}

		label {
			padding-top: 5px;
			font-size: 10pt;
			color: gray;
			font-weight: normal;
		}
	}

	div {
		&.disabled {
			opacity: 0.5;
			pointer-events: none;
		}
	}

}