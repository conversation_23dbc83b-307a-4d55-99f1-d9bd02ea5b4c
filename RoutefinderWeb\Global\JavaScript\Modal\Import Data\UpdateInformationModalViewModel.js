(function()
{
	createNamespace('TF.Modal').UpdateInformationModalViewModel = UpdateInformationModalViewModel;

	function UpdateInformationModalViewModel(compareResult)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.contentTemplate('modal/import data/updateinformationcontrol');
		self.buttonTemplate('modal/interactiveImportControl');
		self.updateInformationViewModel = new TF.Control.UpdateInformationViewModel(compareResult, self.shortCutKeyHashMapKeyName);
		self.data(self.updateInformationViewModel);
		self.title("Update Information");
		self.obPositiveButtonLabel("Finish");
		self.obRevertButtonLabel = ko.observable("Revert")
		self.obNegativeButtonLabel("Cancel");

		self.updateInformationViewModel.onFinish.subscribe(function(e, data)
		{
			if (data)
			{
				self.positiveClose(data);
			}
		});

		self.updateInformationViewModel.isRunning.subscribe(function(val)
		{
			self.obDisableControl(val);
		}, self);
	};

	UpdateInformationModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	UpdateInformationModalViewModel.prototype.constructor = UpdateInformationModalViewModel;

	/**
	 * The event of finish button click.
	 * @return {void}
	 */
	UpdateInformationModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.updateInformationViewModel.finish();
	};

	/**
	 * The event of cancel button click.
	 * @return {void}
	 */
	UpdateInformationModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		var self = this;
		self.updateInformationViewModel.cancel();
	};
	UpdateInformationModalViewModel.prototype.RevertClick = function(viewModel, e)
	{
		var self = this;
		self.updateInformationViewModel.Revert();
	}

	UpdateInformationModalViewModel.prototype.dispose = function()
	{
		var self = this;
		self.updateInformationViewModel.dispose();
	};
})();
