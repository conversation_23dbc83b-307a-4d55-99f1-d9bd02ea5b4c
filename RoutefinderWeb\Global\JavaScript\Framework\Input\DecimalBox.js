﻿(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.DecimalBox = DecimalBox;

	function DecimalBox(initialValue, attributes, disable, events)
	{
		var self = this;
		var existingRetainPrecision = Object.keys(attributes).some(function(i) { return i === "retainPrecision"; });
		if (existingRetainPrecision)
		{
			delete attributes.retainPrecision;
			this.retainPrecision = true;
		}
		if (Object.keys(attributes).some(function(i) { return i === "fixedPrecision"; }))
		{
			this.fixedPrecision = parseInt(attributes["fixedPrecision"]);
			delete attributes.fixedPrecision;
		}
		self.nonNegative = attributes && Boolean(attributes.nonNegative);
		this.inputEnable = attributes && attributes.inputEnable == false ? false : true;
		this.showCommas = attributes && attributes.showCommas ? true : false;
		this.decimalPlaces = attributes?.decimalPlaces;
		namespace.StringBox.call(this, initialValue, attributes, disable, undefined, undefined, events);

		this.getElement().on("keypress keyup", function(event)
		{
			var keyCode = event.which || event.keyCode || 0;
			if (self.inputEnable && ((keyCode == 64 && ($(this).val() === '' || $(this).val() === '@')) || /^@@/.test($(this).val())))
			{
				return;
			}
			const allowMinusSign = (keyCode != "-".charCodeAt(0) || ($(this).val().indexOf('-') !== -1 ? this.selectionStart > 0 || this.selectionEnd === 0 : false) || this.selectionStart > 0);
			if ((keyCode != ".".charCodeAt(0) || $(this).val().indexOf('.') !== -1)
				&& (self.nonNegative ? !allowMinusSign : allowMinusSign)
				&& (keyCode < "0".charCodeAt(0) || keyCode > "9".charCodeAt(0))
				&& !(keyCode == 37 && event.key != "%")
				&& !(keyCode == 39 && event.key != "'")
				&& keyCode !== 9)
			{
				event.preventDefault();
				event.stopPropagation();
			}
		});

		var precisionValue = self.getCurrentPrecisionValue();

		this.getElement().on("blur", function(event)
		{
			if (!/^[+-]?\d*\.?\d*$/.test($(this).val()))
			{
				$(this).val('');
				return;
			}

			//  if attributes.decimalPlaces is assigned?
			var number = $(this).val();
			if (precisionValue !== null && self.isNumber(number))
			{
				var value = Number(number).toFixed(precisionValue);
				
				$(this).val(value);
			}
			
			//  if attributes.showCommas is assigned?
			if (self.showCommas)
			{
				$(this).val(tf.dataFormatHelper.numberFormatter($(this).val(), precisionValue));
			}
		});

		if (self.showCommas || precisionValue !== null)
		{
			this.getElement().on("focus", function(event)
			{
				var formatValue = $(this).val();

				formatValue = formatValue.replaceAll(',','');

				if (!self.isNumber(formatValue))
				{
					$(this).val('');
					return;
				}
				
				var value = Number(formatValue);

				$(this).val(value);
			});
		}		
	};

	DecimalBox.prototype = Object.create(namespace.StringBox.prototype);

	DecimalBox.constructor = DecimalBox;

	DecimalBox.prototype.getCurrentPrecisionValue = function()
	{
		return typeof (this.decimalPlaces) === "number" ? this.decimalPlaces : null;
	};

	DecimalBox.prototype.isNumber = function(number)
	{
		return !isNaN(parseFloat(number)) && isFinite(number);
	}

	DecimalBox.prototype.type = "Decimal";

	//DecimalBox.prototype.dispose = function()
	//{
	//	namespace.StringBox.prototype.dispose.call(this);
	//	ko.cleanNode(this.getElement()[0]);
	//	this.getElement().remove();
	//}
})();