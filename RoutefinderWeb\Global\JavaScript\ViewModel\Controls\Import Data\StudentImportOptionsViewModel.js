(function()
{
	createNamespace('TF.Control').StudentImportOptionsViewModel = StudentImportOptionsViewModel;

	const SYNC_IMPORT_STATUS_INTERVAL = 15000;
	const IMPORT_STUDENT_STATUS = "ImportingMainStudentRecords";
	const MAIN_STATUS_FLAGS = {
		"DeletingUnmatchedStudents": {
			onlyDisplayInFirst: true,
			format: (cur, max) => `Deleting ${cur} of ${max} records.`,
		},
		"GettingCompareDataInteractively": {
			onlyDisplayInFirst: true,
			format: (cur, max) => `Detecting Changes in ${cur} of ${max} records.`,
		},
		"ProcessingContactRecords": {
			onlyDisplayInFirst: true,
			format: (cur, max) => `Processing ${cur} of ${max} contacts.`,
		},
		"ImportingMainStudentRecords": {
			format: (cur, max) => `Importing ${cur} of ${max} students.`,
		},
		"FinishingRemainingImportOperations": {
			format: (cur, max) => `Finishing ${cur} of ${max} remaining tasks.`,
		},
		"CancellingRemainingImportOperations": {
			format: (cur, max) => `Cancelling ${cur} of ${max} remaining tasks.`,
		},
		"ProcessingGeoCode": {
			onlyDisplayInLast: true,
			format: () => 'Geocodeing.',
		},
		"ProcessingAutoDistance": {
			onlyDisplayInLast: true,
			format: () => 'Calculating distance.',
		},
		"ProcessingUpdateSystemAddress": {
			onlyDisplayInLast: true,
			format: () => 'Update address.',
		},
		"Start committing Alternate Sites data": {
			onlyDisplayInLast: true,
			format: () => 'Start committing Alternate Sites data.',
		},
		"Finished committing Alternate Sites data": {
			onlyDisplayInLast: true,
			format: () => 'Finished committing Alternate Sites data.',
		}
	};

	function StudentImportOptionsViewModel(options, shortCutKeyName)
	{
		var self = this;

		// Require modal view model to pass positiveClose function.
		self.modalPositiveClose = options.PositiveClose;
		self.TFDFields = options.TFDFields;
		self.TFDRows = options.TFDRows;
		self.obFileName = ko.observable(options.ConvertedFileName);
		self.obStudentCount = ko.observable(options.RecordCounts);
		self.obCreatedDate = ko.observable(options.CreatedDatetime);
		self.tfxfile = {
			file: options.tfxFile,
			name: options.ConvertedFileName,
			type: options.externalFileType
		};

		self.obInteractive = ko.observable(false);
		self.obFullImport = ko.observable(false);
		self.obFullImportForContact = ko.observable(false);
		self.obRejectDuplicateContact = ko.observable(false);
		self.obRestetLoadTimes = ko.observable(false);
		self.obGeocode = ko.observable(false);
		self.obDistance = ko.observable(false);
		self.obSchedule = ko.observable(false);
		self.obResidence = ko.observable(false);
		self.obPopulationRegion = ko.observable(false);

		self.obCreateDoorToDoorStops = ko.observable(false);
		self.obUngeocodeClearsSchoolOfResidence = ko.observable(false);
		self.obDoNotAddCardEndDates = ko.observable(false);
		self.obUseStopsInStopPool = ko.observable(false);
		self.disable = ko.computed(function()
		{
			return !self.obUseStopsInStopPool() || !self.obSchedule();
		});
		self.schduelDisable = ko.computed(function()
		{
			return !self.obSchedule();
		});
		self.stopPoolCategories = ko.observableArray();
		self.selectedStopPoolCategory = ko.observable();
		self.selectedName = ko.computed(function()
		{
			return self.selectedStopPoolCategory() ? self.selectedStopPoolCategory().Name : '';
		});
		self.initStopPoolCategory().then(function(data)
		{
			self.stopPoolCategories(data);
			self.selectedStopPoolCategory(self.stopPoolCategories()[0]);
		});

		self.obAvailableDataModels = ko.observable([]);
		self.obSelectedDataModels = ko.observable([]);
		self.obSelectedResdictIds = ko.observable('');

		self.init = self.init.bind(self);

		tf.shortCutKeys.bind("alt+i", self.interactiveCheckboxClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+f", self.fullImportCheckboxClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+r", self.restetLoadTimesCheckboxClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+a", self.openAdvancedModalBtnClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+g", self.geocodeCheckboxClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+d", self.distanceCheckboxClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+s", self.scheduleCheckboxClick.bind(self), shortCutKeyName);

		//Events
		self.onHide = new TF.Events.Event();
		self.onImportAborted = new TF.Events.Event();

		self.obSelectedGeocodeSource = ko.observable(tf.authManager.hasNationalMaps() ? '' : 'Street Address Range');
		self.obGeocodeSources = ko.observable(['Address Point', 'Street Address Range']);

		self.file = options.tfxFile;
		self.externalFileType = options.externalFileType;
		self.filePath = '';

		self.compareResult = null;
		self.prepareIdsOfStudentsToDeletePromise = null; // promise for preparing the idsOfSelectedStudentsToDelete list (for FullImport scenario)
		self.importProgressValue = 0; // used for tracking current import progress at client-side
		self.importProgressUpdateIntervalHandle = null; // a handle for current setInterval function call for updating local progress-value
		self.lastImportProgressUpdatedTimestamp = new Date().getTime(); // a variable holding the timestamp of last time the server-side progress-value is pushed to client

		self.obAbortUngeocoded = ko.observable(false);
		self.obAbortUngeocoded.subscribe(self.abortUngeocodedBoxClick.bind(self, null, null, true));
		self.obAbortUngeocoded.subscribe(val =>
		{
			if (val)
			{
				setTimeout(() =>
				{
					$('input[name="UngeocodedNumber"]').data('kendoNumericTextBox').focus();
				});
			}
		});
		self.obUngeocodedNumber = ko.observable();
		self.obAbortDeleted = ko.observable(false);
		self.obAbortDeleted.subscribe(self.abortDeletedBoxClick.bind(self, null, null, true));
		self.obAbortDeleted.subscribe(val =>
		{
			if (val)
			{
				setTimeout(() =>
				{
					$('input[name="DeletedNumber"]').data('kendoNumericTextBox').focus();
				});
			}
		});
		self.obDeletedNumber = ko.observable();
	}

	/**
	 * Compose message for displaying in loadingIndicator (with progress value) based on the status and progress value of Importing Data action
	 *
	 */
	StudentImportOptionsViewModel.prototype.composeImportProgressMessage = function(statusFlag, curProgressValue, maxProgressValue, isFirst, isLast)
	{
		const processData = MAIN_STATUS_FLAGS[statusFlag];
		if (processData)
		{
			const { onlyDisplayInFirst, onlyDisplayInLast, format } = processData;
			const displayAlternative = (!isFirst && onlyDisplayInFirst) || (!isLast && onlyDisplayInLast);

			if (!displayAlternative)
			{
				return format(curProgressValue, maxProgressValue);
			}

			return MAIN_STATUS_FLAGS[IMPORT_STUDENT_STATUS].format(curProgressValue, maxProgressValue);
		}

		return statusFlag;
	};

	/**
	 * For ImportData action, when the server-api is running in a loop (such as seleting records, importing records, finishing pending tasks...)
	 * We will send progress value in certain interval (not every unit increment). This method will help increasing the progress-value at the unit of 1
	 * So that the client won't see obvious progress-value gaps between updates
	 * @param {For} status 
	 */
	StudentImportOptionsViewModel.prototype.updateImportProgressSmoothly = function(status)
	{
		let self = this,
			statusMessage = status.Message,
			latestProgressValue = status.CurrentProgressValue, maxProgressValue = status.MaxProgressValue,
			localReportTimerInterval = 40,
			millionSecondsBetweenServerProgressReport = new Date().getTime() - self.lastImportProgressUpdatedTimestamp, // determine how quick we should incrementing progress-value locally
			smoothIncrementStep = Math.max(1, self.importProgressValue >= latestProgressValue ? 0 : Math.floor((latestProgressValue - self.importProgressValue) / (millionSecondsBetweenServerProgressReport / localReportTimerInterval + 5)));

		self.lastImportProgressUpdatedTimestamp = new Date().getTime(); // update the timestamp of last updated time of local progress-value
		window.clearInterval(self.importProgressUpdateIntervalHandle); // clear previous local progress anitmation interval

		if (document.hidden !== false)
		{
			// if browser page tab is inactive or document.hidden is not supported, we simply update progress with the server-api pushed value immediately without smoothing animation
			self.importProgressValue = latestProgressValue;
			let latestPercentage = Math.floor((self.importProgressValue / maxProgressValue) * 100),
				latestProgressMessage = self.composeImportProgressMessage(statusMessage, self.importProgressValue, maxProgressValue, status.IsFirstPart, status.IsLastPart);
			tf.loadingIndicator.changeProgressbar(latestPercentage, latestProgressMessage, true);
			return;
		}

		self.importProgressUpdateIntervalHandle = window.setInterval(function()
		{
			self.importProgressValue += smoothIncrementStep;
			if (!tf.loadingIndicator._isImportRunning || self.importProgressValue > latestProgressValue)
			{
				window.clearInterval(self.importProgressUpdateIntervalHandle);
				self.importProgressValue = latestProgressValue;

				if (!tf.loadingIndicator._isImportRunning)
				{
					tf.loadingIndicator.tryHide(); // processing has finished or terminated, should hide loadingIndicator
				}
			}

			let calculatedPercentage = Math.floor((self.importProgressValue / maxProgressValue) * 100),
				composedProgressMessage = self.composeImportProgressMessage(statusMessage, self.importProgressValue, maxProgressValue, status.IsFirstPart, status.IsLastPart);

			tf.loadingIndicator.changeProgressbar(calculatedPercentage, composedProgressMessage, true);

		}, localReportTimerInterval);
	};

	StudentImportOptionsViewModel.prototype.initStopPoolCategory = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "stoppoolcategories"), {
			paramData: {
				dbid: tf.datasourceManager.databaseId
			}
		}).then(function(apiResponse)
		{
			return apiResponse.Items;
		});
	};

	StudentImportOptionsViewModel.prototype.statusUpdated = function(status)
	{
		let self = this,
			statusPercentage = status.Percentage,
			statusMessage = status.Message;

		if (!tf.startAutomation)
		{
			if (statusPercentage === undefined || statusPercentage === null || Number.isNaN(parseInt(statusPercentage)))
			{
				// If no explicit progress percentage value supplied, we will call updateImportProgressSmoothly to generate progress message and percentage value 
				self.updateImportProgressSmoothly(status);
			}
			else
			{
				window.clearInterval(self.importProgressUpdateIntervalHandle); // clear existing local progress anitmation interval (smooting simulation)
				if (statusPercentage <= 0)
				{
					self.importProgressValue = 0; // reset local progress-value
				}
				self.lastImportProgressUpdatedTimestamp = new Date().getTime(); // update the timestamp of last updated time of local progress-value
				tf.loadingIndicator.changeProgressbar(statusPercentage, statusMessage, null, statusMessage === "Update address.");
			}
		}
	};

	/**
	 * Initialize the student import options modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.$el = $(el);

		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix("V2"), "redistricts"));
		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"));

		Promise.all([p1, p2]).then(function(response)
		{
			if (response[0] && response[0].Items && response[0].Items.length > 0)
			{
				var items = response[0].Items, schoolGradeRange = {}, allSchools = [];
				if (response[1] && response[1].Items && response[1].Items.length > 0)
				{
					response[1].Items.forEach(function(item)
					{
						schoolGradeRange[item.SchoolCode] = item.GradeRange;
					});
				}
				items.map(function(item)
				{
					if (item.Schools)
					{
						allSchools = item.Schools.split('!');
						item.DisplaySchools = allSchools.map(function(schoolCode)
						{
							return schoolCode + (schoolGradeRange[schoolCode] ? "(" + schoolGradeRange[schoolCode] + ")" : "");
						}).join(',');
					}
					else
					{
						item.DisplaySchools = "";
					}

				});
				self.obAvailableDataModels(items);
			}
		});
	};

	/**
	 * Get tfx file name.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.getTFXFileName = function()
	{
		var tmp = this.obFileName().split('\\');
		if (!tmp.length)
		{
			return "";
		}

		return tmp[tmp.length - 1];
	}

	/**
	 * Open tfx file grid modal.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.openTFXGridModalBtnClick = function()
	{
		var self = this, tfxFileName = self.getTFXFileName();
		if (tfxFileName)
		{
			var options = {
				convertedTfxFileName: tfxFileName,
				tfdFields: self.TFDFields,
				tfdRows: self.TFDRows
			}
			tf.modalManager.showModal(new TF.Modal.TFXFileGirdModalViewModel(options));
		}
	};

	/**
	 * Open advanced modal.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.openAdvancedModalBtnClick = function()
	{
		var self = this;
		tf.modalManager.showModal(new TF.Modal.AdvancedImportOptionsModalViewModel(self.TFDFields)).then(function(result)
		{
			if (result)
			{
				self.TFDFields = result;
			}
		});

		return false;
	};

	/**
	 * Open school of residence modal.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.openSelectBoundaryModalBtnClick = function()
	{
		var self = this;
		tf.modalManager.showModal(
			new TF.Modal.SelectBoundarySetsModalViewModel(
				self.obAvailableDataModels(),
				self.obSelectedDataModels()
			)
		).then(function(result)
		{
			if (result)
			{
				self.obSelectedDataModels(result);
				var resdictIds = '';
				result.map(function(res)
				{
					resdictIds += res.Id + ','
				});
				self.obSelectedResdictIds(resdictIds.slice(0, -1));
			}
		});
	};

	/**
	 * Valid data.
	 * @return {Promise} The response from API
	 */
	StudentImportOptionsViewModel.prototype.valid = function()
	{
		var self = this;

		var primaryFields = self.TFDFields.filter(function(field) { return field.IsPrimaryKey });
		if (primaryFields.length <= 0)
		{
			return tf.promiseBootbox.alert("You must specify at least one field as a key for use in matching student records.")
				.then(function()
				{
					return Promise.resolve(false);
				});
		}

		var assignedFields = primaryFields.filter(function(field) { return field.OutputFieldName.startsWith("@"); });
		if (assignedFields.length > 0)
		{
			return tf.promiseBootbox.alert("You can't set the field '" + assignedFields[0].InputFieldName + "' to primary key.")
				.then(function()
				{
					return Promise.resolve(false);
				});
		}

		if (self.obResidence() && !self.obSelectedDataModels().length)
		{
			return tf.promiseBootbox.alert("At least one boundary set must be selected if the 'Find School of Residence' setting is enabled.")
				.then(function()
				{
					return Promise.resolve(false);
				});
		}

		if ((self.obAbortUngeocoded() && isNullObj(self.obUngeocodedNumber())) || (self.obAbortDeleted() && isNullObj(self.obDeletedNumber())))
		{
			tf.promiseBootbox.alert('Safety checks value is required.');
			return Promise.resolve(false);
		}

		return Promise.resolve(true);
	};

	/**
	 * Import data.
	 * @return {Promise} The response from API
	 */
	StudentImportOptionsViewModel.prototype.apply = function()
	{
		let self = this,
			isGeocode = self.obGeocode(),
			isSchedule = self.obSchedule(),
			isResidence = self.obResidence(),
			isDistance = self.obDistance(),
			selectedResdictIds = self.obSelectedResdictIds(),
			selectedGeocodeSource = self.obSelectedGeocodeSource(),
			isUseStopPool = self.obUseStopsInStopPool(),
			selectedStopPoolCategory = self.selectedStopPoolCategory(),
			isCreateDoorToDoor = self.obCreateDoorToDoorStops(),
			isFindPopulationRegion = self.obPopulationRegion();

		function delayInMs(ms)
		{
			return new Promise((r) => setTimeout(() => { r(); }, ms));
		}

		function showImportResult(response)
		{
			//var students = response.importStatus.Students;
			let numOfImportedStudents = response.importStatus.InsertedCount + response.importStatus.UpdatedCount;
			let dataFilePathOfImportedStudents = response.importStatus.DataFilePathOfImportedStudents;
			let isUdfCanged = response.importStatus.ContainsUdf;
			let duration = moment(response.importStatus.Duration, "HH:mm:ss");
			let startTime = new Date();
			response.compareResult.selectedResdictIds = selectedResdictIds;
			response.compareResult.selectedGeocodeSource = selectedGeocodeSource;
			response.compareResult.selectedStopPoolCategory = selectedStopPoolCategory;
			response.compareResult.isGeocode = isGeocode;
			response.compareResult.isSchedule = isSchedule;
			response.compareResult.isResidence = isResidence;
			response.compareResult.isDistance = isDistance;
			response.compareResult.isUseStopPool = isUseStopPool;
			response.compareResult.isCreateDoorToDoor = isCreateDoorToDoor;
			response.compareResult.IsFindPopulationRegion = isFindPopulationRegion;
			let notUpdate = !isSchedule;//RW-32899 Geocode and Find distance already include in import student API.
			let hasStudent = numOfImportedStudents > 0;
			if (hasStudent)
			{
				var automationPromise = Promise.resolve();
				if (!notUpdate)
				{
					automationPromise = delayInMs(400).then(() =>
					{
						tf.loadingIndicator.show(true);
						tf.loadingIndicator.resetProgressbar();
						tf.startAutomation = true;

						return self.runAutomationForAllImportedStudents(
							dataFilePathOfImportedStudents,
							numOfImportedStudents,
							isSchedule,
							isUseStopPool,
							selectedStopPoolCategory,
							isCreateDoorToDoor
						).then(() =>
						{
							tf.startAutomation = false;
							tf.loadingIndicator.tryHide();
							setTimeout(function()
							{
								tf.loadingIndicator.resetProgressbar();
							}, 200);
						});
					});
				}

				var promiseList = [];
				promiseList.push(automationPromise);

				//Reload udfs if updated UDF
				if (isUdfCanged)
				{
					promiseList.push(tf.UDFDefinition.loadAll());
				}
				return Promise.all(promiseList).then(function()
				{
					var timeDiff = new Date() - startTime;
					var time = Math.floor(timeDiff / 1000) + (duration.hours() * 3600) + (duration.minute() * 60) + duration.second();
					return self.logDurationTime(response.importStatus.LogFilePath, time).then(function()
					{
						closeModalOperation(response);
					});
				});
			}
			else
			{
				closeModalOperation(response);
			}
		}

		function closeModalOperation(response)
		{
			if (self.obInteractive())
			{
				self.onHide.notify();
				tf.modalManager.showModal(new TF.Modal.UpdateInformationModalViewModel(
					{
						...response.compareResult,
						file: self.tfxFile
					})).then(function(importResult)
					{
						if (importResult)
						{
							let compareResult = importResult.compareResult || {};
							compareResult.IdsOfSelectedStudentsToDelete = compareResult.IsInteractiveCancel ? [] : compareResult.IdsOfSelectedStudentsToDelete;

							if (importResult.isReverted === true)
							{
								tf.promiseBootbox.alert("All changes have been reverted.", "Import Result");
							}
							else if (compareResult.IsInteractiveCancel && !compareResult.CurrentRecordType)
							{
								// Here the user do cancel when the initial GetCompareData call is still in-progress (no RecordType for current record is returned yet)
								// Then, we need to send request to cancel the GetCompareData request (it might be in a buzy loop) 
								self.cancelGetCompareDataDuringInteractiveMode(importResult);
							}
							else if (compareResult.IsInteractiveCancel && !importResult.hasAcceptedRecords)
							{
								// If interactive mode is canceled and there is not any accepted record.
								self.revertImport(importResult);
							}
							else if (importResult.isNeedContinue)
							{
								self.importProgressValue = compareResult.CurrentIndex; // For continue/finish importing (run to end) we should first set local progress value to the number of records already processed (interactively)
								self.obInteractive(false);
								self.compareResult = compareResult
								self.apply();
							}
							else
							{
								self.modalPositiveClose(importResult);
							}
						}
					});
			}
			else
			{
				self.modalPositiveClose(response);
			}
		}

		//verify the token before import to ensure this token will not be expired between this import
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'tokens'), {
			paramData: {
				verify: true
			}
		});

		return self.valid().then(function(result)
		{
			if (!result)
			{
				return Promise.resolve(false);
			}

			let schoolCodesArray = self.obSelectedDataModels().map(function(dataItem) { return dataItem.Schools.split('!'); });
			let selectedSchoolCodes = [];
			schoolCodesArray.forEach(function(schoolCodes)
			{
				schoolCodes.forEach(function(schoolCode)
				{
					if (selectedSchoolCodes.indexOf(schoolCode) === -1)
					{
						selectedSchoolCodes.push(schoolCode);
					}
				});
			});

			let isInteractiveCancel = self.compareResult ? self.compareResult.IsInteractiveCancel : false;
			let handleResponse = {
				idsOfSelectedStudentsToDelete: isInteractiveCancel ? [] : null, // hold IdsOfStudentsToDelete (returned from Api). Now, only for < 700 case will we let user to choose the exact students to delete
				cancelled: false,
				closeStudentImportOptionsPage: false
			};
			let promiseGetDeletedStudentIds = Promise.resolve(handleResponse);


			// if import tfx file directly, the full import deal with in backend
			if (self.obFullImport() && !isInteractiveCancel)
			{
				if (!self.prepareIdsOfStudentsToDeletePromise)
				{
					self.prepareIdsOfStudentsToDeletePromise = self.externalFileType === 'TFX' ? self.getTfxDeletedStudentIds(handleResponse) : self.getDeletedStudentIds(handleResponse, self.getTFXFileName());
				}

				// resolve selected studentIds (for deletion) from existing promise (because for interactive-mode, the "apply" method will be invoked twice and we should avoid fetching delete student ids twice)
				promiseGetDeletedStudentIds = self.prepareIdsOfStudentsToDeletePromise;
			}

			return promiseGetDeletedStudentIds.then(function(result)
			{
				if (result.cancelled)
				{
					return false;
				}

				if (result.closeStudentImportOptionsPage)
				{
					return false;
				}

				return self.importData(result.idsOfSelectedStudentsToDelete, selectedSchoolCodes).then(function(response)
				{
					if (!response) return;

					if (response.importStatus.Reverted)
					{
						self.modalPositiveClose(response);
					}
					else
					{
						return showImportResult(response);
					}
				});
			});
		});
	};

	StudentImportOptionsViewModel.prototype.updateSystemAddress = function(importedStudentsCount, dataFilePathOfImportedStudents)
	{
		var self = this, miniBatchSize = 1000, promises = [];
		for (var i = 0; i <= importedStudentsCount / miniBatchSize; i++)
		{
			promises.push(self.getDataOfImportedStudentsInBatch(dataFilePathOfImportedStudents, i * miniBatchSize, miniBatchSize));
		}
		return Promise.all(promises).then((studentLists) =>
		{
			studentLists.forEach(async (students) =>
			{
				return await TF.ImportAndMergeData.ImportStep.prototype.updateImportStudentsSysAddresses(students);
			})
		})
	}

	StudentImportOptionsViewModel.prototype.logDurationTime = function(logFileName, seconds)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "ImportedData"), {
			paramData: {
				seconds: seconds,
				logFileName: logFileName
			}
		});
	};

	StudentImportOptionsViewModel.prototype.getDataOfImportedStudentsInBatch = function(dataFilePath, startIndex, takeCount)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "ImportedData"), {
			paramData: {
				dataFilePathOfImportedStudents: dataFilePath,
				startIndex: startIndex,
				takeCount: takeCount,
				isResidence: false
			}
		}).then(function(resp)
		{
			if (!Array.isArray(resp.Items))
			{
				return [];
			}

			return resp.Items;
		}).catch(function()
		{
			return [];
		});
	};

	StudentImportOptionsViewModel.prototype.runAutomationForAllImportedStudents = function(dataFilePath, numOfImportedStudents, isSchedule, isUseStopPool, selectedStopPoolCategory, isCreateDoorToDoor)
	{
		var self = this;

		if (!dataFilePath || !numOfImportedStudents || numOfImportedStudents <= 0)
		{
			return Promise.resolve(false);
		}

		return self.getDataOfImportedStudentsInBatch(dataFilePath, 0, numOfImportedStudents).then(importedStudents => 
		{
			return self.automationRunner(
				importedStudents,
				isSchedule,
				isUseStopPool,
				selectedStopPoolCategory,
				isCreateDoorToDoor,
				{ Percentage: 0, Message: 'Run Automation' });
		});
	};

	StudentImportOptionsViewModel.prototype.automationRunner = function(students, isSchedule, isUseStopPool, selectedStopPoolCategory, isCreateDoorToDoor, progress)
	{
		if (!isSchedule)
		{
			return Promise.resolve(true);
		}

		return TF.AutomationHelper.findSchedule(students.map(s => s.Id), isUseStopPool, selectedStopPoolCategory ? selectedStopPoolCategory.Id : 0, isCreateDoorToDoor, null, progress);
	};

	/**
	 * Get the ids of students which will be deleted.
	 * @param {Object} handleResponse the response of deleted student ids
	 * @return {Promise} The response from API
	 */
	StudentImportOptionsViewModel.prototype.getDeletedStudentIds = function(handleResponse, fileName)
	{
		var self = this;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "ImportedData"), {
			paramData: {
				fileName: fileName //self.getTFXFileName()
			},
			loadingSubtitle: "Comparing data..."
		}).then(function(response)
		{
			let totalNumberOfStudentsToDelete = response.TotalRecordCount;
			if (totalNumberOfStudentsToDelete === 0)
			{
				// null indicates 
				handleResponse.idsOfSelectedStudentsToDelete = null;
				return handleResponse;
			}
			else
			{
				var candidateStudentsToDelete = Array.isArray(response.Items) && response.Items.length > 0 ? response.Items.map(function(item)
				{
					// The array structure [Id,LocalId,FirstName,LastName,Locked]
					let itemArr = JSON.parse(item);
					return {
						Id: itemArr[0],
						Name: [itemArr[2], itemArr[3]].filter(x => x).join(" ")
					};
				}) : null;

				if (!Array.isArray(candidateStudentsToDelete)) // If total number > 700, api will not return student lists for selection
				{
					return tf.promiseBootbox.yesNo(`${totalNumberOfStudentsToDelete} records have been marked for deletion. Continue to delete all these records?`, "Confirm Message")
						.then(function(result)
						{
							if (result)
							{
								handleResponse.idsOfSelectedStudentsToDelete = null; // set to null means we select all candidate students to delete
							}
							else if (result === false)
							{
								handleResponse.idsOfSelectedStudentsToDelete = []; // do not delete any student
							}
							else 
							{
								handleResponse.idsOfSelectedStudentsToDelete = []; // do not delete any student
								handleResponse.cancelled = false;
							}
							return handleResponse;
						});
				}
				else
				{
					return tf.modalManager.showModal(new TF.Modal.StudentToBeDeletedModalViewModel(candidateStudentsToDelete, [])).then(function(result)
					{
						if (!result || !result.length)
						{
							handleResponse.idsOfSelectedStudentsToDelete = [];
						}
						else
						{
							handleResponse.idsOfSelectedStudentsToDelete = result.map(stud => stud.Id);
						}

						return handleResponse;
					});
				}
			}
		}).catch(function(ex)
		{
			tf.promiseBootbox.alert(ex.Message, "Error");
			handleResponse.cancelled = true;
			return handleResponse;
		});
	};

	StudentImportOptionsViewModel.prototype.getTfxDeletedStudentIds = async function(handleResponse)
	{
		var self = this;
		return sendTFXRequest(self, null, true).then(response =>
		{
			self.filePath = response.Items && response.Items[0] ? response.Items[0] : '';
			return self.getDeletedStudentIds(handleResponse, self.filePath);
		});
	}

	/**
	 * Import data into database.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.importData = function(idsOfSelectedStudentsToDelete, selectedSchoolCodes)
	{
		if (this.externalFileType === 'TFX')
		{
			return this.tfxImport(idsOfSelectedStudentsToDelete, selectedSchoolCodes);
		} else
		{
			return this.predefinedImport(idsOfSelectedStudentsToDelete, selectedSchoolCodes);
		}
	};

	StudentImportOptionsViewModel.prototype.cancelGetCompareDataDuringInteractiveMode = function(result)
	{
		let stepName = "usercancel_comparedata";
		return tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportedDataStepNameUrl(stepName), {
			data: result.compareResult,
			headers: {}
		}).then(res =>
		{
			console.log("The GetCompareData at interactive-mode was cancelled.");
		}).catch(ex =>
		{
			tf.promiseBootbox.alert(ex.Message)
		});
	};

	StudentImportOptionsViewModel.prototype.cancelCurrentRunningImportAction = function(result)
	{
		tf.promiseBootbox.yesNo("Are you sure you want to cancel?", "Confirm Message")
			.then(confirmResult =>
			{
				if (confirmResult)
				{
					let stepName = "usercancel_runningimport";
					result.compareResult.IsInteractiveCancel = true;
					return tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportedDataStepNameUrl(stepName), {
						data: result.compareResult,
						headers: {}
					}).catch(ex =>
					{
						tf.promiseBootbox.alert(ex.Message)
					});
				}
			});

	};

	StudentImportOptionsViewModel.prototype.revertImport = function(result)
	{
		result.compareResult.IsRevert = true;
		sendRequest(result.compareResult, 'revert');
	};

	StudentImportOptionsViewModel.prototype.predefinedImport = function(idsOfSelectedStudentsToDelete, selectedSchoolCodes)
	{
		let self = this;
		let result = {
			isInteractive: self.obInteractive(),
			importStatus: {},
			compareResult: {}
		}
		let importAndMergeDataOptions = {
			TFXTFDFields: self.TFDFields,
			ImportOptions: {
				IsFullImport: self.obFullImport(),
				IsFullImportForContact: self.obFullImportForContact(),
				IsRejectDuplicateContact: self.obRejectDuplicateContact(),
				IdsOfSelectedStudentsToDelete: idsOfSelectedStudentsToDelete,
				IsResetManuallyAdjustedLoadTimes: self.obRestetLoadTimes(),
			},
			AutoMationOptions: {
				IsGeoCode: self.obGeocode(),
				IsFindDistance: self.obDistance(),
				IsFindSchedule: self.obSchedule(),
				IsFindSchoolOfResidence: self.obResidence(),
				SelectedSchoolIds: selectedSchoolCodes,
				GeocodeSource: self.obSelectedGeocodeSource(),
				IsFindPopulationRegion: self.obPopulationRegion(),
				SelectedResdictIds: self.obSelectedResdictIds().split(",").filter(x => x).map(x => parseInt(x)),
				UngeocodeClearsSchoolOfResidence: self.obUngeocodeClearsSchoolOfResidence(),
				DoNotAddCardEndDates: self.obDoNotAddCardEndDates()
			}
		}

		if (self.obAbortUngeocoded())
		{
			importAndMergeDataOptions.ImportOptions.AllowUngeocodedPercentage = self.obUngeocodedNumber();
		}

		let importOption = importAndMergeDataOptions.ImportOptions;
		// Perform safety checks for deleted students when full import is selected and IdsOfSelectedStudentsToDelete is null (null means we select all candidate students to delete) or IdsOfSelectedStudentsToDelete is not empty
		if (self.obFullImport() && self.obAbortDeleted() && (isNullObj(importOption.IdsOfSelectedStudentsToDelete) || importOption.IdsOfSelectedStudentsToDelete.length > 0))
		{
			importAndMergeDataOptions.ImportOptions.AllowDeletedPercentage = self.obDeletedNumber();
		}

		if (self.obInteractive()) 
		{
			// If the importData action is triggered via Interactive-mode, we simply return the result with compareResult property assigned (as the sequential code need to launch the UpdateInformationModalViewModel)
			result.compareResult.TFXFilePath = this.obFileName();
			result.compareResult.fileName = self.getTFXFileName();
			result.compareResult.ImportAndMergeDataOptions = importAndMergeDataOptions;
			return Promise.resolve(result);
		}
		else
		{
			// If the importData action is triggered via non-Interactive-mode, we directly invoke ImportData api 
			tf.loadingIndicator.resetProgressbar();
			let revertEle = $('<button id="RevertBtn" type="submit" class="btn tf-btn-black" >Cancel</button>')
			revertEle.on('click', function(e)
			{
				self.cancelCurrentRunningImportAction(result);
			})

			let options = {
				elements: [revertEle]
			}
			tf.loadingIndicator.show(true, true, 0, options); // this one will show "Cancel button" in importing progress bar
			//tf.loadingIndicator.show(true); // Now we do not show revert button
			tf.loadingIndicator._isImportRunning = true; // indicating that the batch import action is execution (and progress-value will be reported from server)

			let postData;
			const paramData = {
				fileName: self.getTFXFileName()
			};

			if (self.compareResult)
			{
				// if self.compareResult exists, means we already enter interactive-mode, and now we're to continue the importdata operation by run-to-end mode
				self.compareResult.ImportAndMergeDataOptions = importAndMergeDataOptions;
				postData = self.compareResult;
				paramData.isAfterCompareData = true;
			}
			else
			{
				// If no self.compareResult exists, means we directly do importdata via run-to-end mode (non-interactive)
				postData = importAndMergeDataOptions;
				paramData.isCompare = false;
			}

			// Call API to create schedule job
			return tf.promiseAjax.post(
				TF.Helper.ApiUrlHelper.postImportedDataScheduledJobsUrl(self),
				{
					data: postData,
					paramData: paramData,
				},
				{ overlay: false }
			).then((res) =>
			{
				const scheduledJobId = res.Items[0].ID;
				return self.syncingImportStatus(scheduledJobId)
					.then((importStatus) =>
					{
						self.showSuccessStatus();
						result.importStatus = importStatus;
						return result;
					}, (error) =>
					{
						self.onImportRequestError(error);
					});
			}).catch(function(response)
			{
				// If error call error function.
				self.onImportRequestError(response);
			});
		}
	}

	/**
	 * Get current import status.
	 *
	 * @param {number} scheduledJobId
	 * @returns
	 */
	StudentImportOptionsViewModel.prototype.getImportStatus = function(scheduledJobId)
	{
		// Call api to get import result and status
		return tf.promiseAjax.get(
			pathCombine(tf.api.apiPrefix(), "ImportedData", "ScheduledJobImportStatus"),
			{ paramData: { scheduleJobId: scheduledJobId } },
			{ overlay: false }
		)
			.then((res) =>
			{
				const importStatus = res.Items[0];
				if (importStatus.JobCompleted)
				{
					return importStatus.ImportResult;
				}

				if (importStatus.ImportDataStatus)
				{
					this.statusUpdated(importStatus.ImportDataStatus);
				}

				return false;
			});
	}

	/**
	 * Start syncing import status after configured interval.
	 *
	 * @param {number} scheduledJobId
	 * @returns
	 */
	StudentImportOptionsViewModel.prototype.syncingImportStatus = function(scheduledJobId)
	{
		return this.getImportStatus(scheduledJobId)
			.then((response) =>
			{
				if (!response)
				{
					const delayQuery = new Promise((resolve, reject) =>
					{
						setTimeout(() =>
						{
							const nextQuery = this.syncingImportStatus(scheduledJobId);
							resolve(nextQuery);

						}, SYNC_IMPORT_STATUS_INTERVAL);
					});

					return delayQuery;
				}

				return response;
			});
	}

	StudentImportOptionsViewModel.prototype.showSuccessStatus = function()
	{
		// Set percentage to 100.
		this.statusUpdated({ Percentage: 100, Message: "" });

		return new Promise((resolve) =>
		{
			// Show 2s 100% status.
			setTimeout(() =>
			{
				tf.loadingIndicator.tryHide();
				tf.loadingIndicator._isImportRunning = false;

				// Reset progress bar after show 2s.
				setTimeout(() =>
				{
					tf.loadingIndicator.resetProgressbar();
				}, 200);

				// Call resolve to next promise step.
				resolve();

			}, 2000);
		})
	}

	/**
	 * Call this function when import request error.
	 * @param {*} response 
	 * @returns 
	 */
	StudentImportOptionsViewModel.prototype.onImportRequestError = function(response)
	{
		tf.loadingIndicator.tryHide();
		tf.loadingIndicator._isImportRunning = false; // Reset _isImportRunning flag on the global LoadingIndicator
		setTimeout(() =>
		{
			tf.loadingIndicator.resetProgressbar();
		}, 200)

		let statusCode = response.StatusCode, msg = response.Message;
		if (statusCode >= 400)
		{
			tf.promiseBootbox.alert(msg, "Error").then(() => this.onImportAborted.notify());
			return false;
		}
	}

	StudentImportOptionsViewModel.prototype.tfxImport = function(idsOfSelectedStudentsToDelete, selectedSchoolCodes)
	{
		var self = this;
		var result = {
			isInteractive: self.obInteractive(),
			importStatus: {},
			compareResult: {}
		}
		var importAndMergeDataOptions = {
			TFXTFDFields: self.TFDFields,
			ImportOptions: {
				IsFullImport: self.obFullImport(),
				IsFullImportForContact: self.obFullImportForContact(),
				IsRejectDuplicateContact: self.obRejectDuplicateContact(),
				IdsOfSelectedStudentsToDelete: idsOfSelectedStudentsToDelete,
				IsResetManuallyAdjustedLoadTimes: self.obRestetLoadTimes(),
			},
			AutoMationOptions: {
				IsGeoCode: self.obGeocode(),
				IsFindDistance: self.obDistance(),
				IsFindSchedule: self.obSchedule(),
				IsFindSchoolOfResidence: self.obResidence(),
				SelectedSchoolIds: selectedSchoolCodes,
				GeocodeSource: self.obSelectedGeocodeSource(),
				IsFindPopulationRegion: self.obPopulationRegion(),
				SelectedResdictIds: self.obSelectedResdictIds().split(",").filter(x => x).map(x => parseInt(x)),
				UngeocodeClearsSchoolOfResidence: self.obUngeocodeClearsSchoolOfResidence(),
				DoNotAddCardEndDates: self.obDoNotAddCardEndDates()
			}
		}
		tf.loadingIndicator.resetProgressbar();
		tf.loadingIndicator.show(true);
		if (self.obInteractive())
		{
			return sendTFXRequest(self, importAndMergeDataOptions, !self.obFullImport()).then(response =>
			{
				if (response && response.Items && response.Items[0])
				{
					result.compareResult = response.Items[0];
				}

				if (result.compareResult.IsFinish)
				{
					return sendRequest(result.compareResult, 'finish').then(() =>
					{
						if (response && response.Items && response.Items[0])
						{
							result.importStatus = response.Items[0];
						}
						result.isInteractive = false;
						return result;
					}).catch((e) =>
					{
						tf.promiseBootbox.alert(message, "Error");
						tf.loadingIndicator.tryHide();
						tf.loadingIndicator.resetProgressbar();
						return false;
					})
				}
				else
				{
					tf.loadingIndicator.tryHide();
					tf.loadingIndicator.resetProgressbar();
					return result;
				}
			}).catch(() =>
			{
				tf.promiseBootbox.alert(message, "Error");
				tf.loadingIndicator.tryHide();
				tf.loadingIndicator.resetProgressbar();
				return false;
			})
		} else
		{
			return sendTFXRequest(self, importAndMergeDataOptions, !self.obFullImport()).then(response =>
			{
				tf.loadingIndicator.tryHide();
				tf.loadingIndicator.resetProgressbar();
				if (response && response.Items && response.Items[0])
				{
					result.importStatus = response.Items[0];
				}

				return result;
			}).catch(() =>
			{
				tf.promiseBootbox.alert(message, "Error");
				tf.loadingIndicator.tryHide();
				tf.loadingIndicator.resetProgressbar();
				return resolve(false);
			});
		}
	}

	/**
	 * Change the geocode checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.interactiveCheckboxClick = function()
	{
		var self = this;
		self.obInteractive(!self.obInteractive());
		return false;
	};

	/**
	 * Change the geocode checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.fullImportCheckboxClick = function()
	{
		var self = this;
		self.obFullImport(!self.obFullImport());
		return false;
	};

	StudentImportOptionsViewModel.prototype.fullContactImportCheckboxClick = function()
	{
		var self = this;
		self.obFullImportForContact(!self.obFullImportForContact());
		return false;
	};

	StudentImportOptionsViewModel.prototype.rejectDuplicateContactCheckboxClick = function()
	{
		var self = this;
		self.obRejectDuplicateContact(!self.obRejectDuplicateContact());
		return false;
	};

	/**
	 * Change the geocode checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.restetLoadTimesCheckboxClick = function()
	{
		var self = this;
		self.obRestetLoadTimes(!self.obRestetLoadTimes());

		return false;
	};

	/**
	 * Change the geocode checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.geocodeCheckboxClick = function()
	{
		var self = this;
		self.obGeocode(!self.obGeocode());

		return false;
	};

	/**
	 * Change the distance checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.distanceCheckboxClick = function()
	{
		var self = this;
		self.obDistance(!self.obDistance());

		return false;
	};

	/**
	 * Change the schedule checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.scheduleCheckboxClick = function()
	{
		var self = this;
		self.obSchedule(!self.obSchedule());

		return false;
	};

	/**
	 * Change the residence checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.residenceCheckboxClick = function()
	{
		var self = this;
		self.obResidence(!self.obResidence());
	};

	/**
	 * Change the population region checkbox status.
	 * @return {void}
	 */
	StudentImportOptionsViewModel.prototype.populationRegionCheckboxClick = function()
	{
		var self = this;
		self.obPopulationRegion(!self.obPopulationRegion());
	};

	/**
	  * Click abort ungeocoded checkbox.
	  * @return {void}
	  */
	StudentImportOptionsViewModel.prototype.abortUngeocodedBoxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (!checkClick)
		{
			self.obAbortUngeocoded(!self.obAbortUngeocoded());
			if (self.obAbortUngeocoded())
			{
				$('input[name="UngeocodedNumber"]').data('kendoNumericTextBox').focus();
			}
			return;
		}
	};

	StudentImportOptionsViewModel.prototype.geocodedNumberChangeEvent = function(viewModel)
	{
		var self = this;
		self.selectedTableConfig().AllowUngeocodedPercentage = self.obUngeocodedNumber();
	}

	StudentImportOptionsViewModel.prototype.deletedNumberChangeEvent = function(viewModel)
	{
		var self = this;
		self.selectedTableConfig().AllowDeletedPercentage = self.obDeletedNumber();
	}

	/**
	* Click abort deleted checkbox.
	* @return {void}
	*/
	StudentImportOptionsViewModel.prototype.abortDeletedBoxClick = function(viewModel, e, checkClick)
	{
		var self = this;
		if (!checkClick)
		{
			self.obAbortDeleted(!self.obAbortDeleted());
			if (self.obAbortDeleted())
			{
				$('input[name="DeletedNumber"]').data('kendoNumericTextBox').focus();
			}
			return;
		}
	};

	/**
	* Valid numeric input
	* @param  {viewModel} data StudentImportOptionsViewModel.
	* @param  {Event} e jQuery Event object.
	* @returns {boolean}
	*/
	StudentImportOptionsViewModel.prototype.numericalValidation = function(data, e)
	{
		var key = e.which || e.keyCode || 0;
		// Only number keys and backspace, arrowleft,arrowright are available, value is in the range of [1,100]
		if (key == 37 || key == 39 || key == 8)
		{
			return true;
		}
		if (!isNumber(e.key) || (e.target.value + e.key > 100 || e.key == 0 && e.target.value == 0))
		{
			e.preventDefault();
			e.stopPropagation();
			return false;
		}

		return true;
	};


	function sendRequest(options, step)
	{
		let stepName = step;
		return tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportedDataStepNameUrl(stepName), {
			data: options,
			headers: {
				'ConnectionId': TF.getConnectionId()
			}
		});
	}

	function sendTFXRequest(self, options, firstUploadFile)
	{
		var url = TF.Helper.ApiUrlHelper.postImportedDataTfxUrl();
		var myFormData = new FormData();
		if (!firstUploadFile && self.filePath != '') myFormData.append('filePath', self.filePath);
		if (firstUploadFile) myFormData.append('file', self.file);
		if (!!options) myFormData.append('importAndMergeDataOptions', JSON.stringify(options));
		if (self.obFullImport() && firstUploadFile) myFormData.append('isFullImport', 'true');
		if (self.obFullImportForContact() && firstUploadFile) myFormData.append('isFullImportForContact', 'true');
		if (self.obRejectDuplicateContact() && firstUploadFile) myFormData.append('isRejectDuplicateContact', 'true');
		if (self.obInteractive())
		{
			myFormData.append('isCompare', 'true');
		} else
		{
			myFormData.append('isCompare', 'false');
		}
		return new Promise((resolve, reject) =>
		{
			$.ajax({
				url: pathCombine(tf.api.apiPrefix(), url),
				type: 'POST',
				processData: false, // important
				contentType: false, // important
				dataType: 'json',
				data: myFormData,
				headers: {
					'Token': tf.entStorageManager.get("token", true),
					'ConnectionId': TF.getConnectionId()
				},
				tfx: true,
				success: (response =>
				{
					return resolve(response);
				}),
				error: ((e) =>
				{
					return reject({
						message: e.responseJSON.Message,
						statusCode: e.status
					});
				})
			});
		}).catch((e) =>
		{
			console.log(e);
		});
	}

	StudentImportOptionsViewModel.prototype.dispose = function()
	{
		this.onHide.unsubscribeAll();
		this.onImportAborted.unsubscribeAll();
	};
})();