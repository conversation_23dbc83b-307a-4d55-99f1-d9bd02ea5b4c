﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleMakeDeletion = DataListVehicleMakeDeletion;

	function DataListVehicleMakeDeletion()
	{
		this.type = 'vehiclemake';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Make';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleMakeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleMakeDeletion.prototype.constructor = DataListVehicleMakeDeletion;

	DataListVehicleMakeDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?MakeChassisId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleMakeDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclemake", pb.DELETE), ids);
	}

	DataListVehicleMakeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();