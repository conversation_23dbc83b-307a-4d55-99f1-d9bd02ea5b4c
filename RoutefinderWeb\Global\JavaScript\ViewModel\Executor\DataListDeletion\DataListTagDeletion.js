(function()
{
	const namespace = createNamespace("TF.Executor");
	namespace.DataListTagDeletion = DataListTagDeletion;

	function DataListTagDeletion(withoutDatabase)
	{
		this.type = 'tags';
		this.deleteType = 'Tag';
		this.deleteRecordName = 'Tag';
		namespace.DataListBaseDeletion.apply(this, [withoutDatabase]);
	}

	DataListTagDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListTagDeletion.prototype.constructor = DataListTagDeletion;

	/**
	 * Query the entity lock status
	 */
	DataListTagDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	}

	/**
	 *
	 * @param {*} ids here should only one record, tags does not support to delete multiple record by one time
	 * @returns
	 */
	DataListTagDeletion.prototype.getAssociatedData = async function(ids)
	{
		const res = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `tags/${ids[0]}/status`));
		if (res?.InUse)
		{
			return [{
				type: 'tags',
				items: ids,
				message: "Cannot delete a tag that is associated with data records found in any data source."
			}];
		}

		return [];
	}
})();
