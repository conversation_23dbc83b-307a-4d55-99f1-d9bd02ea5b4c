﻿(function()
{
	createNamespace("TF.Modal").GenerateMergeReportModalViewModel = GenerateMergeReportModalViewModel;

	function GenerateMergeReportModalViewModel(udReport, output, options)
	{
		this.options = options;
		TF.Modal.BaseModalViewModel.call(this);
		var outputType = TF.Control.GenerateMergeReportViewModel.outputTypes[output];
		this.title(outputType.title + (outputType.titleWithDocName ? (": " + udReport.Document.Name) : ""));
		this.obPositiveButtonLabel(outputType.action);
		this.sizeCss = "modal-lg";
		this.contentTemplate('modal/GenerateMergeReportControl');
		this.buttonTemplate('modal/positivenegative');
		this.addReportViewModel = new TF.Control.GenerateMergeReportViewModel(udReport, output, options);
		this.data(this.addReportViewModel);
	}

	GenerateMergeReportModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	GenerateMergeReportModalViewModel.prototype.constructor = GenerateMergeReportModalViewModel;

	GenerateMergeReportModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		return this.addReportViewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
			}.bind(this))
			.catch(function()
			{
			});
	};

	GenerateMergeReportModalViewModel.prototype.negativeClick = function()
	{
		this.addReportViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};

	GenerateMergeReportModalViewModel.prototype.dispose = function()
	{
		this.addReportViewModel.dispose();
	};
})();
