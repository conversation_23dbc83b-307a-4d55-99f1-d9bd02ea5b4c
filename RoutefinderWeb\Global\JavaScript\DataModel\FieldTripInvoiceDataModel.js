﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.FieldTripInvoiceDataModel = function(fieldTripInvoiceEntity)
	{
		namespace.BaseDataModel.call(this, fieldTripInvoiceEntity);
	}

	namespace.FieldTripInvoiceDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.FieldTripInvoiceDataModel.prototype.constructor = namespace.FieldTripInvoiceDataModel;

	namespace.FieldTripInvoiceDataModel.prototype.mapping = [
		{ from: "PublicID", default: "" },
		{ from: "FieldTripName", default: null },
		{ from: "School", default: null },
		{ from: "DepartDatetime", default: null },
		{ from: "FieldTripStageName", default: null },
		{ from: "Amount", default: 0 },
		{ from: "PurchaseOrder", default: "" },
		{ from: "InvoiceDate", default: null },
		{ from: "PaymentDate", default: null },
		{ from: "SubmittedByName", default: "" },
		{ from: "LastUpdated", default: null },
		{ from: "LastUpdatedUserLoginId", default: null },
		{ from: "DepartFromSchool", default: null },
		{ from: "EstimatedReturnDatetime", default: null },
		{ from: "FieldTripClassificationCode", default: null },
		{ from: "FieldTripActivityName", default: null },
		{ from: "DistrictDepartmentName", default: null },
		{ from: "Destination", default: "" },
		{ from: "EstimatedDistance", default: 0 },
		{ from: "EstimatedHours", default: 0 },
		{ from: "EstimatedCost", default: 0 },
		{ from: "BillingClassificationName", default: null },
		{ from: "FieldTripAccountId", default: null },
		{ from: "AccountName", default: null },
		{ from: "FieldTripAccountCode", default: null },
		{ from: "FieldTripAccountDescription", default: null },
		{ from: "FieldTripAccountSchool", default: null },
		{ from: "FieldTripAccountDepartmentName", default: null },
		{ from: "FieldTripAccountFieldTripActivityName", default: null },
		{ from: "FieldTripAccountActiveFromDate", default: null },
		{ from: "FieldTripAccountActiveToDate", default: null },
	];

})();