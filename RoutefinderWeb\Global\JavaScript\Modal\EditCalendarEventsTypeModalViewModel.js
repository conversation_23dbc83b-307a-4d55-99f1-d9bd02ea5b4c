﻿(function()
{
	createNamespace('TF.Modal').EditCalendarEventsTypeModalViewModel = EditCalendarEventsTypeModalViewModel;

	function EditCalendarEventsTypeModalViewModel(fieldName, id)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.modalWidth("530px");
		this.contentTemplate('modal/EditCalendarEventsTypeControl');
		this.buttonTemplate('modal/positivenegative');
		this.EditCalendarEventsTypeViewModel = new TF.Control.EditCalendarEventsTypeViewModel(fieldName, id);
		this.data(this.EditCalendarEventsTypeViewModel);
		this.sizeCss = "modal-dialog-sm";
		if (id)
		{
			this.title('Edit Calendar Event Type');
		}
		else
		{
			this.title('Add Calendar Event Type');
			this.buttonTemplate('modal/positivenegativeextend');
		}
	}

	EditCalendarEventsTypeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	EditCalendarEventsTypeModalViewModel.prototype.constructor = EditCalendarEventsTypeModalViewModel;

	EditCalendarEventsTypeModalViewModel.prototype.positiveClick = function()
	{
		tf.loadingIndicator.show();
		this.EditCalendarEventsTypeViewModel.apply().then(function(result)
		{
			tf.loadingIndicator.tryHide();
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	EditCalendarEventsTypeModalViewModel.prototype.negativeClose = function(returnData)
	{
		if (this.EditCalendarEventsTypeViewModel.obEntityDataModel().apiIsDirty())
		{
			return tf.promiseBootbox.yesNo({ message: "You have unsaved changes.  Would you like to save your changes prior to closing?", backdrop: true, title: "Unsaved Changes", closeButton: true })
				.then(function(result)
				{
					if (result == true)
					{
						return this.positiveClick();
					}
					if (result == false)
					{
						return TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
					}
				}.bind(this));
		}
		else
		{
			TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
		}
	};

	EditCalendarEventsTypeModalViewModel.prototype.saveAndNewClick = function()
	{
		this.EditCalendarEventsTypeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.EditCalendarEventsTypeViewModel.obEntityDataModel(new this.EditCalendarEventsTypeViewModel.entityDataModel());
				this.newDataList.push(result);
				if ($("input[name=name]") && $("input[name=name]").length > 0)
				{
					$("input[name=name]").focus();
				}
				PubSub.publish(topicCombine(pb.DATA_CHANGE, "listmover"));
			}
		}.bind(this));
	};

	EditCalendarEventsTypeModalViewModel.prototype.dispose = function()
	{
		this.EditCalendarEventsTypeViewModel.dispose();
	};

})();
