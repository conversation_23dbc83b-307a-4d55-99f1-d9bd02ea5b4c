(function()
{
	createNamespace("TF.Modal.Event").EventFieldControl = EventFieldControl;

	function EventFieldControl(options, type)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-sm";
		this.title('Select Data');
		this.contentTemplate('modal/EventFieldControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Apply");
		this.viewModel = new TF.Modal.Event.EventFieldViewModel(options,type,this);
		this.data(this.viewModel);
	}

	EventFieldControl.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	EventFieldControl.prototype.constructor = EventFieldControl;

	EventFieldControl.prototype.positiveClick = function(viewModel, e)
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	EventFieldControl.prototype.negativeClick = function(viewModel, e)
	{
		this.negativeClose();
	}
})();