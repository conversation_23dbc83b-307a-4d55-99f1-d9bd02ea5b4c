﻿(function()
{
	createNamespace('TF.Control.ResourceScheduler').SelectEditItemViewModel = SelectEditItemViewModel;

	function SelectEditItemViewModel(items)
	{
		items = items.filter(item => item.Type !== "conflict")
			.map((item, index) =>
			{
				return {
					displayTitle: String.format("({0}) {1}", item.Type, item.Name),
					entity: item,
					index: index
				};
			});
		this.obItems = ko.observableArray(items);
		this.obSelectedIndex = ko.observable(0);
	}

	SelectEditItemViewModel.prototype.selectItemClick = function(item, e)
	{
		this.obSelectedIndex(item.index);
	};

	SelectEditItemViewModel.prototype.apply = function()
	{
		var self = this;
		return Promise.resolve(true).then(function()
		{
			return self.obItems()[self.obSelectedIndex()].entity;
		});
	};
})();