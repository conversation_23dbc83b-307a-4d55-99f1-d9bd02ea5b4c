(function()
{
	createNamespace('TF.Control').CopyTripViewModel = CopyTripViewModel;

	function CopyTripViewModel(tripName, tripType)
	{
		this.obName = ko.observable(tripName);
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		this.obContentText = ko.observable("Create a new trip by copying " + this.obName() + ". You may preserve the Trip Type or change the trip's direction. Additionally, you may assign the same students and/or reverse the order of the trip stops.");

		this.obTripType = ko.observable(tripType);
		this.obTripOptionsStop = ko.observable(0);
		this.obTripOptionsStudent = ko.observable(1);
	}

	CopyTripViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate()
		.then(function(result)
		{
			if (result)
			{
				return {
					Name: this.obName(),
					Session: this.obTripType(),
					ReverseTripStop: this.obTripOptionsStop(),
					AssignStudent: this.obTripOptionsStudent()
				};
			}
		}.bind(this));
	}

	CopyTripViewModel.prototype.init = function(viewModel, el)
	{
		this.$form = $(el);

		var validatorFields = {}, isValidating = false, self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.name = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: "must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "trip", "uniquenamecheck"),
							{
								paramData: {
									id: 0,
									name: this.obName()
								}
							},
							{ overlay: false })
						.then(function(apiResponse)
						{
							return apiResponse.Items[0];
						})
					}.bind(this)
				}
			}
		};

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		setTimeout(function()
		{
			this.load();
		}.bind(this), 0);

		this.$form.find("input[name=name]").focus();
	};

	CopyTripViewModel.prototype.load = function()
	{
		//if (this.obEntityDataModel().id())
		//{
		//	tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), this.fieldName, this.obEntityDataModel().id()))
		//	.then(function(data)
		//	{
		//		this.obEntityDataModel(new this.entityDataModel(data.Items[0]));
		//	}.bind(this))
		//}

		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	};

	CopyTripViewModel.prototype.apply = function()
	{
		return this.save()
		.then(function(data)
		{
			return data;
			this.dispose();
		}, function()
		{
		});
	};

	CopyTripViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

