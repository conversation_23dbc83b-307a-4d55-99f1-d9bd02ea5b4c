﻿(function()
{
	createNamespace('TF.Control').AddOneFieldViewModel = AddOneFieldViewModel;

	function AddOneFieldViewModel(type, fieldName, data, fieldDisplay, withoutDatabase, maxlength)
	{
		this.obFieldName = ko.observable(fieldName);
		this.fieldDisplay = fieldDisplay || fieldName;
		this.fieldName = fieldName.toLowerCase();
		this.obFieldValue = ko.observable();
		this.obEntityDataModel = ko.observable(data);
		this.initFileText = this.obEntityDataModel()[this.fieldName]();
		this.type = type;
		this.withoutDatabase = withoutDatabase;
		this.apiPrefix = withoutDatabase ? tf.api.apiPrefixWithoutDatabase() : tf.api.apiPrefix();
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.maxlength = maxlength || 49;
		this.isSaving = false;
	}

	AddOneFieldViewModel.prototype.save = function()
	{
		return this.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(result)
			{
				if (result)
				{
					if (this.type === "fieldtriptemplate")
					{// no need to save data in DB, so direct return the value.
						return Promise.resolve(this.obEntityDataModel()[this.fieldName]());
					}
					if (this.isSaving)
					{
						return;
					}
					this.isSaving = true;

					if (this.type === "mailingpostalcode")
					{
						this.obEntityDataModel().postal(this.obEntityDataModel().postal()?.trim());
					}

					var isNew = this.obEntityDataModel().id() ? false : true;
					return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(this.apiPrefix, tf.dataTypeHelper.getEndpoint(this.type)),
						{ data: [this.obEntityDataModel().toData()] })
						.then(function(data)
						{
							this.isSaving = false;
							PubSub.publish(topicCombine(pb.DATA_CHANGE, this.type, pb.EDIT));
							return data.Items[0];
						}.bind(this)).catch(() =>
						{
							this.isSaving = false;
						});
				}
			}.bind(this));
	};

	AddOneFieldViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		this.$form = $(el);
		var validatorFields = {}, isValidating = false, self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};
		validatorFields.fieldName = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: self.fieldDisplay + " is required"
				},
				callback: {
					message: self.fieldDisplay + " must be unique",
					callback: function(value, validator, $field)
					{
						value = value == null ? null : value.trim();
						
						if (!value)
						{
							updateErrors($field, "unique");
							return {valid: true, message: ''};
						}
						else
						{
							updateErrors($field, "required");
						}

						if (this.type === 'mailingpostalcode')
						{
							const usZipCodeRegex = /(^\d{5}$)|(^\d{5}-\d{4}$)/;
							const commonWealthZipCodeRegex = /^[ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ] \d[ABCEGHJKLMNPRSTVWXYZ][0-9]$/;
							if (value.length > 7 || (!usZipCodeRegex.test(value) && !commonWealthZipCodeRegex.test(value)))
							{
								return {valid: false, message: `The input is not a valid postal code`};
							}
						}

						var data = { Id: this.obEntityDataModel().id() };
						data[this.obFieldName()] = value;

						return tf.promiseAjax.get(pathCombine(this.apiPrefix, tf.dataTypeHelper.getEndpoint(this.type)), {
							paramData: {
								"@filter": "eq(" + this.obFieldName() + "," + value + ")"
							}
						}, { overlay: false })
						.then(function(apiResponse)
						{

							const uniqueValueCheckResult = !apiResponse.Items.some(function(item)
							{
								var itemId = item.Id == null ? item.ID : item.Id;
								return item[this.obFieldName()].toLowerCase() == value.toLowerCase() && itemId != data.Id;
							}.bind(this));

							return {valid: uniqueValueCheckResult, message: uniqueValueCheckResult ? '' : `${self.fieldDisplay} must be unique`};
						}.bind(this));
					}.bind(this)
				}
			}
		};

		if (this.type === 'fieldtriptemplate')
		{
			validatorFields.fieldName.validators.callback = {
				message: 'Its name must be unique',
				callback: function(value, validator, $field)
				{
					if (value == "" || this.obEntityDataModel()[this.fieldName]() == "")
					{
						return true;
					}

					return tf.promiseAjax.get(pathCombine(this.apiPrefix, "fieldtriptemplate", "uniquenamecheck"),
						{
							paramData: {
								name: this.obEntityDataModel()[this.fieldName]()
							}
						},
						{ overlay: false })
						.then(function(data)
						{
							return !data.Items.some(function(item)
							{
								return item;
							}.bind(this));
						}.bind(this));
				}.bind(this)
			}
		}

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.load();
	};

	AddOneFieldViewModel.prototype.load = function()
	{
		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	};

	AddOneFieldViewModel.prototype.apply = function()
	{
		var self = this;
		return self.save()
			.then(function(data)
			{
				self.dispose();
				return data;
			});
	};

	AddOneFieldViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

