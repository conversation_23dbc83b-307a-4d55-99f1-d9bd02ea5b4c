﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleBodyTypeDeletion = VehicleBodyTypeDeletion;

	function VehicleBodyTypeDeletion()
	{
		this.type = 'vehiclebodytype';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleBodyTypeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleBodyTypeDeletion.prototype.constructor = VehicleBodyTypeDeletion;

	VehicleBodyTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();