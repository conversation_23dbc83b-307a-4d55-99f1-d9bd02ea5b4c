﻿(function()
{
	createNamespace('TF.Modal').EditFieldTripNoteModalViewModel = EditFieldTripNoteModalViewModel;

	function EditFieldTripNoteModalViewModel(type, fieldTripId, note)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/editfieldtripnotecontrol');
		this.buttonTemplate('modal/positivenegative');
		this.obVisibleControl = ko.observable(false);
		this.editFieldTripNoteViewModel = new TF.Control.EditFieldTripNoteViewModel(type, fieldTripId, note, this.obVisibleControl);
		this.data(this.editFieldTripNoteViewModel);
		this.sizeCss = "modal-dialog-sm";

		var viewTitle;

		switch (type)
		{
			case 'direction':
				viewTitle = "Edit Directions";
				this.obVisibleControl(true);
				break;
			case 'destination':
				viewTitle = tf.applicationTerm.getApplicationTermSingularByName("Destination Notes");
				break;
			case 'departure':
				viewTitle = tf.applicationTerm.getApplicationTermSingularByName("Departure Notes");
				break;
			default:
				viewTitle = "Edit Note";
				break;
		}
		this.title(viewTitle);
	}

	EditFieldTripNoteModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	EditFieldTripNoteModalViewModel.prototype.constructor = EditFieldTripNoteModalViewModel;

	EditFieldTripNoteModalViewModel.prototype.positiveClick = function()
	{
		this.editFieldTripNoteViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();
