(function()
{
	createNamespace('TF.Control').ListMoverForAdjustSupportedLanguagesViewModel = ListMoverForAdjustSupportedLanguagesViewModel;
	function ListMoverForAdjustSupportedLanguagesViewModel(selectedData, options)
	{
		options.getUrl = function(gridType)
		{
			return pathCombine(tf.api.apiPrefix(), "search", "language");
		};
		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
	}

	ListMoverForAdjustSupportedLanguagesViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverForAdjustSupportedLanguagesViewModel.prototype.constructor = ListMoverForAdjustSupportedLanguagesViewModel;

	ListMoverForAdjustSupportedLanguagesViewModel.prototype.columnSources = {
		language: [
			{
				FieldName: 'LocalLanguage',
				DisplayName: 'Name',
				Width: '120px',
				type: 'string',
				isSortItem: true,
				template: function(data)
				{
					return data.LocalLanguage;
				}
			}
		]
	};


	// ListMoverForAdjustSupportedLanguagesViewModel.prototype.getFields = function()
	// {
	// 	return this.columns.map(function(item) { return item.FieldName; }).concat(['Id','Description']);
	// };

	ListMoverForAdjustSupportedLanguagesViewModel.prototype.apply = function()
	{
		// Todo: input data struct (studentDataModel) different with output data struct (subClass),  need convert data
		// TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this);
		return new Promise(function(resolve, reject)
		{
			if (this.options.mustSelect && this.selectedData.length === 0)
			{
				reject();
			} else
			{
				resolve(this.selectedData);
			}
		}.bind(this));
	};

	ListMoverForAdjustSupportedLanguagesViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (!isArraySame(this.oldData, this.selectedData))
			{
				return tf.promiseBootbox.yesNo("You have unsaved changes.  Are you sure you want to cancel?", "Confirmation Message").then(function(result)
				{
					if (result)
					{
						resolve(true);
					}
					else
					{
						reject();
					}
				});
			} else
			{
				resolve(true);
			}
		}.bind(this));
	};


	ListMoverForAdjustSupportedLanguagesViewModel.prototype.afterInit = function()
	{
		this.$form.find(".checkbox.list-mover-grid-right-label").hide();
		this.$form.find(".checkbox.list-mover-grid-right-label").hide();
	}

	ListMoverForAdjustSupportedLanguagesViewModel.prototype.onBeforeLeftGridDataBound = function(leftSearchGrid)
	{
	};

	ListMoverForAdjustSupportedLanguagesViewModel.prototype.onAddClick = function(viewModel, el)
	{

	};

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var oldIds = oldData.map(function(item)
		{
			return item.Id;
		});
		var newIds = newData.map(function(item)
		{
			return item.Id;
		});
		var diffData1 = Enumerable.From(newIds).Where(function(x)
		{
			return !Array.contain(oldIds, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldIds).Where(function(x)
		{
			return !Array.contain(newIds, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();
