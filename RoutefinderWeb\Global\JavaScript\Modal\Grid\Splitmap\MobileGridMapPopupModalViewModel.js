
(function()
{
	createNamespace("TF.Modal").MobileGridMapPopupModalViewModel = MobileGridMapPopupModalViewModel;

	function MobileGridMapPopupModalViewModel(dataModels, event, mapPopup, crossTypeOptions)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.title(null);

		var dataTypeName = tf.dataTypeHelper.getDisplayNameByDataType(mapPopup.options.type);
		var pageTitle = tf.applicationTerm.getApplicationTermPluralByName(dataTypeName).toUpperCase();

		self.pageTitle = ko.observable(pageTitle);

		self.sizeCss = "modal-fullscreen";
		self.modalClass = 'mobile-modal-grid-modal has-navigation-bar';

		if (tf.isViewfinder)
		{
			self.contentTemplate("modal/mobileGridMapPopup");
		}
		else
		{
			self.contentTemplate("en-US/Html/modal/mobileGridMapPopup");
		}

		self.mobileGridMapPopupViewModel = new TF.Modal.MobileGridMapPopupViewModel(dataModels, event, mapPopup, crossTypeOptions);
		self.data(self.mobileGridMapPopupViewModel);
	}

	MobileGridMapPopupModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	MobileGridMapPopupModalViewModel.prototype.constructor = MobileGridMapPopupModalViewModel;

	MobileGridMapPopupModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose(false);
	};
})();