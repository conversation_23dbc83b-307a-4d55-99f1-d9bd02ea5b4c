﻿.tf-trip-stop-map-view-continer {
	border-style: groove;
	border-width: 1px;
	margin: 20px 0;
}

.tf-trip-stop-map-view,
.tf-trip-stop-list-view {
	padding: 0px;
}

.tf-trip-stop-list-view {
	overflow-x: hidden;
}

.trip-info-bar {
	height: 30px;
	line-height: 30px;
	background-color: #6b6b6b;
	padding: 0 5px;
}

.trip-info-bar.left-border {
	border-width: 0px 0px 0px 1px;
	border-style: groove;
	border-color: white;
}

.trip-info-note {
	color: #ffffff;
	margin: 0 2px;
}

.trip-info-note.info {
	margin: 0px 2px;
	opacity: 0.6;
}

.trip-info-note.divider {
	margin: 0px 5px;
}

.trip-info-note.vcenter {
	vertical-align: middle;
}

.trip-info-list-continer,
.trip-info-map-continer {
	height: 450px;
}

.trip-info-list-continer {
	overflow-y: hidden;
	overflow-x: auto;
}

.trip-info-list-continer.has-scrollbar {
	overflow-y: auto;
}

.trip-info-list-continer ul {
	clear: both;
	list-style: none;
	margin: 0px;
	padding: 0px;
}

.trip-info-map-continer {
	background: #e1e1e1;
}

.trip-info-list-item {
	min-width: 410px;
	line-height: 30px;
	padding-right: 10px;
	border-width: 0px 0px 1px 0px;
	border-style: groove;
	font-weight: bold;
	opacity: 0.8;
	cursor: default;
}

.trip-info-list-item .sequence-no {
	font-size: 1.3em;
	opacity: 1;
	font-weight: bold;
	margin: 0 5px 0 5px;
	color: #000000;
}

.trip-info-list-item .info {
	color: #808080;
}

.trip-info-list > li:nth-child(even) .trip-stop {
	background-color: #ecf2f9;
}

.trip-info-list-item:hover,
.trip-info-list > li:nth-child(n) .trip-stop:hover {
	opacity: 1;
	background-color: #ffffd6;
}

.tf-trip-stop-map-view-continer .iconbutton {
	vertical-align: middle;
	display: inline-block;
}

.tf-trip-stop-map-view-continer .iconbutton:hover {
	background-color: transparent;
	border: none;
}

.trip-info-list-item .hovered-display,
.trip-info-list-item .iconbutton.hovered-display {
	display: none;
}

.trip-info-list-student .trip-info-list-item:hover .iconbutton.hovered-display {
	display: inline-block;
}

.trip-info-list-item.is-focused:hover .hovered-hidden {
	display: none;
}

.trip-info-list-item.is-focused:hover .hovered-display {
	display: inline-block;
}

.trip-info-list > li {
	.istripstop.trip-stop.is-focused {
		opacity: 1;
		background-color: #d6def5;
	}

	.trip-stop.is-focused.is-deleted {
		opacity: 0.4;
	}

	.trip-info-list-student.is-deleted .trip-info-list-item {
		opacity: 0.3;
	}

	.trip-info-list-item.student.is-deleted {
		opacity: 0.3;
	}
}

.trip-info-list > li .trip-info-list-item div {
	float: left;
	margin-right: 2px;

	&.sequence-no {
		margin-right: 5px;
	}
}

.tf-trip-stop-map-view-continer .icon {
	height: 16px;
	width: 16px;
	display: inline-block;
	vertical-align: middle;
}

.icon-exception-large {
	height: inherit;
}

.icon-exception-small {
	margin-left: 2px;
	height: 15px;
	vertical-align: middle;
	display: inline-block;
}

.icon-exception-large,
.icon-exception-small {
	width: 5px;
	background-color: #ff9600;
}

.pull-right.trip-tripstop {
	height: 30px;
}
