button {
	outline: none;
}

.btn-fill {
	width: 100%;
}

/*div.olControlZoom{
	bottom: 15px;
	right: 8px;
	top: auto;
	left: auto;
}*/



/*.navbar{
	margin-bottom: auto;
}

.navbar .nav>li>a{
	padding: 5px 10px 5px;
	text-shadow:none;
	color: #DDD;
}

.navbar .nav>li>a:focus,.navbar .nav>li>a:hover{
	color: #FFF;
}

.navbar .nav>.active>a, .navbar .nav>.active>a:hover, .navbar .nav>.active>a:focus{
	background: #EB7A21;
	color: #FFF;
	cursor: pointer;
}

.navbar .nav>.active>a:hover{
	background: #FAA561;
}

.navbar-inner{
	min-height: 30px;
	background: #000;
	-webkit-border-radius:0;
	border-radius:0;
	border: none;
}

ul, ol {
	list-style: none;
	margin: 0;
}
	*/

.olControlLayerSwitcher {
	opacity: 0.2;
}

.clear {
	clear: both;
}

/*this is important, bootstrap and openlayers has a conflict*/
img.olTileImage {
	max-width: none;
}

/*
.popover-title span {
	display:block;
	float: left;
	width: 236px;
}
*/


/*.modal
{
	overflow: hidden;
}

.modal-dialog{
	margin-right: 0;
	margin-left: 0;
}*/


label {
	margin-bottom: 0;
}

.btn[disabled] {
	opacity: 0.4;
}

.form-control {
	height: 22px;
	padding: 2px 8px;
	-webkit-box-shadow: none;
	box-shadow: none;
	-webkit-appearance: none;

	&:focus {
		-webkit-box-shadow: none;
		box-shadow: none;
		-webkit-appearance: none;
	}

	&.k-timepicker {
		display: block;
		width: 100%;
	}

	&.k-datepicker {
		display: block;
		width: 100%;
		background-color: white;

		.k-picker-wrap {
			border-radius: 0;
			height: 20px;
			box-shadow: none;

			input {
				height: 20px;
				min-height: 20px;
				padding: 2px 8px;
				box-sizing: border-box;
				color: #333;
				text-indent: 0;
			}

			.k-icon.k-i-calendar {
				margin: 3px 0 0 3px;
				display: block;
			}
		}
	}

	&.input-datepicker-no-border {
		border: 0px;
		background: transparent;
	}
}

.input-group {
	width: 100%;
}

.form-inline .input-group {
	width: auto;
}

.toast-top-right {
	top: 60px;
	right: 15px;
}

.toast-top-left {
	top: 60px;
}

.container.noafterclear::after {
	content: none;
	clear: none;
}

/*select.hiddenSelect{
	width: 0;
	height: 0;
	border: none;
}*/

.has-warning.form-control {
	border-color: #8a6d3b;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-warning.form-control:focus {
	border-color: #66512c;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}

.has-success.form-control {
	border-color: #3c763d;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-success.form-control:focus {
	border-color: #2b542c;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}

.has-error.form-control {
	border-color: #a94442;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.has-error.form-control:focus {
	border-color: #843534;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}

.progress.active .progress-bar,
.progress-bar.active {
	-webkit-animation: progress-bar-stripes 0.7s linear infinite;
	-o-animation: progress-bar-stripes 0.7s linear infinite;
	animation: progress-bar-stripes 0.7s linear infinite;
}

.input-group .datepickerbutton {
	position: static;
	border-radius: 0 !important;
	padding: 0 2px 0 3px;
}

/*bootstrap datetimepicker*/
.bootstrap-datetimepicker-widget table td span {
	height: 24px;
	line-height: 24px;
}

.bootstrap-datetimepicker-widget table td {
	height: 18px;
	line-height: 18px;
}

/**/

.input-group-addon {
	padding: 0 4px;
	font-size: 12px;
	height: 22px;
}

.gps-report-daterange .input-group-addon {
	height: 20px;
}

.tf-filter .input-group-addon {
	height: 20px;
}

.input-group-btn,
.document-dataentry .input-group-btn {

	>.btn,
	.btn {
		padding: 0px;
		font-size: 12px;
		height: 22px;
	}
}

.k-edit-form-container {

	input.k-textbox,
	.k-dropdown-wrap .k-select,
	.k-textbox>input,
	.k-autocomplete .k-input,
	.k-picker-wrap .k-input,
	.k-numeric-wrap .k-input,
	.k-dropdown-wrap .k-input,
	.k-selectbox .k-input {
		height: 22px;
		min-height: 22px;
		line-height: 18px;
		-moz-box-sizing: padding-box;
		-webkit-box-sizing: padding-box;
		box-sizing: padding-box;
	}
}

.k-filter-row {
	.datepickerbutton.glyphicon-time:before {
		position: relative;
		left: -0.3px;
	}

	span.k-icon.k-i-calendar {
		position: relative;
		left: -0.3px;
	}

	span.k-icon.k-i-close {
		position: relative;
		left: 0.4px;
	}
}

.k-numeric-wrap .k-link.k-selected {
	background-color: inherit;
	background-image: none;

	span {
		transform: scale(0.8);
	}
}

.form-control.k-datepicker .k-picker-wrap {
	height: 22px;
}

.btn.btn-default.btn-sharp {
	.glyphicon {
		cursor: pointer;
		left: 0;
	}

	&.btn-calculateStopTime,
	&.btn-3dots {
		.glyphicon {
			top: 2px;
		}
	}
}

.input-group-btn>.btn,
.document-dataentry .input-group-btn>.btn,
.input-group-btn .btn,
.document-dataentry .input-group-btn .btn {
	width: 22px;
	min-width: 22px;
}

.modal-dialog .input-group-btn>button {
	min-width: 22px;
}

.forms .input-group-btn>button {
	min-width: 22px;
	background-color: #eeeeee;
	box-sizing: border-box;
}

.forms .group-udf-tab input[type=checkbox] {
	position: absolute;
	left: 0px;
	width: 1em;
	height: 1em;
	margin: 4px 0 0 0;
	vertical-align: top;
}

.tf-filter .form-control.datepickerinput {
	padding-left: 4px;
}

.has-success .input-group-addon {
	color: #555;
	border-color: #ccc;
	background-color: #eee;
}

.form-group {
	.input-group {
		.k-dropdowntree {
			>.k-input {
				line-height: 15px;
			}

			.k-select {
				height: 20px;
				min-height: 20px;
				background-color: #eee;
				line-height: 20px;
				border-left: solid 1px #BFBFBF;

				.k-icon {
					display: inline-block;
					width: 0;
					height: 0;
					vertical-align: middle;
					border-top: 4px solid;
					border-right: 4px solid transparent;
					border-left: 4px solid transparent;
				}
			}
		}
	}
}