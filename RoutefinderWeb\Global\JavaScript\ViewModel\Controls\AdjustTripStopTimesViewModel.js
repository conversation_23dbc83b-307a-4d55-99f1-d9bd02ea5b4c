﻿(function()
{
	createNamespace('TF.Control').AdjustTripStopTimesViewModel = AdjustTripStopTimesViewModel;
	var TripStopHelper = TF.Helper.TripStopHelper;
	function AdjustTripStopTimesViewModel(stopTimeCalc, totalStopTimeCalc, tripstop, tripdata)
	{
		this.tripStop = tripstop;
		this.stopTimeCalc = moment(stopTimeCalc, "hh:mm A");
		this.totalStopTimeCalc = totalStopTimeCalc;
		this.tripData = tripdata;
		this.tripData.TripStops = this.tripData.TripStops.sort(function(a, b) { return a.Sequence - b.Sequence });
		this.obMinutesDifferent = ko.observable();
		this.obEarlierOrLater = ko.observable();
		this.obTimeOverlapHint = ko.observable("may");
		this.obAdjust = ko.observable("none");
		this.obStartTime = ko.observable();
		this.obEndTime = ko.observable();
		this.obAllStartTime = ko.observable();
		this.obAllEndTime = ko.observable();
		this.obStartTimeViolate = ko.observable(false);
		this.obEndTimeViolate = ko.observable(false);
		this.tripStops = this.tripData.TripStops.map(function(item)
		{
			return {
				Id: item.Id,
				Sequence: item.Sequence,
				StopTime: tripstop.id() == item.Id ? tripstop.stopTime() : item.StopTimeFromDB,
				NewStopTime: tripstop.id() == item.Id ? tripstop.stopTime() : item.StopTimeFromDB,
				School: item.School
			}
		}.bind(this));
		this.init();
	}

	AdjustTripStopTimesViewModel.prototype.getSeconds = function(stopTime)
	{
		return TripStopHelper.convertToSeconds(moment(stopTime).format("LT"));
	};

	AdjustTripStopTimesViewModel.prototype.init = function()
	{
		var secondsDiff = this.getSeconds(this.tripStop.stopTime()) - this.getSeconds(this.stopTimeCalc);
		this.secondsDiff = secondsDiff;
		this.obEarlierOrLater(secondsDiff > 0 ? "later" : "earlier");
		this.obMinutesDifferent((Math.abs(secondsDiff) / 60).toFixed(0));
		var firstTripStop = this.tripStops[0];
		var lastTripStop = this.tripStops[this.tripStops.length - 1];

		this.obStartTime(TripStopHelper.formatTime(firstTripStop.StopTime));
		this.obEndTime(TripStopHelper.formatTime(lastTripStop.StopTime));

		this.secondsChangeDiff = this.getSeconds(this.tripStop.stopTime()) - this.getSeconds(this.tripStop._entityBackup.StopTime);
		var totalStopTimeChangeDiff = this.tripStop.totalStopTime() - this.tripStop._entityBackup.TotalStopTime;
		if (this.tripData.Session == 0 && this.tripStop.sequence() == this.tripStops.length)
		{
			totalStopTimeChangeDiff = -totalStopTimeChangeDiff;
		}

		for (var i = 0; i < this.tripData.TripStops.length; i++)
		{
			var earlierVerify = this.tripData.TripStops[i].Sequence < this.tripStop.sequence() &&
				moment(this.tripData.TripStops[i].StopTimeFromDB) > moment(this.tripStop.stopTime()).add(totalStopTimeChangeDiff, 's');
			var laterVerify = this.tripData.TripStops[i].Sequence > this.tripStop.sequence() &&
				moment(this.tripData.TripStops[i].StopTimeFromDB) < moment(this.tripStop.stopTime()).add(totalStopTimeChangeDiff, 's');
			if (earlierVerify || laterVerify)
			{
				this.obTimeOverlapHint("must");
				this.obAdjust = ko.observable("after");
				break;
			}
		}

		for (var i = 0; i < this.tripStops.length; i++)
		{
			if (this.tripStops[i].Sequence != this.tripStop.sequence())
			{
				this.tripStops[i].NewStopTime = this.addSeconds(this.tripStops[i].StopTime, this.secondsChangeDiff);
			}
			if (this.tripData.Session == 0 && this.tripStop.sequence() == this.tripStops.length && this.tripStops[i].Sequence != this.tripStop.sequence())
			{
				this.tripStops[i].NewStopTime = this.addSeconds(this.tripStops[i].NewStopTime, totalStopTimeChangeDiff);
			}
			else if (this.tripStops[i].Sequence > this.tripStop.sequence())
			{
				this.tripStops[i].NewStopTime = this.addSeconds(this.tripStops[i].NewStopTime, totalStopTimeChangeDiff);
			}
		}

		this.obAllStartTime(moment(this.tripStops[0].NewStopTime).format("LT"));
		this.obAllEndTime(moment(this.tripStops[this.tripStops.length - 1].NewStopTime).format("LT"));

		//if (this.tripStop.sequence() == 1 && this.tripData.Session == 0)
		//{
		//	this.obAllStartTime(this.addSeconds(this.tripStop.stopTime(), 0));
		//}

		if (this.tripData.Session == 1)
		{
			if (firstTripStop.School)
			{
				if (TripStopHelper.convertToSeconds(this.obAllStartTime()) < TripStopHelper.convertToSeconds((moment(firstTripStop.School.DepartTime)).format("LT")))
				{
					this.obStartTimeViolate(true);
				}
			}

		} else if (this.tripData.Session == 0)
		{
			if (lastTripStop.School)
			{
				if (TripStopHelper.convertToSeconds(this.obAllEndTime()) > TripStopHelper.convertToSeconds((moment(lastTripStop.School.ArrivalTime)).format("LT")))
				{
					this.obEndTimeViolate(true);
				}
			}
		}
	};


	AdjustTripStopTimesViewModel.prototype.addSeconds = function(time, seconds)
	{
		return moment(time).add(seconds, 's');
	};

	AdjustTripStopTimesViewModel.prototype.apply = function()
	{
		if (this.obAdjust() == "none")
		{
			return Promise.resolve(true);
		}
		var beforeTripStops = this.tripStops.filter(function(item)
		{
			return item.Sequence < this.tripStop.sequence();
		}.bind(this));
		var afterTripStops = this.tripStops.filter(function(item)
		{
			return item.Sequence > this.tripStop.sequence();
		}.bind(this));
		var updateTripStops = [];
		if (this.obAdjust() == "before")
		{
			updateTripStops = beforeTripStops;
		}
		if (this.obAdjust() == "after")
		{
			updateTripStops = afterTripStops;
		}
		if (this.obAdjust() == "all")
		{
			updateTripStops = beforeTripStops.concat(afterTripStops);
		}
		updateTripStops = updateTripStops.map(function(item)
		{
			return {
				Id: item.Id,
				Op: "replace",
				Path: "StopTime",
				Value: toISOStringWithoutTimeZone(moment(item.NewStopTime))
			}
		}.bind(this));
		return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), "tripstops"), {
			data: updateTripStops
		}).then(function(data)
		{
			if (data.Items[0])
			{
				return true;
			}
			return false;
		}.bind(this));
	};

	AdjustTripStopTimesViewModel.prototype.dispose = function()
	{

	};

})();