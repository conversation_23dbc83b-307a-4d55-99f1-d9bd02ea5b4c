(function()
{
	createNamespace('TF.Modal').StudentImportOptionsModalViewModel = StudentImportOptionsModalViewModel;

	function StudentImportOptionsModalViewModel(options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-md";
		self.contentTemplate('modal/import data/studentimportoptionscontrol');
		self.buttonTemplate('modal/positivenegative');

		// pass positiveClose function to view model, because events would be unsubscribed when modal is hidden.
		options["PositiveClose"] = self.positiveClose.bind(self);

		self.studentImportOptionsViewModel = new TF.Control.StudentImportOptionsViewModel(options, self.shortCutKeyHashMapKeyName);
		self.data(self.studentImportOptionsViewModel);
		self.title("Student Import Options");
		self.obPositiveButtonLabel("OK");
		self.obNegativeButtonLabel("Cancel");

		self.studentImportOptionsViewModel.onHide.subscribe(function(e, data)
		{
			self.hide();
		});

		self.studentImportOptionsViewModel.onImportAborted.subscribe(() => this.negativeClose(null, true));
	};

	StudentImportOptionsModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	StudentImportOptionsModalViewModel.prototype.constructor = StudentImportOptionsModalViewModel;

	/**
	 * The event of OK button click.
	 * @return {void}
	 */
	StudentImportOptionsModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.studentImportOptionsViewModel.apply().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};

	/**
	 * Close this modal.
	 * @return {void}
	 */
	StudentImportOptionsModalViewModel.prototype.negativeClose = function(returnData, skipCloseConfirm)
	{
		var self = this;
		let closeConfirm = true;

		if (!skipCloseConfirm)
		{
			closeConfirm = tf.promiseBootbox.yesNo({
				message: "Do you want to leave this page?",
				backdrop: true,
				title: "Confirm Message",
				closeButton: true,
			});
		}

		return Promise.resolve(closeConfirm)
			.then(function(result)
			{
				if (result === true)
				{
					return TF.Modal.BaseModalViewModel.prototype.negativeClose.call(self, returnData);
				}
			});
	};

})();
