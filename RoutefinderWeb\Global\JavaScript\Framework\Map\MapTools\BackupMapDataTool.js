(function()
{
	createNamespace("TF.Map").BackupMapDataTool = BackupMapDataTool;

	function BackupMapDataTool()
	{
		this._arcgis = tf.map.ArcGIS;
		this.backupMapDataUrl = arcgisUrls.getTFUtilitiesGPServiceTaskPath("BackupMapData");
	}

	BackupMapDataTool.prototype.constructor = BackupMapDataTool;

	BackupMapDataTool.prototype.hasMapBackupLocation = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tfsysinfo"), { paramData: { "@filter": "eq(InfoID, MapBackupLocation)" } })
			.then(response => response.Items && response.Items[0] && response.Items[0].InfoValue);
	}

	BackupMapDataTool.prototype.generateBackupFile = function(needDownload = true, needConfirm = true)
	{
		const url = this.backupMapDataUrl;
		return this.hasMapBackupLocation().then(result =>
		{
			if (!result)
			{
				return tf.promiseBootbox.alert("Map Backup Location is not specified. Please update the location and try again.").then(() => false);
			}

			if (needConfirm)
			{
				return tf.promiseBootbox.yesNo("Are you sure you want to create a map backup file?", "Confirmation Message");
			}

			return Promise.resolve(true);
		}).then(yes =>
		{
			if (!yes) return;

			let folderPath = arcgisUrls.LinuxFolderRootPath ? arcgisUrls.LinuxFolderRootPath : arcgisUrls.FileGDBPath,
				params = {
					'folder_path': arcgisUrls.LinuxFolderRootPath ? folderPath + '/' + arcgisUrls.ActiveServiceFolderName : folderPath + "\\" + arcgisUrls.ActiveServiceFolderName,
					'ClientID': tf.entStorageManager.get("clientKey"),
					uploadUrl: pathCombine(tf.api.apiPrefixWithoutDatabase(), "mapbackupfiles"),
					uploadUrlToken: tf.entStorageManager.get("token"),
				};
			if (tf.authManager.hasNationalMaps())
			{
				params["is_national_maps"] = true;
			}

			tf.loadingIndicator.setSubtitle('Exporting Backup File ');
			tf.loadingIndicator.show();
			return tf.map.ArcGIS.geoprocessor.submitJob(url, params).then(jobTask =>
			{
				return jobTask.waitForJobCompletion({ interval: TF.Map.BaseMap.longJobInterval }).then(jobResult =>
				{
					tf.loadingIndicator.tryHide();
					if (jobResult.jobStatus !== "job-succeeded")
					{
						return tf.promiseBootbox.alert("Map backup failed. Please check the ArcGIS logs.");
					}

					var downloadConfirm = Promise.resolve();
					if (needDownload)
					{
						downloadConfirm = tf.promiseBootbox.yesNo("Maps have been successfully backed up. Do you want to download a copy?", "Confirmation Message").then(isDownload =>
						{
							if (!isDownload) return;

							return jobResult.fetchResultData('ZipFile').then(zipFileResult =>
							{
								if (!zipFileResult.value) return;

								window.open(zipFileResult.value.url, '_blank');
							});
						})
					}

					return downloadConfirm.then(() =>
					{
						return { success: true }
					});
				}).catch(err =>
				{
					tf.loadingIndicator.tryHide();
					return tf.promiseBootbox.alert("Map backup failed. Please check the ArcGIS logs.");
				}).finally(() =>
				{
					if (TF.RoutingMap.MapEditSaveHelper.isHandledService(url))
					{
						TF.RoutingMap.MapEditSaveHelper.setSaving(false);
					}
				});
			});
		});
	}
})();