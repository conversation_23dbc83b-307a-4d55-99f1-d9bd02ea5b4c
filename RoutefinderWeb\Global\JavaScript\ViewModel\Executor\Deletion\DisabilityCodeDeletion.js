﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DisabilityCodeDeletion = DisabilityCodeDeletion;

	function DisabilityCodeDeletion()
	{
		this.type = 'disabilitycode';
		namespace.BaseDeletion.apply(this, arguments);
	}

	DisabilityCodeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	DisabilityCodeDeletion.prototype.constructor = DisabilityCodeDeletion;

	DisabilityCodeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();