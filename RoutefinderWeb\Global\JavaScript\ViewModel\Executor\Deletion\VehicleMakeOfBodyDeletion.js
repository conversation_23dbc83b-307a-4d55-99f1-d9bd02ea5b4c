﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleMakeOfBodyDeletion = VehicleMakeOfBodyDeletion;

	function VehicleMakeOfBodyDeletion()
	{
		this.type = 'vehiclemakeofbody';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleMakeOfBodyDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleMakeOfBodyDeletion.prototype.constructor = VehicleMakeOfBodyDeletion;

	VehicleMakeOfBodyDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();