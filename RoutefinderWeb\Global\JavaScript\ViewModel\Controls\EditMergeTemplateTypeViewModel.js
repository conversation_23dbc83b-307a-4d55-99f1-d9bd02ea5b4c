﻿(function()
{
	createNamespace("TF.Control").EditMergeTemplateTypeViewModel = EditMergeTemplateTypeViewModel;

	function EditMergeTemplateTypeViewModel(model, options)
	{
		var self = this;
		self.obSelectedTemplate = ko.observable(model);
		self.originalTemplate = self.obSelectedTemplate().toData();
		self.roleMergeDocuments = self.obSelectedTemplate().roleMergeDocuments();
		self.obTemplateNameEditable = ko.observable(options.templateNameEditable === undefined ? true : options.templateNameEditable);
		self.obAdvancedSettingExpended = ko.observable(false);
		self.hideRolesAccess = ko.observable(!!options.hideRolesAccess);

		self.previewLayout = new TF.Control.MergeDocumentPageLayout(self.obSelectedTemplate, true);

		self.obIsAssociateToOtherDocuments = ko.observable(options.isAssociateToOtherDocuments || false);
		self.init = self.init.bind(self);
		self.getValidatorFields = self.getValidatorFields.bind(self);

		self.obIsSmallModal = ko.observable(true);
		self.enableHighlightField = true;
		self.obSaveAsTemplateEnabled = ko.observable(!self.obIsAssociateToOtherDocuments());
		self.renderAdvancedSettingTemplate = self.renderAdvancedSettingTemplate.bind(self);

		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		self.recalculatePageSettingTimer;
		self.recalculatePageSetting = function(options)
		{
			if (self.recalculatePageSettingTimer)
			{
				clearTimeout(self.recalculatePageSettingTimer);
			}
			self.recalculatePageSettingTimer = setTimeout(function()
			{
				return self._recalculatePageSetting(options);
			}, 200);
		}.bind(self);

		self._recalculatePageSetting = function(options)
		{
			self.prevTemplateData = self.prevTemplateData || new TF.DataModel.MergeTemplateTypeModel(self.originalTemplate);
			var newTemplate = TF.MergeTemplateTypeCalculationHelper.recalculatePageSettingFields(
				self.obSelectedTemplate().toData(), options, self.prevTemplateData.toData());
			TF.MergeTemplateTypeHelper.resetTemplateData(newTemplate, self.prevTemplateData);
			TF.MergeTemplateTypeHelper.resetTemplateData(newTemplate, self.obSelectedTemplate());

			return newTemplate;
		}.bind(self);

	}

	EditMergeTemplateTypeViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.el = el;
		TF.MergeTemplateTypeHelper.bindMergeTemplateEvents(self);

		!self.hideRolesAccess() && self.initRolesAccessControl();
	};

	EditMergeTemplateTypeViewModel.prototype.dispose = function()
	{
		if (this.pageLevelViewModel)
		{
			this.pageLevelViewModel.dispose();
			this.pageLevelViewModel = null;
		}

		if (this.validator)
		{
			this.validator.destroy();
			this.validator = null;
		}
	};

	EditMergeTemplateTypeViewModel.prototype.renderAdvancedSettingTemplate = function()
	{
		var self = this,
			validatorFields = self.getValidatorFields();
		self.validator = ($(this.el).bootstrapValidator({
			excluded: [":disabled"],
			live: "enabled",
			fields: validatorFields
		})).data("bootstrapValidator");

		self.pageOrientationDropDownList = TF.MergeTemplateTypeHelper.initPageOrientationDropDownList(
			self.obSelectedTemplate,
			self.el,
			self.recalculatePageSetting,
			self);
		self.pageOrientationDropDownList.data('kendoDropDownList').enable(self.obSaveAsTemplateEnabled());
	};

	EditMergeTemplateTypeViewModel.prototype.onToggleAdvancedSettings = function(ev)
	{
		var self = this, element = $(self.el).find(".merge-doc-template-advanced-settings-container");

		if (!element.is(":visible"))
		{
			self.previewLayout.hide();
		}

		element.slideToggle("slow", function()
		{
			if (element.is(":visible"))
			{
				self.obAdvancedSettingExpended(true);
				self.previewLayout.show();
				self.previewLayout.refresh();
			}
			else
			{
				self.obAdvancedSettingExpended(false);
			}
		});
	};

	EditMergeTemplateTypeViewModel.prototype.getValidatorFields = function()
	{
		var self = this;
		var nameUniqueCheck = function(value)
		{
			if (!value || !value.trim() ||
				(self.obSelectedTemplate()._entityBackup.Name == value))
			{
				return true;
			}

			return tf.promiseAjax.get(
				pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergeTemplateTypes"),
				{ async: false, data: { name: value } },
				{ overlay: false }
			).then(function(apiResponse)
			{
				return !(apiResponse.Items[0] && apiResponse.Items[0].Id)
			});
		};

		var validatorFields = {
			name: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					callback: {
						message: "must be unique",
						callback: nameUniqueCheck
					}
				}
			}
		};

		validatorFields = $.extend({},
			validatorFields,
			TF.MergeTemplateTypeHelper.getAdvancedSettingValidator(self)
		);
		return validatorFields;
	}

	EditMergeTemplateTypeViewModel.prototype.apply = function(viewModel, e)
	{
		var self = this;
		return self.validate().then(function(valid)
		{
			return valid ? self.save() : pageLevelPrompt(self, self.pageLevelViewModel);
		});
	};

	EditMergeTemplateTypeViewModel.prototype.save = function(viewModel, e)
	{
		return Promise.resolve(true);
	};

	EditMergeTemplateTypeViewModel.prototype.validate = function(viewModel, e)
	{
		return this.validator ? this.validator.validate() : Promise.resolve(true);
	};

	EditMergeTemplateTypeViewModel.prototype.initRolesAccessControl = function()
	{
		var self = this;
		self.$typeRoles = $(self.el).find("#typeRoles");
		var typeRolesData = [
			{ text: "Administrator", value: -1 }
		];
		function disableAdministratorButton()
		{
			let $selectedValues = self.$typeRoles.parent().find(".k-input-values .k-chip");
			$selectedValues.filter((_, e) => $(e).find(".k-chip-label").text() === 'Administrator').addClass(TF.KendoClasses.STATE.DISABLED);
		}
		self.$typeRoles.kendoMultiSelect({
			enable: this.isRolesAccessControlEnable(),
			dataTextField: "text",
			dataValueField: "value",
			itemTemplate: '<input type="checkbox" style="margin-right: 5px"/> #= text #',
			downArrow: true,
			autoClose: false,
			dataSource: typeRolesData,
			value: [-1],
			select: function(e)
			{
				e.preventDefault();
				//to prevent list to auto scroll
				var offset = this.list.offset().top - this.ul.offset().top + 1;
				var dataItem = e.dataItem;
				if (dataItem.value === -1)
				{
					return;
				}
				const roles = self.typeRoles.value();
				roles.push(dataItem.value);
				self.typeRoles.value(roles);

				const roleMergeDocuments = roles.filter(role => role >= 0).map(role =>
				{
					return { RoleID: role }
				});
				self.obSelectedTemplate().roleMergeDocuments(roleMergeDocuments);
				this.list.find(".k-list-scroller").scrollTop(offset);
				disableAdministratorButton();
				self.checkRoleAccessSelectedCheckboxes();
			},
			deselect: function(e)
			{
				e.preventDefault();
				//to prevent list to auto scroll
				var offset = this.list.offset().top - this.ul.offset().top + 1;
				var dataItem = e.dataItem;
				if (dataItem == null || dataItem.value === -1)
				{
					return;
				}
				this.list.find(".k-list-scroller").scrollTop(offset);
				var roles = self.typeRoles.value();
				roles = roles.filter(x => x !== dataItem.value);
				self.typeRoles.value(roles);

				const roleMergeDocuments = roles.filter(role => role >= 0).map(role =>
				{
					return { RoleID: role }
				});
				self.obSelectedTemplate().roleMergeDocuments(roleMergeDocuments);

				disableAdministratorButton();
				self.checkRoleAccessSelectedCheckboxes();
			},
			close: function()
			{
				self.typeRoles.isOpen = false;
			},
			dataBound: function()
			{
				//RW-35992 Checkbox Status incorrect after filter and dataBound
				self.typeRoles && self.checkRoleAccessSelectedCheckboxes();
			}
		});
		self.typeRoles = self.$typeRoles.data("kendoMultiSelect");
		
		return self.setRolesDataSource(self.typeRoles, typeRolesData, self.gridType)
			.then(() =>
			{
				const selectedValues = [-1].concat(self.roleMergeDocuments ? self.roleMergeDocuments.map(x => x.RoleID) : []);
				self.typeRoles.value(selectedValues);
				if (typeRolesData.filter(x => x.value >= 0).length
					=== self.typeRoles.value().filter(x => x >= 0).length)
				{
					self.typeRoles.value(typeRolesData.map(x => x.value));
				}
				disableAdministratorButton();
				self.checkRoleAccessSelectedCheckboxes();
			});
	}

	EditMergeTemplateTypeViewModel.prototype.checkRoleAccessSelectedCheckboxes = function()
	{
		var self = this;
		var elements = self.typeRoles.ul.find("li");

		elements.each(index =>
		{
			const element = $(elements[index]);
			element.css("background-color", "transparent");
			const input = element.find("input[type='checkbox']");
			input.prop("checked", element.hasClass(TF.KendoClasses.STATE.SELECTED));
			if (index == 0 && element[0] && element[0].innerText.trim() == "Administrator")
			{
				input.prop("disabled", true);
			}

		});
	}

	EditMergeTemplateTypeViewModel.prototype.setRolesDataSource = function(typeRolesControl, typeRolesData)
	{
		return tf.authManager.getAllRolesData()
			.then(rolesData =>
			{
				for (const role of rolesData)
				{
					typeRolesData.push({ text: role.Name, value: role.RoleID });
				}
				const rolesDataSource = new kendo.data.DataSource({
					data: typeRolesData
				});
				typeRolesControl.setDataSource(rolesDataSource);
			});
	}

	EditMergeTemplateTypeViewModel.prototype.isRolesAccessControlEnable = function()
	{
		const authInfo = tf.authManager.authorizationInfo;
		if (authInfo.isAdmin)
		{
			return true;
		}
		else
		{
			const securedItems = authInfo.authorizationTree &&
				authInfo.authorizationTree.securedItems &&
				authInfo.authorizationTree.securedItems.mergeDocuments;

			if (securedItems != null && Array.isArray(securedItems))
			{
				return securedItems.some(item => item === 'save');
			}
			return false;
		}
	}

	function pageLevelPrompt(viewModel, plMsgBox)
	{
		var errors = $(viewModel.el).find("small[data-bv-result='INVALID']");
		plMsgBox.clearError();
		for (var i = 0; i < errors.length; i++)
		{
			var errorMessage = $(errors[i]).parent().find("label").html() + plMsgBox.getMessage(errors[i].innerHTML);
			plMsgBox.popupErrorMessage(errorMessage)
				.then(function()
				{
					return false;
				});
		}
	}
})();