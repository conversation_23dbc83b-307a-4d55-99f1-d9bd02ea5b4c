(function()
{
	createNamespace("TF.DataModel").RollOverDataModel = RollOverDataModel;

	function RollOverDataModel()
	{
		this.selectedDBID = ko.observable();
		this.useCurrentDBID = ko.computed({
			read: function()
			{
				return this.selectedDBID() === tf.datasourceManager.databaseId;
			}.bind(this),
			write: function(value)
			{
				var dbid = value ? tf.datasourceManager.databaseId : null;
				this.selectedDBID(dbid);
			}.bind(this)
		});

		this.dataSource = new TF.DataModel.DataSourceDataModel();
		this.repeatStudentIds = [];
		this.purgeGraduated = ko.observable(false);
		var currentYear = moment(new Date()).format("YYYY");
		this.cohort = ko.observable(currentYear);
		this.purgeGraduated.subscribe(function()
		{
			this.cohort(this.purgeGraduated() ? '' : currentYear);
		}, this);

		this.findSchedule = ko.observable(false);
		this.useStopsInStopPool = ko.observable(false);
		this.selectedStopPoolId = ko.observable();
		this.createDoorToDoorStops = ko.observable(false);
		this.findSchedule.subscribe(function()
		{
			if (!this.findSchedule())
			{
				this.useStopsInStopPool(false);
				this.createDoorToDoorStops(false);
			}
		}, this);
		this.findDistance = ko.observable(false);
		this.findResidence = ko.observable(false);
		this.residenceIds = ko.observableArray([]);

		this.resetLoadTime = ko.observable(true);
		this.preserveAssignment = ko.observable(true);

		this.selectedDBID.subscribe(function()
		{
			this.repeatStudentIds = [];
			this.selectedStopPoolId(null);
			this.residenceIds([]);
		}, this);
	}

	RollOverDataModel.prototype.constructor = RollOverDataModel;
})();
