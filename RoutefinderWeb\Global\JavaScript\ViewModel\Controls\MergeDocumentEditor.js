(function()
{
	createNamespace("TF.Control").MergeDocumentEditor = MergeDocumentEditor;

	var updateEditorDelay = 1,
		subjectEditorClass = MergeDocumentEditor.subjectEditorClass = "merge-doc-subject",
		designEditorClass = MergeDocumentEditor.designEditorClass = "MergeDocumentEditor",
		layoutProperties = ["left", "top", "right", "bottom", "width", "height"];

	function MergeDocumentEditor(element, options)
	{
		options = options || {};
		var self = this;
		self.options = options;
		self.options.context = self.options.context || {};
		self.dataModel = self.options.context.obEntityDataModel;

		let templateType = self.dataModel().templateType();
		if (templateType && templateType.isEmail())
		{
			self.isEmail = true;
		}

		self.element = $(element);
		self.element.data(MergeDocumentEditor.dataKey, self).addClass(self.options.simpleMode ? subjectEditorClass : designEditorClass);

		self.tools = !self.options.simpleMode ? ["formatting", "fontName", "fontSize", "foreColor", "backColor", "bold", "italic", "underline", "justifyLeft", "justifyCenter", "justifyRight", "insertUnorderedList", "insertOrderedList", "indent", 
		["createLink", "unlink",
		{
			name: "image-btn",
			tooltip: "Insert image",
			ui: {
				type: "button",
				icon: "image"
			},
			exec: function()
			{
				self.createSettingImageModal();
				return false;
			}
		}], "createTable", "addRowAbove", "addRowBelow", "addColumnLeft", "addColumnRight", "deleteRow", "deleteColumn"] : [];
		self.readonlyTableTools = self.tools.filter((item) => {
			return !["insertUnorderedList", "insertOrderedList", "indent", "createLink", "unlink", "image-btn", "createTable", 
			"addRowAbove", "addRowBelow", "addColumnLeft", "addColumnRight", "deleteRow", "deleteColumn"].includes(typeof item === "object" ? item.name : item);
		})

		var stylesheets = !self.options.simpleMode ? [
			"../../Global/ThirdParty/bootstrap/css/bootstrap.min.css",
			"../../Global/Css/KendoEditor.css"
		] : ["../../Global/Css/KendoEditor.css"];

		self.initEditorState = true;

		let editorOptions = {
			tools: self.tools,
			stylesheets: stylesheets,
			messages:
			{
				fontNameInherit: 'Default Font',
				fontSizeInherit: 'Default Font Size'
			},
			change: self.onKendoEditorChange.bind(self)
		};
		if (!self.isEmail)
		{
			editorOptions = $.extend(editorOptions, {
				execute: function(e)
				{
					// flag for Insert Hyperlink modal
					self.isCreateLink = e.name?.toLowerCase() == "createlink";
				},
				dialogOptions: {
					open: function(e)
					{
						if (self.isCreateLink) // createLink command will open Insert Hyperlink modal
						{
							self.isCreateLink = false;

							// .k-edit-field for Kendo UI v2021.2.616
							// .k-form-field for Kendo UI v2024.3.1015
							e.sender.wrapper.find("#k-editor-link-target").closest(".k-edit-field, .k-form-field").css("display", "none");
						}
					}
				}
			});
		}
		self.kendoEditor = self.element.kendoEditor(editorOptions).data("kendoEditor");

		self.customBodyCss();

		if (self.options.toolbarSelector && self.options.context.$dom)
		{
			var toolbarContainer = $(self.options.context.$dom).find(self.options.toolbarSelector);
			if (toolbarContainer.length)
			{
				toolbarContainer.html("");
				self.kendoEditor.toolbar.element.appendTo(toolbarContainer);
			}
		}

		self.subscriptions = [];
		var value = self.options.value;
		if (value)
		{
			if (ko.isObservable(value))
			{
				self.subscriptions.push(value.subscribe(function()
				{
					if (value() !== self.kendoEditor.value())
					{
						self.kendoEditor.value(value());
					}
				}));

				self.kendoEditor.value(value());
			}
			else
			{
				self.kendoEditor.value(value);
			}
		}

		var visible = self.options.visible;
		if (visible != null)
		{
			var updateVisible = function(v)
			{
				if (v)
				{
					self.kendoEditor.wrapper.show();
				}
				else
				{
					self.kendoEditor.wrapper.hide();
				}
			};

			if (ko.isObservable(visible))
			{
				self.subscriptions.push(visible.subscribe(function()
				{
					updateVisible(visible());
				}));

				updateVisible(visible());
			}
			else
			{
				updateVisible(visible);
			}
		}

		layoutProperties.forEach(function(item)
		{
			var prop = self.options[item];
			if (ko.isObservable(prop))
			{
				self.subscriptions.push(prop.subscribe(function()
				{
					self.updateLayout();
				}));
			}
		});

		self.updateLayout();
		self.bindEvents();
	}

	MergeDocumentEditor.dataKey = "mergeDocumentEditor";

	MergeDocumentEditor.prototype.updateLayout = function()
	{
		var self = this, style = {},
			wrapper = self.kendoEditor.wrapper,
			iframe = wrapper.find('iframe');
		layoutProperties.forEach(function(item)
		{
			style[item] = ko.unwrap(self.options[item]);
		});

		iframe.css("height", style.height);
		iframe.css("pointer-events", 'auto');
		wrapper.css(style);
		if (style.left || style.top)
		{
			wrapper.css("position", "absolute");
		}


		if (self.options.isReadOnly)
		{
			let $body = $(self.kendoEditor.body);
			$body.attr("contenteditable", "false");
			$body.css({ "pointer-events": 'none', "user-select": 'none' });
			$body.parent().css('cursor', 'default');
			$(self.kendoEditor.document).on("contextmenu", () => { return false; });
		}
	};

	MergeDocumentEditor.prototype.dispose = function()
	{
		var self = this;
		if (self.subscriptions)
		{
			self.subscriptions.forEach(function(item) { item.dispose() });
			self.subscriptions = null;
		}

		if (self.kendoEditor)
		{
			self.kendoEditor.destroy();
			self.kendoEditor = null;
		}
	};

	MergeDocumentEditor.prototype.refresh = function()
	{
		if (!this.kendoEditor) return;

		var oldBody = this.kendoEditor.body;
		this.kendoEditor.refresh();
		this.updateLayout();
		var body = this.kendoEditor.body;
		if (oldBody != body)
		{
			this.customBodyCss();
			this.bindBodyEvents();
		}
	};

	MergeDocumentEditor.prototype.customBodyCss = function()
	{
		var self = this, css = "merge-doc-body";
		if (self.options.simpleMode)
		{
			css += " merge-doc-subject-body";
		}

		if (self.options.supportLabels)
		{
			css += " mergedoc-editor-label";
			var height = parseFloat(self.kendoEditor.element.css("height"));
			if (height && !Number.isNaN(height))
			{
				var wrapper = self.kendoEditor.wrapper;
				var topBorder = parseFloat(wrapper.css("border-top-width"));
				if (Number.isNaN(topBorder))
				{
					topBorder = 1;
				}

				var bottomBorder = parseFloat(wrapper.css("border-bottom-width"));
				if (Number.isNaN(bottomBorder))
				{
					bottomBorder = 1;
				}

				height = height - topBorder - bottomBorder;
				$(self.kendoEditor.window.frameElement).css("height", height);
			}
		}

		$(self.kendoEditor.body).addClass(css);
	};

	Object.defineProperty(MergeDocumentEditor.prototype, "currentVariable", {
		get: function()
		{
			return this.options.context.currentVariable;
		}
	});

	var getRichEditorIframeXY = MergeDocumentEditor.getRichEditorIframeXY = function()
	{
		var kContentElements = window.parent.document.getElementsByClassName("k-content"),
			richEditorIframe = null;
		for (var i = 0; i < kContentElements.length; i++)
		{
			if (kContentElements[i].offsetParent !== null)
			{
				richEditorIframe = kContentElements[i];
			}
		}

		if (richEditorIframe == null)
		{
			return null;
		}

		var rect = richEditorIframe.getBoundingClientRect();
		return [rect.left, rect.top];
	};

	MergeDocumentEditor.prototype.popupRightClickMenu = function(e)
	{
		e.preventDefault();
		var table = $(e.target).closest("table"),
			grid = TF.DataEntry.GridMergeDocumentVariable.create(table);
		if (grid.columnEditable == false)
		{
			return;
		}
		grid.changed.subscribe(function()
		{
			var newTable = grid.toFieldBlock(true);
			table.replaceWith(newTable);
			this.dataModel().content(this.kendoEditor.value());
		}.bind(this));

		var contextmenu = new TF.ContextMenu.TemplateContextMenu("workspace/dataentry/mergedocuments/MergeDocumentGridVaraibleContextMenu", grid),
			iframeXY = getRichEditorIframeXY(),
			$virtualTarget = $("<div/>").css(
				{
					position: "absolute",
					left: iframeXY[0] + (window.screen.availWidth - e.clientX < 20 ? window.screen.availWidth - 20 : e.clientX),
					top: iframeXY[1] + e.clientY
				}).appendTo('body');
		tf.contextMenuManager.showMenu($virtualTarget, contextmenu);
	};

	MergeDocumentEditor.prototype.insertField = function(field)
	{
		var self = this;
		var text = field.toFieldBlock();
		self.insertHtml(text);
		if (field.Column == "ActiveStudentSchedule")
		{
			var studentExceptionVariable = self.options.context.variableGroups()[0].variables.filter(x => x.Column == "StudentException")[0];
			self.insertHtml("<p><span style='font-weight:bold;'>Exceptions</span><br class='k-br'></p>" + studentExceptionVariable.toFieldBlock());
		}
		else if (field.Column == "StudentDefaultRequirements")
		{
			var addtionalRequirementsVariable = self.options.context.variableGroups()[0].variables.filter(x => x.Column == "StudentAdditionalRequirements")[0];
			self.insertHtml("<p><span style='font-weight:bold;'>Additional Requirements</span><br class='k-br'></p>" + addtionalRequirementsVariable.toFieldBlock());
		}
		self.resolveFocus();
		self.onFocus();
	};

	MergeDocumentEditor.prototype.focus = function()
	{
		this.kendoEditor.focus();
		// sometimes focus event doesn't fire after call kendoEditor focus,
		// so trigger focus event by SetTimeout if focus event doesn't fire.
		this.resolveFocusEventTimer = setTimeout(this.onFocus.bind(this));
	};

	MergeDocumentEditor.prototype.onFocus = function()
	{
		if (this.resolveFocusEventTimer != null)
		{
			clearTimeout(this.resolveFocusEventTimer);
			this.resolveFocusEventTimer = null;
		}

		if (this.options.focus)
		{
			this.options.focus(this);
		}
	};

	// TODO: after Edge changes its core to Chromium, we can delete this method
	MergeDocumentEditor.prototype.resolveSelectionInEdge = function()
	{
		if (!Sys.edge)
		{
			return;
		}

		var editor = this.kendoEditor,
			range = editor.getRange(),
			startSpan = ($(range.startContainer).closest("span[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]")[0] || { childNodes: [] }).childNodes[0],
			endSpan = ($(range.endContainer).closest("span[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]")[0] || { childNodes: [] }).childNodes[0],
			needResolve = (startSpan && (range.startContainer != startSpan || (range.startOffset != 0 && range.startOffset != $(startSpan).text().length)))
				|| (endSpan && (range.endContainer != endSpan || (range.endOffset != $(endSpan).text().length && range.endOffset != 0)));

		if (!needResolve)
		{
			return;
		}

		$(editor.body).attr("contenteditable", "false");
		var newRange = editor.createRange();
		if (startSpan)
		{
			newRange.setStart(startSpan, 0);
		}
		else
		{
			newRange.setStart(range.startContainer, range.startOffset);
		}

		if (endSpan)
		{
			newRange.setEnd(endSpan, $(endSpan).text().length);
		}
		else
		{
			newRange.setEnd(range.endContainer, range.endOffset);
		}

		editor.selectRange(newRange);
		$(editor.body).attr("contenteditable", "true");
	};

	MergeDocumentEditor.prototype.insertHtml = function(html)
	{
		var editor = this.kendoEditor,
			range = editor.getRange(),
			focusEle = range.startContainer;
		if (focusEle.nodeName == "#text")
		{
			focusEle = focusEle.parentElement;
		}

		if (getContentEditbableAttr(focusEle) === false)
		{
			return;
		}

		editor.exec("insertHtml", { value: html });

		// resize the table column width when insert a field
		if (focusEle.nodeName == "TD")
		{
			this.setColumnsComputedWidth($(focusEle).closest('table')[0]);
		}
	};

	MergeDocumentEditor.prototype.setColumnsComputedWidth = function(tableElement)
	{
		let tableInnerElement = $(tableElement.tHead || tableElement.tBodies[0]);
		let innerElementWidth = tableInnerElement.outerWidth(false) || 0;
		let columns = tableInnerElement.children("tr").children("td");
		let length = columns.length;
		let currentColumnsWidths = columns.map(function()
		{
			return $(this).outerWidth(false);
		});

		for (let i = 0; i < length; i++)
		{
			if (this.inPercentages(columns[i].style["width"]))
			{
				let percentageRatio = parseFloat(currentColumnsWidths[i]) / innerElementWidth * 100;
				columns[i].style["width"] = parseFloat(percentageRatio) + "%";
			} else
			{
				columns[i].style["width"] = parseFloat(currentColumnsWidths[i]) + "px";
			}
		}
	};

	MergeDocumentEditor.prototype.inPercentages = function(value)
	{
		let regexPercentages = /(\d+)(\.?)(\d*)%/;
		return typeof value === "string" && regexPercentages.test(value);
	};

	MergeDocumentEditor.prototype.resolveNewLineBeforeUneditableSpan = function(focusContainer)
	{
		var editor = this.kendoEditor;
		if (editor.beforeDropRootNodesCount == null)
		{
			return;
		}

		var rootNodesCount = editor.body.childNodes.length;
		if (rootNodesCount > editor.beforeDropRootNodesCount)
		{
			var newLine = focusContainer.next();
			if (newLine.length)
			{
				var children = newLine.children();
				newLine.remove();
				var last = focusContainer.children().last();
				if (isBr(last[0]))
				{
					children.each(function()
					{
						if (isBr(this)) return;
						$(this).insertBefore(last);
					});
				}
				else
				{
					children.each(function()
					{
						if (isBr(this)) return;
						focusContainer.append(this);
					});
				}
			}
		}

		editor.beforeDropRootNodesCount = null;
	};

	function getContentEditbableAttr(element)
	{
		var value = $(element).attr("contenteditable");
		if (value === "" || value == null)
		{
			return null;
		}

		var value = value.toLowerCase();
		if (value === "true")
		{
			return true;
		}

		if (value === "false")
		{
			return false;
		}

		return null;
	}

	function isBr(ele)
	{
		return ele && ele.nodeName.toLowerCase() == "br";
	}

	function getIndexOfParent(element)
	{
		var $element = $(element),
			parent = $element.parent(),
			index = 0;
		$.each(parent[0].childNodes, function(i, item)
		{
			if (item == $element[0])
			{
				index = i;
				return false;
			}
		});

		return index;
	}

	function isElementSelected(range, ele)
	{
		var rangeIterator = new TF.RangeIterator(range);
		var result = rangeIterator.findFirst(function(node)
		{
			if (node == ele)
			{
				return node;
			}
		});

		return !!result;
	}

	MergeDocumentEditor.prototype.resolveFocus = function(focusNode)
	{
		var editor = this.kendoEditor,
			focusEle = focusNode ? $(focusNode) : $(editor.getSelection().focusNode).parent(),
			focusContainer = focusEle.parent(),
			index = getIndexOfParent(focusEle);
		this.resolveNewLineBeforeUneditableSpan(focusContainer);
		var range = editor.createRange();
		range.setStart(focusContainer[0], index + 1);
		range.setEnd(focusContainer[0], index + 1);
		editor.selectRange(range);
		editor.focus();
	};

	MergeDocumentEditor.prototype.bindEvents = function()
	{
		var self = this, editor = self.kendoEditor;
		editor.bind("select", function()
		{
			self.onFocus();
			self.resolveSelectionInEdge();
		});

		if (!self.options.simpleMode)
		{
			editor.bind("keydown", function(e)
			{
				var tabKeyCode = 9;
				if (e.keyCode == tabKeyCode)
				{
					var tabChar = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
					editor.body.ownerDocument.execCommand("insertHTML", false, tabChar);
					e.preventDefault();
				}
			});
		}

		self.bindBodyEvents();
	};

	MergeDocumentEditor.prototype.showTool = function(iconSelector, value)
	{
		var toolbar = this.kendoEditor.toolbar.element,
			item = toolbar.find(iconSelector),
			container;
		if (!item.length)
		{
			return;
		}

		if (item.prop("tagName").toLowerCase() == "select")
		{
			container = item.closest('.k-editor-dropdown');
		}
		else
		{
			container = item.parent();
		}

		value = value == null ? true : value;
		if (value)
		{
			container.show();
		}
		else
		{
			container.hide();
		}
	};
	MergeDocumentEditor.prototype.bindBodyEvents = function()
	{
		var self = this, editor = self.kendoEditor;
		$(editor.body).on("input", function()
		{
			if (editor.currentVariableHtml)
			{
				var currentVariableHtml = editor.currentVariableHtml;
				setTimeout(function()
				{
					var range = editor.getRange();
					editor.selectRange(range);
					self.insertHtml(currentVariableHtml);
				}, updateEditorDelay);

				editor.currentVariableHtml = null;
				return;
			}

			if (!self.currentVariable)
			{
				return;
			}
			var range = editor.getRange();
			editor.selectRange(range);
			var field = self.currentVariable;
			self.insertField(field);

		}).on("keydown", function(e)
		{
			// 1. change keyup to keydown: for avoid the undefined icon can be seen
			// 2. when the enter key is press, remove the span[contenteditable=false] element that content is empty
			if (e.keyCode === $.ui.keyCode.ENTER)
			{
				var dumpElement = $(editor.body).find("span[contenteditable=false][" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]");
				dumpElement.each(function()
				{
					var ele = $(this);
					if (!ele.text() || ele.text().trim() === '')
					{
						var parent = ele.parent();
						ele.remove();
						parent.html("&nbsp;");
						var range = editor.createRange();
						range.selectNode(parent[0]);
						range.collapse();
						editor.selectRange(range);
					}
				});
			}
		}).off("dragstart.kendoEditor").on("dragstart", "*[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]", function(ev)
		{
			var dt = ev.originalEvent.dataTransfer, target = ev.target, tableTarget = $(target).closest("table[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]");
			if (tableTarget.length) target = tableTarget[0];
			var currentVariableHtml = target.outerHTML;
			dt.setData("text", "&nbsp;");
			dt.setData("text/html", "&nbsp;");
			dt.setData("application/html", currentVariableHtml);
		}).on("drop", function(ev)
		{
			var dt = ev.originalEvent.dataTransfer;
			var currentVariableHtml = (dt.getData("application/html") || "").trim();
			editor.currentVariableHtml = currentVariableHtml;
		}).on("click", "span[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "], table[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]", function(ev)
		{
			var target = $(ev.currentTarget), removeIconWidth = 16, documentScrollLeft = $(ev.currentTarget.ownerDocument).scrollLeft();
			if (ev.clientX > target.offset().left + target.width() - removeIconWidth - documentScrollLeft)
			{
				target.remove();
				return;
			}

			self.resolveFieldClickInEdge();
		}).on("click", "a", function(ev)
		{
			ev.preventDefault();
		}).on("dblclick", "img", function(ev)
		{
			var range = editor.createRange();
			range.selectNode(ev.target)
			setTimeout(function()
			{
				self.createSettingImageModal();
			});
		}).on("dragover", function(ev)
		{
			if (self.options.dragover)
			{
				self.options.dragover(ev);
			}
		}).on("contextmenu", "table[" + TF.DataEntry.MergeDocumentVariable.dataFieldAttr + "]", function(e)
		{
			self.popupRightClickMenu(e);
		});

		if (self.options.simpleMode)
		{
			editor.body.ownerDocument.addEventListener("keydown", function(e)
			{
				if (e.keyCode === $.ui.keyCode.ENTER)
				{
					e.preventDefault();
					e.stopPropagation();
				}
			}, true);

			$(editor.body).on("input paste", function()
			{
				setTimeout(function()
				{
					self.cleanRichContent();
				}, updateEditorDelay);
			});
		}
	};

	MergeDocumentEditor.prototype.cleanRichContent = function()
	{
		var self = this,
			editor = self.kendoEditor,
			range = editor.getRange(),
			focusNode = range.endContainer,
			offset = range.endOffset,
			nodes = editor.body.childNodes,
			filterNodes = function(nodes)
			{
				var results = [];
				for (var i = 0; i < nodes.length; i++)
				{
					var node = nodes[i];
					if (node.nodeName == "#text" || node.nodeName == "BR")
					{
						results.push(node);
						continue;
					}

					if (node.nodeName.toLowerCase() == "span")
					{
						if (node.hasAttribute(TF.DataEntry.MergeDocumentVariable.dataFieldAttr))
						{
							node.removeAttribute("style");
							node.removeAttribute("class");
							results.push(node);
							continue;
						}

						var text = $(node).text();
						if (text)
						{
							var textNode = document.createTextNode(text);
							results.push(textNode);
						}

						continue;
					}

					if (node.childNodes && node.childNodes.length)
					{
						var childNodes = filterNodes(node.childNodes);
						results = results.concat(childNodes);
					}
				}

				return results;
			},
			filteredNodes = filterNodes(nodes);

		$(editor.body).html("");
		for (var i = 0; i < filteredNodes.length; i++)
		{
			editor.body.appendChild(filteredNodes[i]);
		}

		if (focusNode.nodeName == "#text")
		{
			var parent = $(focusNode).parent();
			if (getContentEditbableAttr(parent) === false)
			{
				self.resolveFocus(parent);
				return;
			}
		}

		try
		{
			var range = editor.createRange();
			range.setStart(focusNode, offset);
			range.setEnd(focusNode, offset);
			editor.selectRange(range);
		}
		catch (e) { }
		editor.body.focus();
	};

	MergeDocumentEditor.prototype.resolveFieldClickInEdge = function()
	{
		if (!Sys.edge)
		{
			return;
		}

		var editor = this.kendoEditor;
		setTimeout(function()
		{
			var textNode = target[0].childNodes[0];
			if (isElementSelected(editor.getRange(), textNode))
			{
				return;
			}

			$(editor.body).attr("contenteditable", "false");
			var range = editor.createRange();
			range.setStart(textNode, 0);
			range.setEnd(textNode, $(textNode).text().length);
			editor.selectRange(range);
			$(editor.body).attr("contenteditable", "true");
		});
	};

	MergeDocumentEditor.prototype.onKendoEditorChange = function()
	{
		var self = this,
			value = self.options.value;
		if (value && ko.isObservable(value))
		{
			value(self.kendoEditor.value());
		}

		$(self.kendoEditor.document).find("table").each(function()
		{
			self.setColumnsComputedWidth(this);
		})
	};

	MergeDocumentEditor.prototype.createSettingImageModal = function()
	{
		var self = this;
		var editor = self.kendoEditor,
			selection = editor.getSelection(),
			existedImage = getSelectedImage(selection);
		var options = (existedImage == null) ?
			{ title: "Insert Image", positiveButtonLabel: "Insert" } :
			{ title: "Edit Image", positiveButtonLabel: "Save" };

		tf.modalManager.showModal(
			new TF.Modal.ImageSettingsModalViewModel(existedImage, options)
		).then(function(image)
		{
			if (image !== false)
			{
				if (!existedImage)
				{
					self.insertHtml(image.outerHTML);
				}
			}

			editor.focus();
		});
	};

	function getSelectedImage(selection)
	{
		if (selection && !selection.isCollapsed)
		{
			var count = selection.rangeCount;
			for (var i = 0; i < count; i++)
			{
				var node = getRangeSelectedNode(selection.getRangeAt(i), "img");
				if (node)
				{
					return node;
				}
			}
		}

		return null;
	}

	function getRangeSelectedNode(range, nodeName)
	{
		var rangeIterator = new TF.RangeIterator(range), nodeName = nodeName.toLowerCase();
		return rangeIterator.findFirst(function(node)
		{
			if (node.nodeName.toLowerCase() == nodeName)
			{
				return node;
			}
		});
	}

	ko.bindingHandlers.mergeDocumentEditor = {
		init: function(element, valueAccessor)
		{
			var options = ko.unwrap(valueAccessor());
			var editor = new MergeDocumentEditor(element, options);
			ko.utils.domNodeDisposal.addDisposeCallback(element, function()
			{
				editor.dispose();
			});
		}
	};
})();