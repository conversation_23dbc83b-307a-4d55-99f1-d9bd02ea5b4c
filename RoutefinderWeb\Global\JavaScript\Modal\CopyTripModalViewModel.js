﻿(function()
{
	createNamespace('TF.Modal').CopyTripModalViewModel = CopyTripModalViewModel;

	function CopyTripModalViewModel(tripName, tripType)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/CopyTripControl');
		this.buttonTemplate('modal/positivenegative');
		this.CopyTripViewModel = new TF.Control.CopyTripViewModel(tripName, tripType);
		this.data(this.CopyTripViewModel);
		this.sizeCss = "modal-dialog-sm";
		this.title("Copy Trip");
		this.obPositiveButtonLabel("Create Trip");
	}

	CopyTripModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	CopyTripModalViewModel.prototype.constructor = CopyTripModalViewModel;

	CopyTripModalViewModel.prototype.positiveClick = function()
	{
		this.CopyTripViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	CopyTripModalViewModel.prototype.dispose = function()
	{
		this.CopyTripViewModel.dispose();
	};

})();
