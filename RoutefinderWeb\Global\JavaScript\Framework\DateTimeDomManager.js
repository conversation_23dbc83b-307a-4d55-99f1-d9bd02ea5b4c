﻿(function()
{
	TF.DateTimeDomManager = function(format)
	{
		this._format = format;
	};

	TF.DateTimeDomManager.prototype = {
		incrementSelectedSection: function(element)
		{
			var changeDelegate = function(momentValue, unit, amount)
			{
				momentValue.add(unit, amount);
			}

			this._changeSelectedSectionValue(element, changeDelegate, 1);
		},

		decrementSelectedSection: function(element)
		{
			var changeDelegate = function(momentValue, unit, amount)
			{
				momentValue.subtract(unit, amount);
			}

			this._changeSelectedSectionValue(element, changeDelegate, 1);
		},

		selectSection: function(element, position)
		{
			if (element.selectionStart == element.selectionEnd)
			{
				if (moment(element.value, this._format, true).isValid())
				{
					var startPosition = position == null ? element.selectionStart : position;
					var token = this._findTokenByPosition(element.value, startPosition, this._format);

					if (token != null)
					{
						element.setSelectionRange(token.start, token.end);
						return true;
					}
				}
			}
			return false;
		},

		_changeSelectedSectionValue: function(element, changeDelegate, amount)
		{
			var value = element.value;
			var position = element.selectionStart;
			var momentValue = moment(value, this._format, true);
			if (momentValue.isValid())
			{
				var token = this._findTokenByPosition(value, position, this._format);
				if (token != null)
				{
					var unit = this._findSelectedUnit(token);

					if (/A/i.test(unit))
					{
						this._toggleMeridiem(momentValue);
					}
					else
					{
						changeDelegate(momentValue, unit, amount);
					}

					element.value = momentValue.format(this._format);

					this._selectSectionByTokenIndex(element, token.index);
					return true;
				}
			}
			return false;
		},

		_findSelectedUnit: function(token)
		{
			if (token.format.length)
			{
				var returnValue = token.format[0];
				switch (token.format[0])
				{
					case 'Q':
					case 'X':
					case 'Z':
					case 'z':
						returnValue = null;
						break;
					case 'D':
						if (token.format == 'DDD')
						{
							returnValue = null;
						}
						else
						{
							returnValue = 'd';
						}
						break;
					case 'g':
						returnValue = 'gg';
						break;
					case 'H':
						returnValue = 'h';
						break;
					case 'G':
						returnValue = 'GG';
						break;
					case 'S':
						returnValue = 'ms';
						break;
					case 'Y':
						returnValue = 'y';
						break;
				}
				return returnValue;
			}
		},

		_findTokenByPosition: function(value, position)
		{
			if (value.length && moment(value, this._format).isValid())
			{
				var tokens = moment.tokenizeInput(value, this._format);
				var currentLength = 0;

				var tokenFound = false;

				// Find the token where the active selection exists.
				for (var i = 0; i < tokens.value.length; i++)
				{
					currentLength += tokens.value[i].length;
					if (currentLength >= position && moment.isValidToken(tokens.format[i]))
					{
						tokenFound = true;
						break;
					}
				}

				if (tokenFound)
				{
					return {
						start: currentLength - tokens.value[i].length,
						end: currentLength,
						format: tokens.format[i],
						value: tokens.value[i],
						index: i
					};
				}
			}

			return null;
		},

		_toggleMeridiem: function(momentValue)
		{
			/// <param name="momentValue" type="moment">A moment object containing the time to be altered.</param>
			var momentNoon = momentValue.clone();
			momentNoon.hour(12);
			momentNoon.second(0);
			momentNoon.millisecond(0);

			if (momentValue.isBefore(momentNoon))
			{
				momentValue.add('hours', 12);
			}
			else
			{
				momentValue.subtract('hours', 12);
			}
		},

		_selectSectionByTokenIndex: function(element, index)
		{
			var tokens = moment.tokenizeInput(element.value, this._format);
			var totalLength = 0;
			for (var i = 0; i < index; i++)
			{
				totalLength += tokens.value[i].length;
			}

			this.selectSection(element, totalLength);
		}
	};

})();