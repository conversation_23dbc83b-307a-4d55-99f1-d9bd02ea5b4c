@import "z-index";

.modal-dialog {
	&.modal-sm {
		&.legend-setting {
			font-family: "SourceSansPro-Regular";
			width: 380px;
		}
	}
}

.thematic-legend {
	position: absolute;
	top: 15px;
	left: 15px;
	max-width: 275px;
	font-family: "SourceSansPro-Regular";
	background-color: #ffffff;
	pointer-events: auto;
	cursor: move;
	z-index: @MAP_ONMAP_TOOL + 1;
	box-shadow: 4px 4px 12px 1px rgba(0, 0, 0, 0.15);

	.legend-draghandle {
		display: none;
		position: absolute;
		width: 36px;
		height: 2px;
		background-color: #BBBBBB;
		left: calc(~"50% - 16px");
		cursor: ns-resize;
		border: transparent solid 1px;
		border-radius: 2px;
		z-index: @MAP_ONMAP_TOOL + 2;

		&.ontop {
			top: 3px
		}

		&.onbottom {
			bottom: 3px;
		}
	}

	.legend-button {
		z-index: @MAP_ONMAP_TOOL + 2;
		display: none;
		position: absolute;
		top: 0px;
		right: 0px;
		height: 100%;
		width: 24px;
		background-color: #EAD9EC;

		.legend-close {
			cursor: pointer;
			display: none;
			position: absolute;
			background-image: url(../img/map/thematics/LegendClose.svg);
			height: 12px;
			width: 12px;
			top: 6px;
			right: 6px;
		}

		.legend-setting {
			display: none;
			cursor: pointer;
			position: absolute;
			background-image: url(../img/map/thematics/LegendSettings.svg);
			height: 12px;
			width: 12px;
			top: 27px;
			right: 6px;
		}
	}

	.legend-head {
		background-color: rgba(143, 82, 162, 0.1);
		font-family: "SourceSansPro-SemiBold";
		color: #8F52A1;
		padding: 16px 32px 16px 16px;

		.legend-title {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			line-height: 18px;
			font-size: 19px;
		}

		.legend-description {
			margin-top: 16px;

			&.no-margin {
				margin-top: 0px;
			}

			line-height: 13px;
			font-size: 14px;
		}
	}

	.legend-body {
		overflow: hidden;

		.draghandle-background {
			visibility: hidden;
			position: absolute;
			height: 9px;
			bottom: 0px;
			width: 100%;
			background-color: #ffffff;

			&.ontop {
				top: 0px;
			}

			&.onbottom {
				bottom: 0px;
			}
		}

		.virtual-panel {
			cursor: default;
			overflow-x: hidden;

			.scrolling-panel {
				padding: 16px 16px 16px 16px;
				cursor: move;
				float: left;
			}

			.virtual-container .data-item {
				min-height: 12px;
				display: flex;
				max-width: 227px;
				float: none;
				margin-right: 0;
				cursor: default;

				.item-box {
					margin-right: 16px;
					display: flex;
					flex-shrink: 0;
					align-items: center;
					justify-content: center;
				}

				.item-text {
					display: flex;
					flex-flow: column;
					justify-content: center;
					max-width: calc(100% - 27px);
				}

				.item-text div {
					font-size: 13px;
					color: #333333;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.item-text-name ~ div{
					margin-top:-5px;
					font-size: 11px;
				}
			}
		}
	}

	&:hover {
		.legend-draghandle {
			display: block;

			&.hide {
				display: none;
			}
		}

		.draghandle-background {
			visibility: visible;

			&.hide {
				visibility: hidden;
			}
		}

		.legend-button {
			display: block;

			.legend-close {
				display: block;
			}

			.legend-setting {
				display: block;
			}
		}
	}

	&.dragging {
		.legend-draghandle {
			display: block;
		}

		.draghandle-background {
			visibility: visible;
		}

		.legend-button {
			display: none;

			.legend-close {
				display: none;
			}

			.legend-setting {
				display: none;
			}
		}
	}
}

.thematic-legend-shadow {
	display: none;
	position: absolute;
	top: 10px;
	left: 10px;
	background-color: #0094ff;
	opacity: 0.5;
	z-index: @MAP_ONMAP_TOOL;

	&.active {
		display: block;
	}
}

span.text-container {
	font-family: "SourceSansPro-Regular";
	font-size: 12px;
}
