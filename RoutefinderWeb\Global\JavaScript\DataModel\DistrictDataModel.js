﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.DistrictDataModel = function(districtEntity)
	{
		namespace.BaseDataModel.call(this, districtEntity);
	}

	namespace.DistrictDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.DistrictDataModel.prototype.constructor = namespace.DistrictDataModel;

	// namespace.DistrictDataModel.prototype.mapping = [
	// 	{ from: "Id", default: 0 },
	// 	{ from: "DBID", default: function() { return tf.datasourceManager.databaseId; } },
	// 	{ from: "IdString", default: function() { return tf.setting.userProfile.District; } },
	// 	{ from: "Name", default: "" },
	// 	{ from: "MailStreet1", default: "" },
	// 	{ from: "MailStreet2", default: "" },
	// 	{ from: "MailCity", default: function() { return tf.setting.userProfile.Mailcity; } },
	// 	{ from: "MailState", default: function() { return tf.setting.userProfile.MailState; } },
	// 	{ from: "MailZip", default: function() { return tf.setting.userProfile.Mailzip; } },
	// 	{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
	// 	{ from: "LastUpdatedId", default: 0 },
	// 	{ from: "LastUpdatedName", default: "" },
	// 	{ from: "LastUpdatedType", default: 0 },
	// 	{ from: "System1", default: "" },
	// 	{ from: "System2", default: "" },
	// 	{ from: "System3", default: "" },
	// 	{ from: "System4", default: "" },
	// 	{ from: "Comments", default: "" }
	// ];

	namespace.DistrictDataModel.prototype.mapping = [
		{ "from": "Comments", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "DistrictNameWithId", "default": null },
		{ "from": "Id", "default": 0 },
		{ "from": "IdString", "default": function() { return tf.setting.userProfile.District; } },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "MailCity", "default": function() { return tf.setting.userProfile.MailCityName; } },
		{ "from": "MailCityId", "default": function() { return tf.setting.userProfile.MailCity; } },
		{ "from": "MailState", "default": function() { return tf.setting.userProfile.MailStateName; } },
		{ "from": "MailStateId", "default": function() { return tf.setting.userProfile.MailState; } },
		{ "from": "MailStreet1", "default": "" },
		{ "from": "MailStreet2", "default": "" },
		{ "from": "MailZip", "default": function() { return tf.setting.userProfile.MailZipName; } },
		{ "from": "MailZipId", "default": function() { return tf.setting.userProfile.MailPostalCode; } },
		{ "from": "Name", "default": "" },
		{ "from": "SiteOwnerName", "default": null },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null }
	];
})();
