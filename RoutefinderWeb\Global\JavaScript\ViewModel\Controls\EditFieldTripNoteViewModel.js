﻿(function()
{
	createNamespace('TF.Control').EditFieldTripNoteViewModel = EditFieldTripNoteViewModel;

	function EditFieldTripNoteViewModel(type, fieldTripId, note, visibleControl)
	{
		this.type = type;
		this.fieldTripId = fieldTripId;
		this.note = note;
		this.obVisibleControl = visibleControl;
	}

	EditFieldTripNoteViewModel.prototype.save = function()
	{
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtrip", "note", "bytype", this.fieldTripId, this.type), { data: "'" + this.note + "'" })
		.then(function()
		{
			return { Id: this.fieldTripId, Note: this.note, Type: this.type };
		}.bind(this))
	}

	EditFieldTripNoteViewModel.prototype.apply = function()
	{
		return this.save()
		.then(function(data)
		{
			return data;
		});
	}
})();

