(function()
{
	createNamespace('TF.Modal').ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel = ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel;

	function ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel(selectedData, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.options = options;
		this.title("Filter " + options.DisplayFilterTypeName);
		if (options.DisplayFilterTypeName === "GPS Events")
		{
			this.helpKey = "filterGPSevent";
		}

		this.sizeCss = options.sizeCss || 'modal-dialog-lg listmover';

		this.contentTemplate(options.contentTemplate || 'modal/ListMoverForListFilterWithSelectDateTimeRangeControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Apply");

		tf.modalManager.useShortCutKey = true;
		this.filterField = options.filterField;
		this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel = options.viewModel ? new TF.Control[options.viewModel](selectedData, options) : new TF.Control.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel(selectedData, options);
		this.afterInitialize();
		this.data(this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel);
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype.constructor = ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel;

	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.apply()
			.then(function(result)
			{
				result.selectedData = TF.ListMoverForListFilterHelper.processSelectedData(result.selectedData, self.filterField);
				self.positiveClose(result);
			})
			.catch(function(arg)
			{
				// console.log('arg');
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype.negativeClick = function()
	{
		var self = this;
		self.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.cancel().then(function(result)
		{
			if (TF.isPhoneDevice)
			{
				var filterChanged = result.filterChanged;
				var resultValue = result.resultValue;
				var promise;
				if (filterChanged)
					promise = tf.promiseBootbox.yesNo(
						"Are you sure you want to Cancel?",
						"Confirmation"
					);
				else
					promise = Promise.resolve(true);

				return promise.then(function(ans)
				{
					if (ans)
					{
						if (resultValue)
							self.positiveClose(resultValue);
						else
							self.negativeClose(resultValue);
					}
				});
			}
			else
			{
				if (result)
					self.positiveClose(result);
				else
					self.negativeClose(result);
			}
		}.bind(self), function(error)
		{
			// console.log(error);
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype.buildResult = function()
	{
		return {
			selectedData: arguments[0]
		};
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype.afterInitialize = function(isMobile)
	{
		if (this.options.description)
		{
			this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.obPageDescription = this.options.description;
		}
		else
		{
			if (this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.obIsLocationEvent())
			{
				this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.obPageDescription = ko.observable('Specify the dates (Start Date and End Date), times (Start Time and End Time), Day Of Week, On-Time Status, Operator, Vehicles and Location that you would like to view.');
			}
			else
			{
				this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.obPageDescription = ko.observable('Specify the dates (Start Date and End Date), times (Start Time and End Time), Day Of Week, Event Types, and Vehicles that you would like to view.');
			}
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlModalViewModel.prototype.dispose = function()
	{
		this.ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.dispose();
		if (this.tooltip)
		{
			this.tooltip.dispose();
			this.tooltip = null;
		}
	}

})();
