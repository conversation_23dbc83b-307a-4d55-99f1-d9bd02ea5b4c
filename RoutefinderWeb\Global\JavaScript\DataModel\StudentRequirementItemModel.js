﻿(function()
{
	createNamespace("TF.DataModel").StudentRequirementItemModel = StudentRequirementItemModel;
	var dataKey = "_data";

	StudentRequirementItemModel.getEntity = function(studentRequirementItemModel)
	{
		return studentRequirementItemModel[dataKey];
	};

	function StudentRequirementItemModel(studentRequirementModel, allSchools, allSites, allNoTransportationTypes)
	{
		var self = this;
		allSchools = allSchools || [];
		allSites = allSites || [];
		allNoTransportationTypes = allNoTransportationTypes || [];
		self.data = studentRequirementModel;
		self.id = studentRequirementModel.id;
		self.startDate = studentRequirementModel.startDate;
		self.endDate = studentRequirementModel.endDate;
		self.prohibitCross = studentRequirementModel.prohibitCross();
		self.type = ko.pureComputed(function()
		{
			return TF.SessionType.toString(studentRequirementModel.sessionId());
		});

		var findSchoolName = function(code)
		{
			var school = allSchools.find(function(item)
			{
				return item.SchoolCode == code;
			});

			if (!school)
			{
				return code;
			}

			var name = school.SchoolCodeWithName || "";
			var index = name.indexOf("(");
			if (index > -1)
			{
				name = name.substring(0, index);
			}

			return name.trim() || code;
		};

		var findSiteName = function(id)
		{
			var site = allSites.find(function(item)
			{
				return item.Id == id;
			});

			return (site && site.Name) || id;
		};

		var findNoTransportationType = function(id)
		{
			let noTransportation = allNoTransportationTypes.find(function(item)
			{
				return item.Id == id;
			});

			return (noTransportation && noTransportation.Type) || id;
		};

		var findLocation = function()
		{
			if (studentRequirementModel.isSchool())
			{
				return findSchoolName(studentRequirementModel.locationSchoolCode());
			}

			if (studentRequirementModel.isAltSite())
			{
				return findSiteName(studentRequirementModel.altSiteId());
			}

			if (studentRequirementModel.isNoTransportation())
			{
				return findNoTransportationType(studentRequirementModel.noTransportationId());
			}

			return "Home";
		}

		self.puLocation = ko.pureComputed(function()
		{
			if (studentRequirementModel.sessionId() == TF.SessionType.ToSchool)
			{
				return findLocation();
			}

			return findSchoolName(studentRequirementModel.schoolCode());
		});

		self.doLocation = ko.pureComputed(function()
		{
			if (studentRequirementModel.sessionId() == TF.SessionType.ToSchool)
			{
				return findSchoolName(studentRequirementModel.schoolCode());
			}

			return findLocation();
		});

		self.days = ko.pureComputed(function()
		{
			var days = "";
			if (studentRequirementModel.monday()) days += "Mo ";
			if (studentRequirementModel.tuesday()) days += "Tu ";
			if (studentRequirementModel.wednesday()) days += "We ";
			if (studentRequirementModel.thursday()) days += "Th ";
			if (studentRequirementModel.friday()) days += "Fr ";
			if (studentRequirementModel.saturday()) days += "Sa ";
			if (studentRequirementModel.sunday()) days += "Su ";
			return days.trim();
		});
	};

	StudentRequirementItemModel.prototype = Object.create(TF.DataModel.BaseDataModel.prototype);

	StudentRequirementItemModel.prototype.constructor = StudentRequirementItemModel;

	StudentRequirementItemModel.prototype.toData = function()
	{
		var data = {
			Id: this.id(),
			Type: this.type(),
			PuLocation: this.puLocation(),
			DoLocation: this.doLocation(),
			Days: this.days(),
			StartDate: this.startDate(),
			EndDate: this.endDate(),
			ProhibitCross: this.prohibitCross,
			Session: this.type()
		};

		data[dataKey] = this.data.toData();
		return data;
	};

})();
