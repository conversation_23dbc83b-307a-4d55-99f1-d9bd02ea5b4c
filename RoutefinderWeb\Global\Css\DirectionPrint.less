@import "z-index";
/***********************************
** progress style for small map create
************************************/

.direction-progress {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: @route-loadingindicator-z-index;

	>.overlay {
		background: rgba(102, 102, 102, 0.8);
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
	}

	.progressbar {
		width: 450px;
		background: white;
		margin: 18% auto 0 auto;
		padding: 25px 40px 20px 40px;
		box-shadow: 0px 0px 20px 2px rgba(0, 0, 0, 0.75);
		border-radius: 4px;

		.spinner {
			width: 16px;
			margin-right: 10px;
			margin-top: -3px;
		}
	}
}

/***********************************
** Direction style for map print
************************************/

#direction-detail-map-print {
	margin-left: 50px;
}

.direction-print {
	display: none;
	font-size: 20px;

	.direction-head-print {
		min-height: 20px;
		font-weight: bold;
		display: flex;
		justify-content: space-between;

		.direction-head-left-print {
			width: 60%;
		}

		.direction-head-right-print {
			width: 30%;

			.direction-total-distance-print,
			.direction-total-time-print {
				width: 50%;
				float: left;
			}
		}
	}

	.direction-detail-group-print {
		page-break-inside: avoid;
	}

	.direction-map-print {
		position: relative;
		padding: 20px 10px;
		page-break-inside: avoid;

		&.base-map-print {
			padding-top: 40px;
		}

		.vertical-line {
			position: absolute;
			left: 19px;
			border-right: 2px solid #ccc;
			width: 1px;
			top: 0;
			bottom: 0;
		}
	}

	.directions-info-print {
		position: inherit;
		width: auto;
		height: auto;
		padding: 0 10px;
	}
}

@media print {

	body>div,
	body>input,
	body>a,
	body>button,
	body>nav {
		display: none;
	}

	body {
		overflow: auto;
		height: auto;
	}

	.direction-print {
		display: block !important;

		.direction-map-print #direction-detail-map-print {
			display: block !important;
		}

		.directions-info .directions-details .direction-detail-group-print {
			.directions-element {
				.symbol.esriDMTStop.last-child {
					background-color: #c00 !important;
				}

				.vertical-line {
					top: 0 !important;
					bottom: 0 !important;
				}
			}

			&:first-child {
				.directions-element .vertical-line {
					top: calc(88%) !important;
				}
			}

			&:last-child {
				.directions-element .vertical-line {
					bottom: 50% !important;
				}
			}
		}
	}
}
