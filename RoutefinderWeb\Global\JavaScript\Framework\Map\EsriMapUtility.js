﻿(function()
{
	createNamespace("TF.Map").EsriMapUtility = EsriMapUtility;

	function EsriMapUtility() { };

	/**
	*  convert extent geometry to polygon geometry
	*/
	EsriMapUtility.prototype.convertExtentToPolygon = function(extentGeometry)
	{
		if (extentGeometry.type != "extent")
		{
			return extentGeometry;
		}
		var polygon = new tf.map.ArcGIS.Polygon(extentGeometry.spatialReference);
		polygon.addRing([
			[extentGeometry.xmin, extentGeometry.ymin],
			[extentGeometry.xmax, extentGeometry.ymin],
			[extentGeometry.xmax, extentGeometry.ymax],
			[extentGeometry.xmin, extentGeometry.ymax],
			[extentGeometry.xmin, extentGeometry.ymin]]);
		return polygon;
	};
})();