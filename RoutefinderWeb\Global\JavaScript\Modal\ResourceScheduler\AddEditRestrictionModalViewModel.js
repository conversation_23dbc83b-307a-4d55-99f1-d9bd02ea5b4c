﻿(function()
{
	createNamespace("TF.Modal.ResourceScheduler").AddEditRestrictionModalViewModel = AddEditRestrictionModalViewModel;

	function AddEditRestrictionModalViewModel(resourceId, resourceType, displayName, startDate, endDate, restrictionEntity)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Restriction - " + displayName);
		this.sizeCss = "modal-dialog-md";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/resourcescheduler/addeditrestriction');
		this.buttonTemplate("modal/positivenegative");
		this.addEditRestrictionViewModel = new TF.Control.ResourceScheduler.AddEditRestrictionViewModel(resourceId, resourceType, startDate, endDate, restrictionEntity);
		this.data(this.addEditRestrictionViewModel);
	};

	AddEditRestrictionModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AddEditRestrictionModalViewModel.prototype.constructor = AddEditRestrictionModalViewModel;

	AddEditRestrictionModalViewModel.prototype.positiveClick = function()
	{
		return this.addEditRestrictionViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();

