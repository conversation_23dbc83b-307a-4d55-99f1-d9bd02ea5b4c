﻿(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.UrlBox = UrlBox;

	function UrlBox()
	{
		namespace.StringBox.apply(this, arguments);
	}

	UrlBox.prototype = Object.create(namespace.StringBox.prototype);

	UrlBox.constructor = UrlBox;

	UrlBox.prototype.type = "Url";

	UrlBox.prototype.valueChange = function(value)
	{
		if (value && TF.URLHelper.isWithoutLinkProtocolText(value))
		{
			value = `https://${value}`
		}
		namespace.StringBox.prototype.valueChange.call(this, value);
	};
})();
