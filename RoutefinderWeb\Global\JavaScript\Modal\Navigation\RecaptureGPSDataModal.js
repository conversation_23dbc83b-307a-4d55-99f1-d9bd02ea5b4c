(function()
{
	createNamespace("TF.Modal.Navigation").RecaptureGPSDataModal = RecaptureGPSDataModal;

	function RecaptureGPSDataModal()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-400";
		this.title('Recapture GPS Data');
		this.contentTemplate('Navigation/RecaptureGPSData');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Recapture Data");
		this.openDataSourceViewModel = new TF.Navigation.RecaptureGPSDataViewModel();
		this.data(this.openDataSourceViewModel);
	}

	RecaptureGPSDataModal.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	RecaptureGPSDataModal.prototype.constructor = RecaptureGPSDataModal;

	RecaptureGPSDataModal.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	RecaptureGPSDataModal.prototype.dispose = function()
	{
		this.data()?.pageLevelViewModel?.clearError();
		TF.Modal.BaseModalViewModel.prototype.dispose.apply(this, arguments);
	};
})();