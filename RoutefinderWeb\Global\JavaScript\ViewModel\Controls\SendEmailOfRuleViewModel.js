(function()
{
	const dataFieldAttr = "data-field";
	const dataFieldFormAttr = "data-field-form";
	const dataFieldUdfAttr = "data-field-udf";
	const dataFieldUdfidAttr = "data-field-udfid";
	const dataFieldUdfGuidAttr = "data-field-udfguid";

	createNamespace("TF.Control").SendEmailOfRuleViewModel = SendEmailOfRuleViewModel;
	const emailMessageSelector = 'input[name=emailMessage]';
	const ASSOCIATED_EMAIL_ADDRESS = "<associated emails>";

	function SendEmailOfRuleViewModel(options)
	{
		this.options = options;
		this.titleContent = ko.observable("SEND TO");

		this.obRecipientList = ko.observableArray([]);
		this.obSearchRecipient = ko.observable("");
		this.obRecipientSearchResultIsEmpty = ko.observable(false);
		this.obNewEmail = ko.observable("");
		this.obIsNewEditing = ko.observable(false);
		this.selectRecipientToClick = this.selectRecipientToClick.bind(this);
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);
		this.changePattern = this.changePattern.bind(this);
		const emailAddressList = this.options.EmailAddressList;
		this.obEmailAddressList = ko.observableArray(emailAddressList || []);
		if (emailAddressList.length > 0 && !this.options.EmailAddress)
		{
			this.options.EmailAddress = this.options.EmailAddressList[0];
		}

		if (this.options.IncludeFormAsPDF === undefined ||
			this.options.IncludeFormAsPDF === null)
		{
			this.options.IncludeFormAsPDF = true;
		}

		this.obEntityDataModel = ko.observable(new TF.DataModel.FormRuleActionEmailDataModal(this.options));

		this._initEmailVariablesAndControls();

		this.pageLevelViewModel = new TF.PageLevel.EmailPageLevelViewModel(this);
	}

	SendEmailOfRuleViewModel.prototype.changePattern = function(viewModel, e)
	{
		var self = this, htmlEditorId = "#ruleMessageBodyHtmlEditor",
			$MessageBodyHtmlEditor = self._$form.find(htmlEditorId), $optionBtn = $(e.target).closest(".option");
		if ($optionBtn.hasClass("selected"))
		{
			return;
		}

		var $container = $optionBtn.closest(".editor-wrapper");
		$container.find(".option").removeClass("selected");
		$optionBtn.addClass("selected");

		if ($optionBtn.hasClass("design"))
		{
			$container.find(".text-editor-wrapper").show();
			$container.find(".html-editor-wrapper").hide();
			self.messageBodyEditor.value($MessageBodyHtmlEditor.val());
		}
		else
		{
			$container.find(".html-editor-wrapper").show();
			$container.find(".text-editor-wrapper").hide();
			$MessageBodyHtmlEditor.val(self.messageBodyEditor.value());
		}
	};

	SendEmailOfRuleViewModel.prototype._initEmailVariablesAndControls = function()
	{
		this.obCcEnable = ko.observable(false);
		this.obBccEnable = ko.observable(false);

		this.obSelectEmailToList = ko.observableArray([]);
		this.obSelectEmailCcList = ko.observableArray([]);
		this.obSelectEmailBccList = ko.observableArray([]);

		this.obEmailToList = ko.observableArray([]);
		this.obEmailCcList = ko.observableArray([]);
		this.obEmailBccList = ko.observableArray([]);

		this.obUDFEmailToFields = ko.observableArray([]);
		this.obUDFEmailCcFields = ko.observableArray([]);
		this.obUDFEmailBccFields = ko.observableArray([]);

		this.obQuestionEmailToFields = ko.observableArray([]);
		this.obQuestionEmailCcFields = ko.observableArray([]);
		this.obQuestionEmailBccFields = ko.observableArray([]);

		if (this.obEntityDataModel().to())
		{
			const mailToList = this.convertEmailStringToList(this.obEntityDataModel().to());
			this.obEmailToList(mailToList);
			this.obSelectEmailToList(mailToList);
		}

		if (this.obEntityDataModel().cc())
		{
			const mailCcList = this.convertEmailStringToList(this.obEntityDataModel().cc());
			this.obEmailCcList(mailCcList);
			this.obSelectEmailCcList(mailCcList);
			this.obCcEnable(true);
		}

		if (this.obEntityDataModel().bcc())
		{
			const mailBccList = this.convertEmailStringToList(this.obEntityDataModel().bcc());
			this.obEmailBccList(mailBccList);
			this.obSelectEmailBccList(mailBccList);
			this.obBccEnable(true);
		}

		this.obEmailToErrorList = ko.observableArray([]);
		this.obEmailToString = ko.computed(function()
		{
			return this.getEmailString(this.obEmailToList(), this.obUDFEmailToFields(), this.obQuestionEmailToFields());
		}.bind(this));

		this.obEmailCcString = ko.computed(function()
		{
			let ccEmail = this.getEmailString(this.obEmailCcList(), this.obUDFEmailCcFields(), this.obQuestionEmailCcFields());
			if (!_.isEmpty(ccEmail))
			{
				this.obCcEnable(true);
			}

			return ccEmail;
		}.bind(this));

		this.obEmailBccString = ko.computed(function()
		{
			let bccEmail = this.getEmailString(this.obEmailBccList(), this.obUDFEmailBccFields(), this.obQuestionEmailBccFields());
			if (!_.isEmpty(bccEmail))
			{
				this.obBccEnable(true);
			}
			return bccEmail;
		}.bind(this));

		if (this.obEntityDataModel().uDFTo())
		{
			this.obUDFEmailToFields(this.obEntityDataModel().uDFTo().split(";"));
		}

		if (this.obEntityDataModel().uDFCc())
		{
			this.obUDFEmailCcFields(this.obEntityDataModel().uDFCc().split(";"));
		}

		if (this.obEntityDataModel().uDFBcc())
		{
			this.obUDFEmailBccFields(this.obEntityDataModel().uDFBcc().split(";"));
		}

		if (this.obEntityDataModel().questionTo())
		{
			this.obQuestionEmailToFields(this.obEntityDataModel().questionTo().split(";"));
		}

		if (this.obEntityDataModel().questionCc())
		{
			this.obQuestionEmailCcFields(this.obEntityDataModel().questionCc().split(";"));
		}

		if (this.obEntityDataModel().questionBcc())
		{
			this.obQuestionEmailBccFields(this.obEntityDataModel().questionBcc().split(";"));
		}
	}

	SendEmailOfRuleViewModel.prototype.getEmailString = function(userEmails, udfEmails, questionEmails)
	{
		let emailStr = this.convertEmailListToString(userEmails);

		let realUDFEmails = this.getCurrentUDFEmails(udfEmails);
		let realEmailQuestions = this.getCurrentEmailQuestions(questionEmails);
		let withoutAssociatedEmailAddressText = emailStr.indexOf(ASSOCIATED_EMAIL_ADDRESS) < 0;

		if ((realUDFEmails?.length || realEmailQuestions?.length) && withoutAssociatedEmailAddressText)
		{
			emailStr = _.isEmpty(emailStr) ? ASSOCIATED_EMAIL_ADDRESS :
				ASSOCIATED_EMAIL_ADDRESS + ';' + emailStr;
		}

		return emailStr;
	};

	SendEmailOfRuleViewModel.prototype.getCurrentUDFEmails = function(udfEmails)
	{
		let emailFields = this.options.allCurrentFormEmailUDFs.filter(udf =>
		{
			return udfEmails.includes(udf.UDFGuid);
		});
		return emailFields;
	}

	SendEmailOfRuleViewModel.prototype.getCurrentEmailQuestions = function(questionEmails)
	{
		let ret = this.options.allCurrentFormEmailQuestions.filter(q =>
		{
			return questionEmails.includes(q.Guid);
		});
		return ret;
	}

	SendEmailOfRuleViewModel.prototype.convertEmailStringToList = function(emailListString)
	{
		const emailList = (emailListString || "").split(";").filter(Boolean);
		return emailList.map(function(email)
		{
			return new TF.DataModel.ScheduledReportReceiptDataModel({
				SelectedUserId: 0,
				EmailAddress: email
			});
		});
	};

	SendEmailOfRuleViewModel.prototype.convertEmailListToString = function(emailList)
	{
		var list = _.uniq((emailList || []).map(function(i)
		{
			return (i.emailAddress() || "").toLowerCase();
		}).filter(Boolean));
		return list.join(";");
	};

	SendEmailOfRuleViewModel.prototype.EmailFormatter = function(item)
	{
		return item.emailAddress();
	};

	SendEmailOfRuleViewModel.prototype.initModel = function(viewModel, el)
	{
		this._$form = $(el);
		var validatorFields = {},
			isValidating = false,
			self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		if (!TF.isPhoneDevice)
		{
			this._$form.find("input[name='from']").focus();
		}

		validatorFields.FromAddress = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				},
				callback:
				{
					message: "invalid email",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "email");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						if (!testEmail(value).valid)
						{
							return false;
						}
						return true;
					}
				}
			}
		};

		validatorFields["mailToList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						return self.validateEmail("To", value, $field, updateErrors);
					}
				}
			}
		};

		validatorFields["mailCcList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						return self.validateEmail("Cc", value, $field, updateErrors);
					}
				}
			}
		};

		validatorFields["mailBccList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						return self.validateEmail("Bcc", value, $field, updateErrors);
					}
				}
			}
		};

		validatorFields["emailSubject"] = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [],
				live: "enabled",
				message: "This value is not valid",
				fields: validatorFields
			}).on("success.field.bv", function(e, data)
			{
				var $parent = data.element.closest(".form-group");
				$parent.removeClass("has-success");
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});
		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
		this.obEntityDataModel().apiIsDirty(false);
		this._initMessageEditor();
	};

	SendEmailOfRuleViewModel.prototype._initMessageEditor = function(viewModel, el)
	{
		var self = this, editorId = "#ruleEmailMessageEditor",
			$editorWrapper = self._$form.find(".editor-wrapper");

		$editorWrapper.css("visibility", "visible");
		if (self.messageBodyEditor)
		{
			self.messageBodyEditor.destroy();
		}
		self.messageBodyEditor = self._$form.find(editorId).kendoEditor({
			resizable: {
				toolbar: false,
				content: false
			},
			tools: ["formatting", "cleanFormatting", "undo", "redo", "fontName", "fontSize", "foreColor", "backColor", "bold", "italic", "underline", "justifyLeft",
				"justifyCenter", "justifyRight", "insertUnorderedList", "insertOrderedList", "indent", "createLink", "unlink", "createTable",
				"addRowAbove", "addRowBelow", "addColumnLeft", "addColumnRight", "deleteRow", "deleteColumn", BuildInsertEntityFieldToolbarItem(self.createInsertField.bind(self))],
			messages:
			{
				fontNameInherit: 'Default Font',
				fontSizeInherit: 'Default Font Size'
			},
			stylesheets:
				[
					"../../Global/ThirdParty/bootstrap/css/bootstrap.min.css",
					"../../Global/Css/KendoEditor.css"
				],
			select: function()
			{
				const $clearCssIcon = self.messageBodyEditor.toolbar.element.find("span.k-svg-i-clear-css");
				const hasSelectedText = self.messageBodyEditor.selectedHtml().length > 0;
				if (!hasSelectedText)
				{
					$clearCssIcon.addClass("disabled");
				}
				else
				{
					$clearCssIcon.removeClass("disabled");
				}
			}
		}).data("kendoEditor");

		self.compressedContentKendoEditor = new TF.Helper.CompressedContentKendoEditorDecorator(self.messageBodyEditor);
		self.compressedContentKendoEditor.bindCompressContentWithPasteEvent();

		setTimeout(function()
		{
			self.bindBodyEvents();
			$(self.messageBodyEditor.body).addClass("merge-doc-body");
			$(self.messageBodyEditor.body).blur(function()
			{
				self.obEntityDataModel().message(self.messageBodyEditor.value());
				self._$form.find(emailMessageSelector).change();
			});

			$(self.messageBodyEditor.body).on("mouseup mouseout touchmove keyup focus blur", function()
			{
				const $clearCssIcon = self.messageBodyEditor.toolbar.element.find("span.k-svg-i-clear-css");
				const hasSelectedText = self.messageBodyEditor.selectedHtml().length > 0;
				if (!hasSelectedText)
				{
					$clearCssIcon.addClass("disabled");
				}
				else
				{
					$clearCssIcon.removeClass("disabled");
				}
			});
		}, 300);
		let $MessageBodyHtmlEditor = self._$form.find('#ruleMessageBodyHtmlEditor');
		$MessageBodyHtmlEditor.blur(function()
		{
			self._$form.find(emailMessageSelector).change();
		});
		$editorWrapper.find(".k-insertImage").closest(".k-tool").hide();
		self.messageBodyEditor.refresh();

		self._$form.find(".editor-options-wrap .design").addClass("selected");
		self._$form.find(".editor-options-wrap .html").removeClass("selected");
		self.messageBodyEditor.toolbar.element.find("span.k-svg-i-clear-css").addClass("disabled");
		self.messageBodyEditor.value(self.options.Message || '');
	};

	SendEmailOfRuleViewModel.prototype.createInsertField = function()
	{
		var self = this;
		return tf.modalManager.showModal(new TF.Modal.SingleDataTypeFieldSelectorModalViewModel(
			self.options.dataType, TF.UserDefinedField.SingleDataTypeFieldSelectorViewModel))
			.then(fieldData =>
			{
				if (!fieldData || !fieldData.primaryField)
				{
					return;
				}

				self.insertHtml(fieldData.primaryField.toFieldBlock());
				if (fieldData.secondaryField)
				{
					self.insertHtml(`<p><span style='font-weight:bold;'>${fieldData.secondaryFieldTitle}</span><br class='k-br'></p>`);
					self.insertHtml(fieldData.secondaryField.toFieldBlock())
				}
				let editor = self.messageBodyEditor;
				editor.focus();
			});
	}

	SendEmailOfRuleViewModel.prototype.editInsertField = function(field)
	{
		if (!field.hasAttribute(dataFieldAttr))
		{
			return;
		}

		const isForm = field.hasAttribute(dataFieldFormAttr) && (field.getAttribute(dataFieldFormAttr) == "true");
		const isUdf = field.hasAttribute(dataFieldUdfAttr) && (field.getAttribute(dataFieldUdfAttr) == "true");
		const udfGuid = field.hasAttribute(dataFieldUdfGuidAttr) ? field.getAttribute(dataFieldUdfGuidAttr) : null;
		const displayName = field.nodeName === 'TABLE' ? '' : field.textContent;
		const selectedField =
		{
			key: (isUdf && udfGuid) ? udfGuid : field.getAttribute(dataFieldAttr),
			IsForm: isForm,
			isUdf,
			displayName
		};

		var self = this;
		return tf.modalManager.showModal(new TF.Modal.SingleDataTypeFieldSelectorModalViewModel(
			self.options.dataType, TF.UserDefinedField.SingleDataTypeFieldSelectorViewModel, selectedField))
			.then(fieldData =>
			{
				if (!fieldData || !fieldData.primaryField)
				{
					return;
				}

				if (!fieldData.primaryField.IsForm)
				{
					fieldData.primaryField.dataType = self.dataType;
				}

				var appliedStyle = field.hasAttribute("style") ? field.getAttribute("style") : null;
				const htmlTag = fieldData.primaryField.toFieldBlock(false, function()
				{
					if (appliedStyle)
					{
						return `style='${appliedStyle}' `;
					}

					return '';
				});
				$(field).replaceWith(htmlTag);
				let editor = self.messageBodyEditor;
				editor.focus();
			});
	}

	SendEmailOfRuleViewModel.prototype.insertHtml = function(html)
	{
		var editor = this.messageBodyEditor,
			range = editor.getRange(),
			focusEle = range.startContainer;
		if (focusEle.nodeName == "#text")
		{
			focusEle = focusEle.parentElement;
		}
		editor.exec("insertHtml", { value: html });
	};

	SendEmailOfRuleViewModel.prototype.bindBodyEvents = function()
	{
		var self = this, editor = self.messageBodyEditor;
		$(editor.body).on("input", function()
		{
			if (editor.currentVariableHtml)
			{
				var currentVariableHtml = editor.currentVariableHtml;
				setTimeout(function()
				{
					var range = editor.getRange();
					editor.selectRange(range);
					self.insertHtml(currentVariableHtml);
				}, 1);

				editor.currentVariableHtml = null;
				return;
			}
		}).on("keyup", function(e)
		{
			if (e.keyCode === $.ui.keyCode.ENTER)
			{
				var dumpElement = $(editor.body).find("span[contenteditable=false][data-field]");
				dumpElement.each(function()
				{
					var ele = $(this);
					if (!ele.text().trim())
					{
						var parent = ele.parent();
						ele.remove();
						var range = editor.createRange();
						range.selectNode(parent[0]);
						range.collapse();
						editor.selectRange(range);
					}
				});
			}
		}).off("dragstart.kendoEditor").on("dragstart", "*[data-field]", function(ev)
		{
			var dt = ev.originalEvent.dataTransfer, target = ev.target, tableTarget = $(target).closest("table[data-field]");
			if (tableTarget.length) target = tableTarget[0];
			var currentVariableHtml = target.outerHTML;
			dt.setData("text", "&nbsp;");
			dt.setData("text/html", "&nbsp;");
			dt.setData("application/html", currentVariableHtml);
		}).on("drop", function(ev)
		{
			var dt = ev.originalEvent.dataTransfer;
			var currentVariableHtml = (dt.getData("application/html") || "").trim();
			editor.currentVariableHtml = currentVariableHtml;
		}).on("dblclick", "img", function(ev)
		{
			var range = editor.createRange();
			range.selectNode(ev.target)
			setTimeout(function()
			{
				self.createSettingImageModal();
			});
		}).on("click", 'span[data-field], table[data-field]', function(ev)
		{
			var target = $(ev.currentTarget), removeIconWidth = 16, documentScrollLeft = $(ev.currentTarget.ownerDocument).scrollLeft();
			if (ev.clientX > target.offset().left + target.width() - removeIconWidth - documentScrollLeft)
			{
				target.remove();
				return;
			}
			else
			{
				self.editInsertField(ev.currentTarget);
			}
		});
	};

	function BuildInsertEntityFieldToolbarItem(clickHandler)
	{
		return {
			name: "insertfield",
			tooltip: "Insert field",
			exec: (e) => clickHandler()
		};
	}

	SendEmailOfRuleViewModel.prototype.validateEmail = function(emailType, value, $field, updateErrors)
	{
		if (TF.isPhoneDevice)
		{
			return true;
		}

		var self = this,
			obEmailList = self[String.format("obEmail{0}List", emailType)];
		if (value)
		{
			updateErrors($field, "required");
		}
		value = value.trim();

		if (!value)
		{
			obEmailList([]);
			return true;
		}

		var result = true,
			reg = /[,;]/,
			errorEmails = [],
			oldList = obEmailList(),
			emptyEmailList = obEmailList().filter(function(item) { return !self.EmailFormatter(item); }),
			newList = [];

		_.uniq(value.split(reg).map(function(i) { return i.trim(); })).forEach(function(item)
		{
			item = item.trim();
			if (!item)
			{
				return;
			}
			if (item === ASSOCIATED_EMAIL_ADDRESS)
			{
				return;
			}

			if (!testEmail(item).valid)
			{
				errorEmails.push(item);
				result = false;
			}

			var matched = oldList.filter(function(c)
			{
				return (self.EmailFormatter(c) || "").trim().toLowerCase() == item.trim().toLowerCase();
			})

			if (matched.length > 0)
			{
				newList = newList.concat(matched);
			}
			else
			{
				newList.push(new TF.DataModel.ScheduledReportReceiptDataModel({
					SelectedUserId: 0,
					EmailAddress: item
				}));
			}
		});

		obEmailList(newList.concat(emptyEmailList));
		var message = errorEmails.length == 1 ? errorEmails[0] + " is not a valid email." : errorEmails.length + " emails are invalid.";
		self._$form.find(String.format("small[data-bv-for=mail{0}List][data-bv-validator=callback]", emailType)).text(message);

		if (result)
		{
			self.pageLevelViewModel.obValidationErrorsSpecifed([]);
		}

		return result;
	};

	SendEmailOfRuleViewModel.prototype.focusField = function(viewModel, e)
	{
		$(viewModel.field).focus();
	};

	SendEmailOfRuleViewModel.prototype.CcEnableClick = function()
	{
		this.obCcEnable(true);
	};

	SendEmailOfRuleViewModel.prototype.BccEnableClick = function()
	{
		this.obBccEnable(true);
	};

	SendEmailOfRuleViewModel.prototype.onRecipientInputChanged = function(viewModel, e)
	{
		var self = this;

		var $input = $(e.currentTarget);
		var inputDom = $input[0];

		var newValue = inputDom.value;

		const hasAssociatedEmails = self._hasAssociatedEmails(inputDom.name);

		if (!hasAssociatedEmails || newValue.includes(ASSOCIATED_EMAIL_ADDRESS))
		{
			return;
		}

		let validEmails = newValue.replace(ASSOCIATED_EMAIL_ADDRESS, '').split(';').filter(function(email)
		{
			const rawEmail = email.trim();
			return !!rawEmail && testEmail(rawEmail).valid;
		});

		validEmails.unshift(ASSOCIATED_EMAIL_ADDRESS);
		inputDom.value = validEmails.join(';');
	}

	SendEmailOfRuleViewModel.prototype._hasAssociatedEmails = function(name)
	{
		if (name == 'mailToList')
		{
			return this.obUDFEmailToFields().length > 0 || this.obQuestionEmailToFields().length > 0;
		}
		else if (name == 'mailCcList')
		{
			return this.obUDFEmailCcFields().length > 0 || this.obQuestionEmailCcFields().length > 0;
		}
		else if (name == 'mailBccList')
		{
			return this.obUDFEmailBccFields().length > 0 || this.obQuestionEmailBccFields().length > 0;
		}
	}

	SendEmailOfRuleViewModel.prototype.selectRecipientToClick = function(viewModel, e)
	{
		var self = this;
		let sendType = $(e.currentTarget).data("send-type");
		let addressList = self[String.format("obEmail{0}List", sendType)];
		let selectAddressList = self[String.format("obSelectEmail{0}List", sendType)];

		self.getSelectedItems(addressList()).then(function(selectedItems)
		{
			let udfEmails = self[String.format("obUDFEmail{0}Fields", sendType)]();
			let questionEmails = self[String.format("obQuestionEmail{0}Fields", sendType)]();

			var options = {
				dataType: self.options.dataType,
				dataSource: self.options.dataSource,
				udfEmails: udfEmails,
				questionEmails: questionEmails,
				allCurrentFormEmailQuestions: self.options.allCurrentFormEmailQuestions,
			};

			const emailAddressList = selectedItems.map(i => i.Email.toLowerCase());
			tf.modalManager.showModal(new TF.Modal.FormEmailRuleSelectRecipientsModalViewModel(selectedItems, options)).then(function(result)
			{
				if (!result)
				{
					return;
				}

				let list = [];
				let udfEmail = self[String.format("obUDFEmail{0}Fields", sendType)];
				let questionEmail = self[String.format("obQuestionEmail{0}Fields", sendType)];

				if (result.emailType) // udf email
				{
					udfEmail(result.emailUDFGuids);
					questionEmail(result.emailQuestionGuids);
				}
				else
				{
					udfEmail([]); //clear udf email
					questionEmail([]); //clear question email

					list = result.map(function(item)
					{
						const name = [item.FirstName, item.LastName].filter(x => x).join(" ") || item.LoginId;
						if (!emailAddressList.includes(item.Email.toLowerCase()))
						{
							emailAddressList.push(item.Email.toLowerCase());
						}
						return new TF.DataModel.ScheduledReportReceiptDataModel(
							{
								SelectedUserId: item.Id,
								EmailAddress: item.Email,
								UserName: name
							});
					});
				}

				list = list.concat(addressList().filter(function(i) { return i.selectedUserId() == 0 && !emailAddressList.includes(i.emailAddress().toLowerCase()) }));
				addressList(list);
				selectAddressList(list);
				let emailInput = self._$form.find(`input[name="mail${sendType}List"]`);
				emailInput.blur();
			});
		});
	};

	SendEmailOfRuleViewModel.prototype.getSelectedItems = function(recipients)
	{
		var self = this;
		return self.options.modelType === "Email" ? self.getSelectedItemsForContact(recipients) : self.getSelectedItemsForSystemUser(recipients);
	};

	SendEmailOfRuleViewModel.prototype.getSelectedItemsForContact = function(recipients)
	{
		var self = this,
			emails = recipients.filter(function(item)
			{
				return item.selectedUserId() === 0;
			}).map(function(item)
			{
				return (item.emailAddress() || "").trim().toLowerCase();
			}).filter(Boolean),
			ids = recipients.filter(function(item)
			{
				return item.selectedUserId() !== 0;
			}).map(function(item)
			{
				return item.selectedUserId();
			});

		emails = _.uniq(emails);

		return Promise.all([
			self.getSelectedItemsByIds(ids),
			self.getSelectedItemsByEmails(emails).then(function(v) { return v.filter(function(i) { return i.DBID == tf.datasourceManager.databaseId; }) })
		]).then(function(values)
		{
			return _.uniqBy(_.flattenDeep(values), function(i) { return i.Id; });
		});
	};

	SendEmailOfRuleViewModel.prototype.getSelectedItemsForSystemUser = function(recipients)
	{
		var self = this,
			emails = recipients.map(function(item)
			{
				return (item.emailAddress() || "").trim().toLowerCase();
			}).filter(Boolean);

		return self.getSelectedItemsByEmails(emails);
	};

	SendEmailOfRuleViewModel.prototype.getSelectedItemsByIds = function(ids)
	{
		var self = this;
		return !ids.length ? Promise.resolve([]) : Promise.all(tf.urlHelper.chunk(ids, 1000).map(function(emailChunk)
		{
			var filterSyntax = emailChunk.join(","),
				paramData = { "@filter": String.format("in(Id,{0})", filterSyntax) };

			return tf.promiseAjax.get(self.getRequestUrl(), { paramData: paramData }).then(function(r)
			{
				return r.Items;
			}, function()
			{
				return [];
			});
		})).then(function(values)
		{
			return _.flattenDeep(values);
		});
	};

	SendEmailOfRuleViewModel.prototype.getSelectedItemsByEmails = function(emails)
	{
		var self = this;
		return !emails.length ? Promise.resolve([]) : Promise.all(tf.urlHelper.chunk(emails, 100).map(function(emailChunk)
		{
			var filterSyntax = emailChunk.join(","),
				paramData = { "@filter": String.format("in(Email,{0})", filterSyntax) };

			return tf.promiseAjax.get(self.getRequestUrl(), { paramData: paramData }).then(function(r)
			{
				return r.Items;
			}, function()
			{
				return [];
			});
		})).then(function(values)
		{
			return _.flattenDeep(values);
		});
	};

	SendEmailOfRuleViewModel.prototype.getRequestUrl = function()
	{
		return pathCombine(tf.api.apiPrefixWithoutDatabase(), "users");
	};

	SendEmailOfRuleViewModel.prototype.apply = function()
	{
		return this.trySave().catch(function() { });
	};

	SendEmailOfRuleViewModel.prototype.trySave = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
				{
					if (TF.isPhoneDevice)
					{
						if (this.obEmailToList().length == 0 && this.obEmailCcList().length == 0 && this.obEmailBccList().length == 0)
						{
							var message = "At least one recipient is required.";
							tf.promiseBootbox.alert(message, "Warning");
							return Promise.reject();
						}
					}
					var messages = validator.getMessages(validator.getInvalidFields());
					var $fields = validator.getInvalidFields();
					var validationErrors = [];
					$fields.each(function(i, fielddata)
					{
						if (i == 0)
						{
							$(fielddata).focus();
						}
						validationErrors.push(
							{
								name: ($(fielddata).attr("data-bv-error-name") ? $(fielddata).attr("data-bv-error-name") : $(fielddata).closest("div.form-group").find("strong").text()),
								message: messages[i].replace("&lt;", "<").replace("&gt;", ">"),
								field: $(fielddata)
							});

					}.bind(this));
					this.obErrorMessageDivIsShow(true);
					this.obValidationErrors(validationErrors);

					return Promise.reject();
				}
				else
				{
					if (this.obEntityDataModel().subject() === "" || this.obEntityDataModel().message() === "" ||
						this.obEntityDataModel().subject() === null || this.obEntityDataModel().message() === null)
					{
						var info = "The Subject and Message have";
						if ((this.obEntityDataModel().subject() === "" || this.obEntityDataModel().subject() === null) && (this.obEntityDataModel().message() != "" && this.obEntityDataModel().message() != null))
						{
							info = "The Subject has";
						}
						if ((this.obEntityDataModel().subject() != "" && this.obEntityDataModel().subject() != null) &&
							(this.obEntityDataModel().message() === "" || this.obEntityDataModel().message() === null))
						{
							info = "The Message has";
						}
						return tf.promiseBootbox.yesNo(info + " not been specified.  Are you sure you want to send this email?", "Confirmation Message")
							.then(function(ans)
							{
								if (!ans)
								{
									return Promise.reject();
								}
								else
								{
									return this.save();
								}
							}.bind(this));
					}
					return this.save();
				}
			}.bind(this));
	};

	SendEmailOfRuleViewModel.prototype.save = function()
	{
		var self = this;
		return Promise.resolve().then(function()
		{
			self.obEntityDataModel().to(self.obEmailToString());
			self.obEntityDataModel().cc(self.obEmailCcString());
			self.obEntityDataModel().bcc(self.obEmailBccString());

			self.obEntityDataModel().uDFTo(self.obUDFEmailToFields().join(";"));
			self.obEntityDataModel().uDFCc(self.obUDFEmailCcFields().join(";"));
			self.obEntityDataModel().uDFBcc(self.obUDFEmailBccFields().join(";"));

			self.obEntityDataModel().questionTo(self.obQuestionEmailToFields().join(";"));
			self.obEntityDataModel().questionCc(self.obQuestionEmailCcFields().join(";"));
			self.obEntityDataModel().questionBcc(self.obQuestionEmailBccFields().join(";"));

			const sendData = self.obEntityDataModel().toData()
			return sendData;
		});
	};

	SendEmailOfRuleViewModel.prototype.hasUDFEmails = function()
	{
		return this.obUDFEmailToFields()?.length ||
			this.obUDFEmailCcFields()?.length ||
			this.obUDFEmailBccFields()?.length;
	}

	SendEmailOfRuleViewModel.prototype.hasEmailQuestions = function()
	{
		return this.obQuestionEmailToFields()?.length ||
			this.obQuestionEmailCcFields()?.length ||
			this.obQuestionEmailBccFields()?.length;
	}

	SendEmailOfRuleViewModel.prototype.close = function()
	{
		return new Promise(function(resolve, reject)
		{
			resolve(true);
		}.bind(this));
	};

	SendEmailOfRuleViewModel.prototype.emptySearchRecipient = function()
	{
		this.obSearchRecipient("");
	};

	SendEmailOfRuleViewModel.prototype.createSettingImageModal = function()
	{
		var self = this;
		var editor = self.messageBodyEditor,
			selection = editor.getSelection(),
			existedImage = getSelectedImage(selection);
		var options = (existedImage == null) ?
			{ title: "Insert Image", positiveButtonLabel: "Insert" } :
			{ title: "Edit Image", positiveButtonLabel: "Save" };

		if (!existedImage)
		{
			return;
		}

		tf.modalManager.showModal(
			new TF.Modal.ImageSettingsModalViewModel(existedImage, options)
		).then(function(_)
		{
			editor.focus();
			setTimeout(() =>
			{
				//Add a setTimeout here to delay remove onload event of the img element, to prevent incorrect image dimensions.
				existedImage.hasAttribute("onload") && existedImage.removeAttribute("onload");
			});
		});
	};

	SendEmailOfRuleViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
		if (this.documentEntities)
		{
			this.documentEntities.length = 0;
		}
		if (this.messageBodyEditor)
		{
			this.messageBodyEditor.destroy();
		}
		if (this.intervalID)
		{
			clearInterval(this.intervalID);
		}

		//this._$form.data("bootstrapValidator").destroy();

		return Promise.resolve(true);
	};

	function getSelectedImage(selection)
	{
		if (selection && !selection.isCollapsed)
		{
			var count = selection.rangeCount;
			for (var i = 0; i < count; i++)
			{
				var node = getRangeSelectedNode(selection.getRangeAt(i), "img");
				if (node)
				{
					return node;
				}
			}
		}

		return null;
	}

	function getRangeSelectedNode(range, nodeName)
	{
		var rangeIterator = new TF.RangeIterator(range), nodeName = nodeName.toLowerCase();
		return rangeIterator.findFirst(function(node)
		{
			if (node.nodeName.toLowerCase() == nodeName)
			{
				return node;
			}
		});
	}
})();