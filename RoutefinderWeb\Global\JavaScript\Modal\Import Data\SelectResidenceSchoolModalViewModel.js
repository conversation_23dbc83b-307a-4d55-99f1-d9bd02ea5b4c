(function()
{
	createNamespace('TF.Modal').SelectResidenceSchoolModalViewModel = SelectResidenceSchoolModalViewModel;

	function SelectResidenceSchoolModalViewModel(residence)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.contentTemplate('modal/import data/selectresidenceschool');
		self.buttonTemplate('modal/positivenegative');
		self.selectResidenceSchoolViewModel = new TF.Control.SelectResidenceSchoolViewModel(residence);
		self.data(self.selectResidenceSchoolViewModel);
		self.obPositiveButtonLabel("OK");
		self.obNegativeButtonLabel("Cancel");
		self.title(residence.Student.FirstName + " " + residence.Student.Mi + " " + residence.Student.LastName);
	};

	SelectResidenceSchoolModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	SelectResidenceSchoolModalViewModel.prototype.constructor = SelectResidenceSchoolModalViewModel;

	/**
	 * The event of OK button click.
	 * @return {void}
	 */
	SelectResidenceSchoolModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;

		self.selectResidenceSchoolViewModel.apply().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};

	/**
	 * Unsaved changes.
	 * @return {void}
	 */
	SelectResidenceSchoolModalViewModel.prototype.negativeClose = function(returnData)
	{
		var self = this;

		return tf.promiseBootbox.yesNo({ message: "You have unselected school.  Would you like to save your changes prior to closing?", backdrop: true, title: "Unselected school", closeButton: true })
			.then(function(result)
			{
				if (result == true)
				{
					return self.positiveClick();
				}
				if (result == false)
				{
					return TF.Modal.BaseModalViewModel.prototype.negativeClose.call(self, returnData);
				}
			});
	};

	/**
	 * Dispose.
	 * @return {void}
	 */
	SelectResidenceSchoolModalViewModel.prototype.dispose = function()
	{
		var self = this;
		self.selectResidenceSchoolViewModel.dispose();
	};
})();
