﻿(function()
{
	createNamespace("TF.Modal").KendoListMoverModalViewModel = KendoListMoverModalViewModel;

	function KendoListMoverModalViewModel(EntityViewModels_unassigned, EntityViewModels_assigned, ungeoEntity, options, successCallback, failCallback, assignStudents, unAssignedStudents, exception)
	{
		TF.Modal.BaseModalViewModel.call(this, successCallback, failCallback);
		this.options = options;
		this.title(options.pageTitle);
		this.contentTemplate("controls/KendoListMover");
		this.buttonTemplate("modal/positivenegative");
		this.sizeCss = "modal-dialog-xlg";
		this.KendoListMoverViewModel = new TF.Fragment.KendoListMoverViewModel(EntityViewModels_unassigned, EntityViewModels_assigned, ungeoEntity, options, this.shortCutKeyHashMapKeyName, this.obDisableControl, assignStudents, unAssignedStudents, exception);
		this.data(this.KendoListMoverViewModel);
		this.obPositiveButtonLabel("Apply");
		this.obResizable(false);
	}

	KendoListMoverModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	KendoListMoverModalViewModel.prototype.constructor = KendoListMoverModalViewModel;

	KendoListMoverModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.KendoListMoverViewModel.apply().then(function(result)
		{
			if (this.options.checkOnApply ? this.options.checkOnApply(result) : result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	KendoListMoverModalViewModel.prototype.dispose = function()
	{
		this.KendoListMoverViewModel.dispose();
	};

})();

