﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.MailingCityDataModel = function(cityEntity)
	{
		namespace.BaseDataModel.call(this, cityEntity);
	}

	namespace.MailingCityDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.MailingCityDataModel.prototype.constructor = namespace.MailingCityDataModel;

	namespace.MailingCityDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "", toMapping: function(v) { return (v || "").trim(); } }
	];
})();