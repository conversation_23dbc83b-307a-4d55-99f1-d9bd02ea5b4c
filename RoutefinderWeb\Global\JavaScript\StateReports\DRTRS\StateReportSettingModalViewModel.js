(function()
{
	createNamespace('TF.Modal').StateReportSettingModalViewModel = StateReportSettingModalViewModel;

	function StateReportSettingModalViewModel()
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-md";
		self.contentTemplate('modal/StateReport/StateReportSetting');
		self.buttonTemplate('modal/positivenegative');
		self.title("Transfinder DRTRS Export Module 2020-2021");
		self.stateReportSettingViewModel = new TF.Control.StateReportSettingViewModel();
		self.data(self.stateReportSettingViewModel);
		self.obPositiveButtonLabel('Export Files');
	}

	StateReportSettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	StateReportSettingModalViewModel.prototype.constructor = StateReportSettingModalViewModel;
	StateReportSettingModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.stateReportSettingViewModel.apply().then((res) =>
		{
			tf.loadingIndicator.tryHide();
			if (Array.isArray(res) && res.length === 2)
			{
				let ctxId = res[0].Id;
				var option = _.extend(res[0], res[1]);
				self.negativeClose();
				tf.modalManager.showModal(new TF.Modal.StateReportRunResultModalViewModel(option)).then(function()
				{
					// Send request to cleanup temp resources after showResult modal was dismissed
					tf.promiseAjax.delete(pathCombine(tf.api.apiPrefixWithoutDatabase(), "drtrsprocesses"), {
						paramData: { contextId: ctxId }
					}, { overlay: false });
				});
			}
		}).catch(ex =>
		{
			tf.loadingIndicator.tryHide();
			tf.promiseBootbox.alert("Error occurred when processing students.");
		})
	}
})()