﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.UserProfileDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	}

	namespace.UserProfileDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.UserProfileDataModel.prototype.constructor = namespace.UserProfileDataModel;

	namespace.UserProfileDataModel.prototype.mapping = [
		{ from: "ArchivePrompt", default: false },
		{ from: "UserId", default: 0 },
		{ from: "LastName", default: '', toMapping: function(v) { return (v || "").trim(); } },
		{ from: "LoginID", to: 'loginId', default: '' },
		{ from: "FirstName", default: '', toMapping: function(v) { return (v || "").trim(); } },
		{ from: "Email", default: '' },
		{ from: "Phone", default: '' },
		{ from: "WorkPhoneExt", default: '' },
		{ from: "Password", default: '' },
		{ from: "AccountEnabled", default: true },
		{ from: "Id", default: 0 },
		{ from: "EnhancedMapText", default: true },
		{ from: "EnhancedMapImages", default: true },
		{ from: "EnhancedMapLines", default: true },
		{ from: "GeoCodeType", default: true },
		{ from: "GeocodeAltSite", default: false },
		{ from: "GeocodeStudent", default: false },
		{ from: "GeocodeGeoRegion", default: false },
		{ from: "GeocodeSchool", default: false },
		{ from: "RemoveOverlapStopBdy", default: true },
		{ from: "MoveDuplicateNode", default: false },
		{ from: "RemoveOverlapSchoolBdy", default: false },
		{ from: "RemoveDocCenterOnDelete", default: false },
		{ from: "RemoveOverlapParcelBdy", default: false },
		{ from: "Interactive", default: false },
		{ from: "FindPopulationRegion", default: false },
		{ from: "FindDistanceforStudent", default: false },
		{ from: "FindScheduleforStudent", default: false },
		{ from: "SchoolOfResidence", default: false },
		{ from: "DefaultTime", default: "1899-12-30T05:00:00" },
		{ from: "District", default: "" },
		{ from: "MailCity", default: 0 },
		{ from: "MailState", default: 0 },
		{ from: "MailPostalCode", default: 0 },
		{ from: "AreaCode", default: "0" },
		{ from: "TripColors", default: "" },
		{ from: "GridAltRow", default: "" },
		{ from: "Tripcolor0", default: "" },
		{ from: "Tripcolor1", default: "" },
		{ from: "Tripcolor2", default: "" },
		{ from: "Tripcolor3", default: "" },
		{ from: "Tripcolor4", default: "" },
		{ from: "Tripcolor5", default: "" },
		{ from: "Tripcolor6", default: "" },
		{ from: "Tripcolor7", default: "" },
		{ from: "Tripcolor8", default: "" },
		{ from: "Tripcolor9", default: "" },
		{ from: "Tripcolor10", default: "" },
		{ from: "Tripcolor11", default: "" },
		{ from: "Tripcolor12", default: "" },
		{ from: "Tripcolor13", default: "" },
		{ from: "Tripcolor14", default: "" },
		{ from: "Tripcolor15", default: "" },
		{ from: "Tripcolor16", default: "" },
		{ from: "Tripcolor17", default: "" },
		{ from: "Tripcolor18", default: "" },
		{ from: "Tripcolor19", default: "" },
		{ from: "Tripcolor20", default: "" },
		{ from: "Tripcolor21", default: "" },
		{ from: "Tripcolor22", default: "" },
		{ from: "Tripcolor23", default: "" },
		{ from: "SchOfResRedistIds", default: "" },
		{ from: "RoutingProfileId", default: 0 },
		{ from: "Title", default: '' },
	];
})();
