(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.SettingsConfigurationDataModal = function(settingsConfigurationEntity)
	{
		namespace.BaseDataModel.call(this, settingsConfigurationEntity);
	};

	namespace.SettingsConfigurationDataModal.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.SettingsConfigurationDataModal.prototype.constructor = namespace.SettingsConfigurationDataModal;

	namespace.SettingsConfigurationDataModal.prototype.mapping = [
		{ from: "TimeZone", default: "" },
		{ from: "ClientId", default: "" },
		{ from: "SMTPHost", default: "" },
		{ from: "SMTPPort", default: 0 },
		{ from: "SMTPUserName", default: "" },
		{ from: "SMTPPassword", default: "" },
		{ from: "SMTPSSL", default: false },
		{ from: "EmailAddress", default: "" },
		{ from: "EmailName", default: "" },
		{ from: "MailToList", default: [] },
		{ from: "MailCcList", default: [] },
		{ from: "MailBccList", default: [] },
		{ from: "EmailSubject", default: "" },
		{ from: "EmailMessage", default: "" },
		{ from: "TransfinderDataFolder", default: "" },
		{ from: "InstallationLocation", default: "" },
		{ from: "DatabaseServer", default: "" },
		{ from: "DatabaseName", default: "" },
		{ from: "DatabaseLoginId", default: "" },
		{ from: "DatabasePassword", default: "" },
		{ from: "IsRespectDaylight", default: true },
		{ from: "GroupPoints", default: true },
		{ from: "ClusterMinMapPoints", default: 0 },
		{ from: "ClusterMinZoomLevel", default: 14 },
		{ from: "ClusterTolerance", default: 50 },
		{ from: "TripCalcOption", default: 0 },
		{ from: "UnitOfMeasure", default: 0 },
		{ from: "AllowMultipleCardStudent", default: false },
		{ from: "AllowMultipleCardStaff", default: false }
	];

})();