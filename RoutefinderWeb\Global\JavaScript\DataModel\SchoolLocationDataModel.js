(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.SchoolLocationDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	};

	namespace.SchoolLocationDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.SchoolLocationDataModel.prototype.constructor = namespace.SchoolLocationDataModel;

	namespace.SchoolLocationDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "SchoolCode", default: "" },
		{ from: "DBID", default: 0 },
		{ from: "Xcoord", default: 0 },
		{ from: "Ycoord", default: 0 },
		{ from: "PolygonXMax", default: 0 },
		{ from: "PolygonYMax", default: 0 },
		{ from: "PolygonXMin", default: 0 },
		{ from: "PolygonYMin", default: 0 },
		{ from: "PolygonRings", default: "[]" },
	];
})();
