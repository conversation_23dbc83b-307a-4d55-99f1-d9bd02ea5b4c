(function()
{
	createNamespace("TF").TFNotification = TFNotification;

	function TFNotification(options)
	{
		this.resizeTimer = null;
		this.element = options.element;
		this.eventNamespace = getUniqueId();
	}

	function getUniqueId()
	{
		return Math.random().toString(36).substring(7);
	}

	let instance;
	TFNotification.getInstance = function()
	{
		if (!instance)
		{
			let element = $(`<div class="global-notification">`).appendTo(document.body);
			instance = new TFNotification({
				element: element
			});
		}
		return instance;
	};

	TFNotification.prototype.dispose = function()
	{
		if (this.resizeTimer)
		{
			window.clearTimeout(this.resizeTimer);
			this.resizeTimer = null;
		}

		$(document.body).off(this.eventNamespace);
		$(window).off(this.eventNamespace);
		let notification = this.element.data("kendoNotification");
		if (notification)
		{
			notification.destroy();
		}
	};

	TFNotification.prototype.show = function(info)
	{
		let implement = this.getImplement(),
			id = info.id || getUniqueId(),
			type = info.type || "info";
		delete info.type;
		info.id = id;
		implement.getNotifications().find(`[item-id=${id}]`).parent().parent().parent().remove();
		this.refreshNotificationLayout(implement);
		implement.show(info, type);
		if (info.zIndex)
		{
			var curElement = implement.getNotifications().find(`[item-id=${id}]`) || implement.getNotifications().last();
			curElement.closest(".k-animation-container").css("z-index", info.zIndex);
		}
		return id;
	};

	TFNotification.prototype.getImplement = function()
	{
		let notification = this.element.data("kendoNotification");
		if (!notification)
		{
			let errorTemplate = `<div class="notification-item edit-error" item-id="#=id#">
			<div class="icon-container">
				<span class="iconbutton error"></span>
			</div>
			<div class="error-description">
				<span>#= message #</span>
			</div>
			<div class="close-button">
			</div>
		</div>`,
				infoTemplate = `<div class="notification-item edit-success" item-id="#=id#">
			<div class="icon-container">
				<span class="iconbutton success"></span>
			</div>
			<div class="success-description">
				<span>#= message #</span>
			</div>
			<div class="close-button">
			</div>
		</div>`;
			notification = this.element.kendoNotification({
				position: {
					pinned: true,
					top: 23,
					right: 35
				},
				width: 400,
				autoHideAfter: 0,
				hideOnClick: false,
				stacking: "down",
				templates: [{
					type: "info",
					template: infoTemplate
				}, {
					type: "error",
					template: errorTemplate
				}, {
					type: "success",
					template: infoTemplate
				}]
			}).data("kendoNotification");

			$(document.body).on('click.' + this.eventNamespace, '.notification-item .close-button', e => $(e.target).closest(".k-notification").parent().parent().remove());

			$(window).on("resize." + this.eventNamespace, () =>
			{
				if (this.resizeTimer)
				{
					window.clearTimeout(this.resizeTimer);
				}

				this.resizeTimer = window.setTimeout(() =>
				{
					this.refreshNotificationLayout(notification);
					window.clearTimeout(this.resizeTimer);
					this.resizeTimer = null;
				}, 100);
			});
		}

		return notification;
	};

	TFNotification.prototype.refreshNotificationLayout = function(notification)
	{
		let allNotificationPopups = notification.getNotifications();
		if (!allNotificationPopups.length)
		{
			return;
		}

		let y = notification.options.position.top;
		allNotificationPopups.each(function()
		{
			let element = $(this),
				container = element.parent().parent();

			container.css({
				top: y,
				left: '50%',
				transform: 'translateX(-50%)',
			});

			y += container.height();
		});
	};
})();
