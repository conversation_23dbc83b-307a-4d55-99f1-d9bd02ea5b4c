(function()
{
	createNamespace('TF.Control').ListMoverSelectSchoolBoundarySetsViewModel = ListMoverSelectSchoolBoundarySetsViewModel;

	function ListMoverSelectSchoolBoundarySetsViewModel(selectedData, options)
	{
		options.getUrl = function()
		{
			return pathCombine(tf.api.apiPrefix("V2"), "redistricts") + "?@relationships=School"
		};
		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
	}

	ListMoverSelectSchoolBoundarySetsViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectSchoolBoundarySetsViewModel.prototype.constructor = ListMoverSelectSchoolBoundarySetsViewModel;

	ListMoverSelectSchoolBoundarySetsViewModel.prototype.columnSources = {
		school: [
			{
				FieldName: 'Name',
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName('Name'),
				Width: '150px',
				type: 'string'//,
				// isSortItem: true
			},
			{
				FieldName: 'SchoolDisplayField',
				DisplayName: 'Schools',
				Width: '120px',
				type: 'string'//,
				// isSortItem: true
			}
		]
	};

	ListMoverSelectSchoolBoundarySetsViewModel.prototype.getFields = function()
	{
		return this.columns.map(function(item) { return item.FieldName; }).concat(['Id']);
	};

	ListMoverSelectSchoolBoundarySetsViewModel.prototype.initGridScrollBar = function(container)
	{//need check soon
		var $gridContent = container.find(".k-grid-content");
		$gridContent.css({
			"overflow-y": "auto"
		});

		if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
		{
			$gridContent.find("colgroup col:last").css({
				width: 117
			});
		}
		else
		{
			$gridContent.find("colgroup col:last").css({
				width: 100
			});
		}
	};
	ListMoverSelectSchoolBoundarySetsViewModel.prototype.getAllRecords = function()
	{
		if (this.options.serverPaging)
		{
			return Promise.resolve();
		}
		var requestOption = {
			data: {},
			paramData: {}
		};
		requestOption.data.idFilter = {};
		var sortColumns = TF.FilterHelper.getSortColumns(this.columns);
		TF.FilterHelper.setSortItems(requestOption, sortColumns);
		var self = this;

		requestOption = this.setLeftRequestOption(
			{
				data: {},
				paramData: {}
			});
		requestOption.data.idFilter = {};

		self._addSortItemIntoRequest(requestOption);

		return tf.promiseAjax.get(this.setLeftGridRequestURL(this.options.getUrl(this.options.type, this.options)), requestOption)
			.then(function(response)
			{
				if (!self.obshowRemoveColumnButton())
				{
					// remove empty items
					response.Items = (response.Items || []).filter(function(item)
					{
						return self.columns.some(function(c) { return !!item[c.FieldName]; });
					});

					// remove duplicated value
					response.Items = _.uniqBy(response.Items || [], function(item)
					{
						return self.columns.map(function(c)
						{
							return item[c.FieldName] || "";
						}).join("_*&^@_");// to avoid accident
					});
				}

				return self._getSourceFromResponse(response).then(function(records)
				{
					self.allRecords = records;
					if (self.options.invalidIds &&
						self.options.invalidIds.length > 0)
					{
						self.allRecords = self.allRecords.filter(function(item)
						{
							return !Enumerable.From(self.options.invalidIds).Any("$=='" + item.Id + "'");
						});
					}
					// change all records data to use selected data, because some time selected data is temp changed by user
					self.selectedData.forEach(function(data)
					{
						for (var i = 0; i < self.allRecords.length; i++)
						{
							if (self.allRecords[i].Id == data.Id)
							{
								for (var key in self.allRecords[i])
								{
									if (self.allRecords[i].hasOwnProperty(key) && data.hasOwnProperty(key))
									{
										self.allRecords[i][key] = data[key];
									}
								}
							}
						}
					});
					self.allRecords.forEach(function(r)
					{
						var fieldText = "";
						r.SchoolList.forEach(function(s) { return fieldText += s.SchoolCode + "(" + s.GradeRange + ")," });
						r.SchoolDisplayField = fieldText.slice(0, -1);
					})
				});
			});
	};
	ListMoverSelectSchoolBoundarySetsViewModel.prototype.setLeftRequestOption = function(requestOptions)
	{
		requestOptions = TF.Control.KendoListMoverWithSearchControlViewModel.prototype.setLeftRequestOption.call(this, requestOptions);
		// requestOptions.paramData.time = toISOStringWithoutTimeZone(moment().currentTimeZoneTime());
		//
		// if (!this.obShowEnabled())
		// {
		// 	requestOptions.data.filterSet = requestOptions.data.filterSet || {
		// 		FilterItems: [], FilterSets: [], LogicalOperator: "and"
		// 	};
		// 	var filterItems = requestOptions.data.filterSet.FilterItems;
		// 	filterItems = filterItems.filter(function(filterItem)
		// 	{
		// 		if (filterItem.FieldName !== "InProgress")
		// 		{
		// 			return true;
		// 		}
		// 		return false;
		// 	});
		// 	if (filterItems.length === 0)
		// 		requestOptions.data.filterSet = null;
		// 	else
		// 		requestOptions.data.filterSet.FilterItems = filterItems;
		// }

		return requestOptions;
	};

	ListMoverSelectSchoolBoundarySetsViewModel.prototype.setRightRequestOption = function(requestOptions)
	{
		requestOptions = TF.Control.KendoListMoverWithSearchControlViewModel.prototype.setRightRequestOption.call(this, requestOptions);
		return requestOptions;
	};

	ListMoverSelectSchoolBoundarySetsViewModel.prototype.apply = function()
	{
		// Todo: input data struct (studentDataModel) different with output data struct (subClass),  need convert data
		return new Promise(function(resolve, reject)
		{
			resolve(this.selectedData);
		}.bind(this));
	};

	ListMoverSelectSchoolBoundarySetsViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			resolve(false);
		});
	};
})();
