(function()
{
	createNamespace("TF.Modal.Navigation").RestoreProArchiveModal = RestoreProArchiveModal;

	function RestoreProArchiveModal()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-sm";
		this.title('New Data Source from Routefinder Pro Archive');
		this.contentTemplate('Navigation/RestoreProArchive');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Create");
		this.openDataSourceViewModel = new TF.Navigation.RestoreProArchiveViewModel();
		this.data(this.openDataSourceViewModel);
	};

	RestoreProArchiveModal.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	RestoreProArchiveModal.prototype.constructor = RestoreProArchiveModal;

	RestoreProArchiveModal.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();