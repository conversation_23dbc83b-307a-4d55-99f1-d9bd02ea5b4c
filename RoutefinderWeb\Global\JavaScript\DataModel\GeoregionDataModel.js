﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.GeoregionDataModel = function(georegionEntity)
	{
		namespace.BaseDataModel.call(this, georegionEntity);
	}

	namespace.GeoregionDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.GeoregionDataModel.prototype.constructor = namespace.GeoregionDataModel;

	namespace.GeoregionDataModel.prototype.mapping = [
		{ "from": "AltsitesCount", "default": null },
		{ "from": "BoundaryObject", "default": null },
		{ "from": "Comments", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "GDB_GEOMATTR_DATA", "default": null },
		{ "from": "GeoCity", "default": "" },
		{ "from": "GeoConfidence", "default": null },
		{ "from": "GeoCounty", "default": "" },
		{ "from": "GeoRegionType", "default": null },
		{ "from": "GeoRegionTypeId", "default": 0 },
		{ "from": "GeoStreet", "default": "" },
		{ "from": "GeoZip", "default": "" },
		{ "from": "GeoRegionTypeName", "default": null },
		{ "from": "HotLink", "default": "" },
		{ "from": "Id", "default": 0 },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "MailCity", "default": function() { return tf.setting.userProfile.MailCityName; } },
		{ "from": "MailCityId", "default": function() { return tf.setting.userProfile.MailCity; } },
		{ "from": "MailState", "default": function() { return tf.setting.userProfile.MailStateName; } },
		{ "from": "MailStateId", "default": function() { return tf.setting.userProfile.MailState; } },
		{ "from": "MailStreet1", "default": null },
		{ "from": "MailStreet2", "default": "" },
		{ "from": "MailZip", "default": function() { return tf.setting.userProfile.MailZipName; } },
		{ "from": "MailZipId", "default": function() { return tf.setting.userProfile.MailPostalCode; } },
		{ "from": "Name", "default": "" },
		{ "from": "SchoolCount", "default": null },
		{ "from": "Schools", "default": null },
		{ "from": "Shape", "default": null },
		{ "from": "StudentCount", "default": null },
		{ "from": "TripstopsCount", "default": null },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
		{ "from": "Xcoord", "default": 0 },
		{ "from": "Ycoord", "default": 0 }
	];

})();
