﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.TripSymbolDataModel = function(tripSymbolEntity)
	{
		namespace.BaseDataModel.call(this, tripSymbolEntity);
	}

	namespace.TripSymbolDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.TripSymbolDataModel.prototype.constructor = namespace.TripSymbolDataModel;

	namespace.TripSymbolDataModel.prototype.mapping = [
		{ from: "Color", default: "#00A2E8" },
		{ from: "Style", default: "solid" },
		{ from: "Width", default: 4 },
		{ from: "Alpha", default: 1 }
	];
})();