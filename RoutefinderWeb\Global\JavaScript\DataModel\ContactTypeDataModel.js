﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.ContactTypeDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	}

	namespace.ContactTypeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ContactTypeDataModel.prototype.constructor = namespace.ContactTypeDataModel;

	namespace.ContactTypeDataModel.prototype.mapping = [
		{ from: "ID", to: "id", default: 0 },
		{ from: "Type", default: "" },
		{ from: "SystemRequired", default: false }
	];

	namespace.ContactTypeDataModel.prototype.toData = function(full)
	{
		let data = namespace.BaseDataModel.prototype.toData.call(this, full);
		data.Type = data.Type == null ? null : data.Type.trim();

		return data;
	};
})();