﻿(function()
{
	createNamespace('TF.Modal').ListMoverSelectTripStopModalViewModel = ListMoverSelectTripStopModalViewModel;

	function ListMoverSelectTripStopModalViewModel(selectedData, options)
	{
		options.displayCheckbox = false;
		options.displayAddButton = false;
		//options.mustSelect = false;
		selectedData = selectedData.map(function(item) { return item; });
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverSelectTripStopViewModel = new TF.Control.ListMoverSelectTripStopViewModel(selectedData, options);
		this.data(this.ListMoverSelectTripStopViewModel);
	}

	ListMoverSelectTripStopModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectTripStopModalViewModel.prototype.constructor = ListMoverSelectTripStopModalViewModel;

	ListMoverSelectTripStopModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverSelectTripStopViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ListMoverSelectTripStopModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverSelectTripStopViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};

})();
