(function()
{
	createNamespace('TF.Control').SelectImportDefinitionFileViewModel = SelectImportDefinitionFileViewModel;

	function SelectImportDefinitionFileViewModel()
	{
		var self = this;

		self.selectTFDItem = null;
		self.obTFDList = ko.observableArray([]);
		self.obFileAccept = ko.observable();

		//Events
		self.tfbItemClick = self.tfbItemClick.bind(self);
		self.onSelectOneTFDItem = new TF.Events.Event();
		self.onFileUploadComplete = new TF.Events.Event();
	};


	/**
	 * Initialize the select import definition file modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	SelectImportDefinitionFileViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.$element = $(el);
		self.initData();
	};

	/**
	 * Get essential data for initialization.
	 * @return {Promise} When the process is done.
	 */
	SelectImportDefinitionFileViewModel.prototype.initData = function()
	{
		var self = this;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "ImportedTFDData")).then(function(response)
		{
			if (response && response.Items)
			{
				self.tfdList = response.Items.map(function(item)
				{
					return {
						name: item.Name,
						className: ko.observable(""),
						importFileType: item.ImportFileType
					};
				});
				self.obTFDList(self.tfdList);
			}
		});
	};

	/**
	 * The event of tfd item clicked.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	SelectImportDefinitionFileViewModel.prototype.tfbItemClick = function(viewModel, e)
	{
		if (viewModel.className() !== "")
		{
			return;
		}

		var self = this;
		self.tfdList.forEach(function(item)
		{
			if (item.name === viewModel.name)
			{
				item.className("select");
				self.selectTFDItem = viewModel;
				self.onSelectOneTFDItem.notify();
			}
			else
			{
				item.className("");
			}
		});
	};

	/**
	 * Get the result data.
	 * @return {Object} The result data, has type and file name.
	 */
	SelectImportDefinitionFileViewModel.prototype.apply = function()
	{
		var self = this;
		return Promise.resolve({
			fileName: self.selectTFDItem.name,
			importFileType: self.selectTFDItem.importFileType
		});
	}

	/**
	 * Open the select data file windows modal.
	 * @param {Object} type The file type.
	 * @return {void}
	 */
	SelectImportDefinitionFileViewModel.prototype.selectDataFile = function()
	{
		var self = this;
		self.obFileAccept(".tfd");
		self.$element.find(".input-file").val("");
		self.$element.find(".input-file").trigger("click");
	};

	/**
	 * The event of upload a new file.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} e The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	SelectImportDefinitionFileViewModel.prototype.UploadedFileChangeEvent = function(viewModel, e)
	{
		var self = this, files = e.target.files;
		if (files.length > 0)
		{
			self.UploadedFile(files[0]);
		}
	};

	/**
	 * Upload a new file.
	 * @param {Object} file The file info.
	 * @return {void}
	 */
	SelectImportDefinitionFileViewModel.prototype.UploadedFile = function(file)
	{
		// RW-31723 Attach the file object directly to fileModel (so that sequential processing can directly use it)
		var self = this, fileModel = { FileName: file.name, RawFile: file }, reader = new FileReader();
		reader.fileName = file.name;
		reader.onload = function(event)
		{
			fileModel.FileContent = event.target.result;
			self.onFileUploadComplete.notify(fileModel);
		};
		reader.readAsDataURL(file);
	};

})();

