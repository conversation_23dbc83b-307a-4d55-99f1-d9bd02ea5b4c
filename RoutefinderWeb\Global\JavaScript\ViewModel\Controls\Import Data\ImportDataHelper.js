(function()
{
	createNamespace('TF.Control').ImportDataHelper = ImportDataHelper;

	ImportDataHelper.instance = new ImportDataHelper();

	function ImportDataHelper(viewModel)
	{
		this.viewModel = viewModel;
	}

	ImportDataHelper.prototype.init = function()
	{
		var self = this;
		self.getDBColumns().then(function(result)
		{
			self.tableDBColumns = result;
		});
	}

	ImportDataHelper.prototype.getDBColumns = function()
	{
		var tableStr = '';
		TF.ImportAndMergeData.avaliableDataTypes.map(function(t)
		{
			tableStr += t.name + ',';
		});
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "dataBaseColumns"),
			{
				paramData: {
					"tables": tableStr
				}
			}).then(function(result)
			{
				var mapInfos = result.Items[0];
				return mapInfos;
			});
	}

	ImportDataHelper.prototype.getDBColumnsByEntityName = function(tableName)
	{
		var tableType = this.getTableTypeByName(tableName);
		if (tableType.slice(tableType.length - 1) === 's' && tableType !== 'TripAlias')
		{
			tableType = tableType.substring(0, tableType.length - 1);
			if (tableType.slice(-2) === 'ie')
			{
				tableType = tableType.replace(/.{2}$/, 'y');
			}
		}

		var columnInfo = Enumerable.From(this.tableDBColumns).FirstOrDefault(null, function(c) { return c.EntityName == tableType });
		return columnInfo != null ? columnInfo.ColumnMappings : [];
	}

	ImportDataHelper.prototype.getColumnsByTable = function(tableName, includeTagsField = false)
	{
		var ImportDataType = TF.ImportAndMergeData.ImportDataType;
		const getColumns = () =>
		{
			switch (tableName)
			{
				case 'Address Point':
					return [{ Destination: 'Number', Source: 'AddressNumber', MatchOn: true },
					{ Destination: 'Street Name', Source: 'Street', MatchOn: true },
					{ Destination: 'X Coord', Source: 'XCoord', MatchOn: false },
					{ Destination: 'Y Coord', Source: 'YCoord', MatchOn: false },
					{ Destination: 'Do not update during integration', Source: 'Lock', MatchOn: false }];
				case 'Alternate Site':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Geo_City', Source: 'Geo_City', MatchOn: false },
					{ Destination: 'Geo_County', Source: 'Geo_County', MatchOn: false },
					{ Destination: 'Geo_Street', Source: 'Geo_Street', MatchOn: false },
					{ Destination: 'Geo_Zip', Source: 'Geo_Zip', MatchOn: false },
					{ Destination: 'Mail_City', Source: 'Mail_City', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'Mail_Street1', Source: 'Mail_Street1', MatchOn: false },
					{ Destination: 'Mail_Street2', Source: 'Mail_Street2', MatchOn: false },
					{ Destination: 'Mail_Zip', Source: 'Mail_Zip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode },
					{ Destination: 'XCoord', Source: 'XCoord', MatchOn: false },
					{ Destination: 'YCoord', Source: 'YCoord', MatchOn: false },
					{ Destination: 'Public', Source: 'Public', MatchOn: false },
					{ Destination: 'Phone', Source: 'Phone', MatchOn: false },
					{ Destination: 'Phone_Ext', Source: 'Phone_Ext', MatchOn: false },
					{ Destination: 'GeoConfidence', Source: 'GeoConfidence', MatchOn: false }];
				case 'Contractor':
					return [{ Destination: 'Contractor_ID', Source: 'Contractor_ID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Mail_City', Source: 'Mail_City', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'Mail_Street1', Source: 'Mail_Street1', MatchOn: false },
					{ Destination: 'Mail_Street2', Source: 'Mail_Street2', MatchOn: false },
					{ Destination: 'Mail_Zip', Source: 'Mail_Zip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode }];
				case 'District':
					return [{ Destination: 'District Code', Source: 'District', MatchOn: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'MailCity', Source: 'MailCity', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'Mail_street1', Source: 'MailStreet1', MatchOn: false },
					{ Destination: 'Mail_street2', Source: 'MailStreet2', MatchOn: false },
					{ Destination: 'Mail_Zip', Source: 'MailZip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode }];
				case 'Field Trip':
					let columns = [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Field Trip Stage', Source: 'FieldTripStageID', MatchOn: false },
					{ Destination: 'Classification', Source: 'FieldTripClassificationID', MatchOn: false, LookupTable: ImportDataType.FieldTripClassification },
					{ Destination: 'FieldTripAccount', Source: 'FieldTripAccountID', MatchOn: false, LookupTable: ImportDataType.FieldTripAccount },
					{ Destination: 'Activity', Source: 'FieldTripActivityID', MatchOn: false, LookupTable: ImportDataType.FieldTripActivity },
					{ Destination: 'School', Source: 'School', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'DepartFromSchool', Source: 'DepartFromSchool', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'BillingClassificationID', Source: 'BillingClassificationID', MatchOn: false, LookupTable: ImportDataType.FieldTripBillingClassification },
					{ Destination: 'Department', Source: 'DistrictDepartmentID', MatchOn: false, LookupTable: ImportDataType.$DistrictDepartment },
					{ Destination: 'PublicID', Source: 'PublicID', MatchOn: false },
					{ Destination: 'EstimatedReturnDatetime', Source: 'EstimatedReturnDatetime', MatchOn: false },
					{ Destination: 'Notes', Source: 'Notes', MatchOn: false },
					{ Destination: 'Destination', Source: 'Destination', MatchOn: false },
					{ Destination: 'DestinationStreet', Source: 'DestinationStreet', MatchOn: false },
					{ Destination: 'DestinationCity', Source: 'DestinationCity', MatchOn: false },
					{ Destination: 'DestinationState', Source: 'DestinationState', MatchOn: false },
					{ Destination: 'DestinationZip', Source: 'DestinationZip', MatchOn: false },
					{ Destination: 'DirectionNotes', Source: 'DirectionNotes', MatchOn: false },
					{ Destination: 'DestinationNotes', Source: 'DestinationNotes', MatchOn: false },
					{ Destination: 'DestinationContact', Source: 'DestinationContact', MatchOn: false },
					{ Destination: 'DestinationContactPhone', Source: 'DestinationContactPhone', MatchOn: false },
					{ Destination: 'DepartDatetime', Source: 'DepartDatetime', MatchOn: false },
					{ Destination: 'DepartureNotes', Source: 'DepartureNotes', MatchOn: false },
					{ Destination: 'NumberOfStudents', Source: 'NumberOfStudents', MatchOn: false },
					{ Destination: 'NumberOfAdults', Source: 'NumberOfAdults', MatchOn: false },
					{ Destination: 'NumberOfVehicles', Source: 'NumberOfVehicles', MatchOn: false },
					{ Destination: 'NumberOfWheelchairs', Source: 'NumberOfWheelchairs', MatchOn: false },
					{ Destination: 'EstimatedDistance', Source: 'EstimatedDistance', MatchOn: false },
					{ Destination: 'EstimatedHours', Source: 'EstimatedHours', MatchOn: false },
					{ Destination: 'EstimatedCost', Source: 'EstimatedCost', MatchOn: false },
					{ Destination: 'ShowPublic', Source: 'ShowPublic', MatchOn: false },
					{ Destination: 'PublicNotes', Source: 'PublicNotes', MatchOn: false },
					{ Destination: 'BillingNotes', Source: 'BillingNotes', MatchOn: false },
					{ Destination: 'FuelConsumptionRate', Source: 'FuelConsumptionRate', MatchOn: false },
					{ Destination: 'FixedCost', Source: 'FixedCost', MatchOn: false },
					{ Destination: 'DriverFixedCost', Source: 'DriverFixedCost', MatchOn: false },
					{ Destination: 'VehFixedCost', Source: 'VehFixedCost', MatchOn: false },
					{ Destination: 'MinimumCost', Source: 'MinimumCost', MatchOn: false },
					{ Destination: 'DriverRate', Source: 'DriverRate', MatchOn: false },
					{ Destination: 'DriverOTRate', Source: 'DriverOTRate', MatchOn: false },
					{ Destination: 'AideRate', Source: 'AideRate', MatchOn: false },
					{ Destination: 'AideOTRate', Source: 'AideOTRate', MatchOn: false },
					{ Destination: 'AideFixedCost', Source: 'AideFixedCost', MatchOn: false },
					{ Destination: 'FieldTripDestinationID', Source: 'FieldTripDestinationID', MatchOn: false, LookupTable: ImportDataType.FieldTripDestination },
					{ Destination: 'FieldTripContact', Source: 'FieldTripContact', MatchOn: false },
					{ Destination: 'DestinationContactTitle', Source: 'DestinationContactTitle', MatchOn: false },
					{ Destination: 'DestinationPhoneExt', Source: 'DestinationPhoneExt', MatchOn: false },
					{ Destination: 'DestinationFax', Source: 'DestinationFax', MatchOn: false },
					{ Destination: 'DestinationEmail', Source: 'DestinationEmail', MatchOn: false },
					{ Destination: 'ContactPhone', Source: 'ContactPhone', MatchOn: false },
					{ Destination: 'ContactPhoneExt', Source: 'ContactPhoneExt', MatchOn: false },
					{ Destination: 'ContactEmail', Source: 'ContactEmail', MatchOn: false }];

					if (tf.authManager.hasTripfinderRouting())
					{
						columns.push({ Destination: 'TravelScenario', Source: 'TravelScenarioId', MatchOn: false, LookupTable: ImportDataType.FieldTripTravelScenario });
					}

					return columns;
				case 'Field Trip Account':
					return [{ Destination: 'FieldTripAccountID', Source: 'FieldTripAccountID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Code', Source: 'Code', MatchOn: true },
					{ Destination: 'School', Source: 'School', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'Department', Source: 'DepartmentID', MatchOn: false, LookupTable: ImportDataType.$DistrictDepartment },
					{ Destination: 'Activity', Source: 'FieldTripActivityID', MatchOn: false, LookupTable: ImportDataType.FieldTripActivity },
					{ Destination: 'ActiveFromDate', Source: 'ActiveFromDate', MatchOn: false },
					{ Destination: 'ActiveToDate', Source: 'ActiveToDate', MatchOn: false },
					{ Destination: 'OpeningBalance', Source: 'OpeningBalance', MatchOn: false },
					{ Destination: 'Description', Source: 'Description', MatchOn: false }];
				case 'Field Trip Activity':
					return [{ Destination: 'FieldTripActivityID', Source: 'FieldTripActivityID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Description', Source: 'Description', MatchOn: false }];
				case 'Field Trip Billing Classification':
					return [{ Destination: 'BillingClassificationID', Source: 'BillingClassificationID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Classification', Source: 'Classification', MatchOn: true },
					{ Destination: 'FuelConsumptionRate', Source: 'FuelConsumptionRate', MatchOn: false },
					{ Destination: 'FixedCost', Source: 'FixedCost', MatchOn: false },
					{ Destination: 'AideFixedCost', Source: 'AideFixedCost', MatchOn: false },
					{ Destination: 'DriverFixedCost', Source: 'DriverFixedCost', MatchOn: false },
					{ Destination: 'VehFixedCost', Source: 'VehFixedCost', MatchOn: false },
					{ Destination: 'MinimumCost', Source: 'MinimumCost', MatchOn: false },
					{ Destination: 'DriverRate', Source: 'DriverRate', MatchOn: false },
					{ Destination: 'DriverOTRate', Source: 'DriverOTRate', MatchOn: false },
					{ Destination: 'AideRate', Source: 'AideRate', MatchOn: false },
					{ Destination: 'AideOTRate', Source: 'AideOTRate', MatchOn: false },
					{ Destination: 'UsedForEstimates', Source: 'UsedForEstimates', MatchOn: false }];
				case 'Field Trip Classification':
					return [{ Destination: 'FieldTripClassificationID', Source: 'FieldTripClassificationID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Code', Source: 'Code', MatchOn: true },
					{ Destination: 'Description', Source: 'Description', MatchOn: false }];
				case 'Field Trip Destination':
					return [{ Destination: 'FieldTripDestinationID', Source: 'FieldTripDestinationID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'City', Source: 'City', MatchOn: false },
					{ Destination: 'State', Source: 'State', MatchOn: false },
					{ Destination: 'Zip', Source: 'Zip', MatchOn: false },
					{ Destination: 'Contact', Source: 'Contact', MatchOn: false },
					{ Destination: 'ContactTitle', Source: 'ContactTitle', MatchOn: false },
					{ Destination: 'Phone', Source: 'Phone', MatchOn: false },
					{ Destination: 'Phone_ext', Source: 'PhoneExt', MatchOn: false },
					{ Destination: 'Notes', Source: 'Notes', MatchOn: false },
					{ Destination: 'Street', Source: 'Street', MatchOn: false },
					{ Destination: 'Fax', Source: 'Fax', MatchOn: false },
					{ Destination: 'Email', Source: 'Email', MatchOn: false }];
				case 'Field Trip Equipment':
					return [{ Destination: 'FieldTripEquipmentID', Source: 'FieldTripEquipmentID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Name', Source: 'EquipmentName', MatchOn: true }];
				case 'Field Trip Template':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Field Trip Stage', Source: 'FieldTripStageID', MatchOn: false },
					{ Destination: 'Field Trip Activity', Source: 'FieldTripActivityID', MatchOn: false, LookupTable: ImportDataType.FieldTripActivity },
					{ Destination: 'Field Trip Account', Source: 'FieldTripAccountID', MatchOn: false, LookupTable: ImportDataType.FieldTripAccount },
					{ Destination: 'Department', Source: 'DistrictDepartmentID', MatchOn: false, LookupTable: ImportDataType.DistrictDepartment },
					{ Destination: 'Billing Classification ID', Source: 'BillingClassificationID', MatchOn: false, LookupTable: ImportDataType.FieldTripBillingClassification },
					{ Destination: 'Field Trip Equipment', Source: 'FieldTripEquipmentID', MatchOn: false, LookupTable: ImportDataType.FieldTripEquipment },
					{ Destination: 'PublicID', Source: 'PublicID', MatchOn: false },
					{ Destination: 'DepartFromSchool', Source: 'DepartFromSchool', MatchOn: false },
					{ Destination: 'School', Source: 'School', MatchOn: false },
					{ Destination: 'FieldTripClassificationID', Source: 'FieldTripClassificationID', MatchOn: false, LookupTable: ImportDataType.FieldTripClassification },
					{ Destination: 'Notes', Source: 'Notes', MatchOn: false },
					{ Destination: 'Field Trip Destination', Source: 'FieldTripDestinationID', MatchOn: false, LookupTable: ImportDataType.FieldTripDestination },
					{ Destination: 'Destination', Source: 'Destination', MatchOn: false },
					{ Destination: 'DestinationStreet', Source: 'DestinationStreet', MatchOn: false },
					{ Destination: 'DestinationCity', Source: 'DestinationCity', MatchOn: false },
					{ Destination: 'DestinationState', Source: 'DestinationState', MatchOn: false },
					{ Destination: 'DestinationZip', Source: 'DestinationZip', MatchOn: false },
					{ Destination: 'DirectionNotes', Source: 'DirectionNotes', MatchOn: false },
					{ Destination: 'DestinationNotes', Source: 'DestinationNotes', MatchOn: false },
					{ Destination: 'DestinationContact', Source: 'DestinationContact', MatchOn: false },
					{ Destination: 'DestinationContactPhone', Source: 'DestinationContactPhone', MatchOn: false },
					{ Destination: 'DepartureNotes', Source: 'DepartureNotes', MatchOn: false },
					{ Destination: 'NumberOfStudents', Source: 'NumberOfStudents', MatchOn: false },
					{ Destination: 'NumberOfAdults', Source: 'NumberOfAdults', MatchOn: false },
					{ Destination: 'NumberOfVehicles', Source: 'NumberOfVehicles', MatchOn: false },
					{ Destination: 'NumberOfWheelChairs', Source: 'NumberOfWheelChairs', MatchOn: false },
					{ Destination: 'EstimatedDistance', Source: 'EstimatedDistance', MatchOn: false },
					{ Destination: 'EstimatedHours', Source: 'EstimatedHours', MatchOn: false },
					{ Destination: 'EstimatedCost', Source: 'EstimatedCost', MatchOn: false },
					{ Destination: 'ShowPublic', Source: 'ShowPublic', MatchOn: false },
					{ Destination: 'PublicNotes', Source: 'PublicNotes', MatchOn: false },
					{ Destination: 'BillingNotes', Source: 'BillingNotes', MatchOn: false },
					{ Destination: 'FuelConsumptionRate', Source: 'FuelConsumptionRate', MatchOn: false },
					{ Destination: 'FixedCost', Source: 'FixedCost', MatchOn: false },
					{ Destination: 'MinimumCost', Source: 'MinimumCost', MatchOn: false },
					{ Destination: 'AideFixedCost', Source: 'AideFixedCost', MatchOn: false },
					{ Destination: 'DriverFixedCost', Source: 'DriverFixedCost', MatchOn: false },
					{ Destination: 'VehFixedCost', Source: 'VehFixedCost', MatchOn: false },
					{ Destination: 'DriverRate', Source: 'DriverRate', MatchOn: false },
					{ Destination: 'DriverOTRate', Source: 'DriverOTRate', MatchOn: false },
					{ Destination: 'AideRate', Source: 'AideRate', MatchOn: false },
					{ Destination: 'AideOTRate', Source: 'AideOTRate', MatchOn: false },
					{ Destination: 'PurchaseOrder', Source: 'PurchaseOrder', MatchOn: false },
					{ Destination: 'FieldTripContact', Source: 'FieldTripContact', MatchOn: false },
					{ Destination: 'TemplateStatus', Source: 'TemplateStatus', MatchOn: false },
					{ Destination: 'FieldTripName', Source: 'FieldTripName', MatchOn: false },
					{ Destination: 'InvoiceAmountType', Source: 'InvoiceAmountType', MatchOn: false },
					{ Destination: 'DestinationContactTitle', Source: 'DestinationContactTitle', MatchOn: false },
					{ Destination: 'DestinationPhoneExt', Source: 'DestinationPhoneExt', MatchOn: false },
					{ Destination: 'DestinationFax', Source: 'DestinationFax', MatchOn: false },
					{ Destination: 'DestinationEmail', Source: 'DestinationEmail', MatchOn: false },
					{ Destination: 'ContactPhone', Source: 'ContactPhone', MatchOn: false },
					{ Destination: 'ContactPhoneExt', Source: 'ContactPhoneExt', MatchOn: false },
					{ Destination: 'ContactEmail', Source: 'ContactEmail', MatchOn: false }];
				case 'Field Trip Travel Scenario':
					return [
						{ Destination: 'ID', Source: 'ID', MatchOn: true },
						{ Destination: 'Name', Source: 'Name', MatchOn: true }
					];
				case 'Filter':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'DataType', Source: 'DataType', MatchOn: true },
					{ Destination: 'Where_Clause', Source: 'WhereClause', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'IsForQuickSearch', Source: 'IsForQuickSearch', MatchOn: false },
					{ Destination: 'IsValid', Source: 'IsValid', MatchOn: false }];
				case 'Geo Region':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Geo Region Type', Source: 'GeoRegionTypeID', MatchOn: false, LookupTable: ImportDataType.GeoRegionType },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Geo_City', Source: 'Geo_City', MatchOn: false },
					{ Destination: 'Geo_County', Source: 'Geo_County', MatchOn: false },
					{ Destination: 'Geo_Street', Source: 'Geo_Street', MatchOn: false },
					{ Destination: 'Geo_Zip', Source: 'Geo_Zip', MatchOn: false },
					{ Destination: 'Mail_City', Source: 'Mail_City', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'Mail_Street1', Source: 'Mail_Street1', MatchOn: false },
					{ Destination: 'Mail_Street2', Source: 'Mail_Street2', MatchOn: false },
					{ Destination: 'Mail_Zip', Source: 'Mail_Zip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode },
					{ Destination: 'XCoord', Source: 'XCoord', MatchOn: false },
					{ Destination: 'YCoord', Source: 'YCoord', MatchOn: false },
					{ Destination: 'GeoConfidence', Source: 'GeoConfidence', MatchOn: false },
					{ Destination: 'HotLink', Source: 'HotLink', MatchOn: false },
					{ Destination: 'BoundaryObject', Source: 'BoundaryObject', MatchOn: false }];
				case 'Geo Region Type':
					return [{ Destination: 'GeoRegionTypeID', Source: 'GeoRegionTypeID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Boundary', Source: 'Boundary', MatchOn: false },
					{ Destination: 'Distance', Source: 'Distance', MatchOn: false },
					{ Destination: 'Buffer', Source: 'Buffer', MatchOn: false },
					{ Destination: 'DistanceUnits', Source: 'DistanceUnits', MatchOn: false },
					{ Destination: 'BufferUnits', Source: 'BufferUnits', MatchOn: false },
					{ Destination: 'BoundaryFill', Source: 'BoundaryFill', MatchOn: false },
					{ Destination: 'BoundaryColor', Source: 'BoundaryColor', MatchOn: false },
					{ Destination: 'BoundaryThickness', Source: 'BoundaryThickness', MatchOn: false }];
				case 'Redistrict':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Description', Source: 'Description', MatchOn: false },
					{ Destination: 'FilterName', Source: 'FilterName', MatchOn: false },
					{ Destination: 'FilterSpec', Source: 'FilterSpec', MatchOn: false },
					{ Destination: 'Schools', Source: 'Schools', MatchOn: false },
					{ Destination: 'Id', Source: 'RedistID', MatchOn: false }];
				case 'School':
					return [{ Destination: 'School Code', Source: 'SchoolCode', MatchOn: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: false },
					{ Destination: 'District', Source: 'DistrictID', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Geo_City', Source: 'Geo_City', MatchOn: false },
					{ Destination: 'Geo_County', Source: 'Geo_County', MatchOn: false },
					{ Destination: 'Geo_Street', Source: 'Geo_Street', MatchOn: false },
					{ Destination: 'Geo_Zip', Source: 'Geo_Zip', MatchOn: false },
					{ Destination: 'Mail_City', Source: 'Mail_City', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'Mail_Street1', Source: 'Mail_Street1', MatchOn: false },
					{ Destination: 'Mail_Street2', Source: 'Mail_Street2', MatchOn: false },
					{ Destination: 'Mail_Zip', Source: 'Mail_Zip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode },
					{ Destination: 'Phone', Source: 'Phone', MatchOn: false },
					{ Destination: 'Private', Source: 'Private', MatchOn: false },
					{ Destination: 'XCoord', Source: 'XCoord', MatchOn: false },
					{ Destination: 'YCoord', Source: 'YCoord', MatchOn: false },
					{ Destination: 'Begin_time', Source: 'Begin_time', MatchOn: false },
					{ Destination: 'End_time', Source: 'End_time', MatchOn: false },
					{ Destination: 'ArrivalTime', Source: 'ArrivalTime', MatchOn: false },
					{ Destination: 'DepartTime', Source: 'DepartTime', MatchOn: false },
					{ Destination: 'Feed_Schl', Source: 'FeedSchoolCode', MatchOn: false },
					{ Destination: 'TSchl', Source: 'TSchl', MatchOn: false },
					{ Destination: 'GeoConfidence', Source: 'GeoConfidence', MatchOn: false },
					{ Destination: 'DispGrade', Source: 'DispGrade', MatchOn: false },
					{ Destination: 'GUID', Source: 'GUID', MatchOn: false },
					{ Destination: 'Capacity', Source: 'Capacity', MatchOn: false }];
				case 'Staff':
					return [{ Destination: 'Staff TF GUID', Source: 'StaffGUID', MatchOn: true },
					{ Destination: 'Last Name', Source: 'LastName', MatchOn: true },
					{ Destination: 'First Name', Source: 'FirstName', MatchOn: false },
					{ Destination: 'Contractor', Source: 'ContractorID', MatchOn: false, LookupTable: ImportDataType.Contractor },
					{ Destination: 'MiddleName', Source: 'MiddleName', MatchOn: false },
					{ Destination: 'StaffType', Source: 'StaffType', MatchOn: false },
					{ Destination: 'StaffLocalID', Source: 'StaffLocalID', MatchOn: false },
					{ Destination: 'ActiveFlag', Source: 'ActiveFlag', MatchOn: false },
					{ Destination: 'InactiveDate', Source: 'InactiveDate', MatchOn: false },
					{ Destination: 'Email', Source: 'EMail', MatchOn: false },
					{ Destination: 'MailStreet1', Source: 'MailStreet1', MatchOn: false },
					{ Destination: 'MailStreet2', Source: 'MailStreet2', MatchOn: false },
					{ Destination: 'MailCity', Source: 'MailCity', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'MailCounty', Source: 'MailCounty', MatchOn: false },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'MailZip', Source: 'MailZip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode },
					{ Destination: 'HomePhone', Source: 'HomePhone', MatchOn: false },
					{ Destination: 'WorkPhone', Source: 'WorkPhone', MatchOn: false },
					{ Destination: 'CellPhone', Source: 'CellPhone', MatchOn: false },
					{ Destination: 'LicenseNumber', Source: 'LicenseNumber', MatchOn: false },
					{ Destination: 'LicenseState', Source: 'LicenseState', MatchOn: false },
					{ Destination: 'LicenseExpiration', Source: 'LicenseExpiration', MatchOn: false },
					{ Destination: 'LicenseClass', Source: 'LicenseClass', MatchOn: false },
					{ Destination: 'LicenseEndorsements', Source: 'LicenseEndorsements', MatchOn: false },
					{ Destination: 'LicenseRestrictions', Source: 'LicenseRestrictions', MatchOn: false },
					{ Destination: 'DateOfBirth', Source: 'DateOfBirth', MatchOn: false },
					{ Destination: 'Gender', Source: 'Gender', Alternate: 'Sex', MatchOn: false, LookupTable: ImportDataType.$Gender },
					{ Destination: 'HireDate', Source: 'HireDate', MatchOn: false },
					{ Destination: 'Rate', Source: 'Rate', MatchOn: false },
					{ Destination: 'OTRate', Source: 'OTRate', MatchOn: false },
					{ Destination: 'CardID', Source: 'CardID', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'DeletedFlag', Source: 'DeletedFlag', MatchOn: false },
					{ Destination: 'DeletedDate', Source: 'DeletedDate', MatchOn: false },
					{ Destination: 'EmployeeId', Source: 'EmployeeID', MatchOn: false },
					{ Destination: 'UserID', Source: 'UserID', MatchOn: false },
					{ Destination: 'ApplicationField', Source: 'ApplicationField', MatchOn: false },
					{ Destination: 'FingerPrint', Source: 'FingerPrint', MatchOn: false },
					{ Destination: 'SuperintendentApprov', Source: 'SuperintendentApprov', MatchOn: false },
					{ Destination: 'NewHireOrient', Source: 'NewHireOrient', MatchOn: false },
					{ Destination: 'Abstract', Source: 'Abstract', MatchOn: false },
					{ Destination: 'Interview', Source: 'Interview', MatchOn: false },
					{ Destination: 'DefensiveDriving', Source: 'DefensiveDriving', MatchOn: false },
					{ Destination: 'DrivingTestPractical', Source: 'DrivingTestPractical', MatchOn: false },
					{ Destination: 'DrivingTestWritten', Source: 'DrivingTestWritten', MatchOn: false },
					{ Destination: 'MedicalExam', Source: 'MedicalExam', MatchOn: false },
					{ Destination: 'PPTField', Source: 'PPTField', MatchOn: false },
					{ Destination: 'HepatitisB', Source: 'HepatitisB', MatchOn: false },
					{ Destination: 'Certification', Source: 'Certification', MatchOn: false },
					{ Destination: 'BasicField', Source: 'BasicField', MatchOn: false },
					{ Destination: 'Advanced', Source: 'Advanced', MatchOn: false },
					{ Destination: 'PreService', Source: 'PreService', MatchOn: false },
					{ Destination: 'HandicapPreService', Source: 'HandicapPreService', MatchOn: false },
					{ Destination: 'RefresherPart1', Source: 'RefresherPart1', MatchOn: false },
					{ Destination: 'RefresherPart2', Source: 'RefresherPart2', MatchOn: false },
					{ Destination: 'HandicapRef', Source: 'HandicapRef', MatchOn: false },
					{ Destination: 'District', Source: 'DistrictID', MatchOn: false, LookupTable: ImportDataType.District },
					];
				case 'Student':
					return [{ Destination: 'LocalID', Source: 'Local_ID', MatchOn: true },
					{ Destination: 'Last Name', Source: 'Last_Name', MatchOn: true },
					{ Destination: 'First Name', Source: 'First_Name', MatchOn: false },
					{ Destination: 'School of Attendance', Source: 'School', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'School of Residence', Source: 'ResidSchool', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'Previous Residence School', Source: 'PreRedistSchool', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'Prior School', Source: 'PriorSchool', MatchOn: false, LookupTable: ImportDataType.School },
					{ Destination: 'Distance From School of Attendance', Source: 'DistanceFromSchl', MatchOn: false },
					{ Destination: 'Distance From School of Residence', Source: 'DistanceFromResidSch', MatchOn: false },
					{ Destination: 'Distance From AM Stop', Source: 'DistanceFromAMStop', MatchOn: false },
					{ Destination: 'Distance From PM Stop', Source: 'DistanceFromPmStop', MatchOn: false },
					{ Destination: 'Mi', Source: 'Mi', MatchOn: false },
					{ Destination: 'Aid_Eligible', Source: 'Aid_Eligible', MatchOn: false },
					{ Destination: 'Aide_Req', Source: 'Aide_Req', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Disabled', Source: 'Disabled', MatchOn: false },
					{ Destination: 'District', Source: 'DistrictID', MatchOn: false, LookupTable: ImportDataType.District },
					{ Destination: 'Dob', Source: 'Dob', MatchOn: false },
					{ Destination: 'Geo_City', Source: 'Geo_City', MatchOn: false },
					{ Destination: 'Geo_County', Source: 'Geo_County', MatchOn: false },
					{ Destination: 'Geo_Street', Source: 'Geo_Street', MatchOn: false },
					{ Destination: 'Geo_Zip', Source: 'Geo_Zip', MatchOn: false },
					{ Destination: 'LoadTime', Source: 'LoadTime', MatchOn: false },
					{ Destination: 'LoadTimeManuallyChanged', Source: 'LoadTimeManuallyChanged', MatchOn: false },
					{ Destination: 'CardID', Source: 'CardID', MatchOn: false },
					{ Destination: 'Grade', Source: 'Grade', MatchOn: false, LookupTable: ImportDataType.$Grade },
					{ Destination: 'Mail_City', Source: 'Mail_City', MatchOn: false, LookupTable: ImportDataType.$MailingCity },
					{ Destination: 'Mail_State_Id', Source: 'Mail_State_Id', MatchOn: false, LookupTable: ImportDataType.$MailingState },
					{ Destination: 'Mail_Street1', Source: 'Mail_Street1', MatchOn: false },
					{ Destination: 'Mail_Street2', Source: 'Mail_Street2', MatchOn: false },
					{ Destination: 'Mail_Zip', Source: 'Mail_Zip', MatchOn: false, LookupTable: ImportDataType.$MailingPostalCode },
					{ Destination: 'XCoord', Source: 'XCoord', MatchOn: false },
					{ Destination: 'YCoord', Source: 'YCoord', MatchOn: false },
					{ Destination: 'Transported', Source: 'Transported', MatchOn: false },
					{ Destination: 'Gender', Source: 'Gender', Alternate: 'Sex', MatchOn: false, LookupTable: ImportDataType.$Gender },
					{ Destination: 'GeoConfidence', Source: 'GeoConfidence', MatchOn: false },
					{ Destination: 'GUID', Source: 'GUID', MatchOn: false },
					{ Destination: 'ProhibitCross', Source: 'ProhibitCross', MatchOn: false }];
				case 'Student Disability Codes':
					return [{ Destination: 'Code', Source: 'Code', MatchOn: true },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Description', Source: 'Description', MatchOn: false },
					{ Destination: 'LoadTimePerStudent', Source: 'LoadTimePerStudent', MatchOn: false }];
				case 'Student Ethnic Codes':
					return [{ Destination: 'Code', Source: 'Code', MatchOn: true },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Description', Source: 'Description', MatchOn: false }];
				case 'Trip':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Aide_ID', Source: 'AideID', MatchOn: false, LookupTable: ImportDataType.Staff },
					{ Destination: 'Bus_Aide', Source: 'BusAide', MatchOn: false },
					{ Destination: 'Day', Source: 'Day', MatchOn: false },
					{ Destination: 'Description', Source: 'Description', MatchOn: false },
					{ Destination: 'Disabled', Source: 'Disabled', MatchOn: false },
					{ Destination: 'Distance', Source: 'Distance', MatchOn: false },
					{ Destination: 'Driver_ID', Source: 'DriverID', MatchOn: false, LookupTable: ImportDataType.Staff },
					{ Destination: 'FilterName', Source: 'FilterName', MatchOn: false },
					{ Destination: 'NonDisabled', Source: 'NonDisabled', MatchOn: false },
					{ Destination: 'Schools', Source: 'Schools', MatchOn: false },
					{ Destination: 'Session', Source: 'Session', MatchOn: false },
					{ Destination: 'Vehicle_ID', Source: 'VehicleID', MatchOn: false, LookupTable: ImportDataType.Vehicle },
					{ Destination: 'NumTransport', Source: 'NumTransport', MatchOn: false },
					{ Destination: 'MaxOnBus', Source: 'MaxOnBus', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'TripAlias', Source: 'TripAlias', MatchOn: false, LookupTable: ImportDataType.TripAlias },
					{ Destination: 'IntGratnum1', Source: 'IntGratNum1', MatchOn: false },
					{ Destination: 'IntGratnum2', Source: 'IntGratNum2', MatchOn: false },
					{ Destination: 'IntGratChar1', Source: 'IntGratChar1', MatchOn: false },
					{ Destination: 'IntGratChar2', Source: 'IntGratChar2', MatchOn: false },
					{ Destination: 'IntGratDate1', Source: 'IntGratDate1', MatchOn: false },
					{ Destination: 'IntGratDate2', Source: 'IntGratDate2', MatchOn: false },
					{ Destination: 'Cost', Source: 'Cost', MatchOn: false },
					{ Destination: 'I_Show', Source: 'iShow', MatchOn: false },
					{ Destination: 'I_Name', Source: 'iName', MatchOn: false },
					{ Destination: 'I_Description', Source: 'iDescription', MatchOn: false },
					{ Destination: 'GUID', Source: 'GUID', MatchOn: false },
					{ Destination: 'DHDistance', Source: 'DHDistance', MatchOn: false },
					{ Destination: 'GPSEnabledFlag', Source: 'GPSEnabledFlag', MatchOn: false },
					{ Destination: 'ActivityTrip', Source: 'ActivityTrip', MatchOn: false },
					{ Destination: 'TravelScenarioId', Source: 'TravelScenarioId', MatchOn: false },
					{ Destination: 'FilterSpec', Source: 'FilterSpec', MatchOn: false },
					{ Destination: 'StartTime', Source: 'StartTime', MatchOn: false },
					{ Destination: 'FinishTime', Source: 'FinishTime', MatchOn: false },
					{ Destination: 'Monday', Source: 'Monday', MatchOn: false },
					{ Destination: 'Tuesday', Source: 'Tuesday', MatchOn: false },
					{ Destination: 'Wednesday', Source: 'Wednesday', MatchOn: false },
					{ Destination: 'Thursday', Source: 'Thursday', MatchOn: false },
					{ Destination: 'Friday', Source: 'Friday', MatchOn: false },
					{ Destination: 'Saturday', Source: 'Saturday', MatchOn: false },
					{ Destination: 'Sunday', Source: 'Sunday', MatchOn: false },
					{ Destination: 'StartDate', Source: 'StartDate', MatchOn: false },
					{ Destination: 'EndDate', Source: 'EndDate', MatchOn: false },
					{ Destination: 'SpeedType', Source: 'SpeedType', MatchOn: false },
					{ Destination: 'DefaultSpeed', Source: 'DefaultSpeed', MatchOn: false },
					{ Destination: 'Route', Source: 'RouteId', MatchOn: false }];
				case 'Vehicle':
					return [{ Destination: 'Bus Num', Source: 'Bus_Num', MatchOn: true },
					{ Destination: 'VIN', Source: 'VIN', MatchOn: true },
					{ Destination: 'Capacity', Source: 'Capacity', MatchOn: false },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Contractor', Source: 'ContractorID', MatchOn: false },
					{ Destination: 'WC_Capacity', Source: 'WC_Capacity', MatchOn: false },
					{ Destination: 'RegisExp', Source: 'RegisExp', MatchOn: false },
					{ Destination: 'RegisNum', Source: 'RegisNum', MatchOn: false },
					{ Destination: 'MakeChassis', Source: 'MakeChassis', MatchOn: false },
					{ Destination: 'Model', Source: 'Model', MatchOn: false, LookupTable: ImportDataType.VehicleModel },
					{ Destination: 'YearMade', Source: 'YearMade', MatchOn: false },
					{ Destination: 'MakeBody', Source: 'MakeBody', MatchOn: false, LookupTable: ImportDataType.VehicleMakeOfBody },
					{ Destination: 'InsuranceNum', Source: 'InsuranceNum', MatchOn: false },
					{ Destination: 'InsuranceExp', Source: 'InsuranceExp', MatchOn: false },
					{ Destination: 'StateInspection', Source: 'StateInspection', MatchOn: false },
					{ Destination: 'InspectionExp', Source: 'InspectionExp', MatchOn: false },
					{ Destination: 'FuelType', Source: 'FuelType', MatchOn: false, LookupTable: ImportDataType.VehicleFuelType },
					{ Destination: 'PurchaseDate', Source: 'PurchaseDate', MatchOn: false },
					{ Destination: 'PurchasePrice', Source: 'PurchasePrice', MatchOn: false },
					{ Destination: 'LicensePlate', Source: 'LicensePlate', MatchOn: false },
					{ Destination: 'BodyType', Source: 'BodyType', MatchOn: false, LookupTable: ImportDataType.VehicleBodyType },
					{ Destination: 'Cost', Source: 'Cost', MatchOn: false },
					{ Destination: 'EmmissInsp', Source: 'EmmissInsp', MatchOn: false },
					{ Destination: 'GUID', Source: 'GUID', MatchOn: false },
					{ Destination: 'ExternalId', Source: 'ExternalId', MatchOn: false },
					{ Destination: 'GPSID', Source: 'GPSID', MatchOn: false },
					{ Destination: 'FuelConsumption', Source: 'FuelConsumption', MatchOn: false },
					{ Destination: 'EstLife', Source: 'EstLife', MatchOn: false },
					{ Destination: 'PurchaseOdometer', Source: 'PurchaseOdometer', MatchOn: false },
					{ Destination: 'SalvageOdometer', Source: 'SalvageOdometer', MatchOn: false },
					{ Destination: 'SalvageValue', Source: 'SalvageValue', MatchOn: false },
					{ Destination: 'SalvageDate', Source: 'SalvageDate', MatchOn: false },
					{ Destination: 'Height', Source: 'Height', MatchOn: false },
					{ Destination: 'Length', Source: 'Length', MatchOn: false },
					{ Destination: 'MaxWeight', Source: 'MaxWeight', MatchOn: false },
					{ Destination: 'LongName', Source: 'LongName', MatchOn: false },
					{ Destination: 'AssetID', Source: 'AssetID', MatchOn: false },
					{ Destination: 'InActive', Source: 'InActive', MatchOn: false },
					{ Destination: 'BrakeType', Source: 'BrakeType', MatchOn: false, LookupTable: ImportDataType.VehicleBrakeType },
					{ Destination: 'Width', Source: 'Width', MatchOn: false },
					{ Destination: 'FuelCapacity', Source: 'FuelCapacity', MatchOn: false },
					{ Destination: 'CardID', Source: 'CardID', MatchOn: false }];
				case 'Vehicle Category':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Color', Source: 'Color', MatchOn: false },
					{ Destination: 'MapDisplay', Source: 'MapDisplay', MatchOn: false },
					{ Destination: 'Active', Source: 'Active', MatchOn: false },
					{ Destination: 'TrailLength', Source: 'TrailLength', MatchOn: false },
					{ Destination: 'Symbol', Source: 'Symbol', MatchOn: false }];
				case 'Vehicle Equipment':
					return [{ Destination: 'Code', Source: 'Code', MatchOn: true },
					{ Destination: 'Comments', Source: 'Comments', MatchOn: false },
					{ Destination: 'Description', Source: 'Description', MatchOn: false }];
				case 'Vehicle Body Type':
				case 'Vehicle Brake Type':
				case 'Vehicle Fuel Type':
				case 'Vehicle Make':
				case 'Vehicle Make Of Body':
				case 'Vehicle Model':
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true }];
				case "District Department":
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Description', Source: 'Description', MatchOn: true },
					{ Destination: 'DistrictDepartmentID', Source: 'DistrictDepartmentID', MatchOn: true }];
				case "Mailing City":
					return [{ Destination: 'ID', Source: 'ID', MatchOn: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true }];
				case "Mailing Postal Code":
					return [{ Destination: 'ID', Source: 'ID', MatchOn: true },
					{ Destination: 'Postal', Source: 'Postal', MatchOn: true }];
				case "Trip Alias":
					return [{ Destination: 'TripAliasID', Source: 'TripAliasID', MatchOn: true, ImportDisable: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true }];
				case "Grade":
					return [{ Destination: 'ID', Source: 'ID', MatchOn: true },
					{ Destination: 'Code', Source: 'Code', MatchOn: true }];
				case "Route":
					return [{ Destination: 'Name', Source: 'Name', MatchOn: true },
					{ Destination: 'Notes', Source: 'Notes', MatchOn: false },
					{ Destination: 'StartTime', Source: 'StartTime', MatchOn: false },
					{ Destination: 'EndTime', Source: 'EndTime', MatchOn: false },];
				case "Gender":
					return [{ Destination: 'ID', Source: 'ID', MatchOn: true },
					{ Destination: 'Code', Source: 'Code', MatchOn: true }];
				case "MailingState":
					return [{ Destination: 'ID', Source: 'ID', MatchOn: true },
					{ Destination: 'Name', Source: 'Name', MatchOn: true }];
				case "Contact":
					return [{ Destination: 'FirstName', Source: 'FirstName', MatchOn: true },
					{ Destination: 'LastName', Source: 'LastName', MatchOn: true },
					{ Destination: 'Title', Source: 'Title', MatchOn: false },
					{ Destination: 'Street1', Source: 'Street1', MatchOn: false },
					{ Destination: 'Street2', Source: 'Street2', MatchOn: false },
					{ Destination: 'City', Source: 'City', MatchOn: false },
					{ Destination: 'State', Source: 'State', MatchOn: false },
					{ Destination: 'Zip', Source: 'Zip', MatchOn: false },
					{ Destination: 'Phone', Source: 'Phone', MatchOn: false },
					{ Destination: 'Ext', Source: 'Ext', MatchOn: false },
					{ Destination: 'Email', Source: 'Email', MatchOn: false },
					{ Destination: 'Fax', Source: 'Fax', MatchOn: false },
					{ Destination: 'Mobile', Source: 'Mobile', MatchOn: false },
					{ Destination: 'LocalID', Source: 'LocalID', MatchOn: false },
					{ Destination: 'Notes', Source: 'Notes', MatchOn: false },
					];
				case "Tag":
					return [{ Destination: 'Tag Name', Source: 'Name', MatchOn: true }];
			}
		};

		const columns = getColumns();
		if (tf.authManager.hasTags() && includeTagsField)
		{
			const dataTypesSupportTags = tf.dataTypeHelper.getAvailableDataTypes().filter(x => x.enableTag)
			tableName === 'Address Point' && (tableName = "Parcel Address Point");
			if (dataTypesSupportTags.some(o => o.id === tf.dataTypeHelper.getIdByName(tableName)))
			{
				columns.push({ Destination: 'Tags', Source: 'Tags', MatchOn: false, LookupTable: ImportDataType.$Tag });
			}
		}

		return columns;
	};

	ImportDataHelper.prototype.getPreviewColumnsByTable = function(tableName)
	{
		switch (tableName)
		{
			case 'All':
				return [
					{
						field: "Id",
						type: "integer",
					},
					{
						field: "TableName",
						width: '200px',
						type: "string"
					},
					{
						field: "Record",
						width: '700px',
						type: "string"
					}
				];
			case 'Alternate Site':
				return [
					{
						field: "Id",
						title: "AltSiteID",
						width: '80px',
						type: "integer",
					},
					{
						field: "Name",
						width: '180px',
						type: "string"
					},
					{
						field: "Public",
						width: '100px',
						type: "boolean"
					},
					{
						field: "MailStreet1",
						title: "Mail Street #1",
						width: '150px',
						type: "string"
					},
					{
						field: "MailCity",
						title: "Mail City",
						width: '150px',
						type: "string"
					},
					{
						field: "MailState",
						title: "Mail State/Province",
						width: '150px',
						type: "string"
					},
					{
						field: "MailZip",
						title: "Mail Postal Code",
						width: '150px',
						type: "string"
					},
					{
						field: "Xcoord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}"
					},
					{
						field: "Ycoord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}"
					},
					{
						field: "GeoCity",
						title: "Geo City",
						width: '150px',
						type: "string"
					},
					{
						field: "GeoConfidence",
						title: "Geo Confidence",
						width: '150px',
						type: "integer"
					},
					{
						field: "GeoStreet",
						title: "Geo Street",
						width: '150px',
						type: "string"
					},
					{
						field: "GeoZip",
						title: "Geo Postal Code",
						width: '150px',
						type: "string"
					},
					{
						field: "MailStreet2",
						title: "Mail Street #2",
						width: '150px',
						type: "string"
					},
					{
						field: "GeoCounty",
						title: "Geo County",
						width: '150px',
						type: "string"
					},
					{
						field: "Comments",
						title: "Comments",
						width: '150px',
						type: "string"
					}];
			case 'Contractor':
				return [
					{
						field: "Name",
						width: '180px',
						type: "string",
					},
					{
						field: "MailStreet1",
						title: "Mail Street #1",
						width: '160px',
						type: "string"
					},
					{
						field: "MailCity",
						title: "Mail City",
						width: '150px',
						type: "string",
					},
					{
						field: "MailState",
						title: "Mail State/Province",
						width: '150px',
						type: "string"
					},
					{
						field: "MailZip",
						title: "Mail Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "Comments",
						title: "Notes",
						width: '300px',
						type: "string",
					},
					{
						field: "MailStreet2",
						title: "Mail Street #2",
						width: '160px',
						type: "string",
					},
					{
						field: "Id",
						width: '150px',
						type: "integer",
					}
				];
			case 'District':
				return [
					{
						field: "Id",
						width: '60px',
						type: "integer",
					},
					{
						field: "Name",
						width: '150px',
						type: "string",
					},
					{
						field: "MailCity",
						title: "Mail City",
						width: '150px',
						type: "string",
					},
					{
						field: "MailState",
						title: "Mail State/Province",
						width: '150px',
						type: "string"
					},
					{
						field: "MailStreet1",
						title: "Mail Street #1",
						width: '200px',
						type: "string"
					},
					{
						field: "MailStreet2",
						title: "Mail Street #2",
						width: '200px',
						type: "string"
					},
					{
						field: "MailZip",
						title: "Mail Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "Comments",
						title: "Notes",
						width: '150px',
						type: "string",
					}];
			case 'Field Trip':
				let columns = [
					{
						field: "Id",
						title: "FieldTripID",
						width: '60px',
						type: "integer",
					},
					{
						field: "PublicId",
						title: "ID",
						width: '150px',
						type: "string"
					},
					{
						field: "FieldTripStageName",
						title: "Trip Stage",
						width: '250px',
						type: "string",
						template: "<div style='height:15px;width:15px;margin-right:.5em;border:1px solid rgb(213, 213, 213);background-color:#: tf.fieldTripGridDefinition.gridDefinition().stageFormatter(data.FieldTripStageId)#;float:left'></div><span>#:FieldTripStageName#</span>"
					},
					{
						field: "Name",
						width: '150px',
						type: "string",
					},
					{
						field: "DepartFromSchool",
						title: "Depart From",
						width: '150px',
						type: "string"
					},
					{
						field: "School",
						title: "School",
						width: '150px',
						type: "string",
					},
					{
						field: "SchoolName",
						title: "School Name",
						width: '250px',
						type: "string",
					},
					{
						field: "ReturnDate",
						title: "Return Date",
						width: '180px',
						type: "date",
					},
					{
						field: "FieldTripContact",
						title: "Contact",
						width: '150px',
						type: "string"
					},
					{
						field: "ContactPhone",
						title: "Contact Phone",
						width: '150px',
						type: "string"
					},
					{
						field: "ContactPhoneExt",
						title: "Contact Phone Ext.",
						width: '130px',
						type: "string"
					},
					{
						field: "ContactEmail",
						title: "Contact Email",
						width: '170px',
						type: "string",
					},
					{
						field: "Notes",
						title: "Notes",
						width: '150px',
						type: "string"
					},
					{
						field: "Destination",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationContact",
						title: "Destination Contact",
						width: '160px',
						type: "string"
					},
					{
						field: "DestinationContactPhone",
						title: "Destination Contact Phone",
						width: '190px',
						type: "string"
					},
					{
						field: "DepartDate",
						title: "Departure Date",
						width: '200px',
						type: "date",
					},
					{
						field: "NumberOfStudents",
						title: "# Students",
						width: '150px',
						type: "integer"
					},
					{
						field: "NumberOfAdults",
						title: "# Adults",
						width: '150px',
						type: "integer"
					},
					{
						field: "NumberOfVehicles",
						title: "# Vehicles",
						width: '150px',
						type: "integer"
					},
					{
						field: "NumberOfWheelChairs",
						title: "# Wheelchairs",
						width: '150px',
						type: "integer"
					},
					{
						field: "EstimatedDistance",
						title: "Estimated Distance",
						width: '150px',
						type: "number"
					},
					{
						field: "EstimatedHours",
						title: "Estimated Hours",
						width: '150px',
						type: "number"
					},
					{
						field: "EstimatedCost",
						title: "Estimated Cost",
						width: '150px',
						type: "number"
					},
					{
						field: "DepartTime",
						title: "Departure Time",
						width: '200px',
						type: "time",
					},
					{
						field: "ReturnTime",
						title: "Return Time",
						width: '200px',
						type: "time",
					},
					{
						field: "DestinationStreet",
						title: "Destination Street",
						width: '200px',
						type: "string",
					},
					{
						field: "DestinationCity",
						title: "Destination City",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationState",
						title: "Destination State",
						width: '200px',
						type: "string",
					},
					{
						field: "DestinationZip",
						title: "Destination Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "DirectionNotes",
						title: "Directions",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationNotes",
						title: "Destination Notes",
						width: '180px',
						type: "string",
					},
					{
						field: "DestinationContactTitle",
						title: "Destination Contact Title",
						width: '180px',
						type: "string",
					},
					{
						field: "DestinationPhoneExt",
						title: "Destination Phone Ext.",
						width: '160px',
						type: "string",
					},
					{
						field: "DestinationFax",
						title: "Destination Fax",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationEmail",
						title: "Destination Email",
						width: '170px',
						type: "string",
					},
					{
						field: "DepartureNotes",
						title: "Departure Notes",
						width: '150px',
						type: "string",
					},
					{
						field: "FuelConsumptionRate",
						title: `Rate/${tf.measurementUnitConverter.getShortUnits()}`,
						width: '150px',
						type: "number",
					},
					{
						field: "FixedCost",
						title: "Fixed Costs",
						width: '150px',
						type: "number",
					},
					{
						field: "MinimumCost",
						title: "Minimum Costs",
						width: '150px',
						type: "number",
					},
					{
						field: "DriverRate",
						title: "Driver Rate",
						width: '150px',
						type: "number",
					},
					{
						field: "DriverOtrate",
						title: "Driver OT Rate",
						width: '150px',
						type: "number",
					},
					{
						field: "AideRate",
						title: "Aide Rate",
						width: '150px',
						type: "number",
					},
					{
						field: "AideOtrate",
						title: "Aide OT Rate",
						width: '160px',
						type: "number",
					},
					{
						field: "BillingNotes",
						width: '150px',
						type: "string",
					},
					{
						field: "DistrictDepartmentID",
						width: '150px',
						type: "integer",
					},
					{
						field: "DriverFixedCost",
						width: '150px',
						type: "number",
					},
					{
						field: "FieldTripAccountId",
						width: '150px',
						type: "integer",
					},
					{
						field: "FieldTripActivityId",
						width: '150px',
						type: "integer",
					},
					{
						field: "FieldTripDestinationId",
						width: '150px',
						type: "integer",
					},
					{
						field: "GUID",
						width: '150px',
						type: "string",
					},
					{
						field: "PaymentDate",
						width: '180px',
						type: "date",
					},
					{
						field: "PublicNotes",
						width: '150px',
						type: "string",
					},
					{
						field: "PurchaseOrder",
						width: '150px',
						type: "string",
					},
					{
						field: "ShowPublic",
						width: '150px',
						type: "boolean",
					},
					{
						field: "VehFixedCost",
						width: '150px',
						type: "number",
					}];

				if (tf.authManager.hasTripfinderRouting())
				{
					columns.push({
						field: "TravelScenarioName",
						title: "Travel Scenario",
						width: '150px',
						type: "string"
					});
				}

				return columns;
			case 'Field Trip Account':
				return [
					{
						field: "Code",
						title: "Code",
						type: "string",
						width: '200px',
					},
					{
						field: "Description",
						type: "string",
						width: '200px',
					},
					{
						field: "School",
						type: "string",
						width: '200px',
					},
					{
						field: "DepartmentName",
						title: "Department",
						type: "string",
						width: '200px',
					},
					{
						field: "FieldTripActivityName",
						title: "Activity",
						type: "string",
						width: '200px',
					},
					{
						field: "ActiveFromDate",
						title: "Active From",
						type: "date",
						format: "{0:MM/dd/yyyy}",
						width: '200px',
					},
					{
						field: "ActiveToDate",
						title: "Active To",
						type: "date",
						format: "{0:MM/dd/yyyy}",
						width: '200px',
					}];
			case 'Field Trip Activity':
				return [
					{
						field: "Name",
						title: "Code",
						width: '200px',
						type: "string"
					},
					{
						field: "Description",
						type: "string",
						width: '200px',
					}
				];
			case 'Field Trip Billing Classification':
				return [
					{
						field: "Id",
						width: '150px',
						type: "integer",
					},
					{
						field: "Classification",
						title: "Billing Classification",
						type: "string",
						width: "180px"
					},
					{
						field: "FuelConsumptionRate",
						title: `Rate/${tf.measurementUnitConverter.getShortUnits()}`,
						type: "number",
						width: "180px"
					},
					{
						field: "FixedCost",
						title: "Trip Fixed Cost",
						type: "number",
						width: "180px"
					},
					{
						field: "AideFixedCost",
						title: "Bus Aide Fixed Cost",
						type: "number",
						width: "180px"
					},
					{
						field: "DriverFixedCost",
						title: "Driver Fixed Cost",
						type: "number",
						width: "180px"
					},
					{
						field: "VehFixedCost",
						title: "Vehicle Fixed Cost",
						type: "number",
						width: "180px"
					},
					{
						field: "MinimumCost",
						title: "Minimum Cost",
						type: "number",
						width: "180px"
					},
					{
						field: "DriverRate",
						title: "Driver Rate",
						type: "number",
						width: "180px"
					},
					{
						field: "DriverOTRate",
						title: "Driver Overtime Rate",
						type: "number",
						width: "180px"
					},
					{
						field: "AideRate",
						title: "Aide Rate",
						type: "number",
						width: "180px"
					},
					{
						field: "AideOTRate",
						title: "Aide Overtime Rate",
						type: "number",
						width: "180px"
					}
				];
			case 'Field Trip Classification':
				return [
					{
						field: "Id",
						width: '150px',
						type: "integer",
					},
					{
						field: "Code",
						title: "Code",
						type: "string",
						width: '200px'
					},
					{
						field: "Description",
						type: "string",
						width: '200px',
					}
				];
			case 'Field Trip Destination':
				return [
					{
						field: "Name",
						title: "Destination",
						type: "string",
						width: '200px',
					},
					{
						field: "City",
						type: "string",
						width: '200px',
					},
					{
						field: "Street",
						type: "string",
						width: '200px',
					},
					{
						field: "State",
						type: "string",
						width: '200px',
					},
					{
						field: "Zip",
						title: "Postal Code",
						type: "string",
						width: '200px',
					},
					{
						field: "Notes",
						type: "string",
						width: '200px',
					},
					{
						field: "Contact",
						title: "Contact",
						type: "string",
						width: '200px',
					},
					{
						field: "ContactTitle",
						title: "Title",
						type: "string",
						width: '200px',
					},
					{
						field: "Email",
						defaultValue: "",
						type: "string",
						width: '200px',
					},
					{
						field: "Phone",
						type: "string",
						width: '200px',
					},
					{
						field: "PhoneExt",
						title: "Phone Ext.",
						type: "string",
						width: '200px',
					},
					{
						field: "Fax",
						type: "string",
						width: '200px',
					}
				];
			case 'Field Trip Equipment':
				return [{
					field: "Id",
					width: '150px',
					type: "integer",
				},
				{
					field: "EquipmentName",
					title: "Name",
					type: "string",
					width: '200px',
				}];
			case 'Field Trip Template':
				return [
					{
						field: "PublicId",
						title: "ID",
						width: '150px',
						type: "string"
					},
					{
						field: "FieldTripStageId",
						title: "Field Trip Stage Id",
						width: '250px',
						type: "string",
						template: "<div style='height:15px;width:15px;margin-right:.5em;border:1px solid rgb(213, 213, 213);background-color:#: tf.fieldTripTemplatesGridDefinition.gridDefinition().stageFormatter(data.FieldTripStageId)#;float:left'></div>"
					},
					{
						field: "Name",
						title: "Name",
						width: '150px',
						type: "string",
					},
					{
						field: "DepartFromSchool",
						title: "Depart From",
						width: '150px',
						type: "string"
					},
					{
						field: "School",
						title: "School",
						width: '150px',
						type: "string",
					},
					{
						field: "FieldTripContact",
						title: "Contact",
						width: '150px',
						type: "string"
					},
					{
						field: "ContactPhone",
						title: "Contact Phone",
						width: '150px',
						type: "string"
					},
					{
						field: "ContactPhoneExt",
						title: "Contact Phone Ext.",
						width: '130px',
						type: "string"
					},
					{
						field: "ContactEmail",
						title: "Contact Email",
						width: '170px',
						type: "string",
					},
					{
						field: "Notes",
						title: "Notes",
						width: '150px',
						type: "string"
					},
					{
						field: "Destination",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationContact",
						title: "Destination Contact",
						width: '160px',
						type: "string"
					},
					{
						field: "DestinationContactPhone",
						title: "Destination Contact Phone",
						width: '190px',
						type: "string"
					},
					{
						field: "NumberOfStudents",
						title: "# Students",
						width: '150px',
						type: "integer"
					},
					{
						field: "NumberOfAdults",
						title: "# Adults",
						width: '150px',
						type: "integer"
					},
					{
						field: "NumberOfVehicles",
						title: "# Vehicles",
						width: '150px',
						type: "integer"
					},
					{
						field: "NumberOfWheelChairs",
						title: "# Wheelchairs",
						width: '150px',
						type: "integer"
					},
					{
						field: "EstimatedDistance",
						title: "Estimated Distance",
						width: '150px',
						type: "number"
					},
					{
						field: "EstimatedHours",
						title: "Estimated Hours",
						width: '150px',
						type: "number"
					},
					{
						field: "EstimatedCost",
						title: "Estimated Cost",
						width: '150px',
						type: "number"
					},
					{
						field: "DestinationStreet",
						title: "Destination Street",
						width: '130px',
						type: "string",
					},
					{
						field: "DestinationCity",
						title: "Destination City",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationState",
						title: "Destination State",
						width: '130px',
						type: "string",
					},
					{
						field: "DestinationZip",
						title: "Destination Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "DirectionNotes",
						title: "Directions",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationNotes",
						title: "Destination Notes",
						width: '180px',
						type: "string",
					},
					{
						field: "DestinationContactTitle",
						title: "Destination Contact Title",
						width: '180px',
						type: "string",
					},
					{
						field: "DestinationPhoneExt",
						title: "Destination Phone Ext.",
						width: '160px',
						type: "string",
					},
					{
						field: "DestinationFax",
						title: "Destination Fax",
						width: '150px',
						type: "string",
					},
					{
						field: "DestinationEmail",
						title: "Destination Email",
						width: '170px',
						type: "string",
					},
					{
						field: "DepartureNotes",
						title: "Departure Notes",
						width: '150px',
						type: "string",
					},

					{
						field: "FuelConsumptionRate",
						title: `Rate/${tf.measurementUnitConverter.getShortUnits()}`,
						width: '150px',
						type: "number",
					},
					{
						field: "FixedCost",
						title: "Fixed Costs",
						width: '150px',
						type: "number",
					},
					{
						field: "MinimumCost",
						title: "Minimum Costs",
						width: '150px',
						type: "number",
					},
					{
						field: "DriverRate",
						title: "Driver Rate",
						width: '150px',
						type: "number",
					},
					{
						field: "DriverOtrate",
						title: "Driver OT Rate",
						width: '150px',
						type: "number",
					},
					{
						field: "AideRate",
						title: "Aide Rate",
						width: '150px',
						type: "number",
					},
					{
						field: "AideOtrate",
						title: "Aide OT Rate",
						width: '160px',
						type: "number",
					}
				];
			case 'Filter':
				return [
					{
						field: "Id",
						type: "integer",
						width: '50px',
					},
					{
						field: "Name",
						width: '200px',
						type: "string"
					},
					{
						field: "Comments",
						width: '200px',
						type: "string"
					},
					{
						field: "DataTypeID",
						width: '80px',
						type: "integer"
					},
					{
						field: "IsForQuickSearch",
						width: '80px',
						type: "boolean"
					},
					{
						field: "WhereClause",
						width: '200px',
						type: "string"
					}
				];
			case 'Geo Region':
				return [
					{
						field: "Name",
						width: '250px',
						type: "string",
					},
					{
						field: "HotLink",
						title: "Hotlink",
						width: '640px',
						type: "string"
					},
					{
						field: "Comments",
						title: "Notes",
						width: '150px',
						type: "string"
					},
					{
						field: "Georegiontype",
						title: "Geo Region Type",
						width: '140px',
						type: "string",
					},
					{
						field: "Geo_City",
						title: "Geo City",
						width: '150px',
						type: "string",
					},
					{
						field: "Geo_County",
						title: "Map Set",
						width: '150px',
						type: "string",
					},
					{
						field: "Geo_Street",
						title: "Geo Street",
						width: '200px',
						type: "string"
					},
					{
						field: "Geo_Zip",
						title: "Geo Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "GeoConfidence",
						title: "Geo Confidence",
						width: '150px',
						type: "string",
					},
					{
						field: "MailCity",
						title: "Mail City",
						width: '150px',
						type: "string",
					},
					{
						field: "MailState",
						title: "Mail State/Province",
						width: '150px',
						type: "string",
					},
					{
						field: "MailStreet1",
						title: "Mail Street #1",
						width: '200px',
						type: "string",
					},
					{
						field: "MailStreet2",
						title: "Mail Street #2",
						width: '200px',
						type: "string",
					},
					{
						field: "MailZip",
						title: "Mail Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "Xcoord",
						title: "X Coord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}",
					},
					{
						field: "Ycoord",
						title: "Y Coord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}",
					},
					{
						field: "Id",
						width: '150px',
						type: "integer",
					},
					{
						field: "GeoRegionTypeId",
						width: '150px',
						type: "integer",
					}];
			case 'Geo Region Type':
				return [
					{
						field: "Name",
						width: '200px',
						type: "string",
					},
					{
						field: "Boundary",
						type: "string",
						width: '200px',
					},
					{
						field: "Distance",
						type: "number",
						width: '200px',
					},
					{
						field: "DistanceUnits",
						title: "Distance Units",
						type: "string",
						width: '200px',
					},
					{
						field: "Buffer",
						type: "number",
						width: '200px',
					},
					{
						field: "BufferUnits",
						title: "Buffer Units",
						type: "string",
						width: '200px',
					},
					{
						field: "BoundaryThickness",
						title: "Boundary Thickness",
						type: "integer",
						width: '200px',
					},
					{
						field: "BoundaryColor",
						title: "Boundary Color",
						type: "color",
						template: "<div class='tf-grid-color-field' style='background-color:\\##= BoundaryColor #'></div>",
						width: '200px',
					},
					{
						field: "BoundaryFill",
						title: "Boundary Fill",
						type: "string",
						width: '200px',
					}
				];
			case 'Redistrict':
				return [
					{
						field: "Id",
						width: '260px',
						type: "integer",
					},
					{
						field: "Name",
						width: '150px',
						type: "string",
					},
					{
						field: "Description",
						width: '150px',
						type: "string",
					},
					{
						field: "FilterName",
						width: '150px',
						type: "string",
					},
					{
						field: "FilterSpec",
						width: '150px',
						type: "string"
					},
					{
						field: "Schools",
						width: '180px',
						type: "string"
					}];
			case 'School':
				return [
					{
						field: "SchoolCode",
						title: "School Code",
						width: '150px',
						type: "string",
					},
					{
						field: "Name",
						width: '290px',
						type: "string"
					},
					{
						field: "ArrivalTime",
						title: "Arrival Time",
						width: '150px',
						type: "time"
					},
					{
						field: "DepartTime",
						title: "Departure Time",
						width: '150px',
						type: "time"
					},
					{
						field: "MailStreet1",
						title: "Mail Street #1",
						width: '200px',
						type: "string"
					},
					{
						field: "MailCity",
						title: "Mail City",
						width: '150px',
						type: "string",
					},
					{
						field: "MailState",
						title: "Mail State/Province",
						width: '150px',
						type: "string"
					},
					{
						field: "MailZip",
						title: "Mail Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "StudentCount",
						title: "Student Count",
						width: '150px',
						type: "integer"
					},
					{
						field: "Xcoord",
						title: "X Coord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}"
					},
					{
						field: "Ycoord",
						title: "Y Coord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}"
					},
					{
						field: "Tschl",
						title: "Allows Transfers",
						width: '150px',
						type: "boolean",
					},
					{
						field: "BeginTime",
						title: "Begin Time",
						width: '150px',
						type: "time",
					},
					{
						field: "District",
						width: '150px',
						type: "string",
					},
					{
						field: "EndTime",
						title: "End Time",
						width: '150px',
						type: "time",
					},
					{
						field: "FeedSchl",
						title: "Feed School",
						width: '150px',
						type: "string",
					},
					{
						field: "GeoCity",
						title: "Geo City",
						width: '150px',
						type: "string",
					},
					{
						field: "GeoStreet",
						title: "Geo Street",
						width: '200px',
						type: "string",
					},
					{
						field: "GeoZip",
						title: "Geo Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "GradeRange",
						title: "Grade Range",
						width: '160px',
						type: "string",
					},
					{
						field: "Guid",
						title: "GUID",
						width: '150px',
						type: "string",
					},
					{
						field: "MailStreet2",
						title: "Mail Street #2",
						width: '200px',
						type: "string",
					},
					{
						field: "GeoCounty",
						title: "Geo County",
						width: '150px',
						type: "string",
					},
					{
						field: "Comments",
						title: "Notes",
						width: '150px',
						type: "string",
					},
					{
						field: "Phone",
						title: "Phone",
						width: '150px',
						type: "string",
					},
					{
						field: "Private",
						title: "Private School",
						width: '150px',
						type: "boolean",
					},
					{
						field: "Capacity",
						title: "Student Capacity",
						width: '130px',
						type: "integer",
					},
					{
						field: "DispGrade",
						width: '150px',
						type: "string",
					},
					{
						field: "geoConfidence",
						width: '150px',
						type: "integer",
					},
					{
						field: "Id",
						width: '150px',
						type: "integer",
					}];
			case 'Staff':
				return [{
					field: "Id",
					width: '150px',
					type: "integer",
				},
				{
					field: "LastName",
					title: "Last Name",
					width: '150px',
					type: "string",
				},
				{
					field: "FirstName",
					title: "First Name",
					width: '150px',
					type: "string",
				},
				{
					field: "StaffLocalId",
					title: "Local ID",
					width: '150px',
					type: "string"
				},
				{
					field: "DateOfBirth",
					title: "Date of Birth",
					width: '180px',
					type: "date"
				},
				{
					field: "Gender",
					width: '150px',
					type: "string",
				},
				{
					field: "HireDate",
					title: "Hire Date",
					width: '180px',
					type: "date"
				},
				{
					field: "ActiveFlag",
					title: "Active",
					width: '150px',
					type: "boolean"
				},
				{
					field: "ContractorName",
					title: "Contractor Name",
					width: '150px',
					type: "string",
				},
				{
					field: "MailStreet1",
					title: "Mail Street #1",
					width: '200px',
					type: "string"
				},
				{
					field: "MailStreet2",
					title: "Mail Street #2",
					width: '200px',
					type: "string"
				},
				{
					field: "MailCity",
					title: "Mail City",
					width: '150px',
					type: "string",
				},
				{
					field: "MailCounty",
					title: "Mail County",
					width: '150px',
					type: "string"
				},
				{
					field: "MailState",
					title: "Mail " + tf.localization.AreaName,
					width: '150px',
					type: "string"
				},
				{
					field: "MailZip",
					title: "Mail " + tf.localization.Postal,
					width: '150px',
					type: "string",
				},
				{
					field: "HomePhone",
					title: "Home Phone",
					width: '150px',
					type: "string"
				},
				{
					field: "CellPhone",
					title: "Cell Phone",
					width: '150px',
					type: "string"
				},
				{
					field: "LicenseNumber",
					title: "License #",
					width: '150px',
					type: "string"
				},
				{
					field: "LicenseState",
					title: "License State",
					width: '150px',
					type: "string"
				},
				{
					field: "LicenseExpiration",
					title: "License Expiration",
					width: '180px',
					type: "date"
				},
				{
					field: "LicenseClass",
					title: "License Class",
					width: '150px',
					type: "string"
				},
				{
					field: "LicenseEndorsements",
					title: "License Endorsements",
					width: '150px',
					type: "string"
				},
				{
					field: "LicenseRestrictions",
					title: "License Restrictions",
					width: '150px',
					type: "string"
				},
				{
					field: "EmployeeId",
					title: "Employee ID",
					width: '150px',
					type: "string"
				},
				{
					field: "MiddleName",
					title: "MI",
					width: '150px',
					type: "string",
				},
				{
					field: "FullName",
					title: "Name",
					width: '150px',
					type: "string",
				},
				{
					field: "InactiveDate",
					title: "Date Inactive",
					width: '180px',
					type: "date",
				},

				{
					field: "Email",
					title: "Email",
					width: '150px',
					type: "string",
				},
				{
					field: "WorkPhone",
					title: "Work Phone",
					width: '150px',
					type: "boolean",
				},
				{
					field: "Rate",
					width: '150px',
					type: "number",
				},
				{
					field: "Otrate",
					title: "OT Rate",
					width: '150px',
					type: "number",
				},
				{
					field: "Comments",
					title: "Notes",
					width: '150px',
					type: "string",
				},
				{
					field: "StaffGuid",
					title: "GUID",
					width: '150px',
					type: "string",
				},
				{
					field: "ContractorID",
					width: '150px',
					type: "integer",
				},
				{
					field: "DeletedDate",
					width: '180px',
					type: "date",
				},
				{
					field: "DeletedFlag",
					width: '150px',
					type: "boolean",
				}
				];
			case 'Student':
				return [
					{
						field: "LocalId",
						title: "Local ID",
						width: '150px',
						type: "string"
					},
					{
						field: "LastName",
						title: "Last Name",
						width: '150px',
						type: "string"
					},
					{
						field: "FirstName",
						title: "First Name",
						width: '150px',
						type: "string"
					},
					{
						field: "SchoolName",
						title: "School Name",
						width: '150px',
						type: "string",
					},
					{
						field: "School",
						title: "School",
						width: '150px',
						type: "string"
					},
					{
						field: "LoadTime",
						title: "Load Time",
						width: '100px',
						type: "integer"
					},
					{
						field: "MailStreet1",
						title: "Mail Street #1",
						width: '150px',
						type: "string"
					},
					{
						field: "MailCity",
						title: "Mail City",
						width: '150px',
						type: "string",
					},
					{
						field: "MailState",
						title: "Mail State/Province ",
						width: '150px',
						type: "string"
					},
					{
						field: "MailZip",
						title: "Mail Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "DistanceFromAMStop",
						title: "AM Distance From Stop",
						width: '150px',
						type: "number",
					},
					{
						field: "AideReq",
						title: "Bus Aide Required",
						width: '150px',
						type: "boolean",
					},
					{
						field: "TagId",
						title: "Card ID",
						width: '150px',
						type: "string",
					},
					{
						field: "Cohort",
						title: "Cohort",
						width: '150px',
						type: "string",
					},
					{
						field: "Dob",
						title: "Date of Birth",
						width: '150px',
						type: "date",
					},
					{
						field: "Disabled",
						title: "Disabled",
						width: '150px',
						type: "boolean",
					},
					{
						field: "DistanceFromSchl",
						title: "Distance From School of Attendance",
						width: '150px',
						type: "number",
					},
					{
						field: "DistanceFromResidSch",
						title: "Distance From School of Residence",
						width: '150px',
						type: "number",
					},
					{
						field: "District",
						title: "District",
						width: '150px',
						type: "string",
					},
					{
						field: "AidEligible",
						title: "Eligible for Aid",
						width: '150px',
						type: "boolean",
					},
					{
						field: "Transported",
						title: "Eligible for Transport",
						width: '150px',
						type: "boolean",
					},
					{
						field: "EntryDate",
						title: "Entry Date",
						width: '150px',
						type: "date",
						hidden: true
					},
					{
						field: "EthnicCodes",
						title: "Ethnic Codes",
						width: '150px',
						type: "string",
					},
					{
						field: "Gender",
						title: "Gender",
						width: '150px',
						type: "string",
					},
					{
						field: "GeoCity",
						title: "Geo City",
						width: '150px',
						type: "string",
					},
					{
						field: "GeoConfidence",
						title: "Geo Confidence",
						width: '150px',
						type: "integer",
					},
					{
						field: "GeoStreet",
						title: "Geo Street",
						DBName: "geo_street",
						width: '150px',
						type: "string",
						hidden: true
					},
					{
						field: "GeoStreetName",
						title: "Geo Street Name",
						width: '150px',
						type: "string",
					},
					{
						field: "GeoZip",
						title: "Geo Postal Code",
						width: '150px',
						type: "string",
					},
					{
						field: "Geocoded",
						title: "Geocoded",
						width: '150px',
						type: "boolean",
					},
					{
						field: "Grade",
						title: "Grade",
						width: '150px',
						type: "string",
					},
					{
						field: "Guid",
						title: "GUID",
						width: '150px',
						type: "string",
					},
					{
						field: "InActive",
						title: "Inactive",
						width: '150px',
						type: "boolean",
					},
					{
						field: "IntGratDate1",
						title: "Integration Date",
						width: '150px',
						type: "date",
					},
					{
						field: "LoadTimeManuallyChanged",
						title: "Load Time Adjusted",
						width: '150px',
						type: "boolean",
					},
					{
						field: "MailStreet2",
						title: "Mail Street #2",
						DBName: "mail_Street2",
						width: '150px',
						type: "string",
						hidden: true
					},
					{
						field: "GeoCounty",
						title: "Map Set",
						width: '150px',
						type: "string",
					},
					{
						field: "Mi",
						title: "Middle Initial",
						width: '150px',
						type: "string",
					},
					{
						field: "Comments",
						title: "Notes",
						width: '150px',
						type: "string",
					},
					{
						field: "DistanceFromPmStop",
						title: "PM Distance From Stop",
						width: '150px',
						type: "number",
					},
					{
						field: "PreRedistSchool",
						title: "PreRedistricting",
						width: '260px',
						type: "string",
					},
					{
						field: "Priorschool",
						title: "Prior School",
						width: '190px',
						type: "string",
					},
					{
						field: "ResidSchool",
						title: "School of Residence Code",
						width: '150px',
						type: "string",
					},
					{
						field: "Xcoord",
						title: "X Coord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}"
					},
					{
						field: "Ycoord",
						title: "Y Coord",
						width: '150px',
						type: "number",
						Precision: 6,
						format: "{0:0.000000}"
					},
					{
						field: "IntGratChar1",
						width: '150px',
						type: "string",
					},
					{
						field: "IntGratChar2",
						width: '150px',
						type: "string",
					},
					{
						field: "IntGratDate2",
						width: '150px',
						type: "date",
					},
					{
						field: "IntGratNum1",
						width: '150px',
						type: "number",
					},
					{
						field: "IntGratNum2",
						width: '150px',
						type: "number",
					},
					{
						field: "Id",
						DBName: "stud_id",
						width: '150px',
						type: "integer",
					}
				];
			case 'Student Disability Codes':
				return [
					{
						field: "Code",
						title: "Code",
						width: '150px',
						type: "string"
					},
					{
						field: "Comments",
						title: "Comments",
						width: '150px',
						type: "string"
					},
					{
						field: "LoadTimePerStudent",
						title: "LoadTimePerStudent",
						width: '150px',
						type: "int"
					},
					{
						field: "Description",
						title: "Description",
						width: '150px',
						type: "string"
					}
				];
			case 'Student Ethnic Codes':
				return [
					{
						field: "Code",
						type: "string",
					},
					{
						field: "Description",
						width: '200px',
						type: "string"
					},
					{
						field: "Comments",
						width: '300px',
						type: "string"
					}
				];
			// case 'Student Requirement':
			// 	return [
			// 		{
			// 			field: "Id",
			// 			type: "integer",
			// 			width: '150px',
			// 		},
			// 		{
			// 			field: "SchoolCode",
			// 			width: '100px',
			// 			type: "string"
			// 		},
			// 		{
			// 			field: "SessionID",
			// 			width: '80px',
			// 			type: "integer"
			// 		},
			// 		{
			// 			field: "StudentId",
			// 			width: '80px',
			// 			type: "integer"
			// 		},
			// 		{
			// 			field: "LocationID",
			// 			width: '80px',
			// 			type: "integer"
			// 		},
			// 		{
			// 			field: "Type",
			// 			width: '80px',
			// 			type: "integer"
			// 		},
			// 		{
			// 			field: "Monday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "Tuesday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "Wednesday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "Thursday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "Friday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "Saturday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "Sunday",
			// 			width: '80px',
			// 			type: "boolean"
			// 		},
			// 		{
			// 			field: "StartDate",
			// 			width: '180px',
			// 			type: "date"
			// 		},
			// 		{
			// 			field: "EndDate",
			// 			width: '180px',
			// 			type: "date"
			// 		}
			// 	];
			// case 'Student Location':
			// 	return [
			// 		{
			// 			field: "Id",
			// 			type: "integer",
			// 			width: '150px',
			// 		},
			// 		{
			// 			field: "StudentId",
			// 			width: '100px',
			// 			type: "integer"
			// 		},
			// 		{
			// 			field: "AltSiteID",
			// 			width: '80px',
			// 			type: "integer"
			// 		},
			// 		{
			// 			field: "HomeXCoord",
			// 			width: '100px',
			// 			type: "number",
			// 			Precision: 6,
			// 			format: "{0:0.000000}"
			// 		},
			// 		{
			// 			field: "HomeYCoord",
			// 			width: '100px',
			// 			type: "number",
			// 			Precision: 6,
			// 			format: "{0:0.000000}"
			// 		},
			// 		{
			// 			field: "SchoolCode",
			// 			width: '80px',
			// 			type: "string"
			// 		},
			// 		{
			// 			field: "ProhibitCross",
			// 			width: '80px',
			// 			type: "boolean"
			// 		}
			// 	];
			case 'Trip':
				return [
					{
						field: "Name",
						title: "Name",
						width: '200px',
						type: "string",
					},
					{
						field: "BusAide",
						title: "Bus Aide",
						width: '150px',
						type: "boolean",
					},
					{
						field: "BusAideName",
						title: "Bus Aide Name",
						width: '150px',
						type: "string",
					},
					{
						field: "Dhdistance",
						title: "Dhdistance",
						width: '150px',
						type: "number",
						format: "{0:0.00}"
					},
					{
						field: "Description",
						title: "Description",
						width: '150px',
						type: "string"
					},
					{
						field: "Disabled",
						title: "Disabled Students",
						width: '130px',
						type: "boolean",
					},
					{
						field: "Distance",
						title: "Distance",
						width: '150px',
						type: "number",
						format: "{0:0.00}"
					},
					{
						field: "DriverName",
						title: "Driver",
						width: '160px',
						type: "string",
					},
					{
						field: "FilterName",
						title: "Filter Name",
						width: '150px',
						type: "string",
					},
					{
						field: "FilterSpec",
						title: "Filter Spec",
						width: '150px',
						type: "string",
					},
					{
						field: "NonDisabled",
						title: "Non-Disabled Students",
						width: '165px',
						type: "boolean",
					},
					{
						field: "Schools",
						width: '150px',
						type: "string",
						template: "#: tf.tripGridDefinition.gridDefinition().formatter(Schools)#",
					},
					{
						field: "VehicleName",
						title: "Vehicle Name",
						width: '150px',
						type: "string",
					},
					{
						field: "HomeSchl",
						title: "Home To School",
						width: '150px',
						type: "boolean",
					},
					{
						field: "HomeTrans",
						title: "Home To Transfer",
						width: '150px',
						type: "boolean",
					},
					{
						field: "Shuttle",
						title: "Transfer",
						width: '150px',
						type: "boolean",
					},
					{
						field: "TravelScenarioName",
						title: "Travel Scenario",
						width: '150px',
						type: "string"
					},
					{
						field: "Comments",
						title: "Notes",
						width: '150px',
						type: "string"
					},
					{
						field: "NumTransport",
						title: "Number Assigned",
						width: '130px',
						type: "integer"
					},
					{
						field: "MaxOnBus",
						title: "Max On Bus",
						width: '150px',
						type: "integer",
					},
					{
						field: "StartTime",
						title: "Start Time",
						width: '150px',
						type: "time"
					},
					{
						field: "FinishTime",
						title: "Finish Time",
						width: '150px',
						type: "time"
					},
					{
						field: "TripAlias",
						title: "Trip Alias",
						width: '150px',
						type: "string",
					},
					{
						field: "Cost",
						title: "Cost",
						width: '150px',
						type: "number",
					},
					{
						field: "IShow",
						title: "Infofinder i Visible",
						width: '130px',
						type: "boolean",
					},
					{
						field: "IName",
						title: "Infofinder i Display Name",
						width: '135px',
						type: "string",
					},
					{
						field: "IDescription",
						title: "Infofinder i Description",
						width: '170px',
						type: "string",
					},
					{
						field: "Guid",
						title: "GUID",
						width: '285px',
						type: "string",
					},
					{
						field: "Duration",
						width: '150px',
						type: "integer",
					},
					{
						field: "ActivityTrip",
						title: "Activity Trip",
						width: '150px',
						type: "boolean",
					},
					{
						field: "GPSEnabledFlag",
						title: "GPS Enabled Flag",
						width: '140px',
						type: "boolean",
					},
					{
						field: "AideId",
						DBName: "Aide_ID",
						width: '150px',
						type: "integer",
					},
					{
						field: "Day",
						width: '150px',
						type: "integer",
					},
					{
						field: "DriverId",
						DBName: "Driver_ID",
						width: '150px',
						type: "integer",
					},
					{
						field: "IntGratChar1",
						width: '150px',
						type: "string",
					},
					{
						field: "IntGratChar2",
						width: '150px',
						type: "string",
					},
					{
						field: "IntGratDate1",
						width: '150px',
						type: "date",
					},
					{
						field: "IntGratDate2",
						width: '150px',
						type: "date",
					},
					{
						field: "IntGratNum1",
						width: '150px',
						type: "number",
					},
					{
						field: "IntGratNum2",
						width: '150px',
						type: "number",
					},
					{
						field: "Id",
						width: '150px',
						type: "integer",
					},
					{
						field: "VehicleId",
						width: '150px',
						type: "integer",
					}];
			case 'Vehicle':
				return [
					{
						field: "BusNum",
						title: "Vehicle",
						width: '150px',
						type: "string",
					},
					{
						field: "Capacity",
						width: '150px',
						type: "integer"
					},
					{
						field: "WcCapacity",
						title: "W/C Capacity",
						width: '150px',
						type: "integer"
					},
					{
						field: "Vin",
						title: "VIN",
						width: '150px',
						type: "string"
					},
					{
						field: "LicensePlate",
						title: "License Plate",
						width: '150px',
						type: "string"
					},
					{
						field: "YearMade",
						title: "Year Made",
						width: '150px',
						type: "number",
						format: "{0:####}"
					},
					{
						field: "MakeBody",
						title: "Make Body",
						width: '150px',
						type: "string",
					},
					{
						field: "MakeChassis",
						title: "Make Chassis",
						width: '160px',
						type: "string",
					},
					{
						field: "Model",
						width: '150px',
						type: "string",
					},
					{
						field: "BodyType",
						title: "Body Type",
						width: '150px',
						type: "string",
					},
					{
						field: "PurchaseDate",
						title: "Purchase Date",
						width: '160px',
						type: "date"
					},
					{
						field: "AssetId",
						title: "Asset ID",
						width: '150px',
						type: "string",
					},
					{
						field: "CategoryNames",
						width: '150px',
						type: "string",
					},
					{
						field: "Cost",
						width: '150px',
						type: "number",
					},
					{
						field: "EmmissInsp",
						title: "Emission Inspection",
						width: '160px',
						type: "date",
					},
					{
						field: "EstLife",
						title: "Est Life (yrs)",
						width: '150px',
						type: "number",
					},
					{
						field: "FuelType",
						title: "Fuel",
						width: '150px',
						type: "string",
					},
					{
						field: "FuelCapacity",
						title: "Fuel Capacity",
						width: '150px',
						type: "number",
					},
					{
						field: "ExternalId",
						title: "External ID",
						width: '160px',
						type: "string",
					},
					{
						field: "Gpsid",
						title: "GPS ID",
						width: '160px',
						type: "string",
					},
					{
						field: "Guid",
						title: "GUID",
						width: '280px',
						type: "string",
					},
					{
						field: "Height",
						width: '150px',
						type: "number",
					},
					{
						field: "InActive",
						title: "Inactive",
						width: '150px',
						type: "boolean",
					},
					{
						field: "InsuranceNum",
						title: "Insurance #",
						width: '150px',
						type: "string",
					},
					{
						field: "InsuranceExp",
						title: "Insurance Expiration",
						width: '160px',
						type: "date",
					},
					{
						field: "Length",
						width: '150px',
						type: "number",
					},
					{
						field: "MaxWeight",
						title: "Max Weight",
						width: '150px',
						type: "number",
					},
					{
						field: "FuelConsumption",
						title: `${tf.measurementUnitConverter.isImperial() ? "MPG" : "KM/L"}`,
						width: '150px',
						type: "number",
					},
					{
						field: "LongName",
						title: "LongName",
						width: '150px',
						type: "string",
					},
					{
						field: "Comments",
						width: '250px',
						type: "string",
					},
					{
						field: "PurchaseOdometer",
						title: "Purchase Odometer",
						width: '150px',
						type: "number",
					},
					{
						field: "PurchasePrice",
						title: "Purchase Price",
						width: '150px',
						type: "number",
					},
					{
						field: "RegisExp",
						width: '160px',
						type: "date",
					},
					{
						field: "RegisNum",
						width: '150px',
						type: "string",
					},
					{
						field: "SalvageOdometer",
						title: "Salvage Odometer",
						width: '150px',
						type: "number",
					},
					{
						field: "SalvageValue",
						title: "Salvage Value",
						width: '150px',
						type: "number",
					},
					{
						field: "SalvageDate",
						title: "Salvage Date",
						width: '160px',
						type: "date",
					},
					{
						field: "StateInspection",
						width: '150px',
						type: "string",
					},
					{
						field: "InspectionExp",
						width: '160px',
						type: "date",
					},
					{
						field: "width",
						width: '150px',
						type: "number",
					},
					{
						field: "ContractorId",
						width: '150px',
						type: "integer",
					},
					{
						field: "Id",
						width: '150px',
						type: "integer",
					}];
			case 'Vehicle Equipment':
				return [
					{
						field: "Code",
						title: "Code",
						width: '200px',
						type: "string"
					},
					{
						field: "Comments",
						width: '200px',
						type: "string"
					},
					{
						field: "Description",
						type: "string"
					}
				];
			case 'Vehicle Body Type':
			case 'Vehicle Brake Type':
			case 'Vehicle Fuel Type':
			case 'Vehicle Make':
			case 'Vehicle Make Of Body':
			case 'Vehicle Model':
				return [
					{
						field: "Name",
						width: '200px',
						type: "string"
					}];
			case 'Vehicle Category':
				return [
					{
						field: "Name",
						title: "Name",
						width: '200px',
						type: "string"
					},
					{
						field: "Color",
						width: '50px',
						type: "string",
						template: "<div style='height:15px;width:15px;margin-right:.5em;border:1px solid rgb(213, 213, 213);background-color:#: data.Color#;float:left'></div>"
					},
					{
						field: "MapDisplay",
						width: '50px',
						type: "integer"
					},
					{
						field: "Active",
						width: '50px',
						type: "boolean"
					},
					{
						field: "TrailLength",
						width: '50px',
						type: "integer"
					},
					{
						field: "Symbol",
						width: '200px',
						type: "string"
					}
				];
			case "Mailing City":
			case "Trip Alias":
				return [
					{
						field: "Name",
						width: '200px',
						type: "string"
					}];
			case "Mailing Postal Code":
				return [
					{
						field: "Postal",
						width: '200px',
						type: "string"
					}];
			case "Route":
				return [
					{
						field: "Name",
						width: '200px',
						type: "string"
					},
					{
						field: "Notes",
						width: '200px',
						type: "string"
					}];
			case "Contact":
				return [
					{
						field: "FirstName",
						title: "First Name",
						type: "string",
						width: '150px'
					},
					{
						field: "LastName",
						title: "Last Name",
						type: "string",
						width: '150px'
					},
					{
						field: "LocalID",
						title: "Local ID",
						width: '100px',
						type: "string"
					},
					{
						field: "Title",
						title: "Title",
						type: "string",
						width: '100px'
					},
					{
						field: "Email",
						title: "Email",
						type: "string",
						width: '150px'
					},
					{
						field: "Phone",
						title: "Phone",
						type: "string",
						width: '100px',
						formatType: "phone",
						template: function(item)
						{
							return tf.dataFormatHelper.phoneFormatter(item.Phone) || '';
						}
					},
					{
						field: "Ext",
						title: "Ext",
						type: "string",
						width: '100px'
					},
					{
						field: "Mobile",
						title: "Mobile Phone",
						type: "string",
						width: '100px',
						template: function(item)
						{
							return tf.dataFormatHelper.phoneFormatter(item.Mobile) || '';
						}
					},
					{
						field: "Fax",
						title: "Fax",
						type: "string",
						width: '100px',
						template: function(item)
						{
							return tf.dataFormatHelper.phoneFormatter(item.Fax) || '';
						}
					},
					{
						field: "Street1",
						title: "Mail Street 1",
						type: "string",
						width: '100px'
					},
					{
						field: "Street2",
						title: "Mail Street 2",
						type: "string",
						width: '100px'
					},
					{
						field: "City",
						title: "Mail City",
						type: "string",
						width: '100px',
						ListFilterTemplate: TF.ListFilterDefinition.ListFilterTemplate.DistinctListValue("GeneralDataListsMailingCity", "contact", "City")
					},
					{
						field: "State",
						title: "Mail State/Province",
						type: "string",
						width: '150px',
						ListFilterTemplate: TF.ListFilterDefinition.ListFilterTemplate.DistinctListValue("GeneralDataListsMailingState", "contact", "State")
					},
					{
						field: "Zip",
						title: "Mail Postal Code",
						type: "string",
						width: '150px'
					},
					{
						field: "Notes",
						title: "Notes",
						type: "string",
						width: '150px'
					}
				];
		}
	};

	ImportDataHelper.prototype.getApiUrl = function(tableType, dbid)
	{
		if (tableType == 'Filter' || tableType == 'All')
		{
			return tf.api.apiPrefixWithoutDatabase();
		}
		else
		{
			return tf.api.apiPrefix('v2', dbid);
		}
	}

	ImportDataHelper.prototype.getTableTypeByName = function(tableName)
	{
		var self = this;
		switch (tableName)
		{
			case 'All':
				return 'AllImportTables';
			case 'Alternate Site':
				return 'AlternateSites';
			case 'Contractor':
				return 'Contractors';
			case 'District':
				return 'Districts';
			case 'Field Trip':
				return 'FieldTrips';
			case 'Field Trip Account':
				return 'FieldTripAccounts';
			case 'Field Trip Activity':
				return 'FieldTripActivities';
			case 'Field Trip Billing Classification':
				return 'FieldTripBillingClassifications';
			case 'Field Trip Classification':
				return 'FieldTripClassifications';
			case 'Field Trip Destination':
				return 'FieldTripDestinations';
			case 'Field Trip Equipment':
				return 'FieldTripEquipments';
			case 'Field Trip Template':
				return 'FieldTripTemplates';
			case 'Filter':
				return 'GridFilters';
			case 'Geo Region':
				return 'GeoRegions';
			case 'Geo Region Type':
				return 'GeoRegionTypes';
			case 'Redistrict':
				return 'Redistricts';
			case 'School':
				return 'Schools';
			case 'Staff':
				return 'Staff';
			case 'Student':
				return 'Students';
			case 'Student Disability Codes':
				return 'DisabilityCodes';
			case 'Student Ethnic Codes':
				return 'EthnicCodes';
			// case 'Student Requirement':
			// 	return 'StudentRequirements';
			// case 'Student Location':
			// 	return 'StudentLocations';
			case 'Trip':
				return 'Trips';
			case 'Vehicle':
				return 'Vehicles';
			case 'Vehicle Equipment':
				return 'VehicleEquips';
			case 'Vehicle Body Type':
				return 'VehicleBodyTypes';
			case 'Vehicle Brake Type':
				return 'VehicleBrakeTypes';
			case 'Vehicle Category':
				return 'Categories';
			case 'Vehicle Fuel Type':
				return 'VehicleFuelTypes';
			case 'Vehicle Make':
				return 'VehicleMakes';
			case 'Vehicle Make Of Body':
				return 'VehicleMakeOfBodies';
			case 'Vehicle Model':
				return 'VehicleModels';
			case "Mailing City":
				return 'MailingCities';
			case "Mailing Postal Code":
				return 'MailingPostalCodes';
			case "Trip Alias":
				return 'TripAlias';
			case 'Route':
				return 'Routes';
			case 'Contact':
				return 'Contacts';
		}
	}

	ImportDataHelper.prototype.getDataTypeGridSource = function()
	{
		var contactTables = [{ DataType: 'Contact', Selected: true, TableName: 'Contact', FilterTable: true }];

		var FieldTripTables = [{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Account', FilterTable: false, RelatedTables: ['Field Trip', 'Field Trip Template'] },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Activity', FilterTable: false, RelatedTables: ['Field Trip', 'Field Trip Template'] },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Billing Classification', FilterTable: false, RelatedTables: ['Field Trip', 'Field Trip Template'] },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Classification', FilterTable: false, RelatedTables: ['Field Trip', 'Field Trip Template'] },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Equipment', FilterTable: false, RelatedTables: ['Field Trip', 'Field Trip Template'] },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip', FilterTable: true },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Destination', FilterTable: false, RelatedTables: ['Field Trip', 'Field Trip Template'] },
		{ DataType: 'Field Trip', Selected: true, TableName: 'Field Trip Template', FilterTable: false }];

		var GeoRegionTables = [{ DataType: 'Geo Region', Selected: true, TableName: 'Geo Region', FilterTable: true },
		{ DataType: 'Geo Region', Selected: true, TableName: 'Geo Region Type', FilterTable: false, RelatedTables: ['Geo Region'] }];

		var RedistictingTables = [{ DataType: 'Redistricting', Selected: true, TableName: 'Redistrict', FilterTable: false }];

		var ResourcesTables = [{ DataType: 'Resources', Selected: true, TableName: 'Contractor', FilterTable: true, RelatedTables: ['Staff', 'Vehicle'] },
		{ DataType: 'Resources', Selected: true, TableName: 'Staff', FilterTable: true, RelatedTables: ['Trip'] }];

		var VehicleTables = [{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle', FilterTable: true, RelatedTables: ['Trip'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Equipment', FilterTable: false, RelatedTables: ['Vehicle'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Body Type', FilterTable: false, RelatedTables: ['Vehicle'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Brake Type', FilterTable: false, RelatedTables: ['Vehicle'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Fuel Type', FilterTable: false, RelatedTables: ['Vehicle'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Make', FilterTable: false, RelatedTables: ['Vehicle'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Make Of Body', FilterTable: false, RelatedTables: ['Vehicle'] },
		{ DataType: 'Vehicle', Selected: true, TableName: 'Vehicle Model', FilterTable: false, RelatedTables: ['Vehicle'] }];

		var SchoolTables = [{ DataType: 'School', Selected: true, TableName: 'District', FilterTable: true, RelatedTables: ['Student', 'School'] },
		{ DataType: 'School', Selected: true, TableName: 'School', FilterTable: true, RelatedTables: ['Trip', 'Student', 'Field Trip', 'Field Trip Template', 'Redistrict'] }];

		var StudentTables = [{ DataType: 'Student', Selected: true, TableName: 'Alternate Site', FilterTable: true, RelatedTables: ['Student'] },
		{ DataType: 'Student', Selected: true, TableName: 'Student Disability Codes', FilterTable: false, RelatedTables: ['Student'] },
		{ DataType: 'Student', Selected: true, TableName: 'Student Ethnic Codes', FilterTable: false, RelatedTables: ['Student'] },
		{ DataType: 'Student', Selected: true, TableName: 'Student', FilterTable: true }];

		var TransportationTables = [{ DataType: 'Transportation', Selected: true, TableName: 'Trip', FilterTable: true, RelatedTables: ['Route'] },
		{ DataType: 'Transportation', Selected: true, TableName: 'Route', FilterTable: true },
		{ DataType: 'Transportation', Selected: true, TableName: 'Trip Alias', FilterTable: false, RelatedTables: ['Trip'] }];

		var UserDefinedTables = [{ DataType: 'User Defined', Selected: true, TableName: 'Filter', FilterTable: false }];
		return contactTables.concat(FieldTripTables, GeoRegionTables, RedistictingTables, ResourcesTables, VehicleTables, SchoolTables, StudentTables, TransportationTables, UserDefinedTables);
	}

	ImportDataHelper.prototype.getDisplayData = function(data, tableName)
	{
		var self = this, result = [];
		switch (tableName)
		{
			case 'All':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, element.TableName),
						TableName: element.TableName,
						Record: (element.Record || '')
					});
				});
				break;
			case 'Alternate Site':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Alternate Site',
						Record: (element.Name || '') + '(' + (element.Public ? 'Public' : 'Private') + ', Street 1: ' + (element.MailStreet1 || '') + ', City: ' + (element.MailCity || '') + ')'
					});
				});
				break;
			case 'Contractor':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Contractor',
						Record: (element.Name || '')
					});
				});
				break;
			case 'District':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'District',
						Record: (element.Id || '') + ' - ' + (element.Name || '')
					});
				});
				break;
			case 'Field Trip':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip',
						Record: (element.Name || '') + ' (School: ' + (element.School || '') + ')'
					});
				});
				break;
			case 'Field Trip Account':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Account',
						Record: (element.Description || '') + ' (School: ' + (element.School || '') + ')'
					});
				});
				break;
			case 'Field Trip Activity':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Activity',
						Record: (element.Name || '') + ' - ' + (element.Description || '')
					});
				});
				break;
			case 'Field Trip Billing Classification':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Billing Classification',
						Record: (element.Classification || '')
					});
				});
				break;
			case 'Field Trip Classification':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Classification',
						Record: element.Code + ' - ' + (element.Description || '')
					});
				});
				break;
			case 'Field Trip Destination':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Destination',
						Record: (element.Name || '') + ' - ' + ' (' + (element.Street || '') + ', ' + (element.City || '') + ', ' + (element.State || '') + ' ' + (element.Zip || '') + ')'
					});
				});
				break;
			case 'Field Trip Equipment':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Equipment',
						Record: (element.EquipmentName || '')
					});
				});
				break;
			case 'Field Trip Template':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Field Trip Template',
						Record: (element.Name || '')
					});
				});
				break;
			case 'Filter':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Filter',
						Record: (element.Name || '')
					});
				});
				break;
			case 'Geo Region':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Geo Region',
						Record: (element.Name || '')
					});
				});
				break;
			case 'Geo Region Type':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Geo Region Type',
						Record: (element.Name || '')
					});
				});
				break;
			case 'Redistrict':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Redistrict',
						Record: (element.Name || '')
					});
				});
				break;
			case 'School':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'School',
						Record: (element.School || '') + ' - ' + (element.Name || '')
					});
				});
				break;
			case 'Staff':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Staff',
						Record: (element.FirstName || '') + ' ' + (element.MiddleName || '') + ' ' + (element.LastName || '')
					});
				});
				break;
			case 'Student':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Student',
						Record: (element.FirstName || '') + ' ' + (element.Mi || '') + ' ' + (element.LastName || '') + ' (School: ' + (element.School || '') + ', Grade: ' + (element.Grade || '') + ', Street 1: ' + (element.GeoStreet || '') + ', City: ' + (element.MailCity || '') + ')'
					});
				});
				break;
			case 'Student Disability Codes':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Student Disability Codes',
						Record: (element.Code || '') + ' - ' + (element.Description || '')
					});
				});
				break;
			case 'Student Ethnic Codes':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Student Ethnic Codes',
						Record: (element.Code || '') + ' - ' + (element.Description || '')
					});
				});
				break;
			// case 'Student Requirement':
			// 	data.map(function(element)
			// 	{
			// 		result.push({
			// 			Id: element.Id,
			// 			Selected: self.getDataSelectedProperty(element, tableName),
			// 			TableName: 'Student Requirement',
			// 			Record: (element.SchoolCode || '') + ' (studentId: ' + (element.StudentId || '') + ', SessionID: ' + (element.SessionID == 1 ? 'From School' : 'To School') + ', Type: ' + (element.Type == 0 ? 'Default' : 'Additional') + ')'
			// 		});
			// 	});
			// 	break;
			// case 'Student Location':
			// 	data.map(function(element)
			// 	{
			// 		result.push({
			// 			Id: element.Id,
			// 			Selected: self.getDataSelectedProperty(element, tableName),
			// 			TableName: 'Student Location',
			// 			Record: (element.SchoolCode || '') + ' (studentId: ' + (element.StudentId || '') + ', ProhibitCross: ' + element.ProhibitCross + ')'
			// 		});
			// 	});
			// 	break;
			case 'Trip':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Trip',
						Record: (element.Name || '') + ' (' + (element.Session == 0 ? 'To School' : (element.Session == 1 ? 'From School' : (element.Session == 2 ? 'Shuttle' : 'Both'))) + ')'
					});
				});
				break;
			case 'Vehicle':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Vehicle',
						Record: (element.BusNum || '') + ' (Capacity: ' + (element.Capacity || '') + ', W/C Capacity: ' + (element.WcCapacity || '') + ')'
					});
				});
				break;
			case 'Vehicle Equipment':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Vehicle Equipment',
						Record: (element.Code || '') + ' - ' + (element.Description || '')
					});
				});
				break;
			case 'Vehicle Body Type':
			case 'Vehicle Brake Type':
			case 'Vehicle Fuel Type':
			case 'Vehicle Make':
			case 'Vehicle Make Of Body':
			case 'Vehicle Model':
			case "Mailing City":
			case "Trip Alias":
			case "Route":
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: tableName,
						Record: element.Name
					});
				});
				break;
			case 'Vehicle Category':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: tableName,
						Record: element.Name + ' (Color: ' + element.Color + ' ,TrailLength:' + element.TrailLength + ')'
					});
				});
				break;
			case "Mailing Postal Code":
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: tableName,
						Record: element.Postal
					});
				});
				break;
			case 'Contact':
				data.map(function(element)
				{
					result.push({
						Id: element.Id,
						Selected: self.getDataSelectedProperty(element, tableName),
						TableName: 'Contact',
						Record: (element.FirstName || '') + ' ' + (element.LastName || '') + ' (LocalID: ' + (element.LocalID || '') + ')'
					});
				});
				break;
		}
		return result;
	}

	ImportDataHelper.prototype.getDataSelectedProperty = function(data, tableName)
	{
		var self = this, key = data.Id;
		if (self.viewModel.allDataUnselected['All'])
		{
			return false;
		}

		if (self.viewModel.allDataSelected['All'])
		{
			return true;
		}

		if (self.viewModel.allDataUnselected[tableName])
		{
			if (!self.viewModel.allDataSelectedArray[tableName] || (self.viewModel.allDataSelectedArray[tableName] && self.viewModel.allDataSelectedArray[tableName].indexOf(key) < 0))
			{
				return false;
			}
			else
			{
				return true;
			}
		}
		else if (self.viewModel.allDataUnselectedArray[tableName] && self.viewModel.allDataUnselectedArray[tableName].length > 0)
		{
			if (self.viewModel.allDataUnselectedArray[tableName].indexOf(key) < 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		else
		{
			return true;
		}
	}

	ImportDataHelper.prototype.getValidLookupActions = function(tableName)
	{
		if (tableName === 'School' || tableName === 'Field Trip Travel Scenario')
		{
			return [TF.ImportAndMergeData.LookupAction.findById(TF.ImportAndMergeData.LookupAction.RejectIfNotFound)];
		}

		return TF.ImportAndMergeData.LookupAction.all;
	};

	ImportDataHelper.prototype.getSortProperty = function(tableName)
	{
		var self = this;
		switch (tableName)
		{
			case 'District':
				return 'Id';
			case 'Field Trip Account':
				return 'Description';
			case 'Field Trip Billing Classification':
				return 'Classification';
			case 'Field Trip Classification':
				return 'Description';
			case 'Field Trip Equipment':
				return 'EquipmentName';
			case 'School':
				return 'School';
			case 'Staff':
				return 'FirstName';
			case 'Student':
				return 'FirstName';
			case 'Student Disability Codes':
			case 'Student Ethnic Codes':
			case 'Vehicle Equipment':
				return 'Code';
			// case 'Student Requirement':
			// 	return 'SchoolCode';
			// case 'Student Location':
			// 	return 'SchoolCode';
			case 'Vehicle':
				return 'BusNum';
			case 'Vehicle Body Type':
			case 'Vehicle Brake Type':
			case 'Vehicle Category':
			case 'Vehicle Fuel Type':
			case 'Vehicle Make':
			case 'Vehicle Make Of Body':
			case 'Vehicle Model':
			case "Mailing City":
			case 'Trip':
			case 'Redistrict':
			case 'Geo Region Type':
			case 'Geo Region':
			case 'Filter':
			case 'Field Trip Template':
			case 'Field Trip Destination':
			case 'Field Trip Activity':
			case 'Field Trip':
			case 'Contractor':
			case 'Alternate Site':
			case "Trip Alias":
			case "Route":
			case "Contact":
				return 'Name';
			case "Mailing Postal Code":
				return 'Postal';
		}
	}

	ImportDataHelper.prototype.getFilterNameByTableName = function(tableName)
	{
		var self = this;
		switch (tableName)
		{
			case 'Alternate Site':
				return 'altsite';
			case 'Contractor':
				return 'contractor';
			case 'District':
				return 'district';
			case 'Field Trip':
				return 'fieldtrip';
			case 'Geo Region':
				return 'georegion';
			case 'School':
				return 'school';
			case 'Staff':
				return 'staff';
			case 'Student':
				return 'student';
			case 'Trip':
				return 'trip';
			case 'Vehicle':
				return 'vehicle';
			case 'Vehicle Equipment':
				return 'vehicleequipment';
			case 'Route':
				return 'route';
			case 'Contact':
				return 'contact';
		}
	}

	ImportDataHelper.prototype.getTableOptionDefaultSetting = function(tableName)
	{
		switch (tableName)
		{
			case 'Alternate Site':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: true, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Staff':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: false, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					doNotAddCardEndDates: { display: true, default: false, disabled: !tf.allowMultipleCardStaff },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Contractor':
			case 'District':
			case 'Field Trip':
			case 'Field Trip Account':
			case 'Field Trip Activity':
			case 'Field Trip Billing Classification':
			case 'Field Trip Classification':
			case 'Field Trip Destination':
			case 'Field Trip Equipment':
			case 'Filter':
			case 'Geo Region Type':
			case 'School':
			case 'Student Ethnic Codes':
			case 'Vehicle':
			case 'Vehicle Equipment':
			case 'Vehicle Body Type':
			case 'Vehicle Brake Type':
			case 'Vehicle Category':
			case 'Vehicle Fuel Type':
			case 'Vehicle Make':
			case 'Vehicle Make Of Body':
			case 'Vehicle Model':
			case "Mailing City":
			case "Mailing Postal Code":
			case "Trip Alias":
			case "Route":
			case "Contact":
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: false, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Field Trip Template':
			case 'Geo Region':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: false },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: false, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Redistrict':
				return {
					update: { display: true, default: false, disabled: true },
					delete: { display: true, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: false, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Student':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: false },
					schedule: { display: true, default: true, disabled: false },
					useStopPool: { display: true, default: false, disabled: false },
					createDoorToDoor: { display: true, default: false, disabled: false },
					school: { display: true, default: false, disabled: false },
					reset: { display: true, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					geocode: { display: true, default: true, disabled: false },
					doNotAddCardEndDates: { display: true, default: false, disabled: !tf.allowMultipleCardStudent },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Student Disability Codes':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: true, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: true },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Trip':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: true, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: false, default: false, disabled: true },
					stoppool: { display: true, default: false, disabled: false },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
			case 'Address Point':
				return {
					update: { display: true, default: false, disabled: false },
					delete: { display: false, default: false, disabled: true },
					schedule: { display: false, default: false, disabled: true },
					useStopPool: { display: false, default: false, disabled: true },
					createDoorToDoor: { display: false, default: false, disabled: true },
					school: { display: false, default: false, disabled: true },
					reset: { display: false, default: false, disabled: true },
					stoppool: { display: false, default: false, disabled: false },
					doNotAddCardEndDates: { display: false, default: false, disabled: false },
					selectedDataModels: [],
					selectedStopPool: null
				};
		}
	}

	ImportDataHelper.generateNewImportName = function(name, existedNames)
	{
		let newName = name;
		if (!existedNames.includes(newName.toLowerCase()))
		{
			return newName;
		}

		let num = 1;
		newName = name + " (" + num + ")";
		while (existedNames.includes(newName.toLowerCase()))
		{
			num = num + 1;
			newName = name + " (" + num + ")";
		}
		return newName;
	}
})();