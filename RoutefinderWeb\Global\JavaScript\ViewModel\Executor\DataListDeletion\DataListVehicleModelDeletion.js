﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleModelDeletion = DataListVehicleModelDeletion;

	function DataListVehicleModelDeletion()
	{
		this.type = 'vehiclemodel';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Model';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleModelDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleModelDeletion.prototype.constructor = DataListVehicleModelDeletion;

	DataListVehicleModelDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?ModelId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleModelDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclemodel", pb.DELETE), ids);
	}

	DataListVehicleModelDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();