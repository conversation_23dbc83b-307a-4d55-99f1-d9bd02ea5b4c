﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleCategoryDeletion = VehicleCategoryDeletion;

	function VehicleCategoryDeletion()
	{
		this.type = 'vehiclecategory';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleCategoryDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleCategoryDeletion.prototype.constructor = VehicleCategoryDeletion;

	VehicleCategoryDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();