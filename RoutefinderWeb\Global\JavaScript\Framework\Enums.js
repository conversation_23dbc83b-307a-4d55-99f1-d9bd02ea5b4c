(function()
{
	var ns = createNamespace("TF.Enums");

	ns.SpecifyRecordsType = TF.createEnum(["All Records", "Filter", "Specific Records"], 1);

	ns.EditStatus = TF.createEnum(["No Changed", "Created", "Updated", "Deleted"]);

	ns.RoutingSpeedType = TF.createEnum(["StreetSpeed", "DefaultSpeed"]);

	ns.BoundaryType = TF.createEnum(["StreetPath", "Radius"]);

	ns.PathType = TF.createEnum(["GPS", "Distance", "Time"]);

	ns.FormRule = TF.createEnumWithValues(['SendEmail', 'CreateRecord', 'UpdateRecord', 'CreateAssociatedRecord'], null, ['sendemail', 'createrecord', 'updaterecord', 'createassociatedrecord']);
	ns.RuleType = TF.createEnumWithValues(['Question', 'Form'], null, ['question', 'form']);

	ns.RULE_IMPACT_TYPE = TF.createEnumWithValues(['NO_IMPACT', 'IMPACT_BUT_VALID', 'CAUSE_INVALID']);

	ns.DELETE_ITEM_TYPE = TF.createEnumWithValues(['IsSectionDeleted', 'IsLastSectionDeleted', 'IsQuestionDeleted']);

	ns.DBLockReason = TF.createEnum(
		["StopPool", "RoutingSession", "CritChange", "StudGlobalReplace", "StudFindSchedule", "ResourceScheduler", "Archive", "CopyFrom", "Rollover", "Restore", "Copy", "AllTypes"],
		[1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 99]
	);

	ns.UDFBoundarySetType = TF.createEnum(["GeoRegionType", "PopulationRegion", "SchoolBoundarySet", "NEZ", "StopPoolCategory"], [0, 1, 2, 3, 4]);

	ns.UngeocodedReason = TF.createEnum(["Import", "ManualAddressChange", "MassUpdate", "UngeocodeTool", "FormUpdate"], [1, 2, 3, 4, 5]);

	ns.CommentStatus = TF.createEnum(["Created", "Updated", "Deleted"]);

	ns.UDFType = TF.createEnum(
		['Boolean', 'Date', 'DateTime', 'Memo', 'Number', 'PhoneNumber', 'Text', 'Time', 'List', 'RatingScale', 'Currency', 'SystemField', 'Attachment', 'Signature', 'Map', 'ListFromData', 'QRAndBarcode', 'Rollup', 'Case', 'RatingScaleMatrix', 'Email', 'Hyperlink', 'Image', 'InBoundary'],
		[1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
	);

	createNamespace("TF").KendoClasses = {
		STATE: {
			ACTIVE: 'k-active',
			DISABLED: 'k-disabled',
			FOCUS: 'k-focus',
			HOVER: 'k-hover',
			SELECTED: 'k-selected',

		}
	}

	ns.Recurring = {
		RecurringTypeEnum: [
			{ text: "Day", value: 0 },
			{ text: "Week", value: 1 },
			{ text: "Month", value: 2 }
		],
		MonthModeEnum: [
			{ text: "SpecificDate", value: 0 },
			{ text: "Pattern", value: 1 }
		],
		MonthPatternSelectionEnum: [
			{ text: "First", value: 1 },
			{ text: "Second", value: 2 },
			{ text: "Third", value: 3 },
			{ text: "Fourth", value: 4 },
			{ text: "Last", value: 0 }
		],
		MonthPatternWeekDayEnum: [
			{ text: "Monday", value: 1 },
			{ text: "Tuesday", value: 2 },
			{ text: "Wednesday", value: 3 },
			{ text: "Thursday", value: 4 },
			{ text: "Friday", value: 5 },
			{ text: "Saturday", value: 6 },
			{ text: "Sunday", value: 7 },
			{ text: "Day", value: 8 },
			{ text: "Weekday", value: 9 },
			{ text: "Weekend Day", value: 10 }
		]
	}
})();
