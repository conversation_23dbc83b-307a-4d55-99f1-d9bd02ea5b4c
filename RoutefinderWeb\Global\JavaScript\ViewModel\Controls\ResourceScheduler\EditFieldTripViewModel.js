﻿(function()
{
	createNamespace('TF.Control.ResourceScheduler').EditFieldTripViewModel = EditFieldTripViewModel;

	function EditFieldTripViewModel(fieldTripEntity, drivers, aides, vehicles)
	{
		if (!fieldTripEntity.FieldTripResourceGroups)
		{
			fieldTripEntity.FieldTripResourceGroups = [];
		}

		this.resourceGroups = fieldTripEntity.FieldTripResourceGroups.map(function(resourceGroup)
		{
			return {
				FieldTripResourceGroupId: resourceGroup.FieldTripResourceGroupId,
				DriverId: resourceGroup.DriverId,
				AideId: resourceGroup.AideId,
				VehicleId: resourceGroup.VehicleId
			};
		});

		this.fieldTripEntity = fieldTripEntity;
		this.drivers = drivers;
		this.aides = aides;
		this.vehicles = vehicles;
		this.updateResourceNames();

		this.grid = null;

		this.obName = ko.observable(fieldTripEntity.Name);
		this.obDepartDate = ko.observable(moment(fieldTripEntity.DepartDate).locale("en-us").format("YYYY-MM-DD"));
		this.obDepartTime = ko.observable(moment(fieldTripEntity.DepartTime).locale("en-us").format("hh:mm A"));
		this.obSchool = ko.observable(fieldTripEntity.School);
		this.obReturnDate = ko.observable(fieldTripEntity.ReturnDate && moment(fieldTripEntity.ReturnDate).locale("en-us").format("YYYY-MM-DD"));
		this.obReturnTime = ko.observable(fieldTripEntity.ReturnTime && moment(fieldTripEntity.ReturnTime).locale("en-us").format("hh:mm A"));
		this.obDepartment = ko.observable(fieldTripEntity.DistrictDepartmentName == "" ? "Any" : fieldTripEntity.DistrictDepartmentName);//RW-18234 change "None" to "Any"
		this.obActivity = ko.observable(fieldTripEntity.FieldTripActivityName == "" ? "Any:" : fieldTripEntity.FieldTripActivityName);//RW-18234 change "None" to "Any"
		this.obStatus = ko.observable(fieldTripEntity.FieldTripStageName);
		this.obDestination = ko.observable(fieldTripEntity.Destination);
		this.obResourceGroups = ko.observableArray(this.resourceGroups);
		this.obNumOfVehicles = ko.observable(fieldTripEntity.NumberOfVehicles);

		this.isAdmin = tf.authManager.authorizationInfo.isAdmin || tf.authManager.authorizationInfo.isAuthorizedFor("transportationAdministrator", "edit");
		this.ftLevelPermissions = [];

		if (tf.authManager.isAuthorizedFor("level4Administrator", "edit"))
		{
			this.ftLevelPermissions.concat([4, 5, 6, 7]);
		}
		else if (tf.authManager.isAuthorizedFor("level3Administrator", "edit"))
		{
			this.ftLevelPermissions.concat([2, 3, 4, 5]);
		}
		else if (tf.authManager.isAuthorizedFor("level2Administrator", "edit"))
		{
			this.ftLevelPermissions.concat([1, 2, 3]);
		}
		else if (tf.authManager.isAuthorizedFor("level1Requestor", "edit"))
		{
			this.ftLevelPermissions.concat([1]);
		}

		this.fieldTripStatus = [
			{ id: 1, name: "Level 1 - Request Submitted", isApprove: true },
			{ id: 2, name: "Level 2 - Request Declined", isApprove: false },
			{ id: 3, name: "Level 2 - Request Approved", isApprove: true },
			{ id: 4, name: "Level 3 - Request Declined", isApprove: false },
			{ id: 5, name: "Level 3 - Request Approved", isApprove: true },
			{ id: 6, name: "Level 4 - Request Declined", isApprove: false },
			{ id: 7, name: "Level 4 - Request Approved", isApprove: true },
			{ id: 98, name: "Declined by Transportation", isApprove: false },
			{ id: 99, name: "Transportation Approved", isApprove: true },
			{ id: 100, name: "Canceled - Request Canceled", isApprove: false },
			{ id: 101, name: "Completed - Request Completed", isApprove: true },
		];
		this.obFieldTripStatus = ko.observableArray([]);
		this.obStatusEditable = ko.observable(false);
		this.obSelectedStatus = ko.observable(null);
		this.obSelectedStatusText = ko.computed(function()
		{
			var selectedStatus = this.obSelectedStatus(),
				item = this.obFieldTripStatus().filter(function(c)
				{
					return c.id === selectedStatus.id;
				}.bind(this))[0];

			return item ? item.name : "";
		}.bind(this));

		this.obResourceEditable = ko.observable(tf.helpers.fieldTripAuthHelper.canModifyResourceRecord());
		this.initializedEvent = new TF.Events.Event();
	}

	EditFieldTripViewModel.prototype.init = function()
	{
		this.initStatus();
		this.bindGrid();
		this.initializedEvent.notify();
	};

	EditFieldTripViewModel.prototype.initStatus = function()
	{
		var stageId = this.fieldTripEntity.FieldTripStageId,
			stageEntity = this.fieldTripStatus.filter(function(item) { return item.id === stageId; })[0];

		if (this.isAdmin)
		{
			this.obStatusEditable = ko.observable(true);
			this.obFieldTripStatus = ko.observableArray(this.fieldTripStatus);
			this.obSelectedStatus(stageEntity);
			return;
		}

		var permissionIndex = $.inArray(stageId, this.ftLevelPermissions);
		if (permissionIndex >= 0)
		{
			this.obFieldTripStatus = ko.observableArray([
				this.ftLevelPermissions[permissionIndex],
				{ id: -1, name: "Approve", isApprove: true },
				{ id: -2, name: "Decline", isApprove: false }
			]);
			this.obStatusEditable = ko.observable(true);
			this.obSelectedStatus(this.obFieldTripStatus()[0]);
			return;
		}

		this.obStatusEditable = ko.observable(false);
	};

	EditFieldTripViewModel.prototype.updateResourceNames = function()
	{
		var self = this;
		self.resourceGroups.forEach(function(resourceGroup)
		{
			var driver = self.drivers.find(function(item) { return item.DriverId === resourceGroup.DriverId; });
			resourceGroup.DriverName = driver ? driver.DriverName : null;

			var aide = self.aides.find(function(item) { return item.AideId === resourceGroup.AideId; });
			resourceGroup.AideName = aide ? aide.AideName : null;

			var vehicle = self.vehicles.find(function(item) { return item.VehicleId === resourceGroup.VehicleId; });
			resourceGroup.VehicleName = vehicle ? vehicle.VehicleName : null;
		});
	};

	EditFieldTripViewModel.prototype.addResourceGroupClick = function()
	{
		var self = this;

		tf.modalManager.showModal(
			new TF.Modal.ResourceScheduler.AddEditResourceGroupModalViewModel(null, self.drivers, self.aides, self.vehicles, self.fieldTripEntity.Id, self.resourceGroups)
		).then(function(result)
		{
			if (result)
			{
				this.refreshGrid(result, "add");
			}
		}.bind(this));
	};

	EditFieldTripViewModel.prototype.refreshGrid = function(resourceGroup, operation)
	{
		if (operation === "add")
		{
			resourceGroup.operation = "add";
			this.resourceGroups.push(resourceGroup);
		}
		else
		{
			var targetIndex, targetResourceGroup = this.resourceGroups.filter(function(item, index)
			{
				targetIndex = index;
				return item.FieldTripResourceGroupId === resourceGroup.FieldTripResourceGroupId;
			})[0];

			if (operation === "delete")
			{
				if (targetResourceGroup.operation && targetResourceGroup.operation === "add")
				{
					this.resourceGroups.splice(targetIndex, 1);
				}
				else
				{
					targetResourceGroup.operation = "delete";
				}
			}
			if (operation === "edit")
			{
				if (!targetResourceGroup.operation || targetResourceGroup.operation !== "add")
				{
					targetResourceGroup.operation = "edit";
				}

				targetResourceGroup.DriverId = resourceGroup.DriverId;
				targetResourceGroup.AideId = resourceGroup.AideId;
				targetResourceGroup.VehicleId = resourceGroup.VehicleId;
			}
		}

		//Filter out data marked for deletion
		const availableResourceGroups = this.resourceGroups.filter((item) => { return !item.operation || item.operation !== "delete" })
		this.obResourceGroups(availableResourceGroups);

		this.updateResourceNames();
		this.bindGrid();
	};

	EditFieldTripViewModel.prototype.actionClick = function(e)
	{
		e.preventDefault();
		var self = this, tr = $(e.target).closest("tr"),
			data = this.grid.data("kendoGrid").dataItem(tr),
			resourceGroup = {
				FieldTripResourceGroupId: data.FieldTripResourceGroupId,
				DriverId: data.DriverId,
				AideId: data.AideId,
				VehicleId: data.VehicleId
			};

		if (e && $(e.target).hasClass("k-grid-delete"))
		{
			this.refreshGrid(resourceGroup, "delete");
		}
		else if (e && $(e.target).hasClass("k-grid-edit-command"))
		{
			tf.modalManager.showModal(
				new TF.Modal.ResourceScheduler.AddEditResourceGroupModalViewModel(resourceGroup, self.drivers, self.aides, self.vehicles, self.fieldTripEntity.Id, self.resourceGroups)
			).then(function(result)
			{
				if (result)
				{
					this.refreshGrid(result, "edit");
				}
			}.bind(this));
		}
	};

	EditFieldTripViewModel.prototype.bindGrid = function()
	{
		var self = this, $gridContainer = $(".edit-modal.fieldtrip .resourceGroupGrid"),
			columns = [
				{ field: "VehicleName", title: "Vehicle", width: 86 },
				{ field: "DriverName", title: "Driver", width: 138 },
				{ field: "AideName", title: "Aide", width: 138 }
			];

		let actionColumnWidth = 60;
		if (this.obResourceEditable())
		{
			columns.push({
				field: "Action",
				width: actionColumnWidth,
				command: [{
					name: "edit-command",
					click: self.actionClick.bind(self)
				}, {
					name: "delete",
					click: self.actionClick.bind(self)
				}]
			});
		}
		else
		{
			let adjustWidth = actionColumnWidth / columns.length;
			for (let i = 0; i < columns.length; i++)
			{
				columns[i].width += adjustWidth;
			}
		}

		if (this.grid && this.grid.data("kendoGrid"))
		{
			this.grid.data("kendoGrid").destroy();
			this.grid.empty();
		}

		this.grid = $gridContainer.kendoGrid({
			columns: columns,
			scrollable: true,
			dataSource: self.resourceGroups.filter(function(item) { return !item.operation || item.operation != "delete" }),
			dataBound: function(e)
			{
				self.initGridScrollBar($gridContainer);
			}
		});
	};

	EditFieldTripViewModel.prototype.initGridScrollBar = function(container)
	{
		var $gridContent = container.find(".k-grid-content");

		$gridContent.css({
			"overflow-y": "auto"
		});
	};

	EditFieldTripViewModel.prototype.getStatusId = function()
	{
		// status of field trip
		// id		name
		// 1		Level 1 - Request Submitted
		// 2		Level 2 - Request Declined
		// 3		Level 2 - Request Approved
		// 4		Level 3 - Request Declined
		// 5		Level 3 - Request Approved
		// 6		Level 4 - Request Declined
		// 7		Level 4 - Request Approved
		// 98		Declined by Transportation
		// 99		Transportation Approved
		// 100	Canceled - Request Canceled
		// 101	Completed - Request Completed
		var self = this, statusId = -1, authInfo = tf.authManager.authorizationInfo,
			isApprove = self.obSelectedStatus().isApprove, selectedId = self.obSelectedStatus().id;
		if (self.isAdmin || selectedId > 0)
		{
			statusId = selectedId;
		}
		else if (authInfo.isAuthorizedFor("level4Administrator", "edit"))
		{
			statusId = isApprove ? 7 : 6;
		}
		else if (authInfo.isAuthorizedFor("level3Administrator", "edit"))
		{
			statusId = isApprove ? 5 : 4;
		}
		else if (authInfo.isAuthorizedFor("level2Administrator", "edit"))
		{
			statusId = isApprove ? 3 : 2;
		}
		else if (authInfo.isAuthorizedFor("level1Requestor", "edit"))
		{
			statusId = 1;
		}
		return statusId;
	};

	EditFieldTripViewModel.prototype.apply = function()
	{
		var self = this;

		if (self.obSelectedStatus())
		{
			self.fieldTripEntity.FieldTripStageId = self.getStatusId();
		}

		self.resourceGroups.forEach(function(resourceGroup)
		{
			switch (resourceGroup.operation)
			{
				case "add":
					self.fieldTripEntity.FieldTripResourceGroups.push({
						FieldTripId: self.fieldTripEntity.Id,
						DriverId: resourceGroup.DriverId,
						AideId: resourceGroup.AideId,
						VehicleId: resourceGroup.VehicleId
					});
					break;
				case "edit":
					if (!resourceGroup.DriverId && !resourceGroup.AideId && !resourceGroup.VehicleId)
					{
						// just like delete
						self.fieldTripEntity.FieldTripResourceGroups = self.fieldTripEntity.FieldTripResourceGroups.filter(function(group)
						{
							return group.FieldTripResourceGroupId !== resourceGroup.FieldTripResourceGroupId;
						});
					}
					else
					{
						self.fieldTripEntity.FieldTripResourceGroups.forEach(function(group)
						{
							if (group.FieldTripResourceGroupId === resourceGroup.FieldTripResourceGroupId)
							{
								group.DriverId = resourceGroup.DriverId;
								group.AideId = resourceGroup.AideId;
								group.VehicleId = resourceGroup.VehicleId;
							};
						});
					}
					break;
				case "delete":
					self.fieldTripEntity.FieldTripResourceGroups = self.fieldTripEntity.FieldTripResourceGroups.filter(function(group)
					{
						return group.FieldTripResourceGroupId !== resourceGroup.FieldTripResourceGroupId;
					});
					break;
			}
		});

		return tf.promiseAjax.put(pathCombine(tf.api.apiPrefix(), "fieldtrips"), {
			data: [self.fieldTripEntity],
			paramData: {
				"@relationships": "FieldTripResourceGroup"
			}
		});
	};
})();