﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.TripDataModel = function(tripEntity)
	{
		namespace.BaseDataModel.call(this, tripEntity);
	}

	namespace.TripDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.TripDataModel.prototype.constructor = namespace.TripDataModel;

	namespace.TripDataModel.prototype.mapping = [
		{ "from": "ActivityTrip", "default": false },
		{ "from": "Aide", "default": null },
		{ "from": "AideFirstName", "default": null },
		{ "from": "AideId", "default": 0 },
		{ "from": "AideLastName", "default": null },
		{ "from": "BusAide", "to": "hasBusAide", "default": 0 },
		{ "from": "BusAideContractorName", "default": null },
		{ "from": "BusAideName", "default": null },
		{ "from": "Capacity", "default": null },
		{ "from": "Comments", "default": "" },
		{ "from": "Cost", "default": 0 },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "Day", "default": 0 },
		{ "from": "Description", "default": "" },
		{ "from": "Dhdistance", "default": 0, "to": "deadheadDistance" },
		{ "from": "Disabled", "default": false },
		{ "from": "Distance", "default": 0 },
		{ "from": "Driver", "default": null },
		{ "from": "DriverContractorName", "default": null },
		{ "from": "DriverFirstName", "default": null },
		{ "from": "DriverId", "default": 0 },
		{ "from": "DriverLastName", "default": null },
		{ "from": "DriverName", "default": null },
		{ "from": "Duration", "default": 0 },
		{ "from": "EndDate", "default": null },
		{ "from": "EstTransport", "default": null },
		{ "from": "FilterName", "default": "" },
		{ "from": "FilterSpec", "default": "" },
		{ "from": "FinishTime", "default": "00:00:00" },
		{ "from": "Friday", "default": true },
		{ "from": "GPSEnabledFlag", "default": false },
		{ "from": "GUID", "default": "" },
		{ "from": "HasBusAide", "default": false },
		{ "from": "HomeSchl", "default": true },
		{ "from": "HomeTrans", "default": false },
		{ "from": "IName", "default": "" },
		{ "from": "IShow", "default": false },
		{ "from": "Id", "default": 0 },
		{ "from": "IntGratChar1", "default": "" },
		{ "from": "IntGratChar2", "default": "" },
		{ "from": "IntGratDate1", "default": null },
		{ "from": "IntGratDate2", "default": null },
		{ "from": "IntGratNum1", "default": 0 },
		{ "from": "IntGratNum2", "default": 0 },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "MaxOnBus", "default": 0 },
		{ "from": "Monday", "default": true },
		{ "from": "Name", "default": "" },
		{ "from": "NonDisabled", "default": true },
		{ "from": "ExcludeNoStudStopAndDirections", "default": false },
		{ "from": "NumTransport", "default": 0 },
		{ "from": "ObjectId", "default": 0 },
		{ "from": "Saturday", "default": false },
		{ "from": "SchoolIds", "default": "" },
		{ "from": "Schools", "default": "" },
		{ "from": "Session", "default": 0 },
		{ "from": "Shuttle", "default": false },
		{ "from": "StartDate", "default": null },
		{ "from": "StartTime", "default": "00:00:00" },
		{ "from": "Sunday", "default": false },
		{ "from": "Thursday", "default": true },
		{ "from": "TotalStudents", "default": 0 },
		{ "from": "TravelScenarioId", "default": 0 },
		{ "from": "TripAlias", "default": null },
		{ "from": "TripAliasID", "default": 0 },
		{ "from": "TripStops", "default": [] },
		{ "from": "TripTypeName", "default": null },
		{ "from": "Tuesday", "default": true },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
		{ "from": "Vehicle", "default": null },
		{ "from": "VehicleContractorName", "default": null },
		{ "from": "VehicleId", "default": 0 },
		{ "from": "VehicleName", "default": null },
		{ "from": "Wednesday", "default": true },
		{ "from": "iDescription", "default": "" },
		{ "from": "SpeedType", "default": 0 },
		{ "from": "DefaultSpeed", "default": 25 },
		{ "from": "TripDateRanges", "default": [] },
		{ "from": "TripDateRangeSettings", "default": [] },
		{ "from": "TripStudentTags", "default": [] }
	];
})();