(function()
{
	createNamespace("TF.Control").BaseWizardStepViewModel = BaseWizardStepViewModel;

	function BaseWizardStepViewModel(data)
	{
		this.disposables = [];
		this.template = "";
		this.data = data;
		this.name = ko.observable("");
		this.description = ko.observable("");
		this.shortCutKeyHashMapKeyName = Math.random().toString(36).substring(7);
	}

	BaseWizardStepViewModel.prototype.constructor = BaseWizardStepViewModel;

	BaseWizardStepViewModel.prototype.init = function(viewModel, el)
	{
		this.initValidator(el);
	};

	BaseWizardStepViewModel.prototype.getValidatorFields = function(el)
	{
		return {};
	};

	BaseWizardStepViewModel.prototype.initValidator = function(el)
	{
		var self = this, validatorFields = self.getValidatorFields(el);
		if (!validatorFields) return;

		self.validator = ($(el).bootstrapValidator({
			excluded: [":hidden", ":not(:visible)", ":disabled"],
			live: "enabled",
			fields: validatorFields
		})).data("bootstrapValidator");
	};

	BaseWizardStepViewModel.prototype.validate = function()
	{
		return this.validator ? this.validator.validate() : Promise.resolve(true);
	};

	BaseWizardStepViewModel.prototype.apply = function()
	{
		return this.validate().then(function(valid)
		{
			return valid && this.execute();
		}.bind(this));
	};

	BaseWizardStepViewModel.prototype.execute = function()
	{
		return Promise.resolve(true);
	};

	BaseWizardStepViewModel.prototype.back = function()
	{
		return Promise.resolve();
	};

	BaseWizardStepViewModel.prototype.dispose = function()
	{
		ko.utils.arrayForEach(this.disposables, this.disposeOne);
		this.disposables = [];
		ko.utils.objectForEach(this, this.disposeOne);
	};

	BaseWizardStepViewModel.prototype.disposeOne = function(propOrValue, value)
	{
		var disposable = value || propOrValue;
		if (disposable && typeof disposable.dispose === "function")
		{
			disposable.dispose();
		}
	};
})();