.form-container {
	.attachment-container {
		max-height: 400px;

		.attach-document-block {
			border: 2px dashed lightgrey;
			border-radius: 5px;

			&.has-invalid {
				border: 2px dashed #DB4D37;
			}
		}

		.document-list {
			max-height: 230px;
			overflow: auto;
		}

		.content-wrapper {
			height: 60px;
			padding: 10px 0px 0px 0px;
			margin: 5px;
			display: inline-block;
			text-align: left;
			width: 428px;
			background-color: #3F3F3F;


			.file-container {
				display: inline-block;
				white-space: nowrap;
				width: 325px;

				.file-icon {
					width: 25px;
					height: 25px;
					display: inline-block;
					background: url("../../Global/img/detail-screen/document_classifications.svg") no-repeat center center;
				}

				.file-name {
					color: #fff;
					font-size: 14px;
					line-height: 20px;
					font-weight: bold;
					max-width: 290px;
					margin-top: 10px;
					padding-left: 3px;
					display: inline-block;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap
				}
			}

			.control-bar {
				display: inline-block;
				padding-left: 5px;
				width: 100px;

				a {
					margin-bottom: 5px;
					line-height: 16px;
					min-width: 65px;
					cursor: pointer;
					display: inline-block;
					color: lightblue;
				}

				a.preview {
					display: block;
				}

				.file-warning {
					display: none;
				}

				.trash-can {
					width: 20px;
					height: 20px;
					display: inline-block;
					cursor: pointer;
					visibility: hidden;
					background: url("../../Global/img/menu/Delete-White.svg") no-repeat center center;
					margin-bottom: -3px;
				}

				&:hover {
					.trash-can {
						visibility: visible;
					}
				}
			}
		}

		.content-wrapper.invalid-file {
			position: relative;

			.control-bar {
				.preview, .download {
					visibility: hidden;
				}

				.file-warning {
					position: absolute;
					color: red;
					font-size: 20px;
					right: 5px;
					top: 1px;
					display: block;
				}
			}
		}
	}

	.place-holder {
		padding: 5px;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.browse-file {
		color: #0000FF;
		cursor: pointer;
		text-decoration: underline;

		&.disable-upload {
			color: lightblue;
			cursor: not-allowed;
		}
	}

	.dragover-mask {
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		position: absolute;
		z-index: -1;
		width: 100%;
	}

	.upload-file-container {
		top: 0;
		bottom: 0
	}

	.upload-file-container {
		bottom: -5px !important;
		left: 0px;
		right: -5px;
		top: -5px !important;
		position: absolute;
		display: none;
		z-index: 99999;
		border: #FB9823 dashed 2px;
		overflow: hidden;
		width: 100%;

		.input-box {
			border: none;
			position: relative;
			z-index: 1;
			top: 0;
			left: 0;
			height: 100%;

			.input {
				height: 100%;
			}
		}

		.input-label {
			position: relative;
			width: 36%;
			border: #FB9823 solid 2px;
			left: 50%;
			transform: translateX(-50%);
			font-size: 12px;
			text-align: center;
			padding: 1px;
			margin-top: 4px;
			background-color: white;
		}

		.upload-file-input {
			border: none;
			width: 0;
		}
	}
}

.k-mobile {
	.attachment-container {
		.attach-document-block {
			border: none;
			padding-left: 8px;
		}
	}


	.place-holder {
		display: none;
	}

	.browse-file {
		//margin: 10px;
		height: 70px;
		width: 70px;
		display: inline-block;
		border: 1px dashed lightgrey;
		font-size: 50px;
		line-height: 70px;
		text-align: center;
		text-decoration: none;
		border-radius: 5px;
		padding: 10px;
		background: linear-gradient(#000,#000), linear-gradient(#000,#000), #fff;
		background-position: center;
		background-size: 50% 2px,2px 50%; /*thickness = 2px, length = 50% (25px)*/
		background-repeat: no-repeat;
		margin-right: 2px;

		&.disable-upload {
			background-color: lightgray;
		}
	}

	.attachment-container .content-wrapper {
		position: relative;
		width: 70px;
		height: 70px;
		margin: 5px 5px 5px 0px;
		text-align: center;
		border: 1px solid lightgrey;
		background-color: white;
		border-radius: 5px;
		vertical-align: bottom;

		.file-container {
			margin-top: 6px;
			width: 70px;

			.file-name {
				height: 35px;
				max-width: 60px;
				color: unset;
				font-size: 12px;
				line-height: unset;
				margin-top: 6px;
				white-space: break-spaces;
			}

			.file-icon {
				margin-left: 25px;
				display: block;
				width: 15px;
				height: 15px;
				background: url("../../Global/img/icons/document_classifications_black.svg") no-repeat center center;
			}
		}

		.control-bar {
			display: block;
			padding: 0px;
			width: 100%;

			a.preview, a.download {
				display: none;
			}

			.trash-can {
				position: absolute;
				top: 4px;
				right: -1px;
				z-index: 100;
				color: #000;
				font-weight: bold;
				text-align: center;
				font-size: 18px;
				line-height: 10px;
				border-radius: 50%;
				background: none;
				visibility: visible;
			}
		}
	}

	.attachment-container .content-wrapper.invalid-file {
		.control-bar .file-warning {
			font-size: 12px;
			left: 5px;
			right: unset;
		}
	}
}
