﻿
(function()
{
	createNamespace('TF.Control').TestEmailViewModel = TestEmailViewModel;
	function TestEmailViewModel(settingsConfigurationDataModal)
	{
		this.selectRecipientToClick = this.selectRecipientToClick.bind(this);
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);
		this.obEntityDataModel = ko.observable(settingsConfigurationDataModal);
		this.obEmailToList = ko.observableArray([]);
		this.obEmailToErrorList = ko.observableArray([]);
		this.obEmailToString = ko.computed(function()
		{
			return this.obEmailToList().map(function(item)
			{
				return this.EmailFormatter(item);
			}.bind(this)).join(";");
		}.bind(this));

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}
	TestEmailViewModel.prototype.EmailFormatter = function(item)
	{
		return item.emailAddress();
	};
	TestEmailViewModel.prototype.initModel = function(viewModel, el)
	{
		this._$form = $(el);
		var validatorFields = {}, isValidating = false, self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};
		var validator;
		this._$form.find("input[name='from']").focus();

		validatorFields.FromAddress = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback:
				{
					message: "invalid email",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "email");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						var emailRegExp = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
						var isValid = emailRegExp.test(value);
						if (!isValid)
						{
							return false;
						}
						return true;
					}
				}
			}
		};

		validatorFields["mailToList"] =
		{
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback:
				{
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "email");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						value = value.trim();
						if (value === "")
						{
							this.obEmailToList([]);
							return true;
						}
						var result = true;
						var reg = /[,;]/;
						var emailList = value.split(reg);
						var emailRegExp = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
						var errorEmails = [];
						var oldList = this.obEmailToList();
						var newList = [];
						$.each(emailList, function(n, item)
						{
							item = item.trim();
							if (item == "")
							{
								return;
							}
							var isValid = emailRegExp.test(item);
							if (!isValid)
							{
								errorEmails.push(item);
								result = false;
							}

							var to = Enumerable.From(oldList).Where(function(c)
							{
								return this.EmailFormatter(c).trim() == item.trim();
							}.bind(this)).ToArray();
							if (to.length > 0)
							{
								newList.push(to[0]);
							}
							else
							{
								newList.push(
									new TF.DataModel.ScheduledReportReceiptDataModel({
										SelectedUserId: 0,
										EmailAddress: item
									})
								);
							}

						}.bind(this));

						this.obEmailToList(newList);
						this._$form.find("small[data-bv-for=mailToList][data-bv-validator=callback]").text(errorEmails.length == 1 ? errorEmails[0] + ' is not a valid email.' : errorEmails.length + ' emails are invalid.');

						return result;

					}.bind(this)
				}
			}
		};

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
			var $parent = data.element.closest('.form-group');
			$parent.removeClass('has-success');
		});

		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
	};

	TestEmailViewModel.prototype.focusField = function(viewModel, e)
	{
		$(viewModel.field).focus();
	};

	TestEmailViewModel.prototype.selectRecipientToClick = function(viewModel, e)
	{
		var ids = this.obEmailToList().map(function(item)
		{
			return item.selectedUserId();
		});
		var searchData = new TF.SearchParameters(null, null, null, null, null, ids, null);
		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", "user"), {
			data: searchData.data
		})
			.then(function(apiResponse)
			{
				tf.modalManager.showModal(new TF.Modal.ListMoverSelectRecipientControlModalViewModel(apiResponse.Items)).then(function(result)
				{
					if (!result)
					{
						return;
					}
					var list = result.map(function(item)
					{
						var name = item.LoginId;
						if (item.FirstName != "" || item.LastName != "")
						{
							name = item.FirstName + " " + item.LastName;
						}
						return new TF.DataModel.ScheduledReportReceiptDataModel({
							SelectedUserId: item.Id,
							EmailAddress: item.Email,
							UserName: name
						});
					});
					$.each(this.obEmailToList(), function(n, item)
					{
						if (item.selectedUserId() == 0)
						{
							list.push(item);
						}
					});
					this.obEmailToList(list);
				}.bind(this));
			}.bind(this));
	};

	TestEmailViewModel.prototype.apply = function()
	{
		return this.trysave()
			.then(function(data)
			{
				return data;
			});
	};

	TestEmailViewModel.prototype.trysave = function()
	{
		this.obErrorMessageDivIsShow(false);
		this.obValidationErrors([]);

		var validator = this._$form.data("bootstrapValidator");
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (valid)
				{
					// Check if the subject and message are empty
					var emailSubjectText = !!this.obEntityDataModel().emailSubject() ? !!(this.obEntityDataModel().emailSubject().trim()) : false;
					var emailMessageText = !!this.obEntityDataModel().emailMessage() ? !!(this.obEntityDataModel().emailMessage().trim()) : false;

					if (!!emailSubjectText && !!emailMessageText) { return this.save(); }

					var emptyList = [];
					if (!emailSubjectText) { emptyList.push("Subject"); }
					if (!emailMessageText) { emptyList.push("Message"); }

					var warningContent = ("A " + (emptyList.length > 1 ? emptyList.join(" and a ") : emptyList.join(" and ")) + (emptyList.length > 1 ? " have" : " has") + " not been specified.  Are you sure you want to send the test email?");

					return tf.promiseBootbox.yesNo({ message: warningContent, closeButton: true }, "Confirmation Message")
						.then(function(result)
						{
							if (result) { return this.save(); }
						}.bind(this));
				}
			}.bind(this));
	};

	TestEmailViewModel.prototype.save = function()
	{
		this.obEntityDataModel().mailToList(this.obEmailToList().map(function(item)
		{
			return item.emailAddress();
		}));
		return tf.promiseAjax["post"](pathCombine(tf.api.apiPrefixWithoutDatabase(), "emails"),
			{
				paramData:
				{
					isTest: true
				},
				data: this.obEntityDataModel().toData()
			}).then(function(data)
			{
				if (data.Items[0] !== "")
				{
					tf.promiseBootbox.alert("A test email could not be sent.", "Unable to Send Test Email");
				} else
				{
					return tf.promiseBootbox.alert("An email has been successfully sent. Verify that the " + this.obEmailToString() + (this.obEmailToList().length == 1 ? " has" : " have") + " received this email.", "Test Email Successfully Sent")
						.then(function()
						{
							return true;
						}.bind(this));
				}
			}.bind(this));
	};

	TestEmailViewModel.prototype.close = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (this.obEntityDataModel().apiIsDirty())
			{
				resolve(tf.promiseBootbox.yesNo("Are you sure you want to cancel this test email?", "Confirmation Message"));
			} else
			{
				resolve(true);
			}
		}.bind(this));
	};

	TestEmailViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

