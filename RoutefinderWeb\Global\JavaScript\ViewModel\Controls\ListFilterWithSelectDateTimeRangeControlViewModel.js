(function()
{
	var timeControlHelper = TF.TimeRangeWithWeekDayControlHelper;

	createNamespace('TF.Control').ListFilterWithSelectDateTimeRangeControlViewModel = ListFilterWithSelectDateTimeRangeControlViewModel;
	function ListFilterWithSelectDateTimeRangeControlViewModel(selectedData, options)
	{
		selectedData = null;

		var self = this;
		self.options = options;
		self.validationMessage = null;
		self.pageLevelViewModel = new TF.PageLevel.ListFilterWithSelectDateTimeRangeControlPageLevelViewModel(self);

		var userPreferenceKey = options.parentPageName + '.listFilterWithSelectDateTimeRange';

		timeControlHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		timeControlHelper.initFilterTimeRange.bind(self)({
			startDate: moment(),
			endDate: moment(),
			startTime: moment().format('YYYY-MM-DD 00:00:00'),
			endTime: moment().format('YYYY-MM-DD HH:mm:00')
		});
		timeControlHelper.initFilterWeekdays.bind(self)();

		self.obErrorMessageDivIsShow = ko.observable(false);
		self.obErrorMessage = ko.observable('');
		self.obValidationErrors = ko.observableArray([]);
		self.obErrorMessageTitle = ko.observable("Error Occurred");
		self.obErrorMessageDescription = ko.observable("The following error occurred.");
	}

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.constructor = ListFilterWithSelectDateTimeRangeControlViewModel;

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.columnSources = TF.ListFilterDefinition.ColumnSource;

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.apply = function()
	{
		var self = this;
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(self);

		return self.trySave()
			.then(function(valid)
			{
				if (!valid)
				{
					return Promise.reject();
				}
				else
				{
					timeControlHelper.saveUserPreference.call(self);
					return Promise.resolve(true);
				}
			}.bind(self));
	};

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.trySave = function(disablePubsub)
	{
		var self = this;
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
					return Promise.reject();
				else
					return Promise.resolve(true);
			});
	};

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.dispose = function()
	{
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.dispose.call(this);
		if (this.validationMessage && this.validationMessage.length > 0)
		{
			this.validationMessage.remove();
		}
		this.pageLevelViewModel.dispose();
	};

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.cancel = function()
	{
		if (this.validationMessage && this.validationMessage.length > 0)
		{
			this.validationMessage.remove();
		}
		return new Promise(function(resolve, reject)
		{
			resolve(false);
		}.bind(this));
	};

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.init.call(this, viewModel, el);
		// add setTimeout for make validationInitialize later than datePicker controls init finished
		setTimeout(function()
		{
			// if (!self.obHasCachedSelecedVehicles())
			// {
			// 	var $container = $(el).closest('.modal-content');
			// 	$container.find('.modal-header button.close').remove();
			// 	$container.find('.modal-header button.close').remove();
			// 	$container.find('.modal-footer button.btn-link').remove();
			// }

			viewModel.validationInitialize(viewModel, el);
		}.bind(this), 1000);
	};

	ListFilterWithSelectDateTimeRangeControlViewModel.prototype.validationInitialize = function(viewModel, el)
	{
		var self = this;
		self._$form = $(el);

		if (self._$form.closest(".tfmodal-container").length > 0)
		{
			self.validationMessage = self._$form.closest(".tfmodal-container").find(".page-level-message-container");
			self.validationMessage.css("z-index", self._$form.closest(".tfmodal.modal").css("z-index"));
			$("body").append(self.validationMessage);
		}

		var validatorFields = {};
		var isValidating = false;
		validatorFields.startDate = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				}
			}
		};

		validatorFields.endDate = {
			trigger: "blur change",
			validators: {

				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				}
			}
		};

		validatorFields.startTime = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'invalid time',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value, 'h:mm A', true);
						return m.isValid();
					}.bind(this)
				}
			}
		};

		validatorFields.endTime = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'invalid time',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value, 'h:mm A', true);
						return m.isValid();
					}.bind(this)
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			})
			.on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));

	};

})();
