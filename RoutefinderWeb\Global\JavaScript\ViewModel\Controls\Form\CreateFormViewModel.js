(function()
{
	createNamespace("TF.Control.Form").CreateFormViewModel = CreateFormViewModel;

	AllDataTypeList =
		[{ ID: 1, Type: "Alternate Site" }
			, { ID: 19, Type: "Contact" }
			, { ID: 2, Type: "Contractor" }
			, { ID: 3, Type: "District" }
			, { ID: 4, Type: "Field Trip" }
			, { ID: 5, Type: "Geo Region" }
			, { ID: 31, Type: "Route" }
			, { ID: 7, Type: "School" }
			, { ID: 8, Type: "Staff" }
			, { ID: 9, Type: "Student" }
			, { ID: 10, Type: "Trip" }
			, { ID: 13, Type: "Trip Stop" }
			, { ID: 11, Type: "Vehicle" }];
	CreateFormViewModel.AllDataTypeList = AllDataTypeList;
	function CreateFormViewModel(options)
	{
		var self = this;
		const defaultDataType = AllDataTypeList[0];
		self.obDataTypeOptions = ko.observableArray(AllDataTypeList);
		self.obSelectedDataType = ko.observable(defaultDataType);
		self.obSelectedDataTypeName = ko.observable(defaultDataType.Type);
	}

	CreateFormViewModel.prototype.constructor = CreateFormViewModel;

	/**
	 * Initialization.
	 *
	 */
	CreateFormViewModel.prototype.init = function(viewModel, element)
	{
		var self = this;
		self.element = $(element);
		// AllDataTypeList = [];
		// tf.promiseAjax
		// 	.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "dataTypes"))
		// 	.then(function(result)
		// 	{
		// 		result.Items.filter(x => x.DisplayInNav && x.Type !== 'ALL').sort((a, b) => (a.Type > b.Type ? 1 : -1)).forEach(
		// 			(item) => AllDataTypeList.push(item)
		// 		);
		// 	});
	};

	CreateFormViewModel.prototype.validate = function()
	{
		var self = this;
		var result = self.obSelectedDataType !== 'ALL';
		return Promise.resolve(result);
	}

	CreateFormViewModel.prototype.dispose = function()
	{
		var self = this;
	};
})();
