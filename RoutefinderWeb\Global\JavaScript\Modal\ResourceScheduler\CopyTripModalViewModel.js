(function()
{
	createNamespace("TF.Modal.ResourceScheduler").CopyTripModalViewModel = CopyTripModalViewModel;

	function CopyTripModalViewModel(trip, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Copy Trip");
		this.modalWidth("350px");
		this.contentTemplate('modal/resourcescheduler/copytrip');
		this.buttonTemplate('modal/PositiveNegative');
		this.obPositiveButtonLabel("OK");
		this.obNegativeButtonLabel("Cancel");
		this.viewResourceScheduleViewModel = new TF.Control.ResourceScheduler.CopyTripViewModel(trip, options);
		this.data(this.viewResourceScheduleViewModel);
	};

	CopyTripModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	CopyTripModalViewModel.prototype.constructor = CopyTripModalViewModel;

	CopyTripModalViewModel.prototype.positiveClick = function()
	{
		return this.viewResourceScheduleViewModel.apply().then(result =>
		{
			result && this.positiveClose(result);
		});
	};
})();