@import "fontsize";
@systemColor: #D74B3C;

.modal-body.searchSettings-modal {
	.search-setting-control-container {
		height: 100%;
		font-family: "SourceSansPro-Regular";
		.fontSize(1, 2);
		width: 670px;

		&>div {
			float: left;
			width: 325px;
			height: 100%;
		}

		.left-panel {
			float: left;
			padding-top: 40px;
			margin-right: 20px;

			&>div {
				height: 90px;
			}

			.search-result-num-control {
				#result-num-per-datatype-slider {
					height: 100%;
				}
			}

			.display-image-control {
				padding-top: 25px;

				&>div {
					float: left;
				}

				.control-checkbox {
					margin-right: 20px;
				}

				.control-label label {
					display: block;
					font-weight: normal;
				}
			}

			.recent-search-num-control {
				#recent-result-number-slider {
					height: 100%;
				}
			}

			.settings-button-control {
				padding-top: 15px;
				color: @systemColor;
				height: 60px;

				span.control-button {
					font-family: "SourceSansPro-SemiBold";
					color: @systemColor;
					cursor: pointer;
				}
			}

			.control-title {
				margin-bottom: 10px;
				font-family: "SourceSansPro-SemiBold";

				span.count-label {
					float: right;
				}
			}
		}

		.right-panel {
			float: left;

			.data-type-list-container {
				line-height: 32px;
				box-sizing: border-box;
				border: 1px solid #e4e4e4;

				.list-header {
					padding-left: 40px;
					padding-right: 10px;
					height: 32px;
					font-family: "SourceSansPro-SemiBold";
					color: #fff;
					background-color: #4a4a4a;

					.header-lable.data-type {
						float: left;
					}

					.header-lable.enabled {
						float: right;
						width: 50px;
					}
				}

				.list-content {
					.fontSize(1, 1);
					position: relative;
					overflow: hidden;

					&.onDrag {
						.list-item:hover {
							border-color: #fff;

							.item-drag-icon {
								background: none !important;
							}
						}
					}

					.list-item-helper,
					.list-item {
						position: absolute;
						height: 32px;
						width: 323px;
						border-top: 1px solid #fff;
						border-bottom: 1px solid #fff;
						background-color: #fff;
						cursor: pointer;

						&.list-item {
							transition: top 250ms ease;
						}

						&.list-item-helper {
							z-index: 10;
							box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.2);
						}

						&.list-item-helper,
						&:hover {
							border-color: #e4e4e4;

							.item-drag-icon {
								background-image: url(../img/map/thematics/UpDn.svg);
								background-repeat: no-repeat;
								background-position: center center;
								background-size: 8px;
							}
						}

						&.down .item-drag-icon {
							background-image: url(../img/map/thematics/Dn.svg);
						}

						&.list-item:first-child {
							border-top: none;

							&:hover .item-drag-icon {
								background-image: url(../img/map/thematics/Dn.svg);
							}
						}

						&.up .item-drag-icon {
							background-image: url(../img/map/thematics/Up.svg);
						}

						&.list-item:last-child {
							border-bottom: none;

							&:hover .item-drag-icon {
								background-image: url(../img/map/thematics/Up.svg);
							}
						}

						&.onDrag {
							height: 24px !important;
							width: 315px;
							border: 1px dashed #ccc !important;
							margin: 4px;

							.item-drag-icon,
							.item-name,
							.control-checkbox {
								display: none;
							}
						}

						.item-drag-icon {
							float: left;
							width: 40px;
							height: 100%;
						}

						.item-name {
							float: left;
						}

						.control-checkbox {
							float: right;
							margin-top: 8px;
							margin-right: 44px;
							height: 16px;
							width: 16px;

							.checkmark {
								height: 16px;
								width: 16px;

								&:after {
									top: 1px;
									left: 5px;
								}
							}
						}
					}
				}
			}
		}

		.control-checkbox {
			display: block;
			position: relative;
			height: 18px;
			width: 18px;
			cursor: pointer;
			.fontSize(1, 10);
			-webkit-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			user-select: none;

			&:hover input~.checkmark {
				background-color: #e4e4e4;
			}

			input:checked~.checkmark {
				&:after {
					display: block;
				}
			}

			input {
				display: none;
			}

			.checkmark {
				position: absolute;
				top: 0;
				left: 0;
				height: 18px;
				width: 18px;
				background: #fff;
				border: 1px solid #999;
				cursor: pointer;

				&:after {
					content: "";
					position: absolute;
					display: none;
					left: 5px;
					top: 2px;
					width: 5px;
					height: 10px;
					border: solid #333;
					border-width: 0 3px 3px 0;
					-webkit-transform: rotate(45deg);
					-ms-transform: rotate(45deg);
					transform: rotate(45deg);
				}
			}
		}

		.control-slider {
			.slider {
				width: 100%;

				.slider-track {
					height: 2px;
					margin-top: 0px;

					.slider-selection {
						background: @systemColor;
					}
				}

				.slider-handle.min-slider-handle.round {
					background: whitesmoke;
					margin-top: -9px;
					box-shadow: #666666 0px 0px 12px;
				}

				.tooltip {
					display: none;
				}
			}

			label {
				padding-top: 5px;
				.fontSize(1, 1);
				color: gray;
			}
		}
	}
}
