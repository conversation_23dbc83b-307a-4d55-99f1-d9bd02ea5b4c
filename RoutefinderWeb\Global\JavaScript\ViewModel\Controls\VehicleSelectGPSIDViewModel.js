(function()
{
	createNamespace("TF.Control").VehicleSelectGPSIDViewModel = VehicleSelectGPSIDViewModel;

	VehicleSelectGPSIDViewModel.prototype = Object.create(TF.Control.BaseControl.prototype);
	VehicleSelectGPSIDViewModel.prototype.constructor = VehicleSelectGPSIDViewModel;
	function VehicleSelectGPSIDViewModel(positiveClick)
	{
		this.positiveClick = positiveClick;
		this.type = "vehicle";
		this._selectGPSIDModal = null;
		this.obErrorMessage = ko.observable("");
	}

	VehicleSelectGPSIDViewModel.prototype.init = function(viewModel, element)
	{
		var self = this;
		this.columns = this.gridDefinition().Columns;
		var stickyColumns = this.getCurrentSelectedColumns(this.type);
		if (stickyColumns)
		{
			this.columns = stickyColumns;
		}
		// this.originalColumns = this.columns.map(function(item) { return $.extend({}, item); });
		this.originalColumns = this.gridDefinition().Columns;
		this.$element = $(element);
		this.searchGrid = new TF.Grid.LightKendoGrid(this.$element.find(".selectgpsid-container"),
			{
				gridDefinition:
				{
					Columns: this.columns
				},
				kendoGridOption: {
					dataSource: {
						serverPaging: false
					}
				},
				selectable: 'row',
				showOmittedCount: false,
				showSelectedCount: false,
				gridType: self.type,
				isSmallGrid: true,
				url: pathCombine(tf.api.apiPrefix(), "search", "GPSIDModal"),
				showBulkMenu: false,
				showLockedColumn: false,
				setRequestOption: function(options)
				{
					var filter = { FieldName: "ShowMapped", Operator: "EqualTo", Value: self.$onMapped[0].checked ? "True" : "False" };
					if (!options.data.filterSet)
					{
						options.data.filterSet = {
							FilterItems: [],
							FilterSets: null,
							LogicalOperator: 'and'
						}
					}
					options.data.filterSet.FilterItems.push(filter);
					return options;
				},
				onDataBound: function()
				{
					//this.initGridScrollBar(this.availableColGridContainer);
				}.bind(this)
			});
		this.searchGrid.onDoubleClick.subscribe(this.positiveClick.bind(this));
		this.$onMapped = $(".onMapped");
		this.$onMapped.on("change", this.mappedOnOFF.bind(this));
	};

	VehicleSelectGPSIDViewModel.prototype._clearMessage = function()
	{
		this.obErrorMessage("");
	};

	VehicleSelectGPSIDViewModel.prototype.addRemoveColumnClick = function(viewModel, el)
	{
		function initHiddenLockedField(column)
		{
			if (typeof (column.hidden) == "undefined")
			{
				column.hidden = false;
			}
			if (typeof (column.locked) == "undefined")
			{
				column.locked = false;
			}
		}
		var listMoverColumns = this.columns;
		var availableColumns = [];
		var selectedColumns = [];
		var allColumns = this.gridDefinition().Columns;
		availableColumns = allColumns.slice(0);
		allColumns.forEach(function(item)
		{
			item.hidden = true;
			initHiddenLockedField(item);
		});

		for (var i = 0, l = listMoverColumns.length; i < l; i++)
		{
			var existsColumn = null;
			for (var j = 0, jl = allColumns.length; j < jl; j++)
			{
				if (allColumns[j].FieldName == listMoverColumns[i].FieldName)
				{
					existsColumn = listMoverColumns[i];
					var tempColumn = Enumerable.From(availableColumns).Where("$.FieldName=='" + allColumns[j].FieldName + "'").FirstOrDefault();
					tempColumn.FieldName = "";
					allColumns[j] = existsColumn;
					break;
				}
			}
			var columnClone = $.extend({}, listMoverColumns[i]);
			if (!columnClone.DisplayName || $.trim(columnClone.DisplayName) == "")
			{
				columnClone.DisplayName = columnClone.FieldName;
			}
			initHiddenLockedField(columnClone);
			selectedColumns.push(columnClone);
			if (!existsColumn)
			{
				allColumns.unshift(columnClone);
			}
		}

		availableColumns = Enumerable.From(availableColumns).Where("$.FieldName!=''").ToArray();
		var resetColumns = this.originalColumns.map(function(item)
		{
			return Enumerable.From(allColumns).Where("$.FieldName=='" + item.FieldName + "'").FirstOrDefault();
		});
		var self = this;
		tf.modalManager.showModal(
			new TF.Modal.Grid.EditKendoColumnModalViewModel(
				availableColumns,
				selectedColumns,
				resetColumns
			)
		).then(function(editColumnViewModel)
		{
			if (!editColumnViewModel)
			{
				return;
			}
			var enumerable = Enumerable.From(self.originalColumns);
			//reset column setting to default
			editColumnViewModel.selectedColumns = editColumnViewModel.selectedColumns.map(function(item)
			{
				var oc = enumerable.Where("$.FieldName=='" + item.FieldName + "'").FirstOrDefault();
				return oc || item;
			});

			self.columns = editColumnViewModel.selectedColumns;
			// self.originalColumns = editColumnViewModel.selectedColumns;
			self.saveCurrentSelectedColumns(self.type, self.columns);
			self.searchGrid._gridDefinition.Columns = editColumnViewModel.selectedColumns;
			self.searchGrid.rebuildGrid();
		});
	};

	VehicleSelectGPSIDViewModel.prototype.mappedOnOFF = function()
	{
		this.searchGrid.refresh();
	};

	VehicleSelectGPSIDViewModel.prototype.gridDefinition = function()
	{
		return {
			Columns: [
				{
					FieldName: "VendorId",
					DisplayName: "Vendor ID",
					Width: '150px',
					type: "integer"
				},
				{
					FieldName: "GPSVendor",
					DisplayName: "Vendor",
					Width: '150px',
					type: "string"
				},
				{
					FieldName: "GPSVendorName",
					DisplayName: "Vendor Name",
					Width: '150px',
					type: "string"
				},
				{
					FieldName: "DisplayName",
					DisplayName: "Vendor Vehicle Name",
					Width: '150px',
					type: "string"
				},
				{
					FieldName: "Vin",
					DisplayName: "Vendor VIN",
					Width: '150px',
					type: "string"
				},
				{
					FieldName: "TFName",
					DisplayName: "TF Name",
					Width: '150px',
					type: "string"
				},
				{
					FieldName: "TFVIN",
					DisplayName: "TF VIN",
					Width: '150px',
					type: "string"
				}]
		}
	};


	VehicleSelectGPSIDViewModel.prototype.apply = function(viewModel, e)
	{
		this._clearMessage();
		return new Promise(function(resolve, reject)
		{
			var _manageGrid = $(".selectgpsid-container").data("kendoGrid");
			var currentTarget = _manageGrid.dataItem(_manageGrid.selectable.userEvents.currentTarget);
			if (currentTarget == null)
			{
				this.obErrorMessage("One record must be selected.");
				reject();
			}
			else
			{
				this._selectGPSIDModal = currentTarget;
				resolve(this._selectGPSIDModal);
			}
		}.bind(this));
	};

	VehicleSelectGPSIDViewModel.prototype.saveCurrentSelectedColumns = function(gridType, columns)
	{
		return tf.storageManager.save(tf.storageManager.listMoverCurrentSelectedColumns(gridType, tf.authManager.authorizationInfo.authorizationTree.username), columns);
	};

	VehicleSelectGPSIDViewModel.prototype.getCurrentSelectedColumns = function(gridType)
	{
		return tf.storageManager.get(tf.storageManager.listMoverCurrentSelectedColumns(gridType, tf.authManager.authorizationInfo.authorizationTree.username));
	};


})();
