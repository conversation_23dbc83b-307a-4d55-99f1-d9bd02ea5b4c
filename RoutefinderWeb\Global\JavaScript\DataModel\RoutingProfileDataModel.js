(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.RoutingProfileDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	};

	namespace.RoutingProfileDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.RoutingProfileDataModel.prototype.constructor = namespace.RoutingProfileDataModel;

	namespace.RoutingProfileDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: '' },
		{ from: "IsDefault", default: false },
		{ from: "IsActive", default: true },
		{ from: "UseThisSpeed", default: false },
		{ from: "AverageSpeed", default: 20 },
		{ from: "VehicleStopTime", default: 0 },
		{ from: "MaxDistBeforeUturn", default: 0 },
		{ from: "UseSchoolArrivalDepartTime", default: false },
		{ from: "CurbSide", default: false },
		{ from: "ValidateStudentStopStreet", default: false },
		{ from: "UseSpeedLimitForPath", default: false },
		{ from: "DefStopBdyLine", default: '4' },
		{ from: "DefStopBdyFill", default: '1' },
		{ from: "RoutingProfileUsers", default: []}
	];
})();