(function()
{
	var namespace = createNamespace("TF.Control");

	createNamespace('TF.Control').KendoListMoverWithAddControlViewModel = KendoListMoverWithAddControlViewModel;

	KendoListMoverWithAddControlViewModel.prototype = Object.create(TF.Control.KendoListMoverControlViewModel.prototype);
	KendoListMoverWithAddControlViewModel.prototype.constructor = KendoListMoverWithAddControlViewModel;

	function KendoListMoverWithAddControlViewModel(availableData, selectedData, options, ModalName)
	{
		this.obAvailable = ko.observable(options && options.availableTitle ? options.availableTitle : "Available");
		this.obSelected = ko.observable(options && options.selectedTitle ? options.selectedTitle : "Selected");
		TF.Control.KendoListMoverControlViewModel.call(this, availableData, selectedData, options, ModalName);
	}

	KendoListMoverWithAddControlViewModel.prototype.addItem = function()
	{
		this.options.addItem().then(function(item)
		{
			var newDataList = tf.modalManager.currentBaseModalViewModel.newDataList;

			// handle click [save & new] button
			for (var i in newDataList)
			{
				this._addItem(newDataList[i]);
			}
			// handle click [save] button
			this._addItem(item);
		}.bind(this));
	};

	KendoListMoverWithAddControlViewModel.prototype._addItem = function(item)
	{
		if (item)
		{
			this.unSelectedItems.push(item);
			this.obavailableColumns.push(item);
		}
	};
})();
