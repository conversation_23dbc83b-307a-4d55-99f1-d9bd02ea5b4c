﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.StudentExceptionDeletion = StudentExceptionDeletion;

	function StudentExceptionDeletion()
	{
		this.type = 'studentexception';
		this.noNeedRefresh = true;
		namespace.BaseDeletion.apply(this, arguments);
	}

	StudentExceptionDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	StudentExceptionDeletion.prototype.constructor = StudentExceptionDeletion;

	StudentExceptionDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();