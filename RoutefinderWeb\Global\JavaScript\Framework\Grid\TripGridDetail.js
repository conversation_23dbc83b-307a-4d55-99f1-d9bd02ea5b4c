(function()
{
	createNamespace("TF.Grid.Detail").TripGridDetail = TripGridDetail;

	function TripGridDetail(mainGrid)
	{
		this.gridType = "triphistory";
		this.mainGrid = mainGrid;
		this.tripDetailGridColumns = gridDefinition().Columns;
		this.DBID = tf.storageManager.get("datasourceId", true, true) || tf.storageManager.get("datasourceId");
		TF.Grid.Detail.BaseGridDetail.call(this);
		this.tripData = null;
		this.disableChangeColumn = true;
		this.canDragDelete = false;
	}

	TripGridDetail.prototype = Object.create(TF.Grid.Detail.BaseGridDetail.prototype);
	TripGridDetail.prototype.constructor = TripGridDetail;

	TripGridDetail.prototype.create = function(record, onFilter, onDataBound, onColumnChange, onColumnResizing, onColumnResized)
	{
		var self = this;
		var template = $(`<div><div class='kendo-grid detail-grid-${this.gridType}'></div></div>`);
		this.grids.forEach(function(grid)
		{
			grid.createGrid(record, onFilter, onDataBound, onColumnChange, onColumnResizing, onColumnResized, template)
				.then(function(grid)
				{
					grid.record = record;
					self.loadedGrids.push(grid);
				});
		});
		return template;
	};

	TripGridDetail.prototype._getGrids = function()
	{
		var self = this;
		var tripDetailGrid = {
			name: "Trip History Grid",
			type: self.gridType,
			defaultLayoutColumns: self.tripDetailGridColumns,
			createGrid: function(tripData, onFilter, onDataBound, onColumnChange, onColumnResizing, onColumnResized, template)
			{
				var gridType = tripDetailGrid.type;
				self.tripData = tripData;
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "TripResources"), {
					paramData: {
						"dbId": self.DBID,
						"tripId": tripData.Id,
						"@relationships": "vehicle,driver,aide",
						"@sort": "EndDate|desc,StartDate|desc"
					}
				}).then(function(response)
				{
					response.Items.forEach(i =>
					{
						i.StartTime = moment('1899-12-30T' + i.StartTime).format('1899-12-30THH:mm:00');
						i.EndTime = moment('1899-12-30T' + i.EndTime).format('1899-12-30THH:mm:00');
						i.LastUpdated = moment(utcToClientTimeZone(i.LastUpdated)).format('YYYY-MM-DDTHH:mm:ss');
					});

					let bulkmenu = !tf.isViewfinder;

					return self.buildGrid(template.find(".detail-grid-" + gridType),
						{
							gridType: gridType,
							columns: self.tripDetailGridColumns,
							gridState: {},
							gridFilter: self.gridFilter,
							displayFilterRow: self.displayFilterRow,
							selectable: "row",
							dataSource: self.formatData(tripData, (response && response.Items) || []),
							bulkMenu: bulkmenu,
							onFilter: function()
							{
							},
							onDataBound: function()
							{
								onDataBound && onDataBound(tripData.Id);
							},
							onColumnChange: function(data)
							{
								if (data)
								{
									data.name = tripDetailGrid.name;
									data.availableColumns = Enumerable.From(tripDetailGrid.defaultLayoutColumns.map(x => $.extend({}, x))).Where(function(all) { return !Enumerable.From(data.selectedColumns).Any(function(c) { return c.FieldName == all.FieldName; }); }).ToArray();
									onColumnChange && onColumnChange(data);
								}
							},
							onColumnResizing: function(data)
							{
								onColumnResizing && onColumnResizing(data, tripData.Id);
							},
							onColumnResized: function(data)
							{
								onColumnResized && onColumnResized(data, tripData.Id, tripDetailGrid.type);
							}
						});
				});
			}
		};
		return [tripDetailGrid];
	};

	TripGridDetail.prototype.hasData = function(ids, option)
	{
		if (!ids || ids.length == 0)
		{
			return Promise.resolve([]);
		}
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "TripResources"), {
			paramData: {
				"databaseId": this.DBID,
				"tripIds": ids.join(","),
				"excludeSelf": true,
				"@fields": "tripId,isTripLevel"
			}
		}, option).then(function(response)
		{
			if (response && response.Items && response.Items.length > 0)
			{
				return response.Items.reduce(function(accumulator, currentValue)
				{
					var tripId = currentValue.TripId;
					return (currentValue.IsTripLevel || accumulator.includes(tripId)) ? accumulator : accumulator.concat(tripId);
				}, []);
			}
			return [];
		});
	};

	TripGridDetail.prototype.formatData = function(tripData, historyData)
	{
		var output = [], unassigned = "Unassigned";
		if (!tripData || !historyData)
		{
			return "";
		}

		var formatString = function(value)
		{
			if (!value)
			{
				return "";
			}
			return value;
		};

		var tripFormatAide = formatString(tripData.AideId),
			tripFormatDriver = formatString(tripData.DriverId),
			tripFormatVehicle = formatString(tripData.VehicleId);
		const tripFormatStartTime = moment(tripData.StartTime).format('1899-12-30THH:mm:00');
		historyData.forEach(function(item)
		{
			var formatAide = formatString(item.AideId),
				formatDriver = formatString(item.DriverId),
				formatVehicle = formatString(item.VehicleId);
			const formatStartTime = moment(item.StartTime).format('1899-12-30THH:mm:00');
			if ((formatAide === tripFormatAide
				&& formatDriver === tripFormatDriver
				&& formatVehicle === tripFormatVehicle
				&& formatStartTime === tripFormatStartTime)
				|| item.IsTripLevel)
			{
				return;
			}

			if (formatAide === tripFormatAide)
			{
				item.AideName = "";
			}
			else if (!item.AideId && tripData.AideId)
			{
				item.AideName = unassigned;
			}

			if (formatDriver === tripFormatDriver)
			{
				item.DriverName = "";
			}
			else if (!item.DriverId && tripData.DriverId)
			{
				item.DriverName = unassigned;
			}

			if (formatVehicle === "<<not assigned>>" || formatVehicle === tripFormatVehicle)
			{
				item.VehicleName = "";
			}
			else if (!item.VehicleId && tripData.VehicleId)
			{
				item.VehicleName = unassigned;
			}

			output.push(item);
		});

		return output;
	};

	TripGridDetail.prototype.editSubstitutions = function(grid)
	{
		var self = this;
		var tripResource = grid.dataItem(grid.select()),
			options = {
				isEdit: true,
				startDate: moment(tripResource.StartDate).format("YYYY-MM-DDT00:00:00"),
				endDate: moment(tripResource.EndDate).format("YYYY-MM-DDT00:00:00"),
				startTime: tripResource.StartTime,
				endTime: tripResource.EndTime,
				days: {
					Monday: tripResource.Monday === "true",
					Tuesday: tripResource.Tuesday === "true",
					Wednesday: tripResource.Wednesday === "true",
					Thursday: tripResource.Thursday === "true",
					Friday: tripResource.Friday === "true",
					Saturday: tripResource.Saturday === "true",
					Sunday: tripResource.Sunday === "true"
				},
				DriverId: tripResource.DriverId,
				AideId: tripResource.AideId,
				VehicleId: tripResource.VehicleId,
			};
		return tf.modalManager.showModal(
			new TF.Modal.ResourceScheduler.SubstituteResourcesModalViewModel([tripResource.TripId], options)
		).then(function(response)
		{
			const isReset = response && response.isReset;
			if (response === true || isReset)
			{
				var tripPage = ko.dataFor(self.mainGrid.$container.closest(".resizable-doc")[0]);
				tripPage.pageLevelViewModel.popupSuccessMessage(isReset ? 'This record has been reset successfully.' : 'This record has been saved successfully.');
				self.mainGrid.refreshClick();
			}
		}.bind(this));
	};

	TripGridDetail.prototype.resetSubstitutions = function(grid)
	{
		var self = this,
			tripResource = grid.dataItem(grid.select()),
			tripPage = ko.dataFor(self.mainGrid.$container.closest(".resizable-doc")[0]),
			tripEntity = self.tripData;
		tf.promiseBootbox.yesNo("Are you sure you want to reset resources?", "Confirmation Message")
			.then(function(response)
			{
				if (response)
				{
					var data = [
						{ "Id": tripResource.Id, "op": "replace", "path": "/AideId", "value": tripEntity.AideId },
						{ "Id": tripResource.Id, "op": "replace", "path": "/DriverId", "value": tripEntity.DriverId },
						{ "Id": tripResource.Id, "op": "replace", "path": "/VehicleId", "value": tripEntity.VehicleId }];
					tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), "TripResources"), {
						data: data
					}).then(function()
					{
						ga("send", "event", "Action", "Reset Substitutions");
						tripPage.pageLevelViewModel.popupSuccessMessage("This record has been successfully saved.");
						self.mainGrid.refreshClick();
					});
				}
			});
	};

	function gridDefinition()
	{
		function booleanToCheckboxFormatter(item)
		{
			var div = "<input style=\"cursor: default; opacity:1;\" type=\"checkbox\"";
			if (item === "true")
			{
				div = div + " checked disabled>";
				return div;
			}
			return div + " disabled>";
		}
		return {
			Columns: [
				{
					FieldName: "VehicleName",
					DisplayName: "Vehicle Substitute",
					DBName: "VehicleName",
					type: "string",
					width: "150px"
				},
				{
					FieldName: "DriverName",
					DisplayName: "Driver Substitute",
					DBName: "DriverName",
					type: "string",
					width: "150px"
				},
				{
					FieldName: "AideName",
					DisplayName: "Bus Aide Substitute",
					DBName: "AideName",
					width: "150px",
					type: "string"
				},
				{
					FieldName: "StartTime",
					DisplayName: "Start Time",
					DBName: "StartTime",
					width: "170px",
					type: "time"
				},
				{
					FieldName: "EndTime",
					DisplayName: "End Time",
					DBName: "EndTime",
					width: "170px",
					type: "time"
				},
				{
					FieldName: "StartDate",
					DisplayName: "Start Date",
					DBName: "StartDate",
					width: "170px",
					type: "date"
				},
				{
					FieldName: "EndDate",
					DisplayName: "End Date",
					DBName: "EndDate",
					width: "170px",
					type: "date"
				},
				{
					FieldName: "LastUpdated",
					DisplayName: "Last Updated",
					DBName: "LastUpdated",
					width: "170px",
					type: "date",
					template: item =>
					{
						if (!item.LastUpdated)
						{
							return "";
						}

						return moment(item.LastUpdated).format("MM/DD/YYYY hh:mm A");
					}
				},
				{
					FieldName: "Monday",
					DisplayName: "Monday",
					DBName: "Monday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Monday);
					}
				},
				{
					FieldName: "Tuesday",
					DisplayName: "Tuesday",
					DBName: "Tuesday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Tuesday);
					}
				},
				{
					FieldName: "Wednesday",
					DisplayName: "Wednesday",
					DBName: "Wednesday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Wednesday);
					}
				},
				{
					FieldName: "Thursday",
					DisplayName: "Thursday",
					DBName: "Thursday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Thursday);
					}
				},
				{
					FieldName: "Friday",
					DisplayName: "Friday",
					DBName: "Friday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Friday);
					}
				},
				{
					FieldName: "Saturday",
					DisplayName: "Saturday",
					DBName: "Saturday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Saturday);
					}
				},
				{
					FieldName: "Sunday",
					DisplayName: "Sunday",
					DBName: "Sunday",
					width: "120px",
					type: "boolean",
					template: function(item)
					{
						return booleanToCheckboxFormatter(item.Sunday);
					}
				}
			]
		};
	}

})();
