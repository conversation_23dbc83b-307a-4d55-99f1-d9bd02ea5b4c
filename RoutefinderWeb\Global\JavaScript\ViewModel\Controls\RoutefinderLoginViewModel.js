﻿
(function()
{
	createNamespace("TF").RoutefinderLoginViewModel = RoutefinderLoginViewModel;

	RoutefinderLoginViewModel.prototype = Object.create(TF.LoginViewModel.prototype);
	RoutefinderLoginViewModel.prototype.constructor = RoutefinderLoginViewModel;

	function RoutefinderLoginViewModel(clientKey)
	{
		TF.LoginViewModel.call(this, clientKey);

		this.obLoginHeading = ko.computed(() =>
		{
			return this.obIsShowCode() ? "Authentication" : "Login";
		});
	}
})();