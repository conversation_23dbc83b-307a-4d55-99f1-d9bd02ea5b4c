(function()
{
	createNamespace("TF.Modal.Grid").CreateMissingPathResultModalViewModel = CreateMissingPathResultModalViewModel;

	function CreateMissingPathResultModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-sm";
		this.title("Create Missing Paths Result");
		this.contentTemplate("Modal/CreateMissingPathsResult");
		this.buttonTemplate("modal/positivenegative");
		this.obDisableControl(true);
		this.obPositiveButtonLabel('OK');
		this.model = new TF.Control.CreateMissingPathResultViewModel(options, this);
		this.data(this.model);
		this.model.obComplete.subscribe(() =>
		{
			this.obDisableControl(false);
			this.obNegativeButtonVisbile(false);
		});
	}
	CreateMissingPathResultModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	CreateMissingPathResultModalViewModel.prototype.constructor = CreateMissingPathResultModalViewModel;

	CreateMissingPathResultModalViewModel.prototype.positiveClick = function()
	{
		this.positiveClose(true);
	};

	CreateMissingPathResultModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.cancel();
	};

	CreateMissingPathResultModalViewModel.prototype.closeClick = function(viewModel, e)
	{
		if (this.model.obComplete())
		{
			this.positiveClose(true);
		}
		else
		{
			this.cancel();
		}
	};

	CreateMissingPathResultModalViewModel.prototype.cancel = function()
	{
		this.model.cancel().then(() =>
		{
			this.obNegativeButtonVisbile(false);
			this.model.obCancel(true);
		})
	}

})();