(function()
{
	createNamespace("TF.Map").DrawBoundaryTool = DrawBoundaryTool;

	function DrawBoundaryTool(routingMapTool)
	{
		var self = this;
		self.routingMapTool = routingMapTool;
		self.map = self.routingMapTool.routingMapDocumentViewModel._map;
		self.routeState = self.routingMapTool.routingMapDocumentViewModel.routeState;
		self.detailView = self.routingMapTool.routingMapDocumentViewModel.options.detailView;
		self.polygonLayerId = `${self.routingMapTool.routingMapDocumentViewModel.type}PolygonLayer`;
		self.pointLayerId = `${self.routingMapTool.routingMapDocumentViewModel.type}PointLayer`;
		self.symbolHelper = new TF.Map.Symbol();
		self.initToolbar();
	}

	DrawBoundaryTool.prototype.initToolbar = function()
	{
		var self = this;
		self._initHelper();
		self._initLayers();
		self._initDrawTool();
	};

	DrawBoundaryTool.prototype._initHelper = function()
	{
		var self = this,
			arcgis = tf.map.ArcGIS;

		self._arcgis = arcgis;

		self._georegionPolygonSymbol = {
			type: "simple-fill",
			color: [18, 89, 208, 0.3],
			outline: {
				color: [18, 89, 208, 0.6],
				width: 1,
			}
		};
	};

	DrawBoundaryTool.prototype._initLayers = function()
	{
		var self = this,
			GraphicsLayer = self._arcgis.GraphicsLayer;

		self._drawPolygonLayer = new GraphicsLayer({ id: `${self.routingMapTool.routingMapDocumentViewModel.type}DrawPolygonLayer` });
		self.map.add(self._drawPolygonLayer, 1);
	};

	DrawBoundaryTool.prototype._initDrawTool = function()
	{
		var self = this;
		self._sketchVM = new self._arcgis.SketchViewModel({
			view: self.map.mapView,
			layer: self._drawPolygonLayer,
			updateOnGraphicClick: false,
			defaultUpdateOptions: { // set the default options for the update operations
				toggleToolOnClick: false // only reshape operation will be enabled
			},
			polygonSymbol: self._georegionPolygonSymbol,
			updatePolygonSymbol: new TF.Map.Symbol().editPolygonSymbol()
		});

		self._sketchVM.on("create", function(e)
		{
			self._drawCompleteHandler(e);
		});
		self._sketchVM.on("update", async function(e)
		{
			await self._graphicUpdateHandler(e);
		});
	};

	DrawBoundaryTool.prototype._activateDrawTool = function(type)
	{
		if (type === "draw")
		{
			this._sketchVM.create("polygon", { mode: "freehand" });
		}
		else if (type === "polygon")
		{
			this._sketchVM.create("polygon", { mode: "click" });
		} else
		{
			this._sketchVM.create(type);

		}
	};

	DrawBoundaryTool.prototype._deactivateDrawTool = function()
	{
		if (this._sketchVM.state === "active")
		{
			this._sketchVM.cancel();
		} else
		{
			this._sketchVM.complete();
		}
	};

	DrawBoundaryTool.prototype.getSymbol = function()
	{
		var self = this;

		if (self.routingMapTool.routingMapDocumentViewModel.type === "georegion")
		{
			var geoRegionTypeId = self.detailView.getGeoRegionTypeId();
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "georegiontypes?Id=" + geoRegionTypeId)).then(function(response)
			{
				var georegionPolygonSymbol = self.symbolHelper.georegionPolygon(response.Items[0]);
				return Promise.resolve(georegionPolygonSymbol);
			});
		}
		else if (self.routingMapTool.routingMapDocumentViewModel.type === "parceladdresspoint")
		{
			return Promise.resolve(self.symbolHelper.PolygonCreateSymbol());
		}
	};

	DrawBoundaryTool.prototype.startDraw = function(type, status)
	{
		var self = this;

		self.routingMapTool.startSketch("DrawBoundaryTool");
		self._sketchVM.layer = self._drawPolygonLayer;
		setTimeout(function()
		{
			self._activateDrawTool(type);
			self.currentStatus = status ? status : "create";
		}, 100);

		if (self.map.findLayerById(self.polygonLayerId).graphics.items.length > 0)
		{
			self.map.findLayerById(self.polygonLayerId).graphics.items[0].symbol = new TF.Map.Symbol().editPolygonSymbol()
		}

		this.bindEscEvent();
	};

	DrawBoundaryTool.prototype.stopDraw = function()
	{
		this.routingMapTool && this.routingMapTool.stopSketch("DrawBoundaryTool");
		this.stop();
		this._resetParcelPointSymbol();
	};

	DrawBoundaryTool.prototype.transform = function()
	{
		var self = this;
		self._sketchVM.layer = self.map.findLayerById(self.polygonLayerId);
		setTimeout(function()
		{
			self._sketchVM.update(self.map.findLayerById(self.polygonLayerId).graphics.items[0], {
				tool: "transform"
			});
			self.currentStatus = "update";
		}, 100);

	};

	DrawBoundaryTool.prototype.reshape = function()
	{
		var self = this;
		self._sketchVM.layer = self.map.findLayerById(self.polygonLayerId);
		setTimeout(function()
		{
			self._sketchVM.update(self.map.findLayerById(self.polygonLayerId).graphics.items, {
				tool: "reshape"
			});
			self.currentStatus = "update";
		}, 100);

	};

	DrawBoundaryTool.prototype.addRegion = function(type)
	{
		var self = this;
		self._sketchVM.layer = self._drawPolygonLayer;
		self.startDraw(type, "addRegion");

	};

	DrawBoundaryTool.prototype.removeRegion = function(type)
	{
		var self = this;
		self._sketchVM.layer = self._drawPolygonLayer;
		self.startDraw(type, "removeRegion");

	};

	DrawBoundaryTool.prototype.redrawRegion = function(type)
	{
		var self = this;
		self._sketchVM.layer = self._drawPolygonLayer;
		self.startDraw(type, "redrawRegion");

	};

	DrawBoundaryTool.prototype.delete = async function()
	{
		var self = this;
		self.map.findLayerById(self.polygonLayerId).removeAll();
		await self.modifyRecordToNewBoundary(false);
		PubSub.publish("clear_ContextMenu_Operation");
	};

	DrawBoundaryTool.prototype.bindEscEvent = function()
	{
		var self = this;
		tf.documentEvent.bind("keydown.drawGeoregion", self.routeState, function(e)
		{
			if (e.key === "Escape")
			{
				self.stopDraw();
			}
		});
	};

	DrawBoundaryTool.prototype._drawCompleteHandler = function(e)
	{
		var self = this;
		const hasPopups = this.routingMapTool.routingMapDocumentViewModel.gridMapPopup || (this.routingMapTool.routingMapDocumentViewModel.hasMapPopups && this.routingMapTool.routingMapDocumentViewModel.hasMapPopups());
		if (hasPopups)
		{
			this.routingMapTool.routingMapDocumentViewModel.enableMouseEvent();
		}

		if (e.state === "complete")
		{
			if (e.graphic)
			{
				self.modifyGraphicOnMap(e.graphic.geometry).then(async function(geometry)
				{
					await self.modifyRecordToNewBoundary(geometry);
					PubSub.publish("clear_ContextMenu_Operation");
					self.stop();
				});
			}
		}
	};

	DrawBoundaryTool.prototype._graphicUpdateHandler = async function(e)
	{
		var self = this;
		if (e.state === "complete")
		{
			if (e.graphics)
			{
				if (e?.graphics?.[0]?.geometry?.type === "point")
				{
					self.modifyBoundaryGraphicOfPointOnMap(e.graphics[0].geometry);
					const boundaryGeometry = this.map.findLayerById(self.polygonLayerId)?.graphics?.items?.[0]?.geometry;
					if (boundaryGeometry)
					{
						await self.modifyRecordToNewBoundary(boundaryGeometry);
					}
					else
					{
						await self._updateDetailViewPointFields();
						this.detailView.obEditing(true);
					}
					self._resetParcelPointSymbol();
				}
				else
				{
					self.modifyGraphicOnMap(e.graphics[0].geometry).then(async function(geometry)
					{
						await self.modifyRecordToNewBoundary(geometry);
						PubSub.publish("clear_ContextMenu_Operation");
					});
				}
				self.stop();
			}
		}
		else if (e.state === "cancel")
		{
			PubSub.publish("clear_ContextMenu_Operation");
			self.stop();
		}
	};

	DrawBoundaryTool.prototype.stop = function()
	{
		var self = this;
		PubSub.publish("clear_ContextMenu_Operation");
		self._deactivateDrawTool();
		tf.documentEvent.unbind("keydown.drawGeoregion", this.routeState);
		return self.getSymbol().then(function(symbol)
		{
			if (self.map.findLayerById(self.polygonLayerId).graphics.items[0])
			{
				self.map.findLayerById(self.polygonLayerId).graphics.items[0].symbol = symbol;
			}
			self._drawPolygonLayer.removeAll();
		})
	}


	DrawBoundaryTool.prototype.modifyBoundaryGraphicOfPointOnMap = function(pointGeometry)
	{
		const boundaryGraphic = this.map.findLayerById(this.polygonLayerId)?.graphics?.items?.[0];
		if (boundaryGraphic && !tf.map.ArcGIS.geometryEngine.intersects(boundaryGraphic.geometry, pointGeometry))
		{
			boundaryGraphic.geometry = this._createFinger(pointGeometry, boundaryGraphic.geometry);
		}
	};

	DrawBoundaryTool.prototype.modifyGraphicOnMap = function(geometry)
	{
		var self = this;
		return self.getSymbol().then(function(symbol)
		{
			self.graphic = self.map.findLayerById(self.polygonLayerId).graphics.items[0];
			if (self.graphic)
			{
				self.graphic.geometry = self.getGeometry(geometry);
			}
			else
			{
				geometry = self.getGeometry(geometry);
				self.addGraphic(geometry, symbol);
				self._resetParcelPointSymbol();
			}
			self.graphic.symbol = symbol;
			self._drawPolygonLayer.removeAll();
			return Promise.resolve(self.map.findLayerById(self.polygonLayerId).graphics.items[0].geometry)
		})
	};

	DrawBoundaryTool.prototype.getGeometry = function(polygon)
	{
		var self = this, result = polygon,
			pointLayer = self.map.findLayerById(`${self.routingMapTool.routingMapDocumentViewModel.type}PointLayer`),
			point = pointLayer.graphics.items[0],
			boundaryLayer = self.map.findLayerById(self.polygonLayerId),
			boundary = boundaryLayer.graphics.items[0];
		if (!polygon)
		{
			return new tf.map.ArcGIS.Polygon({ rings: [], spatialReference: tf.map.ArcGIS.SpatialReference.WebMercator });
		}
		if (self.currentStatus === "create" || self.currentStatus === "update")
		{
			if (point && !tf.map.ArcGIS.geometryEngine.intersects(polygon, point.geometry))
			{
				result = self._createFinger(point.geometry, polygon)
			}
		} else if (self.currentStatus === "addRegion")
		{
			if (!tf.map.ArcGIS.geometryEngine.intersects(polygon, boundary.geometry))
			{
				result = boundary.geometry;
			} else
			{
				result = tf.map.ArcGIS.geometryEngine.union(polygon, boundary.geometry)
			}
		} else if (self.currentStatus === "removeRegion")
		{
			if (!tf.map.ArcGIS.geometryEngine.intersects(polygon, boundary.geometry))
			{
				result = boundary.geometry;
			} else if (point && tf.map.ArcGIS.geometryEngine.intersects(polygon, point.geometry))
			{
				result = boundary.geometry;
			} else
			{
				result = tf.map.ArcGIS.geometryEngine.difference(boundary.geometry, polygon)
			}
		}
		else if (self.currentStatus === "redrawRegion")
		{
			if (this.routingMapTool.routingMapDocumentViewModel.type === "parceladdresspoint"
				&& !tf.map.ArcGIS.geometryEngine.intersects(polygon, point.geometry))
			{
				result = self._createFinger(point.geometry, polygon)
			}
			else
			{
				if (!point || tf.map.ArcGIS.geometryEngine.intersects(polygon, point.geometry))
				{
					result = polygon;
				} else
				{
					result = boundary.geometry;
				}
			}
		}
		return point ? self._cutResultHandler(result, point.geometry) : result;
	};

	DrawBoundaryTool.prototype._cutResultHandler = function(cutResult, centroid)
	{
		var self = this;
		if (cutResult.rings.length > 1 && centroid)
		{
			var polygonWithCentroid = self._findRingwithPoint(cutResult.rings, centroid);

			var removedids = [], remove = [], keep = [];
			cutResult.rings.map(function(ring, index)
			{
				var polygon = polygon = new tf.map.ArcGIS.Polygon({
					type: "polygon",
					rings: [ring],
					spatialReference: self.map.mapView.spatialReference
				});
				if (!tf.map.ArcGIS.geometryEngine.contains(polygonWithCentroid, polygon))
				{
					removedids.push(index);
					remove.push(polygon);
				}
				else
				{
					keep.push(polygon);
				}
			});
			for (var i = removedids.length - 1; i >= 0; i--)
			{
				var isremove = true;
				for (var j = 0; j < keep.length; j++)
				{
					if (tf.map.ArcGIS.geometryEngine.contains(keep[j], remove[i]))
					{
						isremove = false;
						break;
					}
				}
				if (isremove)
				{
					cutResult.removeRing(removedids[i]);
				}
			}
		}
		return cutResult;
	};

	DrawBoundaryTool.prototype._findRingwithPoint = function(rings, centroid)
	{
		var self = this,
			polygon, minimalRegoin, result = [];
		rings.map(function(ring)
		{
			polygon = new tf.map.ArcGIS.Polygon({
				type: "polygon",
				rings: [ring],
				spatialReference: self.map.mapView.spatialReference
			});
			if (tf.map.ArcGIS.geometryEngine.intersects(polygon, centroid))
			{
				result.push(polygon);
			}
		});
		if (result.length > 1)
		{
			for (var i = 0; i < result.length - 1; i++)
			{
				if (self._arcgis.geometryEngine.intersects(result[i], result[i + 1]))
				{
					minimalRegoin = result[i + 1];
				} else
				{
					minimalRegoin = result[i];
				}
			}
		} else if (result.length === 1)
		{
			minimalRegoin = result[0];
		}
		return minimalRegoin;
	};

	DrawBoundaryTool.prototype._createFinger = function(point, polygon)
	{
		var self = this;
		var nearestPoint = tf.map.ArcGIS.geometryEngine.nearestCoordinate(polygon, point).coordinate,
			line = new tf.map.ArcGIS.Polyline({ spatialReference: self.map.mapView.spatialReference, paths: [[[point.x, point.y], [nearestPoint.x, nearestPoint.y]]] }),
			distance = TF.Helper.MapHelper.convertPxToDistance(self.map, tf.map.ArcGIS, 5),
			buffer = tf.map.ArcGIS.geometryEngine.buffer(line, distance, "meters");

		return tf.map.ArcGIS.geometryEngine.union([tf.map.ArcGIS.geometryEngine.simplify(buffer), tf.map.ArcGIS.geometryEngine.simplify(polygon)]);
	}

	DrawBoundaryTool.prototype._resetParcelPointSymbol = function()
	{
		if (this.routingMapTool.routingMapDocumentViewModel.type !== "parceladdresspoint")
		{
			return;
		}

		const pointLayer = this.map.findLayerById(`${this.routingMapTool.routingMapDocumentViewModel.type}PointLayer`),
			pointGraphic = pointLayer.graphics.items[0];

		if (pointGraphic)
		{
			pointGraphic.symbol = {
				...this.symbolHelper.drawPointSymbol(), size: 10, outline: {
					color: [255, 255, 0, 1],
					width: 3
				}
			};
			PubSub.publish("clear_ContextMenu_Operation");
		}
	}

	DrawBoundaryTool.prototype.addGraphic = function(geometry, symbol)
	{
		var graphic = new tf.map.ArcGIS.Graphic({
			geometry: geometry,
			symbol: symbol,
			attributes: {
				id: this.record ? this.record.Id : TF.createId(),
				type: this.type
			}
		});

		if (this.routingMapTool.routingMapDocumentViewModel.type === "parceladdresspoint")
		{
			const pointLayer = this.map.findLayerById(`parceladdresspointPointLayer`);
			// make the center as the point if no parcel point exists
			if (pointLayer.graphics.items.length === 0)
			{
				pointLayer.add(new tf.map.ArcGIS.Graphic({
					geometry: geometry.centroid,
					symbol: this.symbolHelper.drawPointSymbol(),
					attributes: {
						type: "parceladdresspoint"
					}
				}));
			}
			else
			{
				// assign point's id as parcel id to avoid conflict with popup 
				graphic.attributes.id = pointLayer.graphics.items[0].attributes.id;
			}
		}

		this.map.findLayerById(this.polygonLayerId).add(graphic);
		this.graphic = graphic;
	};

	DrawBoundaryTool.prototype.modifyRecordToNewBoundary = async function(geometry)
	{
		var self = this;
		this.detailView.obEditing(true);

		if (!self.detailView || !self.detailView.fieldEditorHelper)
		{
			return;
		}


		if (self.routingMapTool.routingMapDocumentViewModel.type === "parceladdresspoint")
		{
			self._updateDetailViewField("Boundary", geometry === false ? null : geometry);
			await self._updateDetailViewPointFields();
			self._updateDetailViewField("Type", geometry === false ? "Address Point" : "Parcel", true);
		} else
		{
			self._updateDetailViewField("Shape", geometry === false ? null : geometry);
		}
	};

	DrawBoundaryTool.prototype.movePoint = function()
	{
		this._sketchVM.layer = this.map.findLayerById(this.pointLayerId);
		const pointGraphic = this.map.findLayerById(this.pointLayerId).graphics.items[0];
		pointGraphic.symbol = this.symbolHelper.editPointSymbol();
		setTimeout(() =>
		{
			this._sketchVM.update(pointGraphic, {
				tool: "move"
			});
			this.currentStatus = "move-point";
			this.bindEscEvent();
		}, 100);
	};

	DrawBoundaryTool.prototype.addRegionToPoint = function(geometryType)
	{
		const self = this;
		const graphic = self.map.findLayerById(self.pointLayerId)?.graphics?.items?.[0];
		if (!graphic)
		{
			return;
		}

		self._oldPointGraphic = graphic;
		graphic.symbol = self.symbolHelper.editPointSymbol();
		self.startDraw(geometryType);
	}

	DrawBoundaryTool.prototype._updateDetailViewPointFields = async function()
	{
		const existingPointGeometry = this.map.findLayerById(this.pointLayerId)?.graphics?.items?.[0]?.geometry;
		if (existingPointGeometry)
		{
			const addressInfo = await TF.RoutingMap.GeocodeHelper.getAddressInfoAsync(existingPointGeometry);

			this._updateDetailViewField("XCoord", existingPointGeometry.x);
			this._updateDetailViewField("YCoord", existingPointGeometry.y);
			this._updateDetailViewField("City", addressInfo?.City, true);
			this._updateDetailViewField("State", addressInfo?.State, true);
			this._updateDetailViewField("PostalCode", addressInfo?.Zip, true);
		}
	}

	DrawBoundaryTool.prototype._updateDetailViewField = function(fieldName, value, refreshDisplay = false)
	{
		this?.detailView?.fieldEditorHelper?._onNonUDFEditorApplied({
			lockName: fieldName,
			errorMessages: null,
			fieldName: fieldName,
			recordValue: value,
			relationshipKey: undefined,
			text: undefined,
		});

		if (refreshDisplay)
		{
			this?.detailView?.fieldEditorHelper?._updateGeneralFieldsContent(fieldName, value, { updateAll: true });
		}
	}


	DrawBoundaryTool.prototype.dispose = function()
	{
		tf.documentEvent.unbind("keydown.drawGeoregion", this.routeState);
		this._deactivateDrawTool();
		this.routingMapTool = null;
		this.map = null;
		this.routeState = null;
		this.record = null;
		this.type = null;
		this.currentStatus = "";
		this.layer = null;
		this.detailView = null;
	};
})();
