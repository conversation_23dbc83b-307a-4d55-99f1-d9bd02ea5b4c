(function()
{
	var timeControlHelper = TF.TimeRangeWithWeekDayControlHelper;
	var listMoverControlHelper = TF.ListMoverControlHelper;
	var listGPSEventTypeHelper = TF.ListGPSEventTypeHelper;

	createNamespace('TF.Control').ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel = ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel;
	function ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel(selectedData, options)
	{
		selectedData = null;

		var self = this;
		self.options = options;
		self.validationMessage = null;
		self.pageLevelViewModel = new TF.PageLevel.ListMoverForListFilterWithSelectDateTimeRangeControlPageLevelViewModel(self);

		var userPreferenceKey = options.parentPageName + '.listFilterWithSelectDateTimeRange';

		timeControlHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		timeControlHelper.initFilterTimeRange.bind(self)({
			startDate: moment(),
			endDate: moment(),
			startTime: moment().format('YYYY-MM-DD 00:00:00'),
			endTime: moment().format('YYYY-MM-DD HH:mm:00')
		});
		timeControlHelper.initFilterWeekdays.bind(self)();

		listMoverControlHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listGPSEventTypeHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);

		var cachedSelectedVehicles = TF.ListMoverControlHelper.getUserPreferenceData.call(this);
		self.obHasCachedSelecedVehicles = ko.observable(cachedSelectedVehicles);

		// query gps event type by listConfig
		self.obSelectedGPSEventType = ko.observableArray();
		self.getSelectedEventTypes('GPSEventType', TF.ListFilterDefinition.ListFilterTemplate.GPSEventType)
			.then(function(result)
			{
				result.Items.sort(function(a, b) { return a.DisplayText.localeCompare(b.DisplayText); });
				self.obSelectedGPSEventType(result.Items);
			});

		self.obAllGPSEventsCount = ko.observable(0);
		self.obAllGPSEvents = ko.observableArray([]);
		getAllGPSEvents().then(function(result) 
		{
			var totalCount = result.TotalRecordCount;
			self.obAllGPSEventsCount(totalCount);
			self.obAllGPSEvents(result.Items || []);
		}.bind(self));

		self.obSelectedGPSEventTypeDisplay = ko.computed(function()
		{
			return self.obSelectedGPSEventType().length !== self.obAllGPSEventsCount() ?
				self.obSelectedGPSEventType() :
				[{ EventTypeName: 'All' }];
		});

		// query gps event type by listConfig
		self.obSelectedVehicles = ko.observableArray();
		self.getSelectedVehicles(TF.ListFilterDefinition.ListFilterTemplate.BusfinderHistoricalVehicle)
			.then(function(result)
			{
				result.Items.sort(function(a, b) { return a.BusNum.localeCompare(b.BusNum); });
				self.obSelectedVehicles(result.Items);
			});

		self.obErrorMessageDivIsShow = ko.observable(false);
		self.obErrorMessage = ko.observable('');
		self.obValidationErrors = ko.observableArray([]);
		self.obErrorMessageTitle = ko.observable("Error Occurred");
		self.obErrorMessageDescription = ko.observable("The following error occurred.");

		self.pages = {
			filterGPSEvents: 0,
			selectDaysOfTheWeek: 1,
			selectEventTypes: 2,
			selectVehicles: 3
		};
		self.obStatus = ko.observable(self.pages.filterGPSEvents);

		self.obTextDaysOfTheWeek = ko.computed(self.computedTextDaysOfTheWeek.bind(self), self);
		self.obTextEventTypes = ko.computed(self.computedTextEventTypes.bind(self), self);
		self.obTextVehicles = ko.computed(self.computedTextVehicles.bind(self), self);


		self.obEventTypeSearchFilter = ko.observable("");
		self.obFilterEventTypes = ko.observableArray([]);
		self.obCanFilterEventTypes = ko.computed(self.computedCanFilterEventTypes, self);
		self.obFilterEventTypesTmp = ko.computed(self.computedFilterEventTypesTmp, self);
		self.obCanFilterEventTypesTmp = ko.computed(self.computedCanFilterEventTypesTmp, self);
		self.obHasSelectedAllGPSEventTypeInSubModal = ko.computed(function()
		{
			return self.obFilterEventTypes().length == self.obAllGPSEventsCount();
		});

		self.obOnRoadFilterApplied = ko.observable(false);
		self.obVehicleSearchFilter = ko.observable("");
		self.obFilterVehicles = ko.observableArray([]);
		self.obCanFilterVehicles = ko.observableArray([]);

		self.obFilterVehicles.subscribe(self.computedFilterVehicles, self);
		self.obOnRoadFilterApplied.subscribe(self.computedFilterVehicles, self);

		self.obTempAllVehicles = ko.observableArray([]);
		self.obAllVehicles = ko.observableArray([]);
		self.obAllOnRoadVehicles = ko.observableArray([]);
		self.obFilterVehiclesTmp = ko.computed(self.computedFilterVehiclesTmp, self);
		self.obCanFilterVehiclesTmp = ko.computed(self.computedCanFilterVehiclesTmp, self);

		self.obHasSelectedAllVehicleInSubModal = ko.computed(function()
		{
			return self.obFilterVehicles().length == self.obTempAllVehicles().length;
		});

		self.obFilterWeekDays = ko.observableArray(weekDayNameList);
		self.obWeekDaysSearchFilter = ko.observable("");
		this.obWeekDaysSearchFilter.subscribe(this.searchWeekDaysFilter.bind(this));

		self.obMondayCheckedTmp = ko.observable(self.obMondayChecked());
		self.obTuesdayCheckedTmp = ko.observable(self.obTuesdayChecked());
		self.obWednesdayCheckedTmp = ko.observable(self.obWednesdayChecked());
		self.obThursdayCheckedTmp = ko.observable(self.obThursdayChecked());
		self.obFridayCheckedTmp = ko.observable(self.obFridayChecked());
		self.obSaturdayCheckedTmp = ko.observable(self.obSaturdayChecked());
		self.obSundayCheckedTmp = ko.observable(self.obSundayChecked());

		self.obMondayVisible = ko.observable(true);
		self.obTuesdayVisible = ko.observable(true);
		self.obWednesdayVisible = ko.observable(true);
		self.obThursdayVisible = ko.observable(true);
		self.obFridayVisible = ko.observable(true);
		self.obSaturdayVisible = ko.observable(true);
		self.obSundayVisible = ko.observable(true);

		setTimeout(function()
		{
			self.initFilterChangedEvents();
		}, 1000);
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.onTapSelectVehicleAll = function(viewModel, event, operator)
	{
		var self = this;

		if (!operator)
		{
			if (self.obHasSelectedAllVehicleInSubModal())
				operator = 'deselectAll';
			else
				operator = 'selectAll';
		}

		var $currentTarget = $(event.currentTarget);
		var $wrapper = $currentTarget.closest('.mobile-modal-content-body.has-search');

		self.animateSelectAllItem(
			{
				currentTarget: $currentTarget,
				wrapper: $wrapper,
				callback: function()
				{
					// $wrapper.animate({opacity: 0}, 300, function()
					// {
					if (operator === 'selectAll')
					{
						self.obFilterVehicles.removeAll();
						self.obFilterVehicles(self.obTempAllVehicles().slice());
					}
					else if (operator === 'deselectAll')
					{
						self.obFilterVehicles.removeAll();
					}

					//$wrapper.animate({opacity: 1}, 300);

					// });
					//);
				}
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.onTapSelectEventAll = function(viewModel, event, operator)
	{
		var self = this;

		if (!operator)
		{
			if (self.obHasSelectedAllGPSEventTypeInSubModal())
				operator = 'deselectAll';
			else
				operator = 'selectAll';
		}

		var $currentTarget = $(event.currentTarget);
		var $wrapper = $currentTarget.closest('.mobile-modal-content-body.has-search');

		self.animateSelectAllItem(
			{
				currentTarget: $currentTarget,
				wrapper: $wrapper,
				callback: function()
				{
					if (operator === 'selectAll')
					{
						self.obFilterEventTypes.removeAll();
						self.obFilterEventTypes(self.obAllGPSEvents().slice());
					}
					else if (operator === 'deselectAll')
					{
						self.obFilterEventTypes.removeAll();
					}
				}
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.initEventChangedEvents = function()
	{
		var self = this;
		self.eventFilterChange = false;
		self.obFilterEventTypes.subscribe(function()
		{
			var self = this;
			if (!self.eventFilterChange)
				self.eventFilterChange = true;
		}, self);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.initVehicleChangedEvents = function()
	{
		var self = this;
		self.vehicleFilterChange = false;
		self.obFilterVehicles.subscribe(function()
		{
			var self = this;
			if (!self.vehicleFilterChange)
				self.vehicleFilterChange = true;
		}, self);

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.initWeekDayChangedEvents = function()
	{
		var self = this;
		self.weekDayFilterChange = false;

		self.obMondayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
		self.obTuesdayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
		self.obWednesdayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
		self.obThursdayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
		self.obFridayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
		self.obSaturdayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
		self.obSundayCheckedTmp.subscribe(self.updateWeekDayFilterChangedFlag, self);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.updateWeekDayFilterChangedFlag = function()
	{
		var self = this;
		if (!self.weekDayFilterChange)
			self.weekDayFilterChange = true;
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.initFilterChangedEvents = function()
	{
		var self = this;
		self.filterChanged = false;
		self.obTextDaysOfTheWeek.subscribe(self.updateFilterChangedFlag, self);
		self.obTextEventTypes.subscribe(self.updateFilterChangedFlag, self);
		self.obTextVehicles.subscribe(self.updateFilterChangedFlag, self);
		self.obStartDate.subscribe(self.updateFilterChangedFlag, self);
		self.obStartTime.subscribe(self.updateFilterChangedFlag, self);
		self.obEndDate.subscribe(self.updateFilterChangedFlag, self);
		self.obEndTime.subscribe(self.updateFilterChangedFlag, self);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.updateFilterChangedFlag = function()
	{
		var self = this;
		if (!self.filterChanged)
			self.filterChanged = true;
	};

	function getAllGPSEvents()
	{
		var url = pathCombine(tf.api.apiPrefixWithoutDatabase(), "gpsevents", "geteventtypes");
		var option = { data: {}, paramData: {} };
		return tf.ajax.post(url, option);
	}

	// function getGPSEventsCount()
	// {
	// 	return getAllGPSEvents()
	// 		.then(function(result)
	// 		{
	// 			return result.TotalRecordCount;
	// 		}.bind(this));
	// }

	//ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.constructor = ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel;

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.columnSources = TF.ListFilterDefinition.ColumnSource;

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.initGridScrollBar = function(container)
	{
		//need check soon.
		var $gridContent = container.find('.k-grid-content');
		$gridContent.css({
			'overflow-y': 'auto'
		});

		if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
		{
			$gridContent.find('colgroup col:last').css({
				width: 137
			});
		}
		else
		{
			$gridContent.find('colgroup col:last').css({
				width: 120
			});
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.apply = function()
	{
		var self = this;
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(self);

		return self.trySave()
			.then(function(valid)
			{
				if (!valid)
				{
					return Promise.reject();
				}
				else
				{
					// return Promise.reject();
					timeControlHelper.saveUserPreference.call(self);
					listMoverControlHelper.saveUserPreference.call(self, null, null, self.obSelectedVehicles());
					listGPSEventTypeHelper.saveUserPreference.call(self, self.obSelectedGPSEventType());

					// return Promise.resolve(
					// 	self.buildResult(self.selectedData, self.obStartDate(), self.obEndDate(), self.obStartTime(), self.obEndTime())
					// );
					return Promise.resolve(true);
				}
			}.bind(self));
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.trySave = function(disablePubsub)
	{
		var self = this;
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
					return Promise.reject();
				else
					return self.tooManyRecordsValidate.bind(self)();
			});
		// return Promise.resolve(true);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.tooManyRecordsValidate = function()
	{
		if (!this.options.tooManyRecordsValidate)
			return Promise.resolve(true);

		return this.options.tooManyRecordsValidate.bind(this)();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.dispose = function()
	{
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.dispose.call(this);
		this.validationMessage.remove();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.cancel = function()
	{
		// var self = this;
		// var cachedSelectedVehicles = TF.ListMoverControlHelper.getUserPreferenceData.call(this);
		// if (!cachedSelectedVehicles)
		// {
		// 	// this.pageLevelViewModel.popupErrorMessage("At least one vehicles must be selected");
		// 	return Promise.reject();
		// }
		// else
		// {
		return new Promise(function(resolve, reject)
		{
			resolve({
				filterChanged: self.filterChanged,
				resultValue: false
			});
		}.bind(this));
		// }

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype._settingFilterChange = function()
	{
		// timeControlHelper.saveUserPreference.bind(this)();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.init.call(this, viewModel, el);
		// add setTimeout for make validationInitialize later than datePicker controls init finished
		setTimeout(function()
		{
			if (!self.obHasCachedSelecedVehicles())
			{
				var $container = $(el).closest('.modal-content');
				$container.find('.modal-header button.close').remove();
				$container.find('.modal-header button.close').remove();
				$container.find('.modal-footer button.btn-link').remove();
			}

			viewModel.validationInitialize(viewModel, el);

		}.bind(this), 1000);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.validationInitialize = function(viewModel, el)
	{
		var self = this;
		self._$form = $(el);

		if (self._$form.closest(".tfmodal-container").length > 0)
		{
			self.validationMessage = self._$form.closest(".tfmodal-container").find(".page-level-message-container");
			self.validationMessage.css("z-index", self._$form.closest(".tfmodal.modal").css("z-index"));
			$("body").append(self.validationMessage);
		}

		var validatorFields = {};
		var isValidating = false;
		validatorFields.startDate = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				}
			}
		};

		validatorFields.endDate = {
			trigger: "blur change",
			validators: {

				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				}
			}
		};

		validatorFields.startTime = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'invalid time',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value, 'h:mm A', true);
						return m.isValid();
					}.bind(this)
				}
			}
		};

		validatorFields.endTime = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'invalid time',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value, 'h:mm A', true);
						return m.isValid();
					}.bind(this)
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			})
			.on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.selectGPSEventType = function()
	{
		var self = this;

		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.GPSEventType;
		var selectedItems = self.obSelectedGPSEventType();

		return tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		)
			.then(function(selectedFilterItems)
			{
				if (selectedFilterItems !== false)
					self.obSelectedGPSEventType(selectedFilterItems);
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.selectVehicle = function()
	{
		var self = this;

		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.BusfinderHistoricalVehicle;
		listFilterTemplate.DisplayFilterTypeName = "Vehicles";
		var selectedItems = self.obSelectedVehicles();

		return tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		)
			.then(function(selectedFilterItems)
			{
				if (selectedFilterItems !== false)
					self.obSelectedVehicles(selectedFilterItems);
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.eventTypeFormatter = function(item)
	{
		return item.EventTypeName;
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.vehicleFormatter = function(item)
	{
		if (item.Gpsid)
			return String.format('{0} ({1})', item.BusNum, item.Gpsid);
		else
			return String.format('{0}', item.BusNum);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.getSelectedEventTypes = function(fieldName, listFilterTemplate)
	{
		var self = this;
		var selectedData = listGPSEventTypeHelper.getUserPreferenceData.call(self);

		if (!selectedData || !selectedData.ListFilterIds || !selectedData.ListFilterIds.length)
		{
			return getAllGPSEvents();
		}
		else
		{
			var requestUrl = listFilterTemplate.getUrl();
			var option = {
				data: {
					FilterClause: "",
					IdFilter: { IncludeOnly: selectedData.ListFilterIds }
				}
			};
			return tf.promiseAjax.post(requestUrl, option);
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.getSelectedVehicles = function(listFilterTemplate)
	{
		var self = this;
		var selectedData = listMoverControlHelper.getUserPreferenceData.call(self);

		var requestUrl = listFilterTemplate.getUrl();

		if (!selectedData)
			return Promise.resolve({ Items: [] });

		return tf.promiseAjax.post(requestUrl, {
			data: {
				FilterClause: "",
				IdFilter: { IncludeOnly: selectedData.ListFilterIds }
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToFilterGPSEventsFromFilterDaysOfWeeks = function()
	{
		var self = this;

		var promise;
		if (self.weekDayFilterChange)
			promise = tf.promiseBootbox.yesNo(
				"Are you sure you want to cancel changes to Days of The Week?",
				"Confirmation"
			);
		else
			promise = Promise.resolve(true);

		return promise.then(function(ans)
		{
			if (ans)
				self.goToFilterGPSEvents();
		}.bind(this));
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToFilterGPSEventsFromFilterEventTypes = function()
	{
		var self = this;

		var promise;
		if (self.eventFilterChange)
			promise = tf.promiseBootbox.yesNo(
				"Are you sure you want to cancel changes to Event Types?",
				"Confirmation"
			);
		else
			promise = Promise.resolve(true);

		return promise.then(function(ans)
		{
			if (ans)
				self.goToFilterGPSEvents();
		}.bind(this));
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToFilterGPSEventsFromFilterVehicles = function()
	{
		var self = this;
		var promise;
		if (self.vehicleFilterChange)
			promise = tf.promiseBootbox.yesNo(
				"Are you sure you want to cancel changes to Vehicles?",
				"Confirmation"
			);
		else
			promise = Promise.resolve(true);

		return promise.then(function(ans)
		{
			if (ans)
				self.goToFilterGPSEvents();
		}.bind(this));
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToFilterGPSEvents = function()
	{
		var self = this;
		self.obStatus(self.pages.filterGPSEvents);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToSelectDaysOfTheWeek = function()
	{
		var self = this;

		self.obMondayCheckedTmp(self.obMondayChecked());
		self.obTuesdayCheckedTmp(self.obTuesdayChecked());
		self.obWednesdayCheckedTmp(self.obWednesdayChecked());
		self.obThursdayCheckedTmp(self.obThursdayChecked());
		self.obFridayCheckedTmp(self.obFridayChecked());
		self.obSaturdayCheckedTmp(self.obSaturdayChecked());
		self.obSundayCheckedTmp(self.obSundayChecked());

		self.initWeekDayChangedEvents();

		self.obStatus(self.pages.selectDaysOfTheWeek);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.onRoadFilterAppliedClick = function(viewModel, event)
	{
		var $currentTarget = $(event.currentTarget);
		if ($currentTarget && $currentTarget.closest('.vehicle-on-road').length > 0)
		{
			if (this.obOnRoadFilterApplied() !== true)
				this.obOnRoadFilterApplied(true);
		}
		else
		{
			if (this.obOnRoadFilterApplied() !== false)
				this.obOnRoadFilterApplied(false);
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToSelectVehicles = function()
	{
		var self = this;
		self.obFilterVehicles(self.obSelectedVehicles().slice());
		self.initVehicleChangedEvents();

		var promise = Promise.resolve();
		if (self.obOnRoadFilterApplied())
			promise = self.getAllOnRoadVehicles.bind(self);
		else
			promise = self.getAllVehicles.bind(self);

		promise().then(function(tempAllVehicles)
		{
			var array = getCanFilterItems(tempAllVehicles, self.obFilterVehicles(), 'BusNum');

			self.obCanFilterVehicles(array);
			self.obStatus(self.pages.selectVehicles);

			if (!self.obOnRoadFilterApplied())
			{
				self.obTempAllVehicles(tempAllVehicles);
			}
			else
			{
				self.getAllVehicles().then(function(result)
				{
					self.obTempAllVehicles(result);
				});
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.getAllVehicles = function()
	{
		var self = this;
		var url = pathCombine(tf.api.apiPrefixWithoutDatabase(), "gpsevents", "getvehicles");
		var option = { data: {}, paramData: {} };
		return tf.ajax.post(url, option).then(function(result)
		{
			if (result.Items)
				self.obAllVehicles(result.Items);
			else
				self.obAllVehicles([]);
			return self.obAllVehicles();
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.getAllOnRoadVehicles = function()
	{
		var self = this;
		var paramData = {
			time: toISOStringWithoutTimeZone(moment().currentTimeZoneTime())
		};
		var url = pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", "onroadvehicle");
		var data = {
			idFilter: {},
			sortItems: [
				{
					Name: "BusNum",
					Direction: "Ascending",
					isAscending: true
				}
			]
		};
		var option = { data: data, paramData: paramData };
		return tf.ajax.post(url, option).then(function(result)
		{
			if (result.Items)
				self.obAllOnRoadVehicles(result.Items);
			else
				self.obAllOnRoadVehicles([]);

			return self.obAllOnRoadVehicles();
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedFilterVehicles = function()
	{
		var self = this;
		var promise = Promise.resolve(self.obTempAllVehicles().slice());
		if (self.obOnRoadFilterApplied())
		{
			// needSendRequestToRetriveData = (self.isOnRoadFilter === false || self.isOnRoadFilter === undefined);
			// self.isOnRoadFilter = true;

			// if (needSendRequestToRetriveData)
			promise = self.getAllOnRoadVehicles.bind(self)();
		}
		else
		{
			// needSendRequestToRetriveData = (self.isOnRoadFilter === true || self.isOnRoadFilter === undefined);
			// self.isOnRoadFilter = false;

			// if (needSendRequestToRetriveData)
			// 	promise = self.getAllVehicles.bind(self)();
		}

		return promise.then(function(tempAllVehicles)
		{
			var array = getCanFilterItems(tempAllVehicles, self.obFilterVehicles(), 'BusNum');
			return self.obCanFilterVehicles(array);
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedFilterVehiclesTmp = function()
	{
		var self = this;
		if (!self.obVehicleSearchFilter())
			return self.obFilterVehicles().sort(sortVehicle);

		return Enumerable.From(self.obFilterVehicles().sort(sortVehicle)).Where(function(item)
		{
			return item.BusNum.toLowerCase().indexOf(self.obVehicleSearchFilter().toLowerCase()) >= 0;
		}).ToArray();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedCanFilterVehiclesTmp = function()
	{
		var self = this;
		if (!self.obVehicleSearchFilter())
			return self.obCanFilterVehicles().sort(sortVehicle);

		return Enumerable.From(self.obCanFilterVehicles().sort(sortVehicle)).Where(function(item)
		{
			return item.BusNum.toLowerCase().indexOf(self.obVehicleSearchFilter().toLowerCase()) >= 0;
		}).ToArray();
	};

	function sortVehicle(left, right)
	{
		var leftString = left.BusNum.toLowerCase();
		var rightString = right.BusNum.toLowerCase();
		return leftString == rightString ? 0 : (leftString < rightString ? -1 : 1);
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.animateSelectAllItem = function(options)
	{
		var $currentTarget = options.currentTarget;
		var $wrapper = options.wrapper;
		$wrapper.animate({ opacity: 0 }, 300, function()
		{
			options.callback();
			$wrapper.animate({ opacity: 1 }, 300);
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.onTapSelectVehicle = function(viewModel, event)
	{
		var self = this;

		if (self.obHasSelectedAllVehicleInSubModal())
		{
			var $currentTarget = $(event.currentTarget);
			var $wrapper = $currentTarget.closest('.mobile-modal-content-body.has-search');

			self.animateSelectAllItem(
				{
					currentTarget: $currentTarget,
					wrapper: $wrapper,
					callback: function()
					{
						self.obFilterVehicles.removeAll();
						self.obFilterVehicles.push(viewModel);
					}
				}
			);

			return;
		}

		var $currentTarget = $(event.currentTarget);
		var $wrapper = $currentTarget.parent();

		self.animateSelectItem({
			currentTarget: $currentTarget,
			wrapper: $wrapper,
			callback: function()
			{
				if ($currentTarget && $currentTarget.closest('.selected').length > 0)
				{
					var tmpIdx = -1;
					self.obFilterVehicles().forEach(function(vehicle, idx)
					{
						if (viewModel.Id === vehicle.Id)
						{
							tmpIdx = idx;
							return false;
						}
					});

					if (tmpIdx >= 0)
						self.obFilterVehicles.splice(tmpIdx, 1);
				}
				else
				{
					self.obFilterVehicles.push(viewModel);
				}
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.animateSelectItem = function(options)
	{
		var durection = 200;
		var $currentTarget = options.currentTarget;
		var $wrapper = options.wrapper;

		var itemHeight = $currentTarget.height();
		var newWrapperHeight = $wrapper.height() - itemHeight;

		$wrapper.animate({ height: newWrapperHeight }, durection, function()
		{
			$wrapper.css('height', '');
		});

		var animateStyle = {
			height: 0,
			opacity: 0,
			paddingTop: 0,
			paddingBottom: 0
		};
		$currentTarget.animate(animateStyle, durection, options.callback);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.goToSelectEventTypes = function()
	{
		var self = this;

		self.obFilterEventTypes(self.obSelectedGPSEventType().slice());
		self.initEventChangedEvents();
		self.obStatus(self.pages.selectEventTypes);
	};

	function getCanFilterItems(allItems, selectedItems, fieleName)
	{
		var array = allItems.filter(function(item)
		{
			var result = selectedItems.filter(function(selectItem)
			{
				return item[fieleName] === selectItem[fieleName];
			});

			return !result.length;
		});
		return array;
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.setSelectWeekDays = function()
	{
		var self = this;

		if (
			!self.obMondayCheckedTmp() &&
			!self.obTuesdayCheckedTmp() &&
			!self.obWednesdayCheckedTmp() &&
			!self.obThursdayCheckedTmp() &&
			!self.obFridayCheckedTmp() &&
			!self.obSaturdayCheckedTmp() &&
			!self.obSundayCheckedTmp()
		)
		{
			tf.promiseBootbox.alert(
				"At least one day of the week is required. Please select a Day of The Week.",
				"Day of The Week is Required"
			);
		}
		else
		{
			self.obMondayChecked(self.obMondayCheckedTmp());
			self.obTuesdayChecked(self.obTuesdayCheckedTmp());
			self.obWednesdayChecked(self.obWednesdayCheckedTmp());
			self.obThursdayChecked(self.obThursdayCheckedTmp());
			self.obFridayChecked(self.obFridayCheckedTmp());
			self.obSaturdayChecked(self.obSaturdayCheckedTmp());
			self.obSundayChecked(self.obSundayCheckedTmp());

			self.goToFilterGPSEvents();
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.setSelectEventTypes = function()
	{
		var self = this;

		if (!self.obFilterEventTypes().length)
		{
			tf.promiseBootbox.alert(
				"At least one event type is required. Please select an Event Type.",
				"Event Type is Required"
			);
		}
		else
		{
			self.obSelectedGPSEventType(self.obFilterEventTypes().slice());
			self.goToFilterGPSEvents();
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.setSeletVehicles = function()
	{
		var self = this;

		if (!self.obFilterVehicles().length)
		{
			tf.promiseBootbox.alert(
				"At least one vehicle is required. Please select a Vehicle.",
				"Vehicle is Required"
			);
		}
		else
		{
			self.obSelectedVehicles(self.obFilterVehicles().slice());
			self.goToFilterGPSEvents();
		}

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedTextDaysOfTheWeek = function()
	{
		var self = this;
		var selectCount = 0;
		var displayWeekDay;
		if (self.obMondayChecked())
		{
			displayWeekDay = displayWeekDay || "Monday";
			selectCount++;
		}

		if (self.obTuesdayChecked())
		{
			displayWeekDay = displayWeekDay || "Tuesday";
			selectCount++;
		}

		if (self.obWednesdayChecked())
		{
			displayWeekDay = displayWeekDay || "Wednesday";
			selectCount++;
		}

		if (self.obThursdayChecked())
		{
			displayWeekDay = displayWeekDay || "Thursday";
			selectCount++;
		}

		if (self.obFridayChecked())
		{
			displayWeekDay = displayWeekDay || "Friday";
			selectCount++;
		}

		if (self.obSaturdayChecked())
		{
			displayWeekDay = displayWeekDay || "Saturday";
			selectCount++;
		}

		if (self.obSundayChecked())
		{
			displayWeekDay = displayWeekDay || "Sunday";
			selectCount++;
		}

		if (selectCount > 1)
			return displayWeekDay + ' (+' + (selectCount - 1) + ')';
		else
			return displayWeekDay;

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedTextEventTypes = function()
	{
		var self = this;
		var array = self.obSelectedGPSEventTypeDisplay();
		if (array.length > 1)
			return array[0].EventTypeName + ' (+' + (array.length - 1) + ')';
		else if (array.length === 1)
			return array[0].EventTypeName;
		else
			return '';

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedTextVehicles = function()
	{
		var self = this;
		var array = self.obSelectedVehicles();
		if (array.length > 1)
			return array[0].BusNum + ' (+' + (array.length - 1) + ')';
		else if (array.length === 1)
			return array[0].BusNum;
		else
			return '';
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.onTapSelectEventType = function(viewModel, event)
	{
		var self = this;

		if (self.obHasSelectedAllGPSEventTypeInSubModal())
		{
			var $currentTarget = $(event.currentTarget);
			var $wrapper = $currentTarget.closest('.mobile-modal-content-body.has-search');

			self.animateSelectAllItem(
				{
					currentTarget: $currentTarget,
					wrapper: $wrapper,
					callback: function()
					{
						self.obFilterEventTypes.removeAll();
						self.obFilterEventTypes.push(viewModel);
					}
				}
			);
			return;
		}

		var $currentTarget = $(event.currentTarget);
		var $wrapper = $currentTarget.parent();

		self.animateSelectItem({
			currentTarget: $currentTarget,
			wrapper: $wrapper,
			callback: function()
			{
				if ($currentTarget && $currentTarget.closest('.selected').length > 0)
				{
					var tmpIdx = -1;
					self.obFilterEventTypes().forEach(function(eventType, idx)
					{
						if (viewModel.Id === eventType.Id)
						{
							tmpIdx = idx;
							return false;
						}
					});

					if (tmpIdx >= 0)
						self.obFilterEventTypes.splice(tmpIdx, 1);
				}
				else
				{
					self.obFilterEventTypes.push(viewModel);
				}
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.emptyVehicleSearchFilter = function()
	{
		this.obVehicleSearchFilter("");
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.emptyEventTypeSearchFilter = function()
	{
		this.obEventTypeSearchFilter("");
	};

	function sortEventType(left, right)
	{
		var leftString = left.EventTypeName.toLowerCase();
		var rightString = right.EventTypeName.toLowerCase();
		return leftString == rightString ? 0 : (leftString < rightString ? -1 : 1);
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedFilterEventTypesTmp = function()
	{
		var self = this;
		if (!self.obEventTypeSearchFilter())
			return self.obFilterEventTypes().sort(sortEventType);

		return Enumerable.From(self.obFilterEventTypes().sort(sortEventType)).Where(function(item)
		{
			return item.EventTypeName.toLowerCase().indexOf(self.obEventTypeSearchFilter().toLowerCase()) >= 0;
		}).ToArray();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedCanFilterEventTypes = function()
	{
		var self = this;
		return getCanFilterItems(self.obAllGPSEvents(), self.obFilterEventTypes(), 'EventTypeName');
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.computedCanFilterEventTypesTmp = function()
	{
		var self = this;
		if (!self.obEventTypeSearchFilter())
			return self.obCanFilterEventTypes().sort(sortEventType);

		return Enumerable.From(self.obCanFilterEventTypes().sort(sortEventType)).Where(function(item)
		{
			return item.EventTypeName.toLowerCase().indexOf(self.obEventTypeSearchFilter().toLowerCase()) >= 0;
		}).ToArray();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.onTapSelectWeekDay = function(viewModel, event)
	{
		var self = this;
		var $currentTarget = $(event.currentTarget);
		var $item = $currentTarget.closest('.weekday-item');
		if ($item && $item.data('name'))
		{
			var weekDay = $item.data('name');
			switch (weekDay)
			{
				case 'Monday':
					if (!self.obMondayDisabled())
						self.obMondayCheckedTmp(!self.obMondayCheckedTmp());
					break;
				case 'Tuesday':
					if (!self.obTuesdayDisabled())
						self.obTuesdayCheckedTmp(!self.obTuesdayCheckedTmp());
					break;
				case 'Wednesday':
					if (!self.obWednesdayDisabled())
						self.obWednesdayCheckedTmp(!self.obWednesdayCheckedTmp());
					break;
				case 'Thursday':
					if (!self.obThursdayDisabled())
						self.obThursdayCheckedTmp(!self.obThursdayCheckedTmp());
					break;

				case 'Friday':
					if (!self.obFridayDisabled())
						self.obFridayCheckedTmp(!self.obFridayCheckedTmp());
					break;
				case 'Saturday':
					if (!self.obSaturdayDisabled())
						self.obSaturdayCheckedTmp(!self.obSaturdayCheckedTmp());
					break;
				case 'Sunday':
					if (!self.obSundayDisabled())
						self.obSundayCheckedTmp(!self.obSundayCheckedTmp());
					break;
				default:
					break;
			}
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.emptySearchWeekDaysFilter = function()
	{
		this.obWeekDaysSearchFilter("");
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewMobileModel.prototype.searchWeekDaysFilter = function()
	{
		var self = this;

		if (!self.obWeekDaysSearchFilter())
			self.obFilterWeekDays(weekDayNameList);
		else
		{
			self.obFilterWeekDays(Enumerable.From(weekDayNameList).Where(function(weekDay)
			{
				return weekDay.toLowerCase().indexOf(self.obWeekDaysSearchFilter().toLowerCase()) >= 0;
			}).ToArray());
		}

		self.obMondayVisible(false);
		self.obTuesdayVisible(false);
		self.obWednesdayVisible(false);
		self.obThursdayVisible(false);
		self.obFridayVisible(false);
		self.obSaturdayVisible(false);
		self.obSundayVisible(false);

		if (self.obFilterWeekDays().length)
		{
			self.obFilterWeekDays().forEach(function(weekDay)
			{
				switch (weekDay)
				{
					case 'Monday':
						self.obMondayVisible(true);
						break;
					case 'Tuesday':
						self.obTuesdayVisible(true);
						break;
					case 'Wednesday':
						self.obWednesdayVisible(true);
						break;
					case 'Thursday':
						self.obThursdayVisible(true);
						break;
					case 'Friday':
						self.obFridayVisible(true);
						break;
					case 'Saturday':
						self.obSaturdayVisible(true);
						break;
					case 'Sunday':
						self.obSundayVisible(true);
						break;
					default:
						break;
				}
			});
		}
	};

	var weekDayNameList = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
})();
