﻿.iconbutton {
	display: block;
	height: 16px;
	width: 16px;
	background-repeat: no-repeat;
	background-position: center;
	background-image: url('../../global/img/grid/u1366.png');
	background-size: 16px 16px;
	background-clip: padding-box;
	cursor: pointer;
}

.iconbutton:hover:not(.contextmenu-open) {
	border: none;

	.iconrow & {
		border: none;
	}
}

.iconbutton.disabled {
	cursor: default;
}

.iconbutton.disabled:active {
	pointer-events: none;
}

.iconbutton.contextmenu-open {
	background-color: #E4E4E4;
}

.iconbutton.schedule {
	background-image: url(../img/icons/schedule.png);
}

.iconbutton.grid {
	background-image: url(../img/icons/grid.png);
}

.iconbutton.globe {
	background-image: url(../img/icons/globe.png);
}

.iconbutton.find {
	background-image: url(../img/icons/find.png);
}

.iconbutton.alarm {
	background-image: url(../img/icons/Reminders-white.png);
}

.iconbutton.message-center {
	background-image: url(../img/icons/message-center-white.png);
}

.iconbutton.report {
	background-image: url(../img/menu/ViewReport-White.png);
}

.iconbutton.map {
	background-image: url(../img/menu/Map-White.png);
}

.iconbutton.document {
	background-image: url(../img/menu/DocumentCenter-White.png);
}

.iconbutton.scheduler {
	background-image: url(../img/menu/ResourceSched-White.png);
}

.iconbutton.message {
	background-image: url(../img/Icons/Email-White.png);
}

.iconbutton.control {
	background-image: url(../img/grid/Control_Panel_White.png);
}

.iconbutton.viewrecord-white {
	background-image: url('../img/grid/view-white.png');
}

.iconbutton.edit-white {
	background-image: url(../img/menu/Edit-White.png);
}

.iconbutton.selected {
	background-image: url(../img/Icons/success.png);
}

.iconbutton.warning {
	background-image: url(../img/Icons/de_forms/AdjustedLoadTime.png);
}

.iconbutton.current {
	background-image: url(../img/Icons/current.png);
}

.iconbutton.datasource-icon {
	cursor: auto;
}

.iconbutton.datasource-icon:hover {
	background-color: transparent;
}

.leftpanel .iconbutton {
	cursor: pointer;
	display: inline-block;
	height: 24px;
	position: relative;
	width: 24px;
}

.iconbutton.CreateMissingPaths {
	background-image: url(../img/icons/SideBar/CreateMissingPaths.png);
}

.iconbutton.CreatePath {
	background-image: url(../img/icons/SideBar/CreatePath.png);
}

.iconbutton.DeleteEntirePath {
	background-image: url(../img/icons/SideBar/DeleteEntirePath.png);
}

.iconbutton.DrawCircle {
	background-image: url(../img/icons/SideBar/DrawCircle.png);
}

.iconbutton.DrawLine {
	background-image: url(../img/icons/SideBar/DrawLine.png);
}

.iconbutton.DrawPolygon {
	background-image: url(../img/icons/SideBar/DrawPolygon.png);
}

.iconbutton.DrawRectangle {
	background-image: url(../img/icons/SideBar/DrawRectangle.png);
}

.iconbutton.DrawSymbol {
	background-image: url(../img/icons/SideBar/DrawSymbol.png);
}

.iconbutton.DrawText {
	background-image: url(../img/icons/SideBar/DrawText.png);
}

.iconbutton.GenericTripPath {
	background-image: url(../img/icons/SideBar/GenericTripPath.png);
}

.iconbutton.GeoStudentSearch {
	background-image: url(../img/icons/SideBar/GeoStudentSearch.png);
}

.iconbutton.GoogleEarth {
	background-image: url(../img/icons/SideBar/GoogleEarth.png);
}

.iconbutton.Label {
	background-image: url(../img/icons/SideBar/Label.png);
}

.iconbutton.RegenerateDirections {
	background-image: url(../img/icons/SideBar/RegenerateDirections.png);
}

.iconbutton.Resequence {
	background-image: url(../img/icons/SideBar/Resequence.png);
}

.iconbutton.Ruler {
	background-image: url(../img/icons/SideBar/Ruler.png);
}

.iconbutton.WalkOut {
	background-image: url(../img/icons/SideBar/WalkOut.png);
}

.iconbutton.DoorToDoorStop {
	background-image: url(../img/icons/newstop/door_to_door.png);
}

.iconbutton.PolygonStop {
	background-image: url(../img/icons/newstop/poly_stop.png);
}

.iconbutton.RectangleStop {
	background-image: url(../img/icons/newstop/rec_stop.png);
}

.iconbutton.RoundStop {
	background-image: url(../img/icons/newstop/round_stop.png);
}

.iconbutton.WalkOutStop {
	background-image: url(../img/icons/newstop/Walkout.png);
}

.iconbutton.user {
	background-image: url(../img/icons/user.png);
}

.iconbutton.error {
	background-image: url(../img/icons/error.png);
	height: 24px;
	width: 24px;
	background-size: 24px;
}

.iconbutton.success {
	background-image: url(../img/icons/success.png);
	height: 24px;
	width: 24px;
	background-size: 24px;
}

.iconbutton.page-previous {
	border: none;
	margin-left: 0;
	background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' height='16' width='10' %3E%3Cpolygon points='10,0 10,16 0,8' style='fill:%23474747;stroke:%23474747;' /%3E%3C/svg%3E");
}

.switch-arrow .iconbutton .page-previous {
	margin-right: 5px;
}

.iconbutton.page-previous.disabled {
	background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' height='16' width='10' %3E%3Cpolyline points='10,0 10,16 0,8 10,0' style='stroke:%23474747;fill:white' /%3E%3C/svg%3E");
}

.iconbutton.page-next {
	border: none;
	margin-left: 0;
	background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' height='16' width='10' %3E%3Cpolygon points='0,0 0,16 10,8' style='fill:%23474747;stroke:%23474747;' /%3E%3C/svg%3E");
}

.iconbutton.page-next.disabled {
	background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' height='16' width='10' %3E%3Cpolyline points='0,0 0,16 10,8 0,0' style='stroke:%23474747;fill:white' /%3E%3C/svg%3E");
}

.iconbutton.help {
	background-image: url(../img/icons/help.png);
}

.iconbutton.nonbookmark {
	background-image: url(../img/icons/nonbookmark.png);
}

.iconbutton.bookmarked {
	background-image: url(../img/icons/bookmark-marked.png);
}

.iconbutton.favoriteBookmarked {
	background-image: url(../img/icons/bookmark-favorite.png);
}

.iconbutton.menu-control-panel {
	background-image: url(../img/menu/control-panel.png);
}

.iconbutton.menu-document {
	background-image: url(../img/menu/document.png);
}

.iconbutton.menu-report {
	background-image: url(../img/menu/report.png);
}

.iconbutton.browse {
	background-image: url(../img/menu/Browse.png);
}

.tf-icon.negative {
	background-image: url('../img/Icons/negative.png');
	background-position: center;
	background-repeat: no-repeat;
}

.icon-adjusted-time {
	background-image: url('../img/icons/de_forms/adjustedloadtime.png');
}

.icon-findassignment {
	background-image: url('../img/icons/de_forms/FindAssignment.png');
}

.icon-findassignment-white {
	background-image: url('../img/menu/FindAssignment-White.png');
}

.icon-viewrecord {
	background-image: url('../img/grid/view.png');
}

.icon-viewrecord-white {
	background-image: url('../img/icons/de_forms/ViewRecord-White.png');
}

.icon-studentstopassignment {
	background-image: url('../img/icons/de_forms/studentstopassignment.png');
}

.icon-delete {
	background-image: url('../img/menu/Delete-Black.svg');
}

.icon-add {
	background-image: url('../img/icons/de_forms/add.png');
}

.icon-edit-black {
	background-image: url('../img/menu/edit-black.png');
}

.icon-new-copy-black {
	background-image: url('../img/menu/Copy.png');
}

.icon-adjusttime-white {
	background-image: url('../img/icons/de_forms/adjusttime-white.png');
}

.icon-altsite-black {
	background-image: url('../img/icons/altsite-black.png');
}

.icon-fieldtrip-black {
	background-image: url('../img/icons/fieldtrip-black.png');
}

.icon-staff-black {
	background-image: url('../img/icons/staff-black.png');
}

.icon-student-black {
	background-image: url('../img/icons/students-black.png');
}

.icon-trip-black {
	background-image: url('../img/icons/trips-black.png');
}

.icon-vehicle-black {
	background-image: url('../img/icons/vehicle-black.png');
}

.icon-center-map-on-vehicle {
	background-image: url('../img/icons/center-map-on-vehicle.png');
}

.icon-geocoded {
	background-image: url('../img/icons/geocoded.png');
	background-position: center;
	background-repeat: no-repeat;
}

.iconbutton.print {
	background-image: url(../img/print.png);
}

.icon-inner {
	height: 16px;
	width: 16px;
	background-repeat: no-repeat;
}

.icon-input-alert {
	background-image: url('../img/icons/alert.svg');
	background-position: center;
	background-repeat: no-repeat;
	background-size: 16px 16px;
}