(function()
{
	createNamespace("TF.Map").MapCanvasRoutingMapTool = MapCanvasRoutingMapTool;

	function MapCanvasRoutingMapTool(routingMapDocumentViewModel, options)
	{
		TF.Map.RoutingMapTool.call(this, routingMapDocumentViewModel, options);
		this.thematicTools = {};
	}

	MapCanvasRoutingMapTool.prototype = Object.create(TF.Map.RoutingMapTool.prototype);
	MapCanvasRoutingMapTool.prototype.constructor = MapCanvasRoutingMapTool;

	MapCanvasRoutingMapTool.prototype.onMapToolActived = function()
	{
		this.calculateMenuItemWidth();
		TF.Map.RoutingMapTool.prototype.onMapToolActived.call(this);
	};

	MapCanvasRoutingMapTool.prototype.addTool = function(name, label, clickFunc, item, Sequence)
	{
		let self = this, sequence = Sequence ? Sequence : self.$mapToolContainer.find(".tool-icon").length + 1,
			$toolBtn = $("<div></div>", { class: "tool-icon", title: label }),
			$label = $("<label></label>", { text: label });

		if (self.isLandscape)
		{
			$toolBtn.attr("title", label);
		}

		$toolBtn.addClass(name);

		let $item = $("<div class='map-tool-item animate'>").addClass("sequence-" + sequence);
		$item.append($label).append($toolBtn);
		self.$mapToolContainer.append($item);

		if (item.helpKey)
		{
			$item.attr("help-key", item.helpKey).attr("help-placement", "left");
		}

		if (item.isDisable)
		{
			$item.addClass("disable");
		}

		$item.click(clickFunc.bind(self));
		item.target = $toolBtn;
	};

	MapCanvasRoutingMapTool.prototype.calculateMenuItemWidth = function()
	{
		this.$offMapTool.find(".map-tool-item").each((index, item) =>
		{
			let $item = $(item),
				width = $item.find(".tool-icon")[0].getBoundingClientRect().right - $item.find("label")[0].getBoundingClientRect().left + 10;
			$item.width(width);
		});
	};

	MapCanvasRoutingMapTool.prototype.BuildFirstLevelMenuItems = function(items)
	{
		TF.Map.RoutingMapTool.prototype.BuildFirstLevelMenuItems.call(this, items);
		this.$toolkitButton.attr({ "help-key": "maptools", "help-placement": "left" });
	};

	MapCanvasRoutingMapTool.prototype.addThematicsMenuItem = function()
	{
		let self = this;
		this.thematicMenuItem = new TF.RoutingMap.MenuItem({
			header: 'Thematics',
			icon: 'thematics',
			children: [
				new TF.RoutingMap.MenuItem({
					header: "Students",
					click: function(menuItem)
					{
						self.thematicsToolClick(menuItem, self.thematicTools.student);
					}
				}), new TF.RoutingMap.MenuItem({
					header: "Parcels & Address Points",
					click: function(menuItem)
					{
						self.thematicsToolClick(menuItem, self.thematicTools.parceladdresspoint);
					}
				})
			],
		})
		this.rootMenuItem.addChild(this.thematicMenuItem);
	};

	MapCanvasRoutingMapTool.prototype.thematicsToolClick = function(menuItem, thematicTool)
	{
		for (let key in this.thematicTools)
		{
			this.thematicTools[key].thematicMenu.deactivate();
			this.thematicTools[key].$caret?.remove();
		}
		thematicTool.thematicMenu.activate().then(() =>
		{
			let $thematicTool = this.$offMapTool.find('.thematic-menu.' + thematicTool.gridType);
			let $caret = $('<div class="caret"></div>');
			this.setMenuPosition(menuItem, $thematicTool, $caret, true);
			thematicTool.$caret = $caret;
		})
	};

	MapCanvasRoutingMapTool.prototype.initThematicsTool = function()
	{
		let self = this;
		self.thematicsToolTimer = setTimeout(function()
		{
			initStudentThematicTool();
			initParcelPointThematicTool();
		});

		function initStudentThematicTool()
		{
			let grid = {
				_gridType: 'student',
				result: { TotalRecordCount: 0 },
				allIds: [],
				allData: [],
				highLightedData: [],
				dataType: 'unassignedStudents',
				isMapCanvas: true,
				routeState: self.getRouteState() || "",
			};

			self.thematicTools.student = new TF.Map.RoutingThematicTool(
				grid,
				self.routingMapDocumentViewModel,
				self.$mapTool,
				self.$offMapTool,
				self.options.isDetailView,
				self.studentIds,
				self.options.isReadMode,
				self.options.thematicInfo,
				self.options.legendStatus,
				"candidateStudentFeatureLayer",
				self.options.mapToolOptions);
			self.thematicTools.student.thematicMenu.onMenuOptionClick.subscribe(self.onThematicMenuOptionClick.bind(self));
		}

		function initParcelPointThematicTool()
		{
			let grid = {
				_gridType: 'parceladdresspoint',
				result: { TotalRecordCount: 0 },
				allIds: [],
				allData: [],
				highLightedData: [],
				dataType: 'parceladdresspoint',
				isMapCanvas: true,
				routeState: self.getRouteState() || "",
			};

			self.thematicTools.parceladdresspoint = new TF.Map.RoutingThematicTool(
				grid,
				self.routingMapDocumentViewModel,
				self.$mapTool,
				self.$offMapTool,
				self.options.isDetailView,
				null,
				self.options.isReadMode,
				self.options.thematicInfo,
				self.options.legendStatus,
				"parcelPointFeatureLayer",
				self.options.mapToolOptions);

			self.thematicTools.parceladdresspoint.thematicMenu.onMenuOptionClick.subscribe(self.onThematicMenuOptionClick.bind(self));
		}
	};

	MapCanvasRoutingMapTool.prototype.dispose = function()
	{
		TF.Map.RoutingMapTool.prototype.dispose.call(this);
		if (this.thematicTools)
		{
			for (let key in this.thematicTools)
			{
				this.thematicTools[key].dispose();
			}
		}
	}
})();