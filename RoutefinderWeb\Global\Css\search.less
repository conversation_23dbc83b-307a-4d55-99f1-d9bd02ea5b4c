﻿.document-search {
	cursor: default;

	@search-container-top-offset: 92px;

	.virtual-scroll {
		&.outer {
			overflow-y: scroll;
			overflow-x: hidden;
			position: absolute;
			right: 5px;
			top: @search-container-top-offset;
			width: 18px;
		}
	}

	.virtual-scroll-container {
		.row {
			display: flex;
		}
	}

	.clearafter.relative {
		padding: 5px 5px 20px 5px;
	}

	.search-title-group {
		line-height: 38px;
		height: 38px;

		.search-title {
			font-family: Arial;
			font-weight: bold;
			color: #D0503C;
			font-size: 24px;
		}
	}

	.search-paginator {
		position: absolute;
		right: 44px;
		top: 16px;
		font-size: 10px;

		.iconbutton {
			background-size: 100% 100%;
			width: 20px;
			height: 20px;
		}

		& > div {
			float: left;
			display: block;
			margin-left: 10px;
		}
	}

	.search-filter {
		padding: 0px 23px 0px 5px;
		height: 30px;

		.filter-bar {

			div {
				margin-right: 2px;
			}

			.filter-item {
				color: grey;
				cursor: pointer;

				span {
					cursor: pointer;
				}

				.name {
					color: #333;

					&.check {
						color: #D0503C;
						font-weight: bold;
					}
				}
			}
		}

		.search-sort-bar {
			margin-right: 160px;
			float: right;
		}
	}

	.search-container {
		overflow: hidden;
		height: calc(~"100% - "@search-container-top-offset);
		width: calc(~"100% - 20px");
		padding: 0 10px;

		.card {
			width: 360px;
			height: 200px;
			border: 1px solid #E7E7E7;
			margin: 10px -5px 0px 15px;
			text-align: center;
			box-shadow: 4px 4px 10px #DBDBDB;
			z-index: 1000;
			position: relative;
			background-color: white;

			.back-color {
				height: 40%;
			}

			.map {
				height: 40%;
				background-image: url('../../global/img/search/map2.png');
				background-position: 80%;
				background-size: 150%;
			}

			.image {
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background-color: #A1A1A1;
				position: absolute;
				top: 3px;
				left: calc(~"50% - 50px");
			}

			.description {
				padding: 20px 0px 0px 0px;
				height: 60%;

				.title {
					font-size: 24px;
					font-weight: bold;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.subtitle {
					color: grey;
					font-size: 18px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.bottom {
					position: absolute;
					width: 100%;
					bottom: 0px;

					.left-info {
						float: left;
						margin: 0px 0px 5px 5px;
					}

					.right-info {
						float: right;
						margin: 0px 5px 5px 0px;
					}

					.phone, .mail {
						margin-left: 5px;
						font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
					}
				}
			}

			.icon-display.pull-left {
				width: 16px;
				height: 16px;
				margin: 5px 5px 0px 5px;
			}

			.hovered-display.group-edit.pull-right, .hovered-display.group-view.pull-right {
				display: none;
				opacity: 0.65;
				margin: 5px 5px 0px 0px;
				cursor: pointer;

				span {
					font-size: 9px;
					color: white;
					cursor: pointer;
				}

				.iconbutton {
					width: 16px;
					margin: 0 auto;
				}
			}


			&:hover {
				z-index: 1001;
				box-shadow: 0px 10px 40px -10px black;
				transform: scale(191/180.0, 191/180.0);

				.hovered-display.group-edit.pull-right, .hovered-display.group-view.pull-right {
					display: block;
				}
			}
		}
	}

	.btn-sharp {
		border-radius: 0;
	}

	.input-group-btn > button {
		padding-right: 6px;
		padding-left: 6px;
		background-color: #eeeeee;
		min-width: 25px;
		outline: none;
	}

	.input-group-sort {
		position: absolute;
		width: 140px;
		z-index: 1002;
	}
}

.card-modal {
	margin: -15px;
	cursor: default;

	.card {
		/*height: 400px;*/
		text-align: center;
		background-color: white;
		color: #333;

		.back-color {
			height: 110px;
			padding: 10px 10px 0px 10px;
		}

		.map {
			height: 120px;
		}

		.image {
			width: 140px;
			height: 140px;
			border-radius: 50%;
			background-color: #A1A1A1;
			position: absolute;
			top: 8px;
			left: calc(~"50% - 70px");
		}

		.card-description {
			padding: 35px 10px 2px 10px;

			.title {
				font-size: 24px;
				font-weight: bold;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.subtitle {
				color: grey;
				font-size: 18px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				height: 25px;
			}

			.bottom {
				margin: 25px 0 0 0;
				height: 17px;

				.left-info {
					float: left;
				}

				.right-info {
					float: right;
				}

				.phone, .mail {
					margin-left: 5px;
					font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
				}
			}
		}

		.icon-display.pull-left {
			width: 20px;
			height: 20px;
			background-size: 100%;
		}

		.group-edit.pull-right, .group-view.pull-right {
			cursor: pointer;

			span {
				font-size: 12px;
				color: white;
				cursor: pointer;
			}

			.iconbutton {
				height: 20px;
				width: 30px;
				background-size: 70%;
			}
		}
	}

	.card-content {
		margin-bottom: 20px;

		.header-group {
			border-bottom: 1px solid;

			.content-group {
				margin-bottom: 5px;
			}

			.header {
				background-color: #666666;
				color: #ffffff;
				font-weight: bold;
				height: 26px;
				line-height: 26px;
				padding-left: 10px;
			}

			.title {

				span:first-child {
					font-weight: bold;
				}
			}

			.iconbutton {
				height: 20px;
				width: 30px;
				background-size: 70%;
			}

			.subtitle {
				font-size: small;
				color: gray;
			}

			.buttons {
				padding: 0;

				.button {
					opacity: 0.6;
					margin: 0 auto;

					&:hover {
						cursor: pointer;
						border: none;
					}
				}
			}
		}

		.content-group {
			margin: 10px 10px 20px 10px;

			label {
				color: #333333;
				font-weight: bold;
				font-size: 14px;
				line-height: 20px;
				min-height: 20px;
			}

			.value {
				color: grey;
				line-height: 16px;
				min-height: 16px;
				display: block;

				.phone, .mail {
					margin-left: 5px;
					font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
				}
			}
		}
	}
}
