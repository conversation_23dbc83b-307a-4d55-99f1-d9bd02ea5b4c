(function()
{
	createNamespace('TF.Modal').SelectImportDefinitionFileModalViewModel = SelectImportDefinitionFileModalViewModel;

	function SelectImportDefinitionFileModalViewModel()
	{
		//tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), type, id));
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.contentTemplate('modal/import data/selectimportdefinitionfilecontrol');
		self.buttonTemplate('modal/positivenegativeother');
		//self.obFileAccept(".jpg, .png, .jpeg, .gif, .bmp, .tif, .tiff|images/*");
		self.title("Select Import Definition File");
		self.obPositiveButtonLabel("OK");
		self.obNegativeButtonLabel("Cancel");
		self.obOtherButtonLabel("Browse...");
		self.obDisableControl(true);

		self.selectImportDefinitionFileViewModel = new TF.Control.SelectImportDefinitionFileViewModel();
		self.data(self.selectImportDefinitionFileViewModel);

		//Events
		self.fileUploadComplete = self.fileUploadComplete.bind(self);

		self.selectImportDefinitionFileViewModel.onSelectOneTFDItem.subscribe(function()
		{
			self.obDisableControl(false);
		});

		self.selectImportDefinitionFileViewModel.onFileUploadComplete.subscribe(self.fileUploadComplete);
	}

	function getImportFileTypeFlag(selectedTfdFile)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "importData/analyzeImportFileType", selectedTfdFile));
	}

	function getAcceptedFileType(typFlag)
	{
		var FileTypeObject = {
			"0": { "name": "Fixed Record Length Text File (txt)", "ext": ".txt" },
			"1": { "name": "Tab Delmited Text File (tab, dat)", "ext": ".tab,.dat" },
			"2": { "name": "Comma Delmited Text File (csv)", "ext": ".csv" },
			"3": { "name": "Transfinder TFX File (tfx)", "ext": ".tfx" },
			"4": { "name": "DBF File (dbf)", "ext": ".dbf" },
			"5": { "name": "MS Excel Spread Sheet (xls)", "ext": ".xls" },
			"6": { "name": "Pipe Delimited File (|)", "ext": "" }
		};
		if (FileTypeObject[typFlag] != null)
		{
			return FileTypeObject[typFlag];
		}
	}

	SelectImportDefinitionFileModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	SelectImportDefinitionFileModalViewModel.prototype.constructor = SelectImportDefinitionFileModalViewModel;

	/**
	 * The event of OK button click.
	 * @return {void}
	 */
	SelectImportDefinitionFileModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.selectImportDefinitionFileViewModel.apply().then(function(result)
		{
			self.positiveClose(result);
		});
	};

	/**
	 * The event of Browse button click.
	 * @return {void}
	 */
	SelectImportDefinitionFileModalViewModel.prototype.otherClick = function()
	{
		var self = this;
		self.selectImportDefinitionFileViewModel.selectDataFile();
	};

	/**
	 * After file uploaded.
	 * @return {void}
	 */
	SelectImportDefinitionFileModalViewModel.prototype.fileUploadComplete = function(e, fileModel)
	{
		var self = this;
		setTimeout(function()
		{
			self.positiveClose({ tfdfile: fileModel, type: "" });
		}, 1000);
	};

})();
