﻿(function()
{
	createNamespace('TF.Modal').EditNoteModalViewModel = EditNoteModalViewModel;

	function EditNoteModalViewModel(type, typeId, note)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/editnotecontrol');
		this.buttonTemplate('modal/positivenegative');
		this.obDisableControl(false);
		this.editNoteViewModel = new TF.Control.EditNoteViewModel(type, typeId, note, this.obDisableControl);
		this.data(this.editNoteViewModel);
		this.title("Edit Note");
	}

	EditNoteModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	EditNoteModalViewModel.prototype.constructor = EditNoteModalViewModel;

	EditNoteModalViewModel.prototype.positiveClick = function()
	{
		this.editNoteViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();
