﻿(function()
{
	createNamespace('TF.Modal').ListMoverSelectRecordControlWidgetViewModel = ListMoverSelectRecordControlWidgetViewModel;

	function ListMoverSelectRecordControlWidgetViewModel(selectedData, options)
	{
		options.displayCheckbox = false;
		if (options.showRemoveColumnButton === undefined)
		{
			options.showRemoveColumnButton = true;
		}
		this.template = "modal/ListMoverSelectRecordControl";
		this.isWidget = ko.observable(true);
		this.inheritChildrenShortCutKey = false;
		TF.Control.ListMoverSelectRecordControlViewModel.call(this, selectedData, options);
	}

	ListMoverSelectRecordControlWidgetViewModel.prototype = Object.create(TF.Control.ListMoverSelectRecordControlViewModel.prototype);

	ListMoverSelectRecordControlWidgetViewModel.prototype.constructor = ListMoverSelectRecordControlWidgetViewModel;

	ListMoverSelectRecordControlWidgetViewModel.prototype.onBeforeLeftGridDataBound = function(leftSearchGrid)
	{
		TF.Control.KendoListMoverWithSearchControlViewModel.prototype.onBeforeLeftGridDataBound.call(leftSearchGrid);
		leftSearchGrid.$container.find(".k-grid-content table.k-grid-table tr").map(function(idx, row)
		{
			var $row = $(row);
			var kendoUid = $row.data('kendoUid');
			var contactEmail = leftSearchGrid.kendoGrid.dataSource.getByUid(kendoUid).ContactEmail;
			if (TF.testEmail(contactEmail).emailList.length === 0)
			{
				$row.addClass("disable");
				$row.css("color", "grey");
				$row.css("pointer-events", "none");
				$row.bind("select", function(e)
				{
					e.preventDefault();
				});
			}
		});
	};
})();
