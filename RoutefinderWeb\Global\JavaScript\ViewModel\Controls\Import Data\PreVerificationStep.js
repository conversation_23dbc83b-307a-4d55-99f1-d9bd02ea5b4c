(function()
{
	createNamespace("TF.ImportAndMergeData").PreVerificationStep = PreVerificationStep;

	function PreVerificationStep(data)
	{
		TF.Control.BaseWizardStepViewModel.call(this, data);
		this.template = "modal/import data/PreVerification";
		this.name = ko.observable("Pre-Verification");
		this.description("Please review the following summary before continuing.");
		this.nextButtonLabel = "Import";
		this.importMessage = this.getImportMessage();
	}

	PreVerificationStep.prototype = Object.create(TF.Control.BaseWizardStepViewModel.prototype);

	PreVerificationStep.prototype.constructor = PreVerificationStep;

	PreVerificationStep.prototype.getImportMessage = function()
	{
		var self = this, text = '';
		self.data.tableConfigs.forEach(function(tableConfig)
		{
			var dataTypeInfo = TF.ImportAndMergeData.ImportDataType.findById(tableConfig.dataType()),
				tableName = dataTypeInfo.displayName,
				matchText = '',
				includeScheduleText,
				deleteText = tableConfig.isDeleteNonexisting() ? tableName + ' DELETE ALL records not found in Import Source' : '',
				stopPoolText,
				optionTypeText = tableConfig.isUpdateExisting() ? 'Update Existing' : 'Add Only';
			if (tableConfig.dataType() == TF.ImportAndMergeData.ImportDataType.Student)
			{
				includeScheduleText = tableConfig.needFindSchedule() ? tableName + ' Include Trip Schedule (New Records Only)' : ' Ignore Trip Schedule';
			}

			if (tableConfig.dataType() == TF.ImportAndMergeData.ImportDataType.Trip)
			{
				stopPoolText = tableConfig.includeStopPool() ? tableName + ' Include Stop Pool' : ' Ignore Stop Pool';
			}

			tableConfig.columnConfigs.forEach(function(columnConfig)
			{
				if (columnConfig.MatchOn)
				{
					if (matchText)
					{
						matchText += ", ";
					}

					matchText += columnConfig.Target;
				}
			});

			matchText = matchText || "None";
			text += tableName + ' (' + optionTypeText + ' - Match on ' + matchText + ')\n';
			if (includeScheduleText)
			{
				text += includeScheduleText + '\n';
			}

			if (deleteText)
			{
				text += deleteText + '\n';
			}

			if (stopPoolText)
			{
				text += stopPoolText + '\n';
			}
		});

		return text;
	};

	PreVerificationStep.prototype.execute = function()
	{
		return this.import();
	};

	PreVerificationStep.prototype.import = function()
	{
		var self = this, importDataOptions = self.data.toData();
		return TF.ImportAndMergeData.ImportAndMergeDataWizard.createImportOperation(importDataOptions).then(function(data)
		{
			if (data)
			{
				self.data.operationId = data.operationId;
				self.data.operationOptions = data.operationOptions;
				return true;
			}

			return false;
		});
	};
})();