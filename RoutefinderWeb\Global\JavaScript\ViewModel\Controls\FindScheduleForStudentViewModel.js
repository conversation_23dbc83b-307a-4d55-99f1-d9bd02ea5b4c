(function()
{
	createNamespace("TF.Control").FindScheduleForStudentViewModel = FindScheduleForStudentViewModel;

	function FindScheduleForStudentViewModel(options, modalViewModel)
	{
		interactive = options.interactive && !options.autoAssign;
		this.initData(options, modalViewModel);
		if (!options.autoAssign)
		{
			this.start();
		}
	}

	var FindScheduleResult =
	{
		Skip: 0,
		FullScheduled: 1,
		PartialScheduled: 2,
		PreviouslyScheduled: 3
	};

	FindScheduleForStudentViewModel.prototype.initData = function(options, modalViewModel)
	{
		this.dbid = options.dbid || tf.datasourceManager.databaseId;
		this.studentTripItems = options?.studentTripItems;
		this.studentIds = options?.studentIds || options;
		this.interactive = options.interactive;
		this.createDoorToDoorStops = options.createDoorToDoorStops;
		this.useStopsInStopPool = options.useStopsInStopPool;
		this.selectedStopPoolCategoryId = options.selectedStopPoolCategoryId;
		this.modalViewModel = modalViewModel;
		this.autoAssign = options.autoAssign;
		this.scheduleTo = options.scheduleTo;
		this.isStop = false;

		this.completed = ko.observable(false);
		this.studentsCount = this.studentIds.length;
		this.leftStudentsCount = ko.observable(this.studentsCount);
		this.previouslyScheduleList = ko.observableArray([]);
		this.successfullyScheduleList = ko.observableArray([]);
		this.partialScheduleList = ko.observableArray([]);
		this.notScheduleList = ko.observableArray([]);
		this.infos = this.getInfos();
		this.currentStudentName = ko.observable("");
		this.studentScheduleOptionsRequests = [];
		this.loadedTrips = {};
		this.vrpURL = arcgisUrls.getTFUtilitiesGPServiceTaskPath("VRPTool");
		this.stopTool = new TF.RoutingMap.RoutingPalette.StopTool();

		this.uturnDic = {
			"allow-backtrack": "ALLOW_UTURNS",
			"at-dead-ends-only": "ALLOW_DEAD_ENDS_ONLY",
			"at-dead-ends-and-intersections": "ALLOW_DEAD_ENDS_AND_INTERSECTIONS_ONLY",
			"no-backtrack": "NO_UTURNS"
		};

		this.tripPathFeatureData = this._createFeatureData(arcgisUrls.getMapEditingOneServiceLayerPath(TF.arcgisLayers.sde.MAP_TRIPPATH), TF.RoutingMap.RoutingPalette.TripPathDataModelMaps, "tripPath");
		this.tripBoundaryFeatureData = this._createFeatureData(arcgisUrls.getMapEditingOneServiceLayerPath(TF.arcgisLayers.sde.MAP_TRIPSTOPBOUNDARY), TF.RoutingMap.RoutingPalette.TripBoundaryDataModelMaps, "tripBoundary");
	};

	FindScheduleForStudentViewModel.prototype.bdyType = { doortodoor: 0, polygon: 1, walkout: 4 };

	FindScheduleForStudentViewModel.prototype.start = function()
	{
		var self = this;
		return self.initFindStudentScheduleHelper().then(function()
		{
			return self._findSchedule(self.studentIds, self.interactive, self.useStopsInStopPool, self.createDoorToDoorStops).then(function()
			{
				return self.complete();
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.initFindStudentScheduleHelper = function()
	{
		var self = this, promises = [];

		if (!self.findScheduleHelper)
		{
			self.findScheduleHelper = new TF.Helper.FindStudentScheduleHelper();
		}

		promises.push(self.findScheduleHelper.initBarriersData());
		return Promise.all(promises);
	};

	FindScheduleForStudentViewModel.prototype.autoSchedule = function(studentIds, createDoorToDoorStops, useStopsInStopPool, selectedStopPoolCategoryId, dbid, progress, isUseExistedfindScheduleHelper)
	{
		var self = this;
		this.initData(
			{
				studentIds: studentIds,
				interactive: false,
				createDoorToDoorStops: createDoorToDoorStops,
				useStopsInStopPool: useStopsInStopPool,
				selectedStopPoolCategoryId: selectedStopPoolCategoryId,
				autoAssign: true,
				dbid: dbid
			});

		if (isUseExistedfindScheduleHelper && self.findScheduleHelper)
		{
			return self._findSchedule(self.studentIds, false, self.useStopsInStopPool, self.createDoorToDoorStops, null, progress);
		}

		self.findScheduleHelper = new TF.Helper.FindStudentScheduleHelper();
		return self.initFindStudentScheduleHelper().then(function()
		{
			return self._findSchedule(self.studentIds, false, self.useStopsInStopPool, self.createDoorToDoorStops, null, progress);
		});
	};

	FindScheduleForStudentViewModel.prototype._findSchedule = function(studentIds, interactive, useStopsInStopPool, createDoorToDoorStops, ignoreCross, progress)
	{
		let scheduleTo = this.scheduleTo;
		let context = createContext({ createDoorToDoorStops, useStopsInStopPool, interactive, studentIds, ignoreCross, progress, scheduleTo });
		if (interactive)
		{
			return this.interactiveRunFindSchedule(context);
		}

		return this.chunkRunFindSchedule(context);
	}

	FindScheduleForStudentViewModel.prototype.interactiveRunFindSchedule = function(context)
	{
		let finishStudentIdIndex = 0;
		return this.sequenceRun(context.studentIds, (id) =>
		{
			return this._findScheduleByChunk(context, [id]).then(() =>
			{
				finishStudentIdIndex++;
				if (this.isStop)
				{
					return false;
				}

				if (!this.interactive)
				{
					context.interactive = this.interactive;
					return this.chunkRunFindSchedule(context, context.studentIds.slice(finishStudentIdIndex, context.studentIds.length))
						.then(() => false);
				}
			})
		}).then((ans) =>
		{
			this.loadedTrips = {};
			return ans;
		});
	};

	FindScheduleForStudentViewModel.prototype.chunkRunFindSchedule = function(context, studentIds)
	{
		//Chunk run the studentIds.
		const chunkSize = 100, parallelCount = 5;
		studentIds = studentIds || context.studentIds;
		const studentCount = studentIds.length;
		studentIds = _.chunk(studentIds, chunkSize);
		let runCount = 0;
		return TF.parallelRun(studentIds, parallelCount, ids =>
		{
			return this._findScheduleByChunk(context, ids).then(result =>
			{
				if (context.progress)
				{
					runCount += ids.length;
					context.progress.Percentage = runCount / studentCount * 100;
					tf.loadingIndicator.changeProgressbar(context.progress.Percentage, context.progress.Message);
				}

				return result;
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.sequenceRun = function(data, func)
	{
		var length = data.length, currentIndex = 0;
		function run(resolve)
		{
			func(data[currentIndex]).then(function(res)
			{
				if (res == false)
				{
					resolve();
					return;
				}
				currentIndex++;
				if (currentIndex < length)
				{
					run(resolve);
				} else
				{
					resolve();
				}
			});
		}
		return new Promise(function(resolve, reject)
		{
			run(resolve);
		});
	}

	function createContext(options)
	{
		return {
			createDoorToDoorStops: options.createDoorToDoorStops,
			useStopsInStopPool: options.useStopsInStopPool,
			interactive: options.interactive,
			studentIds: options.studentIds,
			ignoreCross: options.ignoreCross,
			progress: options.progress,
			tripLockTimesGroup: {},
			tripPromises: {},
			studentResolversGroup: {},
			scheduleTo: options.scheduleTo,
			lockTrips: function(studentInfo)
			{
				if ((!this.createDoorToDoorStops && !this.useStopsInStopPool) || this.interactive)
				{
					return Promise.resolve();
				}

				let tripIds = (studentInfo.ValidRequirements || []).reduce((a, c) => a.concat(c.ValidTripIds || []), []);
				let promises = [];
				let resolvers = [];
				tripIds.forEach(i =>
				{
					let times = this.tripLockTimesGroup[i] || 0;
					this.tripLockTimesGroup[i] = times + 1;
					let promise = this.tripPromises[i];
					if (promise)
					{
						promises.push(promise);
					}

					this.tripPromises[i] = new Promise(resolve => resolvers.push({ resolve: resolve, tripId: i }));
				});

				this.studentResolversGroup[studentInfo.Student.Id] = resolvers;
				if (!promises.length)
				{
					return Promise.resolve();
				}

				return Promise.all(promises);
			},
			releaseTrips: function(studentInfo)
			{
				if ((!this.createDoorToDoorStops && !this.useStopsInStopPool) || this.interactive)
				{
					return Promise.resolve();
				}

				let resolvers = this.studentResolversGroup[studentInfo.Student.Id] || [];
				delete this.studentResolversGroup[studentInfo.Student.Id];

				resolvers.forEach(i =>
				{
					let tripId = i.tripId;
					let times = this.tripLockTimesGroup[tripId] || 0;
					times--;
					if (times <= 0)
					{
						delete this.tripLockTimesGroup[tripId];
						delete this.tripPromises[tripId];
					}

					i.resolve();
				});
			}
		};
	}

	FindScheduleForStudentViewModel.prototype._findScheduleByChunk = function(context, studentIds)
	{
		if (this.isStop) { return Promise.resolve(); }

		if (!this.studentScheduleOptionsRequests)
		{
			this.studentScheduleOptionsRequests = [];
		}

		let xmlHttpRequest;
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.dbid, "studentScheduleOptions"), {
			data: {
				StudentIds: studentIds,
				IgnoreTripIds: Object.keys(this.loadedTrips).map(i => parseInt(i)),
				studentTripItems: this.studentTripItems
			},
			paramData: { compressed: true },
			beforeSend: (xhr) =>
			{
				xmlHttpRequest = xhr;
				this.studentScheduleOptionsRequests.push(xhr);
			}
		}, { overlay: false }).then(res =>
		{
			this.studentScheduleOptionsRequests.splice(this.studentScheduleOptionsRequests.indexOf(xmlHttpRequest), 1);
			if (this.isStop) { return; }

			let promises = [], items = res.Items[0].Options;
			this.setLoadedTrips(res.Items[0].AllValidTrips || {});
			this.initStopGeometry();
			items.forEach(item =>
			{
				if (!item)
				{
					return;
				}

				let promise = context.lockTrips(item).then(() =>
				{
					if (this.isStop) { return; }
					(item.ValidRequirements || []).forEach(i => i.ValidTrips = (i.ValidTripIds || []).map(t => this.loadedTrips[t]));
					const leftCount = this.leftStudentsCount() > 0 ? this.leftStudentsCount() - 1 : 0;
					this.leftStudentsCount(leftCount);
					const scheduleData = item, studentId = scheduleData.Student.Id;
					return this.findScheduleResult(context, scheduleData).then(result =>
					{
						if (!this.isStop)
						{
							switch (result.flag)
							{
								case FindScheduleResult.Skip:
									this.addNotScheduleList(studentId);
									break;
								case FindScheduleResult.FullScheduled:
									this.successfullyScheduleList.push(studentId);
									break;
								case FindScheduleResult.PartialScheduled:
									this.partialScheduleList.push(studentId);
									break;
								case FindScheduleResult.PreviouslyScheduled:
									this.previouslyScheduleList.push(studentId);
									break;
							}
						}

						return result;
					}).catch(() =>
					{
						this.addNotScheduleList(studentId);
						return { flag: FindScheduleResult.Skip }
					}).finally(() =>
					{
						context.releaseTrips(item);
					});
				});

				promises.push(promise);
			});
			return Promise.all(promises).then(results =>
			{
				res.Items.forEach(item => tfdispose(item));
				res.Items = null;
				var tripPatchData = results.map(i => i?.tripPatchData).filter(i => !!i).flat();
				if (tripPatchData.length && !this.isStop)
				{
					return this.patchSaveTrips(tripPatchData);
				}
			});
		}).catch(() =>
		{
			studentIds.forEach(id => this.addNotScheduleList(id));
			return false;
		});
	};

	FindScheduleForStudentViewModel.prototype.setLoadedTrips = function(trips)
	{
		this.loadedTrips = $.extend(trips, this.loadedTrips);
	};

	FindScheduleForStudentViewModel.prototype.updateLoadedTrips = function(trip)
	{
		this.loadedTrips[trip.Id] = trip;
	};

	FindScheduleForStudentViewModel.prototype.addNotScheduleList = function(studentId)
	{
		if (this.notScheduleList().some(x => x === studentId))
		{
			return;
		}

		this.notScheduleList.push(studentId);
	}

	FindScheduleForStudentViewModel.prototype.findScheduleResult = function(context, scheduleData)
	{
		const self = this, student = scheduleData.Student,
			studentId = student.Id;
		if (scheduleData.NotScheduled)
		{
			self.addNotScheduleList(studentId);
			return Promise.resolve({ flag: FindScheduleResult.Skip });
		}
		self.currentStudentName(student.Name);

		if (self.modalViewModel)
		{
			self.modalViewModel.title(student.Name + " Schedule");
		}

		var validRequirements = scheduleData.ValidRequirements || [];
		if (validRequirements.every(t => !t.ValidDays && t.ExistSchedule))
		{
			return Promise.resolve({ flag: FindScheduleResult.PreviouslyScheduled });
		}

		var allStops = [];
		validRequirements.forEach(function(validRequirement)
		{
			if (!validRequirement.ValidTrips || !validRequirement.ValidTrips.length)
			{
				return;
			}
			validRequirement.geometry = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(new tf.map.ArcGIS.Point(validRequirement.LocationX, validRequirement.LocationY, tf.map.ArcGIS.SpatialReference.WGS84));
			allStops = validRequirement.ValidTrips.flatMap(trip => trip.TripStops);
		});

		if (self.isStop)
		{
			return Promise.resolve({ flag: FindScheduleResult.Skip });
		}

		if (!allStops.length && !context.useStopsInStopPool && !context.createDoorToDoorStops)
		{
			return Promise.resolve({ flag: FindScheduleResult.Skip });
		}

		if (context.useStopsInStopPool || context.createDoorToDoorStops)
		{
			return self.findScheduleHelper.attachClosetStreetToStop(allStops, validRequirements).then(function()
			{
				if (context.interactive)
				{
					return self.getAllCandidateStops(context, student, scheduleData);
				}

				return self.autoRunning(context, student, scheduleData);
			})
		}
		else
		{
			if (context.interactive)
			{
				return self.getAllCandidateStops(context, student, scheduleData);
			}

			return self.autoRunning(context, student, scheduleData);
		}
	};

	FindScheduleForStudentViewModel.prototype.autoRunning = function(context, student, scheduleData)
	{
		if (this.isStop) { return Promise.resolve({ flag: FindScheduleResult.Skip }); }

		if (scheduleData.ValidRequirements.every(c => !c.ValidDays && c.ExistSchedule))
		{
			return Promise.resolve({ flag: FindScheduleResult.PreviouslyScheduled });
		}

		var processValidStops = function(validStops, validRequirement)
		{
			validStops = validStops || [];
			if (validStops.length === 0)
			{
				return null;
			}
			if (self.hasOverlapDateRangeOrWeeksForMultipleTrips(validStops))
			{
				return FindScheduleResult.Skip;
			}

			validRequirement.stopBoundaryList = validStops;
			validRequirement.availableDateRanges = toDateRanges(validRequirement.ValidDays);
			let dateRanges = [];
			validStops.forEach(stop =>
			{
				const tripDateRanges = TF.Helper.FindStudentScheduleHelper.getDateRanges(stop.trip);
				if (dateRanges.length === 0)
				{
					dateRanges = tripDateRanges;
				}
				else
				{
					Object.values(dateRanges).forEach((dateRange, index) =>
					{
						dateRanges[index] = dateRanges[index].concat(tripDateRanges[index]);
					})
				}
			});

			const intersection = TF.Helper.FindStudentScheduleHelper.getIntersection(validRequirement.availableDateRanges, dateRanges, true, context.scheduleTo);
			validStops.forEach(stop =>
			{
				stop.intersection = TF.Helper.FindStudentScheduleHelper.getIntersection(validRequirement.availableDateRanges, TF.Helper.FindStudentScheduleHelper.getDateRanges(stop.trip), true, context.scheduleTo);
				stop.intersection.fullIntersected = intersection.fullIntersected;
				stop.intersection.intersected = intersection.intersected;
			})

			if (intersection.fullIntersected)
			{
				return FindScheduleResult.FullScheduled;
			}
			if (intersection.intersected)
			{
				return FindScheduleResult.PartialScheduled;
			}

			return null;
		}, processCreateDoorToDoor = function(validRequirement)
		{
			return self.getDoorToDoor(student, validRequirement).then(function(validStops)
			{
				var result = processValidStops(validStops, validRequirement);
				if (result == null)
				{
					return FindScheduleResult.Skip;
				}

				return result;
			});
		};

		var self = this, promises = scheduleData.ValidRequirements.map(function(validRequirement)
		{
			validRequirement.studentId = scheduleData.Student?.Id;
			return self.getRequirementValidStopByBoundary(validRequirement, false, context.ignoreCross).then(validStops =>
			{
				var result = processValidStops(validStops, validRequirement);
				if (result != null)
				{
					return result;
				}

				if (context.useStopsInStopPool)
				{
					var stopPoolPromise = Promise.resolve();
					if (!self.stopPoolData)
					{
						stopPoolPromise = self.initStoolPool();
					}

					return stopPoolPromise.then(() =>
					{
						return self.getValidStopPool(self.stopPoolData, validRequirement, true).then(validStops =>
						{
							var result = processValidStops(validStops, validRequirement);
							if (result != null)
							{
								return result;
							}

							if (context.createDoorToDoorStops)
							{
								return processCreateDoorToDoor(validRequirement);
							}

							return FindScheduleResult.Skip;
						});
					});
				}

				if (context.createDoorToDoorStops)
				{
					return processCreateDoorToDoor(validRequirement);
				}

				return FindScheduleResult.Skip;
			});
		});

		return Promise.all(promises).then(results =>
		{
			if (results.some(r => r === FindScheduleResult.PartialScheduled || r === FindScheduleResult.FullScheduled))
			{
				return self.findScheduleForStudentAuto(student, scheduleData, context);
			}

			return { flag: FindScheduleResult.Skip };
		});
	};

	FindScheduleForStudentViewModel.prototype.hasOverlapDateRangeOrWeeksForMultipleTrips = function(stops)
	{
		//multiple stops in different trips whose days of the week/date range overlap
		if (stops.length > 1)
		{
			for (let i = 0; i < stops.length - 1; i++)
			{
				for (let j = i + 1; j < stops.length; j++)
				{
					let trip1 = stops[i].trip || stops[i].insertTrip,
						trip2 = stops[j].trip || stops[j].insertTrip,
						tripDateRangeSettings1 = trip1?.TripDateRangeSettings || [trip1],
						tripDateRangeSettings2 = trip2?.TripDateRangeSettings || [trip2],
						tripDateRanges1 = trip1?.TripDateRanges,
						tripDateRanges2 = trip2?.TripDateRanges;
					if (this.compareDateRanges(tripDateRangeSettings1, tripDateRangeSettings2))
					{
						if (tripDateRanges1 && tripDateRanges2)
						{
							this.setDateRangeWeekDays(tripDateRanges1, trip1);
							this.setDateRangeWeekDays(tripDateRanges2, trip2);
							if (this.compareDateRanges(tripDateRanges1, tripDateRanges2))
							{
								return true;
							}
						}
						else
						{
							return true;
						}
					}
				}
			}
		}

		return false;
	}

	FindScheduleForStudentViewModel.prototype.compareDateRanges = function(tripDateRanges1, tripDateRanges2)
	{
		if (!tripDateRanges1 || !tripDateRanges2)
		{
			return false;
		}

		for (var i = 0; i < tripDateRanges1.length; i++)
		{
			for (var j = 0; j < tripDateRanges2.length; j++)
			{
				let range1 = tripDateRanges1[i], range2 = tripDateRanges2[j];
				this.setValidDateRange(range1);
				this.setValidDateRange(range2);
				if (range1.startDate <= range2.endDate && range1.endDate >= range2.startDate
					&& this.hasDayOfWeekOverlap(range1, range2))
				{
					return true;
				}
			}
		}

		return false;
	}

	FindScheduleForStudentViewModel.prototype.setValidDateRange = function(range)
	{
		let startDate = moment(range.StartDate || "0001-01-01T00:00:00");
		let endDate = moment(range.EndDate || "9999-12-31T00:00:00");

		TF.DayOfWeek.all.forEach(day =>
		{
			if (range.StartDate && TF.DayOfWeek[day] === startDate.day() && !range[day])
			{
				startDate = startDate.add(1, 'days');
			}
			if (range.EndDate && TF.DayOfWeek[day] === endDate.day() && !range[day])
			{
				endDate = endDate.subtract(1, 'days');
			}
		});

		range.startDate = startDate;
		range.endDate = endDate;
	}

	FindScheduleForStudentViewModel.prototype.hasDayOfWeekOverlap = function(days1, days2)
	{
		return (!!days1.Monday && !!days2.Monday)
			|| (!!days1.Tuesday && !!days2.Tuesday)
			|| (!!days1.Wednesday && !!days2.Wednesday)
			|| (!!days1.Thursday && !!days2.Thursday)
			|| (!!days1.Friday && !!days2.Friday)
			|| (!!days1.Saturday && !!days2.Saturday)
			|| (!!days1.Sunday && !!days2.Sunday);
	}

	FindScheduleForStudentViewModel.prototype.setDateRangeWeekDays = function(dateRanges, trip)
	{
		if (!this.findScheduleHelper)
		{
			this.findScheduleHelper = new TF.Helper.FindStudentScheduleHelper();
		}

		dateRanges.forEach(dateRange =>
		{
			dateRange.Monday = !!trip.Monday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Monday, dateRange);
			dateRange.Tuesday = !!trip.Tuesday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Tuesday, dateRange);
			dateRange.Wednesday = !!trip.Wednesday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Wednesday, dateRange);
			dateRange.Thursday = !!trip.Thursday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Thursday, dateRange);
			dateRange.Friday = !!trip.Friday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Friday, dateRange);
			dateRange.Saturday = !!trip.Saturday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Saturday, dateRange);
			dateRange.Sunday = !!trip.Sunday && this.findScheduleHelper.weekdayInDateRange(TF.DayOfWeek.Sunday, dateRange);
		});
	}

	var midDaySession = 3;
	FindScheduleForStudentViewModel.prototype.getRequirementValidStopByBoundary = function(validRequirement, interactive, ignoreCross)
	{
		let self = this,
			validStops = [],
			tripStops = [],
			trips = validRequirement.ValidTrips,
			promiseList = [],
			currentSession = validRequirement.StudentRequirement.SessionID;

		trips.forEach(function(trip)
		{
			if (trip.Session != midDaySession && trip.Session != currentSession)
			{
				return;
			}

			var schoolCode = validRequirement.StudentRequirement.SchoolCode,
				schoolTripStop = TF.Helper.FindStudentScheduleHelper.getSchoolTripStop(trip, schoolCode, currentSession);
			if (!schoolTripStop) return;

			trip.TripStops.forEach(function(tripStop)
			{
				if (!tripStop.GeometryBoundaryString && !tripStop.boundary?.geometry)
				{
					return;
				}

				if ((currentSession == 0 && tripStop.Sequence >= schoolTripStop.Sequence)
					|| (currentSession == 1 && tripStop.Sequence <= schoolTripStop.Sequence))
				{
					return;
				}

				var isIntersect = tf.map.ArcGIS.geometryEngine.intersects(validRequirement.geometry, tripStop.boundary.geometry);
				if (!isIntersect)
				{
					return;
				}

				var stopObj = { tripStop: tripStop, trip: trip };
				tripStops.push(stopObj);
			});
		});

		function _getValidCrossStudent(tripStop, req, trip)
		{
			var p = Promise.resolve(true);
			if (!ignoreCross && !self.isStop)
			{
				p = self.findScheduleHelper._isStudentRequirementCanAssign(tripStop, req, trip);
			}

			return p.then(function(res)
			{
				if (ignoreCross || res.isCanAssign)
				{
					let validStop = self.copyAsNew(tripStop);
					validStop.trip = self.copyAsNew(trip);
					validStop.isCross = ignoreCross ? false : res.isCross;
					validStop.StopCrosser = ignoreCross ? false : res.StopCrosser;
					validStop.type = 'stop';
					validStops.push(validStop);
				}
			});
		}

		if (tripStops.length >= 1 || interactive)
		{
			tripStops.forEach(ts =>
			{
				promiseList.push(_getValidCrossStudent(ts.tripStop, validRequirement, ts.trip));
			});
		}

		return Promise.all(promiseList).then(() => validStops);
	};

	FindScheduleForStudentViewModel.prototype.getAllRequirementsValidStopByBoundary = function(context, student, scheduleData)
	{
		let promiseList = scheduleData.ValidRequirements.map(validRequirement =>
		{
			validRequirement.studentId = scheduleData.Student?.Id;
			var subPromiseList = [];
			subPromiseList.push(this.getRequirementValidStopByBoundary(validRequirement, context.interactive, context.ignoreCross));

			if (context.useStopsInStopPool)
			{
				if (!this.stopPoolData)
				{
					subPromiseList.push(this.initStoolPool().then(() => this.getValidStopPool(this.stopPoolData, validRequirement, true)));
				}
				else
				{
					subPromiseList.push(this.getValidStopPool(this.stopPoolData, validRequirement, true));
				}
			}

			if (context.createDoorToDoorStops)
			{
				subPromiseList.push(this.getDoorToDoor(student, validRequirement));
			}

			return Promise.all(subPromiseList).then(result =>
			{
				if (this.isStop) return;

				var stops = [];
				result.forEach(validStops =>
				{
					validStops = validStops || [];
					stops = stops.concat(validStops);
				});

				validRequirement.stopBoundaryList = stops;
			});
		});

		return Promise.all(promiseList).then(() =>
		{
			scheduleData.ValidRequirements = scheduleData.ValidRequirements.filter(r =>
			{
				return r.stopBoundaryList && r.stopBoundaryList.length && (context.interactive || r.stopBoundaryList.length == 1);
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.getAllCandidateStops = function(context, student, scheduleData)
	{
		return this.getAllRequirementsValidStopByBoundary(context, student, scheduleData)
			.then(() =>
			{
				if (this.isStop || !scheduleData.ValidRequirements.length) return { flag: FindScheduleResult.Skip };
				return this.findScheduleForStudentInteractive(student, scheduleData, context);
			});
	};

	FindScheduleForStudentViewModel.prototype.copyAsNew = function(trip)
	{
		return TF.deepClone(trip);
	};

	FindScheduleForStudentViewModel.prototype.initStopGeometry = function()
	{
		$.each(this.loadedTrips, (i, trip) =>
		{
			trip.TripStops.forEach(tripStop =>
			{
				if (tripStop.geometry)
				{
					return;
				}

				if (tripStop.TripPathString)
				{
					tripStop.path = { geometry: this.getPathGeometry(tripStop.TripPathString) };
				}

				if (tripStop.GeometryBoundaryString)
				{
					tripStop.boundary = { geometry: this.getBoundaryGeometry(tripStop.GeometryBoundaryString) };
				}

				tripStop.geometry = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(new tf.map.ArcGIS.Point(tripStop.Xcoord, tripStop.Ycoord, tf.map.ArcGIS.SpatialReference.WGS84));
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.initStoolPool = function()
	{
		this.boundaryFeatureData = this._createFeatureData(arcgisUrls.getMapEditingOneServiceLayerPath(TF.arcgisLayers.sde.MAP_RSPoolBY), TF.RoutingMap.RoutingPalette.StopPoolFeatureData.BoundaryData.maps, TF.RoutingMap.RoutingPalette.StopPoolFeatureData.BoundaryData.type);
		this.stopFeatureData = this._createFeatureData(arcgisUrls.getMapEditingOneServiceLayerPath(TF.arcgisLayers.sde.MAP_RSPoolPT), TF.RoutingMap.RoutingPalette.StopPoolFeatureData.StopPoolData.maps, TF.RoutingMap.RoutingPalette.StopPoolFeatureData.StopPoolData.type);
		return this.query();
	};

	FindScheduleForStudentViewModel.prototype.query = function()
	{
		var self = this;
		var idWhereString = "DBID=" + self.dbid + (this.selectedStopPoolCategoryId ? " and StopPoolCategoryID=" + this.selectedStopPoolCategoryId : "");
		return self.stopFeatureData.query({ where: idWhereString }).then(function(data)
		{
			self.stopPoolData = data;
			return self._queryBoundary();
		}).then(function()
		{
			return self.stopPoolData;
		});
	};

	FindScheduleForStudentViewModel.prototype._queryBoundary = function()
	{
		var self = this;
		if (self.stopPoolData.length == 0)
		{
			return Promise.resolve();
		}
		var stopIds = self.stopPoolData.map(function(c)
		{
			return c.StopId;
		});
		return self.boundaryFeatureData.query({ where: "DBID=" + self.dbid + " and Stop_Id in (" + stopIds.join(",") + ")" }).then(function(boundaryList)
		{
			var boundaryEnumerable = Enumerable.From(boundaryList);
			self.stopPoolData.forEach(function(stop)
			{
				stop.boundary = boundaryEnumerable.FirstOrDefault({}, function(c) { return c.StopId == stop.StopId; });
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.getDoorToDoor = function(student, req)
	{
		var self = this, promiseList = [], result = [], originalTrips = req.ValidTrips,
			trips = originalTrips.map(function(trip)
			{
				return self.copyAsNew(trip);
			});

		function _getStop(stop, originalStop)
		{
			var GeoGeometry = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(originalStop.geometry);
			originalStop.id = TF.createId();
			originalStop.Session = stop.insertTrip.Session;
			originalStop.Sequence = stop.insertIndex + 1;
			originalStop.TripId = stop.insertTrip.Id;
			originalStop.path = { geometry: stop.newStopPath?.geometry };
			originalStop.Distance = parseFloat(stop.newStopPath?.length ?? 0);
			originalStop.time = stop.newStopPath?.time;
			originalStop.LockStopTime = false;
			originalStop.type = 'door to door';
			originalStop.XCoord = GeoGeometry.x;
			originalStop.YCoord = GeoGeometry.y;
			originalStop.DrivingDirections = stop.newStopPath?.direction;
			originalStop.AvgSpeed = !stop.newStopPath ? 0 : stop.newStopPath.length / stop.newStopPath.time * 60;
			originalStop.BdyType = FindScheduleForStudentViewModel.prototype.bdyType.doortodoor;
			originalStop.TripStopGUID = kendo.guid();
			self.resetTripInfo(originalStop, stop.insertTrip, stop.prevPath);
			stop.insertTrip.TripStops.splice(stop.insertIndex, 0, self.copyAsNew(originalStop));
			originalStop.trip = stop.insertTrip;
			return { ...originalStop };
		}

		var schoolTrips = trips.filter(function(t)
		{
			var pathGeometryIsValid = true;
			var tripStops = t.TripStops.sort(function(a, b) { return a.Sequence - b.Sequence })
			for (var i = 0; i < tripStops.length - 1; i++)
			{
				if (!tripStops[i].path || !tripStops[i].path.geometry)
				{
					pathGeometryIsValid = false;
					break;
				}
			}
			return pathGeometryIsValid && (t.Session == req.StudentRequirement.SessionID || t.Session == 3);
		});

		if (schoolTrips.length > 0 && !self.isStop)
		{
			if (req.geometry.x != 0 || req.geometry.y != 0)
			{
				promiseList.push(self.findScheduleHelper.createDoorToDoorStop(req, student).then(function(doorToDoorStop)
				{
					if (self.isStop)
					{
						return false;
					}
					return self.findScheduleHelper.getSmartAssignment(doorToDoorStop, schoolTrips, req.StudentRequirement.SchoolCode, req.StudentRequirement.SessionID).then(function(stops)
					{
						stops.forEach(stop =>
						{
							if (stop)
							{
								result.push(_getStop(stop, doorToDoorStop));
							}
						});
					})
				}));
			}
		}

		return Promise.all(promiseList).then(function(stopsData)
		{
			if (self.isStop)
			{
				return false;
			}
			return result;
		});
	};

	FindScheduleForStudentViewModel.prototype.resetTripInfo = function(newStop, trip, prevPath)
	{
		var timeDiff, distance = 0;
		trip.TripStops.sort(function(a, b)
		{
			return a.Sequence > b.Sequence ? 1 : (a.Sequence < b.Sequence ? -1 : 0);
		});

		let timeOfStopTimeLocked = undefined, timeDiffOfLockTime = 0;
		trip.TripStops.map(function(tripStop)
		{
			tripStop.LockStopTime && (timeOfStopTimeLocked = tripStop.StopTime);
			if (tripStop.Sequence == newStop.Sequence - 1)
			{
				if (!tripStop.path)
				{
					tripStop.path = {};
				}
				tripStop.AvgSpeed = !prevPath ? 0 : prevPath.length / prevPath.time * 60;
				tripStop.DrivingDirections = prevPath?.direction;
				tripStop.RouteDrivingDirections = tripStop.DrivingDirections;
				tripStop.IsCustomDirection = false;
				tripStop.path.geometry = prevPath?.geometry;
				tripStop.Distance = parseFloat(prevPath?.length ?? 0);
				newStop.StopTime = !prevPath ? '' : moment(tripStop.StopTime, "HH:mm:ss").add(prevPath.time, "minutes").format("HH:mm:ss");
			}

			if (tripStop.Sequence >= newStop.Sequence)
			{
				tripStop.Sequence += 1;
				if (tripStop.Sequence == newStop.Sequence + 1)
				{
					if (!newStop.StopTime)
					{
						newStop.StopTime = tripStop.StopTime;
					}

					if (newStop.StopTime)
					{
						var newStopTime = moment(newStop.StopTime, "HH:mm:ss").add(newStop.time, "minutes").format("HH:mm:ss");
						timeDiff = moment.utc("1900-01-01 " + newStopTime).diff(moment.utc("1900-01-01 " + tripStop.StopTime));
						tripStop.StopTime = newStopTime;
					}
					else
					{
						tripStop.StopTime = null;
					}
				}
				else
				{
					tripStop.StopTime = moment(tripStop.StopTime, "HH:mm:ss").add(timeDiff).format("HH:mm:ss");
				}
			}

			if (tripStop.Sequence == 1)
			{
				trip.StartTime = tripStop.StopTime;
			}

			if (tripStop.Sequence == trip.TripStops.length + 1)
			{
				trip.FinishTime = tripStop.StopTime;
			}

			distance += tripStop.Distance;
			if (tripStop.LockStopTime)
			{
				timeDiffOfLockTime = moment.utc("1900-01-01 " + timeOfStopTimeLocked).diff(moment.utc("1900-01-01 " + tripStop.StopTime));
			}
		});

		if (timeDiffOfLockTime !== 0)
		{
			trip.TripStops.forEach(stop =>
			{
				stop.StopTime = moment(stop.StopTime, "HH:mm:ss").add(timeDiffOfLockTime).format("HH:mm:ss");
			});
			newStop.StopTime = moment(newStop.StopTime, "HH:mm:ss").add(timeDiffOfLockTime).format("HH:mm:ss");
		}

		trip.StopCount = trip.TripStops.length + 1;
		trip.Distance = distance;
	};

	FindScheduleForStudentViewModel.prototype.getValidStopPool = function(stopPoolData, req, interactive)
	{
		var self = this, promiseList = [], resultStops = [], originalTrips = req.ValidTrips;
		if (this.isStop) { return Promise.resolve([]); }
		if (!originalTrips.length)
		{
			return Promise.resolve([]);
		}

		function _getStop(stop, originalStop)
		{
			var GeoGeometry = tf.map.ArcGIS.webMercatorUtils.webMercatorToGeographic(originalStop.geometry);
			originalStop.id = TF.createId();
			originalStop.Sequence = stop.insertIndex + 1;
			originalStop.Session = stop.insertTrip.Session;
			originalStop.TripId = stop.insertTrip.Id;
			originalStop.path = { geometry: stop.newStopPath?.geometry };
			originalStop.Distance = parseFloat(stop.newStopPath?.length);
			originalStop.time = stop.newStopPath?.time;
			originalStop.LockStopTime = false;
			originalStop.type = 'stop pool';
			originalStop.XCoord = GeoGeometry.x;
			originalStop.YCoord = GeoGeometry.y;
			originalStop.boundary = originalStop.boundary;
			originalStop.DrivingDirections = stop.newStopPath?.direction;
			originalStop.RouteDrivingDirections = originalStop.DrivingDirections;
			originalStop.IsCustomDirection = false;
			originalStop.AvgSpeed = !stop.newStopPath ? 0 : stop.newStopPath.length / stop.newStopPath.time * 60;
			originalStop.BdyType = FindScheduleForStudentViewModel.prototype.bdyType.polygon;
			originalStop.TripStopGUID = kendo.guid();
			self.resetTripInfo(originalStop, stop.insertTrip, stop.prevPath);
			stop.insertTrip.TripStops.splice(stop.insertIndex, 0, self.copyAsNew(originalStop));
			originalStop.trip = stop.insertTrip;
			return { ...originalStop };
		}

		var schoolTripStops = [],
			trips = originalTrips.map(function(trip)
			{
				return self.copyAsNew(trip);
			});

		var schoolTrips = trips.filter(function(t)
		{
			return t.Session == req.StudentRequirement.SessionID || t.Session == midDaySession;
		});

		stopPoolData.map(function(stopPool)
		{
			if (!stopPool.boundary.geometry)
			{
				return;
			}

			var stop = self.copyToTripStop(stopPool);
			if (tf.map.ArcGIS.geometryEngine.intersects(req.geometry, stop.boundary.geometry) && schoolTrips.length > 0)
			{
				schoolTripStops.push(self.copyAsNew(stop));
			}
		});

		if (schoolTripStops.length == 1 || interactive)
		{
			schoolTripStops.forEach(function(ts)
			{
				promiseList.push(self.findScheduleHelper.getSmartAssignment(ts, schoolTrips, req.StudentRequirement.SchoolCode, req.StudentRequirement.SessionID).then(function(stops)
				{
					stops.forEach(stop =>
					{
						if (stop)
						{
							resultStops.push(_getStop(stop, ts));
						}
					});
				}));
			});
		}

		return Promise.all(promiseList).then(function()
		{
			var promiseList = [], validStops = [];
			resultStops.map(function(tripStop)
			{
				promiseList.push(self.findScheduleHelper._isStudentRequirementCanAssign(tripStop, req, tripStop.trip).then(function(res)
				{
					tripStop.isCross = res.isCross;
					tripStop.StopCrosser = res.StopCrosser;

					if (res.isCanAssign)
					{
						validStops.push(tripStop);
					}
				}));
			});

			return Promise.all(promiseList).then(function()
			{
				return validStops;
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.copyToTripStop = function(tripStop)
	{
		var self = this;
		var newStopBoundary = {
			OBJECTID: 0,
			id: TF.createId(),
			geometry: tripStop.boundary.geometry.clone(),
			type: 'stop pool'
		};
		tripStop.id = TF.createId();
		tripStop = $.extend({}, tripStop, {
			geometry: tripStop.geometry.clone(),
			boundary: newStopBoundary,
			VehicleCurbApproach: 1,
			Street: tripStop.Name,
			ProhibitCrosser: tripStop.ProhibitCrosser,
			TotalStopTime: tripStop.TotalStopTime
		});
		return tripStop;
	};

	FindScheduleForStudentViewModel.prototype._createFeatureData = function(url, dataMaps, type)
	{
		var self = this;
		var featureData = new TF.RoutingMap.FeatureDataModel(
			{
				url: url,
				convertToData: function(item)
				{
					return self._convertLayerDataToData(item, dataMaps, type);
				}.bind(self),
				convertToFeatureData: function(item)
				{
					return self._convertDataToLayerData(item, dataMaps);
				}.bind(self)
			});
		return featureData;
	};

	FindScheduleForStudentViewModel.prototype._convertLayerDataToData = function(item, dataMaps, type)
	{
		var data = this.convertServerToData(item, dataMaps);
		data.geometry = item.geometry;
		data.id = data.OBJECTID;
		if (type)
		{
			data.type = type;
		}
		return data;
	};

	FindScheduleForStudentViewModel.prototype._convertDataToLayerData = function(item, dataMaps)
	{
		var data = TF.RoutingMap.FeatureDataModel.convertDataToServer(item, dataMaps);
		return new tf.map.ArcGIS.Graphic({ geometry: item.geometry, attributes: data });
	};

	FindScheduleForStudentViewModel.prototype.convertServerToData = function(item, dataMaps)
	{
		var data = {};
		var attributesKeyMap = {};
		dataMaps.forEach(function(mapInfo)
		{
			attributesKeyMap[mapInfo.fromServer] = mapInfo.to;
		});
		for (var key in attributesKeyMap)
		{
			if (attributesKeyMap[key])
			{
				if (typeof item.attributes[key] === "string" || item.attributes[key] instanceof String)
				{
					data[attributesKeyMap[key]] = item.attributes[key].trim();
				}
				else
				{
					data[attributesKeyMap[key]] = item.attributes[key];
				}
			}
		}
		return data;
	};

	FindScheduleForStudentViewModel.prototype.getPathGeometry = function(sqlGeometry)
	{
		let geometry = new tf.map.ArcGIS.Polyline(new tf.map.ArcGIS.SpatialReference({ "wkid": 102100 }));
		geometry.paths = getRings(sqlGeometry, 2);
		return geometry;
	};

	FindScheduleForStudentViewModel.prototype.getBoundaryGeometry = function(sqlGeometry)
	{
		var geometry = new tf.map.ArcGIS.Polygon(new tf.map.ArcGIS.SpatialReference({ "wkid": 102100 }));
		geometry.rings = getRings(sqlGeometry);
		return geometry;
	};

	function getRings(geometryStr, maxLength)
	{
		let array = [], c = 0, rings = [];
		geometryStr.split(/([()])/).filter(Boolean).forEach(e =>
		{
			e == '(' ? c++ : e == ')' ? c-- : array.push(e);
		});

		maxLength = maxLength ?? array.length;
		maxLength = Math.min(maxLength, array.length);
		for (let i = 1, l = maxLength; i < l; i += 2)
		{
			let ring = array[i].split(',').map(point => point.split(' ').filter(c => c.trim()).map(parseFloat));
			rings.push(ring);
		}

		return rings;
	}

	function toDateRanges(validDays)
	{
		var result = {};
		TF.DayOfWeek.allValues.forEach(function(i)
		{
			var name = TF.DayOfWeek.toString(i);
			result[i] = [];
			if (validDays[name])
			{
				var ranges = validDays[name + "DateRange"] || [];
				result[i] = ranges.map(function(range)
				{
					return { startDate: range.StartDate, endDate: range.EndDate };
				});
			}
		});

		return result;
	}

	FindScheduleForStudentViewModel.prototype.findScheduleForStudentInteractive = async function(student, scheduleData, context)
	{
		const self = this;
		let noValidStops = true;
		const clientNowDate = moment(utcToClientTimeZone(moment.utc()).format("YYYY-MM-DDT00:00"));
		let requirements = scheduleData.ValidRequirements.map(function(r)
		{
			var originRequirement = r.StudentRequirement,
				locationModel = new TF.DataModel.StudentRequirementModel(originRequirement.StudentLocation),
				availableDateRanges = toDateRanges(r.ValidDays),
				validStops = r.stopBoundaryList.filter(function(s)
				{
					const intersection = TF.Helper.FindStudentScheduleHelper.getIntersection(availableDateRanges, TF.Helper.FindStudentScheduleHelper.getDateRanges(s.trip), true, context.scheduleTo);
					if (intersection.intersected)
					{
						s.intersection = intersection;
					}

					return intersection.intersected;
				}).map(function(s)
				{
					let containsCurrent = false, containsFuture = false;
					if ((s.trip?.TripDateRanges || []).length === 0) //always
					{
						containsCurrent = true;
						containsFuture = true;
					}
					else
					{
						s.trip.TripDateRanges.forEach(r =>
						{
							const startDate = moment(r.startDate || "0001-01-01T00:00:00"), endDate = moment(r.endDate || "9999-12-31T00:00:00");
							if (!containsCurrent && (startDate.isSame(clientNowDate) || startDate.isBefore(clientNowDate))
								&& (endDate.isSame(clientNowDate) || endDate.isAfter(clientNowDate)))
							{
								containsCurrent = true;
							}

							if (!containsFuture && endDate.isAfter(clientNowDate))
							{
								containsFuture = true;
							}
						})
					}

					return {
						id: s.Id ? s.Id : s.id,
						name: s.Street,
						stopTime: s.StopTime,
						type: s.type,
						availableDateRanges: s.intersection.dateRanges,
						tripInfo: {
							id: s.trip.Id,
							name: s.trip.Name,
							maxStudents: s.trip.MaxOnBus,
							capacity: (s.trip.Vehicle ? s.trip.Vehicle.Capacity : null) || s.trip.Capacity,
							stopCount: s.trip.TripStops.length,
							startDate: s.trip.StartDate,
							endDate: s.trip.EndDate,
							startTime: convertToMoment(s.trip.StartTime).format("h:mm a"),
							finishTime: convertToMoment(s.trip.FinishTime).format("h:mm a"),
							TripDateRanges: s.trip.TripDateRanges,
							TripDateRangeSettings: s.trip.TripDateRangeSettings,
						},
						tripStopEntity: s,
						_containsCurrent: containsCurrent,
						_containsFuture: containsFuture
					};
				});

			if (validStops.length > 0)
			{
				noValidStops = false;
				if (validStops.length > 1)
				{
					validStops = Enumerable.From(validStops).OrderByDescending("$._containsCurrent").ThenByDescending("$._containsFuture").ThenBy("$.tripInfo.name").ToArray();
				}
			}

			return {
				id: originRequirement.Id,
				session: TF.SessionType.toString(originRequirement.SessionID, true),
				location: locationModel.locationTypeName(),
				startDate: originRequirement.StartTime,
				endDate: originRequirement.EndTime,
				daysString: TF.DataModel.StudentRequirementModel.daysToString(r.ValidDays),
				days: TF.DataModel.StudentRequirementModel.daysToArray(r.ValidDays),
				validStops: validStops,
				requirementEntity: originRequirement,
				availableDateRanges: availableDateRanges,
				LocationX: r.LocationX,
				LocationY: r.LocationY
			};
		});

		requirements = requirements.filter(r => r.validStops.length > 0);
		if (noValidStops)
		{
			return { flag: FindScheduleResult.Skip };
		}

		const interactiveResult = await tf.modalManager.showModal(new TF.Modal.Grid.FindScheduleForStudentInteractiveModalViewModel({ student: student, requirements: requirements }));
		if (self.isStop)
		{
			return { flag: FindScheduleResult.Skip };
		}

		if (interactiveResult)
		{
			switch (interactiveResult.status)
			{
				case "accept":
					return self.saveStudentFoundSchedules(student, interactiveResult.requirements);
				case "finish":
					self.interactive = false;
					return self.saveStudentFoundSchedules(student, interactiveResult.requirements);
				case "skip":
					return { flag: FindScheduleResult.Skip };
				case "cancel":
					self.stop();
					return { flag: FindScheduleResult.Skip };
			}
		}

		return { flag: FindScheduleResult.Skip };
	};

	FindScheduleForStudentViewModel.prototype.findScheduleForStudentAuto = function(student, scheduleData, context)
	{
		var self = this,
			requirements = scheduleData.ValidRequirements.filter(function(r) { return r.stopBoundaryList && r.stopBoundaryList.length; }).map(function(r)
			{
				var originRequirement = r.StudentRequirement,
					locationModel = new TF.DataModel.StudentRequirementModel(originRequirement.StudentLocation),
					availableDateRanges = r.availableDateRanges || toDateRanges(r.ValidDays),
					validStops = r.stopBoundaryList.filter(function(s)
					{
						var intersection = s.intersection || TF.Helper.FindStudentScheduleHelper.getIntersection(availableDateRanges, TF.Helper.FindStudentScheduleHelper.getDateRanges(s.trip), true, context.scheduleTo);
						if (intersection.intersected)
						{
							s.intersection = intersection;
							if (originRequirement.isFullSchedule == null)
							{
								originRequirement.isFullSchedule = intersection.fullIntersected;
							}
							else
							{
								originRequirement.isFullSchedule = originRequirement.isFullSchedule && intersection.fullIntersected;
							}
						}

						return !!intersection;
					}).map(function(s)
					{
						return {
							id: s.Id ? s.Id : s.id,
							name: s.Street,
							stopTime: s.StopTime,
							type: s.type,
							availableDateRanges: s.intersection.dateRanges,
							tripInfo: {
								id: s.trip.Id,
								name: s.trip.Name,
								maxStudents: s.trip.MaxOnBus,
								capacity: s.trip.Capacity,
								stopCount: s.trip.TripStops.length,
								startDate: s.trip.StartDate,
								endDate: s.trip.EndDate,
								startTime: convertToMoment(s.trip.StartTime).format("h:mm a"),
								finishTime: convertToMoment(s.trip.FinishTime).format("h:mm a"),
							},
							assignedDateBlocks: s.intersection.dateRanges,
							tripStopEntity: s
						};
					});
				return {
					id: originRequirement.Id,
					session: TF.SessionType.toString(originRequirement.SessionID, true),
					location: locationModel.locationTypeName(),
					startDate: originRequirement.StartTime,
					endDate: originRequirement.EndTime,
					daysString: TF.DataModel.StudentRequirementModel.daysToString(originRequirement),
					days: TF.DataModel.StudentRequirementModel.daysToArray(originRequirement),
					validStops: validStops,
					LocationX: r.LocationX,
					LocationY: r.LocationY,
					requirementEntity: originRequirement,
					isFullSchedule: originRequirement.isFullSchedule
				};
			});

		return self.saveStudentFoundSchedules(student, requirements);
	};

	FindScheduleForStudentViewModel.prototype.save = function(trip, backendTrip)
	{
		let tripPaths = [], tripBoundaries = [], stopId = [], date = moment().utc().format("YYYY-MM-DD HH:mm:ss"),
			backendStops = {}, newStop;
		backendTrip.TripStops.forEach(i => backendStops[i.Id] = i);
		trip.TripStops.forEach(stop =>
		{
			let backendStop = backendStops[stop.Id];
			if (backendStop)
			{
				delete backendStops[stop.Id];
			}
			newStop = stop.isNew ? stop : newStop;
			stopId.push(stop.Id);
			if (stop.path && stop.path.geometry)
			{
				stop.path.AvgSpeed = stop.AvgSpeed;
				stop.path.DBID = this.dbid;
				stop.path.Dist = stop.Distance;
				stop.path.TripId = stop.TripId;
				stop.path.TripStopId = stop.Id;
				stop.path.TPTlist = '';
				let isBeforeStop = trip.TripStops.some(ts => ts.Sequence === stop.Sequence + 1 && ts.isNew);
				if (stop.isNew)
				{
					stop.path.LastUpdated = date;
					stop.path.LastUpdatedBy = tf.authManager.authorizationInfo.authorizationTree.userId;
					stop.path.LastUpdatedMethod = "Find Student Schedule";
					stop.path.CreatedOn = date;
					stop.path.CreatedBy = tf.authManager.authorizationInfo.authorizationTree.userId;
				}
				else if (isBeforeStop)
				{
					stop.path.LastUpdated = date;
					stop.path.LastUpdatedBy = tf.authManager.authorizationInfo.authorizationTree.userId;
					stop.path.LastUpdatedMethod = "Find Student Schedule";
				}

				tripPaths.push(this._convertDataToLayerData(stop.path, TF.RoutingMap.RoutingPalette.TripPathDataModelMaps));
			}
			if (stop.boundary && stop.boundary.geometry)
			{
				stop.boundary.BdyType = stop.BdyType;
				stop.boundary.DBID = this.dbid;
				stop.boundary.TripId = stop.TripId;
				stop.boundary.TripStopId = stop.Id;
				tripBoundaries.push(this._convertDataToLayerData(stop.boundary, TF.RoutingMap.RoutingPalette.TripBoundaryDataModelMaps));
			}

			if (backendStop)
			{
				backendStop.geometry = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(new tf.map.ArcGIS.Point(backendStop.Xcoord, backendStop.Ycoord, tf.map.ArcGIS.SpatialReference.WGS84));
				backendStop.path = stop.path;
				backendStop.boundary = stop.boundary;
			}
		});

		if (newStop)
		{
			$.each(backendStops, (k, backendStop) =>
			{
				backendStop.geometry = tf.map.ArcGIS.webMercatorUtils.geographicToWebMercator(new tf.map.ArcGIS.Point(backendStop.Xcoord, backendStop.Ycoord, tf.map.ArcGIS.SpatialReference.WGS84));
				backendStop.path = newStop.path;
				backendStop.boundary = newStop.boundary;
				return false;
			})
		}

		this.updateLoadedTrips(backendTrip);
		return Promise.all([
			this.saveChangeData(this.tripPathFeatureData, trip.Id, stopId, "Tripid", "Tripstop", tripPaths),
			this.saveChangeData(this.tripBoundaryFeatureData, trip.Id, stopId, "Trip_ID", "TripStopID", tripBoundaries)
		]);
	};

	FindScheduleForStudentViewModel.prototype.saveChangeData = function(featureData, tripId, stopIds, tripIdFieldName, stopIdFieldName, newFeatures)
	{
		var dbid = this.dbid;
		return new Promise(function(resolve)
		{
			var query = new tf.map.ArcGIS.Query();
			query.outFields = ["*"];
			query.returnGeometry = false;
			query.where = `${tripIdFieldName}=${tripId} and DBID=${dbid} and ${stopIdFieldName} in (${stopIds.join()})`;
			function save(addFeatures, updateFeatures)
			{
				var editObject = {};
				if (addFeatures.length > 0)
				{
					addFeatures.forEach(function(c)
					{
						c.OBJECTID = 0;
					});
					editObject.addFeatures = addFeatures;
				}
				if (updateFeatures.length > 0)
				{
					editObject.updateFeatures = updateFeatures;
				}
				featureData.getFeatureLayer().applyEdits(editObject).then(function()
				{
					resolve();
				}, function()
				{
					resolve();
				});
			}
			featureData.getFeatureLayer().queryFeatures(query).then(function(featureSet)
			{
				var features = Enumerable.From(featureSet.features || []), addFeatures = [], updateFeatures = [];

				newFeatures.forEach(featureToModify =>
				{
					var feature = features.FirstOrDefault(null, c => c.attributes[stopIdFieldName] == featureToModify.attributes[stopIdFieldName]);
					if (feature)
					{
						featureToModify.attributes.OBJECTID = feature.attributes.OBJECTID;
						updateFeatures.push(featureToModify);
					} else
					{
						addFeatures.push(featureToModify);
					}
				})

				save(addFeatures, updateFeatures);
			});
		});
	};

	FindScheduleForStudentViewModel.prototype.saveStudentFoundSchedules = async function(student, requirements)
	{
		var self = this, promises = [];
		function getDateRangeObj(daysList, dateRange)
		{
			return (daysList || []).find(function(item)
			{
				return TF.Helper.FindStudentScheduleHelper.dateEquals(item.startDate, dateRange.startDate, true) && TF.Helper.FindStudentScheduleHelper.dateEquals(item.endDate, dateRange.endDate, true);
			});
		}

		var isFullSchedule = true, changed = false;
		for (const requirement of requirements)
		{
			var reqEntity = requirement.requirementEntity;
			for (const validStop of requirement.validStops)
			{
				var assignedDateBlocks = validStop.assignedDateBlocks;
				if (!assignedDateBlocks) continue;
				assignedDateBlocks = TF.Helper.FindStudentScheduleHelper.resolveDaysDateRanges(assignedDateBlocks);
				var scheduleDaysList = [];
				TF.DayOfWeek.allValues.forEach(function(i)
				{
					var dayOfWeek = TF.DayOfWeek.toString(i),
						dateRange = assignedDateBlocks[i];
					if (!dateRange) return;

					dateRange.forEach(function(item)
					{
						var current = getDateRangeObj(scheduleDaysList, item);
						if (!current)
						{
							current = item;
							scheduleDaysList.push(current);
						}

						current[dayOfWeek] = true;
					});
				});

				if (!scheduleDaysList.length) continue;

				scheduleDaysList.forEach(function(item)
				{
					item.StartDate = TF.toDateString(item.startDate);
					delete item.startDate;
					item.EndDate = TF.toDateString(item.endDate);
					delete item.endDate;
					item.DBID = student.DBID;
				});

				var schoolTripStop = TF.Helper.FindStudentScheduleHelper.getSchoolTripStop(validStop.tripStopEntity.trip, reqEntity.SchoolCode, reqEntity.SessionID),
					schedule = {
						Sequence: 1,
						StudentRequirementId: reqEntity.Id,
						StudentScheduleDays: scheduleDaysList,
						StudentID: student.Id,
						DBID: student.DBID,
						CrossStatus: validStop.tripStopEntity.isCross,
						StopCrosser: validStop.tripStopEntity.StopCrosser,
						TripId: validStop.tripStopEntity.trip.Id,
						PUStopId: reqEntity.SessionID == TF.SessionType.ToSchool ? validStop.tripStopEntity.Id : schoolTripStop.Id,
						DOStopId: reqEntity.SessionID == TF.SessionType.FromSchool ? validStop.tripStopEntity.Id : schoolTripStop.Id,
						TripStopGUID: validStop.tripStopEntity.TripStopGUID,
						WalkToStopDistance: validStop.tripStopEntity.walkToStopDistance != null ? validStop.tripStopEntity.walkToStopDistance : null
					};

				changed = true;
				if (schedule.TripStopGUID)
				{
					const saveTripStopResult = await saveTripStop(validStop.tripStopEntity, schedule, requirement)
					promises.push(Promise.resolve(saveTripStopResult));
				}
				else
				{
					promises.push(saveTripStop(validStop.tripStopEntity, schedule, requirement));
				}
			}
			if (!requirement.isFullSchedule) isFullSchedule = false;
		};

		function saveTripStop(tripStopEntity, schedule, requirement)
		{
			return self.calculateWalkToStopDistance(tripStopEntity, schedule, requirement).then(() =>
			{
				return self.saveTripStop(tripStopEntity, schedule);
			});
		}

		return Promise.all(promises).then(function(results)
		{
			if (!changed) return { flag: FindScheduleResult.Skip };

			var flag = isFullSchedule ? FindScheduleResult.FullScheduled : FindScheduleResult.PartialScheduled;
			var tripPatchData = results.map(i => i.tripPatchData).filter(i => !!i).flat();
			return { flag: flag, tripPatchData: tripPatchData };
		});
	};

	FindScheduleForStudentViewModel.prototype.calculateWalkToStopDistance = function(tripStopEntity, schedule, requirement)
	{
		if (this.isStop) { return Promise.resolve(); }
		if (schedule.WalkToStopDistance != null)
		{
			return Promise.resolve();
		}
		return TF.calculateDistance(requirement.LocationX, requirement.LocationY, tripStopEntity.Xcoord || tripStopEntity.XCoord, tripStopEntity.Ycoord || tripStopEntity.YCoord).then((distance) =>
		{
			schedule.WalkToStopDistance = distance;
		}).catch(() =>
		{
			schedule.WalkToStopDistance = null;
		});
	};

	FindScheduleForStudentViewModel.prototype.saveTripStop = async function(modifiedTripStop, schedule)
	{
		var self = this;
		if (!modifiedTripStop)
		{
			self.obNotScheduleCount(self.obNotScheduleCount() + 1);
			return Promise.resolve({ flag: true });
		}

		var modifiedTrip = modifiedTripStop.trip,
			tripId = modifiedTrip.Id,
			newTripStop = Enumerable.From(modifiedTrip.TripStops).FirstOrDefault(null, (i) => i.type == 'door to door' || i.type == 'stop pool'),
			data = [];
		data.push({ "Id": tripId, "op": "relationship.add", "path": "StudentSchedules", "value": JSON.stringify([schedule]) });
		if (newTripStop)
		{
			if (!await TF.Helper.TripHelper.CheckGisServer()) { self.stop(); return { flag: FindScheduleResult.Skip }; }
			const stops = [];
			for (let tripStop of modifiedTrip.TripStops)
			{
				tripStop.TripId = tripStop.TripId || tripId;
				if (newTripStop.Sequence - 1 === tripStop.Sequence)
				{
					tripStop.WayPoints = null;
				}
				stops.push(await self.createTripStopData(tripStop));
			}

			data.push({ "Id": tripId, "op": "relationship", "path": "/TripStops", "value": JSON.stringify(stops) });
			return self.patchSaveTrips(data).then(function(result)
			{
				if (!newTripStop)
				{
					return { flag: FindScheduleResult.Skip };
				}

				var tripNew = Enumerable.From(result.Items).FirstOrDefault(null, function(t)
				{
					return t.Id == tripId;
				});

				modifiedTrip.TripStops.map(function(tripStop)
				{
					var tripStopNew = Enumerable.From(tripNew.TripStops).FirstOrDefault(null, function(ts)
					{
						return ts.Sequence == tripStop.Sequence && ts.Id !== tripStop.Id;
					});
					if (tripStopNew)
					{
						tripStop.Id = tripStopNew.Id;
						tripStop.isNew = true;
					}
				});

				return { flag: self.save(modifiedTrip, tripNew) };
			});
		}
		else
		{
			return Promise.resolve({ flag: true, tripPatchData: data });
		}
	};

	FindScheduleForStudentViewModel.prototype.patchSaveTrips = function(data)
	{
		var idDic = (data || []).map(i => i.Id).reduce((acc, item) =>
		{
			acc[item.toString()] = true;
			return acc;
		}, {});

		for (var id in idDic)
		{
			// Don't affect existed TripResources when findschedule
			data.push({ Id: id, Op: "relationship", Path: "AffectFutureCalendarRecord", Value: false });
		}

		return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.dbid, "trips?@relationships=tripstop,StudentSchedule"), {
			data: data,
		})
	}

	FindScheduleForStudentViewModel.prototype.createTripStopData = async function(tripStop)
	{
		const nearestStreets = await this.stopTool.getStreetSegmentsIfStreetStop(tripStop.geometry);
		return {
			id: tripStop.Id,
			TripId: tripStop.TripId,
			Street: tripStop.Street,
			StopTime: tripStop.StopTime,
			VehicleCurbApproach: tripStop.VehicleCurbApproach,
			XCoord: tripStop.XCoord,
			YCoord: tripStop.YCoord,
			Sequence: tripStop.Sequence,
			Comment: tripStop.Comment,
			Distance: tripStop.Distance,
			LockStopTime: tripStop.LockStopTime,
			ProhibitCrosser: tripStop.ProhibitCrosser,
			AvgSpeed: tripStop.AvgSpeed,
			DrivingDirections: tripStop.DrivingDirections,
			BdyType: tripStop.BdyType,
			TripStopGUID: tripStop.TripStopGUID,
			WayPoints: tripStop.WayPoints,
			IsCenterOfStreet: nearestStreets.length > 0
		};
	};

	FindScheduleForStudentViewModel.prototype.setStudentData = function(student)
	{
		return {
			id: student.id,
			Dly_Pu_TripID: student.Dly_Pu_TripID,
			Dly_Pu_TripStop: student.Dly_Pu_TripStop,
			Dly_Pu_CrossToStop: student.Dly_Pu_CrossToStop,
			Dly_Do_TripID: student.Dly_Do_TripID,
			Dly_Do_TripStop: student.Dly_Do_TripStop,
			Dly_Do_CrossToStop: student.Dly_Do_CrossToStop
		};
	};

	FindScheduleForStudentViewModel.prototype.tryToClose = function(prompt)
	{
		if (this.completed())
		{
			return Promise.resolve(true);
		}

		var promise = prompt ? tf.promiseBootbox.yesNo("Do you want to stop finding schedule?", 'Warning') : Promise.resolve(true);
		return promise.then(function(result)
		{
			if (result)
			{
				this.stop();
			}

			return false;
		}.bind(this));
	};

	FindScheduleForStudentViewModel.prototype.stop = function()
	{
		this.isStop = true;
		return this.complete();
	};

	FindScheduleForStudentViewModel.prototype.getInfos = function()
	{
		function getMessage(count, suffix, disableBe)
		{
			return count
				+ (count > 1 ? " records " : " record ")
				+ (disableBe ? '' : (count > 1 ? "were " : "was "))
				+ suffix;
		}

		function getInfo(studentIds, suffix, disableBe)
		{
			return {
				studentIds: studentIds,
				message: ko.pureComputed(function()
				{
					return getMessage(studentIds().length, suffix, disableBe);
				})
			};
		}

		return [
			getInfo(this.previouslyScheduleList, "previously scheduled"),
			getInfo(this.successfullyScheduleList, "successfully scheduled"),
			getInfo(this.partialScheduleList, "partially scheduled"),
			getInfo(this.notScheduleList, "could not be scheduled", true),
		];
	};

	FindScheduleForStudentViewModel.prototype.openStudentGrid = function(ids)
	{
		if (ids.length == 0)
		{
			return;
		}

		var documentData = new TF.Document.DocumentData(TF.Document.DocumentData.Grid,
			{
				gridType: 'student',
				isTemporaryFilter: false,
				gridState: {
					gridFilterId: null,
					filteredIds: ids
				}
			});
		tf.documentManagerViewModel.add(documentData, true, true, "", true, event.shiftKey);
	};

	FindScheduleForStudentViewModel.prototype.stopStudentScheduleOptionsRequest = function()
	{
		if (this.studentScheduleOptionsRequests)
		{
			for (var i = 0; i < this.studentScheduleOptionsRequests.length; i++)
			{
				this.studentScheduleOptionsRequests[i].abort();
			}
		}
		this.studentScheduleOptionsRequests = null;
	};

	FindScheduleForStudentViewModel.prototype.complete = function()
	{
		this.stopStudentScheduleOptionsRequest();
		this.currentStudentName("");
		this.modalViewModel.title("Find Schedule Result");
		this.modalViewModel.obPositiveButtonLabel("OK");
		this.completed(true);
		tfdispose(this.findScheduleHelper);
		return Promise.resolve(true);
	};
})();