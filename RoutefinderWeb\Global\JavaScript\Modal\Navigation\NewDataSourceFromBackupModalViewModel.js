(function()
{
	createNamespace("TF.Modal.Navigation").NewDataSourceFromBackupModalViewModel = NewDataSourceFromBackupModalViewModel;

	function NewDataSourceFromBackupModalViewModel(archiveName)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-sm";
		this.title('New Data Source from Routefinder Plus Archive');
		this.contentTemplate('Navigation/NewDataSourceFromBackup');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Create");
		this.obDisableControl(true);
		this.openDataSourceViewModel = new TF.Navigation.NewDataSourceFromBackupViewModel(this, archiveName);
		this.openDataSourceViewModel.onDisableControl.subscribe(disabled =>
		{
			this.obDisableControl(disabled);
		});
		this.data(this.openDataSourceViewModel);
	};

	NewDataSourceFromBackupModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	NewDataSourceFromBackupModalViewModel.prototype.constructor = NewDataSourceFromBackupModalViewModel;

	NewDataSourceFromBackupModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	NewDataSourceFromBackupModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.cancel()
			.then(function()
			{
				this.negativeClose();
			}.bind(this));
	};

})();