(function()
{
	createNamespace('TF.Control').ManageDocumentContactViewModel = ManageDocumentContactViewModel;

	function ManageDocumentContactViewModel(model, openContactModalOnLoad)
	{
		this.Id = model.Id;
		this.Document = model.Document;
		this.openContactModalOnLoad = !!openContactModalOnLoad;
		this.generateMergeReportDataList = [];
		this.documentContact = [];
		this.addContactClick = this.addContactClick.bind(this);
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.displayName = tf.dataTypeHelper.getDisplayNameByDataType(model.dataType);
	}

	ManageDocumentContactViewModel.prototype.addContactClick = function(model)
	{
		var self = this;
		self.currentViewModel = new TF.Modal.GenerateMergeReportModalViewModel(
			{
				Id: self.Id,
				Document: self.Document
			}, "email", { specifyRecordOption: "Specific Records" });
		tf.modalManager.showModal(self.currentViewModel).then(function(result)
		{
			if (result)
			{
				result.postData.isContactType = self.Document.DataTypeName.toLowerCase() === "contact";
				self.generateMergeReportDataList.push(result);
				self.documentContact.push(getDocumentContactViewModel(self.currentViewModel));
				var gird = self.dataSourceGridContainer.data("kendoGrid");
				gird.dataSource.data(self.documentContact);
			}
		});
	};

	ManageDocumentContactViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.element = $(el);
		self.dataSourceGridContainer = $(el).find(".manageDocumentContact-container");
		self.dataSourceGridContainer.kendoGrid(
			{
				dataSource:
				{
					data: []
				},
				height: 200,
				scrollable: true,
				selectable: true,
				columns: [
					{
						field: "DataSource",
						title: "Data Source",
						encoded: true
					},
					{
						title: "Send As",
						field: "SendAs",
						width: "80px"
					},
					{
						title: "Data Type Refinement",
						field: "DataTypeRefinement",
						width: "160px"
					},
					{
						title: "Record/Filter",
						field: "FilterName"
					},
					{
						title: "# Recipients Selected",
						field: "Count",
						width: "160px"
					},
					{
						title: "",
						width: "80px",
						command: [
							{
								name: "edit",
								template: "<a class=\"k-button k-button-icontext k-grid-edit\" style=\"display: none; right: -33px; position: relative;\" title=\"Edit\"></a>",
								click: function(e)
								{
									e.preventDefault();

									var currentIndex = getselectedItemIndex(e);
									self.currentViewModel = new TF.Modal.GenerateMergeReportModalViewModel(
										{
											Id: self.Id,
											Document: self.Document,
											isEditing: true,
											modalData: self.generateMergeReportDataList[currentIndex].modalData
										}, "email", self.generateMergeReportDataList[currentIndex].postData)
									tf.modalManager.showModal(self.currentViewModel).then(function(result)
									{
										if (result)
										{
											self.generateMergeReportDataList[currentIndex] = result;
											self.documentContact[currentIndex] = getDocumentContactViewModel(self.currentViewModel);
											var gird = self.dataSourceGridContainer.data("kendoGrid");
											gird.dataSource.data(self.documentContact);
										}
									});
								}
							},
							{
								name: "delete",
								template: "<a class=\"k-button k-button-icontext k-grid-delete\" style=\"display: none; right: -33px; position: relative;\" title=\"Delete\"></a>",
								click: function(e)
								{
									e.preventDefault();
									tf.promiseBootbox.confirm({
										message: "Once you remove a Contact, they will not longer receive the Email. Are you sure you want to remove it?",
										title: "Confirmation Message"
									})
										.then(function(result)
										{
											if (result)
											{
												var deleteIndex = getselectedItemIndex(e);
												self.generateMergeReportDataList.splice(deleteIndex, 1);
												var gird = self.dataSourceGridContainer.data("kendoGrid");
												self.documentContact.splice(deleteIndex, 1);
												gird.dataSource.data(self.documentContact);
											}
										});
								}
							}
						]
					}],
				dataBound: function(e)
				{
					var $gridRows = self.dataSourceGridContainer.find('.k-grid-content table.k-grid-table tr');
					$gridRows.each(function display(i, row)
					{
						$(row).find("td a").hide();
					})
					var g = self.dataSourceGridContainer;
					var gridHeight = g.outerHeight();
					var gridHeaderHeight = g.find("table:eq(0)").outerHeight();
					var gridBodyHeight = g.find("table:eq(1)").outerHeight();
					if (gridHeight < gridHeaderHeight + gridBodyHeight)
					{ // show the scrollbar
						g.find(".k-grid-header").css('padding', '');
						g.find(".k-grid-header").css('padding-right', '17px');
						g.find(".k-grid-content").css('overflow-y', 'auto');
					}
					else
					{ // hide the scrollbar
						g.find(".k-grid-header").css('padding-right', '0');
						g.find(".k-grid-content").css('overflow-y', 'auto');
					}
				}
			});

		// mouseover
		$(this.element).on("mouseover", "tr", function()
		{
			if ($(this).find("td").length > 5)
			{
				$(this).addClass(TF.KendoClasses.STATE.SELECTED);
				$(this).find("td a").show();
			}
		});
		// mouseout
		$(this.element).on("mouseout", "tr", function()
		{
			if ($(this).find("td").length > 5)
			{
				$(this).removeClass(TF.KendoClasses.STATE.SELECTED);
				$(this).find("td a").hide();
			}
		});

		if (this.openContactModalOnLoad)
		{
			setTimeout(() =>
			{
				this.addContactClick();
			}, 0);
		}
	};

	ManageDocumentContactViewModel.prototype.isAllRecordWithoutContact = function(emailMappingArray)
	{
		var self = this;
		var noneValid = false;
		if (emailMappingArray.length > 0)
		{
			var invalidRecords = emailMappingArray.filter(checkCount);

			noneValid = (invalidRecords.length == emailMappingArray.length);
		}

		return noneValid;
	};

	function checkCount(oneEmail)
	{
		return testEmail(oneEmail.To).emailList.length == 0 && testEmail(oneEmail.Cc).emailList.length == 0 && testEmail(oneEmail.Bcc).emailList.length == 0;
	}

	ManageDocumentContactViewModel.prototype.sendEmail = function()
	{
		var self = this;
		var allGroups = self.generateMergeReportDataList;
		var data = [];
		var allAreValid = !!allGroups.length, invalidIndex = -1;
		if (allGroups.length === 0)
		{
			return tf.promiseBootbox.alert("No email has been sent as no contact has been specified.", "No Email Sent")
				.then(function()
				{
					return false;
				});
		}
		for (var i = 0; i < allGroups.length; i++)
		{
			var currentData = allGroups[i].postData,
				invalidIndex = i;

			currentData && (currentData.IsContactType = currentData.isContactType);
			data.push(currentData);
			if (self.isAllRecordWithoutContact(currentData.EmailMapping))
			{
				allAreValid = false;
				break;
			}
		}

		if (!allAreValid)
		{
			return self.pageLevelViewModel.popupErrorMessage("At least one recipient is required(To, Cc or Bcc) at row " + (invalidIndex + 1) + ".")
				.then(function()
				{
					return false;
				});
		}

		// not need wait the send result on send merge email
		tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocumentemails"), { data: data }, { overlay: false });

		return tf.modalManager.showModal(new TF.Modal.ResultModalViewModel({ title: "Send Email Result", content: `Merge Email has been submitted. Merge status and results can be found in the "Sent Merge" Grid.` }));
	};

	function getselectedItemIndex(e)
	{
		var row = $(e.target.closest("tr"));
		var allRows = row.parent().find("tr");

		var currentIndex = -1;
		for (var i = 0; i < allRows.length; i++)
		{
			var element = allRows[i];
			if (element == row[0])
			{
				currentIndex = i;
				break;
			}
		}
		return currentIndex;
	}

	function getDocumentContactViewModel(mergeReportModalViewModel)
	{
		function getFilterName(mergeReportModalViewModelData)
		{
			switch (mergeReportModalViewModelData.obSpecifyRecordOptionText())
			{
				case "Specific Records":
					return mergeReportModalViewModelData.obSelectedSpecificRecord().length + " Selected Records";
				case "Filter":
					return mergeReportModalViewModelData.obFilterText();
				default:
					return "";
			}
		}

		let generateMergeReportViewModel = mergeReportModalViewModel.data();
		let recipientsNumber = generateMergeReportViewModel.modalData.selectedRecordContactIds.length + generateMergeReportViewModel.modalData.selectedEmailUDFs.length;

		return {
			DataSource: generateMergeReportViewModel.selectedDatasourceText(),
			SendAs: generateMergeReportViewModel.destinationType(),
			DataTypeRefinement: generateMergeReportViewModel.obSpecifyRecordOptionText(),
			FilterName: getFilterName(generateMergeReportViewModel),
			Count: recipientsNumber
		};
	}
})();
