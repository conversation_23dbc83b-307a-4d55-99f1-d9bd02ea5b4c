﻿/**
* Map symbol for ArcGIS API for Javascript 4.X
*/

(function()
{
	createNamespace("TF.Map").Symbol = Symbol;

	function Symbol()
	{
		this.ArcGIS = tf.map.ArcGIS;

		this.default = {
			color: "#FF5500",
			alpha: 0.75
		};
		this.selectionColor = "#B0FFFF";

		this._colorList = ["#FF0800", "#8800FF", "#3333FF", "#FF6700", "#FF00FF", "#00FFFF", "#73D952", "#FFFF00", "#AA0000", "#0000A2", "#CC5200", "#E10087", "#00CCCC", "#006600", "#FFCC00", "#D47F7F", "#7F7FD0", "#E5A87F", "#F07FC3", "#7FE5E5", "#7FB27F", "#FFE57F"];

		this.symbolColors = {
			blueForSelect: [39, 147, 226],
			orangeForCreate: [255, 168, 80],
			yellowForHighlight: [255, 255, 0, 255],
			grayForEditing: [90, 90, 90],
			bluePoint: [0, 51, 204],
			greenPoly: [0, 136, 0],
			brightGreen: [55, 247, 62]
		};

		this.offSetX = 16;
		this.offSetY = 8;
	}

	Symbol.prototype.school = function(hexColor, alpha, size, outline)
	{
		var symbol, rgb;
		hexColor = hexColor ? hexColor : "#E000E0";
		alpha = alpha || alpha == 0 ? alpha : this.default.alpha;
		rgb = this.ArcGIS.Color.fromHex(hexColor);
		rgb.a = alpha;
		size = size ? size : 16;
		var pathString = 'M9.000,5.000 L9.000,10.000 L-0.000,10.000 L-0.000,5.000 L4.000,5.000 L4.000,3.000 L4.000,2.000 L4.000,-0.000 L5.000,-0.000 L6.000,-0.000 L6.000,1.000 L5.000,1.000 L5.000,2.000 L6.000,2.000 L6.000,3.000 L5.000,3.000 L5.000,5.000 L9.000,5.000 ZM8.000,1.000 L8.000,2.000 L6.000,2.000 L6.000,1.000 L8.000,1.000 Z';
		symbol = new this.ArcGIS.SimpleMarkerSymbol();
		symbol.size = size;
		symbol.color = rgb;
		symbol.path = pathString;
		symbol.outline = outline ? outline : new this.ArcGIS.SimpleLineSymbol({ style: "none" });

		return symbol;
	};

	Symbol.prototype.student = function(hexColor, alpha, size)
	{
		var symbol, rgb;
		hexColor = hexColor ? hexColor : '#6B7CFC';
		alpha = alpha ? alpha : 1;
		size = size ? size : 16;
		rgb = this.ArcGIS.Color.fromHex(hexColor);
		rgb.a = alpha;
		symbol = {
			type: "simple-marker",
			color: rgb,
			size: size
		};
		if (size < 2)
		{
			symbol.outline = null;
		}
		else
		{
			symbol.outline = new tf.map.ArcGIS.SimpleLineSymbol({ width: 1.333 });
		}
		return symbol;
	};

	Symbol.prototype.assignedStudent = function(hexColor)
	{
		var rgb;
		hexColor = hexColor ? hexColor : this.default.color;
		rgb = this.ArcGIS.Color.fromHex(hexColor);
		var pathString = "M101.141 14.0454C116.141 29.0454 116.141 75.0454 101.141 90.0454C93.1407 98.0454 93.1407 102.045 100.141 102.045C116.141 102.045 152.141 163.045 146.141 179.045C143.141 186.045 135.141 192.045 127.141 192.045C118.141 192.045 113.141 210.045 111.141 249.045C108.141 306.045 108.141 307.045 78.1407 307.045C49.1407 306.045 48.1407 305.045 45.1407 249.045C42.1407 200.045 39.1407 192.045 23.1407 192.045C-4.8593 192.045 -2.8593 160.045 27.1407 125.045C45.1407 103.045 49.1407 92.0454 41.1407 73.0454C35.1407 56.0455 37.1407 42.0455 48.1407 26.0454C65.1407 0.0454478 83.1407 -3.95455 101.141 14.0454Z";

		return {
			type: "simple-marker",
			path: pathString,
			color: rgb,
			size: 20
		};
	};

	Symbol.prototype.unassignedStudent = function()
	{
		return this.student("#005CE6", 0.75);
	};

	Symbol.unassignedStudentColor = "#1940aa";
	Symbol.unassignedStudentHighlightColor = "#ffff00";

	Symbol.prototype.getUnassignedStudentSymbol = function()
	{
		return this.student(TF.Map.Symbol.unassignedStudentColor, 1, 10);
	};

	Symbol.prototype.getPartMatchStudentSymbol = function()
	{
		var symbol = this.student("#60b5ff", 1, 10);
		symbol.outline = new tf.map.ArcGIS.SimpleLineSymbol({ width: 2, color: "#005CE6" });
		return symbol;
	};

	Symbol.prototype.getUnassignedStudentHighlightSymbol = function()
	{
		return this.student(TF.Map.Symbol.unassignedStudentHighlightColor, 1, 10);
	};

	Symbol.prototype.getAssignedStudentSymbol = function(color)
	{
		return this.student(color, 1, 10);
	};

	Symbol.prototype.stop = function(hexColor, width, alpha)
	{
		var rgb;
		hexColor = hexColor ? hexColor : this.default.color;
		alpha = alpha ? alpha : 1;
		width = width ? width : 6;

		rgb = this.ArcGIS.Color.fromHex(hexColor);
		rgb.a = alpha;

		return {
			type: "simple-marker",
			path: "M30 12 L20 12 L20 2 L12 2 L12 12 L2 12 L2 20 L12 20 L12 30 L20 30 L20 20 L30 20 Z",
			color: rgb,
			size: 14,
			outline: null
		};
	};

	Symbol.prototype.tripStop = function(sequence, color)
	{
		var labelColor = TF.isLightness(color) ? "#000000" : "#ffffff"
		var svgString = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" width="28" height="28">' +
			'<g>' +
			'<circle r="12" cy="14" cx="14" stroke-linecap="butt" stroke="#000000" stroke-width="2" fill="{2}" />' +
			'<text text-anchor="middle" font-size="12" x="50%" y="50%" dy=".3em" stroke-width="0" fill="{1}" >{0}</text>' +
			'</g>' +
			'</svg >';
		var svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(String.format(svgString, sequence, labelColor, color || 'gray'));
		return new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: 28, width: 28 });
	};

	Symbol.prototype.tripBoundaryPolygon = function(color)
	{
		return {
			type: "simple-fill",
			style: "solid",
			color: this.setHexColorOpacity(color, 0.2),
			outline: {
				type: "simple-line",
				style: "solid",
				color: color,
				width: 2
			}
		};
	};

	Symbol.prototype.tripPath = function(color)
	{
		return {
			type: "simple-line",
			style: "solid",
			color: color,
			width: TF.RoutingMap.BaseMapDataModel.getSettingByKey("pathThicknessRouting", 5)
		};
	};

	Symbol.prototype.createStudentLabelSymbol = function(data, autoOffset)
	{
		var self = this;
		var width = 60, height = 18;
		var svgString = "<svg xmlns='http://www.w3.org/2000/svg' xmlns:svg='http://www.w3.org/2000/svg' width='{0}' height='{1}'>" +
			"<g>" +
			"<rect x='0' y='0' rx='3px' ry='3px' stroke-width='2px' stroke='black' width='{0}' height='{1}' fill='white'/>" +
			"<text x='5' y='{3}' color='black' font-size='10'>{2}</text>" +
			"</g>" +
			"</svg>";
		const text = data.studentCount === 1 ? " Student" : " Students";
		width = data.studentCount === 1 ? 50 : width;
		var svg = "data:image/svg+xml;charset=UTF-8;base64," + btoa(String.format(svgString, width, height, data.studentCount + text, height / 2 + 4));
		var symbol = new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: height, width: width, xoffset: (autoOffset != false ? (-self.offSetX - width / 2) : 0), yoffset: (autoOffset != false ? (-self.offSetY - height / 2) : 0) });

		return symbol;
	};

	Symbol.prototype.trialStopSymbol = function(color)
	{
		var svgString = "<svg xmlns='http://www.w3.org/2000/svg' xmlns:svg='http://www.w3.org/2000/svg' width='28' height='28'>" +
			"<g>" +
			"<circle r='12' cy='14' cx='14' stroke-linecap='butt' fill='{0}' />" +
			"<rect x='8' y='8' height='12' width='12' fill='white'/>" +
			"</g>" +
			"</svg >";
		var svg = "data:image/svg+xml;charset=UTF-8;base64," + btoa(String.format(svgString, color || "black"));
		return new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: 32, width: 32 });
	};

	Symbol.prototype.labelSymbol = function(text, width, height, backgroundColor, borderColor, xoffset, yoffset)
	{
		var svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(
			'<svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 {width} {height}" enable-background="new 0 0 {width} {height}" xml:space="preserve" ><rect x="0" y="0" rx="3px" ry="3px" stroke-width="2px" stroke="{borderColor}" width="{width}" height="{height}" fill="{background}"/> <text x="3" y="14"  font-family="Verdana" font-size="12">{content}</text></svg>'
				.replace(/{content}/g, text)
				.replace(/{borderColor}/g, borderColor)
				.replace(/{background}/g, backgroundColor)
				.replace(/{width}/g, width)
				.replace(/{height}/g, height));
		return new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: height + "px", width: width + "px", xoffset: xoffset + "px", yoffset: yoffset + "px" });
	};

	Symbol.prototype.createStopPoolSymbol = function(color)
	{
		var self = this;
		color = color ? color : self.symbolColors.bluePoint;
		return {
			type: "simple-marker",
			style: "circle",
			color: color,
			size: 14,
			outline: {
				type: "simple-line",
				style: "solid",
				color: [0, 0, 0],
				width: 2
			}
		};
	};
	Symbol.prototype.highlightStopPoolSymbol = function(color)
	{
		var self = this;
		color = color ? color : self.symbolColors.bluePoint;
		return {
			type: "simple-marker",
			style: "circle",
			color: color,
			size: 14,
			outline: {
				type: "simple-line",
				style: "solid",
				color: self.symbolColors.yellowForHighlight,
				width: 2
			}
		};
	};

	Symbol.prototype.PolygonReDrawSymbol = function()
	{
		var self = this;
		return {
			type: "simple-fill",
			style: "solid",
			color: self._setOpacity(self.symbolColors.grayForEditing, 80),
			outline: {
				type: "simple-line",
				style: "solid",
				color: self.symbolColors.grayForEditing,
				width: 2
			}
		};
	};

	Symbol.prototype.PolygonCreateSymbol = function()
	{
		var self = this;
		return {
			type: "simple-fill",
			style: "solid",
			color: self._setOpacity(self.symbolColors.orangeForCreate, 80),
			outline: {
				type: "simple-line",
				style: "solid",
				color: self.symbolColors.orangeForCreate,
				width: 2
			}
		};
	};
	Symbol.prototype.highlightPolygonSymbol = function()
	{
		var self = this;
		return {
			type: "simple-fill",
			style: "solid",
			color: self._setOpacity(self.symbolColors.orangeForCreate, 80),
			outline: {
				type: "simple-line",
				style: "solid",
				color: self.symbolColors.yellowForHighlight,
				width: 2
			}
		};
	};

	Symbol.prototype.getHighlightLineSymbol = function()
	{
		return {
			type: "simple-line",
			color: [255, 255, 0],
			width: 5,
		};
	};

	Symbol.prototype.tripStopSimpleMarker = function(color, size)
	{
		return {
			type: "simple-marker",
			style: "circle",
			color: color,
			size: size || 16,
			outline: {
				type: "simple-line",
				style: "solid",
				color: "black",
				width: 2
			}
		};
	};

	Symbol.prototype.tripStopLabel = function(color)
	{
		var labelColor = TF.isLightness(color) ? "#000000" : "#ffffff"
		return {
			type: "text",
			text: "Stop",
			yoffset: -3,
			color: labelColor,
			font: {
				size: 12
			}
		};
	};

	Symbol.prototype.studentCount = function(sequence, color)
	{
		var labelColor = TF.isLightness(color) ? "#000000" : "#ffffff"
		var svgString = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" width="28" height="28">' +
			'<g>' +
			'<circle r="12" cy="14" cx="14" stroke-linecap="butt" fill="{2}" />' +
			'<text text-anchor="middle" font-size="12" font-family="sans-serif" x="50%" y="50%" dy=".3em" stroke-width="0" fill="{1}" >{0}</text>' +
			'</g>' +
			'</svg >';
		var svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(String.format(svgString, sequence, labelColor, color));
		return new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: 24, width: 24 });
	};

	Symbol.prototype.stopSequenceLabel = function(label, color)
	{
		var labelColor = TF.isLightness(color) ? "#000000" : "#ffffff"
		var svgString = '<svg xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" width="28" height="28">' +
			'<g>' +
			'<circle r="12" cy="14" cx="14" stroke="black" stroke-width="2" fill="{2}" />' +
			'<text text-anchor="middle" font-size="12" font-family="sans-serif" x="50%" y="50%" dy=".3em" stroke-width="0" fill="{1}" >{0}</text>' +
			'</g>' +
			'</svg >';
		var svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(String.format(svgString, label, labelColor, color));
		return new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: 24, width: 24 });
	};

	Symbol.prototype.schoolLocation = function(color, outlineColor, outline)
	{
		color = color || [112, 123, 249];
		outlineColor = outlineColor || [215, 236, 254];
		var symbol = new tf.map.ArcGIS.SimpleMarkerSymbol(),
			pathString = "M7,8.5 L7,29 L8,29 L8,14 L23,14 L23,3 L7,3 L7,8.5 L7,8.5 Z";
		symbol.size = 20;
		symbol.xoffset = 7;
		symbol.yoffset = 9;
		symbol.color = color;
		symbol.path = pathString;
		symbol.outline = outline ? outline : new tf.map.ArcGIS.SimpleLineSymbol({
			color: outlineColor,
			width: 2
		});
		return symbol;
	};

	Symbol.prototype.schoolLocationBoundarySymbol = function()
	{
		return this.drawPolygonSymbol(this.symbolColors.brightGreen);
	};

	Symbol.prototype.getVehicleSymbol = function(heading)
	{
		var url = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAUgQAAFIEBla/LogAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAPlSURBVEiJnZXPSyRHFMe/VV09PXOYQBbZ0ZVRL7ogITE6Bxf0IrL+2MFA2KMKC4InPe+e9Jbsn6AxIYi3eIk/unvUm5LdwxgkOBvQU2ZWtwc1l1no+VFVLwedQd0Zd8wXGpr3Xr0P79WrKoY69fvW1qOAUs1E9CUR/StN8/S7p09P61nL7nJubGx8xTifYow9J6Lm237O2HtFtCo4XxoeHk7dC5JIJB5KrV+DaNKyLNXU2Gg2NDQgFArBNE2USiX4vo+z83N4nlcqFAoGA34F8HJ0dPTss5B11/3GINoUQkQed3SIaDQKxmoXTERIp9M4Oj6WUkqPAc9GRkb+qgm5AvwRDocDsVhMBC2rZvLbyufzSO7vy1wuV2TAk+ugCiSRSDxUWv8ZDocjT3p7hWEYdQPKUkrhzdu3MpfLZUH0bbl1vBwgtX4thIjEYjFhGAaI6N4QwzAQ6+kRQogIMfZD2c6ByykC0eTjjo5KixhjmJmZweLiIvL5fN2gYDCIjvZ2AaIXtm13ViCM8ynLslQ0Gr2xIB6PY3p6Gm1tbZifn8fFxUVdoJaWFliWpYhoqmJ0HCeTeveOqqmvr48AEACyLIsmJiYolUpVjb2uw1SKbMd5D+DyJG/aNmWz2arBrutWIOWPc06Dg4O0trZGWuuq67xsljZtm1zXbeIBpZoBIBQKVS19aGgIfX19N2xaa+zs7GBsbAzd3d1V9y0UDAIApJSPuAIeAIBpmjV7PD4+XtN3cHCA6elp9Pf3I5vNVuyBQODyxzAaONP6AgCKpVLNRCsrKzV9XV1dWFhYwO7uLiKRSMVeLBYBAMTYmZCmeSqkRN738UU4/EmSRCKBvb29GzbOOQYGBjA7O4t4PF712vF9/zJWqcub2nGcTK2J+d/TdXhItuv+A1ydE0W0+sHzSnTrlJeriEQimJubw8nJCZaXl9HZ2VmzfcDlYHieJ4loFQAEAAjOlwqFwmw6nUZra2sleGNjAwsLC5icnETwalrqUTqTQaFYZKT10g2Hbds/b21vl3zfJyKqOf+fk+/7tLW9Xdp0nJ/Kufk1zksppZfc35dKqTvfkFpSSiGZTEolpVcwzZdl+41MjuN8TcCbcDgciPX0iPu0KJ/PI5lMytzHjwWtVG88Hj+sCrkG2hRCNHa0t4uWlpY7q9JaI53J4PjoSEqtT7WUz64DqkIAYH19vYEL8SOIXliWpRqvvfEB00Tx6o0/PzuDl82WCoUCZ8Av+UDg1feDg59c1Xc23rbtTiKa4obxXGsdve3nnGc00W+k1FI8Hv+7Vp66d9d13SalVDNj7IHW+kIIcTo8PPyhnrX/ASZevYMuAxBOAAAAAElFTkSuQmCC';
		var symbol = new tf.map.ArcGIS.PictureMarkerSymbol({ url: url, height: 25, width: 25 });
		if (heading)
		{
			symbol.angle = heading - 90;
		}
		else
		{
			symbol.angle = -90;
		}
		return symbol;
	};

	Symbol.prototype.getVehicleSymbolWithColor = function(heading, color)
	{
		let svgString = `<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" width="56px" height="56px">
			<path stroke="${color}" fill="none" d="M 12.035156 2 C 6.5121563 2 2.0351563 6.477 2.0351562 12 C 2.0351562 17.523 6.5121562 22 12.035156 22 C 17.558156 22 22.035156 17.523 22.035156 12 C 22.035156 6.477 17.558156 2 12.035156 2 z  "/>
			<path stroke="${color}" fill="white" d="M 12.035156 3 C 16.998156 3 21.035156 7.037 21.035156 12 C 21.035156 16.963 16.998156 21 12.035156 21 C 7.0721562 21 3.0351562 16.963 3.0351562 12 C 3.0351562 7.037 7.0721562 3 12.035156 3 z"/>
			<path  d="M 16.222656 7.4335938 C 16.133781 7.4124687 16.038719 7.4167656 15.949219 7.4472656 L 6.3867188 10.638672 C 6.1827188 10.706672 6.0449219 10.896328 6.0449219 11.111328 C 6.0449219 11.326328 6.1827188 11.517938 6.3867188 11.585938 L 10.931641 13.101562 L 12.447266 17.648438 C 12.515266 17.852438 12.706875 17.990234 12.921875 17.990234 C 13.136875 17.990234 13.327484 17.852437 13.396484 17.648438 L 16.582031 8.0800781 C 16.642031 7.9000781 16.596891 7.7023594 16.462891 7.5683594 C 16.395891 7.5013594 16.311531 7.4547188 16.222656 7.4335938 z"/>
		</svg>`;

		let svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(svgString);
		let symbol = new tf.map.ArcGIS.PictureMarkerSymbol({ url: svg, height: 28, width: 28 });
		if (heading)
		{
			symbol.angle = heading - 45;
		}
		else
		{
			symbol.angle = -45;
		}
		return symbol;
	}

	Symbol.prototype.getEventSymbol = function(isStopEvent, color)
	{
		var symbol;
		if (isStopEvent)
		{
			symbol = this.crossInCircle(color, 18, 0);
		} else
		{
			symbol = new tf.map.ArcGIS.SimpleMarkerSymbol();
			symbol.color = new tf.map.ArcGIS.Color(color);
			symbol.size = 5;
		}
		return symbol;
	};

	Symbol.crossInCircleTemplate =
		'<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" viewBox="0 0 24 24" x="0px" xml:space="preserve" y="0px">'
		+ '<g>'
		+ '<circle cx="12" cy="12" fill="none" r="11" stroke="black" stroke-miterlimit="10" stroke-width="1"/>'
		+ '<circle cx="12" cy="12" fill="white" r="10"/>'
		+ '<circle cx="12" cy="12" fill="none" r="10" stroke="{color}" stroke-miterlimit="10" stroke-width="1"/>'
		+ '<rect x="9" y="5" height="14" width="6" fill="black"/>'
		+ '<rect x="5" y="9" height="6" width="14" fill="black"/>'
		+ '<rect x="10" y="6" height="12" width="4" fill="{color}"/>'
		+ '<rect x="6" y="10" height="4" width="12" fill="{color}"/>'
		+ '</g>'
		+ '</svg>';
	Symbol.circleTemplate = '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 6 6" viewBox="0 0 6 6" x="0px" xml:space="preserve" y="0px">'
		+ '<circle cx="3" cy="3" r="3" fill="black"/>'
		+ '<circle cx="3" cy="3" r="2" fill="{color}"/>'
		+ '</svg>';
	Symbol.prototype.crossInCircle = function(color, size, offset)
	{
		var svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(Symbol.crossInCircleTemplate.split("{color}").join(color));
		return this.svgSymbol(svg, size, offset);
	};

	Symbol.prototype.circle = function(color)
	{
		var size = [6, 6];
		var svg = 'data:image/svg+xml;charset=UTF-8;base64,' + btoa(Symbol.circleTemplate.split("{color}").join(color));
		return this.svgSymbol(svg, size);
	};

	Symbol.prototype.editVertex = function()
	{
		var symbol = new this.ArcGIS.SimpleMarkerSymbol();
		symbol.setSize(10);
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setColor(new this.ArcGIS.Color([255, 128, 0, 0.6])).setWidth(2));
		symbol.setColor(new this.ArcGIS.Color([255, 128, 0, 0.5]));

		return symbol;
	};

	Symbol.prototype.editGhostVertex = function()
	{
		var symbol = new this.ArcGIS.SimpleMarkerSymbol();
		symbol.setSize(10);
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setColor(new this.ArcGIS.Color([255, 128, 0, 0.3])).setWidth(2));
		symbol.setColor(new this.ArcGIS.Color([255, 128, 0, 0.2]));

		return symbol;
	};

	Symbol.prototype.editGhostLine = function(hexColor)
	{
		hexColor = hexColor ? hexColor : "#89C8C7";
		var rgb = this.ArcGIS.Color.fromHex(hexColor);
		rgb.a = 0.4;

		var symbol = new this.ArcGIS.SimpleLineSymbol();
		symbol.style = this.ArcGIS.SimpleLineSymbol.STYLE_LONGDASH;
		symbol.color = rgb;
		symbol.width = 2;

		return symbol;
	};

	Symbol.prototype.formSubmittedPoint = function()
	{
		return {
			type: "simple-marker",
			color: [18, 89, 208, 1],
			size: 6
		};
	};

	Symbol.prototype.formPinPoint = function()
	{
		return {
			type: "simple-marker",
			color: [0, 92, 230, 1],
			size: 8
		};
	};

	Symbol.prototype.georegionPoint = function()
	{
		return {
			type: "simple-marker",
			color: [18, 89, 208, 1],
			size: 6
		};
	};

	Symbol.prototype.georegionPointByType = function(georegionType)
	{
		let symbolSVG = null, symbolSetting;
		let settings = georegionType?.SymbolSetting;
		if (settings)
		{
			symbolSetting = JSON.parse(settings);
			if (symbolSetting && symbolSetting.symbol !== "default" && symbolSetting.symbol !== "-1")
			{
				symbolSVG = this.getSVGMarkSymbol(symbolSetting);
			}
		}
		return symbolSVG;
	};

	Symbol.prototype.georegionPolygon = function(geoRegionType, isHighlighted)
	{
		var opacity = 0,
			borderWidth = geoRegionType ? geoRegionType.BoundaryThickness : 1,
			currentColor = geoRegionType && geoRegionType.BoundaryColor ? this.ArcGIS.Color.fromHex("#" + geoRegionType.BoundaryColor) : this.ArcGIS.Color.fromHex('#FF0000');
		if (geoRegionType)
		{
			if (geoRegionType.BoundaryFill === 'Semi')
			{
				opacity = 0.4;
			}
			else if (geoRegionType.BoundaryFill === 'Solid')
			{
				opacity = 1;
			}
		}

		var outLineColor = isHighlighted ? this.symbolColors.yellowForHighlight : currentColor.clone(),
			fillColor = currentColor.clone();
		fillColor.a = opacity;
		return {
			type: "simple-fill",
			style: "solid",
			color: fillColor,
			outline: {
				type: "simple-line",
				style: "solid",
				color: outLineColor,
				width: borderWidth
			}
		};
	};

	Symbol.prototype.geosearchPolygon = function()
	{
		var symbol = null;
		symbol = new this.ArcGIS.SimpleFillSymbol();
		symbol.setColor(new this.ArcGIS.Color([18, 89, 208, 0.3]));
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setWidth(1).setColor([18, 89, 208, 0.6]));
		return symbol;
	};

	Symbol.prototype.geosearchLine = function()
	{
		var symbol = null;
		symbol = new this.ArcGIS.ColorSimpleLineSymbol().setWidth(1).setColor([18, 89, 208, 0.6]);
		return symbol;
	};

	Symbol.prototype.trip = function(hexColor, width, alpha, style)
	{
		var symbol, rgb;
		hexColor = hexColor ? hexColor : this.default.color;
		width = width ? width : 2;
		alpha = alpha ? alpha : 0.4;
		rgb = this.ArcGIS.Color.fromHex(hexColor);
		rgb.a = alpha;

		style = style ? style : this.ArcGIS.SimpleLineSymbol.STYLE_SOLID;

		symbol = new this.ArcGIS.SimpleLineSymbol().setWidth(width).setColor(new this.ArcGIS.Color(rgb)).setStyle(style);
		return symbol;
	};

	Symbol.prototype.svgSymbol = function(svg, size, offset)
	{
		return new tf.map.ArcGIS.PictureMarkerSymbol({
			url: svg,
			height: $.isArray(size) ? size[0] : size,
			width: $.isArray(size) ? size[1] : size,
			xoffset: offset ? offset.x : 0,
			yoffset: offset ? offset.y : 0
		});
	};

	Symbol.prototype.pathSymbol = function(pathString, color, size, isOutline, outlineColor, outlineWidth)
	{
		var symbol, dString;
		dString = $(pathString).attr("d");
		symbol = {
			type: "simple-marker",
			style: "path",
			path: dString,
			color: color,
			size: Number(size),
			outline: {
				style: "none",
				width: 0
			}
		}
		if (isOutline)
		{
			symbol.outline = {
				style: "solid",
				color: outlineColor,
				width: Number(outlineWidth)
			}
			symbol.size = Number(size) + Number(outlineWidth)
		}
		return symbol;
	};

	Symbol.prototype.clusterPoint = function(size)
	{
		var symbol;
		size = size ? size : 16;
		symbol = new this.ArcGIS.SimpleMarkerSymbol().setSize(size).setColor(new this.ArcGIS.Color.fromHex('#000000')).setOutline(new this.ArcGIS.SimpleLineSymbol().setColor(new this.ArcGIS.Color.fromHex('#FFFFFF')).setWidth(2));
		return symbol;
	};

	Symbol.prototype.label = function(text, xoffset, yoffset)
	{
		const symbol = {
			type: "text",
			text: text ? text : "add text here.",
			color: "green",
			font: {
				family: "Calibri Light",
				size: 12
			},
			xoffset: xoffset ? xoffset : -60,
			yoffset: yoffset ? yoffset : 10,
		};

		return symbol;
	};

	Symbol.prototype.snapPoint = function()
	{
		var symbol = new this.ArcGIS.SimpleMarkerSymbol();
		symbol.setStyle(this.ArcGIS.SimpleMarkerSymbol.STYLE_SQUARE);
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setColor(new this.ArcGIS.Color([137, 200, 199])));
		symbol.setColor(new this.ArcGIS.Color([137, 200, 199, 0.4]));
		symbol.setSize(10);

		return symbol;
	};

	Symbol.prototype.stopBuffer = function(hexColor)
	{
		var symbol, rgb;
		hexColor = hexColor ? hexColor : this.default.color;
		rgb = this.ArcGIS.Color.fromHex(hexColor);

		symbol = new this.ArcGIS.SimpleFillSymbol();
		rgb.a = 0.5;
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setColor(new this.ArcGIS.Color(rgb)));

		rgb.a = 0.2;
		symbol.setColor(new this.ArcGIS.Color(rgb));

		return symbol;
	};

	Symbol.prototype.transparentStop = function()
	{
		return this.stop("#000000", null, 0);
	};

	Symbol.prototype.highlightStop = function()
	{
		return this.stop(this.selectionColor);
	};

	Symbol.prototype.drawingCursor = function()
	{
		var symbol = new this.ArcGIS.SimpleMarkerSymbol();
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setColor(new this.ArcGIS.Color([137, 200, 199])));
		symbol.setColor(new this.ArcGIS.Color([137, 200, 199, 0.5]));
		symbol.setSize(10);
		return symbol;
	};

	Symbol.prototype.editGhostStop = function()
	{
		var symbol = this.highlightStop();
		symbol.setOutline(new this.ArcGIS.SimpleLineSymbol().setStyle(this.ArcGIS.SimpleLineSymbol.STYLE_DASH));
		symbol.setColor(new this.ArcGIS.Color([0, 0, 0, 0]));
		return symbol;
	};

	Symbol.prototype.measurementLocation = function()
	{
		var self = this;
		return new self.ArcGIS.SimpleMarkerSymbol()
			.setColor([255, 255, 255, 1])
			.setOutline(new self.ArcGIS.SimpleLineSymbol().setWidth(1))
			.setSize(8);
	};

	Symbol.prototype.measurementLine = function()
	{
		return new this.ArcGIS.SimpleLineSymbol().setWidth(1).setColor([40, 128, 252, 1]);
	};

	Symbol.prototype.measurementLineVertex = function()
	{

	};

	Symbol.prototype.measurementPolygon = function()
	{

	};

	Symbol.prototype.drawPointSymbol = function()
	{
		var self = this;
		return {
			type: "simple-marker",
			color: self.symbolColors.bluePoint,
			size: 6,
			xoffset: 0,
			yoffset: 0,
			style: "circle",
		};
	};

	Symbol.prototype.editPointSymbol = function()
	{
		var self = this;
		return {
			type: "simple-marker",
			color: self.symbolColors.grayForEditing,
			size: 8,
			xoffset: 0,
			yoffset: 0,
			style: "circle",
		};
	};

	Symbol.prototype.highlightPointSymbol = function(size)
	{
		return {
			type: 'simple-marker',
			color: this.symbolColors.bluePoint,
			size: size ? size : 8,
			xoffset: 0,
			yoffset: 0,
			style: "circle",
			outline: {
				type: "simple-line",
				style: "solid",
				color: this.symbolColors.yellowForHighlight,
				width: 2
			}
		};
	};

	Symbol.prototype.drawPolylineSymbol = function()
	{
		var self = this;
		return {
			type: "simple-line",
			style: "solid",
			color: self.symbolColors.orangeForCreate,
			width: 2
		};
	};

	Symbol.prototype.editPolylineSymbol = function()
	{
		return {
			type: "simple-line",
			style: "solid",
			color: this.symbolColors.grayForEditing,
			width: 2
		};
	};

	Symbol.prototype.drawPolygonSymbol = function(color)
	{
		var self = this, color = color || self.symbolColors.orangeForCreate;
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: color.concat(0.5),
			outline: {
				type: "simple-line",
				style: "solid",
				color: color,
				width: 2
			}
		});
	};

	Symbol.prototype.editPolygonSymbol = function()
	{
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: this.symbolColors.grayForEditing.concat(0.5),
			outline: {
				type: "simple-line",
				style: "solid",
				color: this.symbolColors.grayForEditing,
				width: 2
			}
		});
	};

	Symbol.prototype.postalCodePolygonSymbol = function()
	{
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: [0, 0, 0, 0],
			outline: {
				type: "simple-line",
				style: "solid",
				color: [0, 0, 0, 1],
				width: 2
			}
		});
	};

	Symbol.prototype.waterPolygonSymbol = function()
	{
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: this.symbolColors.blueForSelect.concat(0.5),
			outline: {
				type: "simple-line",
				style: "solid",
				color: this.symbolColors.blueForSelect,
				width: 2
			}
		});
	};

	Symbol.prototype.waterPolylineSymbol = function()
	{
		return {
			type: "simple-line",
			style: "solid",
			color: this.symbolColors.blueForSelect,
			width: 2
		};
	};

	Symbol.prototype.landmarkPolygonSymbol = function()
	{
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: this.symbolColors.orangeForCreate.concat(0.5),
			outline: {
				type: "simple-line",
				style: "solid",
				color: this.symbolColors.orangeForCreate,
				width: 2
			}
		});
	};

	Symbol.prototype.landmarkPolylineSymbol = function()
	{
		return {
			type: "simple-line",
			style: "solid",
			color: this.symbolColors.orangeForCreate,
			width: 2
		};
	};

	Symbol.prototype.landmarkPointSymbol = function()
	{
		return {
			type: 'simple-marker',
			color: this.symbolColors.orangeForCreate,
			size: 8,
			xoffset: 0,
			yoffset: 0,
			style: "circle",
			// outline: {
			// 	type: "simple-line",
			// 	style: "solid",
			// 	color: this.symbolColors.orangeForCreate,
			// 	width: 2
			// }
		};
	};

	Symbol.prototype.railroadPolylineSymbol = function()
	{
		return {
			type: "simple-line",
			style: "solid",
			color: this.symbolColors.grayForEditing.concat(0.5),
			width: 2
		};
	};

	Symbol.prototype.polygonSymbol = function(color, outlineColor)
	{
		color = TF.Helper.MapHelper.getColorArray(color);
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: color.concat(0.5),
			outline: {
				type: "simple-line",
				style: "solid",
				color: outlineColor || color,
				width: 2
			}
		});
	};

	Symbol.prototype.polygonSymbolWithOpacity = function(color, outlineColor)
	{
		return new tf.map.ArcGIS.SimpleFillSymbol({
			style: "solid",
			color: color,
			outline: {
				type: "simple-line",
				style: "solid",
				color: outlineColor || color,
				width: 2
			}
		});
	}

	Symbol.prototype._setOpacity = function(color, opacity)
	{
		return color.concat([opacity / 255]);
	};

	Symbol.prototype.setHexColorOpacity = function(hexColor, opacity)
	{
		var currentColor = hexColor ? tf.map.ArcGIS.Color.fromHex(hexColor) : tf.map.ArcGIS.Color.fromHex('#FF0000'),
			currentOpacity = opacity ? opacity : 0;
		currentColor.a = currentOpacity;
		return currentColor;
	};

	Symbol.prototype.arrow = function(color)
	{
		color = color || "#000000";
		return {
			type: "simple-marker",
			size: 17,
			path: "M0 0 L14 12 L0 24 L0 22 L 12 12 L 0 2 Z",
			angle: 0,
			color: color,
			outline: {
				width: 1,
				color: color,
			}
		};
	};

	Symbol.prototype.arrowToPoint = function(color)
	{
		color = color || "#000000";
		return {
			type: "simple-marker",
			size: 24,
			color: color,
			outline: {
				color: [255, 255, 255],
				width: 2
			},
			angle: -45,
			yoffset: -15,
			xoffset: -15,
			path: "M16 2 L16 10 L2 10 L2 22 L16 22 L16 30 L30 16 Z"
		};
	};

	Symbol.prototype.arrowOnSide = function(color)
	{
		return {
			type: "simple-marker",
			size: 20,
			path: "M45 2 L45 13 L2 13 L2 19 L45 19 L45 30 L75 16 Z",
			color: color || "#000000",
			angle: 0,
			outline: null
		};
	};

	Symbol.prototype.railroadCrossingSymbol = function()
	{
		const imageData = 'data:image/png;base64,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'
		return this.svgSymbol(imageData, 20, { x: 10, y: 10 });
	}

	Symbol.prototype.heatmapColorStops = function()
	{
		return [
			{ color: "rgba(63, 40, 102, 0)", ratio: 0 },
			{ color: "#472b77", ratio: 0.1 },
			{ color: "#4e2d87", ratio: 0.2 },
			{ color: "#563098", ratio: 0.3 },
			{ color: "#5d32a8", ratio: 0.4 },
			{ color: "#6735be", ratio: 0.5 },
			{ color: "#7b3ce9", ratio: 0.6 },
			{ color: "#853fff", ratio: 0.7 },
			{ color: "#a46fbf", ratio: 0.8 },
			{ color: "#c29f80", ratio: 0.9 },
			{ color: "#ffff00", ratio: 1 }
		];
	}

	Symbol.prototype.getSVGMarkSymbol = function(setting)
	{
		let symbolNumber = setting.symbol, pathString = this.getOriginSVGSymbolString(symbolNumber);
		if (symbolNumber === "-1")
		{
			return this.pathSymbol(pathString, "transparent", "0", false);
		}
		else
		{
			return this.pathSymbol(pathString, setting.color, setting.size, setting.borderishow, setting.bordercolor, setting.bordersize);
		}
	};

	Symbol.prototype.getOriginSVGSymbolString = function(symbolnumber)
	{
		var pathString = "", i = thematicSymbolPath.length - 1;
		if (symbolnumber === "-1")
		{
			pathString = thematicSymbolPath[0].pathString;
		}
		else
		{
			for (; i >= 0; i--)
			{
				if (thematicSymbolPath[i].id === Number(symbolnumber))
				{
					pathString = thematicSymbolPath[i].pathString;
				}
			}

		}
		return pathString;
	};

	Symbol.prototype.getAddressPointSymbol = function()
	{
		const setting = this.getAddressPointSymbolSetting();
		if (setting)
		{
			return this.getSVGMarkSymbol(setting);
		}
		return this.drawPointSymbol();
	}

	Symbol.prototype.getAddressPointSymbolSetting = function()
	{
		var addressPointSymbolStorage = tf.storageManager.get("displaySetting.Parcels & Address Points");
		if (addressPointSymbolStorage)
		{
			return JSON.parse(addressPointSymbolStorage.selectedSymbolDisplay);
		}
		return null;
	}

	Symbol.prototype.dispose = function()
	{
		tfdispose(this);
	};
})();