(function()
{
	createNamespace("TF.Grid").KendoGridFilterMenu = KendoGridFilterMenu;
	const FILTER_ID_OF_OPEN_IN_NEW_GRID = -9000
	const DATE_PARAMETERS = {
		DateRangeOverlapWith: 'Between',
		LastMonth: 'Last Month',
		LastWeek: 'Last Week',
		LastXDays: 'Last X Days',
		LastYear: 'Last Year',
		NextBusinessDay: 'Next Business Day',
		NextMonth: 'Next Month',
		NextWeek: 'Next Week',
		NextXDays: 'Next X Days',
		NextYear: 'Next Year',
		OnX: 'On',
		OnOrAfterX: 'On or After',
		OnOrBeforeX: 'On or Before',
		ThisMonth: 'This Month',
		ThisWeek: 'This Week',
		ThisYear: 'This Year',
		Today: 'Today',
		Tomorrow: 'Tomorrow',
		Yesterday: 'Yesterday',
	}

	function convertToOldGridDefinition(gridDefinition)
	{
		return gridDefinition.Columns.map(function(definition)
		{
			return TF.Grid.GridHelper.convertToOldGridDefinition(definition);
		});
	}
	function KendoGridFilterMenu()
	{
		this.inited = false;
		this._storageFilterDataKey = `grid.currentfilter.${this._gridType}.id`;
		this._storageGeoRegionIdsKey = `grid.currentGeoRegionIds.${this._gridType}.id`;
		this._storageStopPoolIdsKey = `grid.currentStopPoolIds.${this._gridType}.id`;
		this._storageSchoolBoundariesKey = `grid.currentSchoolBoundaries.${this._gridType}.id`;
		this._storageDisplayQuickFilterBarKey = `grid.displayQuickFilterBar.${this._gridType}`;
		this._storageParameterFilterKey = `grid.currentParameterDateRange.${this._gridType}`;
		this.obHeaderFilters = ko.observableArray([]);
		this.obGridFilterDataModels = ko.observableArray();
		this.obFieldTripStageFilters = ko.observableArray();
		this.selectedFieldTripStageFilters = ko.observableArray();

		this.obGridFilterDataModelsFromDataBase = ko.computed(function()
		{
			var filterDataModels = Enumerable.From(this.obGridFilterDataModels()).Where(function(c)
			{
				return c.id() > 0 && !c.isSystem();
			}).ToArray();

			if (TF.isPhoneDevice && this.obSelectedGridFilterId && this.obSelectedGridFilterId())
			{
				var tmpFilterDataModels = [];
				filterDataModels.forEach(function(filterDataModel, idx)
				{
					if (filterDataModel.id() === this.obSelectedGridFilterId())
						tmpFilterDataModels.splice(0, 0, filterDataModels[idx]);
					else
						tmpFilterDataModels.push(filterDataModels[idx]);
				}, this);
				filterDataModels = tmpFilterDataModels;
			}

			return filterDataModels;
		}, this);

		const customizedSortOfIncompleteSchedule = new Map();
		customizedSortOfIncompleteSchedule.set("incomplete schedules (all)", "incomplete schedules1");
		customizedSortOfIncompleteSchedule.set("incomplete schedules (past only)", "incomplete schedules2");
		customizedSortOfIncompleteSchedule.set("incomplete schedules (past or today)", "incomplete schedules3");
		customizedSortOfIncompleteSchedule.set("incomplete schedules (today only)", "incomplete schedules4");
		customizedSortOfIncompleteSchedule.set("incomplete schedules (today or future)", "incomplete schedules5");
		customizedSortOfIncompleteSchedule.set("incomplete schedules (future only)", "incomplete schedules6");
		this.obGridFilterDataModelsFormDashBoard = ko.computed(function()
		{
			let filterArray = this.obGridFilterDataModels().filter(c => c.id() < 0 && c.type() !== "relatedFilter" || c.isSystem());
			const renameIfIncompleteScheduleFilter = filterName => filterName.startsWith("incomplete schedules") ? customizedSortOfIncompleteSchedule.get(filterName) : filterName;
			return filterArray.sort((a, b) => renameIfIncompleteScheduleFilter(a.name().toLowerCase()) > renameIfIncompleteScheduleFilter(b.name().toLowerCase()) ? 1 : -1);
		}, this);

		this.obGridFilterDataModelsFromRelatedFilter = ko.computed(function()
		{
			return Enumerable.From(this.obGridFilterDataModels()).Where(function(c)
			{
				return c.type() === "relatedFilter";
			}).ToArray();
		}, this);

		this.obSelectedGridFilterId = ko.observable();
		if (this._gridState && typeof this._gridState.gridFilterId == "number")
		{
			this.obSelectedGridFilterId(this._gridState.gridFilterId);
		}
		this.subscriptions.push(this.obSelectedGridFilterId.subscribe(this._selectedGridFilterIdChange, this));
		this.saveFilterClick = this.saveFilterClick.bind(this);
		this.manageFilterClick = this.manageFilterClick.bind(this);
		this.createNewFilterClick = this.createNewFilterClick.bind(this);
		this.saveAsNewFilterClick = this.saveAsNewFilterClick.bind(this);
		this.clearGridFilterClick = this.clearGridFilterClick.bind(this);
		this.clearGridFilterParameterClick = this.clearGridFilterParameter.bind(this);
		this.onClearGridFilterClickEvent = new TF.Events.Event();
		this.onFilterChanged = new TF.Events.Event();
		this.onRefreshPanelEvent = new TF.Events.Event();
		this.gridFilterClick = this.gridFilterClick.bind(this);
		this.quickFilterBarClick = this.quickFilterBarClick.bind(this);
		this.gridFilterParameterClick = this.gridFilterParameterClick.bind(this);
		this.obCallOutFilterName = ko.observable(this.options.callOutFilterName);
		this.obSelectedNonEligibleZone = ko.observable();
		this.obSelectedGeoRegionIds = ko.observableArray([]);
		this.obSelectedGeoRegionReminderId = ko.observable();
		this.obSelectedStopPoolIds = ko.observableArray([]);
		this.obSelectedBoundarySchoolCodes = ko.observableArray([]);
		this.obSelectedGridFilterDataModel = ko.computed(this._selectedGridFilterDataModelComputer, this);
		this.subscriptions.push(this.obSelectedGridFilterDataModel.subscribe(this._omitRecordsChange, this));
		this.obSelectedGridFilterClause = ko.computed(this._selectedGridFilterWhereClauseComputer, this);
		this.obSelectedGridFilterType = ko.computed(this._selectedGridFilterTypeComputer, this);
		this.obSelectedGridFilterName = ko.computed(this._selectedGridFilterNameComputer, this);
		this.dateParameters = this.options.gridType === 'trip' && tf.authManager.hasTripDates() ? Object.keys(DATE_PARAMETERS).map(key => { return { key: key, name: DATE_PARAMETERS[key] } }) : [];
		this.obSelectedGridFilterParameter = ko.observable(null);
		this.obSelectedGridFilterParameterText = ko.computed(this._selectedGridFilterParameterComputer, this);
		this.obSelectedGridFilterParameterFilterSet = ko.observable(null);


		this.obSelectedStaticFilterName = ko.computed(function()
		{
			if (this.obSelectedGridFilterDataModel())
			{
				return this.obSelectedGridFilterDataModel().isStatic() ? this.obSelectedGridFilterDataModel().whereClause() : "";
			}
			return null;
		}, this);
		this.obIsSystemFilter = ko.computed(function()
		{
			if (this.obSelectedGridFilterDataModel())
			{
				return this.obSelectedGridFilterDataModel().isSystem() ? true : false;
			}
			return false;
		}, this);

		this.subscriptions.push(this.obHeaderFilters.subscribe(this._currentFilterChange, this));
		this.subscriptions.push(this.obHeaderFilterSets.subscribe(this._currentFilterChange, this));

		this.obIsQuickFilterApplied = ko.computed(function()
		{
			var quickFilters = this.obHeaderFilters(),
				quickFilterSets = this.obHeaderFilterSets(),
				isQuickFilterApplied = ((quickFilters && quickFilters.length) || (quickFilterSets && quickFilterSets.length));

			$('.grid-filter-clear-all').toggleClass('quick-filter-applied', isQuickFilterApplied);

		}, this);

		// Check if filter has been modified.
		this.obSelectedGridFilterModified = ko.computed(this._selectedGridFilterModifiedComputer, this);

		this.obSelectedGridFilterModifiedMessage = ko.computed(() =>
		{
			return this.obSelectedGridFilterModified() ? "(modified)" : ""
		}, this);

		this.obQuickFilterBarCheckIcon = ko.observable("menu-item-checked");

		if (!this.options.withoutData)
		{
			this.initGeoRegionFilter();
			this.initNonEligibleZone();
			this.initStopPoolFilter();
			this._initSchoolBoundariesFilter();
			this.initReminder();
		}
	}

	KendoGridFilterMenu.prototype._selectedGridFilterParameterComputer = function()
	{
		let days = (value) => parseInt(value) === 1 ? 'Day' : 'Days';
		switch (this.obSelectedGridFilterParameter()?.key)
		{
			case 'DateRangeOverlapWith':
				const value = this.obSelectedGridFilterParameter().value.split('|'),
					start = moment(value[0]).format('MM/DD/YYYY'),
					end = moment(value[1]).format('MM/DD/YYYY');
				return `${this.obSelectedGridFilterParameter().name} ${start} - ${end}`;
			case 'LastXDays':
				return `Last ${this.obSelectedGridFilterParameter().value} ${days(this.obSelectedGridFilterParameter().value)}`;
			case 'NextXDays':
				return `Next ${this.obSelectedGridFilterParameter().value} ${days(this.obSelectedGridFilterParameter().value)}`;
			case 'OnX':
			case 'OnOrAfterX':
			case 'OnOrBeforeX':
				return `${this.obSelectedGridFilterParameter().name} ${moment(this.obSelectedGridFilterParameter().value).format('MM/DD/YYYY')}`
			default:
				return this.obSelectedGridFilterParameter()?.name || "None";
		}
	}

	KendoGridFilterMenu.prototype.initFieldTripStageFilters = function()
	{
		var stageIds = [];
		if (tf.authManager.authorizationInfo.isAdmin || tf.authManager.isAuthorizedFor("transportationAdministrator", "read"))
		{
			stageIds = [101, 100, 99, 98, 7, 6, 5, 4, 3, 2, 1];
		}
		else if (tf.authManager.isAuthorizedFor("level4Administrator", "read"))
		{
			stageIds = [7, 6, 5, 4];
		}
		else if (tf.authManager.isAuthorizedFor("level3Administrator", "read"))
		{
			stageIds = [5, 4, 3, 2];
		}
		else if (tf.authManager.isAuthorizedFor("level2Administrator", "read"))
		{
			stageIds = [3, 2, 1];
		}
		else if (tf.authManager.isAuthorizedFor("level1Administrator", "read"))
		{
			stageIds = [1];
		}
		this.selectedFieldTripStageFilters($.extend([], stageIds));
		this.obFieldTripStageFilters(stageIds);
	};

	KendoGridFilterMenu.prototype._omitRecordsChange = function()
	{
		var omittedRecords = [];
		if (this.obSelectedGridFilterDataModel() && this.obSelectedGridFilterDataModel().omittedRecords())
		{
			for (var i = 0; i < this.obSelectedGridFilterDataModel().omittedRecords().length; i++)
			{
				omittedRecords.push(this.obSelectedGridFilterDataModel().omittedRecords()[i].OmittedRecordID);
			}
			this._gridState.filteredExcludeAnyIds = omittedRecords;
			this.obFilteredExcludeAnyIds(this._gridState.filteredExcludeAnyIds);
		}
	};

	KendoGridFilterMenu.prototype.getQuickFilterRawData = function()
	{
		var currentHeaderFilters = this.findCurrentHeaderFilters();
		var includeIds = this._gridState.isNotSaveIdToQuickFilter ? null : this._gridState.filteredIds;
		var callOutFilterName = this.obCallOutFilterName();
		if (!currentHeaderFilters && includeIds && !callOutFilterName)
		{
			return null;
		}
		return new TF.SearchParameters(null, null, null, this.findCurrentHeaderFilters(), null, includeIds, this.findCurrentOmittedRecords(), this.obCallOutFilterName());
	};

	KendoGridFilterMenu.prototype.saveQuickFilter = function(quickFilters)
	{
		var self = this;
		//IF the request from search, do not sticky quick filter.
		if (self.options.fromSearch || self.options.isTemporaryFilter || self.options.customGridType === "dashboardwidget")
		{
			return Promise.resolve();
		}
		var gridType = self.options.gridType;
		if (self.options.kendoGridOption && self.options.kendoGridOption.entityType)
		{
			gridType = self.options.kendoGridOption.entityType + "." + gridType;
		}
		if (gridType === "form")
		{
			gridType += self.options.gridData.value; //add udgridid
		}
		return TF.Grid.FilterHelper.saveQuickFilter(gridType, quickFilters);
	};

	KendoGridFilterMenu.prototype.clearQuickFilter = function()
	{
		var self = this;
		//IF the request from search, do not sticky quick filter.
		if (self.options.fromSearch || self.options.isTemporaryFilter)
		{
			return Promise.resolve();
		}
		var gridType = self.options.gridType;
		if (self.options.kendoGridOption && self.options.kendoGridOption.entityType)
		{
			gridType = self.options.kendoGridOption.entityType + "." + gridType;
		}
		if (gridType === "form")
		{
			gridType += self.options.gridData.value; //add udgridid
		}
		return TF.Grid.FilterHelper.clearQuickFilter(gridType);
	};

	KendoGridFilterMenu.prototype.getQuickFilter = function()
	{
		var self = this, gridType = self.options.gridType;
		if (self.options.kendoGridOption && self.options.kendoGridOption.entityType)
		{
			gridType = self.options.kendoGridOption.entityType + "." + gridType;
		}
		if (gridType === "form" && self.options.gridData && self.options.gridData.value)
		{
			gridType += self.options.gridData.value; //add udgridid
		}
		//IF the request from search or from a Dashboard Widget Grid, do not use the sticky quick filter.
		if (self.options.fromSearch || self.options.isTemporaryFilter
			|| (self.options.customGridType && self.options.customGridType.toLowerCase() === "dashboardwidget"))
		{
			return new TF.SearchParameters(null, null, null, null, null, null, null);
		}
		//If the request from mini grid. use sticky quick filter from options.
		if (self.options.isMiniGrid)
		{
			let filterSet = self.options.defaultFilter ? self.convertKendo2RequestFilterSet({}, self.options.defaultFilter) : null;
			return new TF.SearchParameters(null, null, null, filterSet, null, null, null);
		}

		return tf.storageManager.get(tf.storageManager.gridCurrentQuickFilter(gridType)) || this.getDefaultFilter();
	};

	KendoGridFilterMenu.prototype.getDefaultFilter = function()
	{
		if (this.options.gridType == "session" && !this.options.predefinedGridData?.isFromDashboard)
		{
			return new TF.SearchParameters(null, null, null, {
				LogicalOperator: "And",
				FilterItems: [
					{
						"FieldName": "LoginTime",
						"Operator": "LastXDays",
						"Value": "7",
						"TypeHint": "DateTime",
						"ConvertedToUTC": true
					},
					{
						"FieldName": "LogoutTime",
						"Operator": "IsNull",
						"Value": "Invalid date",
						"TypeHint": "DateTime",
						"ConvertedToUTC": true
					}
				],
			}, null, null, null);
		}

		return new TF.SearchParameters(null, null, null, null, null, null, null);
	}

	KendoGridFilterMenu.prototype.quickFilterBarClick = function()
	{
		var displayQuickFilterBar = false;
		if (this._quickFilterBarIsEnabled.bind(this)())
		{
			displayQuickFilterBar = !this._quickFilterBarDisplayed.bind(this)();
			this._setQuickFilterBarStatus.bind(this)(displayQuickFilterBar);
		}
		tf.storageManager.save(this._storageDisplayQuickFilterBarKey, displayQuickFilterBar);
	};

	KendoGridFilterMenu.prototype.initQuickFilterBar = function()
	{
		var self = this;
		if (self._quickFilterBarIsEnabled())
		{
			if (self.options.displayQuickFilterBar != null)
			{
				self._setQuickFilterBarStatus(self.options.displayQuickFilterBar);
				return;
			}
			var display = self._getStroageDisplayQuickFilterBarSetting();
			self._setQuickFilterBarStatus(display);
		}
	};

	KendoGridFilterMenu.prototype._getStroageDisplayQuickFilterBarSetting = function()
	{
		var display = tf.storageManager.get(this._storageDisplayQuickFilterBarKey);
		if (display === undefined) display = true;
		display = String.convertToBoolean(display);
		return display;
	};

	KendoGridFilterMenu.prototype._quickFilterBarIsEnabled = function()
	{
		var filterRow = this.$container.find(".k-grid-header").find(".k-filter-row");
		return (filterRow !== undefined && filterRow.length > 0);
	};

	KendoGridFilterMenu.prototype._quickFilterBarDisplayed = function()
	{
		var filterRow = this.$container.find(".k-grid-header").find(".k-filter-row");
		return !(filterRow[0].style.display == "none");
	};

	KendoGridFilterMenu.prototype._setQuickFilterBarStatus = function(display)
	{
		var filterRow = this.$container.children(".k-grid-header").find(".k-filter-row");
		if (display)
		{
			filterRow.css("display", "");
			this._filterHeight = 0;
			this.obQuickFilterBarCheckIcon("menu-item-checked");
		}
		else
		{
			filterRow.css("display", "none");
			this._filterHeight = filterRow.height();
			this.obQuickFilterBarCheckIcon("");
		}
		this.fitContainer();
		this.lightKendoGridDetail && this.lightKendoGridDetail.changeQuickFilterBarStatus(display);
	};

	KendoGridFilterMenu.prototype._selectedGridFilterModifiedComputer = function()
	{
		this._setgridStateTwoRowWhenOverflow && this._setgridStateTwoRowWhenOverflow();

		const quickFilters = this.obHeaderFilters();
		const quickFilterSets = this.obHeaderFilterSets();
		const isValidFilter = Array.isArray(quickFilters) && quickFilters.length > 0
			&& quickFilters.some(f => f['Operator'] != 'In' || f['Value'] != "");

		if (isValidFilter
			|| (Array.isArray(quickFilterSets) && quickFilterSets.length > 0)
			|| this.obTempOmitExcludeAnyIds().length > 0)
		{
			const filterModel = this.obSelectedGridFilterDataModel();

			if (filterModel && !(filterModel.isSystem() || filterModel.isStatic()))
			{
				return true;
			}
		}

		return false;
	};

	KendoGridFilterMenu.prototype._selectedGridFilterDataModelComputer = function()
	{
		const selectedFilterId = this.obSelectedGridFilterId();
		return this.obGridFilterDataModels().find(o => o.id() === selectedFilterId);
	};

	KendoGridFilterMenu.prototype._selectedGridFilterTypeComputer = function()
	{
		const filterModel = this.obSelectedGridFilterDataModel();
		return filterModel && filterModel.isForQuickSearch();
	};

	KendoGridFilterMenu.prototype._selectedGridFilterWhereClauseComputer = function()
	{
		const filterModel = this.obSelectedGridFilterDataModel();
		return filterModel && filterModel.whereClause();
	};

	KendoGridFilterMenu.prototype._selectedGridFilterNameComputer = function()
	{
		//show hide comma when filter and layout toggle split to tow row on tablet
		this._setgridStateTwoRowWhenOverflow && this._setgridStateTwoRowWhenOverflow();

		if (this.isFromRelated && this.isFromRelated())
		{
			return this.options.fromMenu;
		}

		const filterModel = this.obSelectedGridFilterDataModel();
		if (filterModel)
		{
			return filterModel.isValid() ? filterModel.name() : "None";
		}
		else if (this.obCallOutFilterName())
		{
			return this.obCallOutFilterName();
		}
		else if (this.options.fromSearch) //IF the request from search, first go to view display "Search Results".
		{
			return "Search Results";
		}
		else if (this.options.isTemporaryFilter && this.options.filterName)
		{
			return this.options.filterName;
		}
		else if (this.obSelectedNonEligibleZone())
		{
			return this.obSelectedNonEligibleZone().School + "-" + this.obSelectedNonEligibleZone().Name;
		}
		else if (this.obSelectedGeoRegionIds() && this.obSelectedGeoRegionIds().length > 0)
		{
			return "Geo Region";
		}
		else if (this.obSelectedStopPoolIds() && this.obSelectedStopPoolIds().length > 0)
		{
			return "Stop Pool";
		}
		else if (this.obSelectedBoundarySchoolCodes() && this.obSelectedBoundarySchoolCodes().length > 0)
		{
			return "School Boundary";
		}
		return "None";
	};

	KendoGridFilterMenu.prototype.filterMenuClick = function(e, done)
	{
		tf.contextMenuManager.showMenu(e.target, new TF.ContextMenu.TemplateContextMenu("workspace/grid/filtercontextmenu", new TF.Grid.GridMenuViewModel(this, this.searchGrid), () =>
		{
			if (done)
			{
				done();
			}
			if (this.searchGrid?.filterMenuClosed)
			{
				this.searchGrid.filterMenuClosed();
			}
		}));
	};

	KendoGridFilterMenu.prototype.loadGridFilter = function(autoSetSummaryFilter)
	{
		const self = this;
		if (self.options.layoutAndFilterOperation === false || self.options.withoutData)
		{
			return Promise.resolve(true);
		}

		return Promise.resolve(function()
		{
			if (!self.initialFilter)
			{
				return self.clearQuickFilter();
			}

			return true;
		}).then(function()
		{
			return self._loadGridFilter(autoSetSummaryFilter);
		}).then(function()
		{
			return self._loadGeoRegionFilter();
		}).then(() =>
		{
			return self._loadParameterFilter();
		});
	};

	/**
	* use open from reminder or sticky geo region select info to load grid by goe region type
	* - GeoRegion
	* - School Boundary(Boundary Planning)
	* - Stop Pool
	*/
	KendoGridFilterMenu.prototype._loadGeoRegionFilter = function()
	{
		var self = this, geoRegionIds, nonEligibleZoneId, reminderId, stopPoolIds, schoolBoundaries;

		if ((!self.initialFilter && self.options.filterType !== 'NEZFilter') || self.options.filterId || (self.options.gridState && self.options.gridState.filteredIds))
		{
			return;
		}

		// open from reminder
		if (self.options.geoRegionIds)
		{
			geoRegionIds = self.options.geoRegionIds.split(",").map((id) =>
			{
				return parseInt(id, 10);
			});
			reminderId = self.options.reminderId;
		} else if (self.options.nonEligibleZoneId > 0)
		{
			nonEligibleZoneId = self.options.nonEligibleZoneId;
			reminderId = self.options.reminderId;
		} else if (self.options.gridType != "gpsevent")
		{
			// sticky
			var geoRegionIdsStorage = tf.storageManager.get(self._storageGeoRegionIdsKey);
			if (geoRegionIdsStorage && self.options.customGridType !== "dashboardwidget")
			{
				geoRegionIds = geoRegionIdsStorage.ids;
				reminderId = geoRegionIdsStorage.reminderId;
			}

			var nonEligibleZoneStorage = tf.storageManager.get(self._storageNonEligibleZoneIdKey);
			if (nonEligibleZoneStorage && self.options.customGridType !== "dashboardwidget")
			{
				nonEligibleZoneId = nonEligibleZoneStorage.id;
				reminderId = nonEligibleZoneStorage.reminderId;
			}

			var stopPoolStorage = tf.storageManager.get(self._storageStopPoolIdsKey);
			if (stopPoolStorage && self.options.customGridType !== "dashboardwidget")
			{
				stopPoolIds = stopPoolStorage.ids;
			}

			const schoolBoundaryStorage = tf.storageManager.get(self._storageSchoolBoundariesKey);
			if (schoolBoundaryStorage && self.options.customGridType !== "dashboardwidget")
			{
				schoolBoundaries = schoolBoundaryStorage.boundarySchools;
			}
		}

		if (geoRegionIds && geoRegionIds.length)
		{
			return self._setGeoRegionFilterIds({
				ids: geoRegionIds,
				reminderId: reminderId
			}, !!self.options.geoRegionIds);
		}

		if (nonEligibleZoneId > 0)
		{
			self.options.filterType = 'NEZFilter';
			return self._setNonEligibleZoneFilterIds({
				OBJECTID: nonEligibleZoneId,
				reminderId: reminderId
			}, self.options.nonEligibleZoneId > 0);
		}

		if (stopPoolIds && stopPoolIds.length)
		{
			return self._setStopPoolFilterIds({
				ids: stopPoolIds,
			}, !!self.options.stopPoolIds);
		}

		if (schoolBoundaries && schoolBoundaries.length)
		{
			return self._setSchoolBoundaryFilterIds({
				boundarySchools: schoolBoundaries,
			}, !!self.options.schoolBoundaries);
		}
	};

	KendoGridFilterMenu.prototype._loadParameterFilter = function()
	{
		if (this.options.gridType === 'trip' || this.options.customGridType === 'trip')
		{
			var parameterStorage = tf.storageManager.get(this._storageParameterFilterKey);
			const openFromNewGridNoParameterFilter = this._gridState.noParameterFilter || this.options.noParameterFilter || this.relatedFilterEntity?.isSharedLink;
			if (parameterStorage && !openFromNewGridNoParameterFilter)
			{
				return this.setCurrentParameterDateRange(parameterStorage);
			}
		}
	}

	KendoGridFilterMenu.prototype.syncFilterRelationships = function()
	{
		const gridFilters = this.obGridFilterDataModels();
		const dataTypeId = tf.dataTypeHelper.getId(this._gridType);
		if (!gridFilters || !gridFilters.length)
		{
			return Promise.resolve();
		}

		return Promise.all([this._loadReminders(dataTypeId), this._loadScheduledAutoExports(dataTypeId)]).then((res) =>
		{
			const allReminders = res[0];
			const scheduledAutoExports = res[1];

			gridFilters.forEach(gf =>
			{
				const autoExports = scheduledAutoExports.filter(a => a.FilterId === gf.id());
				const reminders = allReminders.filter(r => r.FilterID === gf.id() || (gf.id() < 0 && r.StaticFilterName == gf.name()));
				const isDirty = gf.apiIsDirty();
				gf.autoExportExists(autoExports.length > 0);
				gf.autoExports(autoExports);
				gf.reminders(_.unionWith((gf.reminders() || []).concat(reminders), _.isEqual));
				gf.apiIsDirty(isDirty);
			});
		});
	}

	KendoGridFilterMenu.prototype._findGridFilters = function(options)
	{
		const self = this, filterUrl = "gridfilters";

		let filter = options.queryOtherDataSourceFilters ?
			`noteq(dbid, ${tf.datasourceManager.databaseId})&isnotnull(dbid,)&eq(datatypeId,${tf.dataTypeHelper.getId(self.options.gridType)})` :
			`(eq(dbid, ${tf.datasourceManager.databaseId})|isnull(dbid,))&eq(datatypeId,${tf.dataTypeHelper.getId(self.options.gridType)})`;

		if (self._gridType === 'form' && self.options && self.options.gridData && self.options.gridData.value)
		{
			filter = `${filter}&eq(udgridId,${self.options.gridData.value})`;
		}

		const paramData = { "@filter": filter };
		if (options.relationships)
		{
			paramData["@relationships"] = options.relationships;
		}
		if (options.fields)
		{
			paramData["@fields"] = options.fields;
		}
		if (options.filterId)
		{
			paramData["id"] = options.filterId;
		}

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), filterUrl), {
			paramData: paramData
		}, {
			overlay: self.options.customGridType !== "dashboardwidget"
		}).then(apiResponse => apiResponse.Items, () => []);
	}

	KendoGridFilterMenu.prototype._loadScheduledAutoExports = function(dataTypeId)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "scheduledautoexports"), {
			paramData: { "@filter": `eq(DataTypeId,${dataTypeId})` }
		}).then(apiResponse => apiResponse.Items.map(c => ({
			Name: c.Name,
			Id: c.ID,
			FilterId: JSON.parse(c.ExecutionInfo)?.GridLayoutAndSelectedId?.gridLayoutExtendedEntity.FilterId
		})), () => []);
	}

	KendoGridFilterMenu.prototype._loadReminders = function(dataTypeId)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "reminders"), {
			paramData: { "@filter": `eq(DataTypeId,${dataTypeId})` }
		}).then(apiResponse => apiResponse.Items, () => []);
	}

	/**
	 * Load grid filter
	 *
	 * @param {*} autoSetSummaryFilter
	 * @return {*}
	 */
	KendoGridFilterMenu.prototype._loadGridFilter = function(autoSetSummaryFilter)
	{
		var self = this;
		var gridfiltersPromise = self._findGridFilters({ "relationships": "OmittedRecord,Reminder,AutoExport" });

		let dataTypeId = tf.dataTypeHelper.getId(self._gridType);
		var staticfiltersPromise = Number.isInteger(dataTypeId) && !TF.Helper.DataTypeHelper.ExcludeStaticFilter.includes(self._gridType)
			? tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "staticfilters"),
				{
					paramData: {
						dataTypeId: dataTypeId
					}
				},
				{
					overlay: self.options.customGridType !== "dashboardwidget"
				}).then(function(apiResponse)
				{
					var staticFilters = apiResponse.Items;
					return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), 'reminders'), {
						paramData: {
							"@filter": String.format("(in(StaticFilterName,{0}))", staticFilters.join(','))
						}
					}, {
						overlay: self.options.customGridType !== "dashboardwidget"
					}).then(apiResponse =>
					{
						const reminders = apiResponse.Items;
						return staticFilters.map((item, index) =>
						{
							return {
								Id: -(index + 1),
								IsValid: true,
								DataTypeID: dataTypeId,
								Name: item,
								WhereClause: item,
								Reminders: reminders.filter(r => r.StaticFilterName === item),
								IsStatic: true
							}
						})
					});
				})
			: Promise.resolve([]);

		return Promise.all([gridfiltersPromise, staticfiltersPromise])
			.then(Items =>
			{
				const filterItems = Items.flat();
				const currentLayout = self._obCurrentGridLayoutExtendedDataModel();
				const layoutFilterId = currentLayout && currentLayout.filterId();

				if (dataTypeId == 6)
				{
					(filterItems || []).forEach(filter =>
					{
						if (filter.WhereClause && filter.IsValid)
						{
							const { isValid, whereClause } = self.checkGPSEventFilterIsValid(filter.WhereClause);
							filter.IsValid = isValid;
							isValid && (filter.WhereClause = whereClause);
						}
					});
				}

				var gridFilterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, filterItems);

				gridFilterDataModels.forEach(function(gridFilter)
				{
					if (gridFilter.reminders().length > 0)
					{
						gridFilter.reminderId(gridFilter.reminders()[0].Id);
						gridFilter.reminderUserId(gridFilter.reminders()[0].UserID);
					}
				});

				if (self.options.staticFilterName)
				{
					let staticFilters = Items[1].filter(i => i.Name === self.options.staticFilterName);
					if (staticFilters.length > 0)
					{
						self.options.filterId = staticFilters[0].Id;
					}
				}

				//IF the request from search, do not use the sticky filter.
				if (self.options.fromSearch || self.options.isTemporaryFilter)
				{
					self.obGridFilterDataModels(gridFilterDataModels);
					return Promise.resolve();
				}

				var selectGridFilterEntityId;
				if (!self.inited)
				{
					if (self.options && self.options.changeStorageKey)
					{
						self._storageFilterDataKey = self.options.changeStorageKey(self._storageFilterDataKey);
					}

					if ($.isNumeric(self.options.filterId) && self.options.filterId !== 0)
					{
						selectGridFilterEntityId = self.options.filterId;
					}
					else if (tf.storageManager.get(self._storageFilterDataKey, true))
					{
						//open new grid in viewfinder is use local storage
						selectGridFilterEntityId = tf.storageManager.get(self._storageFilterDataKey, true);
						if (!TF.isPhoneDevice)
						{
							tf.storageManager.save(self._storageFilterDataKey, selectGridFilterEntityId);
						}

						tf.storageManager.delete(self._storageFilterDataKey, true);
					}
					else if (self.options.gridLayout)
					{
						// dashboard grid.
						selectGridFilterEntityId = self.options.gridLayout.FilterId;
					}
					else if (self.options.predefinedGridData && self.options.predefinedGridData.gridType == self._gridType)
					{
						selectGridFilterEntityId = self.options.predefinedGridData;
					}
					else if (tf.isViewfinder)
					{
						if (tf.userPreferenceManager.getUserSetting("shouldRetainGridFilter"))
						{
							selectGridFilterEntityId = tf.storageManager.get(self._storageFilterDataKey) || layoutFilterId;
						}
						else
						{
							var temporaryFilterIdKey = "grid.temporaryfilter." + self._gridType + ".id";
							selectGridFilterEntityId = parseInt(tf.storageManager.get(temporaryFilterIdKey, true, true)) || null;
							tf.storageManager.delete(temporaryFilterIdKey, true, true);
						}
					}
					else if (!self.options.showAllRecords)
					{
						if (self._gridState.isNotSaveIdToQuickFilter)
						{
							selectGridFilterEntityId = {
								filteredIds: self._gridState.filteredIds,
								filterName: self._gridState.filterName
							}
						}
						else
						{
							selectGridFilterEntityId = tf.storageManager.get(self._storageFilterDataKey) || layoutFilterId;
						}
					}

					if (selectGridFilterEntityId && selectGridFilterEntityId.filteredIds)
					{
						self.relatedFilterEntity = selectGridFilterEntityId;
					}
					else
					{
						self.relatedFilterEntity = undefined;
					}

					if (getQueryString("filterId"))
					{
						selectGridFilterEntityId = parseInt(getQueryString("filterId"));
					}
				}

				// for the specific filters in the summary page of the viewfinderweb
				if (self.options.summaryFilters && self.options.summaryFilters.length > 0)
				{
					var summaryGridFilterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, self.options.summaryFilters);
					gridFilterDataModels = gridFilterDataModels.concat(summaryGridFilterDataModels);
					self.obGridFilterDataModels(gridFilterDataModels);
					self._sortGridFilterDataModels();
					if (!self.inited)
					{
						if (!selectGridFilterEntityId && self.options.defaultFilter)
						{
							selectGridFilterEntityId = self.options.defaultFilter;
						}

						if (self.options.summaryFilterFunction && selectGridFilterEntityId && selectGridFilterEntityId < 0 && selectGridFilterEntityId != FILTER_ID_OF_OPEN_IN_NEW_GRID && autoSetSummaryFilter !== false)
						{
							self.obSelectedGridFilterId(selectGridFilterEntityId);
							return self.options.summaryFilterFunction(selectGridFilterEntityId)
								.then(function(filteredIds)
								{
									if ($.isArray(filteredIds))
									{
										self._gridState.filteredIds = self.mergeFilterIds(filteredIds);
									}
									else if (typeof (filteredIds) === "string")
									{
										self._gridState.filterClause = filteredIds;
									}
								});
						}
					}
				}
				else
				{
					if (!self.obGridFilterDataModels)
					{
						self.obGridFilterDataModels = ko.observableArray();
					}
					self.obGridFilterDataModels(gridFilterDataModels);
				}

				if (self.relatedFilterEntity && self.relatedFilterEntity.filteredIds)
				{
					//used to get new grid filter both route finder and view finder
					if (self._gridState)
					{
						self._gridState.filteredIds = self.relatedFilterEntity.filteredIds;
					}
					self.options.fromMenu = self.relatedFilterEntity.filterName;

					self.isFromRelated(true);

					if (!self.relatedFilterEntity.isSharedLink)
					{
						var relatedFilter = [{
							Name: self.relatedFilterEntity.filterName,
							Id: FILTER_ID_OF_OPEN_IN_NEW_GRID,
							Type: 'relatedFilter',
							IsValid: true
						}];
						var relatedFilterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, relatedFilter);
						self.obGridFilterDataModels(gridFilterDataModels.concat(relatedFilterDataModels));
					}

					if (!self.inited)
					{
						if (!self.obSelectedGridFilterId())
						{
							self.obSelectedGridFilterId(relatedFilter && relatedFilter[0].Id);
						} else
						{
							self.isFromRelated(false);
						}
					}
				}

				if (selectGridFilterEntityId && typeof selectGridFilterEntityId == "number")
				{
					self.obSelectedGridFilterId(selectGridFilterEntityId);
				}

				self.inited = true;

				if (currentLayout)
				{
					return Promise.resolve();
				}

				return self.syncFilter();
			});
	};

	KendoGridFilterMenu.prototype.syncFilter = function(filterId)
	{
		// refresh UserPreference Cache before apply filter.
		var self = this;
		return tf.userPreferenceManager.getAllKey().then(function()
		{
			var filterId = filterId || tf.storageManager.get(self._storageFilterDataKey) || self.obSelectedGridFilterId();
			var message = 'The Filter that was applied has been deleted. The system default Filter will be applied to this grid.';
			return self._syncFilterAndNotifyStatusUpdated(filterId, message);
		});
	};

	KendoGridFilterMenu.prototype.dataTypesWithDB = function(gridType)
	{
		const dataTypeConfig = tf.dataTypeHelper.getDataTypeConfig(gridType);
		return dataTypeConfig && dataTypeConfig.hasDBID;
	}

	KendoGridFilterMenu.prototype._getCurrentUrl = function()
	{
		let self = this;
		if (this.dataTypesWithDB(this._gridType))
		{
			let searchPath = TF.Helper.ApiUrlHelper.postRawfilterclauseVerifyUrl(self);
			if (this._gridType === 'form')
			{
				const formId = this.options.gridData.value;
				searchPath = `${searchPath}&UDGridID=${formId}`;
			}

			return pathCombine(tf.api.apiPrefix(), "search", searchPath);
		}

		return pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", TF.Helper.ApiUrlHelper.postRawfilterclauseVerifyUrl(self) + "&databaseId=0");
	}

	KendoGridFilterMenu.prototype._syncFilterAndNotifyStatusUpdated = function(filterId, message)
	{
		var self = this;

		let url = self._getCurrentUrl();
		return TF.Grid.FilterHelper.validFilterId(filterId, url)
			.then(function(result)
			{
				if (result === true)
				{
					return true;
				}
				else if (result === false)
				{
					message = message || 'This Filter has been deleted. It cannot be applied.';
					return tf.promiseBootbox.alert(message, 'Warning', 40000)
						.then(function()
						{
							return Promise.all(
								[self._deleteForeignKey.bind(self)(filterId),
								self._deleteStickFilter.bind(self)(filterId),
								self._clearFilterListCache.bind(self)(filterId),
								self._clearObSelectedGridFilterId.bind(self)(filterId)]
							).then(() => false);
						});
				}
				else
				{
					if (result.Changed)
					{
						return result;
					}

					return true;
				}
			});
	};

	KendoGridFilterMenu.prototype._deleteForeignKey = function(filterId)
	{
		return tf.promiseAjax.delete(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters", filterId));
	};

	KendoGridFilterMenu.prototype._deleteStickFilter = function(filterId)
	{
		var self = this;
		//IF the request from search, do not sticky filter.
		if (self.options.fromSearch || self.options.isTemporaryFilter)
		{
			return Promise.resolve(true);
		}
		var currentStickFilterId = tf.storageManager.get(self._storageFilterDataKey);
		if (currentStickFilterId === filterId)
		{
			return tf.storageManager.delete(self._storageFilterDataKey);
		}
		else
		{
			return Promise.resolve(true);
		}
	};

	KendoGridFilterMenu.prototype._clearFilterListCache = function(filterId)
	{
		var self = this;
		for (var i = 0; i < self.obGridFilterDataModels().length; i++)
		{
			if (self.obGridFilterDataModels()[i].id() === filterId)
			{
				self.obGridFilterDataModels.remove(self.obGridFilterDataModels()[i]);
				i--;
				break;
			}
		}
		return Promise.resolve(true);
	};

	KendoGridFilterMenu.prototype._clearObSelectedGridFilterId = function(filterId)
	{
		var self = this;
		if (self.obSelectedGridFilterId() === filterId)
			self.obSelectedGridFilterId(null);
		return Promise.resolve(true);
	};

	KendoGridFilterMenu.prototype.saveFilterClick = function()
	{
		var self = this;
		var isNew = self.obSelectedGridFilterDataModel() ? false : true;
		if (isNew)
		{
			var options = {};
			if (this._gridType === 'form')
			{
				options.udgridId = self.options.gridData.value;
			}

			return self.saveAndEditGridFilter("new", self.obSelectedGridFilterDataModel(), true, true, options);
		}
		else
		{
			return self.saveFilter().then(function(result)
			{
				if (!result)
				{
					return;
				}

				self.clearKendoGridQuickFilter();
			});
		}
	};

	KendoGridFilterMenu.prototype.createNewFilterClick = function()
	{
		var options = { title: "New Filter" };
		this.saveAndEditGridFilter("new", undefined, undefined, undefined, options);
	};

	KendoGridFilterMenu.prototype.saveAsNewFilterClick = function()
	{
		if (this.obSelectedGridFilterDataModel())
		{
			this.saveAndEditGridFilter("new", this.obSelectedGridFilterDataModel(), true, true);
		}
		else
		{
			this.saveFilterClick();
		}
	};

	KendoGridFilterMenu.prototype._findOtherDataSourceGridFilters = function()
	{
		const _basicFilterOptions = {
			// "fields": "Id,AutoExportExists,AutoExports,Reminders",
			"relationships": "OmittedRecord,Reminder,AutoExport"
		}

		const qryFilterOptions = $.extend(_basicFilterOptions, { queryOtherDataSourceFilters: true });
		return this._findGridFilters(qryFilterOptions);
	}

	KendoGridFilterMenu.prototype.manageFilterClick = function()
	{
		Promise.all([
			this._findOtherDataSourceGridFilters(),
			this.syncFilterRelationships()
		]).then(function(ret)
		{
			const otherGridFilters = ret[0] || [];
			const otherGridFilterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, otherGridFilters);
			// hide the otherGridFilterDataModels in phone device for VIEW-6244
			let gridFilterDataModels = TF.isPhoneDevice && tf.isViewfinder ? this.obGridFilterDataModels() : this.obGridFilterDataModels().concat(otherGridFilterDataModels);
			const filterData = ko.observableArray(gridFilterDataModels);
			filterData.sort(this._sortGridFilterDataModelsInternal);

			var selectedFilterModelJSONString = JSON.stringify(this.obSelectedGridFilterDataModel() ? this.obSelectedGridFilterDataModel().toData() : null);
			const manageFilterModal = new TF.Modal.Grid.ManageFilterModalViewModel({
				obAllFilters: filterData,
				editFilter: this.saveAndEditGridFilter.bind(this),
				applyFilter: this.applyGridFilter.bind(this),
				filterName: this.obSelectedGridFilterName,
				reminderHide: this.options.reminderOptionHide,
				availableAllDataSourcesHide: this.options.availableAllDataSourcesOptionHide,
			});
			const manageFilterModalViewModel = manageFilterModal.data();

			manageFilterModalViewModel.onFilterDeleted.subscribe((evt, filterId) =>
			{
				const currentLayout = this._obCurrentGridLayoutExtendedDataModel();
				if (currentLayout.filterId() === filterId)
				{
					this.clearFilter();
					this.clearGridFilterClick();
				}

				const allLayouts = this.obGridLayoutExtendedDataModels();
				allLayouts.forEach(layout =>
				{
					if (layout.filterId() === filterId)
					{
						layout.filterId(0);
						layout.filterName("");
					}
				});

				this._clearFilterListCache(filterId);
			});

			manageFilterModalViewModel.onFilterEdited.subscribe((evt, filterData) =>
			{
				const filterDataChanged = typeof filterData !== "boolean";
				if (filterDataChanged && selectedFilterModelJSONString !== JSON.stringify(typeof filterData === "object" ? filterData.toData() : null))
				{
					this.refreshClick();
				}
			});

			tf.modalManager.showModal(manageFilterModal);

		}.bind(this));
	};

	KendoGridFilterMenu.prototype._getGeoSearchTool = function()
	{
		var self = this;
		var geoSearchTool = null;
		var gridMap = null;
		var gridViewModel = null;
		if (tf.documentManagerViewModel)
		{
			gridViewModel = tf.documentManagerViewModel.allDocuments().find(function(document)
			{
				return document.routeState == self.options.routeState;
			});
		}
		else if (tf.pageManager && tf.pageManager.getGridViewie)
		{
			gridViewModel = tf.pageManager.getGridViewie();
		}
		if (gridViewModel)
		{
			gridMap = gridViewModel.gridMap ? gridViewModel.gridMap : gridViewModel._gridMap;
		}

		if (gridMap && gridMap.RoutingMapTool && gridMap.RoutingMapTool.geoSearchTool)
		{
			geoSearchTool = gridMap.RoutingMapTool.geoSearchTool;

		}

		return geoSearchTool;
	}

	KendoGridFilterMenu.prototype.clearGridFilterClick = function()
	{
		var self = this;

		var geoSearchTool = self._getGeoSearchTool();

		if (geoSearchTool && geoSearchTool.isPolygonOnMap())
		{
			cleardPolygonOnMap(geoSearchTool);
		}

		return clearFilterOnGrid();

		function cleardPolygonOnMap(geoSearchTool)
		{
			geoSearchTool.clearSearchResults();
		}

		function clearFilterOnGrid()
		{
			if (self.lightKendoGridDetail)
			{
				self.lightKendoGridDetail.refresh();
			}
			if (self.options.fromSearch)
			{
				self.options.fromSearch = false;
			}

			self.clearFilter();
			self.clearFilterParameter();
			self.clearKendoGridQuickFilter(true);
			self.onClearGridFilterClickEvent.notify();
			self.onClearFilter.notify();
			if (TF.isMobileDevice && tf.isViewfinder)
			{
				return self.rebuildGrid().then(function()
				{
					self.onRefreshPanelEvent.notify();
				});
			}
			else
			{
				return self.rebuildGrid();
			}
		}
	};

	KendoGridFilterMenu.prototype.clearFilter = function()
	{
		var self = this;
		self.options.isTemporaryFilter = null;
		self.obCallOutFilterName(null);
		self.options.callOutFilterName = null;
		self.options.searchFilter = null;
		self.obSelectedGridFilterId(null);
		self.obSelectedGeoRegionIds(null);
		self.obSelectedGeoRegionReminderId(null);
		self.obSelectedStopPoolIds(null);
		self.obSelectedBoundarySchoolCodes(null);
		self.obSelectedNonEligibleZone(null);
		if (self.options && self.options.changeStorageKey)
		{
			self._storageFilterDataKey = self.options.changeStorageKey(self._storageFilterDataKey);
		}
		//IF the request from search, do not sticky filter.
		if (!self.options.fromSearch && !self.options.isTemporaryFilter)
		{
			tf.storageManager.delete(self._storageFilterDataKey);
			tf.storageManager.delete(tf.storageManager.gridCurrentQuickFilter(self.options.gridType));
		}
		self.obClassicFilterSet(null);
		tf.storageManager.delete(tf.storageManager.gridCurrentClassicSearchFilter(self.options.gridType));

		// clear predefined related state.
		self.clearPredefinedGridFilter();

		self.getSelectedIds([]);
		self.obTempOmitExcludeAnyIds([]);
		self.obFilteredExcludeAnyIds([]);

		self.shouldIncludeAdditionFilterIds = false;
		if (self.additionalFilterIds)
		{
			// If there is change to be reverted, show the loading indicator.
			self.additionalFilterIds = null;
			self.shouldIncludeAdditionFilterIds = false;
			//self.refresh();
		}

		self._gridState.filteredIds = null;
		self._gridState.filterClause = "";
		self._filteredIds = null;
	};

	KendoGridFilterMenu.prototype.clearGridFilterParameter = function()
	{
		this.clearFilterParameter();
		this.kendoGrid.dataSource.filter(this.kendoGrid.dataSource.filter() || {});
	}

	KendoGridFilterMenu.prototype.clearFilterParameter = function()
	{
		this.obSelectedGridFilterParameterFilterSet(null);
		this.obSelectedGridFilterParameter(null);
		const openFromNewGridNoParameterFilter = this._gridState.noParameterFilter || this.options.noParameterFilter || this.relatedFilterEntity?.isSharedLink;
		!openFromNewGridNoParameterFilter && tf.storageManager.delete(this._storageParameterFilterKey);
	}

	/**
	 * Clear predefined grid filter state, e.g. from share link.
	 *
	 */
	KendoGridFilterMenu.prototype.clearPredefinedGridFilter = function()
	{
		const self = this;

		if (self.isFromRelated())
		{ //if is from related, the id not change, so need refresh it
			self._selectedGridFilterIdChange();
		}

		//need change the is from related once clear filter, don't change the position
		self.isFromRelated(false);
		delete self.relatedFilterEntity;

		if (self.options.predefinedGridData)
		{
			delete self.options.predefinedGridData.filteredIds;
		}
	};

	KendoGridFilterMenu.prototype.gridFilterClick = function(viewModel, event)
	{
		//need change the is from related once clear filter
		this.isFromRelated && this.isFromRelated(false);

		var isCheckedItem = $(event.target).parent().hasClass('menu-item-checked') && !$(event.target).parent().hasClass('menu-item-broken');
		this.options.searchFilter = null;
		if (TF.isPhoneDevice && isCheckedItem)
		{
			this.clearGridFilterClick();
		}
		else
		{
			this.applyGridFilter(viewModel);
			if (TF.isPhoneDevice && tf.isViewfinder)
			{
				this.onRefreshPanelEvent.notify();
			}
		}
	};

	KendoGridFilterMenu.prototype.gridFilterParameterClick = async function(viewModel, event)
	{
		const value = await this.getGridParamterFilterValue(viewModel);
		if (value === false || (this.obSelectedGridFilterParameter()?.name === viewModel.name && this.obSelectedGridFilterParameter()?.value === value))
		{
			return;
		}

		const data = { ...viewModel, value: value };
		this.setCurrentParameterDateRange(data);
		const openFromNewGridNoParameterFilter = this._gridState.noParameterFilter || this.options.noParameterFilter || this.relatedFilterEntity?.isSharedLink;
		!openFromNewGridNoParameterFilter && tf.storageManager.save(this._storageParameterFilterKey, JSON.stringify(data));
		this.kendoGrid.dataSource.filter(this.kendoGrid.dataSource.filter() || {});
	};

	KendoGridFilterMenu.prototype.setCurrentParameterDateRange = function(viewModel)
	{
		const filterSet = {
			FilterItems: [{
				"FieldName": "ParameterDateRange",
				"Operator": viewModel.key,
				"TypeHint": "Date",
				"Value": viewModel.value
			}],
			FilterSets: [],
			Operator: "And"
		};
		this.obSelectedGridFilterParameter(viewModel);
		this.obSelectedGridFilterParameterFilterSet(filterSet);
	}

	KendoGridFilterMenu.prototype.getGridParamterFilterValue = async function(viewModel)
	{
		let value = 'Invalid Value';
		switch (viewModel.key)
		{
			case 'DateRangeOverlapWith':
			case 'LastXDays':
			case 'NextXDays':
			case 'OnX':
			case 'OnOrAfterX':
			case 'OnOrBeforeX':
				value = await tf.modalManager.showModal(new TF.Modal.Grid.SelectDatesModalViewModel(viewModel));
				break;
		}
		return value;
	}

	KendoGridFilterMenu.prototype.saveAndEditGridFilter = function(isNew, gridFilterDataModel, getCurrentHeaderFilters, getCurrentOmittedRecords, options)
	{
		options = options || {};
		options.currentObFilters = this.obGridFilterDataModels.slice();
		options.hideOmitContainer = (this._gridType === "form" || this._gridType === "session") ? true : options.hideOmitContainer;
		if (this._gridType === 'form')
		{
			options.udgridId = this.options.gridData.value;
		}

		const currentHeaderFilters = this.findCurrentHeaderFilters(true);
		options["originalGridDefinition"] = this.options.gridDefinition; // this is put in options for phone format in quick filters type check.
		return tf.modalManager.showModal(
			new TF.Modal.Grid.ModifyFilterModalViewModel(
				this.options.gridType, isNew,
				gridFilterDataModel ? gridFilterDataModel : null,
				getCurrentHeaderFilters ? currentHeaderFilters : null,
				{
					Columns: convertToOldGridDefinition(this.options.gridDefinition)
				},
				this.getOmittedRecordsID(gridFilterDataModel, getCurrentOmittedRecords),
				options,
				getCurrentHeaderFilters ? this.options.searchFilter : null
			)
		).then(function(result)
		{
			if (!result)
			{
				return true;
			}
			var savedGridFilterDataModel = result.savedGridFilterDataModel;
			if (savedGridFilterDataModel)
			{
				if (isNew !== "new")
				{
					gridFilterDataModel.update(savedGridFilterDataModel.toData());
				}
				else
				{
					this.obGridFilterDataModels.push(savedGridFilterDataModel);
				}
				if (result.applyOnSave)
				{
					this.options.searchFilter = null;
					return this.setGridFilter(savedGridFilterDataModel);
				}
			}
			return savedGridFilterDataModel;
		}.bind(this));
	};

	KendoGridFilterMenu.prototype.getOmittedRecordsID = function(gridFilterDataModel, getCurrentOmittedRecords)
	{
		var currentOmittedRecordIDs = [];
		if (getCurrentOmittedRecords)
		{
			return currentOmittedRecordIDs = this.obFilteredExcludeAnyIds() ? this.obFilteredExcludeAnyIds().concat(this.obTempOmitExcludeAnyIds()) : this.obTempOmitExcludeAnyIds();
		}
		else
		{
			if (gridFilterDataModel == null)
			{
				return;
			}

			if (gridFilterDataModel.omittedRecords())
			{
				return currentOmittedRecordIDs.concat(gridFilterDataModel.omittedRecords().map(function(record) { return record.OmittedRecordID }));
			}
			else
			{
				return currentOmittedRecordIDs;
			}
		}
	};

	KendoGridFilterMenu.prototype.saveFilter = function()
	{
		const gridFilter = this.obSelectedGridFilterDataModel();
		const syncPromise = (gridFilter && gridFilter.id())
			? this.syncFilterRelationships(gridFilter.id())
			: Promise.resolve();
		return syncPromise.then(function()
		{
			if (gridFilter && gridFilter.autoExportExists())
			{
				var message = `This filter is associated with [${gridFilter.autoExportNames()}].`;
				message += " Changes to this filter will affect the data and format of the data being exported. Are you sure you want to modify this filter?";
				return tf.promiseBootbox.yesNo(message, "Confirmation Message");
			}
			return Promise.resolve(true);
		}.bind(this)).then(function(canSave)
		{
			return canSave ? this.saveFilterCore() : Promise.resolve(false);
		}.bind(this));
	}

	//save filter on change without open model
	KendoGridFilterMenu.prototype.saveFilterCore = function()
	{
		var searchData = new TF.SearchParameters(null, null, null, this.findCurrentHeaderFilters(true), null, null, null);
		if (this.obTempOmitExcludeAnyIds().length > 0)
		{
			if (this.obSelectedGridFilterDataModel().dBID() == null)
			{
				tf.promiseBootbox.alert("The filter cannot be saved as a cross data source filter because it references specific records.  You can save it as a new data source specific filter.");
				return Promise.resolve(false);
			}
			var filterID = this.obSelectedGridFilterDataModel().id();
			for (var i = 0; i < this.obTempOmitExcludeAnyIds().length; i++)
			{
				var tempOmittedRecords = {
					FilterID: filterID,
					OmittedRecordID: this.obTempOmitExcludeAnyIds()[i],
					DBID: tf.datasourceManager.databaseId
				}
				this.obSelectedGridFilterDataModel().omittedRecords().push(tempOmittedRecords);
			}
		}
		if (!searchData.data.filterSet && this.obTempOmitExcludeAnyIds().length === 0)
		{
			var data = this.obSelectedGridFilterDataModel().toData(), oldDBID = data.DBID;
			data.DBID = TF.Grid.GridHelper.checkFilterContainsDataBaseSpecificFields(this.options.gridType, this.obSelectedGridFilterDataModel().whereClause()) ? tf.datasourceManager.databaseId : null;

			if (oldDBID == data.DBID) return Promise.resolve(false);

			return tf.promiseAjax.put(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters", data.Id),
				{
					data: data
				}).then(function()
				{
					this.obSelectedGridFilterDataModel().update(data);
					return true;
				}.bind(this));
		}
		else if (!searchData.data.filterSet && this.obTempOmitExcludeAnyIds().length > 0) //only change omitted records
		{
			// var entity = { ids: omittedRecordIds, tableType: gridType }
			var data = this.obSelectedGridFilterDataModel().toData();
			return tf.promiseAjax.put(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters", data.Id),
				{
					paramData: { "@relationships": "OmittedRecord" },
					data: data
				}).then(function(apiResponse)
				{
					this.obFilteredExcludeAnyIds(this.obFilteredExcludeAnyIds().concat(this.obTempOmitExcludeAnyIds()));
					this.obTempOmitExcludeAnyIds([]);
					this.obSelectedGridFilterDataModel().omittedRecords([]);
					if (apiResponse.Items.length > 0 && apiResponse.Items[0].OmittedRecords)
					{
						apiResponse.Items[0].OmittedRecords.forEach(function(omittedRecord)
						{
							this.obSelectedGridFilterDataModel().omittedRecords().push(omittedRecord);
						}.bind(this));
					}
					return true;
				}.bind(this));
		}

		if (this._gridType === 'form')
		{
			const udgridId = this.options.gridData.value;
			var defaultFilter = tf.udgHelper.getUDGridIdFilter(udgridId);

			searchData.data.filterSet.FilterItems = searchData.data.filterSet.FilterItems || [];
			searchData.data.filterSet.FilterItems.push(...defaultFilter);
			searchData.data.filterSet.UDGridID = udgridId;
		}

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(this.options.gridType), "RawFilterClause"),
			{
				data: searchData.data.filterSet
			}).then(function(apiResponse)
			{
				this.obSelectedGridFilterDataModel().whereClause((this.obSelectedGridFilterDataModel().whereClause() ? `(${this.obSelectedGridFilterDataModel().whereClause()}) AND ` : "") + apiResponse.Items[0]);
			}.bind(this)).then(function()
			{
				var data = this.obSelectedGridFilterDataModel().toData();
				data.DBID = TF.Grid.GridHelper.checkFilterContainsDataBaseSpecificFields(this.options.gridType, this.obSelectedGridFilterDataModel().whereClause()) ? tf.datasourceManager.databaseId : null;
				return tf.promiseAjax.put(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters", data.Id),
					{
						data: data
					});
			}.bind(this)).then(function()
			{
				return true;
			});
	};

	KendoGridFilterMenu.prototype.triggerClearQuickFilterBtn = function()
	{
		if ($('.grid-filter-clear-all'))
		{
			$('.grid-filter-clear-all').trigger('mousedown');
		}
	};

	KendoGridFilterMenu.prototype.clearQuickFilterCompent = function()
	{
		var self = this;
		self.triggerClearQuickFilterBtn();
		self.obHeaderFilters([]);
	};
	KendoGridFilterMenu.prototype.applyGridFilter = function(gridFilterDataModel)
	{
		if (!gridFilterDataModel)
		{
			return Promise.resolve(false);
		}

		var self = this;

		var currentFilterId = self.obSelectedGridFilterId();
		var nextFilterId = gridFilterDataModel.id();

		if (TF.Grid.FilterHelper.isDrillDownFillter(currentFilterId))
		{
			self.obHeaderFilters([]);
		}

		return self._syncFilterAndNotifyStatusUpdated(nextFilterId)
			.then(function(result)
			{
				if (TF.Grid.FilterHelper.isDrillDownFillter(nextFilterId) && self.obHeaderFilters.length > 0)
				{
					self.clearQuickFilterCompent();
				}

				if (result)
				{
					if (result.Changed)
					{
						gridFilterDataModel.isValid(result.IsValid);
						return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters", nextFilterId), {
							data: [{ "op": "replace", "path": "/IsValid", "value": result.IsValid }]
						});
					}

					return true;
				}

				return false;
			}).then(result =>
			{
				if (result)
				{
					return self.setGridFilter(gridFilterDataModel, true);
				}

				return false;
			});
	};

	KendoGridFilterMenu.prototype.setGridFilter = function(gridFilterDataModel, isApplyFilter)
	{
		if (!gridFilterDataModel.isValid())
		{
			return tf.promiseBootbox.alert("Filter syntax is invalid. It cannot be applied.", 'Warning', 40000).then(function()
			{
				return false;
			}.bind(this));
		}
		this.obSelectedGridFilterId(gridFilterDataModel.id());
		this.obSelectedGeoRegionIds(null);
		this.obSelectedStopPoolIds(null);
		this.obSelectedBoundarySchoolCodes(null);
		this.obSelectedNonEligibleZone(null);

		if (isApplyFilter)
		{
			this.setCurrentFilter(gridFilterDataModel);
			return;
		}

		return this.loadGridFilter(false).then(() =>
		{
			this.setCurrentFilter(gridFilterDataModel);
			return gridFilterDataModel;
		});
	};

	KendoGridFilterMenu.prototype.setCurrentFilter = function(gridFilterDataModel)
	{
		var self = this;
		function refresh()
		{
			var filter = {};
			self.initStatusBeforeRefresh();
			self.listFilters = TF.ListFilterHelper.initListFilters();
			self.kendoGrid && self.kendoGrid.dataSource && self.kendoGrid.dataSource.filter(filter);
			self.clearDateTimeNumberFilterCellBeforeRefresh();
		}
		if (gridFilterDataModel.type() == "relatedFilter")
		{
			self._gridState.filteredIds = this.relatedFilterEntity.filteredIds;
			self._gridState.filterClause = "";
			refresh();
			return false;
		}

		else if (this.options.summaryFilterFunction && gridFilterDataModel.id() < 0)
		{
			return this.options.summaryFilterFunction(gridFilterDataModel.id())
				.then(function(filteredIds)
				{
					if ($.isArray(filteredIds))
					{
						this._gridState.filteredIds = filteredIds;
					}
					else if (typeof (filteredIds) === "string")
					{
						delete this._gridState.filteredIds;
						this._gridState.filterClause = filteredIds;
					}
					refresh();
					return false;
				}.bind(this));
		}
		else
		{
			refresh();
			return Promise.resolve(false);
		}
	};

	KendoGridFilterMenu.prototype._currentFilterChange = function()
	{
		if (!this.initialFilter)
		{
			var quickFilter = this.getQuickFilterRawData();
			if (quickFilter)
				this.saveQuickFilter(quickFilter);
		}
	};

	KendoGridFilterMenu.prototype._selectedGridFilterIdChange = function()
	{
		const self = this;
		self.obClassicFilterSet(null);
		self.obSelectedGeoRegionIds(null);
		self.obSelectedStopPoolIds(null);
		self.obSelectedBoundarySchoolCodes(null);
		self.obSelectedNonEligibleZone(null);

		const selectedFilterId = self.obSelectedGridFilterId();
		const currentLayout = self._obCurrentGridLayoutExtendedDataModel();

		if (currentLayout && currentLayout.filterId() !== selectedFilterId && selectedFilterId !== FILTER_ID_OF_OPEN_IN_NEW_GRID)
		{
			currentLayout.filterId(selectedFilterId);
			if (!selectedFilterId)
			{
				currentLayout.filterName('');
			}

			self._currentLayoutChange();
		}

		self.raiseGridStateChange && self.raiseGridStateChange();

		if (!selectedFilterId || selectedFilterId > 0)
		{
			//_filteredIds is the setting from outside, not grid inside.
			self._gridState.filteredIds = self._filteredIds;
		}

		//IF the request from search, do not sticky filter.
		if (self.options.fromSearch || self.options.isTemporaryFilter)
		{
			return;
		}

		if (self.options && self.options.changeStorageKey)
		{
			self._storageFilterDataKey = self.options.changeStorageKey(self._storageFilterDataKey);
		}

		const currentFilter = self.obGridFilterDataModels().find(o => o.id() === selectedFilterId);
		if (!currentFilter)
		{
			tf.storageManager.delete(self._storageFilterDataKey);
		}
		else if (currentFilter.type() !== "relatedFilter" && (!self.options.customGridType || self.options.customGridType.toLowerCase() != "dashboardwidget"))
		{
			tf.storageManager.save(self._storageFilterDataKey, selectedFilterId);
		}
	};

	KendoGridFilterMenu.prototype.findCurrentHeaderFilters = function(forRawFilterClause)
	{
		let filterItems = $.extend(true, [], this.obHeaderFilters());
		let filterSets = $.extend(true, [], this.obHeaderFilterSets());
		let gridColumns = tf.dataTypeHelper.getGridDefinition(this._gridType) && tf.dataTypeHelper.getGridDefinition(this._gridType).Columns;

		const updateFilterValue = (_filterItems) =>
		{
			(_filterItems || []).forEach((filterItem) =>
			{
				const fieldItemDefinition = (gridColumns || []).find(item => item && (item.FieldName === filterItem.FieldName));
				if (fieldItemDefinition && !!fieldItemDefinition.isUTC && fieldItemDefinition.isUtc2ClientForSaveAsNewFilter !== false && (filterItem.TypeHint || "").toLowerCase() === "datetime" &&
					TF.FilterHelper.dateTimeNilFiltersOperator.indexOf(filterItem.Operator.toLowerCase()) === -1)
				{
					if (TF.FilterHelper.dateTimeDateParamFiltersOperator.indexOf(filterItem.Operator.toLowerCase()) > -1)
					{
						// the onx operator does not handle utc
						filterItem.Value = toISOStringWithoutTimeZone(moment(filterItem.Value).format("YYYY-MM-DDTHH:mm:ss"));
					} else
					{
						filterItem.Value = toISOStringWithoutTimeZone(utcToClientTimeZone(moment(filterItem.Value).format("YYYY-MM-DDTHH:mm:ss")));
					}
				}
			});
		};

		(filterItems && filterItems.length > 0) && updateFilterValue(filterItems);

		if (filterSets && filterSets.length > 0)
		{
			const updateFilterValueByFilterSets = (_filterSets) =>
			{
				(_filterSets || []).forEach(_filterSet =>
				{
					if (_filterSet.FilterSets && _filterSet.FilterSets.length > 0)
					{
						updateFilterValueByFilterSets(_filterSet.FilterSets);
					}

					if (_filterSet.FilterItems && _filterSet.FilterItems.length > 0)
					{
						updateFilterValue(_filterSet.FilterItems);
					}
				});
			};
			updateFilterValueByFilterSets(filterSets);
		}

		TF.ListFilterHelper.addSelectedIdsIntoFilterItems(filterItems, this.listFilters);
		if (filterItems.length || filterSets.length)
		{
			var filterSet = new TF.FilterSet('And', filterItems, filterSets);
			var _dateTimeFields = tf.helpers.kendoGridHelper.getDateTimeFields(this._gridType);
			var _definitionDateTimeColumns = Array.from((gridColumns || []).filter(column => (column.type || "").toLowerCase() === "datetime" && column.isUTC), column => column.FieldName);
			var dateTimeFields = Array.from(new Set(_dateTimeFields.concat(_definitionDateTimeColumns)));
			if (forRawFilterClause && dateTimeFields.length)
			{
				filterSet = JSON.parse(JSON.stringify(filterSet));
				filterSet.IsConvertDateTimeToLocalFormat = true;
				this.setDateTimeSecondTypeHint(filterSet, dateTimeFields);
			}

			if (forRawFilterClause)
			{
				tf.helpers.kendoGridHelper.resetTimeFilterSet(filterSet.FilterItems);
			}

			return filterSet;
		}
		return null;
	};

	KendoGridFilterMenu.prototype.setDateTimeSecondTypeHint = function(filterSet, dateTimeFields)
	{
		(filterSet.FilterSets || []).forEach(fs =>
		{
			this.setDateTimeSecondTypeHint(fs, dateTimeFields);
		});

		(filterSet.FilterItems || []).forEach(fi =>
		{
			if (dateTimeFields.indexOf(fi.FieldName) !== -1)
			{
				fi.TypeHint = "DateTimeSecond"; // DateTime will convert to range in one minute
			}
		});
	};

	KendoGridFilterMenu.prototype.findCurrentOmittedRecords = function()
	{
		return omittedRecords = this.obTempOmitExcludeAnyIds();
	};

	KendoGridFilterMenu.prototype.filterCausetoFilterSet = function(whereClause)
	{
		var arr = whereClause.replace(/ AND /g, ",").replace(/ OR /g, ",").split(",");
		var filterSet = [];
		arr.forEach(function(e)
		{
			var entity = this.convertFilter(e);
			if (entity)
			{
				filterSet.push(entity);
			}
		}.bind(this));

		return filterSet;
	};

	KendoGridFilterMenu.prototype.convertFilter = function(singleClause)
	{
		var operatorMap = {
			'=': 'eq',
			'<>': 'neq',
			"LIKE": 'contains',
			'>': 'gt',
			'>=': 'gte',
			'<': 'lt',
			'<=': 'lte'
		};

		var arr = singleClause.replace(/\s+/g, " ").split(" ");
		var definition = this.getDefinition.call(this, arr[0].substring(1, arr[0].length - 1));
		if (definition)
		{
			return {
				field: definition.FieldName,
				operator: operatorMap[arr[1]],
				value: this.getFieldValue.call(this, definition, arr[2])
			};
		}
	};

	KendoGridFilterMenu.prototype.getDefinition = function(dbName)
	{
		return this.options.gridDefinition.Columns.filter(function(definition)
		{
			if (definition.DBName)
			{
				return definition.DBName.toLowerCase() === dbName.toLowerCase();
			}
			return definition.FieldName.toLowerCase() === dbName.toLowerCase();
		})[0];
	};

	KendoGridFilterMenu.prototype.getFieldValue = function(definition, rawValue)
	{
		var type = definition.DBType ? definition.DBType : definition.type;
		switch (type)
		{
			case "string":
				return rawValue.substring(1, rawValue.length - 1).replace(/%/g, "");
			case "boolean":
				return rawValue == "true";
			case "integer":
				return parseInt(rawValue);
			case "number":
				return parseFloat(rawValue);
			case "date":
			case "time":
				return rawValue.substring(1, rawValue.length - 1).replace(/%/g, "");
			case "image": //there is no image type after tranfer
				break;
		}
		return arr[2][0] == "'" ? arr[2].substring(1, arr[2].length - 1).replace(/%/g, "") : arr[2];
	};

	KendoGridFilterMenu.prototype._sortGridFilterDataModels = function()
	{
		this.obGridFilterDataModels?.sort(this._sortGridFilterDataModelsInternal);
	};

	KendoGridFilterMenu.prototype._sortGridFilterDataModelsInternal = function(left, right)
	{
		if (left.id() < 0 && right.id() > 0)
		{
			return 1;
		}
		if (left.id() > 0 && right.id() < 0)
		{
			return -1;
		}
		return left.name().toLowerCase() == right.name().toLowerCase() ? 0 : (left.name().toLowerCase() < right.name().toLowerCase() ? -1 : 1);
	};

	KendoGridFilterMenu.prototype._gridFilterDataModelsChange = function()
	{
		var self = this;
		if (this._gridFilterDataModelsChangeSubscription)
		{
			this._gridFilterDataModelsChangeSubscription.dispose();
		}
		this._sortGridFilterDataModels();
		this._gridFilterDataModelsChangeSubscription = this.obGridFilterDataModels?.subscribe(this._gridFilterDataModelsChange, this);
		this.subscriptions.push(this._gridFilterDataModelsChangeSubscription);
		if (self.obSelectedGridFilterId() && self.obSelectedGridFilterId() != FILTER_ID_OF_OPEN_IN_NEW_GRID && !Enumerable.From(this.obGridFilterDataModels()).Where(function(c)
		{
			return c.id() == self.obSelectedGridFilterId();
		}).ToArray()[0])
		{
			this.obSelectedGridFilterId(null);
			this.refresh();
		}
	};

	KendoGridFilterMenu.prototype.mergeFilterIds = function(filteredIds)
	{
		if (!this._filteredIds)
		{ //if outside setting is null, just need current setting.
			return filteredIds;
		}

		var mergefilterIds = [];
		filteredIds.forEach(function(filter)
		{
			if (this._filteredIds.includes(filter))
			{
				mergefilterIds.push(filter);
			}
		}.bind(this));

		return mergefilterIds;
	};

	KendoGridFilterMenu.prototype.getBasicFilterCount = function()
	{
		//issues:
		//1. There is a parameter "time" in trip search, but find it useless, so ignore it here.

		var skip = 0;
		var take = 1;
		var omitIds = null;
		var filterSet = (this._gridState && this._gridState.filterSet) ? this._gridState.filterSet : null;
		if (this.obSelectedGridFilterDataModel() && this.obSelectedGridFilterDataModel().omittedRecords())
		{
			omitIds = this.obSelectedGridFilterDataModel().omittedRecords().map(function(o)
			{
				return o.OmittedRecordID;
			});
		}
		var filterClause = "";
		if (this._gridState && this._gridState.filterClause)
		{
			filterClause += this._gridState.filterClause;
		}
		if (this.obSelectedGridFilterClause())
		{
			filterClause += this.obSelectedGridFilterClause();
		}
		if (!(filterClause || "").trim())
		{
			filterClause = " 1=1";
		}
		if (this._gridType === "form" && this.options?.gridData?.value)
		{
			var udgridId = this.options.gridData.value;
			var defaultFilter = tf.udgHelper.getUDGridIdFilter(udgridId);

			if (filterSet)
			{
				filterSet["FilterItems"].push(...defaultFilter);
			}
			else
			{
				filterSet = {};
				filterSet["FilterItems"] = [];
				filterSet["FilterItems"].push(...defaultFilter);
				filterSet["FilterSets"] = [];
				filterSet["LogicalOperator"] = "and";
			}

			filterSet["UDGridID"] = udgridId;
		}

		var searchData = new TF.SearchParameters(skip, take, null, filterSet, filterClause, this._gridState.filteredIds, omitIds);
		searchData.data.fields = ['Id'];
		//searchData.data.idFilter = { IncludeOnly: null, ExcludeAny: [84] };
		searchData.paramData.getCount = true;
		var prefix = tf.api.apiPrefix();
		if (this._gridType === 'mapincident')
		{
			prefix = tf.api.apiPrefixWithoutDatabase();
		}
		return tf.promiseAjax.post(pathCombine(prefix, "search", tf.dataTypeHelper.getEndpoint(this._gridType)),
			{
				paramData: searchData.paramData,
				data: searchData.data
			}, { overlay: false })
			.then(function(response)
			{
				return response.FilteredRecordCount;
			})
			.catch(function()
			{
				throw false;
			});
	};

	KendoGridFilterMenu.prototype.filterItemShiftSelect = function(item, e, getValue)
	{
		var shiftSelectIds = [];
		shiftSelectIds.push(getValue(item));
		var hasPrevSelected = false, tempIds = [];
		$(e.target).closest("li").prevAll("li").each((index, li) =>
		{
			if ($(li).hasClass("menu-item-checked"))
			{
				hasPrevSelected = true;
			}

			if (hasPrevSelected && !$(li).hasClass("menu-item-checked"))
			{
				return;
			}
			tempIds.push(getValue(ko.dataFor(li)));
		});

		if (tempIds.length > 0 && hasPrevSelected)
		{
			shiftSelectIds = shiftSelectIds.concat(tempIds);
		}

		return shiftSelectIds;
	}

	KendoGridFilterMenu.prototype.filterItemClick = function(item, e, type, selectedIds)
	{
		this.geoRegionClicked = false;
		this.stopPoolClicked = false;
		this.schoolBoundaryClicked = false;
		this[type + "Clicked"] = true;

		let idKey = "Id";
		if (type === "stopPool")
		{
			idKey = "OBJECTID";
		}
		var ids = (selectedIds() || []);

		if (e.shiftKey)
		{
			let shiftSelectIds = this.filterItemShiftSelect(item, e, (item) => item[idKey]);

			$(e.target).closest("li").nextAll("li.menu-item-checked").each((index, li) =>
			{
				ids = ids.filter(c => c != ko.dataFor(li)[idKey]);
			});

			ids = Enumerable.From(ids.concat(shiftSelectIds)).Distinct().OrderBy(x => x).ToArray();
		}
		else if (ids.indexOf(item[idKey]) >= 0)
		{
			ids = ids.filter(x => x != item[idKey]);
		} else
		{
			ids = Enumerable.From(ids.concat([item[idKey]])).Distinct().OrderBy(x => x).ToArray();
		}
		selectedIds(ids);
	}

	// #region geo region type
	KendoGridFilterMenu.prototype.initGeoRegionFilter = function()
	{
		var self = this;
		this.obShowGeoRegionType = ko.observable(["student", "trip", "tripstop", "altsite", "school", "gpsevent", "parceladdresspoint", "street"].indexOf(this._gridType) >= 0);
		this.obGeoRegionTypes = ko.observableArray([]);

		if (!this.obShowGeoRegionType())
		{
			return;
		}

		self.stickyGeoRegionFilterChange = self.stickyGeoRegionFilterChange.bind(this);
		this.obSelectedGeoRegionIds.subscribe(self.stickyGeoRegionFilterChange);
		this.obSelectedGeoRegionReminderId.subscribe(self.stickyGeoRegionFilterChange);
		Promise.all([this._getGeoRegionTypes(), this._getGeoRegions()]).then((data) =>
		{
			let geoRegionTypes = data[0];
			let geoRegions = data[1];
			geoRegionTypes.forEach(type =>
			{
				type.geoRegions = Enumerable.From(geoRegions).Where(x => x.GeoRegionTypeId == type.Id).OrderBy(x => x.Name).ToArray();
			})
			this.obGeoRegionTypes(geoRegionTypes);
		})

		this.geoRegionClick = this.geoRegionClick.bind(this);
	};

	KendoGridFilterMenu.prototype.stickyGeoRegionFilterChange = function()
	{
		var self = this;
		clearTimeout(self._setStorageForGeoRegionIdsTimeout);
		self._setStorageForGeoRegionIdsTimeout = setTimeout(function()
		{
			var geoRegionIds = self.obSelectedGeoRegionIds();
			if (geoRegionIds && geoRegionIds.length > 0)
			{
				tf.storageManager.save(self._storageGeoRegionIdsKey, { ids: geoRegionIds, reminderId: self.obSelectedGeoRegionReminderId() });
			} else
			{
				tf.storageManager.save(self._storageGeoRegionIdsKey, null);
			}
		}, 20);
	}

	KendoGridFilterMenu.prototype.geoRegionClick = function(geoRegion, e)
	{
		this.filterItemClick(geoRegion, e, "geoRegion", this.obSelectedGeoRegionIds);
	};

	KendoGridFilterMenu.prototype._getGeoRegionTypes = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "georegiontypes"), {
			paramData: {
				"@fields": "Id,Name"
			}
		}, { overlay: false })
			.then(function(apiResponse)
			{
				return apiResponse.Items.sort((a, b) => a.Name.toLowerCase() > b.Name.toLowerCase() ? 1 : -1);
			});
	};

	KendoGridFilterMenu.prototype._getGeoRegions = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "georegions"), {
			paramData: {
				"@fields": "Id,Name,GeoRegionTypeId",
			}
		}, { overlay: false })
			.then(function(apiResponse)
			{
				return Enumerable.From(apiResponse.Items).Distinct().OrderBy(x => x.Name).ToArray();
			});
	};

	KendoGridFilterMenu.prototype._setGeoRegionFilterIds = async function(geoRegionInfo, clearFilter)
	{
		var self = this;
		if (!geoRegionInfo.ids || geoRegionInfo.ids.length == 0)
		{
			self._gridState.filteredIds = null;
			return;
		}
		let geoRegionIdsStr = geoRegionInfo.ids.join(",");
		let entityIdsResponse = null;
		// get data ids by georegion
		if (["gpsevent", "parceladdresspoint", "street"].includes(self.options.gridType))
		{
			const endPoint = self.options.gridType == "gpsevent" ? "gpsevents" : tf.dataTypeHelper.getEndpoint(self.options.gridType) + "/ids";
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), endPoint), {
				paramData: {
					"databaseId": tf.datasourceManager.databaseId,
					"georegionIDs": geoRegionIdsStr,
				}
			}).then(function(response)
			{
				response.Items = response.Items.map(function(item)
				{
					return { Id: item };
				});
				return response;
			});
		} else
		{
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(self.options.gridType)), {
				paramData: {
					"georegionIDs": geoRegionIdsStr,
					"@fields": "Id"
				}
			});
		}

		if (clearFilter)
		{
			self.clearFilter();
		}

		self.obSelectedGeoRegionIds(geoRegionInfo.ids);
		self.obSelectedGeoRegionReminderId(geoRegionInfo.reminderId);
		self._gridState.filteredIds = Enumerable.From(entityIdsResponse.Items).Select("$.Id").ToArray();
	};
	// #endregion

	// #region stop pool filter
	KendoGridFilterMenu.prototype.initStopPoolFilter = function()
	{
		this.obStopPoolCategories = ko.observableArray([]);

		if (!this.obShowGeoRegionType())
		{
			return;
		}

		this.stickyStopPoolFilterChange = this.stickyStopPoolFilterChange.bind(this);
		this.obSelectedStopPoolIds.subscribe(this.stickyStopPoolFilterChange);
		Promise.all([this._getStopPoolCategories(), this._getStopPools()]).then((data) =>
		{
			let stopPoolCategories = data[0];
			let stopPools = data[1];
			stopPoolCategories.forEach(category =>
			{
				category.stopPools = Enumerable.From(stopPools).Where(x => x.StopPoolCategoryID == category.Id).OrderBy(x => x.Street).ToArray();
			})
			this.obStopPoolCategories(stopPoolCategories);
		})

		this.stopPoolClick = this.stopPoolClick.bind(this);
	};

	KendoGridFilterMenu.prototype._getStopPoolCategories = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "stoppoolcategories"), {
			paramData: {
				dbid: tf.datasourceManager.databaseId
			}
		}).then(function(apiResponse)
		{
			return apiResponse.Items;
		});
	}

	KendoGridFilterMenu.prototype._getStopPools = function()
	{
		var query = new tf.map.ArcGIS.Query();
		query.outFields = ["OBJECTID", "Stop_ID", "Street", "StopPoolCategoryID"];
		query.returnGeometry = false;
		query.where = " DBID=" + tf.api.datasourceManager.databaseId;
		return new tf.map.ArcGIS.FeatureLayer(arcgisUrls.getMapEditingOneServiceLayerPath(TF.arcgisLayers.sde.MAP_RSPoolPT), {
			objectIdField: "OBJECTID"
		}).queryFeatures(query).then(function(featureSet)
		{
			return featureSet.features.map(function(feature)
			{
				return feature.attributes;
			});
		});
	}

	KendoGridFilterMenu.prototype.stopPoolClick = function(stopPool, e)
	{
		this.filterItemClick(stopPool, e, "stopPool", this.obSelectedStopPoolIds);
	}

	KendoGridFilterMenu.prototype.stickyStopPoolFilterChange = function()
	{
		var self = this;
		clearTimeout(self._setStorageForStopPoolIdsTimeout);
		self._setStorageForStopPoolIdsTimeout = setTimeout(function()
		{
			var stopPoolIds = self.obSelectedStopPoolIds();
			if (stopPoolIds && stopPoolIds.length > 0)
			{
				tf.storageManager.save(self._storageStopPoolIdsKey, { ids: stopPoolIds });
			} else
			{
				tf.storageManager.save(self._storageStopPoolIdsKey, null);
			}
		}, 20);
	}

	KendoGridFilterMenu.prototype._setStopPoolFilterIds = async function(stopPoolInfo, clearFilter)
	{
		if (!stopPoolInfo.ids || stopPoolInfo.ids.length == 0)
		{
			this._gridState.filteredIds = null;
			return;
		}
		let stopPoolIdsStr = stopPoolInfo.ids.join(",");
		let entityIdsResponse = null;
		// get data ids by stopPool
		if (["gpsevent", "parceladdresspoint", "street"].includes(this.options.gridType))
		{
			const endPoint = this.options.gridType == "gpsevent" ? "gpsevents" : tf.dataTypeHelper.getEndpoint(this.options.gridType) + "/ids";
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), endPoint), {
				paramData: {
					"databaseId": tf.datasourceManager.databaseId,
					"stoppoolids": stopPoolIdsStr,
				}
			}).then(function(response)
			{
				response.Items = response.Items.map(function(item)
				{
					return { Id: item };
				});
				return response;
			});
		} else
		{
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(this.options.gridType)), {
				paramData: {
					"stopPoolIDs": stopPoolIdsStr,
					"@fields": "Id"
				}
			});
		}

		if (clearFilter)
		{
			this.clearFilter();
		}

		this.obSelectedStopPoolIds(stopPoolInfo.ids);
		this._gridState.filteredIds = Enumerable.From(entityIdsResponse.Items).Select("$.Id").ToArray();
	};
	// #endregion

	// #region school boundary filter
	KendoGridFilterMenu.prototype._initSchoolBoundariesFilter = function()
	{
		this.obSchoolBoundarySets = ko.observableArray([]);
		if (!this.obShowGeoRegionType())
		{
			return;
		}

		this.stickySchoolBoundaryFilterChange = this.stickySchoolBoundaryFilterChange.bind(this);
		this.obSelectedBoundarySchoolCodes.subscribe(this.stickySchoolBoundaryFilterChange);
		Promise.all([
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "redistricts")),
			TF.Helper.QueryFeatureHelper.getSchoolBoundaries(null, tf.datasourceManager.databaseId)
		]).then(([boundarySetsRes, boundaryFeatures]) =>
		{
			const boundariesBySetId = _.groupBy(boundaryFeatures, feature => feature.attributes?.ReDist_ID);
			boundarySetsRes.Items.forEach(bs =>
			{
				const boundariesOfSpecificSet = boundariesBySetId[bs.Id]?.map(feature => ({
					boundaryId: feature.attributes.OBJECTID,
					school: feature.attributes.School
				})) || [];
				const boundariesBySchool = _.groupBy(boundariesOfSpecificSet, item => item.school);
				bs.BoundarySchools = _.keys(boundariesBySchool).map(schlCode => ({
					SchoolCode: schlCode,
					BoundarySetId: bs.Id,
					BoundaryIds: boundariesBySchool[schlCode].map(b => b.boundaryId)
				}));
			});
			this.obSchoolBoundarySets(boundarySetsRes.Items);
		})
		this.schoolBoundaryClick = this.schoolBoundaryClick.bind(this);
	}

	KendoGridFilterMenu.prototype.schoolBoundaryClick = function(boundarySchool, e)
	{
		this.schoolBoundaryClicked = true;
		this.stopPoolClicked = false;
		this.geoRegionClicked = false;
		let bounarySchoolCodes = (this.obSelectedBoundarySchoolCodes() || []);
		if (e.shiftKey)
		{
			let shiftSelects = this.filterItemShiftSelect(boundarySchool, e, (item) => item);

			$(e.target).closest("li").nextAll("li.menu-item-checked").each((index, li) =>
			{
				bounarySchoolCodes = bounarySchoolCodes.filter(bs => bs.SchoolCode != ko.dataFor(li).SchoolCode || bs.BoundarySetId != ko.dataFor(li).BoundarySetId);
			});

			bounarySchoolCodes = Enumerable.From(bounarySchoolCodes.concat(shiftSelects)).Distinct().OrderBy(x => x.SchoolCode).ToArray();
		}
		else if (bounarySchoolCodes.some(bs => bs.SchoolCode === boundarySchool.SchoolCode && bs.BoundarySetId === boundarySchool.BoundarySetId))
		{
			bounarySchoolCodes = bounarySchoolCodes.filter(bs => bs.SchoolCode != boundarySchool.SchoolCode || bs.BoundarySetId != boundarySchool.BoundarySetId);
		}
		else
		{
			bounarySchoolCodes = Enumerable.From(bounarySchoolCodes.concat([boundarySchool])).Distinct().OrderBy(x => x.SchoolCode).ToArray();
		}
		this.obSelectedBoundarySchoolCodes(bounarySchoolCodes);
	}

	KendoGridFilterMenu.prototype.stickySchoolBoundaryFilterChange = function()
	{
		const self = this;
		clearTimeout(self._setStorageForBoundarySchoolCodesTimeout);
		self._setStorageForBoundarySchoolCodesTimeout = setTimeout(function()
		{
			const bounarySchoolCodes = self.obSelectedBoundarySchoolCodes();
			if (bounarySchoolCodes && bounarySchoolCodes.length > 0)
			{
				tf.storageManager.save(self._storageSchoolBoundariesKey, { boundarySchools: bounarySchoolCodes });
			} else
			{
				tf.storageManager.save(self._storageSchoolBoundariesKey, null);
			}
		}, 20);
	}

	KendoGridFilterMenu.prototype._setSchoolBoundaryFilterIds = async function(boundarySchoolInfo, clearFilter)
	{
		if (!boundarySchoolInfo.boundarySchools || boundarySchoolInfo.boundarySchools.length == 0)
		{
			this._gridState.filteredIds = null;
			return;
		}
		const schollBoundaryIdsStr = boundarySchoolInfo.boundarySchools.map(bs => bs.BoundaryIds).flat().join();
		let entityIdsResponse = null;
		// get data ids by school bundary
		if (["gpsevent", "parceladdresspoint", "street"].includes(this.options.gridType))
		{
			const endPoint = this.options.gridType == "gpsevent" ? "gpsevents" : tf.dataTypeHelper.getEndpoint(this.options.gridType) + "/ids";
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), endPoint), {
				paramData: {
					"databaseId": tf.datasourceManager.databaseId,
					"schoolBoundaryIds": schollBoundaryIdsStr,
				}
			}).then(function(response)
			{
				response.Items = response.Items.map(function(item)
				{
					return { Id: item };
				});
				return response;
			});
		} else
		{
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(this.options.gridType)), {
				paramData: {
					"schoolBoundaryIds": schollBoundaryIdsStr,
					"@fields": "Id"
				}
			});
		}

		if (clearFilter)
		{
			this.clearFilter();
		}

		this.obSelectedBoundarySchoolCodes(boundarySchoolInfo.boundarySchools);
		this._gridState.filteredIds = Enumerable.From(entityIdsResponse.Items).Select("$.Id").ToArray();
	};

	// #endregion

	KendoGridFilterMenu.prototype.filterMenuClosed = function()
	{
		var promise = Promise.resolve(false);
		if (this.geoRegionClicked)
		{
			this.geoRegionClicked = false;
			promise = this._setGeoRegionFilterIds({ ids: this.obSelectedGeoRegionIds(), reminderId: this.obSelectedGeoRegionReminderId() }, true)
		}

		if (this.stopPoolClicked)
		{
			this.stopPoolClicked = false;
			promise = this._setStopPoolFilterIds({ ids: this.obSelectedStopPoolIds() }, true);
		}

		if (this.schoolBoundaryClicked)
		{
			this.schoolBoundaryClicked = false;
			promise = this._setSchoolBoundaryFilterIds({ boundarySchools: this.obSelectedBoundarySchoolCodes() }, true);
		}

		return promise.then((ans) =>
		{
			if (ans !== false)
			{
				this.initStatusBeforeRefresh();
				this.listFilters = TF.ListFilterHelper.initListFilters();
				this.kendoGrid.dataSource.filter({});
			}
		})
	}

	// #region non eligible zone
	KendoGridFilterMenu.prototype.initNonEligibleZone = function()
	{
		var self = this;
		this.obShowNonEligibleZone = ko.observable(["student", "parceladdresspoint", "street"].indexOf(this._gridType) >= 0);
		this._storageNonEligibleZoneIdKey = "grid.currentNonEligibleZone." + this._gridType + ".id";
		this.obNonEligibleZones = ko.observableArray([]);
		this.obSelectedNonEligibleZoneId = ko.computed(function()
		{
			if (self.obSelectedNonEligibleZone())
			{
				return self.obSelectedNonEligibleZone().OBJECTID;
			}
			return 0;
		});

		if (this.obShowNonEligibleZone())
		{
			this.obSelectedNonEligibleZone.subscribe(function()
			{
				clearTimeout(self._setStorageForNonEligibleZoneIdTimeout);
				self._setStorageForNonEligibleZoneIdTimeout = setTimeout(function()
				{
					var nonEligibleZone = self.obSelectedNonEligibleZone();
					if (nonEligibleZone)
					{
						tf.storageManager.save(self._storageNonEligibleZoneIdKey, { id: nonEligibleZone.OBJECTID, reminderId: nonEligibleZone.reminderId });
					} else
					{
						tf.storageManager.save(self._storageNonEligibleZoneIdKey, null);
					}
				}, 20);
			});
			this._getNonEligibleZones();
		}
		this.nonEligibleZoneClick = this.nonEligibleZoneClick.bind(this);
	};

	KendoGridFilterMenu.prototype._getNonEligibleZones = function()
	{
		var self = this;
		var getReminderPromise = Promise.resolve();
		if (TF.ReminderHelper.reminders.length == 0)
		{
			getReminderPromise = TF.ReminderHelper.getReminderCount();
		}

		return getReminderPromise.then(function()
		{
			var query = new tf.map.ArcGIS.Query();
			query.outFields = ["*"];
			query.returnGeometry = false;
			query.where = " DBID=" + tf.api.datasourceManager.databaseId;
			return new tf.map.ArcGIS.FeatureLayer(arcgisUrls.getMapEditingOneServiceLayerPath(TF.arcgisLayers.sde.MAP_NEZ), {
				objectIdField: "OBJECTID"
			}).queryFeatures(query).then(function(featureSet)
			{
				var nonEligibleZones = featureSet.features.map(function(feature)
				{
					var nonEligibleZone = feature.attributes;
					TF.ReminderHelper.reminders.forEach(function(reminder)
					{
						if (reminder.DataTypeId == tf.dataTypeHelper.getId(self.options.gridType) && reminder.NonEligibleZoneId == nonEligibleZone.OBJECTID)
						{
							nonEligibleZone.reminderId = reminder.Id;
						}
					});
					return nonEligibleZone;

				}).sort((a, b) => (a.School + a.Name).localeCompare(b.School + b.Name));
				self.obNonEligibleZones(nonEligibleZones);
				if (self.obSelectedNonEligibleZone())
				{
					self.obSelectedNonEligibleZone(Enumerable.From(self.obNonEligibleZones()).FirstOrDefault(null, function(c) { return c.OBJECTID == self.obSelectedNonEligibleZone().OBJECTID; }));
				}
			});
		});
	};

	KendoGridFilterMenu.prototype.nonEligibleZoneClick = function(nonEligibleZone)
	{
		var self = this;
		self._setNonEligibleZoneFilterIds(nonEligibleZone, true).then(function()
		{
			self.initStatusBeforeRefresh();
			self.kendoGrid.dataSource.filter({});
		});
	};

	KendoGridFilterMenu.prototype._setNonEligibleZoneFilterIds = async function(nonEligibleZone, clearFilter)
	{
		var self = this;
		let entityIdsResponse = null;
		if (["parceladdresspoint", "street"].includes(self.options.gridType))
		{
			const endPoint = tf.dataTypeHelper.getEndpoint(self.options.gridType) + "/ids";
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), endPoint), {
				paramData: {
					"databaseId": tf.datasourceManager.databaseId,
					"nonEligibleZoneIds": nonEligibleZone.OBJECTID,
				}
			}).then(function(response)
			{
				response.Items = response.Items.map(function(item)
				{
					return { Id: item };
				});
				return response;
			});
		} else
		{
			entityIdsResponse = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint(self.options.gridType)), {
				paramData: {
					"nonEligibleZoneId": nonEligibleZone.OBJECTID,
					"@fields": "Id"
				}
			});
		}

		if (clearFilter)
		{
			self.clearFilter();
		}

		var data = Enumerable.From(self.obNonEligibleZones()).FirstOrDefault(null, function(c) { return c.OBJECTID == nonEligibleZone.OBJECTID; });
		self.obSelectedNonEligibleZone(data || nonEligibleZone);
		self._gridState.filteredIds = Enumerable.From(entityIdsResponse.Items).Select("$.Id").ToArray();
	};

	KendoGridFilterMenu.prototype.checkGPSEventFilterIsValid = function(whereClause)
	{
		try
		{
			const isGpsConnectPlusEnabled = TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled;
			const currentGPSEventFieldsList = Array.from(TF.Helper.VehicleEventHelper.getGridDefinition().Columns, col => col.FieldName);
			const setGPSEventFieldName = (fieldName) =>
			{
				switch (fieldName)
				{
					case "BusNum":
					case "VehicleExternalName":
						return isGpsConnectPlusEnabled ? "BusNum" : "VehicleExternalName";
					case "AdjLocation":
					case "Location":
						return isGpsConnectPlusEnabled ? "Location" : "AdjLocation";
					case "CreatedOn":
					case "EventCreateDateTime":
						return isGpsConnectPlusEnabled ? "CreatedOn" : "EventCreateDateTime";
					default:
						return fieldName;
				}
			};
			const formatWhereClause = (_whereClause) =>
			{
				const regex = /\[(.+?)\]/g;
				const fields = [];
				((_whereClause || "").match(regex) || []).forEach(field =>
				{
					fields.push(setGPSEventFieldName((field || "").replaceAll("[", "").replaceAll("]", "")));
				});
				return fields;
			};
			let isValid = true;
			(Array.from(new Set(formatWhereClause(whereClause))) || []).forEach(field =>
			{
				if (!currentGPSEventFieldsList.includes(field))
				{
					isValid = false;
				}
			});

			let newWhereClause = whereClause || "";

			if (isValid)
			{
				if (isGpsConnectPlusEnabled)
				{
					newWhereClause = newWhereClause.replaceAll("[VehicleExternalName]", "[BusNum]");
					newWhereClause = newWhereClause.replaceAll("[AdjLocation]", "[Location]");
					newWhereClause = newWhereClause.replaceAll("[EventCreateDateTime]", "[CreatedOn]");
				} else
				{
					newWhereClause = newWhereClause.replaceAll("[BusNum]", "[VehicleExternalName]");
					newWhereClause = newWhereClause.replaceAll("[Location]", "[AdjLocation]");
					newWhereClause = newWhereClause.replaceAll("[CreatedOn]", "[EventCreateDateTime]");
				}
			}

			return {
				isValid,
				whereClause: newWhereClause,
			};
		}
		catch (e)
		{
			return {
				isValid: true,
				whereClause,
			};
		}
	}
	// #endregion

})();
// Reminder
(function()
{
	var KendoGridFilterMenu = TF.Grid.KendoGridFilterMenu;

	KendoGridFilterMenu.prototype.initReminder = function()
	{
		this.obReminderName = ko.observable(this.options.reminderName);
		this.obReminderId = ko.computed(function()
		{
			if (this.obSelectedGridFilterDataModel())
			{
				return this.obSelectedGridFilterDataModel().reminderId();
			}
			if (this.obSelectedGeoRegionReminderId())
			{
				return this.obSelectedGeoRegionReminderId();
			}
			if (this.obSelectedNonEligibleZone())
			{
				return this.obSelectedNonEligibleZone().reminderId;
			}
			return 0;
		}, this);

		this.reminderMenuEnable = ko.computed(function()
		{
			return this.obSelectedGridFilterId() > 0 || (this.obSelectedGeoRegionIds() && this.obSelectedGeoRegionIds().length > 0) || this.obSelectedNonEligibleZoneId() > 0 || !!this.obSelectedStaticFilterName();
		}, this);
		this.loadGridFilter = this.loadGridFilter.bind(this);
		this._reminderSubscription = PubSub.subscribe(topicCombine(pb.DATA_CHANGE, "reminder"), this.loadGridFilter);
	};

	// click on set reminder
	KendoGridFilterMenu.prototype.setReminder = function()
	{
		var self = this;
		if (!this.reminderMenuEnable())
		{
			return;
		}

		let geoRegionIds = this.obSelectedGeoRegionIds();
		tf.modalManager.showModal(new TF.Modal.ReminderViewModalViewModel({
			Id: this.obReminderId(),
			DBID: tf.datasourceManager.databaseId,
			DataTypeId: tf.dataTypeHelper.getId(this.options.gridType),
			GeoRegionIds: Array.isArray(geoRegionIds) && geoRegionIds.length ? geoRegionIds.join() : null,
			NonEligibleZoneId: this.obSelectedNonEligibleZoneId(),
			FilterID: this.obSelectedGridFilterId(),
			FilterName: this.obSelectedGridFilterName(),
			Name: "",
			StaticFilterName: this.obSelectedStaticFilterName()
		})).then(function(data)
		{
			if (data)
			{
				if (self.obSelectedGeoRegionIds() && self.obSelectedGeoRegionIds().length > 0)
				{
					self.obSelectedGeoRegionReminderId(data.Id);
				} else if (self.obSelectedNonEligibleZoneId() > 0)
				{
					self.obSelectedNonEligibleZone().reminderId = data.Id;
					self.obSelectedNonEligibleZone(self.obSelectedNonEligibleZone());
				}
			}
		});
		return;
	};
})();
