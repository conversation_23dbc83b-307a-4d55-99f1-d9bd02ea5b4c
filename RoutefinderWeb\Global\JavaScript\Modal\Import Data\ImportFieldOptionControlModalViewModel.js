(function()
{
	createNamespace('TF.Modal').ImportFieldOptionControlModalViewModel = ImportFieldOptionControlModalViewModel;

	function ImportFieldOptionControlModalViewModel(fieldOption)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-sm";
		self.contentTemplate('modal/import data/importfieldoptioncontrol');
		self.buttonTemplate('modal/positivenegative');
		self.importFieldOptionControlViewModel = new TF.Control.ImportFieldOptionControlViewModel(fieldOption, self.shortCutKeyHashMapKeyName);
		self.data(self.importFieldOptionControlViewModel);
		self.title("Import Field Option");
		self.obPositiveButtonLabel("OK");
		self.obNegativeButtonLabel("Cancel");
	};

	ImportFieldOptionControlModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ImportFieldOptionControlModalViewModel.prototype.constructor = ImportFieldOptionControlModalViewModel;

	/**
	 * The event of OK button click.
	 * @return {void}
	 */
	ImportFieldOptionControlModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.positiveClose(self.importFieldOptionControlViewModel.fieldOption);
	};
})();
