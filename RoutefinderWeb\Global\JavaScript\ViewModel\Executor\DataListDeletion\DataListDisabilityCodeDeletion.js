﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListDisabilityCodeDeletion = DataListDisabilityCodeDeletion;

	function DataListDisabilityCodeDeletion()
	{
		this.type = 'disabilitycode';
		this.deleteType = 'Code';
		this.deleteRecordName = 'Disability Code';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListDisabilityCodeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListDisabilityCodeDeletion.prototype.constructor = DataListDisabilityCodeDeletion;

	DataListDisabilityCodeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();