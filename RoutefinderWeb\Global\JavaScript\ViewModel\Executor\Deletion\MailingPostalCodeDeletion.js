﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.MailingPostalCodeDeletion = MailingPostalCodeDeletion;

	function MailingPostalCodeDeletion()
	{
		this.type = 'mailingpostalcode';
		namespace.BaseDeletion.call(this, true);
	}

	MailingPostalCodeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	MailingPostalCodeDeletion.prototype.constructor = MailingPostalCodeDeletion;

	MailingPostalCodeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}

	MailingPostalCodeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();