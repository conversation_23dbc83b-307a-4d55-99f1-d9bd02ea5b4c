@import "fontSize";

@media print {
	@page {
		margin: 0.5in;
	}

	body {
		&>div {
			display: none;
		}

		-webkit-print-color-adjust: exact;
		color-adjust: exact;
		height: auto;
		position: relative;
		min-width: 0px !important;
		min-height: 0px !important;
		overflow: visible !important;
	}

	body>.other-print-panel,
	body>.detail-view-panel {
		display: block !important;
		width: 100% !important;
		height: 100% !important;
		min-width: 0 !important;
	}

	body>.detail-view-panel {
		.kendo-grid {
			div {
				overflow: hidden !important;
			}

			table {
				.fontSize(1, 2, true);
			}
		}

		.detail-view {
			.container-fluid {
				overflow: visible !important;
				height: 100% !important;
			}

			.grid-stack {
				.grid-stack-item-content {
					.item-title {
						.fontSize(1, -1);
					}

					.item-content {
						.fontSize(1, 2);
					}

					.map {
						img {
							//clear bootstrap.css max-width:100%.
							max-width: none !important;
						}

						.esri-ui,
						.esri-view-user-storage,
						.esriPopup,
						.off-map-tool,
						.map-expand-button,
						.measurement-tab-container,
						.measurement-tab:not(.active),
						.location-track,
						.esri-overlay-surface,
						.measurement-info-panel .close-btn,
						.on-map-tool {
							display: none !important;
						}
					}
				}
			}

			.calendar-item {
				.fontSize(1, -1, true);

				div {
					overflow: hidden !important;
				}

				table {
					.fontSize(1, -1, true);
				}
			}

			.detail-header {
				.group-buttons {
					display: none !important;
				}

				.buttons {
					display: none !important;
				}

				.head-text {
					width: calc(~"100% - 160px") !important;

					.title {
						color: black !important;
						.fontSize(1, 8);
					}

					.sub-title {
						color: black !important;
						.fontSize(1, 1);
						opacity: 0.7 !important;
					}
				}
			}
		}

		.item-icon-container {
			width: 160px;
			height: 54px;
			position: absolute;
			top: 0;
			right: 0;
		}
	}


}