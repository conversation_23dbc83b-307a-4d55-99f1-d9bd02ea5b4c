function tfdispose(obj)
{
	function disposeEsriObj(object)
	{
		if (!object)
		{
			return;
		}

		if (object.attributes && object.attributes.dataModel)
		{
			if (object.attributes.dataModel.path && isEsriObject(object.attributes.dataModel.path.geometry))
			{
				disposeEsriObj(object.attributes.dataModel.path.geometry);
			}

			if (isEsriObject(object.attributes.dataModel.geometry))
			{
				// object.attributes.dataModel.geometry equals to object.geometry
				// disposeEsriObj(object.attributes.dataModel.geometry);
			}
		}

		if (isEsriObject(object.geometry))
		{
			disposeEsriObj(object.geometry);
		}

		if (isEsriObject(object.symbol))
		{
			disposeEsriObj(object.symbol);
		}

		if (!object.destroyed && object.destroy) 
		{
			object.destroy();
		}
	}

	if (obj == window || !obj) return;

	if (isEsriObject(obj))
	{
		disposeEsriObj(obj);
		return;
	}

	tf.ajax.abort(obj);

	const funName = obj && obj.constructor && obj.constructor.name;

	Object.keys(obj).forEach(key =>
	{
		const value = obj[key];

		if (value === null) return;

		if (value instanceof TF.Events.Event)
		{
			value.unsubscribeAll();
		}
		else if (value && value.subscribe && value.getSubscriptionsCount
			&& typeof value.getSubscriptionsCount === "function"
			&& value.getSubscriptionsCount() > 0)
		{
			window.unremovedSubscriptions = window.unremovedSubscriptions || {};
			window.unremovedSubscriptions[funName] = window.unremovedSubscriptions[funName] || new Set();
			window.unremovedSubscriptions[funName].add(key)
		}
		else if (value instanceof $)
		{
			Array.from(value).forEach(el =>
			{
				if (ko.dataFor(el) == obj)
				{
					ko.cleanNode(el);
				}
			});
		}

		obj[key] = null;
	});
}

function isEsriObject(obj)
{
	return Boolean(obj && obj.declaredClass && obj.declaredClass.startsWith("esri."));
}
