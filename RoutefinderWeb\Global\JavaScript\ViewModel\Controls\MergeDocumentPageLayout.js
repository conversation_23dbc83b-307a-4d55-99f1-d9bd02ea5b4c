(function()
{
	createNamespace("TF.Control").MergeDocumentPageLayout = MergeDocumentPageLayout;

	var dpi = 96;
	function MergeDocumentPageLayout(templateType, autoFit)
	{
		var self = this;
		self.templateType = templateType;
		self.autoFit = autoFit;

		self.mergeDocument = ko.observable();
		self.mergeDocument.subscribe(function()
		{
			self.unsubscribeDataModel();
		}, null, "beforeChange");

		self.mergeDocument.subscribe(function()
		{
			self.subscribeDataModel();
		});

		TF.MergeTemplateTypeHelper.AllFieldsNames.forEach(function(fieldName)
		{
			self[fieldName] = ko.computed(function()
			{
				if (self.mergeDocument() && self.mergeDocument()[fieldName] && self.mergeDocument()[fieldName]() != null)
				{
					return self.mergeDocument()[fieldName]();
				}

				if (self.templateType() && self.templateType()[fieldName] && self.templateType()[fieldName]() != null)
				{
					return self.templateType()[fieldName]();
				}

				return getFieldDefaultValue(fieldName);
			});
		});

		self.isValid = ko.computed(function()
		{
			if (!self.pageWidth() || !self.pageHeight())
				return false;

			var minimumHeight = self.cellHeight();
			if (self.hasHeader())
				minimumHeight = NP.plus(minimumHeight, self.headerHeight());
			if (self.hasFooter())
				minimumHeight = NP.plus(minimumHeight, self.footerHeight());

			var minimumWidth = self.cellWidth();
			return self.pageHeight() >= minimumHeight &&
				self.pageWidth() >= minimumWidth;
		});

		self.cellRows = ko.computed(function()
		{
			var cellRows = [], templateType = self.templateType();
			if (templateType)
			{
				var top = (self.hasHeader() ? self.headerHeight() : 0) + self.marginTop();
				for (var i = 0, rowCount = self.rows(); i < rowCount; i++)
				{
					var cellColumns = [];
					var left = self.marginLeft();
					for (var j = 0, columnCount = templateType.columns(); j < columnCount; j++)
					{
						cellColumns.push({ width: templateType.cellWidth(), height: templateType.cellHeight(), top: top, left: left });
						left += templateType.cellWidth() + templateType.columnSpacing();
					}

					cellRows.push(cellColumns);
					top += templateType.cellHeight() + self.rowSpacing();
				}
			}

			return cellRows;
		});

		self.container = ko.observable();
		self.zoomValue = ko.computed(function()
		{
			if (!autoFit) return 1;

			var templateType = self.templateType(),
				container = self.container();
			if (!container || !templateType) return 1;

			return self.getAutoFitZoomValue(container);
		});

		self.outlinePadding = ko.computed(function()
		{
			if (!autoFit)
			{
				return undefined;
			}
			var templateType = self.templateType(),
				container = self.container();
			if (!container ||
				!templateType ||
				templateType.pageHeight() <= 0 ||
				templateType.pageWidth() <= 0) 
			{
				return undefined;
			}

			var zoomValue = self.zoomValue(),
				pageHeight = templateType.pageHeight() * dpi * zoomValue,
				$container = $(container),
				height = $container.innerHeight();

			var paddingTop = self._containerPaddingTop,
				paddingBottom = self._containerPaddingBottom;

			if (pageHeight < height)
			{
				var offset = (height - pageHeight - paddingTop - paddingBottom) / 2;
				paddingTop += offset;
				paddingBottom += offset;
			}

			return paddingTop + "px " + 0 + " " + paddingBottom + "px " + 0;
		});

		self.padding = ko.computed(function()
		{
			return self.marginTop() + 'in ' + self.marginRight() + 'in ' + self.marginBottom() + 'in ' + self.marginLeft() + 'in';
		});

		self.headerEditorHeight = ko.pureComputed(function()
		{
			return (self.headerHeight() * 96 - 1) + 'px';
		});

		self.footerEditorHeight = ko.pureComputed(function()
		{
			return (self.footerHeight() * 96 - 1) + 'px';
		});

		self.editorHeight = ko.pureComputed(function()
		{
			var cellHeight = self.cellHeight();
			var padding = self.cellPadding();
			return (NP.minus(cellHeight, NP.times(padding, 2)) * dpi - 2) + "px";
		});

		self.editorWidth = ko.pureComputed(function()
		{
			var cellWidth = self.cellWidth();
			var padding = self.cellPadding();
			return (NP.minus(cellWidth, NP.times(padding, 2)) * dpi - 2) + "px";
		});

		self.editorTop = ko.pureComputed(function()
		{
			var top = self.marginTop();
			var headerHeight = self.hasHeader() ? self.headerHeight() : 0;
			var padding = self.cellPadding();
			return (NP.plus(top, headerHeight, padding) * dpi + 1) + "px";
		});

		self.editorLeft = ko.pureComputed(function()
		{
			var left = self.marginLeft();
			var padding = self.cellPadding();
			return (NP.plus(left, padding) * dpi + 1) + "px";
		});
	}

	MergeDocumentPageLayout.prototype.unsubscribeDataModel = function()
	{
		if (this.modelSubscriptions)
		{
			this.modelSubscriptions.forEach(function(item)
			{
				item.dispose();
			});
		}
	};

	MergeDocumentPageLayout.prototype.subscribeDataModel = function()
	{
		var self = this,
			model = self.mergeDocument();
		if (!model) return;

		self.modelSubscriptions = self.modelSubscriptions || [];
		self.modelSubscriptions.push(model.hasHeader.subscribe(function()
		{
			setTimeout(function()
			{
				if (!model.hasHeader())
				{
					model.headerContent(null);
				}
				self.refreshHeaderFooter();
			});

		}));

		self.modelSubscriptions.push(model.hasFooter.subscribe(function()
		{
			setTimeout(function()
			{
				if (!model.hasFooter())
				{
					model.footerContent(null);
				}
				self.refreshHeaderFooter();
			});

		}));
	};

	MergeDocumentPageLayout.prototype.show = function()
	{
		$(this.container()).show();
	};

	MergeDocumentPageLayout.prototype.resetPadding = function()
	{
		$(this.container()).css('padding', '0');
	};

	MergeDocumentPageLayout.prototype.hide = function()
	{
		$(this.container()).hide();
	};

	MergeDocumentPageLayout.prototype.refresh = function()
	{
		this.refreshHeaderFooter();
		this.templateType.notifySubscribers();
	};

	function getFieldDefaultValue(fieldName)
	{
		return (fieldName == "hasHeader" || fieldName == "hasFooter") ? false : 0;
	}

	MergeDocumentPageLayout.prototype.refreshHeaderFooter = function()
	{
		var self = this,
			mergeDocument = self.mergeDocument();
		if (mergeDocument)
		{
			var templateType = {};
			var initTemplate = false;
			if (self.prevTemplateType === undefined)
			{
				initTemplate = true;
				self.prevTemplateType = {};
			}

			TF.MergeTemplateTypeHelper.AllFieldsNames.forEach(function(fieldName)
			{
				var value = self[fieldName]();
				value = value == null ? getFieldDefaultValue(fieldName) : value;
				var inputFieldName = toCapitalizeFirstLetter(fieldName);
				templateType[inputFieldName] = value;
				if (initTemplate)
				{
					self.prevTemplateType[inputFieldName] = value;
				}
			});

			var result = TF.MergeTemplateTypeCalculationHelper.recalculatePageSettingFields(templateType, null, self.prevTemplateType);
			TF.MergeTemplateTypeHelper.resetTemplateData(result, mergeDocument);

			TF.MergeTemplateTypeHelper.AllFieldsNames.forEach(function(fieldName)
			{
				var inputFieldName = toCapitalizeFirstLetter(fieldName);
				self.prevTemplateType[inputFieldName] = self[fieldName]();
			});
		}
	};

	MergeDocumentPageLayout.prototype.init = function(el)
	{
		var self = this;
		self.container(el);
		var $container = $(el);
		self._containerPaddingTop = parseFloat($container.css("padding-top").replace("px", ""));
		self._containerPaddingRight = parseFloat($container.css("padding-right").replace("px", ""));
		self._containerPaddingBottom = parseFloat($container.css("padding-bottom").replace("px", ""));
		self._containerPaddingLeft = parseFloat($container.css("padding-left").replace("px", ""));

		if (this.autoFit)
		{
			$(el).css("overflow", "hidden");
			$(el).children(0).css("margin", 0);
		}
	};

	MergeDocumentPageLayout.prototype.getAutoFitZoomValue = function(container)
	{
		var self = this;
		if (!self.templateType().hasPageSettings() ||
			self.pageWidth() <= 0 ||
			self.pageHeight() <= 0)
		{
			return 1;
		}

		var $container = $(container),
			width = $container.innerWidth() - 10 - self._containerPaddingLeft - self._containerPaddingRight,
			height = $container.innerHeight() - 10 - self._containerPaddingTop - self._containerPaddingBottom,
			hZoomValue = width / (self.pageWidth() * dpi),
			vZoomValue = height / (self.pageHeight() * dpi);
		return Math.min(hZoomValue, vZoomValue);
	};
})();