﻿(function()
{
	createNamespace('TF.Modal').GeoFinderModalViewModel = GeoFinderModalViewModel;

	function GeoFinderModalViewModel(geoFinderTool, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/GeoFinderControl');
		this.buttonTemplate('modal/positivenegative');
		this.sizeCss = "modal-dialog-md";
		this.title("Geofinder Results");
		this.obPositiveButtonLabel("Apply");
		this.viewModel = new TF.Control.GeoFinderViewModel(geoFinderTool, options);
		this.data(this.viewModel);
	}

	GeoFinderModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	GeoFinderModalViewModel.prototype.constructor = GeoFinderModalViewModel;

	GeoFinderModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	GeoFinderModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};

})();
