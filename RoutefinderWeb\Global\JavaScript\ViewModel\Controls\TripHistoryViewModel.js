﻿(function()
{
	createNamespace('TF.Control').TripHistoryViewModel = TripHistoryViewModel;

	function TripHistoryViewModel(tripHistoryId, tripId, tripName)
	{
		this.originalTripHistoryData = null;
		this.obEntityDataModel = ko.observable(new TF.DataModel.TripHistoryDataModel());
		this.obIsEdit = ko.observable(tripHistoryId > 0);
		this.obApplySchoolCalendar = ko.observable(false);
		this.tripSchools = '';

		//#region vehicle selector
		this.obVehicleSource = ko.observableArray();
		this.obSelectedVehicle = ko.observable();
		this.obSelectedVehicle.subscribe(TF.Helper.DropDownMenuHelper.setSelectValue(this, "vehicleId", "obSelectedVehicle", function(obj) { return obj ? obj.Id : 0; }), this);
		this.obSelectedVehicleText = ko.observable();
		//#endregion

		//#region driver selector
		this.obDriverSource = ko.observableArray();
		this.obSelectedDriver = ko.observable();
		this.obSelectedDriver.subscribe(TF.Helper.DropDownMenuHelper.setSelectValue(this, "driverId", "obSelectedDriver", function(obj) { return obj ? obj.Id : 0; }), this);
		this.obSelectedDriverText = ko.observable();
		//#endregion

		//#region aide selector
		this.obAideSource = ko.observableArray();
		this.obEndLessThanStart = ko.observableArray(false);
		this.obSelectedAide = ko.observable();
		this.obSelectedAide.subscribe(TF.Helper.DropDownMenuHelper.setSelectValue(this, "aideId", "obSelectedAide", function(obj) { return obj ? obj.Id : 0; }), this);
		this.obSelectedAideText = ko.observable();
		//#endregion

		//#region route selector
		this.obRouteSource = ko.observableArray();
		this.obSelectedRoute = ko.observable();
		this.obSelectedRoute.subscribe(TF.Helper.DropDownMenuHelper.setSelectValue(this, "routeID", "obSelectedRoute", function(obj) { return obj ? obj.Id : 0; }), this);
		this.obSelectedRouteText = ko.observable();
		//#endregion

		//#region day of week
		this.obWeekDayDisable = ko.observable(false);
		this.obDay1 = ko.observable(false);
		this.obDay2 = ko.observable(false);
		this.obDay3 = ko.observable(false);
		this.obDay4 = ko.observable(false);
		this.obDay5 = ko.observable(false);
		this.obDay6 = ko.observable(false);
		this.obDay7 = ko.observable(false);
		this.obDay8 = ko.observable(false);
		this.obDay1Disable = ko.observable(false);
		this.obDay2Disable = ko.observable(false);
		this.obDay3Disable = ko.observable(false);
		this.obDay4Disable = ko.observable(false);
		this.obDay5Disable = ko.observable(false);
		this.obDay6Disable = ko.observable(false);
		this.obDay7Disable = ko.observable(false);
		this.obDay8Disable = ko.observable(false);
		this.obDayColumns = ko.observableArray([]);
		for (var i = 1; i < 8; i++)
		{
			this["obDay" + i].subscribe(() =>
			{
				this.obAvailableDates(this._calculateAvailableDates());
				this.resetAttendanceData();
			});
		}
		//#endregion

		this.pageLevelViewModel = new TF.PageLevel.TripHistoryPageLevelViewModel(this);

		this.obCustomStartDate = ko.observable(moment(new Date()).format("L"));
		this.obCustomEndDate = ko.observable(moment(new Date()).format("L"));
		this.obCustomStartDate.subscribe(this.onCustomDateChanged, this);
		this.obCustomEndDate.subscribe(this.onCustomDateChanged, this);
		this.originalStatus = {};
		ko.computed(function()
		{
			if (this.obEntityDataModel().apiIsDirty())
			{
				this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
			}
		}, this);

		this.days = [this.obDay7, this.obDay1, this.obDay2, this.obDay3, this.obDay4, this.obDay5, this.obDay6];

		this.tripId = tripId;
		this.tripName = tripName;
		// if it is in edit mode, only one day's attendance will be loaded.
		if (tripHistoryId && tripHistoryId > 0)
		{
			this.obEntityDataModel().id(tripHistoryId);
		}

		setTimeout(() =>
		{
			this.updateWeekDayForCustomView();
		});

		this.tripHistories = [];
		this.tripHistoryDates = [];
		this.isDraggingScrolling = false;
		this.selectedItem = null;
		this.obEndLessThanStart(false);
		this.validationFields = {};

		this.showHideStudentClick = this.showHideStudentClick.bind(this);
		this.startShowHideStudentClick = this.startShowHideStudentClick.bind(this);
		this.studentValueChanged = this.studentValueChanged.bind(this);
		this.stopValueChanged = this.stopValueChanged.bind(this);
		this.totalValueChanged = this.totalValueChanged.bind(this);
		this.setTripStopTimePopover = this.setTripStopTimePopover.bind(this);
		this.createAdjustAttendanceTripStopTimeModal = this.createAdjustAttendanceTripStopTimeModal.bind(this);
		this.getAlertText = this.getAlertText.bind(this);
		this.obAvailableDates = ko.observable(this._calculateAvailableDates());
		this.actualAltAttendance = ko.observable(0);
	}

	// Invoke when Monday to Friday checkbox value is changed.
	TripHistoryViewModel.prototype.monFriChanged = function(viewModel, e)
	{
		if (this.$checkbox)
		{
			this.pageLevelViewModel.saveValidate(this.$checkbox);
		}
		if (e.target.checked)
		{
			this.obDay1(true);
			this.obDay2(true);
			this.obDay3(true);
			this.obDay4(true);
			this.obDay5(true);
		}
		else
		{
			this.obDay1(false);
			this.obDay2(false);
			this.obDay3(false);
			this.obDay4(false);
			this.obDay5(false);
		}
		this.dayCheckChanged();
	};

	TripHistoryViewModel.prototype._calculateAvailableDates = function()
	{
		const weekDays = [this.obDay7(), this.obDay1(), this.obDay2(), this.obDay3(), this.obDay4(), this.obDay5(), this.obDay6()];
		return this._getAllDatesBetweenDateRange(this.obCustomStartDate(), this.obCustomEndDate(), weekDays);
	}


	// Update the value of Monday to Friday checkbox according to the values of other work day checkboxes.
	TripHistoryViewModel.prototype._updateWorkDayCheckbox = function()
	{
		var allWorkDayChecked = true;
		for (var i = 1; i <= 5; i++)
		{
			if (!this["obDay" + i]())
			{
				allWorkDayChecked = false;
				break;
			}
		}
		this.obDay8(allWorkDayChecked);
	};

	// Update the layout of calendar after clicking a cell of it.
	TripHistoryViewModel.prototype.dayCheckChanged = function()
	{
		if (this.$checkbox)
		{
			this.pageLevelViewModel.saveValidate(this.$checkbox);
		}

		this._updateWorkDayCheckbox();
		this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
	};

	TripHistoryViewModel.prototype.getFormatScheduledTime = function(stopTime)
	{
		return stopTime ? moment(stopTime).format('LT') : "Unplanned";
	};

	// Get the start date of current attendance.
	TripHistoryViewModel.prototype.getStartDate = function()
	{
		var start;
		start = new Date(this.obCustomStartDate());
		return start;
	};

	TripHistoryViewModel.prototype.editTripStopTimeClick = function(event)
	{
		event.preventDefault();
		event.stopPropagation();
		var stop = ko.dataFor($(event.currentTarget).closest("li")[0]);
		this.createAdjustAttendanceTripStopTimeModal(stop);
	};

	// Invoke when custom start date and end date changed.
	TripHistoryViewModel.prototype.onCustomDateChanged = function()
	{
		if (!this.obCustomStartDate() || !this.obCustomEndDate())
		{
			this.obEndLessThanStart(false);
			return;
		}
		if (new Date(moment(this.obCustomStartDate()).format("L")) > new Date(moment(this.obCustomEndDate()).format("L")))
		{
			this.obEndLessThanStart(true);
			return;
		}

		this.obEndLessThanStart(false);
		this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
		this.updateWeekDayForCustomView();
		this.obAvailableDates(this._calculateAvailableDates());
		this.resetAttendanceData();
	};

	// If it is custom view, should update the week day check status for attendance.
	TripHistoryViewModel.prototype.updateWeekDayForCustomView = function()
	{
		var self = this;
		var startDate = this.getStartDate(),
			dayCount = this.getDayCount();
		if (dayCount >= 7)
		{
			resetAllWeekDayDisable(false);
			resetAllWeekDay(true);
			this.setWeekDayDisableByTrip();
			return;
		}

		var weekdays = [];
		resetAllWeekDayDisable(true);
		resetAllWeekDay(false);
		for (var i = 0; i < dayCount; i++)
		{
			var currentDay = moment(startDate).add(i, 'day'), weekDay = currentDay.toDate().getDay();
			this["obDay" + (weekDay === 0 ? 7 : weekDay) + "Disable"](false);
			this["obDay" + (weekDay === 0 ? 7 : weekDay)](true);
			weekdays.push(weekDay);
		}
		this.setWeekDayDisableByTrip();
		this.obDay8Disable(!(weekdays.indexOf(1) >= 0 && weekdays.indexOf(2) >= 0 && weekdays.indexOf(3) >= 0 && weekdays.indexOf(4) >= 0 && weekdays.indexOf(5) >= 0));
		this.obDay8(!this.obDay8Disable());

		function resetAllWeekDayDisable(flag)
		{
			for (var i = 1; i <= 8; i++)
			{
				self["obDay" + i + "Disable"](flag);
			}
		}
		function resetAllWeekDay(flag)
		{
			for (var i = 1; i <= 8; i++)
			{
				self["obDay" + i](flag);
			}
		}
	};

	TripHistoryViewModel.prototype.setWeekDayDisableByTrip = function()
	{
		if (!this.trip)
		{
			return;
		}
		this.obDay1Disable(this.obDay1Disable() || !this.trip.Monday);
		this.obDay2Disable(this.obDay2Disable() || !this.trip.Tuesday);
		this.obDay3Disable(this.obDay3Disable() || !this.trip.Wednesday);
		this.obDay4Disable(this.obDay4Disable() || !this.trip.Thursday);
		this.obDay5Disable(this.obDay5Disable() || !this.trip.Friday);
		this.obDay6Disable(this.obDay6Disable() || !this.trip.Saturday);
		this.obDay7Disable(this.obDay7Disable() || !this.trip.Sunday);
		for (var i = 1; i <= 7; i++)
		{
			if (this["obDay" + i + "Disable"]())
			{
				this["obDay" + i](false);
			}
		}
		this.obDay8Disable(this.obDay1Disable() || this.obDay2Disable() || this.obDay3Disable() || this.obDay4Disable() || this.obDay5Disable());
	};

	TripHistoryViewModel.prototype.startShowHideStudentClick = function(viewModel, event)
	{
		this._startEvent = event;
		return true;
	};

	TripHistoryViewModel.prototype.showHideStudentClick = function(viewModel, event)
	{
		if (this._startEvent == null
			|| $(event.currentTarget).parent().is('.ui-draggable-dragging')
			|| event.target.nodeName.toLowerCase() == "input"
			|| $(event.target).hasClass("icon")
			|| $(event.target).hasClass("button"))
		{
			return true;
		}

		if (Math.abs(this._startEvent.clientX - event.clientX) > 2 || Math.abs(this._startEvent.clientY - event.clientY) > 2)
		{
			return true;
		}
		var studentContainer = $(event.currentTarget).next();
		studentContainer.css("display") == "none" ? studentContainer.slideDown("fast") : studentContainer.slideUp("fast");
		$(event.currentTarget).find("input").blur();
		return true;
	};

	function roundDecimal(number)
	{
		return measurementUnitConverter(Math.round(number * 100) / 100);
	}

	function measurementUnitConverter(value, convertBack)
	{
		if (tf.measurementUnitConverter.isImperial())
		{
			const currentUnitOfMeasure = tf.measurementUnitConverter.getCurrentUnitOfMeasure();
			if (convertBack)
			{
				return tf.measurementUnitConverter.convert({
					originalUnit: currentUnitOfMeasure,
					targetUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
					value: value
				});
			}
			return tf.measurementUnitConverter.convert({
				originalUnit: tf.measurementUnitConverter.MeasurementUnitEnum.Metric,
				targetUnit: currentUnitOfMeasure,
				value: value
			});
		}
		return value;
	}

	// If the modal is in add mode, set the obEntityDataModel.
	TripHistoryViewModel.prototype.setNewEntityInAddMode = function(data)
	{
		var trip = data.Items[0];
		this.tripSchools = trip.Schools;
		trip.TripStops = trip.TripStops || [];
		this.obEntityDataModel().totalDistance(roundDecimal(trip.Distance));
		this.obEntityDataModel().tripDuration(convertToMoment(data.Items[0].FinishTime).diff(convertToMoment(data.Items[0].StartTime), 'minutes'));
		this.obEntityDataModel().totalDeadheadDistance(roundDecimal(trip.Dhdistance));
		this.obEntityDataModel().numberOfStops(trip.TripStops.length);
		this.tripDateRanges = trip.TripDateRanges;
		this.trip = trip;

		var tripVehicle = Enumerable.From(this.obVehicleSource()).Where(function(c)
		{
			return c.Id === trip.VehicleId;
		}).ToArray()[0];
		this.obSelectedVehicle(tripVehicle);
		if (tripVehicle)
		{
			this.obSelectedVehicleText(tripVehicle.BusNum);
		}

		var tripDriver = Enumerable.From(this.obDriverSource()).Where(function(c)
		{
			return c.Id === trip.DriverId;
		}).ToArray()[0];
		this.obSelectedDriver(tripDriver);
		if (tripDriver)
		{
			this.obSelectedDriverText(this.staffNameFormatter(tripDriver));
		}

		var tripAide = Enumerable.From(this.obAideSource()).Where(function(c)
		{
			return c.Id === trip.AideId;
		}.bind(this)).ToArray()[0];
		this.obSelectedAide(tripAide);
		if (tripAide)
		{
			this.obSelectedAideText(this.staffNameFormatter(tripAide));
		}

		var tripRoute = Enumerable.From(this.obRouteSource()).Where(function(c)
		{
			return c.Id === trip.RouteId;
		}).ToArray()[0];
		this.obSelectedRoute(tripRoute);
		if (tripRoute)
		{
			this.obSelectedRouteText(tripRoute.Name);
		}

		trip.TripStops = Enumerable.From(trip.TripStops).OrderBy("$.Sequence").ToArray();
		// add all the students to school stop.
		var totalStudentsAssigned = _.uniqBy(trip.TripStops.reduce((students, stop) => students.concat(stop.PickUpStudents), []), "Id").length;
		$.each(trip.TripStops, function(index, item)
		{
			item.StopTime = moment(item.StopTime, "HH:mm:ss").toDate();
			item.OrigStopTime = item.StopTime;
			item.OrigSequence = item.Sequence;
		});
		// update the students count and stops count textbox.
		this.obEntityDataModel().totalStudentsAssigned(totalStudentsAssigned);
		// 0 is To school, 1 is From school.
		this.obEntityDataModel().session(trip.Session);
		var lastSchoolStop = Enumerable.From(trip.TripStops).LastOrDefault(null, x => x.SchlCode);
		trip.TripStops.forEach((stop) =>
		{
			stop.AttendanceType = this.stopAttendanceType(trip.Session, stop, lastSchoolStop);
			(stop.PickUpStudents || []).forEach(function(student)
			{
				student.AttendanceInfo = student.AttendanceInfo || {};
				student.AttendanceInfo.AttendanceType = 0;
			});

			(stop.DropOffStudents || []).forEach(function(student)
			{
				student.AttendanceInfo = student.AttendanceInfo || {};
				student.AttendanceInfo.AttendanceType = 1;
			});

			stop.StudentsAssigned = _.uniqBy(
				stop.PickUpStudents.filter(student => this.whetherStudentInSpecifiedDateRange(student)).concat(
					stop.DropOffStudents.filter(student => this.whetherStudentInSpecifiedDateRange(student)))
				, "Id");
		});

		var tripHistoryEntity = new TF.DataModel.TripHistoryDataModel($.extend({}, this.obEntityDataModel().toData(), { TripStops: Enumerable.From(trip.TripStops).OrderBy("$.Sequence").ToArray() }));
		this.obEntityDataModel(tripHistoryEntity);
		this.originalTripHistoryData = $.extend({}, tripHistoryEntity.toData());
		this.setWeekDayDisableByTrip();
	};

	TripHistoryViewModel.prototype.stopAttendanceType = function(session, stop, lastSchoolStop)
	{
		let attendanceType = 0;
		const onlyPickUp = stop.PickUpStudents.length > 0 && stop.DropOffStudents.length === 0;
		const onlyDropOff = stop.PickUpStudents.length === 0 && stop.DropOffStudents.length > 0;
		const isSchoolStop = !!stop.SchlCode;
		switch (session)
		{
			case TF.Helper.TripHelper.Sessions.Shuttle:
				attendanceType = stop == lastSchoolStop ? 1 : 0;
				break;
			case TF.Helper.TripHelper.Sessions.FromSchool:
				attendanceType = isSchoolStop && !onlyDropOff ? 0 : 1;
				break;
			case TF.Helper.TripHelper.Sessions.ToSchool:
				attendanceType = isSchoolStop && !onlyPickUp ? 1 : 0;
				break;
			case TF.Helper.TripHelper.Sessions.Both:
				attendanceType = onlyDropOff ? 1 : 0;
				break;
		}

		return attendanceType;
	}

	TripHistoryViewModel.prototype.init = function(viewModel, el)
	{
		const self = this;
		this.element = el;
		this.$form = $(el);

		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles"))
			.then(function(data)
			{
				this.obVehicleSource(data.Items);
				if (this.obEntityDataModel().id() > 0)
				{
					var vehicleDataModel = Enumerable.From(this.obVehicleSource()).Where(function(c)
					{
						return c.Id == this.obEntityDataModel().vehicleId();
					}.bind(this)).ToArray()[0];
					this.obSelectedVehicle(vehicleDataModel);
					if (vehicleDataModel)
					{
						this.obSelectedVehicleText(vehicleDataModel.BusNum);
					}
				}
			}.bind(this));

		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "staff?staffTypeId=2"))
			.then(function(data)
			{
				this.obDriverSource(data.Items);
				if (this.obEntityDataModel().id() > 0)
				{
					var driverDataModel = Enumerable.From(this.obDriverSource()).Where(function(c)
					{
						return c.Id === this.obEntityDataModel().driverId();
					}.bind(this)).ToArray()[0];
					this.obSelectedDriver(driverDataModel);
					if (driverDataModel)
					{
						this.obSelectedDriverText(this.staffNameFormatter(driverDataModel));
					}
				}
			}.bind(this));

		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "staff?staffTypeId=1"))
			.then(function(data)
			{
				this.obAideSource(data.Items);
				if (this.obEntityDataModel().id() > 0)
				{
					var aideDataModel = Enumerable.From(this.obAideSource()).Where(function(c)
					{
						return c.Id === this.obEntityDataModel().aideId();
					}.bind(this)).ToArray()[0];
					this.obSelectedAide(aideDataModel);
					if (aideDataModel)
					{
						this.obSelectedAideText(this.staffNameFormatter(aideDataModel));
					}
				}
			}.bind(this));

		var p3 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "routes?@fields=Id,Name&@sort=Name"))
			.then(function(data)
			{
				this.obRouteSource(data.Items);
				if (this.obEntityDataModel().id() > 0)
				{
					var routeDataModel = Enumerable.From(this.obRouteSource()).Where(function(c)
					{
						return c.Id == this.obEntityDataModel().routeID();
					}.bind(this)).ToArray()[0];
					this.obSelectedRoute(routeDataModel);
					if (routeDataModel)
					{
						this.obSelectedRouteText(routeDataModel.Name);
					}
				}
			}.bind(this));

		setTimeout(function()
		{
			this.initValidation();
		}.bind(this), 0);

		return Promise.all([p0, p1, p2, p3]).then(() =>
		{
			if (this.obEntityDataModel().id() <= 0)
			{
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "trips?@filter=in(id," + this.tripId + ")&@relationships=TripStop,School,Student,Attendance"))
					.then(function(data)
					{
						data.Items[0].TripStops.forEach(stop =>
						{
							self._flatStudentSchedule(stop);
						});
						this.setNewEntityInAddMode(data);
					}.bind(this));
			}
			else if (this.obEntityDataModel().id() > 0)
			{
				return this.setNewEntityInEditMode();
			}
		}).then(() =>
		{
			this.dragDropAttendanceGridRow();
			this.obEntityDataModel().apiIsDirty(false);
			this.originalStatus = this.getCurrentStatus();
			this.obEntityDataModel()._entityBackup = JSON.parse(JSON.stringify(this.obEntityDataModel().toData()));
			this.bindEvents();
		});
	};

	TripHistoryViewModel.prototype.bindEvents = function()
	{
		var $attendance = $(this.element).find(".attendance-container");
		$attendance.delegate(".icon.stop-delete", "click", this.deleteClick.bind(this));
		$attendance.delegate(".icon.student-delete", "click", this.deleteClick.bind(this));
		$attendance.delegate(".icon.assign", "click", this.editStudentStopAssignmentClick.bind(this));
		$attendance.delegate(".add-new-stop", "click", this.addNewStopClick.bind(this));
		$attendance.delegate(".arrival-time-span", "click", this.editTripStopTimeClick.bind(this));
		// $attendance.delegate(".trip-stop>div", "mousedown", this.startShowHideStudentClick);
	};

	TripHistoryViewModel.prototype.initValidation = function()
	{
		this.$form = $(this.element);
		this.$checkbox = this.$form.find("#scheduleDay8Flag");
		this.$endDate = this.$form.find("input[name=end]");

		var self = this;

		this.validationFields.start = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				},
				callback:
				{
					message: 'Start Date must be <= End Date',
					callback: function()
					{
						return !this.obEndLessThanStart();
					}.bind(this)
				}
			}
		};
		this.validationFields.end = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				},
				callback:
				{
					message: 'End Date must be >= Start Date',
					callback: function()
					{
						return !this.obEndLessThanStart();
					}.bind(this)
				}
			}
		};

		this.$form.bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: this.validationFields
		}).on('success.field.bv', function(e, data)
		{
			data.element.closest('.form-group').removeClass('has-success');

			if (data.field === 'start' && !data.bv.isValidField('end'))
			{
				// We need to revalidate the end date
				data.bv.revalidateField('end');
			}

			if (data.field === 'end' && !data.bv.isValidField('start'))
			{
				// We need to revalidate the start date
				data.bv.revalidateField('start');
			}

			self.pageLevelViewModel.saveValidate(data.element);
		});

		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	};

	// If the modal is in add mode, create a new obEntityDataModel.
	TripHistoryViewModel.prototype.setNewEntityInEditMode = function()
	{
		var self = this;
		var tripHistoryId = this.obEntityDataModel().id();
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint('triphistory')), {
			paramData: {
				id: tripHistoryId,
				"@relationships": "TripStopHistory"
			}
		}).then(function(data)
		{
			if (data.Items && data.Items.length > 0)
			{
				var tripHistory = data.Items[0];
				self.originalTripHistoryData = $.extend({}, tripHistory);
				var attendanceDate = moment(tripHistory.AttendanceDate).format("L");
				self.obCustomStartDate(attendanceDate);
				self.obCustomEndDate(attendanceDate);
				var stops = Enumerable.From(tripHistory.TripStopHistories).OrderBy("$.Sequence").ToArray();
				stops.map(s =>
				{
					if (s.School)
					{
						if (self.tripSchools != '')
						{
							self.tripSchools = `${self.tripSchools},${s.School}`;
						}
						else
						{
							self.tripSchools = s.School;
						}
					}
				});

				$.each(stops, function(index, stop)
				{
					// edit mode is for only one day.
					stop.AttendanceInfo = {};
					stop.Street = stop.StopName;
					stop.Id = stop.TripStopId;
					const plannedStopTime = stop.PlannedStopTime ? convertToMoment(stop.PlannedStopTime).add(tf.timezonetotalminutes, 'm').toDate() : null;
					const actualStopTime = stop.ActualStopTime;
					stop.StopTime = plannedStopTime;
					stop.AttendanceInfo.StopTime = plannedStopTime;
					stop.AttendanceInfo.OrigStopTime = plannedStopTime;
					stop.AttendanceInfo.ActualStopTime = actualStopTime;
					stop.AttendanceInfo.Value = stop.AttendanceType == 0 ? stop.ActualAttendance : 0;
					stop.AttendanceInfo.AltValue = stop.AttendanceType == 1 ? stop.ActualAttendance : 0;
					stop.AttendanceInfo.StopXCoord = stop.XCoord;
					stop.AttendanceInfo.StopYCoord = stop.YCoord;
					stop.Xcoord = stop.XCoord;
					stop.Ycoord = stop.YCoord;
					stop.SchlCode = stop.School;
					stop.OrigSequence = stop.PlannedSequence;
					stop.Sequence = stop.ActualSequence;
					var regularStudents = [];
					$.each(stop.Attendances, function(index, student)
					{
						student.AttendanceInfo = {};
						student.Id = student.StudID;
						student.SchoolCode = student.School;
						student.AttendanceInfo.AttendanceType = student.AttendanceType;
						student.AttendanceInfo.On = student.AttendanceType === 0 && student.AttendanceStatus === 1;
						student.AttendanceInfo.Off = student.AttendanceType === 1 && student.AttendanceStatus === 1;

						if (!student.Status)
						{
							regularStudents.push(student);
						}
					});

					if (stop.AttendanceType == 0)
					{
						stop.AttendanceInfo.AltValue = Enumerable.From(stop.Attendances).Count(x => x.AttendanceInfo.Off);
					} else
					{
						stop.AttendanceInfo.Value = Enumerable.From(stop.Attendances).Count(x => x.AttendanceInfo.On);
					}

					if ((tripHistory.Session === 0 && stop.School) || (tripHistory.Session === 1 && !stop.School))
					{
						stop.DropOffStudents = regularStudents;
						stop.PickUpStudents = [];
					}
					else
					{
						stop.PickUpStudents = regularStudents;
						stop.DropOffStudents = [];
					}
					stop.StudentsAssigned = _.uniqBy(stop.PickUpStudents.concat(stop.DropOffStudents), "Id");
				});
				tripHistory.TripStops = stops;
				tripHistory.TotalDeadheadDistance = measurementUnitConverter(tripHistory.TotalDeadheadDistance);
				tripHistory.TotalDistance = measurementUnitConverter(tripHistory.TotalDistance);
				self.obEntityDataModel(new TF.DataModel.TripHistoryDataModel(tripHistory));
				self.calcTotalAttendanceValue(true);
				// vehicle
				var tripVehicle = Enumerable.From(self.obVehicleSource()).FirstOrDefault(null, function(c)
				{
					return c.Id == self.obEntityDataModel().vehicleId();
				});
				self.obSelectedVehicle(tripVehicle);
				if (tripVehicle)
				{
					self.obSelectedVehicleText(tripVehicle.BusNum);
				}

				// driver
				var tripDriver = Enumerable.From(self.obDriverSource()).FirstOrDefault(null, function(c)
				{
					return c.Id == self.obEntityDataModel().driverId();
				});
				self.obSelectedDriver(tripDriver);
				if (tripDriver)
				{
					self.obSelectedDriverText(self.staffNameFormatter(tripDriver));
				}

				// aide
				var tripAide = Enumerable.From(self.obAideSource()).Where(function(c)
				{
					return c.Id === self.obEntityDataModel().aideId();
				}).ToArray()[0];
				self.obSelectedAide(tripAide);
				if (tripAide)
				{
					self.obSelectedAideText(self.staffNameFormatter(tripAide));
				}

				// route
				var tripRoute = Enumerable.From(self.obRouteSource()).FirstOrDefault(null, function(c)
				{
					return c.Id == self.obEntityDataModel().routeID();
				});
				self.obSelectedRoute(tripRoute);
				if (tripRoute)
				{
					self.obSelectedRouteText(tripRoute.Name);
				}
			}
		});
	};

	TripHistoryViewModel.prototype.studentsInStop = function(stop)
	{
		return [{ students: stop.pickUpStudents, type: 'pickup' }, { students: stop.dropOffStudents, type: 'dropoff' }];
	};

	TripHistoryViewModel.prototype.checkDataSequence = function()
	{
		var self = this;
		var tripStops = self.obEntityDataModel().tripStops();

		for (var i = 0; i < tripStops.length - 1; i++)
		{
			if (moment(tripStops[i].attendanceInfo().stopTime()).second(0).year(0).month(0).date(1) > moment(tripStops[i + 1].attendanceInfo().stopTime()).second(0).year(0).month(0).date(1))
			{
				tf.promiseBootbox.alert("The attendance record contains invalid or out of order times. Please correct before saving.", 'Stop Sequence/Time Validation Failed');
				return false;
			}
		}
		return true;
	};

	TripHistoryViewModel.prototype.checkDataRangeOverlap = function()
	{
		var self = this;
		var start = moment(this.obCustomStartDate());
		var end = moment(this.obCustomEndDate());
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "triphistories"), {
			paramData: {
				"@filter": "ne(Id," + this.obEntityDataModel().id() + ")&eq(TripId," + this.tripId + ")&ge(AttendanceDate," + toISOStringWithoutTimeZone(start) + ")&le(AttendanceDate," + toISOStringWithoutTimeZone(end) + ")",
				"@fields": "Id,AttendanceDate"
			}
		}).then(function(data)
		{
			for (var i = 0; i < data.Items.length; i++)
			{
				var day = moment(data.Items[i].AttendanceDate);
				var dayDisplay = day.format("L");
				if (!self.days[day.startOf('day').toDate().getDay()]())
				{
					continue;
				}
				tf.promiseBootbox.alert("Trip Calendar already exists for " + dayDisplay + ", please choose another date.", 'Warning');
				return false;
			}
			return true;
		});
	};

	// Save the calendar event modal.
	TripHistoryViewModel.prototype.apply = function()
	{
		tf.loadingIndicator.showImmediately();
		return this.save().then((ans) =>
		{
			tf.loadingIndicator.tryHide();
			return ans;
		});
	};

	TripHistoryViewModel.prototype.save = async function()
	{
		function applySchoolCalendar()
		{
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "DateRangesOfSchoolCalendar"), {
				paramData: {
					startDate: moment(self.obCustomStartDate()).locale("en-us").format("MM/DD/YYYY"),
					endDate: moment(self.obCustomEndDate()).locale("en-us").format("MM/DD/YYYY"),
					dayOfWeeks: this.obDay7() + ',' + this.obDay1() + ',' + this.obDay2() + ',' + this.obDay3() + ',' + this.obDay4() + ',' + this.obDay5() + ',' + this.obDay6(),
					schools: this.tripSchools.replace(',', '!')
				}
			});
		}

		function verifyDate(date, validDateRanges)
		{
			for (var i = 0; i < validDateRanges.length; i++)
			{
				if (validDateRanges[i].InSession && date >= new Date(validDateRanges[i].StartDate) && date <= new Date(validDateRanges[i].EndDate))
				{
					return true;
				}
			}
			return false;
		}

		var self = this;
		var result = await this.pageLevelViewModel.saveValidate();
		if (!result) return Promise.resolve(false);

		result = await this.checkDataRangeOverlap();
		if (!result) return Promise.resolve(false);

		result = this.checkDataSequence();
		if (!result) return Promise.resolve(false);

		let validDateRanges;
		if (this.obApplySchoolCalendar())
		{
			const data = await applySchoolCalendar.call(this);
			validDateRanges = data.Items;
			let hasValidDate = false;
			for (var i = 0; i < validDateRanges.length; i++)
			{
				if (validDateRanges[i].InSession)
				{
					hasValidDate = true;
					break;
				}
			}

			if (!hasValidDate)
			{
				tf.promiseBootbox.alert("There is no valid school calendar, please choose another date.", 'Warning');
				return Promise.resolve(false);
			}
		}

		var dayCount = self.getDayCount(),
			tripHistories = [],
			firstDay = self.getStartDate();
		if (self.obEntityDataModel().id() > 0)
		{
			self.obEntityDataModel().apiIsNew(true);
			self.obEntityDataModel().apiIsDirty(false);
		}
		// set some properties of trip history for each day.
		for (var i = 0; i < dayCount; i++)
		{
			const currentDate = moment(firstDay).add(i, 'day').startOf('day').toDate();
			if (!self.days[currentDate.getDay()]() ||
				(self.obApplySchoolCalendar() && !verifyDate(currentDate, validDateRanges)))
			{
				continue;
			}

			if (!self.obIsEdit())
			{
				const isDateValid = TF.Helper.TripHelper.isDateValidInTripDateRanges(self.tripDateRanges, currentDate);

				if (!isDateValid)
				{
					continue;
				}
			}

			// update AttendanceTakenDate
			self.setAttendanceTakenDateOnEditTrip();

			var tripHistory = self.obEntityDataModel().toData();
			tripHistory.AttendanceDate = toISOStringWithoutTimeZone(moment(currentDate));
			tripHistory.CreateDate = new Date();
			tripHistory.TripId = self.tripId;
			tripHistory.AideId = tripHistory.AideId == 0 ? null : tripHistory.AideId;
			tripHistory.DriverId = tripHistory.DriverId == 0 ? null : tripHistory.DriverId;
			tripHistory.VehicleId = tripHistory.VehicleId == 0 ? null : tripHistory.VehicleId;
			tripHistory.RouteID = tripHistory.RouteID == 0 ? null : tripHistory.RouteID;
			tripHistory.TripStopHistories = self.getAttendanceData(currentDate, 0);
			!tripHistory.Id && (self._removeUnAvailableExceptionStudent(tripHistory, moment(currentDate)));
			tripHistory.AttendanceRecordSource = 1;
			tripHistory.AttendanceTakenBy = tf.userEntity.UserID;
			tripHistory.TotalDeadheadDistance = measurementUnitConverter(tripHistory.TotalDeadheadDistance, true);
			tripHistory.TotalDistance = measurementUnitConverter(tripHistory.TotalDistance, true);
			tripHistory.ActualNumberOfStops = self.getActualStopNumber(tripHistory);
			delete tripHistory.TripStops; // reduce request payload
			tripHistories.push(tripHistory);
		}

		if (self.obApplySchoolCalendar() && tripHistories.length === 0)
		{
			tf.promiseBootbox.alert("There is no valid school calendar, please choose another date.", 'Warning');
			return Promise.resolve(false);
		}

		if (tripHistories.length === 0)
		{
			tf.promiseBootbox.alert("There is no valid date in trip date range, please choose another date.", 'Warning');
			return Promise.resolve(false);
		}

		// save trip history first.
		const p1 = tf.promiseAjax[self.obIsEdit() ? "put" : "post"](pathCombine(tf.api.apiPrefix(), "triphistories"), {
			data: tripHistories,
			paramData: {
				"@relationships": "TripStopHistory"
			}
		});

		const studentSchedules = self.getStudentSchedules();
		const p2 = studentSchedules.length > 0 ? tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "StudentSchedules"),
			{
				data: studentSchedules,
				paramData: {
					"@relationships": "ScheduleDays",
				}
			}) : Promise.resolve(true);
		return Promise.all([p1, p2]);
	};

	TripHistoryViewModel.prototype.getActualStopNumber = function(tripHistory)
	{
		let histories = tripHistory?.TripStopHistories;
		if (histories && histories.length > 0)
		{
			let tripStopWithAttendance = histories.filter(t =>
			{
				let isActualStop = t.ActualAttendance > 0;
				if (!isActualStop && t.Attendances?.length > 0)
				{
					isActualStop = t.Attendances.filter(a => a.Attendance > 0).length > 0;
				}
				return isActualStop;
			});
			return tripStopWithAttendance.length;
		}

		return null;
	}

	TripHistoryViewModel.prototype.getStudentSchedules = function()
	{
		let pickUp = [], dropOff = [], studentSchedules = [], tripHistory = this.obEntityDataModel();
		tripHistory.tripStops().forEach(tripStop =>
		{
			if (tripStop.pickUpStudents().length > 0)
			{
				const exceptionStudents = tripStop.pickUpStudents().filter(x => x.newException());
				pickUp = pickUp.concat(exceptionStudents.map(student =>
				{
					return {
						Id: student.id(),
						PUStopId: tripStop.id(),
					}
				}));
			}
			if (tripStop.dropOffStudents().length > 0)
			{
				const exceptionStudents = tripStop.dropOffStudents().filter(x => x.newException());
				dropOff = dropOff.concat(exceptionStudents.map(student =>
				{
					return {
						Id: student.id(),
						DOStopId: tripStop.id(),
					}
				}));
			}
		})

		pickUp.forEach(student =>
		{
			const dropOffStopId = Enumerable.From(dropOff).FirstOrDefault(null, c => c.Id === student.Id)?.DOStopId;
			if (!dropOffStopId)
			{
				return;
			}

			if (pickUp.filter(x => student.Id === x.Id).length > 1)
			{
				return;
			}

			studentSchedules.push({
				Id: 0,
				Sequence: 1,
				StudentID: student.Id,
				DBID: tf.datasourceManager.databaseId,
				TripId: this.tripId,
				PUStopId: student.PUStopId,
				DOStopId: dropOffStopId,
				CrossStatus: 0,
				SessionId: tripHistory.session(),
				StudentScheduleDays: [{
					Sunday: this.obDay7(),
					Monday: this.obDay1(),
					Tuesday: this.obDay2(),
					Wednesday: this.obDay3(),
					Thursday: this.obDay4(),
					Friday: this.obDay5(),
					Saturday: this.obDay6(),
					StartDate: toISOStringWithoutTimeZone(moment(this.obCustomStartDate())),
					EndDate: toISOStringWithoutTimeZone(moment(this.obCustomEndDate()))
				}]
			});
		})

		return studentSchedules;
	}

	// Close the calendar event modal.
	TripHistoryViewModel.prototype.close = function()
	{
		return new Promise(function(resolve, reject)
		{
			var currentEntity = this.obEntityDataModel().toData();
			// compare two object, _entityBackup's apiisdirty is false, so set this as well.
			currentEntity.APIIsDirty = false;
			var currentStatus = this.getCurrentStatus();
			if (JSON.stringify(this.obEntityDataModel()._entityBackup) !== JSON.stringify(currentEntity) || JSON.stringify(this.originalStatus) !== JSON.stringify(currentStatus))
			{
				resolve(tf.promiseBootbox.yesNo("You have unsaved changes.  Would you like to save your changes prior to canceling?", "Unsaved Changes"));
			} else
			{
				resolve(false);
			}
		}.bind(this));
	};

	// Get other status of modal, except obEntityDataModel properties.
	TripHistoryViewModel.prototype.getCurrentStatus = function()
	{
		var status = {};
		status.dayColumns = $.extend(true, {}, this.obDayColumns());
		// status.calendarView = this.calendarView;
		status.customStart = moment(this.obCustomStartDate()).format("L");
		status.customEnd = moment(this.obCustomEndDate()).format("L");
		for (var i = 1; i <= 8; i++)
		{
			status["obDay" + i] = this["obDay" + i]();
		}
		return status;
	};

	// Generate attendance data for trip stops and students.
	TripHistoryViewModel.prototype.getAttendanceData = function(date, tripHistoryId)
	{
		const self = this;
		var tripStopHistories = [], stops = this.obEntityDataModel().tripStops();
		var utcNow = moment().utc().format("YYYY-MM-DDTHH:mm:ss.SSS");
		function addStudents(students, stop, stopDateTime)
		{
			$.each(students, (index, student) =>
			{
				var attendanceType = student.attendanceInfo().attendanceType();
				if (student.attendanceInfo().on())
				{
					attendanceType = 0;
				} else if (student.attendanceInfo().off())
				{
					attendanceType = 1;
				}

				if (student.newException() || self.whetherStudentInSpecifiedDateRange(student))
				{
					stop.Attendances.push({
						DBID: tf.datasourceManager.databaseId,
						StudID: student.id(),
						AttendanceType: attendanceType,
						AttendanceStatus: (student.attendanceInfo().on() || student.attendanceInfo().off()) ? 1 : 0,
						Source: student.source() || stop.Source,
						Time: student.time() || null,
						FirstName: student.firstName(),
						LastName: student.lastName(),
						XCoord: student.xCoord() || stop.XCoord,
						YCoord: student.yCoord() || stop.YCoord,
						Address: student.address() || stop.Address,
						CreatedOn: student.createdOn() || utcNow,
						AssetId: student.assetId(),
						Unplanned: student.unplanned(),
						VendorId: student.vendorId(),
						TagId: student.tagId(),
						Time: student.attendanceInfo().Time || null,
						Attendance: student.attendanceInfo().Attendance || 0
					});
				}
			});
		}

		$.each(stops, (index, stop) =>
		{
			this.setActualStopTimeOnEditStop(stop);
			this.setAttendanceTimeOnCheckedStudent(stop);
			var stopTime = moment(stop.attendanceInfo().stopTime());
			var stopHistory = {
				DBID: tf.datasourceManager.databaseId,
				TripHistoryId: tripHistoryId,
				TripStopId: stop.id(),
				StopName: stop.street(),
				ActualAttendance: stop.attendanceType() != 1 ? stop.attendanceInfo().value() : stop.attendanceInfo().altValue(),
				AttendanceType: stop.attendanceType(),
				ActualSequence: stop.sequence(),
				PlannedSequence: stop.origSequence(),
				School: stop.schlCode(),
				ActualStopTime: stop.attendanceInfo().actualStopTime() || null,
				PlannedStopTime: moment(stopTime).add(-tf.timezonetotalminutes, 'm').format("HH:mm:ss"),
				XCoord: stop.xcoord(),
				YCoord: stop.ycoord(),
				Address: stop.street(),
				Source: stop.source() || 1,
				Attendances: []
			};
			stopHistory.PlannedAttendance = stopHistory.ActualAttendance;
			tripStopHistories.push(stopHistory);
			var stopDateTime = toISOStringWithoutTimeZone(moment(date).set({ "h": stopTime.get("h"), "m": stopTime.get("m") }));
			addStudents(stop.pickUpStudents(), stopHistory, stopDateTime);
			addStudents(stop.dropOffStudents(), stopHistory, stopDateTime);
		});

		return tripStopHistories;
	};

	// Get the day count depends on the calendar view.
	TripHistoryViewModel.prototype.getDayCount = function()
	{
		var dateCount;
		var start = new Date(this.obCustomStartDate());
		var end = new Date(this.obCustomEndDate());
		dateCount = moment(end).startOf('day').diff(moment(start).startOf('day'), 'days') + 1;
		return dateCount;
	};

	TripHistoryViewModel.prototype.isStopTimeChanged = function(stop)
	{
		var stopTimeChanged = false;
		if (moment(stop.attendanceInfo().stopTime()).format("hh:mm A") !== moment(stop.attendanceInfo().origStopTime()).format("hh:mm A"))
		{
			stopTimeChanged = true;
		}
		return stopTimeChanged;
	};

	TripHistoryViewModel.prototype.getAlertText = function(stop)
	{
		var newStudents = stop.pickUpStudents().concat(stop.dropOffStudents()),
			oldStop = Enumerable.From(this.obEntityDataModel()._entityBackup.TripStops).Where(function(c) { return c.Id === parseInt(stop.id()); }).FirstOrDefault(),
			oldStudents = oldStop ? oldStop.PickUpStudents.concat(oldStop.DropOffStudents) : [], originalAlerts = stop.modStatus() ? stop.modStatus().split("\n") : [],
			pushAlertText = function(alert)
			{
				if (originalAlerts.indexOf(alert) < 0)
				{
					originalAlerts.push(alert);
				}
			};
		if (newStudents.length > 0 || oldStudents.length > 0)
		{
			$.each(oldStudents, function(oldItemIndex, oldItem)
			{
				var isExist = false;
				$.each(newStudents, function(newItemIndex, newItem)
				{
					if (oldItem.Id === newItem.id())
					{
						isExist = true;
						return false;
					}
				}.bind(this));

				if (!isExist)
				{
					pushAlertText("Student's Trip Stop changed");
					return false;
				}
			}.bind(this));

			$.each(newStudents, function(newItemIndex, newItem)
			{
				var isExist = false;
				$.each(oldStudents, function(oldItemIndex, oldItem)
				{
					if (oldItem.Id === newItem.id())
					{
						isExist = true;
						return false;
					}
				}.bind(this));

				if (!isExist)
				{
					pushAlertText("Student Added to Trip");
					return false;
				}
			}.bind(this));
		}
		if (this.isStopTimeChanged(stop))
		{
			pushAlertText("Trip Stop Time changed");
		}
		if (stop.sequence() !== stop.origSequence()) pushAlertText("Trip Stop Sequence changed");
		return originalAlerts.join("\n");
	};

	TripHistoryViewModel.prototype.createAdjustAttendanceTripStopTimeModal = function(stop)
	{
		var self = this;
		return tf.modalManager.showModal(new TF.Modal.AdjustAttendanceTripStopTimeModalViewModel(stop, self.days, self.getDayCount(), self.getStartDate()))
			.then(function(data)
			{
				if (data)
				{
					stop.stopTime(data);
					stop.attendanceInfo().stopTime(data);
				}
				return data;
			});
	};

	TripHistoryViewModel.prototype.getDragHint = function(cells)
	{
		var hintElement = $('<div class="k-grid list-mover-drag-hint" style="width:500px;opacity: 0.8;overflow: hidden;position: absolute;z-index: 20000;display: block;background-color: rgb(255, 255, 206)"><table><tbody><tr></tr></tbody></table></div>');
		// use first 3 tds for hint element.
		for (var i = 0; i < cells.length; i++)
		{
			var td = $("<td>").append(cells[i]);
			hintElement.find("tr").append(td);
		}
		return hintElement;
	};

	TripHistoryViewModel.prototype.setTripStopTimePopover = function(viewModel, element)
	{
		$(element).Popover(new TF.Popover.AttendanceStopPopoverViewModel(viewModel, this.days, this));
	};

	// Make attendance grid row draggable and droppable.
	TripHistoryViewModel.prototype.dragDropAttendanceGridRow = function()
	{
		this.dragDropStopRow();
		this.dragDropStudentRow();
	};

	TripHistoryViewModel.prototype.dragDropStopRow = function()
	{
		var self = this,
			itemHeight = 58,
			startIndex = 0,
			toIndex = 0,
			tripStopsContainer = $(this.element).find(".trip-stops"),
			items = tripStopsContainer.children(".trip-stop"),
			placeholder, isUpdate;

		items.draggable({
			distance: 8,
			revertDuration: 100,
			handle: ".trip-stop-row",
			start: function(event, ui)
			{
				items = tripStopsContainer.children(".trip-stop");
				startIndex = items.index(ui.helper);
				toIndex = -1;
				tripStopsContainer.find(".students-container").each(function(index, item)
				{
					if ($(item).css("display") != "none")
					{
						$(item).hide();
					}
				});

				placeholder = $("<li class='sortable-placeholder'></li>");
				placeholder.css({
					width: tripStopsContainer.width(),
					top: (startIndex + (startIndex == items.length - 1 ? 0 : 1)) * itemHeight - 1
				});
				tripStopsContainer.append(placeholder);
			},
			drag: function(event, ui)
			{
				var containerTop = tripStopsContainer.offset().top, top;
				var otherItems = tripStopsContainer.children(".trip-stop:not('.ui-draggable-dragging')");
				for (var i = 0; i < otherItems.length; i++)
				{
					var item = $(otherItems[i]), offset = item.offset();
					if (ui.offset.top >= offset.top && ui.offset.top < (offset.top + itemHeight / 2))
					{
						toIndex = i;
						top = offset.top - containerTop - 1;
						break;
					} else if (ui.offset.top >= (offset.top + itemHeight / 2) && ui.offset.top < (offset.top + itemHeight))
					{
						toIndex = i + 1;
						top = offset.top + itemHeight - containerTop - 1;
						break;
					}
				}
				isUpdate = !(toIndex == startIndex || toIndex == -1);
				if (isUpdate)
				{
					items.draggable("option", "revert", false);
					placeholder.css("top", top);
				} else
				{
					placeholder.css("top", (startIndex + (startIndex == items.length - 1 ? 0 : 1)) * itemHeight - 1);
					items.draggable("option", "revert", true);
				}
			},
			stop: function(event, ui)
			{
				placeholder.remove();
				if (isUpdate)
				{
					if (toIndex == items.length - 1)
					{
						ui.helper.insertAfter(items.eq(toIndex));
					} else
					{
						ui.helper.insertBefore(items.not(ui.helper).eq(toIndex));
					}

					ui.helper.css({
						left: 0,
						top: 0
					});

					var $stops = tripStopsContainer.children();
					var dragStop = ko.dataFor(ui.helper[0]);
					var stopSequences = {};
					$stops.each(function(index, item)
					{
						stopSequences[$(item).attr("stopid")] = index + 1;
					});

					$.each(self.obEntityDataModel().tripStops(), function(index, stop)
					{
						stop.sequence(stopSequences[stop.id()]);
					});
					self.pageLevelViewModel.obSuccessMessageDivIsShow(false);
					self.sortStopsBySequence();
					self.createAdjustAttendanceTripStopTimeModal(dragStop);
				}
			}
		});
	};

	TripHistoryViewModel.prototype.dragDropStudentRow = function()
	{
		var self = this,
			indexInStop = 0,
			itemHeight = 35,
			tripStopsContainer = $(this.element).find(".trip-stops"),
			items = tripStopsContainer.find(".student-li.isNormal"),
			placeholder, isUpdate, stop, toStop, student;

		items.draggable({
			distance: 8,
			revertDuration: 100,
			start: function(event, ui)
			{
				var containerTop = tripStopsContainer.offset().top;
				itemHeight = ui.helper.height();
				student = ko.dataFor(ui.helper[0]);
				items = tripStopsContainer.find(".student-li.isNormal");
				indexInStop = ui.helper.parent().children().index(ui.helper);
				toStop = null;
				placeholder = $("<li class='sortable-placeholder'></li>");
				placeholder.css({
					width: tripStopsContainer.width(),
					top: ui.offset.top - containerTop - 1 + itemHeight
				});
				stop = ko.dataFor(ui.helper.closest(".trip-stop")[0]);
				tripStopsContainer.append(placeholder);
			},
			drag: function(event, ui)
			{
				var containerTop = tripStopsContainer.offset().top;
				var list = tripStopsContainer.find(".student-li:not('.ui-draggable-dragging'),.trip-stop-row");
				var top;
				for (var i = 0; i < list.length; i++)
				{
					var item = $(list[i]), offset = item.offset(), height = item.height();
					if (ui.offset.top > offset.top && ui.offset.top < (offset.top + height))
					{
						toStop = ko.dataFor(item.closest(".trip-stop")[0]);
						top = offset.top + height - containerTop - 1;
						break;
					}
				}

				isUpdate = toStop && toStop != stop && !toStop.schlCode();
				if (isUpdate)
				{
					items.draggable("option", "revert", false);
					placeholder.css("top", top);
				} else
				{
					placeholder.css("top", ui.helper.parent().offset().top + (indexInStop + 1) * itemHeight - containerTop - 1);
					items.draggable("option", "revert", true);
				}
			},
			stop: function(event, ui)
			{
				placeholder.remove();
				if (isUpdate)
				{
					var pickUpStudentLength = stop.pickUpStudents().length,
						dropOffStudentLength = stop.dropOffStudents().length;
					stop.pickUpStudents.remove(student);
					stop.dropOffStudents.remove(student);
					if (pickUpStudentLength != stop.pickUpStudents().length)
					{
						toStop.pickUpStudents.push(student);
					} else if (dropOffStudentLength != stop.dropOffStudents().length)
					{
						toStop.dropOffStudents.push(student);
					}
					self.resetStopValue(toStop);
					self.resetStopValue(stop);
					self.dragDropStudentRow();
				}
			}
		});
	};

	TripHistoryViewModel.prototype.resetStopTime = function(stop, time)
	{
		stop.stopTime(time);
		stop.attendanceInfo().stopTime(time);
		stop.attendanceInfo().origStopTime(time);
	};

	// Sort the obEntityDataModel.tripStops by sequence. if "resetSequence" is true, also reset the Sequence property, based on 1.
	TripHistoryViewModel.prototype.sortStopsBySequence = function(resetSequence)
	{
		this.obEntityDataModel().tripStops.sort(function(up, down)
		{
			return up.sequence() == down.sequence() ? 0 : (up.sequence() > down.sequence() ? 1 : -1);
		});

		if (resetSequence)
		{
			var sequence = 1;
			$.each(this.obEntityDataModel().tripStops(), function(index, stop)
			{
				stop.sequence(sequence);
				sequence++;
			});
		}
	};

	TripHistoryViewModel.prototype.staffNameFormatter = function(staff)
	{
		const flag = !!staff.LastName && !!staff.FirstName;
		return `${staff.LastName || ""}${flag ? ", " : ""}${staff.FirstName || ""}`;
	};

	// Copy and new calendar event.
	TripHistoryViewModel.prototype.createNewTripHistory = function()
	{
		this.obEntityDataModel().id(0);

		var start = this.obCustomStartDate();
		var end = this.obCustomEndDate();
		var dayCount = this.getDayCount();
		var newStart = moment(start).add(dayCount, 'days');
		var newEnd = moment(end).add(dayCount, 'days');
		this.obCustomStartDate(newStart.format("L"));
		this.obCustomEndDate(newEnd.format("L"));

		this.originalStatus = this.getCurrentStatus();
		this.obEntityDataModel()._entityBackup = JSON.parse(JSON.stringify(this.obEntityDataModel().toData()));
		this.obEntityDataModel().apiIsDirty(false);
		this.pageLevelViewModel.popupSuccessMessage();
	};

	function resetStudentValue(students)
	{
		$.each(students, function(index, student)
		{
			student.attendanceInfo().on(false);
			student.attendanceInfo().off(false);
		});
	}

	TripHistoryViewModel.prototype.totalValueChanged = function(isValue, isChanged, stringBox, event)
	{
		if (!isChanged)
		{
			stringBox.keypress(null, event);
		}

		if (event.keyCode == 13 || isChanged)
		{
			event.stopPropagation();
			if (isChanged)
			{
				this.resetAttendanceData();
			}
			this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
		}

		this.setAttendanceTakenDateOnEditTrip();

		return true;
	};

	TripHistoryViewModel.prototype.studentValueChanged = function(stop, type, student)
	{
		var sameStudent;

		this.resetStopValue(stop);
		// update the same student in another stop.
		$.each(this.obEntityDataModel().tripStops(), function(index, tripStop)
		{
			if (tripStop !== stop)
			{
				var students = tripStop.dropOffStudents().concat(tripStop.pickUpStudents());
				$.each(students, function(index, item)
				{
					if (item.id() === student.id())
					{
						sameStudent = item;
						return false;
					}
				});
				if (sameStudent)
				{
					sameStudent.attendanceInfo().on(student.attendanceInfo().off());
					sameStudent.attendanceInfo().off(student.attendanceInfo().on());
					this.resetStopValue(tripStop);
					return false;
				}
			}
		}.bind(this));
		this.calcTotalAttendanceValue();
		this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
		return true;
	};

	// Calculate the total attendance value by column.
	TripHistoryViewModel.prototype.calcTotalAttendanceValue = function(isInit)
	{
		var value = 0, altValue = 0;
		$.each(this.obEntityDataModel().tripStops(), function(index, stop)
		{
			value += parseInt(stop.attendanceInfo().value());
			altValue += parseInt(stop.attendanceInfo().altValue());
		});
		if (this.obEntityDataModel().session() == 1)
		{
			if (!isInit)
			{
				this.obEntityDataModel().actualAttendance(altValue);
			}
			this.actualAltAttendance(value);
		} else
		{
			if (!isInit)
			{
				this.obEntityDataModel().actualAttendance(value);
			}
			this.actualAltAttendance(altValue);
		}
	};

	TripHistoryViewModel.prototype.stopValueChanged = function(stop, isValue, isChanged, stringBox, event)
	{
		var attendanceItem = stop.attendanceInfo(), oldValue = isValue ? attendanceItem.value() : attendanceItem.altValue(), newValue;

		if (!isChanged)
		{
			stringBox.keypress(null, event);
		}
		if (event.keyCode == 13 || isChanged)
		{
			attendanceItem = stop.attendanceInfo();
			newValue = isValue ? attendanceItem.value() : attendanceItem.altValue();
			if (isNaN(parseInt(newValue)))
			{
				newValue = 0;
				isValue ? attendanceItem.value(0) : attendanceItem.altValue(0);
			}
			if (oldValue !== newValue || isChanged)
			{
				resetStudentValue(stop.pickUpStudents());
				resetStudentValue(stop.dropOffStudents());
			}
			this.calcTotalAttendanceValue();
		}

		// update the actual stop time
		this.setActualStopTimeOnEditStop(stop);

		this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
		return true;
	};

	// Refresh the attendance grid.
	TripHistoryViewModel.prototype.refreshClick = function()
	{
		tf.promiseBootbox.yesNo("Are you sure you would like to reload this Trip? This will revert any changes you have made to the Attendance Grid.", "Revert")
			.then(function(result)
			{
				if (result)
				{
					this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
					var stops = [];
					$.each(this.obEntityDataModel()._entityBackup.TripStops, function(index, item)
					{
						stops.push(new TF.DataModel.AttendanceTripStopDataModel(item));
					});
					this.obEntityDataModel().tripStops(stops);
					this.sortStopsBySequence();
					this.dragDropAttendanceGridRow();
					this.calcTotalAttendanceValue();
					this.obEntityDataModel()._entityBackup.TripStops = JSON.parse(JSON.stringify(this.obEntityDataModel().toData().TripStops));
				}
			}.bind(this));
	};

	TripHistoryViewModel.prototype.resetAttendanceData = function()
	{
		$.each(this.obEntityDataModel().tripStops(), (index, stop) =>
		{
			stop.attendanceInfo().value(0);
			stop.attendanceInfo().altValue(0);
			resetStudentValue(stop.pickUpStudents());
			resetStudentValue(stop.dropOffStudents());
			// recalculate assigned students if custom start date or custom end date changes to filter out potential exception student which date not fit
			if ([...stop.pickUpStudents(), ...stop.dropOffStudents()].length > 0)
			{
				stop.studentsAssigned(_.uniqBy(
					stop.pickUpStudents().filter(student => this.whetherStudentInSpecifiedDateRange(student)).concat(
						stop.dropOffStudents().filter(student => this.whetherStudentInSpecifiedDateRange(student)))
					, student => student.id()));
			}
		});
	};

	TripHistoryViewModel.prototype.resetStopValue = function(stop)
	{
		var value = 0, altValue = 0;
		value += Enumerable.From(stop.pickUpStudents()).Count(x => x.attendanceInfo().on());
		altValue += Enumerable.From(stop.pickUpStudents()).Count(x => x.attendanceInfo().off());
		value += Enumerable.From(stop.dropOffStudents()).Count(x => x.attendanceInfo().on());
		altValue += Enumerable.From(stop.dropOffStudents()).Count(x => x.attendanceInfo().off());
		stop.attendanceInfo().value(value);
		stop.attendanceInfo().altValue(altValue);
	};

	// Delete the selected student or stop.
	TripHistoryViewModel.prototype.deleteClick = function(event)
	{
		var self = this;
		event.stopPropagation();
		var selectedItem = ko.dataFor($(event.currentTarget).closest("li")[0]);
		var isStudent = !selectedItem.hasOwnProperty("sequence"), stop, student,
			resetStudentValue = function(students)
			{
				$.each(students, function(index, student)
				{
					student.attendanceInfo().on(false);
					student.attendanceInfo().off(false);
				});
			};
		tf.promiseBootbox.yesNo("Are you sure you would like to remove this " + (isStudent ? "Student" : "Trip Stop") + "?", "Remove Confirmation")
			.then(function(result)
			{
				if (result)
				{
					if (isStudent)
					{
						student = selectedItem;
						this.removeStudentsFromTrip([student.id()]);
						this.obEntityDataModel().tripStops().forEach(function(stop)
						{
							self.resetStopValue(stop);
						});
					}
					else
					{
						stop = selectedItem;
						this.obEntityDataModel().tripStops.remove(stop);
						$(event.currentTarget).closest("li").remove();
						this.sortStopsBySequence(true);
						// reset the attendance value for stop and students.
						resetStudentValue(stop.pickUpStudents());
						resetStudentValue(stop.dropOffStudents());
						this.dragDropAttendanceGridRow();
					}
					this.calcTotalAttendanceValue();
					this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
				}
			}.bind(this));
	};

	// Remove students from current trip stop
	TripHistoryViewModel.prototype.removeStudentsFromTrip = function(studentsIds)
	{
		const self = this;
		var removeStudents = function(student)
		{
			return $.inArray(student.id(), studentsIds) >= 0;
		};
		$.each(this.obEntityDataModel().tripStops(), function(index, item)
		{
			item.pickUpStudents.remove(removeStudents);
			item.dropOffStudents.remove(removeStudents);
			item.studentsAssigned(item.pickUpStudents().filter(student => self.whetherStudentInSpecifiedDateRange(student))
				.concat(item.dropOffStudents().filter(student => self.whetherStudentInSpecifiedDateRange(student)))
				.reduce((arr, current) => arr.concat(arr.some(x => x.id() === current.id()) ? [] : current), []));
		});
	};

	TripHistoryViewModel.prototype.getAllStudentIds = function()
	{
		var ids = [];
		$.each(this.obEntityDataModel().tripStops(), function(index, stop)
		{
			$.each(stop.pickUpStudents().concat(stop.dropOffStudents()), function(index, student)
			{
				if ($.inArray(student.id(), ids) < 0)
				{
					ids.push(student.id());
				}
			});
		});
		return ids;
	};

	// Edit the students' assignment.
	TripHistoryViewModel.prototype.editStudentStopAssignmentClick = function(event)
	{
		event.stopPropagation();
		var stop = ko.dataFor($(event.currentTarget).closest("li")[0]),
			stopObject = stop.toData(),
			students = stopObject.StudentsAssigned.map(student => student instanceof TF.DataModel.AttendanceStudentDataModel ? student.toData() : student);
		var defaultOption = {
			title: 'Edit Student Assignment [' + this.tripName + "]",
			description: '',
			availableTitle: tf.applicationTerm.getApplicationTermPluralByName("Unassigned Students"),
			selectedTitle: tf.applicationTerm.getApplicationTermPluralByName("Assigned Students"),
			mustSelect: false,
			displayCheckbox: true,
			disableDropIndicator: true,
			filterCheckboxText: "Include Ungeocoded Students",
			gridOptions: {
				forceFitColumns: true,
				enableColumnReorder: false,
				// excludeIds: this.getAllStudentIds(),
				filter: {
					"FieldName": "Geo",
					"Operator": "EqualTo",
					"Value": "4"
				}
			}
		};
		tf.modalManager.showModal(
			new TF.Modal.ListMoverSelectStudentModalViewModel(
				students,
				$.extend({}, defaultOption, {
					type: 'student',
					showRawImageColumn: false
				}), true, true)
		)
			.then(function(selectedStudents)
			{
				if (!selectedStudents) return;
				stop.studentsAssigned(selectedStudents);
				var addedIds = [], removedIds = [];
				this.compareArrays(students, selectedStudents, addedIds, removedIds);
				var schoolStop;
				$.each(this.obEntityDataModel().tripStops(), function(index, item)
				{
					if (item.schlCode())
					{
						schoolStop = item;
					}
				});
				if (removedIds.length > 0)
				{
					this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
					this.removeStudentsFromTrip(removedIds);
				}
				if (addedIds.length > 0)
				{
					this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
					tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("student")), {
						paramData: { "@filter": "in(Id," + addedIds.join(",") + ")" }
					})
						.then(function(data)
						{
							$.each(data.Items, function(index, student)
							{
								var obStudent = new TF.DataModel.AttendanceStudentDataModel(student);
								obStudent.newException(true);
								let alreadyAdded = this.obEntityDataModel().tripStops().filter(x => x.schlCode()).some(x => x.pickUpStudents().some(student => student.id() === obStudent.id()));
								if (this.obEntityDataModel().session() === 0)
								{
									if (stop.schlCode())
									{
										!alreadyAdded ? obStudent.attendanceInfo().attendanceType(0) : obStudent.attendanceInfo().attendanceType(1);
										!alreadyAdded ? stop.pickUpStudents.push(obStudent) : stop.dropOffStudents.push(obStudent);
									}
									else
									{
										obStudent.attendanceInfo().attendanceType(0);
										stop.pickUpStudents.push(obStudent);
										const dropOffStudent = obStudent.clone();
										dropOffStudent.attendanceInfo().attendanceType(1);
										schoolStop.dropOffStudents.push(dropOffStudent);
										schoolStop.studentsAssigned.push(dropOffStudent);
									}
								}
								else
								{
									if (stop.schlCode())
									{
										alreadyAdded ? obStudent.attendanceInfo().attendanceType(1) : obStudent.attendanceInfo().attendanceType(0);
										alreadyAdded ? stop.dropOffStudents.push(obStudent) : stop.pickUpStudents.push(obStudent);
									}
									else
									{
										obStudent.attendanceInfo().attendanceType(1);
										stop.dropOffStudents.push(obStudent);
										if (!alreadyAdded)
										{
											const pickUpStudent = obStudent.clone();
											pickUpStudent.attendanceInfo().attendanceType(0)
											schoolStop.pickUpStudents.push(pickUpStudent);
											schoolStop.studentsAssigned.push(pickUpStudent);
										}
									}
								}
							}.bind(this));
							this.dragDropStudentRow();
						}.bind(this));
				}
			}.bind(this));
	};

	TripHistoryViewModel.prototype.compareArrays = function(oldArray, newArray, addedIds, removedIds)
	{
		removedIds = removedIds || [];
		$.each(oldArray, function(oldItemIndex, oldItem)
		{
			var isExist = false;
			$.each(newArray, function(newItemIndex, newItem)
			{
				if (oldItem.Id === newItem.Id)
				{
					isExist = true;
					return false;
				}
			}.bind(this));

			if (!isExist)
			{
				removedIds.push(oldItem.Id);
			}
		}.bind(this));

		$.each(newArray, function(newItemIndex, newItem)
		{
			var isExist = false;
			$.each(oldArray, function(oldItemIndex, oldItem)
			{
				if (oldItem.Id === newItem.Id)
				{
					isExist = true;
					return false;
				}
			}.bind(this));

			if (!isExist)
			{
				if (addedIds.length === 0 || Enumerable.From(addedIds).Where(function(c) { return c.Id === newItem.Id; }).ToArray().length == 0)
				{
					addedIds.push(newItem.Id);
				}
			}
		}.bind(this));
	};

	// Edit the stops' assignment.
	TripHistoryViewModel.prototype.addNewStopClick = function(event)
	{
		event.stopPropagation();
		var self = this,
			sequence = $(event.currentTarget).attr("sequence"),
			tripObject = this.obEntityDataModel().toData(),
			defaultOption = {
				title: "Add Trip Stop to Calendar Event - [" + this.tripName + "]",
				description: '',
				availableTitle: 'Unassigned Trip Stops',
				selectedTitle: 'Assigned Trip Stops',
				mustSelect: true,
				disableDropIndicator: true,
				gridOptions: {
					forceFitColumns: true,
					enableColumnReorder: true,
					excludeIds: tripObject.TripStops.map(function(stop)
					{
						return stop.Id;
					})
				}
			};

		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), 'search', 'tripstops', "id"), {
			data: {
				filterClause: '',
				filterSet: null,
				idFilter: {
					ExcludeAny: tripObject.TripStops.map(function(stop)
					{
						return stop.Id;
					})
				}
			}
		}).then(response =>
		{
			defaultOption.totalRecordCount = response.TotalRecordCount;
			tf.modalManager.showModal(
				new TF.Modal.ListMoverSelectTripStopModalViewModel(
					[],
					$.extend({}, defaultOption, {
						type: 'tripstop'
					})
				)
			).then(function(data)
			{
				if (!data) return;
				var addedIds = data.map(function(c) { return c.Id; });

				if (addedIds.length > 0)
				{
					self.pageLevelViewModel.obSuccessMessageDivIsShow(false);
					tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), 'tripstops'), {
						paramData: { "@filter": "in(Id," + addedIds.join(",") + ")" }
					}).then(function(data)
					{
						var stopTime = new Date();
						self.obEntityDataModel().tripStops().forEach(function(stop)
						{
							if (stop.sequence() < sequence)
							{
								stopTime = stop.stopTime();
							} else
							{
								stop.sequence(stop.sequence() + data.Items.length);
							}
						});
						var addStops = [];
						data.Items.forEach(function(item)
						{
							item.Sequence = sequence;
							item.OrigSequence = sequence;
							item.StopTime = stopTime;
							item.PickUpStudents = [];
							item.DropOffStudents = [];
							item.StudentsAssigned = [];
							item.AttendanceType = tripObject.Session != 1 ? (item.SchlCode ? 1 : 0) : (item.SchlCode ? 0 : 1);
							var stop = new TF.DataModel.AttendanceTripStopDataModel(item);
							self.obEntityDataModel().tripStops.push(stop);
							addStops.push(stop);
							sequence++;
						});
						self.sortStopsBySequence(true);
						self.dragDropAttendanceGridRow();
						self.createAdjustAttendanceTripStopTimeModal(addStops[0]).then(function(time)
						{
							if (time)
							{
								addStops.forEach(function(stop)
								{
									self.resetStopTime(stop, time);
								});
							}
						});
					});
				}
			});
		});
	};

	TripHistoryViewModel.prototype.checkAllClick = function()
	{
		var studentMapping = {};
		// update the same student in another stop.
		$.each(this.obEntityDataModel().tripStops(), function(index, tripStop)
		{
			var students = tripStop.dropOffStudents().concat(tripStop.pickUpStudents());
			$.each(students, function(index, student)
			{
				if (studentMapping[student.id()])
				{
					student.attendanceInfo().on(false);
					student.attendanceInfo().off(true);
				} else
				{
					student.attendanceInfo().on(true);
					student.attendanceInfo().off(false);
					studentMapping[student.id()] = student;
				}
			});
			this.resetStopValue(tripStop);
		}.bind(this));
		this.calcTotalAttendanceValue();
		this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
	};

	TripHistoryViewModel.prototype.clearAllClick = function()
	{
		// update the same student in another stop.
		$.each(this.obEntityDataModel().tripStops(), function(index, tripStop)
		{
			var students = tripStop.dropOffStudents().concat(tripStop.pickUpStudents());
			$.each(students, function(index, student)
			{
				student.attendanceInfo().on(false);
				student.attendanceInfo().off(false);
			});
			this.resetStopValue(tripStop);
		}.bind(this));
		this.calcTotalAttendanceValue();
		this.pageLevelViewModel.obSuccessMessageDivIsShow(false);
	};

	TripHistoryViewModel.prototype.expandClick = function()
	{
		$(this.element).find(".students-container").show();
	};

	TripHistoryViewModel.prototype.collapseClick = function()
	{
		$(this.element).find(".students-container").hide();
	};

	TripHistoryViewModel.prototype.printClick = function()
	{
		var printSourceDom = $(this.element).find(".attendance-container");
		var dom = $("<div class='calendar-event-modal' style='margin:0 auto;'></div>");
		dom.append(printSourceDom.clone());
		var doc = new TF.PrintDocument();
		doc.setInputStatus(printSourceDom, dom);

		doc.title = "Attendance ( " + moment(this.obCustomStartDate()).format("L") + " - " + moment(this.obCustomEndDate()).format("L") + " )";
		dom.find(".students-container").show();
		dom.css("width", "870px");
		doc.append(dom[0]);
		doc.print();
	};

	TripHistoryViewModel.prototype.setAttendanceTakenDateOnEditTrip = function()
	{
		// for story RW-44286
		// If the Trip level PU or DO counts are manually entered set the TripHistory.AttendanceTakenDate to the current date time
		// If a TripHistory.AttendanceTakenDate is already set, modifying Attendance in Plus will not update it
		let currentActualAttendance = this.obEntityDataModel().actualAttendance();
		let _setAttendanceTakenDate = () => this.obEntityDataModel().attendanceTakenDate(moment().utc().format("YYYY-MM-DD HH:mm:ss"));
		if (this.obIsEdit())
		{
			let originalAttendanceTakenDate = this.originalTripHistoryData && this.originalTripHistoryData.AttendanceTakenDate;
			let originalActualAttendance = this.originalTripHistoryData && this.originalTripHistoryData.ActualAttendance;
			if (!originalAttendanceTakenDate && originalActualAttendance !== currentActualAttendance)
			{
				_setAttendanceTakenDate();
			}
		}
		else
		{
			if (!!currentActualAttendance)
			{
				_setAttendanceTakenDate();
			}
		}
	}

	TripHistoryViewModel.prototype.setActualStopTimeOnEditStop = function(obEditStop)
	{
		if (!obEditStop)
		{
			return;
		}
		// for story RW-44286
		// If Stop Level PU or DO counts are manually entered set the each individual the TripStopHistory.ActualStopTime to the TripStopHistory.PlannedStopTime
		// If a TripStopHistory.ActualStopTime  is already set, modifying Attendance in Plus will not update it
		let editStopId = obEditStop.id();
		let currentStopAttendanceInfo = obEditStop.attendanceInfo();
		let currentPlannedAttendance = currentStopAttendanceInfo.value();
		let currentActualAttendance = currentStopAttendanceInfo.altValue();
		let _setActualStopTime = (_currentStopAttendanceInfo) =>
		{
			this.setAttendanceTakenDateOnEditTrip();
			let plannedStopTime = moment(_currentStopAttendanceInfo.stopTime()).add(-tf.timezonetotalminutes, 'm').format("HH:mm:ss")
			_currentStopAttendanceInfo.actualStopTime(plannedStopTime);
		};

		if (this.obIsEdit())
		{
			let originalEditStop = (this.originalTripHistoryData && this.originalTripHistoryData.TripStopHistories || []).find((stop) => stop.Id === editStopId);
			let originalActualStopTime = originalEditStop && originalEditStop.ActualStopTime;
			let originalPlannedAttendance = originalEditStop && originalEditStop.PlannedAttendance;
			let originalActualAttendance = originalEditStop && originalEditStop.ActualAttendance;

			if (!originalActualStopTime && (originalPlannedAttendance !== currentPlannedAttendance || originalActualAttendance !== currentActualAttendance))
			{
				_setActualStopTime(currentStopAttendanceInfo);
			}

			// If Stop Times are manually changed, modify the TripStopHistory.PlannedStopTime. If Attendance is also taken, the ActualStopTime will match the adjusted PlannedStopTime
			let originalPlannedStopTime = originalEditStop && originalEditStop.PlannedStopTime;
			let currentPlannedStopTime = moment(currentStopAttendanceInfo.stopTime()).add(-tf.timezonetotalminutes, 'm').format("HH:mm:ss");
			if (originalActualStopTime && originalPlannedStopTime !== currentPlannedStopTime)
			{
				currentStopAttendanceInfo.actualStopTime(currentPlannedStopTime);
			}
		}
		else
		{
			if (!!currentPlannedAttendance || !!currentActualAttendance)
			{
				_setActualStopTime(currentStopAttendanceInfo);
			}
		}
	}

	TripHistoryViewModel.prototype.setAttendanceTimeOnCheckedStudent = function(obEditStop)
	{
		// for story RW-44286
		// If the Students are individually checked for PU or DO set the TripHistory.AttendanceTakenDate to the current date time in UTC,
		// the TripHistory.ActualAttendance to the number summed in the Trip level PU or DO field, each individual TripStopHistory.ActualAttendance to the number selected for each Stop,
		// the TripStopHistory.ActualStopTime to the TripStopHistory.PlannedStopTime
		let pickUpStudents = obEditStop.pickUpStudents();
		let dropOffStudents = obEditStop.dropOffStudents();
		let hasSelectedStudent = (pickUpStudents || []).concat(dropOffStudents || []).some((student) => student.attendanceInfo().on() || student.attendanceInfo().off());
		if (hasSelectedStudent)
		{
			this.setActualStopTimeOnEditStop(obEditStop);
		}

		// the Attendance.Time to the current date time in UTC and the Attendance.Attendance to 1
		(pickUpStudents || []).concat(dropOffStudents || []).forEach((student) =>
		{
			if (student.attendanceInfo().on() || student.attendanceInfo().off())
			{
				let originalEditStop = (this.originalTripHistoryData && this.originalTripHistoryData.TripStopHistories).find((stop) => stop.Id === obEditStop.id());
				let originalStudent = (originalEditStop && originalEditStop.Attendances || []).find((item) => item.StudID === student.id());
				student.attendanceInfo().Time = (originalStudent && originalStudent.Time) || moment().utc().add(tf.timezonetotalminutes, 'm').format("YYYY-MM-DD HH:mm:ss");
				student.attendanceInfo().Attendance = 1;
			}
		})
	}

	TripHistoryViewModel.prototype.whetherStudentInSpecifiedDateRange = function(student, currentDate)
	{
		let schedules = [];
		schedules.length === 0 && student.StudentSchedules && (schedules = student.StudentSchedules || []);
		schedules.length === 0 && student.studentSchedules && (schedules = student.studentSchedules() || []);

		// when user add new exception student on event calendar modal self, there's no schedule
		if (schedules.length === 0)
		{
			return true;
		}

		const whetherScheduleDayMatch = (scheduleDays, targetPropertyName) =>
		{
			return scheduleDays.some(scheduleDay =>
			{
				if (!scheduleDay[targetPropertyName]
					|| (targetPropertyName === "requirementDates" && (scheduleDay.customStartDate !== this.obCustomStartDate() || scheduleDay.customEndDate !== this.obCustomEndDate())))
				{
					const weekDays = [scheduleDay.Sunday, scheduleDay.Monday, scheduleDay.Tuesday, scheduleDay.Wednesday, scheduleDay.Thursday, scheduleDay.Friday, scheduleDay.Saturday];
					scheduleDay[targetPropertyName] = this._getAllDatesBetweenDateRange(scheduleDay.StartDate || this.obCustomStartDate(), scheduleDay.EndDate || this.obCustomEndDate(), weekDays);
					scheduleDay.customStartDate = this.obCustomStartDate();
					scheduleDay.customEndDate = this.obCustomEndDate();
				}

				if (currentDate)
				{
					return scheduleDay[targetPropertyName].some(exceptionDate => exceptionDate.isSame(currentDate))
				}
				// partial days of week are available
				return scheduleDay[targetPropertyName].some(exceptionDate => this.obAvailableDates().some(date => date.isSame(exceptionDate)));
			});
		};

		return schedules.some(schedule =>
		{
			if (schedule.StudentRequirementId)
			{
				return whetherScheduleDayMatch(schedule.StudentScheduleDays, "requirementDates");
			}

			return whetherScheduleDayMatch(schedule.StudentScheduleDays, "exceptionDates");
		});
	}


	TripHistoryViewModel.prototype._getAllDatesBetweenDateRange = function(startDate, endDate, weekDays)
	{
		if (!startDate || !endDate)
		{
			return [];
		}

		const allDates = [];
		let _startDate = moment(startDate);
		const _endDate = moment(endDate);

		if (_startDate.isAfter(_endDate))
		{
			return [];
		}

		while (true)
		{
			weekDays[_startDate.day()] && allDates.push(_startDate.clone());
			if (_startDate.isSame(_endDate))
			{
				break;
			}
			_startDate = _startDate.add(1, 'd');
		}

		return allDates;
	}

	/**
	 * Remove unavailable exception student if it does not fit specified date.
	 * @param {Object} tripHistory
	 * @param {Moment} currentDate
	 * @return {void}
	 */
	TripHistoryViewModel.prototype._removeUnAvailableExceptionStudent = function(tripHistory, currentDate)
	{
		const tripStopDict = TF.ArrayToObject(tripHistory.TripStops, "Id");
		tripHistory.TripStopHistories.forEach(tripStopHistory =>
		{
			const availableStudents = tripStopDict[tripStopHistory.TripStopId].StudentsAssigned.filter(student =>
				this.whetherStudentInSpecifiedDateRange(student, currentDate))

			tripStopHistory.Attendances = tripStopHistory.Attendances.filter(attendance => availableStudents.some(student => (student.Id || student.id()) === attendance.StudID));
		});
	}

	TripHistoryViewModel.prototype._flatStudentSchedule = function(stop)
	{
		stop.PickUpStudents = this._flatStudentSchedueOnStudent(stop.Id, stop.PickUpStudents, "PUStopId");
		stop.DropOffStudents = this._flatStudentSchedueOnStudent(stop.Id, stop.DropOffStudents, "DOStopId");
	}

	TripHistoryViewModel.prototype._flatStudentSchedueOnStudent = function(stopId, students, targetStopIdProperty)
	{
		const uniqueStudents = _.uniqBy(students, student => student.Id);
		return uniqueStudents.reduce((studs, currentStud) =>
		{
			const schedules = currentStud.StudentSchedules;
			currentStud.StudentSchedules = [];
			schedules.forEach(schedule =>
			{
				const clonedStudent = _.cloneDeep(currentStud);
				clonedStudent.StudentSchedules = [schedule];
				schedule[targetStopIdProperty] === stopId && (studs.push(clonedStudent));
			})

			return studs;
		}, []);
	}

	TripHistoryViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
		this.originalTripHistoryData = null;
	};

})();