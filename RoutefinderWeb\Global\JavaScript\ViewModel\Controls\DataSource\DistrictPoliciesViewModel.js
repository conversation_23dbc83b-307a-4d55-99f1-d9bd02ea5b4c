﻿(function()
{
	createNamespace('TF.Control').DistrictPoliciesViewModel = DistrictPoliciesViewModel;

	function DistrictPoliciesViewModel(name, type, id)
	{
		this.obEntityDataModel = ko.observable(new TF.DataModel.TripPolicyDataModel());

		this._id = id;
		this._type = type;

		this.obDatabsaeName = ko.observable(name);
		this.obAPIIsDirty = ko.observable(false);
		this.gridSource = ko.observableArray([]);
		this.entitySource = ko.observableArray([]);

		this.resetClick = this.resetClick.bind(this);

		this.pageLevelViewModel = new TF.PageLevel.DistrictPolicyPageLevelViewModel();
	}

	DistrictPoliciesViewModel.prototype.tryGoAway = function()
	{
		return Promise.resolve()
			.then(function()
			{
				var data = this._getUnsavedData();
				if (this.obEntityDataModel().apiIsDirty() || this.obAPIIsDirty())
				{
					return tf.promiseBootbox.yesNo({ message: "You have unsaved changes.  Would you like to save your changes prior to closing this form?", backdrop: true, title: "Unsaved Changes", closeButton: true })
						.then(function(result)
						{
							if (result == true)
							{
								return this.trySave(data);
							}
							if (result == false)
							{
								return true;
							}
							if (result == null)
							{
								return false;
							}
						}.bind(this));
				}
				else
				{
					return true;
				}
			}.bind(this));
	};

	DistrictPoliciesViewModel.prototype.trySave = function(data)
	{
		if (this.$form.find(".kendo-grid").data("kendoGrid").editable != null)
		{// there is something wrong with the grid.
			return Promise.resolve(false);
		}

		/// Add special validation of grid load time cannot be null
		this.pageLevelViewModel.gridSource = this.$form.find(".kendo-grid").data("kendoGrid").dataSource.data().toJSON();

		// this.activeValidate = true;
		return this.pageLevelViewModel.saveValidate()
			.then(function(result)
			{
				// The error message will be hide, so should reset page container height.
				if (result)
				{
					return this._checkAffectedStudents(data)
						.then(function(affected)
						{
							if (affected && affected[0] && affected[0].TotalStudentCount)
							{// if affected count >0, need check if there is any reset functions
								return this._resetAffects(affected[0])
									.then(function(isOK)
									{
										if (isOK)
										{
											return this._save(data, isOK);
										}
									}.bind(this));
							}
							else
							{
								return this._save(data);
							}
						}.bind(this));
				}
			}.bind(this));
	};

	DistrictPoliciesViewModel.prototype._save = function(data, resetEntity)
	{
		// save district policy
		if (resetEntity)
		{
			resetEntity.DistrictPolicyEntities = this.affects;
			resetEntity.UpdateEntities = data;
		}
		else
		{
			resetEntity = {};
			resetEntity.UpdateEntities = data;
		}

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), this._id, "districtpolicies", "databaseType", this._type), { data: resetEntity })
			.then(function(data)
			{
				if (data.StatusCode == 200)
				{
					this.rawData = data.Items;
					this.$form.find(".kendo-grid").data("kendoGrid").saveChanges();
					this.pageLevelViewModel.popupSuccessMessage();
					this.obAPIIsDirty(false);
					return true;
				}
				else
				{
					return false;
				}
			}.bind(this));
	};

	DistrictPoliciesViewModel.prototype.init = function(viewModel, el)
	{
		this.$form = $(el);
		this.load();
	};

	DistrictPoliciesViewModel.prototype.load = function()
	{
		this.obAPIIsDirty(false);
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), this._id, "districtpolicies", "databaseType", this._type))
			.then(function(data)
			{

				this.rawData = data.Items;// get the raw data to check the impacts to students once changed.
				this._initKendoGrid(this.$form.find(".kendo-grid"), data.Items);

				this._validationInitialize();// define-initialize-load validate-show-addlink
				this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));

			}.bind(this));
	};

	DistrictPoliciesViewModel.prototype.apply = function()
	{
		var data = this._getUnsavedData();
		if (data.length > 0)
		{
			return this.trySave(data)
				.then(function(data)
				{
					return data;
				});
		}
		else
		{
			return Promise.resolve(true);
		}
	};

	DistrictPoliciesViewModel.prototype.resetClick = function()
	{
		// reset student grid to default values
		var data = this.$form.find(".kendo-grid").data("kendoGrid").dataSource.data().toJSON();

		data.forEach(function(item)
		{
			item.APIIsDirty = true;
			item.LoadTime = 0;
			item.Value1 = null;
			item.Value2 = null;
			item.Value3 = null;
			item.Value4 = null;
		});

		var dataSource = new kendo.data.DataSource({
			data: data,
			schema: {
				model: {
					id: "Id",
					fields: {
						Id: {
							editable: false
						},
						Category: {
							editable: false
						},
						Value1: {
							type: "number"
						},
						Value2: {
							type: "number"
						},
						Value3: {
							type: "number"
						},
						Value4: {
							type: "number"
						},
						LoadTime: {
							type: "number", format: "{0:N0}"
						}
					}
				}
			}
		});

		this._refreshKendoGrid(dataSource);
		this.obAPIIsDirty(true);
	};

	DistrictPoliciesViewModel.prototype.startEditing = function(e)
	{// to set the text selected when focus(editing)
		var input = $(e.currentTarget);
		if (input != undefined)
		{
			input.find(".k-input[role=spinbutton]").on("focus", function(event)
			{
				var self = this;
				setTimeout(function()
				{// incase it will be blocked by jquery
					self.select();
				}, 0);
				event.preventDefault();
				event.stopPropagation();
			});
		}
	};

	DistrictPoliciesViewModel.prototype.endEditing = function(e)
	{

	};

	DistrictPoliciesViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

	DistrictPoliciesViewModel.prototype.columns = function()
	{
		const unit = tf.measurementUnitConverter.getShortUnits();
		return [
			{
				title: "Term",
				field: "Category",
				width: 110
			},
			{
				title: `Walk To ${tf.applicationTerm.getApplicationTermSingularByName("Stop")}(${unit})`,
				field: "Value1",
				width: 150,
				attributes:
				{
					"class": "tf-edit-td"
				}
			},
			{
				title: `Walk To ${tf.applicationTerm.getApplicationTermSingularByName("School")}(${unit})`,
				field: "Value2",
				width: 150,
				attributes:
				{
					"class": "tf-edit-td"
				}
			},
			{
				title: "Seats Per " + tf.applicationTerm.getApplicationTermSingularByName("Student"),
				field: "Value3",
				width: 150,
				attributes:
				{
					"class": "tf-edit-td"
				}
			},
			{
				title: "Ride Time(min)",
				field: "Value4",
				width: 150,
				attributes:
				{
					"class": "tf-edit-td"
				}
			},
			{
				title: tf.applicationTerm.getApplicationTermSingularByName("Load Time") + "(sec)",
				field: "LoadTime",
				width: 150,
				format: "{0:N0}",
				attributes:
				{
					"class": "tf-edit-td"
				},
				editor: function(container, options)
				{
					// create input element and add the validation attribute
					var input = $('<input name="' + options.field + '" />');
					// append the editor
					input.appendTo(container);
					// enhance the input into NumericTextBox
					input.kendoNumericTextBox({
						min: 0,
						step: 1,
						decimals: 0,
						format: '0.'
					});
				}
			}
		]
	}

	DistrictPoliciesViewModel.prototype._initKendoGrid = function($element, source)
	{

		$element.kendoGrid({
			dataSource: this._getGridDataSource($element, source),
			columns: tf.measurementUnitConverter.handleUnitOfMeasure(this.columns()),
			editable: true,
			save: function(e)
			{
				e.model.APIIsDirty = true;
				this.obAPIIsDirty(true);
			}.bind(this)
		});

		$element.find(".tf-edit-td").focusin(this.startEditing.bind(this));
		$element.find(".tf-edit-td").focusout(this.endEditing.bind(this));
	};

	DistrictPoliciesViewModel.prototype._getGridDataSource = function($element, source)
	{
		source.forEach(function(item)
		{
			if (item.Destination == "Student Policies")
			{
				this.gridSource.push(item);
			}
			else if (item.Destination == "Trip Policies")
			{
				this.entitySource.push(item);
			}
		}.bind(this));

		this._convertTripPollicy();

		var dataSource = new kendo.data.DataSource({
			data: this.gridSource(),
			schema: {
				model: {
					id: "Id",
					fields: {
						Id: {
							editable: false
						},
						Category: {
							editable: false
						},
						Value1: {
							type: "number", validation: { min: 0 }
						},
						Value2: {
							type: "number", validation: { min: 0 }
						},
						Value3: {
							type: "number", validation: { min: 0 }
						},
						Value4: {
							type: "number", validation: { min: 0 }
						},
						LoadTime: {
							type: "number", validation: { min: 0 }
						}
					}
				}
			}
		});
		return dataSource;
	};

	DistrictPoliciesViewModel.prototype._convertTripPollicy = function()
	{// this is conver the list data to entity data of table [dbo].[DistrictPolicy] with Destination='Trip Policies'
		var attendance = this.entitySource().filter(function(item)
		{
			return item.Category == "Attendance";
		});

		var stop = this.entitySource().filter(function(item)
		{
			return item.Category == "Student/Stop";
		});

		var ridership = this.entitySource().filter(function(item)
		{
			return item.Category == "Ridership";
		});

		var model = {
			FromDate: attendance[0] ? attendance[0].Value3 ? moment("1899-12-30").add(attendance[0].Value3 + 1, 'd').format("L") : "" : "",
			ToDate: attendance[0] ? attendance[0].Value4 ? moment("1899-12-30").add(attendance[0].Value4 + 1, 'd').format("L") : "" : "",
			EfficiencyPercentage: ridership[0] ? ridership[0].Value1 : "",
			MaximumPercentage: ridership[0] ? ridership[0].Value2 : "",
			MaxStudentsPerStop: stop[0] ? stop[0].Value1 : "",
			SchoolDaysPerYear: attendance[0] ? attendance[0].Value2 : ""
		};

		this.obEntityDataModel(new TF.DataModel.TripPolicyDataModel(model));
	};

	DistrictPoliciesViewModel.prototype._checkAffectedStudents = function(data)
	{// the function of check affects based on the data change
		this.affects = [];
		data.forEach(function(item)
		{
			var currentItem = this.rawData.filter(function(record)
			{
				return record.Id == item.Id;
			})[0];

			if (item.LoadTime !== currentItem.LoadTime)
			{
				this.affects.push(currentItem);
			}
		}.bind(this));

		if (this.affects.length > 0)
		{
			// if there are some changes of the load times.
			return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), this._id, "districtpolicies", "affects", "databaseType", this._type), { data: this.affects })
				.then(function(result)
				{
					if (result.StatusCode == 200)
					{
						return result.Items;
					}
					else
					{/// the check in API is error.
						return -1;
					}

				}.bind(this));
		}

		// if there is no change about load times
		return Promise.all([]);
	};

	DistrictPoliciesViewModel.prototype._resetAffects = function(affected)
	{
		affected.affects = this.affects;
		return tf.modalManager.showModal(new TF.Modal.ResetLoadTimesModalViewModel(affected))
			.then(function(data)
			{
				return data;
			}.bind(this));
	};

	DistrictPoliciesViewModel.prototype._getUnsavedData = function()
	{
		var data = this.$form.find(".kendo-grid").data("kendoGrid").dataSource.data().toJSON();
		if (this.obEntityDataModel().apiIsDirty())
		{
			var attendance = this.entitySource().filter(function(item)
			{
				return item.Category == "Attendance";
			});
			attendance[0].Value2 = this.obEntityDataModel().schoolDaysPerYear();
			attendance[0].Value3 = moment(this.obEntityDataModel().fromDate()).diff(moment("1899-12-30"), 'days');
			attendance[0].Value4 = moment(this.obEntityDataModel().toDate()).diff(moment("1899-12-30"), 'days');
			attendance[0].Value1 = this.obEntityDataModel().efficiencyPercentage();
			attendance[0].APIIsDirty = true;

			var stop = this.entitySource().filter(function(item)
			{
				return item.Category == "Student/Stop";
			});
			stop[0].Value1 = this.obEntityDataModel().maxStudentsPerStop();
			stop[0].APIIsDirty = true;

			var ridership = this.entitySource().filter(function(item)
			{
				return item.Category == "Ridership";
			});
			ridership[0].Value1 = this.obEntityDataModel().efficiencyPercentage();
			ridership[0].Value2 = this.obEntityDataModel().maximumPercentage();
			ridership[0].APIIsDirty = true;

			data.push(attendance[0]);
			data.push(stop[0]);
			data.push(ridership[0]);
		}
		return data;
	};

	DistrictPoliciesViewModel.prototype._validationInitialize = function()
	{
		var validatorFields = {}, isValidating = false, self = this;

		validatorFields.fromDate = {
			trigger: "blur change",
			validators: {
				date: {
					message: 'invalid date'
				},
				callback:
				{
					message: 'must be <= <span class="ActiveTo">To Date</span>',
					callback: function(value, validator)
					{
						if (this.obEntityDataModel().fromDate() == false || this.obEntityDataModel().toDate() == false)
						{
							return true;
						}

						if (this.activeValidate && this.pageLevelViewModel.activeLostfouseName == "activeTo")
						{
							return this.activeInlineDisplay != true;
						}
						this.pageLevelViewModel.activeDateEmpty = false;

						var m = new moment(value);
						var end = new moment(this.obEntityDataModel().toDate());

						if (m.isAfter(end))
						{
							this.pageLevelViewModel.activeLostfouseName = "activeFrom";
							this.activeInlineDisplay = true;
							return false;
						}

						this.inactiveInlineDisplay = false;
						this.activeInlineDisplay = false;
						this.pageLevelViewModel.activeLostfouseName = "";
						this.$form.find(".ActiveFrom").parent().hide();
						return true;

					}.bind(this)
				}
			}
		};

		validatorFields.toDate = {
			trigger: "blur change",
			validators: {
				date: {
					message: 'invalid date'
				},
				callback:
				{
					message: 'must be >= <span class="ActiveFrom">From Date</span>',
					callback: function(value, validator)
					{
						if (this.obEntityDataModel().fromDate() == false || this.obEntityDataModel().toDate() == false)
						{
							return true;
						}

						if (this.activeValidate && this.pageLevelViewModel.activeLostfouseName == "activeFrom")
						{
							return this.inactiveInlineDisplay != true;
						}
						this.pageLevelViewModel.inactiveDateEmpty = false;

						var m = new moment(value);
						var start = new moment(this.obEntityDataModel().fromDate());
						if (start.isAfter(m))
						{
							this.pageLevelViewModel.activeLostfouseName = "activeTo";
							this.inactiveInlineDisplay = true;
							return false;
						}

						this.inactiveInlineDisplay = false;
						this.activeInlineDisplay = false;
						this.pageLevelViewModel.activeLostfouseName = "";
						this.$form.find(".ActiveTo").parent().hide();
						return true;

					}.bind(this)
				}
			}
		};

		validatorFields.efficiency = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 100,
					message: 'must be >= 0 and <= 100'
				}
			}
		};

		validatorFields.maximum = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 100,
					message: 'must be >= 0 and <= 100'
				}
			}
		};

		validatorFields.schoolDays = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 365,
					message: 'must be >= 0 and <= 365'
				}
			}
		};

		validatorFields.vrpCapacity = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 9999999999,
					message: 'must be > =0'
				}
			}
		};

		validatorFields.vrpMaxTripTime = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 9999999999,
					message: 'must be > =0'
				}
			}
		};

		validatorFields.vrpMaxTripDistance = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 9999999999,
					message: 'must be > =0'
				}
			}
		};

		validatorFields.vrpMaxRidingTime = {
			trigger: "blur change",
			validators: {
				between: {
					min: 0,
					max: 9999999999,
					message: 'must be > =0'
				}
			}
		};

		this.$form.bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});
	};

	DistrictPoliciesViewModel.prototype._refreshKendoGrid = function(data)
	{
		this.$form.find(".kendo-grid").data("kendoGrid").setDataSource(data);
	};
})();

