(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.RoutingProfileDeletion = RoutingProfileDeletion;

	function RoutingProfileDeletion()
	{
		this.type = 'routingprofile';
		namespace.BaseDeletion.apply(this, arguments);
	}

	RoutingProfileDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	RoutingProfileDeletion.prototype.constructor = RoutingProfileDeletion;

	RoutingProfileDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	};

})();