(function()
{
	createNamespace('TF').TimeRangeWithWeekDayControlHelper = TimeRangeWithWeekDayControlHelper;
	function TimeRangeWithWeekDayControlHelper() { }

	TimeRangeWithWeekDayControlHelper.buildUserPreferenceKey = function(gridType)
	{
		return {
			_UserPreferenceDayofTheWeekFilter: tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.dayOfTheWeek',
			_UserPreferenceStartDateFilter: tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.startDate',
			_UserPreferenceEndDateFilter: tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.endDate',
			_UserPreferenceStartTimeFilter: tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.startTime',
			_UserPreferenceEndTimeFilter: tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.endTime'
		};
	};

	TimeRangeWithWeekDayControlHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		var userPreferenceKeys = TF.TimeRangeWithWeekDayControlHelper.buildUserPreferenceKey(gridType);
		caller._UserPreferenceDayofTheWeekFilter = userPreferenceKeys._UserPreferenceDayofTheWeekFilter;
		caller._UserPreferenceStartDateFilter = userPreferenceKeys._UserPreferenceStartDateFilter;
		caller._UserPreferenceEndDateFilter = userPreferenceKeys._UserPreferenceEndDateFilter;
		caller._UserPreferenceStartTimeFilter = userPreferenceKeys._UserPreferenceStartTimeFilter;
		caller._UserPreferenceEndTimeFilter = userPreferenceKeys._UserPreferenceEndTimeFilter;
	};

	TimeRangeWithWeekDayControlHelper.getUserPreferenceData = function()
	{
		var caller = this;
		var userPreferenceKeys = {
			_UserPreferenceDayofTheWeekFilter: caller._UserPreferenceDayofTheWeekFilter,
			_UserPreferenceStartDateFilter: caller._UserPreferenceStartDateFilter,
			_UserPreferenceEndDateFilter: caller._UserPreferenceEndDateFilter,
			_UserPreferenceStartTimeFilter: caller._UserPreferenceStartTimeFilter,
			_UserPreferenceEndTimeFilter: caller._UserPreferenceEndTimeFilter
		};
		return TF.TimeRangeWithWeekDayControlHelper.getUserPreferenceDataCommon(userPreferenceKeys);
	};

	TimeRangeWithWeekDayControlHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var caller = this;
		var userPreferenceKeys = TF.TimeRangeWithWeekDayControlHelper.buildUserPreferenceKey(gridType);
		return TF.TimeRangeWithWeekDayControlHelper.getUserPreferenceDataCommon(userPreferenceKeys);
	};

	TimeRangeWithWeekDayControlHelper.getUserPreferenceDataCommon = function(userPreferenceKeys)
	{
		var timeRangeData = {
			dayOfTheWeek: tf.userPreferenceManager.get(userPreferenceKeys._UserPreferenceDayofTheWeekFilter),
			startDate: tf.userPreferenceManager.get(userPreferenceKeys._UserPreferenceStartDateFilter),
			endDate: tf.userPreferenceManager.get(userPreferenceKeys._UserPreferenceEndDateFilter),
			startTime: tf.userPreferenceManager.get(userPreferenceKeys._UserPreferenceStartTimeFilter),
			endTime: tf.userPreferenceManager.get(userPreferenceKeys._UserPreferenceEndTimeFilter)
		};
		return timeRangeData;
	};

	TimeRangeWithWeekDayControlHelper.clearUserPreferenceTimeRangeFilter = function(userPreferenceKey)
	{
		var keys = TF.TimeRangeWithWeekDayControlHelper.buildUserPreferenceKey(userPreferenceKey);
		var timeRangeData = {
			dayOfTheWeek: [true, true, true, true, true, true, true],
			startDate: moment(),
			endDate: moment(),
			startTime: moment().format('YYYY-MM-DDT00:00:00.000'),
			endTime: moment().format('YYYY-MM-DDTHH:mm:00.000')
		};
		TF.TimeRangeWithWeekDayControlHelper.saveUserPreferenceCommonByKeys(timeRangeData, keys);
	};

	TimeRangeWithWeekDayControlHelper.initFilterTimeRange = function(option)
	{
		var caller = this;

		var timeControlHelper = TF.TimeRangeWithWeekDayControlHelper;
		var timeRangeData = timeControlHelper.getUserPreferenceData.call(caller);

		var initStartDate = toISOStringWithoutTimeZone(timeRangeData.startDate && !option.useToday ? moment(timeRangeData.startDate) : (caller.options.defaultTimeRange?.startDate || option.startDate));
		var initEndDate = toISOStringWithoutTimeZone(timeRangeData.endDate && !option.useToday ? moment(timeRangeData.endDate) : (caller.options.defaultTimeRange?.endDate || option.endDate));
		var initStartTime = timeRangeData.startTime && !option.useToday ? toISOStringWithoutTimeZone(moment(timeRangeData.startTime)) : (caller.options.defaultTimeRange?.startTime || option.startTime);
		var initEndTime = timeRangeData.endTime && !option.useToday ? toISOStringWithoutTimeZone(moment(timeRangeData.endTime)) : (caller.options.defaultTimeRange?.endTime || option.endTime);

		caller.obStartDate = ko.observable(initStartDate);
		caller.obEndDate = ko.observable(initEndDate);
		caller.obStartTime = ko.observable(initStartTime);
		caller.obEndTime = ko.observable(initEndTime);

		caller.obStartDateTime = ko.pureComputed(function()
		{
			var date = moment(caller.obStartDate()),
				time = moment(caller.obStartTime());
			return moment([date.year(), date.month(), date.date(), time.hour(), time.minutes(), time.seconds(), time.millisecond()]).format("YYYY-MM-DDTHH:mm:ss");
		}, caller);

		caller.obEndDateTime = ko.pureComputed(function()
		{
			var date = moment(caller.obEndDate()),
				time = moment(caller.obEndTime());
			return moment([date.year(), date.month(), date.date(), time.hour(), time.minutes(), time.seconds(), time.millisecond()]).format("YYYY-MM-DDTHH:mm:ss");
		}, caller);

		caller.obStartDate.subscribe(timeControlHelper.onStartDateChange, caller);
		caller.obEndDate.subscribe(timeControlHelper.onEndDateChange, caller);
		//caller.obStartDate.subscribe(timeControlHelper._fromDateChange.bind(caller), caller);
		//caller.obEndDate.subscribe(timeControlHelper._toDateChange.bind(caller), caller);
		caller.obStartTime.subscribe(timeControlHelper._fromDateChange.bind(caller), caller);
		caller.obEndTime.subscribe(timeControlHelper._toDateChange.bind(caller), caller);
	};

	TimeRangeWithWeekDayControlHelper.initFilterWeekdays = function(options)
	{
		var caller = this;
		var timeControlHelper = TimeRangeWithWeekDayControlHelper;

		caller.obMonToFriChecked = ko.observable(false);
		caller.obMondayChecked = ko.observable(false);
		caller.obTuesdayChecked = ko.observable(false);
		caller.obThursdayChecked = ko.observable(false);
		caller.obWednesdayChecked = ko.observable(false);
		caller.obFridayChecked = ko.observable(false);
		caller.obSaturdayChecked = ko.observable(false);
		caller.obSundayChecked = ko.observable(false);

		caller.obMonToFriChecked.subscribe(timeControlHelper._monToFriCheckedChange.bind(caller), caller);
		caller.obMondayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);
		caller.obTuesdayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);
		caller.obWednesdayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);
		caller.obThursdayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);
		caller.obFridayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);
		caller.obSaturdayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);
		caller.obSundayChecked.subscribe(timeControlHelper._notMonToFriCheckedChange.bind(caller), caller);

		caller.obMonToFriDisabled = ko.observable(true);
		caller.obMondayDisabled = ko.observable(true);
		caller.obTuesdayDisabled = ko.observable(true);
		caller.obThursdayDisabled = ko.observable(true);
		caller.obWednesdayDisabled = ko.observable(true);
		caller.obFridayDisabled = ko.observable(true);
		caller.obSaturdayDisabled = ko.observable(true);
		caller.obSundayDisabled = ko.observable(true);

		// caller._weekdaycheckes = [caller.obMondayChecked, caller.obTuesdayChecked, caller.obWednesdayChecked, caller.obThursdayChecked, caller.obFridayChecked, caller.obSaturdayChecked, caller.obSundayChecked];
		// caller._weekdayDisableds = [caller.obMondayDisabled, caller.obTuesdayDisabled, caller.obWednesdayDisabled, caller.obThursdayDisabled, caller.obFridayDisabled, caller.obSaturdayDisabled, caller.obSundayDisabled];
		caller._weekdaycheckes = [caller.obSundayChecked, caller.obMondayChecked, caller.obTuesdayChecked, caller.obWednesdayChecked, caller.obThursdayChecked, caller.obFridayChecked, caller.obSaturdayChecked];
		caller._weekdayDisableds = [caller.obSundayDisabled, caller.obMondayDisabled, caller.obTuesdayDisabled, caller.obWednesdayDisabled, caller.obThursdayDisabled, caller.obFridayDisabled, caller.obSaturdayDisabled];

		TF.TimeRangeWithWeekDayControlHelper.resetWeekDayChecksAndDisabledsBySelectRange.bind(caller)();

		var userPreferenceDayOfTheWeekFilter = !!caller.options.disableSticky ? (caller.options?.defaultTimeRange?.dayOfTheWeek || null) : tf.userPreferenceManager.get(caller._UserPreferenceDayofTheWeekFilter);
		userPreferenceDayOfTheWeekFilter = userPreferenceDayOfTheWeekFilter || [true, true, true, true, true, true, true];
		TF.TimeRangeWithWeekDayControlHelper.resetAllObWeekdaycheckValuesAndObWeekDayDisableValues.bind(caller)(userPreferenceDayOfTheWeekFilter);
	};

	TimeRangeWithWeekDayControlHelper.resetAllObWeekdaycheckValuesAndObWeekDayDisableValues = function(userPreferenceDayOfTheWeekFilter)
	{
		var caller = this;
		userPreferenceDayOfTheWeekFilter = userPreferenceDayOfTheWeekFilter || [true, true, true, true, true, true, true];
		for (var i = 0; i < caller._weekdaycheckes.length; i++)
		{
			caller._weekdaycheckes[i](caller._weekdayDisableds[i]() ? false : userPreferenceDayOfTheWeekFilter[i]);
		}
	};

	TimeRangeWithWeekDayControlHelper.resetWeekDayChecksAndDisabledsBySelectRange = function()
	{
		var caller = this;

		var startDate = moment(moment(caller.obStartDate()).format('L'));
		var endDate = moment(moment(caller.obEndDate()).format('L'));

		if (startDate._i !== "Invalid date" && endDate._i !== "Invalid date")
		{
			var j = 0;
			for (var i = 0; i < caller._weekdaycheckes.length; i++)
			{
				caller._weekdaycheckes[i](false);
			}

			var sundayDisabled = true;
			var saturdayDisabled = true;

			for (i = startDate; i <= endDate && j <= 8; i = i.add('day', 1))
			{
				if (i.weekday() === 0)
					sundayDisabled = false;

				if (i.weekday() === 6)
					saturdayDisabled = false;

				// if (i.weekday() !== 0 && i.weekday() !== 6)
				caller._weekdaycheckes[i.weekday()](true);
				j++;
			}
			TF.TimeRangeWithWeekDayControlHelper._setMonToFriCheck.bind(caller)();

			for (i = 0; i < caller._weekdaycheckes.length; i++)
			{
				caller._weekdayDisableds[i](!caller._weekdaycheckes[i]());
			}

			if (!saturdayDisabled)
				caller._weekdayDisableds[6](saturdayDisabled);

			if (!sundayDisabled)
				caller._weekdayDisableds[0](sundayDisabled);
		}
		else //EndDate may be empty
		{
			for (i = 0; i < caller._weekdaycheckes.length; i++)
			{
				caller._weekdayDisableds[i](false);
			}
		}
	};

	TimeRangeWithWeekDayControlHelper._monToFriCheckedChange = function()
	{
		var caller = this;
		if (!caller._lockWeekDayChange)
		{
			caller._lockWeekDayChange = true;
			if (caller.obMonToFriChecked())
			{
				caller.obMondayChecked(true);
				caller.obTuesdayChecked(true);
				caller.obWednesdayChecked(true);
				caller.obThursdayChecked(true);
				caller.obFridayChecked(true);
			}
			else
			{
				caller.obMondayChecked(false);
				caller.obTuesdayChecked(false);
				caller.obWednesdayChecked(false);
				caller.obThursdayChecked(false);
				caller.obFridayChecked(false);
			}
			caller._lockWeekDayChange = false;
		}
	};

	TimeRangeWithWeekDayControlHelper._notMonToFriCheckedChange = function()
	{
		var caller = this;
		if (caller._lockWeekDayChange === false)
		{
			caller._lockWeekDayChange = true;
			TF.TimeRangeWithWeekDayControlHelper._setMonToFriCheck.bind(caller)();
			caller._lockWeekDayChange = false;
		}
		// caller._settingFilterChange();
	};

	TF.TimeRangeWithWeekDayControlHelper._setMonToFriCheck = function()
	{
		var caller = this;
		// 		caller.obMonToFriChecked = ko.computed(function(){
		// 			return caller.obMondayChecked() && caller.obThursdayChecked() && caller.obWednesdayChecked() && caller.obTuesdayChecked() && caller.obFridayChecked();
		// 		}, caller);
		caller.obMonToFriChecked(caller.obMondayChecked() && caller.obThursdayChecked() && caller.obWednesdayChecked() && caller.obTuesdayChecked() && caller.obFridayChecked());
	};

	TimeRangeWithWeekDayControlHelper.onStartDateChange = function(dateTime)
	{
		var caller = this;

		//var startDate = e.sender._value;
		var startDate = dateTime;
		var Dateformat = toISOStringWithoutTimeZone(moment(startDate));
		if (caller.obEndDateTime() !== "Invalid date" && startDate && caller.obEndDateTime() && new Date(startDate) > new Date(caller.obEndDateTime()))
		{
			caller.obStartDate(Dateformat);
			caller.obEndDate(caller.obStartDate());
			// caller.obEndTime(caller.obStartTime());
		}
		else
		{
			caller.obStartDate(Dateformat);
		}
		TF.TimeRangeWithWeekDayControlHelper.resetWeekDayChecksAndDisabledsBySelectRange.bind(caller)();
	};

	TimeRangeWithWeekDayControlHelper.onEndDateChange = function(dateTime)
	{
		var caller = this;

		var endDate = dateTime;
		var Dateformat = toISOStringWithoutTimeZone(moment(endDate));
		if (caller.obStartDateTime() !== "Invalid date" && endDate && caller.obStartDateTime() && new Date(caller.obStartDateTime()) > new Date(caller.obEndDateTime()))
		{
			caller.obEndDate(Dateformat);
			caller.obStartDate(caller.obEndDate());
			// caller.obStartTime(caller.obEndTime());
		}
		else
		{
			caller.obEndDate(Dateformat);
		}
		TF.TimeRangeWithWeekDayControlHelper.resetWeekDayChecksAndDisabledsBySelectRange.bind(caller)();
	};

	TimeRangeWithWeekDayControlHelper._fromDateChange = function()
	{
		var caller = this;

		if (caller.obEndDateTime() !== "Invalid date" && caller.obStartDateTime() && caller.obEndDateTime() && caller.obStartDateTime() !== "Invalid date" && new Date(caller.obStartDateTime()) > new Date(caller.obEndDateTime()))
		{
			caller.obEndDate(caller.obStartDate());
			caller.obEndTime(caller.obStartTime());
		}
		TF.TimeRangeWithWeekDayControlHelper.resetWeekDayChecksAndDisabledsBySelectRange.bind(caller)();
	};

	TimeRangeWithWeekDayControlHelper._toDateChange = function()
	{
		var caller = this;

		if (caller.obEndDateTime() !== "Invalid date" && caller.obStartDateTime() && caller.obEndDateTime() && caller.obStartDateTime() !== "Invalid date" && new Date(caller.obStartDateTime()) > new Date(caller.obEndDateTime()))
		{
			caller.obStartDate(caller.obEndDate());
			caller.obStartTime(caller.obEndTime());
		}
		TF.TimeRangeWithWeekDayControlHelper.resetWeekDayChecksAndDisabledsBySelectRange.bind(caller)();
	};

	TimeRangeWithWeekDayControlHelper.getTimeRangeData = function(notSave)
	{
		var caller = this;
		var weekdayCheckedArray = TF.TimeRangeWithWeekDayControlHelper.buildWeekDayCheckedArray(caller.obSundayChecked(), caller.obMondayChecked(), caller.obTuesdayChecked(), caller.obWednesdayChecked(),
			caller.obThursdayChecked(), caller.obFridayChecked(), caller.obSaturdayChecked());

		if (!notSave)
		{
			tf.userPreferenceManager.save(caller._UserPreferenceDayofTheWeekFilter, weekdayCheckedArray);
		}
		var timeRangeData = {
			dayOfTheWeek: weekdayCheckedArray,
			startDate: caller.obStartDate(),
			endDate: caller.obEndDate(),
			startTime: caller.obStartTime(),
			endTime: caller.obEndTime()
		};
		return timeRangeData;
	}

	TimeRangeWithWeekDayControlHelper.saveUserPreference = function()
	{
		var caller = this;
		var timeRangeData = TF.TimeRangeWithWeekDayControlHelper.getTimeRangeData.call(caller);
		TF.TimeRangeWithWeekDayControlHelper.saveUserPreferenceCommon.bind(caller)(timeRangeData);
	};

	TimeRangeWithWeekDayControlHelper.saveUserPreferenceCommon = function(timeRangeData)
	{
		var caller = this;

		var keys = {
			_UserPreferenceStartDateFilter: caller._UserPreferenceStartDateFilter,
			_UserPreferenceEndDateFilter: caller._UserPreferenceEndDateFilter,
			_UserPreferenceStartTimeFilter: caller._UserPreferenceStartTimeFilter,
			_UserPreferenceEndTimeFilter: caller._UserPreferenceEndTimeFilter,
			_UserPreferenceDayofTheWeekFilter: caller._UserPreferenceDayofTheWeekFilter
		};
		TF.TimeRangeWithWeekDayControlHelper.saveUserPreferenceCommonByKeys(timeRangeData, keys);
	};

	TimeRangeWithWeekDayControlHelper.saveUserPreferenceCommonByKeys = function(timeRangeData, keys)
	{
		tf.userPreferenceManager.save(keys._UserPreferenceStartDateFilter, timeRangeData.startDate);
		tf.userPreferenceManager.save(keys._UserPreferenceEndDateFilter, timeRangeData.endDate);
		tf.userPreferenceManager.save(keys._UserPreferenceStartTimeFilter, timeRangeData.startTime);
		tf.userPreferenceManager.save(keys._UserPreferenceEndTimeFilter, timeRangeData.endTime);
		tf.userPreferenceManager.save(keys._UserPreferenceDayofTheWeekFilter, timeRangeData.dayOfTheWeek);
	};

	TimeRangeWithWeekDayControlHelper.buildWeekDayCheckedArray = function(sundayChecked, mondayChecked, tuesdayChecked, wednesdayChecked, thursdayChecked, fridayChecked, saturdayChecked)
	{
		return [sundayChecked, mondayChecked, tuesdayChecked, wednesdayChecked, thursdayChecked, fridayChecked, saturdayChecked];
	};

	TimeRangeWithWeekDayControlHelper.buildRequestWeekDayOption = function(weekDayCheckedArray, disables)
	{
		weekDayCheckedArray = weekDayCheckedArray || [];
		var dayOfTheWeek = [];
		var all = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
		weekDayCheckedArray.map(function(weekDayChecked, idx)
		{
			if (weekDayChecked)
			{
				dayOfTheWeek.push(all[idx]);
			}
		});

		if (disables && (all.length - disables.filter(x => x).length) == dayOfTheWeek.length)
		{
			return null;
		}

		return {
			FieldName: "DayOfTheWeek",
			Operator: "In",
			Value: "",
			ValueList: JSON.stringify(dayOfTheWeek)
		};
	};
})();

(function()
{
	createNamespace('TF').ListMoverControlHelper = ListMoverControlHelper;
	function ListMoverControlHelper() { }

	ListMoverControlHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'vehiclelist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListMoverControlHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceListMoverFilter = TF.ListMoverControlHelper.buildUserPreferenceKey(gridType);
	};

	ListMoverControlHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListMoverControlHelper.getUserPreferenceDataCommon(caller._UserPreferenceListMoverFilter);
	};

	ListMoverControlHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var _UserPreferenceListMoverFilter = TF.ListMoverControlHelper.buildUserPreferenceKey(gridType);
		return TF.ListMoverControlHelper.getUserPreferenceDataCommon(_UserPreferenceListMoverFilter);
	};

	ListMoverControlHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListMoverControlHelper.getListFilterSet = function(fieldName, filterField, data)
	{
		const caller = this;
		const innerData = data || caller.selectedData;
		let listFilterSet = undefined;

		// vehicle filter on field gpsId + vendorId
		if (TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled && !filterField)
		{
			listFilterSet = {
				"LogicalOperator": "Or",
				"FilterItems": [],
				"FilterSets": innerData.map(vehicle => ({
					"LogicalOperator": "And",
					"FilterItems": [
						{
							"FieldName": "EventVehicleID",
							"Operator": "EqualTo",
							"TypeHint": "String",
							"Value": vehicle.Gpsid
						},
						{
							"FieldName": "VendorId",
							"Operator": "EqualTo",
							"TypeHint": "String",
							"Value": vehicle.VendorId?.toString()
						}
					],
					"FilterSets": [],
					"IsReduceRecordsFilterSet": true
				})),
				"IsReduceRecordsFilterSet": true,
				"ListFilterIds": innerData.map(function(item) { return item.Id; }),
				"ValueList": JSON.stringify(innerData.map(item => ({ gpsId: item.Gpsid, vendorId: item.VendorId }))),
			}
		}
		else
		{
			/* normal field or legacy gpsId */
			const option = {
				selectedData: innerData,
				fieldName: fieldName || "VehicleExternalID",
				filterField: filterField || "Gpsid"
			};
			listFilterSet = {
				"LogicalOperator": "And",
				"FilterItems": [TF.ListFilterHelper.buildListFilterItemBySelectedData(option)],
				"IsReduceRecordsFilterSet": true
			};
		}

		return listFilterSet;
	}

	ListMoverControlHelper.saveUserPreference = function(fieldName, filterField, data)
	{
		const caller = this;
		var listFilterSet = TF.ListMoverControlHelper.getListFilterSet.call(caller, fieldName, filterField, data);
		listFilterSet && tf.userPreferenceManager.save(caller._UserPreferenceListMoverFilter, listFilterSet);
	};
})();

(function()
{
	createNamespace('TF').ListGPSEventTypeHelper = ListGPSEventTypeHelper;
	function ListGPSEventTypeHelper() { }

	ListGPSEventTypeHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'gpseventtypelist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListGPSEventTypeHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceGPSEventTypeListMoverFilter = TF.ListGPSEventTypeHelper.buildUserPreferenceKey(gridType);
	};

	ListGPSEventTypeHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListGPSEventTypeHelper.getUserPreferenceDataCommon(caller._UserPreferenceGPSEventTypeListMoverFilter);
	};

	ListGPSEventTypeHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var _UserPreferenceGPSEventTypeListMoverFilter = TF.ListGPSEventTypeHelper.buildUserPreferenceKey(gridType);
		return TF.ListGPSEventTypeHelper.getUserPreferenceDataCommon(_UserPreferenceGPSEventTypeListMoverFilter);
	};

	ListGPSEventTypeHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListGPSEventTypeHelper.getListFilterItem = function(data)
	{
		var caller = this;

		var option = {
			selectedData: data,
			fieldName: caller.options.parentPageName === "locationevent" ? 'EventType' : 'EventName',
			filterField: 'EventTypeName'
		};
		return TF.ListFilterHelper.buildListFilterItemBySelectedData(option);
	}

	ListGPSEventTypeHelper.saveUserPreference = function(data)
	{
		var caller = this;
		var listFilterItem = TF.ListGPSEventTypeHelper.getListFilterItem.call(caller, data);
		tf.userPreferenceManager.save(caller._UserPreferenceGPSEventTypeListMoverFilter, listFilterItem);
	};
})();

(function()
{
	createNamespace('TF').ListLocationEventStatusHelper = ListLocationEventStatusHelper;
	function ListLocationEventStatusHelper() { }

	ListLocationEventStatusHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'locationeventstatuslist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListLocationEventStatusHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceLocationEventStatusListMoverFilter = TF.ListLocationEventStatusHelper.buildUserPreferenceKey(gridType);
	};

	ListLocationEventStatusHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListLocationEventStatusHelper.getUserPreferenceDataCommon(caller._UserPreferenceLocationEventStatusListMoverFilter);
	};

	ListLocationEventStatusHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListLocationEventStatusHelper.buildUserPreferenceKey(gridType);
		return TF.ListLocationEventStatusHelper.getUserPreferenceDataCommon(key);
	};

	ListLocationEventStatusHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListLocationEventStatusHelper.saveUserPreference = function(data, notSave)
	{
		const caller = this;
		const emptyData = (data || []).find(item => item.DisplayText === "[Empty]");
		const selectedData = (data || []).filter(item => item.DisplayText !== "[Empty]");
		const option = {
			selectedData: selectedData,
			fieldName: "OnTime",
			filterField: 'DisplayText'
		};
		const inFilterItem = TF.ListFilterHelper.buildListFilterItemBySelectedData(option);
		const filterSet = {
			LogicalOperator: "or",
			FilterItems: [inFilterItem],
			FilterSets: []
		};
		if (emptyData)
		{
			filterSet.FilterItems.push({
				FieldName: "OnTime",
				Operator: "IsNull",
				Value: "",
				TypeHint: "string",
			});
		}
		const saveData = {
			FilterSet: filterSet,
			ListFilterIds: (data || []).map(item =>
			{
				return item.ID;
			})
		};
		if (!notSave)
		{
			tf.userPreferenceManager.save(caller._UserPreferenceLocationEventStatusListMoverFilter, saveData);
			return null;
		}
		else
		{
			return saveData;
		}
	};
})();

(function()
{
	createNamespace('TF').ListLocationEventOperatorHelper = ListLocationEventOperatorHelper;
	function ListLocationEventOperatorHelper() { }

	ListLocationEventOperatorHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'locationeventoperatorlist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListLocationEventOperatorHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceLocationEventOperatorListMoverFilter = TF.ListLocationEventOperatorHelper.buildUserPreferenceKey(gridType);
	};

	ListLocationEventOperatorHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListLocationEventOperatorHelper.getUserPreferenceDataCommon(caller._UserPreferenceLocationEventOperatorListMoverFilter);
	};

	ListLocationEventOperatorHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListLocationEventOperatorHelper.buildUserPreferenceKey(gridType);
		return TF.ListLocationEventOperatorHelper.getUserPreferenceDataCommon(key);
	};

	ListLocationEventOperatorHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListLocationEventOperatorHelper.saveUserPreference = function(data, notSave)
	{
		const caller = this;
		const selectedData = (data || []);
		const option = {
			selectedData: selectedData,
			fieldName: "Operator",
			filterField: 'DisplayText'
		};
		const inFilterItem = TF.ListFilterHelper.buildListFilterItemBySelectedData(option);
		const filterSet = {
			LogicalOperator: "or",
			FilterItems: [inFilterItem],
			FilterSets: []
		};
		const saveData = {
			FilterSet: filterSet,
			ListFilterIds: (data || []).map(item =>
			{
				return item.ID;
			})
		};
		if (!notSave)
		{
			tf.userPreferenceManager.save(caller._UserPreferenceLocationEventOperatorListMoverFilter, saveData);
			return null;
		}
		return saveData;
	};
})();

(function()
{
	createNamespace('TF').ListLocationHelper = ListLocationHelper;
	function ListLocationHelper() { }

	ListLocationHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'locationlist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListLocationHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceLocationListMoverFilter = TF.ListLocationHelper.buildUserPreferenceKey(gridType);
	};

	ListLocationHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListLocationHelper.getUserPreferenceDataCommon(caller._UserPreferenceLocationListMoverFilter);
	};

	ListLocationHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListLocationHelper.buildUserPreferenceKey(gridType);
		return TF.ListLocationHelper.getUserPreferenceDataCommon(key);
	};

	ListLocationHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListLocationHelper.saveUserPreference = function(data, notSave)
	{
		const schoolLocationIds = (data || []).filter(item => item.Type === "School Location").map(item => item.LocationID);
		const geoRegionIds = (data || []).filter(item => item.Type === "Geo Region").map(item => item.LocationID);
		let schoolLocationFilterSet = null;
		let geoRegionFilterSet = null;
		if (schoolLocationIds.length > 0)
		{
			schoolLocationFilterSet = {
				LogicalOperator: "and",
				FilterItems: [{
					Operator: 'In',
					IsListFilter: true,
					FieldName: "LocationId",
					Value: schoolLocationIds.join(','),
					ValueList: JSON.stringify(schoolLocationIds),
					ListFilterIds: schoolLocationIds
				}, {
					FieldName: "LocationType",
					Operator: "EqualTo",
					Value: "School Location"
				}],
				FilterSets: []
			};
		}
		if (geoRegionIds.length > 0)
		{
			geoRegionFilterSet = {
				LogicalOperator: "and",
				FilterItems: [{
					Operator: 'In',
					IsListFilter: true,
					FieldName: "LocationId",
					Value: geoRegionIds.join(','),
					ValueList: JSON.stringify(geoRegionIds),
					ListFilterIds: geoRegionIds
				}, {
					FieldName: "LocationType",
					Operator: "EqualTo",
					Value: "Geo Region"
				}],
				FilterSets: []
			};
		}
		const filterSet = schoolLocationFilterSet && geoRegionFilterSet ? {
			LogicalOperator: "or",
			FilterItems: [],
			FilterSets: [schoolLocationFilterSet, geoRegionFilterSet]
		} : schoolLocationFilterSet ? schoolLocationFilterSet : geoRegionFilterSet;
		const saveData = {
			FilterSet: filterSet,
			ListFilterIds: (data || []).map(item =>
			{
				return item.Id;
			})
		};
		if (!notSave)
		{
			tf.userPreferenceManager.save(this._UserPreferenceLocationListMoverFilter, saveData);
			return null;
		}
		return saveData;
	};
})();

(function()
{
	createNamespace('TF').ListAuditLogChangedByHelper = ListAuditLogChangedByHelper;
	function ListAuditLogChangedByHelper() { }

	ListAuditLogChangedByHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'auditlogchangedbylist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListAuditLogChangedByHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceAuditLogChangedByListMoverFilter = TF.ListAuditLogChangedByHelper.buildUserPreferenceKey(gridType);
	};

	ListAuditLogChangedByHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListAuditLogChangedByHelper.getUserPreferenceDataCommon(caller._UserPreferenceAuditLogChangedByListMoverFilter);
	};

	ListAuditLogChangedByHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListAuditLogChangedByHelper.buildUserPreferenceKey(gridType);
		return TF.ListAuditLogChangedByHelper.getUserPreferenceDataCommon(key);
	};

	ListAuditLogChangedByHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListAuditLogChangedByHelper.saveUserPreference = function(data)
	{
		const caller = this;
		var listFilterSet = TF.ListAuditLogChangedByHelper.getListFilterSet.call(caller, data);
		listFilterSet && tf.userPreferenceManager.save(caller._UserPreferenceAuditLogChangedByListMoverFilter, listFilterSet);
	};

	ListAuditLogChangedByHelper.getListFilterSet = function (data) {
		const caller = this;
		const innerData = data || caller.selectedData;
		let listFilterSet = undefined;

		listFilterSet = {
			"LogicalOperator": "Or",
			"FilterItems": [],
			"FilterSets": innerData.map(user => ({
				"LogicalOperator": "And",
				"FilterItems": [
					{
						"FieldName": "ChangedBy",
						"Operator": "EqualTo",
						"TypeHint": "String",
						"Value": user.Id
					}
				],
				"FilterSets": [],
				"IsReduceRecordsFilterSet": true
			})),
			"IsReduceRecordsFilterSet": true,
			"ListFilterIds": innerData.map(function (item) { return item.Id; }),
			"ValueList": JSON.stringify(innerData.map(item => ({ userId: item.Id }))),
		}

		return listFilterSet;
	}
})();

(function()
{
	createNamespace('TF').ListAuditLogDataTypeHelper = ListAuditLogDataTypeHelper;
	function ListAuditLogDataTypeHelper() { }

	ListAuditLogDataTypeHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'auditlogdatatypelist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListAuditLogDataTypeHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceAuditLogDataTypeListMoverFilter = TF.ListAuditLogDataTypeHelper.buildUserPreferenceKey(gridType);
	};

	ListAuditLogDataTypeHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListAuditLogDataTypeHelper.getUserPreferenceDataCommon(caller._UserPreferenceAuditLogDataTypeListMoverFilter);
	};

	ListAuditLogDataTypeHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListAuditLogDataTypeHelper.buildUserPreferenceKey(gridType);
		return TF.ListAuditLogDataTypeHelper.getUserPreferenceDataCommon(key);
	};

	ListAuditLogDataTypeHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListAuditLogDataTypeHelper.saveUserPreference = function(data)
	{
		const caller = this;
		var listFilterSet = TF.ListAuditLogDataTypeHelper.getListFilterSet.call(caller, data);
		listFilterSet && tf.userPreferenceManager.save(caller._UserPreferenceAuditLogDataTypeListMoverFilter, listFilterSet);
	};

	ListAuditLogDataTypeHelper.getListFilterSet = function (data) {
		const caller = this;
		const innerData = data || caller.selectedData;
		let listFilterSet = undefined;

		listFilterSet = {
			"LogicalOperator": "Or",
			"FilterItems": [],
			"FilterSets": innerData.map(dataType => ({
				"LogicalOperator": "And",
				"FilterItems": [
					{
						"FieldName": "DataTypeId",
						"Operator": "EqualTo",
						"TypeHint": "String",
						"Value": dataType.Id
					}
				],
				"FilterSets": [],
				"IsReduceRecordsFilterSet": true
			})),
			"IsReduceRecordsFilterSet": true,
			"ListFilterIds": innerData.map(function (item) { return item.Id; }),
			"ValueList": JSON.stringify(innerData),
		}

		return listFilterSet;
	}
})();

(function()
{
	createNamespace('TF').ListAuditLogDataSourceHelper = ListAuditLogDataSourceHelper;
	function ListAuditLogDataSourceHelper() { }

	ListAuditLogDataSourceHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'auditlogdatasourcelist';
		return tf.storageManager.prefix + 'filter.search.' + gridType + '.' + listName;
	};

	ListAuditLogDataSourceHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceAuditLogDataSourceListMoverFilter = TF.ListAuditLogDataSourceHelper.buildUserPreferenceKey(gridType);
	};

	ListAuditLogDataSourceHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListAuditLogDataSourceHelper.getUserPreferenceDataCommon(caller._UserPreferenceAuditLogDataSourceListMoverFilter);
	};

	ListAuditLogDataSourceHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListAuditLogDataSourceHelper.buildUserPreferenceKey(gridType);
		return TF.ListAuditLogDataSourceHelper.getUserPreferenceDataCommon(key);
	};

	ListAuditLogDataSourceHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListAuditLogDataSourceHelper.saveUserPreference = function(data)
	{
		const caller = this;
		var listFilterSet = TF.ListAuditLogDataSourceHelper.getListFilterSet.call(caller, data);
		listFilterSet && tf.userPreferenceManager.save(caller._UserPreferenceAuditLogDataSourceListMoverFilter, listFilterSet);
	};

	ListAuditLogDataSourceHelper.getListFilterSet = function (data) {
		const caller = this;
		const innerData = data || caller.selectedData;
		let listFilterSet = undefined;

		listFilterSet = {
			"LogicalOperator": "Or",
			"FilterItems": [],
			"FilterSets": innerData.map(dataSource => ({
				"LogicalOperator": "And",
				"FilterItems": [
					{
						"FieldName": "DBID",
						"Operator": "EqualTo",
						"TypeHint": "String",
						"Value": dataSource.DBID
					}
				],
				"FilterSets": [],
				"IsReduceRecordsFilterSet": true
			})),
			"IsReduceRecordsFilterSet": true,
			"ListFilterIds": innerData.map(function (item) { return item.DBID; }),
			"ValueList": JSON.stringify(innerData),
		}

		return listFilterSet;
	}
})();

(function()
{
	createNamespace('TF').ListAuditLogEventTypeHelper = ListAuditLogEventTypeHelper;
	function ListAuditLogEventTypeHelper() { }

	ListAuditLogEventTypeHelper.buildUserPreferenceKey = function(gridType)
	{
		var listName = 'auditlogeventtypelist';
		return tf.storageManager.prefix + 'dashboard.viewie.' + gridType + '.' + listName;
	};

	ListAuditLogEventTypeHelper.initUserPreferenceKey = function(gridType)
	{
		var caller = this;
		caller._UserPreferenceAuditLogEventTypeListMoverFilter = TF.ListAuditLogEventTypeHelper.buildUserPreferenceKey(gridType);
	};

	ListAuditLogEventTypeHelper.getUserPreferenceData = function()
	{
		var caller = this;
		return TF.ListAuditLogEventTypeHelper.getUserPreferenceDataCommon(caller._UserPreferenceAuditLogEventTypeListMoverFilter);
	};

	ListAuditLogEventTypeHelper.getUserPreferenceDataByKey = function(gridType)
	{
		var key = TF.ListAuditLogEventTypeHelper.buildUserPreferenceKey(gridType);
		return TF.ListAuditLogEventTypeHelper.getUserPreferenceDataCommon(key);
	};

	ListAuditLogEventTypeHelper.getUserPreferenceDataCommon = function(userPreferenceKey)
	{
		return tf.userPreferenceManager.get(userPreferenceKey);
	};

	ListAuditLogEventTypeHelper.saveUserPreference = function(data)
	{
		const caller = this;
		var listFilterSet = TF.ListAuditLogEventTypeHelper.getListFilterSet.call(caller, data);
		listFilterSet && tf.userPreferenceManager.save(caller._UserPreferenceAuditLogEventTypeListMoverFilter, listFilterSet);
	};

	ListAuditLogEventTypeHelper.getListFilterSet = function (data) {
		const caller = this;
		const innerData = data || caller.selectedData;
		let listFilterSet = undefined;

		listFilterSet = {
			"LogicalOperator": "Or",
			"FilterItems": [],
			"FilterSets": innerData.map(eventType => ({
				"LogicalOperator": "And",
				"FilterItems": [
					{
						"FieldName": "EventTypeId",
						"Operator": "EqualTo",
						"TypeHint": "String",
						"Value": eventType.Id
					}
				],
				"FilterSets": [],
				"IsReduceRecordsFilterSet": true
			})),
			"IsReduceRecordsFilterSet": true,
			"ListFilterIds": innerData.map(function (item) { return item.Id; }),
			"ValueList": JSON.stringify(innerData),
		}

		return listFilterSet;
	}
})();

(function()
{
	var timeControlHelper = TF.TimeRangeWithWeekDayControlHelper;
	var listMoverControlHelper = TF.ListMoverControlHelper;
	var listGPSEventTypeHelper = TF.ListGPSEventTypeHelper;
	var listLocationEventStatusHelper = TF.ListLocationEventStatusHelper;
	var listLocationEventOperatorHelper = TF.ListLocationEventOperatorHelper;
	var listAuditLogChangedByHelper = TF.ListAuditLogChangedByHelper;
	var listAuditLogDataTypeHelper = TF.ListAuditLogDataTypeHelper;
	var listAuditLogDataSourceHelper = TF.ListAuditLogDataSourceHelper;
	var listAuditLogEventTypeHelper = TF.ListAuditLogEventTypeHelper;
	var listLocationHelper = TF.ListLocationHelper;

	createNamespace('TF.Control').ListMoverForListFilterWithSelectDateTimeRangeControlViewModel = ListMoverForListFilterWithSelectDateTimeRangeControlViewModel;
	function ListMoverForListFilterWithSelectDateTimeRangeControlViewModel(selectedData, options)
	{
		selectedData = null;

		var self = this;
		self.options = options;
		self.validationMessage = null;
		self.pageLevelViewModel = new TF.PageLevel.ListMoverForListFilterWithSelectDateTimeRangeControlPageLevelViewModel(self);

		var userPreferenceKey = options.parentPageName + '.listFilterWithSelectDateTimeRange';

		timeControlHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		timeControlHelper.initFilterTimeRange.bind(self)({
			startDate: moment(),
			endDate: moment(),
			startTime: moment().format('YYYY-MM-DD 00:00:00'),
			endTime: moment().format('YYYY-MM-DD HH:mm:00'),
			useToday: !!self.options.disableSticky
		});
		timeControlHelper.initFilterWeekdays.bind(self)();

		listMoverControlHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listGPSEventTypeHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listLocationEventStatusHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listLocationEventOperatorHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listLocationHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);		
		listAuditLogChangedByHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listAuditLogDataTypeHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listAuditLogDataSourceHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);
		listAuditLogEventTypeHelper.initUserPreferenceKey.bind(self)(userPreferenceKey);

		var cachedSelectedVehicles = TF.ListMoverControlHelper.getUserPreferenceData.call(this);
		self.obHasCachedSelecedVehicles = ko.observable(cachedSelectedVehicles);
		self.obDisplayCurrentlyLive = ko.observable(false);

		self.obIsLocationEvent = ko.observable(this.options.parentPageName === "locationevent");
		self.obIsAuditLog = ko.observable(this.options.parentPageName === "auditlog");
		self.obSelectorCss = ko.observable(self.obIsLocationEvent() ? "col-xs-6" : self.obIsAuditLog() ? "col-xs-6" : "col-xs-12");

		//Operator
		self.obSelectedLocationEventOperators = ko.observableArray();
		self.allLocationEventOperators = TF.Grid.LocationEventGridViewModel ? TF.Grid.LocationEventGridViewModel.AllLocationEventOperators : [];

		let selectedOperatorData;
		if (self.obIsLocationEvent())
		{
			if (self.options.defaultSelectedLocationEventOperators)
			{
				selectedOperatorData = self.options.defaultSelectedLocationEventOperators;
			}
			else
			{
				selectedOperatorData = TF.ListLocationEventOperatorHelper.getUserPreferenceData.call(self);
			}
		}

		let selectedLocationEventOperator = selectedOperatorData ? self.allLocationEventOperators.filter(function(item)
		{
			return selectedOperatorData.ListFilterIds && selectedOperatorData.ListFilterIds.indexOf(item.Id) > -1;
		}) : [];
		selectedLocationEventOperator = selectedLocationEventOperator.length > 0 ? selectedLocationEventOperator : self.allLocationEventOperators;
		self.obSelectedLocationEventOperators(selectedLocationEventOperator.sort((a, b) => a.DisplayText.localeCompare(b.DisplayText)));
		self.obSelectedLocationEventOperatorDisplay = ko.computed(function()
		{
			return self.obSelectedLocationEventOperators().length !== self.allLocationEventOperators.length
				? self.obSelectedLocationEventOperators() : [{ DisplayText: 'All' }];
		});

		//On-Time.
		self.obSelectedLocationEventStatus = ko.observableArray();
		self.allLocationEventStatus = TF.Grid.LocationEventGridViewModel ? TF.Grid.LocationEventGridViewModel.AllLocationEventStatus : [];
		let selectedStatusData;

		if (self.obIsLocationEvent())
		{
			if (self.options.defaultSelectedLocationEventStatus)
			{
				selectedStatusData = self.options.defaultSelectedLocationEventStatus;
			}
			else
			{
				selectedStatusData = TF.ListLocationEventStatusHelper.getUserPreferenceData.call(self);
			}
		}
		let selectedLocationEventStatus = selectedStatusData ? self.allLocationEventStatus.filter(function(item)
		{
			return selectedStatusData.ListFilterIds && selectedStatusData.ListFilterIds.indexOf(item.Id) > -1;
		}) : [];
		selectedLocationEventStatus = selectedLocationEventStatus.length > 0 ? selectedLocationEventStatus : self.allLocationEventStatus;
		self.obSelectedLocationEventStatus(selectedLocationEventStatus.sort((a, b) => a.DisplayText.localeCompare(b.DisplayText)));
		self.obSelectedLocationEventStatusDisplay = ko.computed(function()
		{
			return self.obSelectedLocationEventStatus().length !== self.allLocationEventStatus.length
				? self.obSelectedLocationEventStatus() : [{ DisplayText: 'All' }];
		});

		//Location
		self.obSelectedLocations = ko.observableArray();
		self.obAllLocationsCount = ko.observable(0);
		self.obSelectedLocationsDisplay = ko.computed(function()
		{
			return self.obSelectedLocations();
		});
		if (self.obIsLocationEvent())
		{
			if (self.options.disableSticky && self.options.defaultGeoRegionIds)
			{
				tf.promiseAjax.post(TF.ListFilterDefinition.ListFilterTemplate.Location.getUrl(), {
					data: {
						FilterSet: {
							FilterItems: [{
								FieldName: "Type",
								Operator: "EqualTo",
								Value: "Geo Region",
							}, {
								Operator: 'In',
								IsListFilter: true,
								FieldName: "LocationID",
								Value: self.options.defaultGeoRegionIds.join(','),
								ValueList: JSON.stringify(self.options.defaultGeoRegionIds)
							}],
							FilterSets: [],
							LogicalOperator: "and",
						},
						IdFilter: {
							ExcludeAny: [], IncludeOnly: null
						}
					}
				}).then(result =>
				{
					const selectedLocations = result.Items.sort((a, b) => a.Name.localeCompare(b.Name));
					self.obSelectedLocations(selectedLocations);
					self.obAllLocationsCount(result.TotalRecordCount);
				});
			}
			else
			{
				let selectedLocationData;
				if (self.options.defaultSelectedLocations)
				{
					selectedLocationData = self.options.defaultSelectedLocations;
				}
				else
				{
					selectedLocationData = self.options.disableSticky ? null : TF.ListLocationHelper.getUserPreferenceData.call(self);
				}
				if (selectedLocationData && selectedLocationData.ListFilterIds && selectedLocationData.ListFilterIds.length > 0)
				{
					tf.promiseAjax.post(TF.ListFilterDefinition.ListFilterTemplate.Location.getUrl(), {
						data: {
							IdFilter: { IncludeOnly: selectedLocationData.ListFilterIds || [] }
						}
					}).then(result =>
					{
						const selectedLocations = result.Items.filter(item => selectedLocationData.ListFilterIds.some(id => item.Id === id)).sort((a, b) => a.Name.localeCompare(b.Name));
						self.obSelectedLocations(selectedLocations);
						self.obAllLocationsCount(result.TotalRecordCount);
					});
				}
				else
				{
					tf.promiseAjax.post(pathCombine(TF.ListFilterDefinition.ListFilterTemplate.Location.getUrl(), "count")).then(result =>
					{
						self.obAllLocationsCount((result.Items || []).length === 1 ? result.Items[0] : 0);
					});
				}
			}
		}

		// query gps event type by listConfig
		self.obSelectedGPSEventType = ko.observableArray();
		self.obAllGPSEventsCount = ko.observable(0);
		if (!(self.obIsLocationEvent() || self.obIsAuditLog()))
		{
			self.getSelectedEventTypes('GPSEventType', TF.ListFilterDefinition.ListFilterTemplate.GPSEventType)
				.then(function(result)
				{
					result.Items.sort(function(a, b) { return a.DisplayText.localeCompare(b.DisplayText); });

					var selectedData = self.options.disableSticky ? listGPSEventTypeHelper.getListFilterItem.call(self, self.options.defaultGPSEvent || []) : TF.ListGPSEventTypeHelper.getUserPreferenceData.call(self);
					var selectedTypes = selectedData ? result.Items.filter(function(item)
					{
						return selectedData.ListFilterIds.indexOf(item.Id) > -1;
					}) : [];
					self.obSelectedGPSEventType(selectedTypes.length > 0 ? selectedTypes : result.Items);
					self.obAllGPSEventsCount(result.TotalRecordCount);
				});
		}

		self.obSelectedGPSEventTypeDisplay = ko.computed(function()
		{
			return self.obSelectedGPSEventType().length !== self.obAllGPSEventsCount() ?
				self.obSelectedGPSEventType().sort((a, b) => a.EventTypeName.localeCompare(b.EventTypeName)) :
				[{ EventTypeName: 'All' }];
		});

		//data source for audit
		self.obSelectedDataSources = ko.observableArray();

		// query gps event type by listConfig
		self.obSelectedVehicles = ko.observableArray();
		self.obSelectedDataTypes = ko.observableArray();
		self.obSelectedAuditLogEventTypes = ko.observableArray();
		self.obSelectedAuditLogChangedBy = ko.observableArray();
		if (self.obIsAuditLog()) {
			function getValueFromAuditSelectedData(selectedData) {
				return selectedData && selectedData.ValueList ? JSON.parse(selectedData.ValueList) : [];
			}
			var selectedAuditLogDataSourceData = listAuditLogDataSourceHelper.getUserPreferenceData.call(self);
			self.obSelectedDataSources(getValueFromAuditSelectedData(selectedAuditLogDataSourceData));
			var selectedAuditLogDataTypeData = listAuditLogDataTypeHelper.getUserPreferenceData.call(self);
			self.obSelectedDataTypes(getValueFromAuditSelectedData(selectedAuditLogDataTypeData));
			var selectedAuditLogEventTypeData = listAuditLogEventTypeHelper.getUserPreferenceData.call(self);
			self.obSelectedAuditLogEventTypes(getValueFromAuditSelectedData(selectedAuditLogEventTypeData));
			var selectedAuditLogChangedByData = listAuditLogChangedByHelper.getUserPreferenceData.call(self);
			TF.Helper.AuditLogHelper.getSelectedChangedByUsers(selectedAuditLogChangedByData)
				.then(function (result) {
					var selectedTypes = selectedAuditLogChangedByData ? result.Items.filter(function (item) {
						return selectedAuditLogChangedByData.ListFilterIds.indexOf(item.Id) > -1;
					}) : [];
					self.obSelectedAuditLogChangedBy(selectedTypes.length > 0 ? selectedTypes : result.Items);
				});
		}
		else {
			self.getSelectedVehicles('VehicleExternalName', TF.ListFilterDefinition.ListFilterTemplate.BusfinderHistoricalVehicle)
				.then(function (result) {
					result.Items.sort(function (a, b) { return a.BusNum.localeCompare(b.BusNum); });
					if (TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled) {
						self.SetPlusSelectedVehicles(result.Items);
					} else {
						self.setSelectedVehicles(result.Items);
					}
				});
		}

		self.obErrorMessageDivIsShow = ko.observable(false);
		self.obErrorMessage = ko.observable('');
		self.obValidationErrors = ko.observableArray([]);
		self.obErrorMessageTitle = ko.observable("Error Occurred");
		self.obErrorMessageDescription = ko.observable("The following error occurred.");
		self.obOnTimeCountInfo = ko.computed(() =>
		{
			return `(${self.obSelectedLocationEventStatus().length})`;
		});
		self.obEventTypeCountInfo = ko.computed(() =>
		{
			return `(${self.obSelectedGPSEventType().length})`;
		});
		self.obVehicleCountInfo = ko.computed(() =>
		{
			return `(${self.obSelectedVehicles().length})`;
		});
		self.obLocationCountInfo = ko.computed(() =>
		{
			return `(${self.obSelectedLocations().length})`;
		});
		self.obOperatorCountInfo = ko.computed(() =>
		{
			return `(${self.obSelectedLocationEventOperators().length})`;
		});
		self.obDataTypesCountInfo = ko.computed(() => {
			return `(${self.obSelectedDataTypes().length})`;
		});
		self.obDataSourcesCountInfo = ko.computed(() => {
			return `(${self.obSelectedDataSources().length})`;
		});
		self.obAuditLogEventTypeCountInfo = ko.computed(() => {
			return `(${self.obSelectedAuditLogEventTypes().length})`;
		});
		self.obAuditLogChangedByCountInfo = ko.computed(() => {
			return `(${self.obSelectedAuditLogChangedBy().length})`;
		});
	}

	//ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.constructor = ListMoverForListFilterWithSelectDateTimeRangeControlViewModel;

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.columnSources = TF.ListFilterDefinition.ColumnSource;

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.SetPlusSelectedVehicles = function(result)
	{
		var selectedData = this.options.disableSticky ? listMoverControlHelper.getListFilterSet.call(this, null, null, this.options.defaultVehicles) : TF.ListMoverControlHelper.getUserPreferenceData.call(this);
		let selectedVehicles = [];

		// when legacy GPS switch to new GPS, the saved selected vehicle(from userPreference) is not available for new GPS.
		if (selectedData && selectedData.ValueList)
		{
			var legacyGpsVehicleFilter = typeof (selectedData.ValueList) === "string" && selectedData.ValueList.indexOf("vendorId") < 0 && selectedData.ValueList.indexOf("gpsId") < 0;
			var vehicleFilters = [];
			if (!legacyGpsVehicleFilter)
			{
				vehicleFilters = typeof (selectedData.ValueList) === "string" ? JSON.parse(selectedData.ValueList) : selectedData.ValueList;
			}

			selectedVehicles = result.filter((item) =>
			{
				const includeId = selectedData.ListFilterIds.indexOf(item.Id) > -1;
				// it's legacy saved invalid filter, to legacy filter, no vehicle be be applied in vehicle list on the modal of GPS Events filter
				if (legacyGpsVehicleFilter)
				{
					return false;
				}
				return includeId && vehicleFilters.some(gpsIdAndVendorId =>
				{
					if (!gpsIdAndVendorId.gpsId && !gpsIdAndVendorId.vendorId)
					{
						return false;
					}
					return gpsIdAndVendorId.gpsId === item.Gpsid && item.VendorId && gpsIdAndVendorId.vendorId === item.VendorId;
				});
			});
		}

		this.obSelectedVehicles(selectedVehicles);
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.getAllGPSEvents = function()
	{
		return TF.Helper.VehicleEventHelper.getEventTypes(null, this.obIsLocationEvent());
	};

	/*
	 * restore selected vehicle on legacy GPS Event grid.
	 */
	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.setSelectedVehicles = function(result)
	{
		var selectedData = this.options.disableSticky ? listMoverControlHelper.getListFilterSet.call(this, null, null, this.options.defaultVehicles) : TF.ListMoverControlHelper.getUserPreferenceData.call(this);
		let selectedVehicles = [];
		var listFilterIds = selectedData?.ListFilterIds || (selectedData?.FilterItems.length > 0 ? selectedData.FilterItems[0].ListFilterIds : []) || [];
		// when new GPS switch to legacy GPS, the saved selected vehicle is also not available for legacy GPS
		if (listFilterIds)
		{
			selectedVehicles = selectedData ? result.filter(item => listFilterIds.some(id => id === item.Id)) : [];
		}

		this.obSelectedVehicles(selectedVehicles);
	}

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.initGridScrollBar = function(container)
	{
		//need check soon.
		var $gridContent = container.find('.k-grid-content');
		$gridContent.css({
			'overflow-y': 'auto'
		});

		if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
		{
			$gridContent.find('colgroup col:last').css({
				width: 137
			});
		}
		else
		{
			$gridContent.find('colgroup col:last').css({
				width: 120
			});
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.apply = function()
	{
		var self = this;
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(self);

		return self.trySave()
			.then(function(valid)
			{
				if (!valid)
				{
					return Promise.reject();
				}
				else
				{
					// return Promise.reject();

					if (!self.options.disableSticky)
					{
						timeControlHelper.saveUserPreference.call(self);
						listMoverControlHelper.saveUserPreference.call(self, null, null, self.obSelectedVehicles());
					}
					const result = {};
					if (self.options.disableSticky)
					{
						result.selectedTimeRange = timeControlHelper.getTimeRangeData.call(self, true);
						result.selectedVehicles = self.obSelectedVehicles();
						result.selectedVehicleListItems = listMoverControlHelper.getListFilterSet.call(self, null, null, self.obSelectedVehicles());
						result.selectedGPSEventTypeListItems = listGPSEventTypeHelper.getListFilterItem.call(self, self.obSelectedGPSEventType());
					}

					if (self.obIsLocationEvent())
					{
						result.selectedLocationEventStatus = listLocationEventStatusHelper.saveUserPreference.call(self, self.obSelectedLocationEventStatus(), self.options.disableSticky);
						result.selectedLocationEventOperators = listLocationEventOperatorHelper.saveUserPreference.call(self, self.obSelectedLocationEventOperators(), self.options.disableSticky);
						result.selectedLocations = listLocationHelper.saveUserPreference.call(self, self.obSelectedLocations(), self.options.disableSticky);
					} else if (self.obIsAuditLog()) {
						listAuditLogDataTypeHelper.saveUserPreference.call(self, self.obSelectedDataTypes());
						listAuditLogDataSourceHelper.saveUserPreference.call(self, self.obSelectedDataSources());
						listAuditLogEventTypeHelper.saveUserPreference.call(self, self.obSelectedAuditLogEventTypes());
						listAuditLogChangedByHelper.saveUserPreference.call(self, self.obSelectedAuditLogChangedBy());
						result.selectedDataTypes = self.obSelectedDataTypes();
						result.selectedDataSources = self.obSelectedDataSources();
						result.selectedAuditLogEventTypes = self.obSelectedAuditLogEventTypes();
						result.selectedChangedByUsers = self.obSelectedAuditLogChangedBy();
					} else 
					{
						if (!self.options.disableSticky)
						{
							listGPSEventTypeHelper.saveUserPreference.call(self, self.obSelectedGPSEventType());
						}

						result.selectedGPSEventType = self.obSelectedGPSEventType();
						result.allGPSEventsCount = self.obAllGPSEventsCount();
					}

					// return Promise.resolve(
					// 	self.buildResult(self.selectedData, self.obStartDate(), self.obEndDate(), self.obStartTime(), self.obEndTime())
					// );
					return Promise.resolve(result);
				}
			}.bind(self));
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.trySave = function(disablePubsub)
	{
		var self = this;
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
					return Promise.reject();
				else
					return self.tooManyRecordsValidate.bind(self)();
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.tooManyRecordsValidate = function()
	{
		if (!this.options.tooManyRecordsValidate)
			return Promise.resolve(true);

		return this.options.tooManyRecordsValidate.bind(this)();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.dispose = function()
	{
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.dispose.call(this);
		if (this.validationMessage && this.validationMessage.length > 0)
		{
			this.validationMessage.remove();
		}
		this.pageLevelViewModel.dispose();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.cancel = function()
	{
		// var cachedSelectedVehicles = TF.ListMoverControlHelper.getUserPreferenceData.call(this);
		// if (!cachedSelectedVehicles)
		// {
		// 	// this.pageLevelViewModel.popupErrorMessage("At least one vehicles must be selected");
		// 	return Promise.reject();
		// }
		// else
		// {
		if (this.validationMessage && this.validationMessage.length > 0)
		{
			this.validationMessage.remove();
		}
		return new Promise(function(resolve, reject)
		{
			//resolve(this.buildResult(false));
			resolve(false);
		}.bind(this));
		// }
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype._settingFilterChange = function()
	{
		// timeControlHelper.saveUserPreference.bind(this)();
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		//TF.Control.KendoListMoverWithSearchControlViewModel.prototype.init.call(this, viewModel, el);
		// add setTimeout for make validationInitialize later than datePicker controls init finished
		setTimeout(function()
		{
			// if (!self.obHasCachedSelecedVehicles())
			// {
			// 	var $container = $(el).closest('.modal-content');
			// 	$container.find('.modal-header button.close').remove();
			// 	$container.find('.modal-header button.close').remove();
			// 	$container.find('.modal-footer button.btn-link').remove();
			// }

			viewModel.validationInitialize(viewModel, el);
		}.bind(this), 1000);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.validationInitialize = function(viewModel, el)
	{
		var self = this;
		self._$form = $(el);

		if (self._$form.closest(".tfmodal-container").length > 0)
		{
			self.validationMessage = self._$form.closest(".tfmodal-container").find(".page-level-message-container");
			self.validationMessage.css("z-index", self._$form.closest(".tfmodal.modal").css("z-index"));
			$("body").append(self.validationMessage);
		}

		var validatorFields = {};
		var isValidating = false;
		validatorFields.startDate = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				}
			}
		};

		validatorFields.endDate = {
			trigger: "blur change",
			validators: {

				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date'
				}
			}
		};

		validatorFields.startTime = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'invalid time',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value, 'h:mm A', true);
						return m.isValid();
					}.bind(this)
				}
			}
		};

		validatorFields.endTime = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'invalid time',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value, 'h:mm A', true);
						return m.isValid();
					}.bind(this)
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			})
			.on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));

	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectGPSEventType = function()
	{
		var self = this;

		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.GPSEventType;
		var selectedItems = self.obSelectedGPSEventType();
		listFilterTemplate.FullDisplayFilterTypeName = 'Select Event Types';

		return tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		)
			.then(function(selectedFilterItems)
			{
				if (selectedFilterItems !== false)
					self.obSelectedGPSEventType(selectedFilterItems);
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectLocationEventStatusClick = function()
	{
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.LocationEventOnTime;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			serverPaging: false
		});
		listFilterTemplate.FullDisplayFilterTypeName = "Select On-Time Status";

		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(self.obSelectedLocationEventStatus().sort((a, b) => a.DisplayText.localeCompare(b.DisplayText)), listFilterTemplate)
		).then(function(selectedFilterItems)
		{
			if (selectedFilterItems !== false)
			{
				self.obSelectedLocationEventStatus(selectedFilterItems.sort((a, b) => a.DisplayText.localeCompare(b.DisplayText)));
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectLocationEventOperatorClick = function()
	{
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.LocationEventOperator;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			serverPaging: false
		});
		listFilterTemplate.FullDisplayFilterTypeName = "Select Operator";

		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(self.obSelectedLocationEventOperators().sort((a, b) => a.DisplayText.localeCompare(b.DisplayText)), listFilterTemplate)
		).then(function(selectedFilterItems)
		{
			if (selectedFilterItems !== false)
			{
				self.obSelectedLocationEventOperators(selectedFilterItems.sort((a, b) => a.DisplayText.localeCompare(b.DisplayText)));
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectLocationClick = function()
	{
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.Location;
		listFilterTemplate.FullDisplayFilterTypeName = "Select Locations";

		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(self.obSelectedLocations().sort((a, b) => a.Name.localeCompare(b.Name)), listFilterTemplate)
		).then(function(selectedFilterItems)
		{
			if (selectedFilterItems !== false)
			{
				self.obSelectedLocations(selectedFilterItems.sort((a, b) => a.Name.localeCompare(b.Name)));
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectGPSEventTypeClick = function()
	{
		var self = this;

		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.GPSEventType;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			serverPaging: false,
			getUrl: function()
			{
				return TF.Helper.VehicleEventHelper.getEventTypeUrl();
			}
		});

		listFilterTemplate.parentPageName = self.options.parentPageName;
		if (self.options.parentPageName === "locationevent")
		{
			self.obSelectedGPSEventType().sort(x => x.Order);
		}

		var selectedItems = self.obSelectedGPSEventType();
		listFilterTemplate.FullDisplayFilterTypeName = "Select Event Types";

		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		).then(function(selectedFilterItems)
		{
			if (selectedFilterItems !== false)
			{
				self.obSelectedGPSEventType(selectedFilterItems);
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectVehicle = function()
	{
		var self = this;

		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.BusfinderHistoricalVehicle;
		listFilterTemplate.DisplayFilterTypeName = "Vehicles";
		listFilterTemplate.FullDisplayFilterTypeName = 'Select Vehicles';
		var selectedItems = self.obSelectedVehicles();

		return tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		)
			.then(function(selectedFilterItems)
			{
				if (selectedFilterItems !== false)
					self.obSelectedVehicles(selectedFilterItems);
			});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectVehicleClick = function()
	{
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.BusfinderHistoricalVehicle;
		var selectedItems = self.obSelectedVehicles();

		listFilterTemplate = $.extend({}, listFilterTemplate, {
			serverPaging: false,
			setLeftGridRequestURL: null,
			DisplayFilterTypeName: "Vehicles",
			FullDisplayFilterTypeName: "Select Vehicles",
			filterField: "BusNum",
			type: "Vehicle",
			displayCheckbox: false,
			filterCheckboxText: "",
			getUrl: function()
			{
				return pathCombine(tf.api.apiPrefix(), "search", "vehicles");
			}
		});

		listFilterTemplate.setRequestOption = function(option)
		{
			return self.setRequestOption(option);
		};

		const requestOptions = listFilterTemplate.appendNecessaryRequestOptions(self.setRequestOption({ data: {} }));

		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), 'search', 'vehicles', "id"), requestOptions).then(response =>
		{
			listFilterTemplate.totalRecordCount = response.TotalRecordCount;
			tf.modalManager.showModal(
				new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
			).then(function(selectedFilterItems)
			{
				if (selectedFilterItems !== false)
				{
					if (TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled)
					{
						const vehicles = selectedFilterItems.filter(x => x.VendorId !== null);
						self.obSelectedVehicles(vehicles);
					} else
					{
						self.obSelectedVehicles(selectedFilterItems);
					}

				}
			});
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.vehicleFormatter = function(item)
	{
		if (item.Gpsid)
		{
			return String.format("{0} ({1})", item.BusNum, item.Gpsid);
		}

		return String.format("{0}", item.BusNum);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectDataTypeClick = function () {
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.AuditLogDataType;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			showRemoveColumnButton: false
		});
		var selectedItems = self.obSelectedDataTypes();
		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		).then(function (selectedFilterItems) {
			if (selectedFilterItems !== false) {
				self.obSelectedDataTypes(selectedFilterItems);
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectDataSourceClick = function () {
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.DataSources;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			showRemoveColumnButton: false
		});
		var selectedItems = self.obSelectedDataSources();
		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		).then(function (selectedFilterItems) {
			if (selectedFilterItems !== false) {
				self.obSelectedDataSources(selectedFilterItems);
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.auditDataSourceFormatter = function (item) {
		return String.format("{0}", item.Name);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.auditDataTypeFormatter = function (item) {
		return String.format("{0}", item.TableName);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectAuditLogEventTypeClick = function () {
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.AuditLogEventType;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			showRemoveColumnButton: false
		});
		var selectedItems = self.obSelectedAuditLogEventTypes();
		tf.modalManager.showModal(
			new TF.Modal.ListMoverForListFilterControlModalViewModel(selectedItems, listFilterTemplate)
		).then(function (selectedFilterItems) {
			if (selectedFilterItems !== false) {
				self.obSelectedAuditLogEventTypes(selectedFilterItems);
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.auditLogEventTypeFormatter = function (item) {
		return String.format("{0}", item.EventName);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.selectAuditLogChangedByClick = function () {
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.AuditLogChangedBy;
		listFilterTemplate = $.extend({}, listFilterTemplate, {
			modifySource: function (source) {
				return Enumerable.From(source).OrderBy("$.LoginId").ThenBy("$.LastName").ThenBy("$.FirstName").ToArray();
			}
		});
		var selectedItems = self.obSelectedAuditLogChangedBy();
		tf.modalManager.showModal(
			new TF.Modal.ListMoverSelectRecipientControlModalViewModel(selectedItems, listFilterTemplate)
		).then(function (selectedFilterItems) {
			if (selectedFilterItems !== false) {
				self.obSelectedAuditLogChangedBy(selectedFilterItems);
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.auditLogChangedByFormatter = function (item) {
		return String.format("{0}", item.LoginID);
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.setRequestOption = function(option)
	{
		var self = this;
		if (self.options.isHistorical)
		{
			return $.extend(option, {
				data: {
					StartTime: self.createDateTime(self.obStartTime()),
					EndTime: self.createDateTime(self.obEndTime()),
					EventTypeIds: self.obSelectedGPSEventType().map(function(type) { return type.Id; })
				}
			});
		}
		if (self.obDisplayCurrentlyLive())
		{
			var now = moment().format("YYYY-MM-DDTHH:mm:ss");
			var end = moment().add(-Math.max(self.obSelectedTrail().value, TF.RoutingMap.GPSPalette.LiveDataModel.nowTolerance), "m").format("YYYY-MM-DDTHH:mm:ss");

			return $.extend(option, {
				data: {
					StartTime: end,
					EndTime: now,
					EventTypeIds: self.obSelectedGPSEventType().map(function(type) { return type.Id; })
				}
			});
		}
		return $.extend(true, option, {
			data: {
				EventTypeIds: self.obSelectedGPSEventType().map(function(type) { return type.Id; })
			}
		});
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.eventTypeFormatter = function(item)
	{
		return item.EventTypeName;
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.getSelectedEventTypes = function(fieldName, listFilterTemplate)
	{
		var self = this;

		if (self.options.disableSticky)
		{
			return self.getAllGPSEvents();
		}

		var selectedData = listGPSEventTypeHelper.getUserPreferenceData.call(self);

		if (!selectedData || !selectedData.ListFilterIds || !selectedData.ListFilterIds.length)
		{
			return self.getAllGPSEvents();
		}
		else if (fieldName == "GPSEventType")
		{
			return self.getAllGPSEvents();
		}
		else
		{
			var requestUrl = listFilterTemplate.getUrl();
			var option = {
				data: {
					FilterClause: "",
					IdFilter: { IncludeOnly: selectedData.ListFilterIds }
				}
			};
			return tf.promiseAjax.post(requestUrl, option);
		}
	};

	ListMoverForListFilterWithSelectDateTimeRangeControlViewModel.prototype.getSelectedVehicles = function(fieldName, listFilterTemplate)
	{
		var self = this;
		var selectedData = self.options.disableSticky ? {} : listMoverControlHelper.getUserPreferenceData.call(self);

		var requestUrl = pathCombine(tf.api.apiPrefix(), "search", "vehicles");

		if (!selectedData)
			return Promise.resolve({ Items: [] });

		return tf.promiseAjax.post(requestUrl, {
			data: {
				FilterClause: "",
				IdFilter: { IncludeOnly: selectedData.ListFilterIds }
			}
		});
	};

})();
