(function()
{
	createNamespace('TF.Control').SelectResidenceSchoolViewModel = SelectResidenceSchoolViewModel;

	function SelectResidenceSchoolViewModel(residence)
	{
		var self = this,
			student = residence.Student,
			mailStreet = (student.MailStreet1 ? student.MailStreet1 + " " : "") + student.MailStreet2,
			mailStreetExpand = (student.MailCity ? student.MailCity + ", " : "") + (student.MailState ? student.MailState + " " : "") + student.MailZip,
			geoStreetExpand = (student.GeoCity ? student.GeoCity + ", " : "") + student.GeoZip;

		self.obMailingAddress = ko.observable(mailStreet);
		self.obMailingAddressExpand = ko.observable(mailStreetExpand)
		self.obGeocodeAddress = ko.observable(student.GeoStreet);
		self.obGeocodeAddressExpand = ko.observable(geoStreetExpand);
		self.obGrade = ko.observable(student.Grade);

		self.obSchools = ko.observable(residence.Schools);
		self.obSelectSchool = ko.observable();
		self.obSelectSchoolText = ko.observable();

		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	};

	/**
	 * Initialize the update information modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	SelectResidenceSchoolViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.$element = $(el);

		var validatorFields = {}, isValidating = false;

		validatorFields.school = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				}
			}
		};


		setTimeout(function()
		{
			self.$element.bootstrapValidator({
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});

			self.pageLevelViewModel.load(self.$element.data("bootstrapValidator"));
		});
	};

	/**
	 * Apply all changed record into db.
	 * @return {void}
	 */
	SelectResidenceSchoolViewModel.prototype.apply = function()
	{
		var self = this;

		return self.pageLevelViewModel.saveValidate().then(function(result)
		{
			if (result)
			{
				return self.obSelectSchool();
			}
		});
	};

	/**
	 * Dispose.
	 * @return {void}
	 */
	SelectResidenceSchoolViewModel.prototype.dispose = function()
	{
		var self = this;
		self.pageLevelViewModel.dispose();
	};


})();

