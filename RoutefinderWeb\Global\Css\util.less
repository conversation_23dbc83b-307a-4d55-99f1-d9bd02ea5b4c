﻿@import "z-index";

textarea {
	resize: none !important;
}

.clearafter::after {
	content: '';
	clear: both;
	display: block;
}

.relative {
	position: relative;
}

.rotate90 {
	transform: rotate(90deg);
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	-o-transform: rotate(90deg);
	-moz-transform: rotate(90deg);
	display: block;
}

.fs-14 {
	font-size: 14px;
}

.fs-12 {
	font-size: 12px;
}

.dashboard-widget-loading,
#loadingindicator {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: @route-loadingindicator-z-index;

	&.no-overlay {
		position: initial;

		>.overlay {
			position: initial;
		}
	}
}

#open-datasouce-loading>.overlay,
#loadingindicator>.overlay,
.dashboard-widget-loading>.overlay {
	background: rgba(102, 102, 102, 0.8);
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
}

.dashboard-widget-loading .spinner,
#loadingindicator .spinner {
	position: absolute;
	margin: auto;
	left: 0;
	right: 0;
	top: 40%;
	overflow: visible;
	opacity: 0.8;
	border-radius: 2px;
	display: flex;
	width: 148px;
	height: auto;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

#loadingindicator .spinner-icon,
.dashboard-widget-loading .spinner-icon {
	display: flex;
}

#loadingindicator .spinner-icon-container,
.dashboard-widget-loading .spinner-icon-container {
	height: 50px;
	width: 44px;
}

#loadingindicator .spinner .subtitle,
.dashboard-widget-loading .spinner .subtitle {
	/* the subtitle starts off hidden */
	/* center the subtitle region.
		It's max width is 350px */
	margin-top: -5px;
	width: 350px;
}

#loadingindicator .spinner .subtitle-text,
.dashboard-widget-loading .spinner .subtitle-text {
	/* Text style */
	color: white;
	font-size: 18pt;
	font-weight: regular;
	/* Center the text, center the div */
	display: table;
	margin-left: auto;
	margin-right: auto;
	text-align: center;
}

#open-datasouce-loading .progressbar,
#loadingindicator .progressbar {
	display: none;
	width: 450px; // background: white;
	margin: 24% auto 0 auto;
	padding: 25px 40px 20px 40px; // box-shadow: 0px 0px 20px 2px rgba(0, 0, 0, 0.75);
	border-radius: 4px;
}

#loadingindicator .progressbar {
	margin: 0;
}

#loadingindicator .progressbar .progress {
	height: auto;
	margin-bottom: 0;
	border: 0;
	background: none;
	box-shadow: none;

	.progress-bar {
		line-height: 25px;
		width: 99% !important;
		background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);

		.k-selected {
			-webkit-animation: progress-bar-stripes 0.7s linear infinite;
			-o-animation: progress-bar-stripes 0.7s linear infinite;
			animation: progress-bar-stripes 0.7s linear infinite;
			background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
			background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
			background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
			-webkit-background-size: 40px 40px;
			background-size: 40px 40px;
			background-color: #333333;
			border-color: #333333;
		}
	}

	.progress-label {
		display: none;
		position: absolute;
		line-height: 25px;
		padding-left: 5px;
	}
}

#open-datasouce-loading {
	height: 100%;
	width: 100%;
	background-color: rgba(128, 128, 128, 0.5);
	display: none;
	opacity: 1;
}

#open-datasouce-loading .spinner {
	height: 25px;
	width: 25px;
}

#open-datasouce-loading .loading-name {
	margin-left: 10px;
	font-size: 20px;
	vertical-align: middle;
}

#open-datasouce-loading .progressbar {
	display: block;
}

.document-map .header {
	color: white;
	font-size: 20px;
	font-weight: bold;
}


.ui-resizable {
	position: relative;
}

.ui-resizable-handle {
	position: absolute;
	font-size: 0.1px;
	display: block;
}

.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
	display: none;
}

.ui-resizable-n {
	cursor: n-resize;
	height: 7px;
	width: 100%;
	top: -5px;
	left: 0;
}

.ui-resizable-s {
	cursor: s-resize;
	height: 7px;
	width: 100%;
	bottom: -5px;
	left: 0;
}

.ui-resizable-e {
	cursor: e-resize;
	width: 7px;
	right: -5px;
	top: 0;
	height: 100%;
}

.ui-resizable-w {
	cursor: w-resize;
	width: 7px;
	left: -5px;
	top: 0;
	height: 100%;
}

.ui-resizable-se {
	cursor: se-resize;
	width: 12px;
	height: 12px;
	right: 1px;
	bottom: 1px;
}

.ui-resizable-sw {
	cursor: sw-resize;
	width: 9px;
	height: 9px;
	left: -5px;
	bottom: -5px;
}

.ui-resizable-nw {
	cursor: nw-resize;
	width: 9px;
	height: 9px;
	left: -5px;
	top: -5px;
}

.ui-resizable-ne {
	cursor: ne-resize;
	width: 9px;
	height: 9px;
	right: -5px;
	top: -5px;
}

.modal-zindex-40000 {
	z-index: 40000 !important;
}