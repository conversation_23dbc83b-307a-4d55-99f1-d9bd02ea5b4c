(function()
{
    createNamespace("TF.Strings");

    TF.Strings.FieldTrip = {
        ERROR_MESSAGE : {
            STRICT_ACCOUNT_ENABLED: "Strict Account Code Tracking is enabled.",
            STRICT_ACCOUNT_DEPT_ACTIVITY: "Strict Account Code Tracking is enabled.  No account codes match the selected school and Dept/Activity.",
            STRICT_ACCOUNT_INVOICE: "Strict Account Code Tracking is enabled.  Please remove any existing invoice that uses invalid account.",
            NO_MATCHED_ACCOUNT_STRICT: "Strict Account Code Tracking is enabled.  No account codes are available for the selected school and Dept/Activity.  Please configure in field trip configurations or change school and Dept/Activity selection.",
            NO_MATCHED_ACCOUNT: "No account codes are available, please add in field trip configurations.",
        }
    };
})();