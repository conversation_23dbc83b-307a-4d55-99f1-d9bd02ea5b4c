(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.NoTransportationTypeDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	};

	namespace.NoTransportationTypeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.NoTransportationTypeDataModel.prototype.constructor = namespace.NoTransportationTypeDataModel;

	namespace.NoTransportationTypeDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Type", default: "" }
	];
})();