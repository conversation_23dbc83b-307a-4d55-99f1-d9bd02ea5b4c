﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DistrictDeletion = DistrictDeletion;

	function DistrictDeletion()
	{
		this.type = 'district';
		namespace.BaseDeletion.apply(this, arguments);
	}

	DistrictDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	DistrictDeletion.prototype.constructor = DistrictDeletion;

	DistrictDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "student", "ids", "district"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'student',
				items: response.Items[0]
			});
		});

		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"),{
				paramData:{
					"@filter": "in(DistrictID," + ids.join(",") + ")",
					"@fields": "Id"
				}
			}).then(function(response)
			{
				associatedDatas.push({
					type: 'school',
					items: response.Items[0]
				});
			});

		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
      			"AttachedToType": tf.dataTypeHelper.getId("district")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});
		//var p2 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "deltastuds", "ids", "district"), {
		//	data: ids
		//}).then(function(response)
		//{
		//	associatedDatas.push({
		//		type: 'deltastuds',
		//		items: response.Items[0]
		//	});
		//});
		return Promise.all([p0, p1, p2]).then(function()
		{
			return associatedDatas;
		});
	}

	DistrictDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	DistrictDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "student", "district");

		var p2 = this.getDataStatus(this.ids, "school", "district");

		var p3 = this.getDataStatus(this.ids, "deltastuds", "district");

		return Promise.all([p0, p1, p2, p3]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};

})();