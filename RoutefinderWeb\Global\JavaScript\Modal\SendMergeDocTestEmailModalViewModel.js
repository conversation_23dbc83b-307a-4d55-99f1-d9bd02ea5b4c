﻿(function()
{
	createNamespace('TF.Modal').SendMergeDocTestEmailModalViewModel = SendMergeDocTestEmailModalViewModel;

	function SendMergeDocTestEmailModalViewModel(option)
	{
		TF.Modal.BaseModalViewModel.call(this);
		if(option.modelType === 'SendTo')
		{
			this.obDisableControl(true);
			this.title("Send To");
		}
		else
		{
			this.title("Email");
		}
		this.contentTemplate('modal/SendMergeDocTestEmailControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Send");
		this.SendMergeDocTestEmailViewModel = new TF.Control.SendMergeDocTestEmailViewModel(option, this.obDisableControl);
		this.data(this.SendMergeDocTestEmailViewModel);
	}

	SendMergeDocTestEmailModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SendMergeDocTestEmailModalViewModel.prototype.constructor = SendMergeDocTestEmailModalViewModel;



	SendMergeDocTestEmailModalViewModel.prototype.positiveClick = function()
	{
		this.SendMergeDocTestEmailViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	SendMergeDocTestEmailModalViewModel.prototype.negativeClick = function()
	{
		this.SendMergeDocTestEmailViewModel.close().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	SendMergeDocTestEmailModalViewModel.prototype.dispose = function()
	{
		$("#pageMenu .show-menu-button").css('z-index', '1999');
		this.SendMergeDocTestEmailViewModel.dispose();
	};

})();
