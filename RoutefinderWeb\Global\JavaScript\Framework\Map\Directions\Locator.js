(function()
{
	Tool = TF.RoutingMap.Directions.Tool;

	// Using Search Dijit to replace GeoCode Server.
	Tool.prototype._initLocator = function()
	{
		var self = this;

		self._searchTool = new self._arcgis.Search({
			'allPlaceholder': 'Enter address or location',
			'autoSelect': false,
			'locationEnabled': false,
			'maxResults': 1,
			'maxSuggestions': 1,
			'popupEnabled': false,
			'resultGraphicEnabled': false,
			'searchAllEnabled': false,
			'suggestionsEnabled': false,
			"includeDefaultSources": false,
			'view': self._map.mapView,
			sources: [
				{
					singleLineFieldName: "SingleLine",
					outFields: ["Addr_type"],
					name: "Geocoding Service",
					localSearchOptions: {
						distance: 100
					}
				}
			]
		}, document.createElement("div"));
	};

	Tool.prototype._disposeLocator = function()
	{
		var self = this;
		self._searchTool = null;
	};

	Tool.prototype._getAddress = function(mapPoint)
	{
		return tf.arcgisHelper.loadArcgisUrls().then(() =>
		{
			return TF.locationToAddress(mapPoint);
		});
	};
})();