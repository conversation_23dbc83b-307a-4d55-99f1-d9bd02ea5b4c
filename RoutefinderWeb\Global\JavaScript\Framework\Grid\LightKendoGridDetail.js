(function()
{
	createNamespace("TF.Grid").LightKendoGridDetail = LightKendoGridDetail;

	function LightKendoGridDetail(lightKendoGrid, detailDomMap)
	{
		let self = this;

		this.lightKendoGrid = lightKendoGrid;
		this.detailDomMap = detailDomMap || {};// use this data to restore detail from scroll paging
		this._onSubGridFilter = this._onSubGridFilter.bind(this);
		this._onSubGridDataBound = this._onSubGridDataBound.bind(this);
		this._onChangeColumn = this._onChangeColumn.bind(this);
		this._onColumnResizing = this._onColumnResizing.bind(this);
		this._onColumnResized = this._onColumnResized.bind(this);
		this.detail = this.setDetail();
		this.overlay = false;

		self.pubSubSubscriptions = [];
		self.pubSubSubscriptions.push(PubSub.subscribe("studentrequirements", self._onStudentRequirementChange.bind(self)));
		self.pubSubSubscriptions.push(PubSub.subscribe("studentscheduleChange", self._onStudentscheduleChange.bind(self)));
	}

	LightKendoGridDetail.prototype.onCreateGrid = function()
	{
		this._bindToggleDetailEvent();
		this._restoreDetailOnDataBound();
		this.lightKendoGrid.$container.data("lightKendoGrid", this.lightKendoGrid);
		this.lightKendoGrid.onClearFilter.subscribe(this._clearFilter.bind(this));
	};

	LightKendoGridDetail.prototype.refresh = function()
	{
		this.detailDomMap = {};
	};

	LightKendoGridDetail.prototype._bindToggleDetailEvent = function()
	{
		var self = this;
		this.lightKendoGrid.$container.undelegate(".k-hierarchy-cell", "click").delegate(".k-hierarchy-cell", "click", function(e)
		{
			e.preventDefault();
			e.stopPropagation();

			var button = $(e.currentTarget).children();
			var isExpand = button.hasClass("k-i-expand");
			var row = button.closest("tr");
			var uid = row.data("kendoUid");
			var masterRow = self.lightKendoGrid.$container.find("tr[data-kendo-uid='" + uid + "']").eq(1);
			var data = self.lightKendoGrid.kendoGrid.dataItem(masterRow);
			var detailRowMaster = masterRow.next();
			var detailRow = row.next();

			if (!detailRowMaster || !detailRowMaster.hasClass("k-detail-row"))
			{
				var ans = self._appendDetailRow(masterRow);
				detailRow = ans.detailRow;
				detailRowMaster = ans.detailRowMaster;
			}

			if (isExpand)
			{
				button.removeClass("k-i-expand").addClass("k-i-collapse");
				detailRowMaster.show();
				detailRow.show();
				detailRow.height(detailRowMaster.outerHeight());
				detailRowMaster.height(detailRow.height());
				self.detailDomMap[data.Id].show = true;
			}
			else
			{
				button.removeClass("k-i-collapse").addClass("k-i-expand");
				detailRowMaster.hide();
				detailRow.hide();
				self.detailDomMap[data.Id].show = false;
			}
			self._onDetailChange();
		});
	};

	LightKendoGridDetail.prototype._appendDetailRow = function(forRow)
	{
		var self = this;
		var uid = forRow.data("kendoUid");
		var leftLockRow = self.lightKendoGrid.$container.find("tr[data-kendo-uid='" + uid + "']").eq(0);
		var lockCount = self.lightKendoGrid.countLockedColumns(self.lightKendoGrid.kendoGrid.columns);
		var data = self.lightKendoGrid.kendoGrid.dataItem(forRow),
			altClass = "k-alt",
			detailRowMaster, detailRow;
		if (self.detailDomMap[data.Id])
		{
			detailRowMaster = self.detailDomMap[data.Id].detailRowMaster;
			detailRow = self.detailDomMap[data.Id].detailRow;
			detailRowMaster.css("position", "relative");
			detailRow.css("position", "relative");
		} else
		{
			var $detail = self._getDetail(data);
			detailRowMaster = $(self._detailTemplate($detail));
			detailRow = $("<tr class='k-detail-row'></tr>");
			self.detailDomMap[data.Id] = { detailRowMaster: detailRowMaster, detailRow: detailRow };
		}

		detailRow.empty();
		for (var i = 0; i < lockCount; i++)
		{
			detailRow.append("<td></td>");
		}

		if (forRow.hasClass(altClass))
		{
			detailRowMaster.addClass(altClass);
			detailRow.addClass(altClass);
		} else
		{
			detailRowMaster.removeClass(altClass);
			detailRow.removeClass(altClass);
		}

		detailRowMaster.insertAfter(forRow);
		detailRow.insertAfter(leftLockRow);
		var height = detailRowMaster.outerHeight();
		detailRow.height(height);
		self.detailDomMap[data.Id].height = height;

		var kIcon = leftLockRow.find(".k-icon");
		if (detailRowMaster.css("display") == "none")
		{
			kIcon.removeClass("k-i-collapse").addClass("k-i-expand");
		} else
		{
			kIcon.removeClass("k-i-expand").addClass("k-i-collapse");
		}

		return {
			detailRow: detailRow,
			detailRowMaster: detailRowMaster
		};
	};

	LightKendoGridDetail.prototype._detailTemplate = function($detail)
	{
		var self = this,
			colspan = visibleColumns(self.lightKendoGrid.kendoGrid.columns).length + 1,
			$tr = $(`<tr class="k-detail-row">
						<td class="k-detail-cell" ${colspan ? "colspan='" + colspan + "'" : ""}></td>
					</tr>`);

		$tr.find("td").append($detail);
		return $tr;
	};

	LightKendoGridDetail.prototype.setDetail = function()
	{
		switch (this.lightKendoGrid.options.gridType)
		{
			case "student":
				return new TF.Grid.Detail.StudentGridDetail(this.lightKendoGrid);
			case "trip":
			case "tripschedule":
				return new TF.Grid.Detail.TripGridDetail(this.lightKendoGrid);
		}
	};

	LightKendoGridDetail.prototype._getDetail = function(data)
	{
		return this.detail.create(data, this._onSubGridFilter, this._onSubGridDataBound, this._onChangeColumn, this._onColumnResizing, this._onColumnResized);
	};

	LightKendoGridDetail.prototype.getSubGrids = function()
	{
		this.detail.grids = this.detail._getGrids();
		return this.detail.grids;
	};

	/**
	* change sub grid column when use edit column modal
	*/
	LightKendoGridDetail.prototype.changeSubGridColumns = function(editColumnViewModels, isRootGridRebuild)
	{
		this.detail.changeGridColumns(editColumnViewModels, isRootGridRebuild);
	};

	LightKendoGridDetail.prototype.changeQuickFilterBarStatus = function(display)
	{
		this.detail && this.detail.changeQuickFilterBarStatus(display);
		this._onSubGridDataBound();
	};

	// when sub grid filter change, trigger parent grid filter
	LightKendoGridDetail.prototype._onSubGridFilter = function(filter, gridType, getIds)
	{
		var self = this;
		if (!filter.filters)
		{
			this.lightKendoGrid.additionalGridFilterIds = null;
			this.lightKendoGrid.refresh();
		} else
		{
			getIds().then(function(ids)
			{
				self.lightKendoGrid.additionalGridFilterIds = Enumerable.From(ids).Distinct().ToArray();
				self.lightKendoGrid.refresh();
			});
		}

		this.detail.loadedGrids.forEach(function(grid)
		{
			if (grid._gridType == gridType)
			{
				self.detail.applyGridFilter(grid, filter);
			}
		});
	};

	/**
	* trigger when column delete or reorder
	*/
	LightKendoGridDetail.prototype._onChangeColumn = function(data)
	{
		this.detail.changeGridColumns([data]);
	};

	LightKendoGridDetail.prototype._onColumnResizing = function(e, id)
	{
		this._changeHeight(id);
		this.lightKendoGrid._fullfillGridBlank();
	};

	LightKendoGridDetail.prototype._onColumnResized = function(e, id, type)
	{
		this.detail.resizeGridColumns(e, type);
		this._changeHeight(id);
		this._onDetailChange();
	};

	LightKendoGridDetail.prototype._onSubGridDataBound = function(recordId)
	{
		var self = this;
		setTimeout(function()
		{
			if (!recordId)
			{
				for (var key in self.detailDomMap)
				{
					self._changeHeight(key);
				}
			} else
			{
				self._changeHeight(recordId);
			}
			self._onDetailChange();
		});
	};

	LightKendoGridDetail.prototype._changeHeight = function(id)
	{
		var self = this;
		if (self.detailDomMap[id])
		{
			/* 
			 * once sub grid applied filter, a calculated height will be a fixed number as CSS "height" property, 
			 * but the height won't change while use method outerHeight to re-calculate height if new filter(which will reduce records count in sub grid) applied
			 * so that we set height as "auto", after DOM recognized the "auto" property, then we re-calculate height.
			 */
			self.detailDomMap[id].detailRow.css("height", "auto");
			self.detailDomMap[id].detailRowMaster.css("height", "auto");
			setTimeout(() =>
			{
				var height = self.detailDomMap[id].detailRowMaster.outerHeight();
				self.detailDomMap[id].detailRow.height(height);
				self.detailDomMap[id].detailRowMaster.height(height);
				self.detailDomMap[id].height = height;
			}, 0);
		}
	};

	LightKendoGridDetail.prototype._onDetailChange = function()
	{
		if (this.lightKendoGrid?.kendoGrid?.tbody)
		{
			this._changeGridWidth();
			this.lightKendoGrid._fullfillGridBlank();
			this.lightKendoGrid.kendoGrid.virtualScrollable.repaintScrollbar();
		}
	};

	LightKendoGridDetail.prototype._changeGridWidth = function()
	{
		// add empty td to fix sub grid 

		var style = "border-left:none;padding:0;width:100%;";
		if (this.lightKendoGrid.kendoGrid.tbody.children(":not(.k-detail-row)").length > 0)
		{
			this.lightKendoGrid.kendoGrid.tbody.children(":not(.k-detail-row)").each(function(index, item)
			{
				item = $(item);
				if (item.children(".empty-td").length == 0)
				{
					item.append("<td class='empty-td' style='" + style + "'></td>");
				}
			});
			this.lightKendoGrid.kendoGrid.thead.children().each(function(index, item)
			{
				item = $(item);
				if (item.children(".empty-td").length == 0)
				{
					if (item.hasClass("k-filter-row"))
					{
						item.append("<td class='empty-td' style='" + style + "'></td>");
					}
					else
					{
						item.append("<th class='empty-td k-header' style='" + style + "'></th>");
					}
				}
			});
		}

		var widths = [];
		var columnWidths = Enumerable.From(this.lightKendoGrid.kendoGrid.columns).Where(function(c) { return !c.locked; }).Sum(function(c) { return parseInt(c.width); });
		widths.push(columnWidths);
		for (var key in this.detailDomMap)
		{
			if (this.detailDomMap[key].detailRowMaster.is(":visible"))
			{
				widths.push(this.detailDomMap[key].detailRowMaster.children().find(".kendo-grid").width() + 14);
			}
		}
		var gridWidth = Math.max.apply(null, widths);
		this.lightKendoGrid.kendoGrid.table.width(gridWidth);
		this.lightKendoGrid.kendoGrid.thead.parent().width(gridWidth);
		this.lightKendoGrid.changeLockedColumnHeight();
	};

	LightKendoGridDetail.prototype._restoreDetailOnDataBound = function()
	{
		var self = this;
		this.lightKendoGrid.kendoGrid.bind("dataBound", function()
		{
			if (!self.detail.getAvailabilityOnGrid()) { return; }

			var hasDetail = false;
			var $tr = self.lightKendoGrid.$container.find(".k-grid-content>.k-virtual-scrollable-wrap>table.k-grid-table>tbody>tr.k-master-row");
			$tr.each(function(i, row)
			{
				var data = self.lightKendoGrid.kendoGrid.dataItem(row);
				if (self.detailDomMap[data.Id])
				{
					if (self.detail.loadedGrids.length === 0)
					{
						delete self.detailDomMap[data.Id];
					}
					self._appendDetailRow($(row));
					hasDetail = true;
				}
			});

			if (hasDetail)
			{
				self._onDetailChange();
			}
			self._setDetailRowHasData();
		});
	};

	LightKendoGridDetail.prototype._setDetailRowHasData = function()
	{
		var self = this;
		var ids = this.lightKendoGrid.kendoGrid.dataSource.data().map(function(item)
		{
			return item.Id;
		});
		this.detail.hasData(ids, { overlay: self.overlay }).then(function(hasDataIds)
		{
			if ($.isArray(hasDataIds))
			{
				var $tr = self.lightKendoGrid.$container.find(".k-grid-content-locked tr");
				$tr.each(function(i, row)
				{
					var data = self.lightKendoGrid.kendoGrid.dataItem(row);
					var hasDetail = Enumerable.From(hasDataIds).Any(function(c) { return c == data.Id; });
					if (hasDetail)
					{
						$(row).find(".k-icon").show();
					}
				});
			}
		});
	};

	LightKendoGridDetail.prototype._clearFilter = function()
	{
		var self = this;
		this.detail.gridFilter = {};
		this.lightKendoGrid.additionalGridFilterIds = null;
		this.detail.loadedGrids.forEach(function(grid)
		{
			self.detail.applyGridFilter(grid, {});
		});
	};

	LightKendoGridDetail.prototype._onStudentRequirementChange = function(key, result)
	{
		this.detail.loadedGrids.forEach(function(grid)
		{
			if (grid._gridType == "studentschedule" && result.StudentId == grid.student.id)
			{
				grid.refresh();
			}
		});
	};

	LightKendoGridDetail.prototype._onStudentscheduleChange = function(key, result)
	{
		let self = this;
		self.detail.loadedGrids.forEach(function(grid)
		{
			if (grid._gridType == "studentschedule" && result.studentId == grid.student.id)
			{
				grid.refresh();
			}
		});

		if (result.scheduleCount <= 1)
		{
			let $tr = self.lightKendoGrid.$container.find(".k-grid-content-locked tr");
			$tr.each(function(i, row)
			{
				let data = self.lightKendoGrid.kendoGrid.dataItem(row);
				if (result.studentId == data.Id)
				{
					let button = $(row).find(".k-icon");
					if (!button.hasClass("k-i-expand"))
					{
						button.click();
					}

					button.hide();
				}
			});
		}
	};

	LightKendoGridDetail.prototype.dispose = function()
	{
		let self = this;
		if (self.pubSubSubscriptions)
		{
			self.pubSubSubscriptions.forEach(function(item)
			{
				PubSub.unsubscribe(item);
			});

			self.pubSubSubscriptions = [];
		}

		this.detailDomMap = null;
		this.detail.dispose();
		this.detail = null;
	};

	function visibleColumns(columns)
	{
		return columns.filter(function(column)
		{
			return !column.locked && !column.hidden;
		});
	}

})();