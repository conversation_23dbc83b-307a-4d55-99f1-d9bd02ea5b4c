﻿(function()
{
	const FeedToNoneValue = null;

	createNamespace('TF.Control').GradeViewModel = GradeViewModel;

	function GradeViewModel(options)
	{
		const { type, id, grades } = options;
		this.grades = grades;
		this.fieldName = type;
		this.endpoint = tf.dataTypeHelper.getEndpoint(type);
		this.type = type;
		this.obEntityDataModel = ko.observable(new TF.DataModel.GradeDataModel());
		this.obEntityDataModel().id(id);
		this.isEditMode = !!this.obEntityDataModel().id();

		this.pageLevelViewModel = new TF.PageLevel.GradePageLevelViewModel();

		this.obShowFeedToRequiredWarning = ko.observable(false);
	}

	GradeViewModel.prototype.save = function()
	{
		const self = this;
		self.validFeedToRequired();

		// Trim Input Value
		var entity = self.obEntityDataModel();
		const code = entity.code().trim();
		entity.code(code);
		const name = entity.name().trim();
		entity.name(name);

		return self.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(result)
			{
				if (result && !self.obShowFeedToRequiredWarning())
				{
					return self.basicSave();
				}
				else
				{
					return false;
				}
			});
	};

	GradeViewModel.prototype.basicSave = function()
	{
		var entity = this.obEntityDataModel().toData();
		var isNew = this.obEntityDataModel().id() ? false : true;
		delete entity["APIIsDirty"];
		return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefixWithoutDatabase(), this.endpoint), { data: [entity] })
			.then(function()
			{
				PubSub.publish(topicCombine(pb.DATA_CHANGE, this.type, pb.EDIT));
				return entity;
			}.bind(this));
	};

	GradeViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.$form = $(el);

		var validatorFields = {}, isValidating = false,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};

		validatorFields.code = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Code is required"
				},
				callback: {
					message: "Code must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endpoint), {
							paramData: {
								"@filter": "eq(Code," + value + ")"
							}
						}, { overlay: false })
							.then(function(apiResponse)
							{
								return !apiResponse.Items.some(function(item)
								{
									return item.Code.toLowerCase() == value.toLowerCase() && item.Id != self.obEntityDataModel().id();
								}.bind(this));
							}.bind(this));
					}
				}
			}
		};

		validatorFields.name = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Name is required"
				},
				callback: {
					message: "Name must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endpoint), {
							paramData: {
								"@filter": "eq(Name," + value + ")"
							}
						}, { overlay: false })
							.then(function(apiResponse)
							{
								return !apiResponse.Items.some(function(item)
								{
									return item.Name.toLowerCase() == value.toLowerCase() && item.Id != self.obEntityDataModel().id();
								}.bind(this));
							}.bind(this));
					}
				}
			}
		};

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.load();
	};

	GradeViewModel.prototype.load = function()
	{
		var self = this,
			gradeId = self.obEntityDataModel().id();

		let getDataPromise = Promise.resolve();
		if (gradeId)
		{
			getDataPromise = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endpoint),
				{
					paramData: {
						Id: gradeId
					}
				})
				.then(function(data)
				{
					var item = data.Items[0];
					self.obEntityDataModel(new TF.DataModel.GradeDataModel(item));
				});
		}

		getDataPromise.then(() =>
		{
			self.pageLevelViewModel.load(self.$form.data("bootstrapValidator"));
			self.initFeedToDropdownList();
		});
	};

	GradeViewModel.prototype.initFeedToDropdownList = function()
	{
		var self = this;
		var feedToItems = self.grades.map(g =>
		{
			return {
				text: `${g.Name} (${g.Code})`,
				value: g.Id,
			};
		});

		var currentlyGradeId = this.obEntityDataModel().id();
		if (currentlyGradeId >= 0)
		{
			feedToItems = feedToItems.filter(f => f.value !== currentlyGradeId);
		}

		feedToItems.sort((a, b) =>
		{
			return a.text.toLowerCase().localeCompare(b.text.toLowerCase());
		});

		$("#FeedTo").kendoDropDownList({
			autoBind: self.isEditMode,
			dataTextField: "text",
			dataValueField: "value",
			height: 260,
			dataSource: {
				transport: {
					read: function(options)
					{
						Promise.resolve(feedToItems)
							.then(function(records)
							{
								records.unshift({ text: 'None', value: FeedToNoneValue });
								options.success(records)
							})
							.catch(function(err)
							{
								options.success([{ text: 'None', value: FeedToNoneValue }]);
							});
					}
				},
			},
			change: function()
			{
				let feedTo = $("#FeedTo").val()
				feedTo = (feedTo === '') ? FeedToNoneValue : feedTo;
				self.obEntityDataModel().feedTo(feedTo);
			},
			template: '<div class="tf-kendo-dropdown-li-item first-item-with-split">#: text #</div>',
			open: function(e)
			{
				const $li = this.listView.element.find('.tf-kendo-dropdown-li-item').first();
				$li.addClass('item-with-split');
			},
			select: function(e)
			{
				self.obShowFeedToRequiredWarning(false);
			},
			dataBound: function(e)
			{
				if (!self.isEditMode)
				{
					this.select(-1);
				}
			}
		});

		self.feedToDDL = $("#FeedTo").data("kendoDropDownList");

		if (self.isEditMode)
		{
			self.feedToDDL.value(self.obEntityDataModel().feedTo());
		}
		self.feedToDDL.enable(!self.obEntityDataModel().isGraduatedGrade());

		self.obEntityDataModel().isGraduatedGrade.subscribe((val) =>
		{
			self.feedToDDL.enable(!val);
			self.feedToDDL.value(FeedToNoneValue);
			self.obEntityDataModel().feedTo(FeedToNoneValue);
		}, self);
	};

	GradeViewModel.prototype.validFeedToRequired = function()
	{
		const self = this;
		const validFailed = !(self.obEntityDataModel().isGraduatedGrade() ||
			self.feedToDDL.selectedIndex !== -1);

		self.obShowFeedToRequiredWarning(validFailed);
	}

	GradeViewModel.prototype.apply = function()
	{
		return this.save();
	};

	GradeViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};

})();

