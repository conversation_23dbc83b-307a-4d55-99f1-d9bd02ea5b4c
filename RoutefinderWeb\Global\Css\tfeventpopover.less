/* popover styles for TF
*/

.tf-popover-wrapper {
	position: absolute;
	margin-bottom: 13px;
	width: 450px;
}

.with-max-height {
	overflow-y: auto;
	overflow-x: hidden;

	&.one-event {
		/*max-height: 100%;*/
	}

	&.two-event {
		/*max-height: 100%;*/
	}

	&.more-event {
		/*max-height: 100%;*/
	}
}

.tf-popover-wrapper .header {
	border-bottom: 1px solid;
	height: 40px;
}

.tf-popover-wrapper .title {
	font-size: 14pt;
	margin: 4px 0 4px 0;
	color: #333333;
}

.tf-popover-wrapper .no-event {
	margin-top: 5px;
	min-height: 80px;
}

.tf-popover-wrapper .event {
	margin: 10px 15px 0 15px;
	padding-bottom: 10px;
	/*height: 80px;*/
	font-size: 10pt;
	font-family: Arial;
	color: #333333;
}

.tf-popover-wrapper .event-boder {
	border-bottom: 1px solid;
	border-color: #F2F2F2;
}

.tf-popover-wrapper .icon {
	height: 16px;
	width: 16px;
	background-repeat: no-repeat;
	margin: 0 2px;
	display: block;
	padding: 0 10px;
	opacity: 0.8;
}

.tf-popover-wrapper .icon-expand {
	height: 16px;
	width: 8px;
	margin: 0 2px;
	display: block;
}

.tf-popover-wrapper .icon-closed {
	background-image: url("../img/calendar/closed.png");
}

.tf-popover-wrapper .icon-school-year {
	background-image: url("../img/calendar/schoolyear.png");
}

.tf-popover-wrapper .icon-trip {
	background-image: url("../img/calendar/session.png");
}

.tf-popover-wrapper .event .event-name {
	font-weight: bold;
}

.tf-popover-wrapper .event .timespan {
	text-align: right;
}

.tf-popover-wrapper .icon-trash {
	background-image: url("../img/menu/Delete-Black.svg");
}

.tf-popover-wrapper {
	.icon-trash:hover::after, .icon-edit-black:hover::after, .mapview:hover::after, .icon-viewrecord:hover::after {
		content: " ";
		width: 16px;
		height: 0;
		position: absolute;
		margin-top: 17px;
		margin-left: -10px;
		border: 1px solid red;
	}
}

.tf-popover-wrapper .button {
}

.tf-popover-wrapper .button:hover {
	cursor: pointer;
}

.tf-popover-wrapper .plus-button {
	color: white;
	background-color: black;
	padding: 3px;
	margin: 7px;
}

.tf-popover-wrapper .event .school {
	/* margin-left: 6%; */
}

.tf-popover-wrapper .event .notes {
	margin-top: 10px;
	/* margin-left: 7%; */
}

.tf-popover-wrapper .event .actions {
	margin-left: 5px;
}

/* arrao at top */
.arrow_box_top {
	position: relative;
	background: #ffffff;
	border: 1px solid #000000;
}

.arrow_box_top:after, .arrow_box_top:before {
	bottom: 100%;
	left: 93%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.arrow_box_top:after {
	border-color: rgba(255, 255, 255, 0);
	border-bottom-color: #ffffff;
	border-width: 10px;
	margin-left: -10px;
}

.arrow_box_top:before {
	border-color: rgba(0, 0, 0, 0);
	border-bottom-color: #000000;
	border-width: 11px;
	margin-left: -11px;
}

/* arrow at bottom */
.arrow_box_bottom {
	position: relative;
	background: #ffffff;
	border: 1px solid #000000;
}

.arrow_box_bottom:after, .arrow_box_bottom:before {
	top: 100%;
	left: 93%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.arrow_box_bottom:after {
	border-color: rgba(255, 255, 255, 0);
	border-top-color: #ffffff;
	border-width: 15px;
	margin-left: -15px;
}

.arrow_box_bottom:before {
	border-color: rgba(0, 0, 0, 0);
	border-top-color: #000000;
	border-width: 16px;
	margin-left: -16px;
}
