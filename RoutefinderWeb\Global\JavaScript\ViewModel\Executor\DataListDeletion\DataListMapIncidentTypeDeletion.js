(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListMapIncidentTypeDeletion = DataListMapIncidentTypeDeletion;

	function DataListMapIncidentTypeDeletion()
	{
		this.type = 'mapincidenttypes';
		this.deleteType = 'Map Incident Type';
		this.deleteRecordName = 'map incident type';
		namespace.DataListBaseDeletion.apply(this, [true]);
	}

	DataListMapIncidentTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListMapIncidentTypeDeletion.prototype.constructor = DataListMapIncidentTypeDeletion;

	DataListMapIncidentTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [], mapincident = 'mapincident';
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), tf.dataTypeHelper.getEndpoint(mapincident)),
			{
				paramData: { "@filter": "in(IncidentType," + ids.join(",") + ")" }
			})
			.then(function(response)
			{
				associatedDatas.push({
					type: mapincident,
					items: response.Items
				});
				return associatedDatas;
			});
	};

	DataListMapIncidentTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();