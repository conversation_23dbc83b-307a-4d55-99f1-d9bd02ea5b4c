﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.TripStopDataModel = function(stopEntity)
	{
		/// <param name="stopEntity" type="Object">optional. Omitting will result in an empty new object</param>
		namespace.BaseDataModel.call(this, stopEntity);
	};

	namespace.TripStopDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);
	namespace.TripStopDataModel.prototype.constructor = namespace.TripStopDataModel;

	namespace.TripStopDataModel.prototype.mapping = [
		{ "from": "ApproachXCoord", "default": null },
		{ "from": "ApproachYCoord", "default": null },
		{ "from": "AttendanceList", "default": null },
		{ "from": "AvgSpeed", "default": null },
		{ "from": "BdyType", "default": null },
		{ "from": "CalculatedLoadTime", "default": 0 },
		{ "from": "City", "default": "" },
		{ "from": "Comment", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "Distance", "default": 0 },
		{
			"from": "DrivingDirections", "default": [], fromMapping: function(value)
			{
				if (value)
				{
					return value.split("\r\n");
				}
				return "";
			},
			toMapping: function(value)
			{
				if (value)
				{
					return value.join("\r\n");
				}
				return "";
			}
		},
		{ "from": "RouteDrivingDirections", "default": "" },
		{ "from": "DropOffStudents", "default": [], subDataModelType: namespace.StudentDataModel },
		{ "from": "DropOffTransferStudents", "default": null },
		{ "from": "Duration", "default": null },
		{ "from": "ExceptionStudent", "default": null },
		{ "from": "GUID", "default": "" },
		{ "from": "Id", "default": 0 },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "LockStopTime", "default": null },
		{ "from": "NextStreet", "default": null },
		{ "from": "NextStudents", "default": null },
		{ "from": "NextTime", "default": null },
		{ "from": "NumDOShut", "default": 0 },
		{ "from": "NumPUShut", "default": 0 },
		{ "from": "NumStuds", "default": 0 },
		{ "from": "NumTrans", "default": 0 },
		{ "from": "PickUpStudents", "default": [], "subDataModelType": namespace.StudentDataModel },
		{ "from": "PickUpTransferStudents", "default": null },
		{ "from": "PreviousDistance", "default": null },
		{ "from": "PreviousDrivingDirections", "default": null },
		{ "from": "ProhibitCrosser", "default": false },
		{ "from": "SchlCode", "default": "" },
		{ "from": "SchoolLocationId", "default": null },
		{ "from": "SchoolName", "default": null },
		{ "from": "Sequence", "default": 0 },
		{ "from": "StopTime", "default": "00:00:00" },
		{ "from": "Street", "default": "" },
		{ "from": "StudentList", "default": [] },
		{ "from": "TotalStopTime", "default": null },
		{ "from": "TotalStopTimeManualChanged", "default": false },
		{ "from": "Trip", "default": null },
		{ "from": "TripId", "default": 0 },
		{ "from": "TripName", "default": null },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
		{ "from": "VehicleCurbApproach", "default": 0 },
		{ "from": "XStop", "default": false },
		{ "from": "Xcoord", "default": 0 },
		{ "from": "Ycoord", "default": 0 },
		{ "from": "IncludeNoStudStop", "default": false },
		{ "from": "IShow", "default": true },
		{ "from": "IsCustomDirection", "default": false },
		{ "from": "StudentsAssigned", "default": [] },
		{ "from": "WayPoints", "default": null },
		{ "from": "SpeedChanged", "default": false },
	];
})()
