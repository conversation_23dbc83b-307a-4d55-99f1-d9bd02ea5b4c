﻿(function()
{
	createNamespace('TF.Control').AddDocumentClassificationViewModel = AddDocumentClassificationViewModel;

	function AddDocumentClassificationViewModel(options)
	{
		var self = this;
		self._recordId = options.dataItem ? options.dataItem.Id : 0;
		self._initName = options.dataItem ? options.dataItem.Name : "";
		self.type = options.type;
		self.isEditing = options.isEditing;
		self.endPoint = tf.dataTypeHelper.getEndpoint(self.type);
		self.name = ko.observable(self._initName);
		self.description = options.dataItem ? ko.observable(options.dataItem.Description) : ko.observable("");
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	AddDocumentClassificationViewModel.prototype.save = function()
	{
		var self = this,
			name = self.name().trim(),
			description = self.description();
		return self.pageLevelViewModel.saveValidate(null, { hideToast: true })
			.then(function(valid)
			{
				if (!valid)
				{
					//may popup message "some fields are invalid"
					return Promise.reject();
				}
				else
				{
					var method, url, data;
					if (self.isEditing)
					{
						method = 'put';
						url = pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endPoint, self._recordId);
						data = {
							Name: name,
							Description: description
						};
					}
					else
					{
						method = 'post';
						url = pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endPoint);
						data = [{
							Name: name,
							Description: description
						}];
					}

					return tf.promiseAjax[method](url, { 'data': data })
						.then(function(apiResponse)
						{
							return apiResponse.Items[0];
						});
				}
			});
	};

	AddDocumentClassificationViewModel.prototype.init = function(viewModel, el)
	{
		var self = this,
			endPoint = self.endPoint,
			isValidating = false,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};
		self.$form = $(el);

		var validatorFields = {};

		validatorFields.classificationName = {
			trigger: "blur change",
			container: self.$form.find("input[name='classificationName']").closest("div"),
			validators: {
				notEmpty: {
					message: "Name is required"
				},
				callback: {
					message: "Name must be unique",
					callback: function(value, validator, $field)
					{
						if (!value.trim())
						{
							updateErrors($field, "unique");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}

						return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), endPoint), {
							paramData: { Name: value.trim() }
						}, { overlay: false }).then(function(apiResponse)
						{
							if (apiResponse.Items.length === 0 ||
								(self.isEditing === true &&
									apiResponse.Items[0].Name === self._initName))
							{
								return true;
							}

							return false;
						});
					}
				}
			}
		}

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		self.pageLevelViewModel.load(self.$form.data("bootstrapValidator"));
	};

	AddDocumentClassificationViewModel.prototype.apply = function()
	{
		var self = this;
		return self.save()
			.then(function(data)
			{
				self.dispose();
				return data;
			});
	};

	AddDocumentClassificationViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};
})();