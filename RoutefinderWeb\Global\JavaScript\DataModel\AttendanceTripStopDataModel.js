﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.AttendanceTripStopDataModel = function(stopEntity)
	{
		namespace.TripStopDataModel.call(this, stopEntity);
		if (!this.attendanceInfo().stopTime())
		{
			this.attendanceInfo().stopTime(this.stopTime());
		}
		if (!this.attendanceInfo().origStopTime())
		{
			this.attendanceInfo().origStopTime(this.stopTime());
		}
		if (!this.origSequence() || this.origSequence() < 0)
		{
			this.origSequence(this.sequence());
		}
	};

	namespace.AttendanceTripStopDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.AttendanceTripStopDataModel.prototype.constructor = namespace.AttendanceTripStopDataModel;

	namespace.AttendanceTripStopDataModel.updateMapping = function(mapping)
	{
		$.each(mapping, function(index, item)
		{
			if (item.from === "PickUpStudents" || item.from === "DropOffStudents" || item.from === "ExceptionStudent" || item.from === "StudentList")
			{
				item.subDataModelType = namespace.AttendanceStudentDataModel;
			}
		});

		mapping.push({ from: 'AttendanceInfo', default: new namespace.AttendanceItemDataModel(), subDataModelType: namespace.AttendanceItemDataModel });
		mapping.push({ from: 'ModStatus', default: null });
		mapping.push({ from: 'OrigSequence', default: -1 });
		mapping.push({ from: 'IsRemoved', default: false });
		mapping.push({ from: 'AttendanceType', default: 0 });
		mapping.push({ from: 'RecordType', default: 0 });
		mapping.push({ from: 'Source', default: 'Pro' });
		mapping.push({ from: "ExceptionStudent", default: [], subDataModelType: namespace.StudentDataModel });
		mapping.push({ from: 'StudentsAssigned', default: [] });
		return mapping;
	};

	namespace.AttendanceTripStopDataModel.prototype.mapping = namespace.AttendanceTripStopDataModel.updateMapping($.extend(true, [], namespace.TripStopDataModel.prototype.mapping));
})();
