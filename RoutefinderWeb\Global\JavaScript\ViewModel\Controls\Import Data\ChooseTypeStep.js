(function()
{
	createNamespace("TF.ImportAndMergeData").ChooseTypeStep = ChooseTypeStep;

	function ChooseTypeStep(data)
	{
		TF.Control.BaseWizardStepViewModel.call(this, data);
		var self = this;
		self.name(null);
		self.template = "modal/import data/importandmergedatacontrol";
		self.dataSources = ko.observableArray([]);
		self.uploadCompleteFlag = false;

		self.useTfDataSource = ko.pureComputed(function()
		{
			return self.data.type() == TF.ImportAndMergeData.ImportAndMergeDataType.TransfinderDataSource;
		});

		self.useExternalSource = ko.pureComputed(function()
		{
			return self.data.type() == TF.ImportAndMergeData.ImportAndMergeDataType.ExternalSource;
		});

		// TODO: need optimize
		self.uploadedFileChangeEvent = self.uploadedFileChangeEvent.bind(self);
		self.onFileUploadComplete = new TF.Events.Event();
		self.onFileUploadComplete.subscribe(self.fileUploadComplete.bind(self));
		self.obFileAccept = ko.observable();
		self.sourceDB = ko.pureComputed(function()
		{
			var dataSources = self.dataSources() || [];
			return dataSources.find(function(i) { return i.DBID === self.data.sourceDBID() });
		});
	}

	ChooseTypeStep.prototype = Object.create(TF.Control.BaseWizardStepViewModel.prototype);

	ChooseTypeStep.prototype.constructor = ChooseTypeStep;

	ChooseTypeStep.prototype.uploadedFileChangeEvent = function(viewModel, e)
	{
		var self = this, files = e.target.files;
		if (files.length > 0)
		{
			self.uploadedFile(files[0]);
		}
	};

	ChooseTypeStep.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.element = $(el);

		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "clientconfigs")).then((data) =>
		{
			tf.allowMultipleCardStudent = !!data.Items[0].AllowMultipleCardStudent;
			tf.allowMultipleCardStaff = !!data.Items[0].AllowMultipleCardStaff;
		});

		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "databases")).then(function(response)
		{
			var items = response.Items.filter(function(i) { return i.DBID != tf.datasourceManager.databaseId });
			Array.sortBy(items, "Name");
			self.dataSources(items);
			if (items.length)
			{
				self.data.sourceDBID(items[0].DBID);
			}
		});
	};

	ChooseTypeStep.prototype.readExternalSource = function()
	{
		var self = this, promiseAlert = function(name)
		{
			return tf.promiseBootbox.alert({
				message: "Please select a valid " + name + " file.",
				title: "Warning"
			}).then(function() { return false; });
		};

		if (!self.data.externalSourceDataPath())
		{
			return promiseAlert("external source");
		}

		var file = self.data.externalSourceDataFile();
		var fileName = file.name;
		var type = fileName.substring(fileName.lastIndexOf(".") + 1);

		switch (type.toUpperCase())
		{
			case "XLS":
			case "XLSX":
				return TF.openExcel(file).then(function(workbook)
				{
					self.data.readExternalExcel(workbook);
					self.data.externalFileType = type.toUpperCase();
					if (self.data.externalSourceTemplatePath())
					{
						return TF.openXml(self.data.externalSourceTemplateFile()).then(function(xmlDoc)
						{
							return self.data.readTemplateFile(xmlDoc) || promiseAlert("template");
						}).catch(function()
						{
							return promiseAlert("template");
						});
					}

					return true;
				}).catch(function()
				{
					return promiseAlert("external source");
				});
			case "MDB":
				// to do
				return promiseAlert("external source");
			case "TFX":
				return TF.openTFX(file).then(list =>
				{
					var records = TF.getTFXInfo(list);
					self.data.externalFileType = type.toUpperCase();
					self.data.tfxData({
						ConvertedFileName: file.name,
						CreatedDatetime: moment(file.lastModifiedDate).format("MM/DD/YYYY"),
						RecordCounts: records.count,
						TFDFields: records.columns,
						TFDRows: records.rows,
						tfxFile: file,
						externalFileType: self.data.externalFileType,
					});
					return true;

				}).catch(function(e)
				{
					return promiseAlert("external source");
				});
			default:
				return promiseAlert("external source");
		}
	};

	ChooseTypeStep.prototype.readPredefinedFile = function()
	{
		let self = this;
		return tf.modalManager.showModal(new TF.Modal.SelectImportDefinitionFileModalViewModel()).then(function(result)
		{
			if (!result)
			{
				return;
			}

			if (result.fileName)
			{
				var importFileType = result.importFileType;
				return tf.promiseBootbox.alert("Please select the predefined file.", "Info").then(function()
				{
					return self.selectDataFile(result, importFileType);
				});
			}

			return sendRequestToGetFileTFD(result)
				.then(function(tfdHeaderInfo)
				{
					var importFileType = tfdHeaderInfo.ExternalFileType;
					var validateMessage = tfdHeaderInfo.ValidateMessage;
					if (validateMessage)
					{
						return tf.promiseBootbox.alert(validateMessage, "Error").then(function()
						{
							return false;
						});
					}

					return tf.promiseBootbox.alert("Please select the predefined file.", "Info").then(function()
					{
						return self.selectDataFile(result, importFileType);
					});
				})
		}).catch(function()
		{
			return promiseAlert("predefine");
		});
	};

	ChooseTypeStep.prototype.validate = function()
	{
		let self = this;
		switch (this.data.type())
		{
			case TF.ImportAndMergeData.ImportAndMergeDataType.ExternalSource:
				return this.readExternalSource();
			case TF.ImportAndMergeData.ImportAndMergeDataType.PredefinedImport:
				if (self.uploadCompleteFlag)
				{
					break;
				}

				return this.readPredefinedFile();
			case TF.ImportAndMergeData.ImportAndMergeDataType.TransfinderDataSource:
				if (this.data.sourceDBID() == null)
				{
					return tf.promiseBootbox.alert({
						message: "Please select a Data Source.",
						title: "Warning"
					}).then(() => false);
				}
		}

		return Promise.resolve(true);
	};

	ChooseTypeStep.prototype.execute = function()
	{
		var self = this;
		switch (self.data.type())
		{
			case TF.ImportAndMergeData.ImportAndMergeDataType.ExternalSource:
				return Promise.resolve(true);
			case TF.ImportAndMergeData.ImportAndMergeDataType.PredefinedImport:
				return Promise.resolve(true);
			case TF.ImportAndMergeData.ImportAndMergeDataType.TransfinderDataSource:
				return tf.modalManager.showModal(new TF.Modal.ImportDataFromTFDatasourceModalViewModel(self.sourceDB())).then(function(result)
				{
					if (result && result !== 'close')
					{
						self.data.tfdbImportData = result.operationOptions;
						self.data.operationOptions = result.operationOptions;
						self.data.operationId = result.operationId;
						return true;
					}
					else if (result === 'close')
					{
						self.element.closest('.modal-content').find('.btn.other').trigger('click', true);
						return false;
					}

					return false;
				});
		}

		return Promise.resolve(false);
	};

	ChooseTypeStep.prototype.selectDataFile = function(result, importFileType, callbackPromise)
	{
		this.obFileAccept(getAcceptedFileType(result.type));
		this.fileName = result.fileName;
		this.tfdfile = result.tfdfile;
		this.importFileType = importFileType;

		let file = this.element.find(".input-file");
		file.val("");
		let acceptedFile = getAcceptedFileType(importFileType);
		if (acceptedFile != null)
		{
			this.obFileAccept(acceptedFile.ext);
		}

		callbackPromise = callbackPromise || (this.tfxDataFileReadPromise = new Promise(resolve => this.tfxDataFileReadPromiseResolver = resolve));
		file.trigger("click");
		return callbackPromise;
	};

	ChooseTypeStep.prototype.uploadedFile = function(file)
	{
		// RW-31723 Attach the file object directly to fileModel (so that sequential processing can directly use it)
		var self = this, fileModel = { FileName: file.name, RawFile: file }, reader = new FileReader();

		if (self.tfdfile && self.tfdfile.RawFile)
		{
			// RW-31723 This path send TFD and DATA files as raw files (multipart form) to avoid additional memory consumption for large file
			fileModel.RawFile = file;
			var requestData = {
				ExternameFileInfo: fileModel,
				TFDFileInfo: self.tfdfile,
				FileName: self.fileName ? self.fileName : ""
			};
			self.onFileUploadComplete.notify(requestData);
		}
		else
		{
			// This path follows original way to send TFD and DATA files as BASE64 JSON data
			reader.fileName = file.name;
			reader.onload = function(event)
			{
				fileModel.FileContent = event.target.result;
				var requestData = {
					ExternameFileInfo: fileModel,
					TFDFileInfo: self.tfdfile ? self.tfdfile : { FileName: "", FileContent: "" },
					FileName: self.fileName ? self.fileName : ""
				};
				self.onFileUploadComplete.notify(requestData);
			};
			reader.readAsDataURL(file);
		}
	};

	function isFileTypeMatched(acceptedList, uploadedFileName)
	{
		let uploadefileType = uploadedFileName.substring(uploadedFileName.lastIndexOf('.'));
		if (!(acceptedList && acceptedList[0]))
		{//RW-16989 Import file  with or with out any file extension as Pipe Delimited File.
			return true;
		}
		else
		{
			return acceptedList.some(ftype => ftype.toUpperCase() === uploadefileType.toUpperCase());
		}
	}

	ChooseTypeStep.prototype.fileUploadComplete = function(e, requestData)
	{
		if (!isFileTypeMatched(this.obFileAccept().split(','), requestData.ExternameFileInfo.FileName))
		{
			return tf.promiseBootbox.alert('Invalid file type.').then(() =>
			{
				this.selectDataFile({ tfdfile: this.tfdfile }, this.importFileType, this.tfxDataFileReadPromise);
			})
		}

		const useRawFiles = requestData.TFDFileInfo && requestData.TFDFileInfo.RawFile
			&& requestData.ExternameFileInfo && requestData.ExternameFileInfo.RawFile;
		const convertTFXFileActionPromise = useRawFiles ?
			this.convertToTFXFileByRawTFDAndExternalFiles(requestData.TFDFileInfo, requestData.ExternameFileInfo) :
			tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "ImportedTFXData"), {
				data: requestData
			});

		convertTFXFileActionPromise.then(response =>
		{
			if (response && response.Items && response.Items[0])
			{
				this.data.readTFDAndTFX(response.Items[0]);
				this.uploadCompleteFlag = true;
				this.tfxDataFileReadPromiseResolver(true);
				return;
			}

			this.tfxDataFileReadPromiseResolver(false);
		}).catch(error =>
		{
			let errMsg = (error && error.TransfinderMessage) ? error.TransfinderMessage : "TFX Conversion Error";
			tf.promiseBootbox.alert(errMsg, "Error");
			this.tfxDataFileReadPromiseResolver(false);
		});
	};

	/**
	 * RW-31723 This method send both TFD and Data files as raw binary file, the API side will convert them
	 * to TFX file through disk files directly so as to avoid memory spike due to large BASE64 conversion
	 * @param {*} tfdFileInfo 
	 * @param {*} externalFileInfo 
	 * @returns 
	 */
	ChooseTypeStep.prototype.convertToTFXFileByRawTFDAndExternalFiles = function(tfdFileInfo, externalFileInfo)
	{
		const self = this;

		return new Promise((resolve, reject) =>
		{
			tf.loadingIndicator.show();
			var fmData = new FormData();
			fmData.append("TFDFILE", tfdFileInfo.RawFile);
			fmData.append("DATAFILE", externalFileInfo.RawFile);

			$.ajax({
				type: "POST",
				url: TF.Helper.ApiUrlHelper.postImportedTFXDataUploadUrl(),
				contentType: false,
				processData: false,
				data: fmData,
				headers: { 'Token': tf.entStorageManager.get("token") },
				success: function(response)
				{
					resolve(response);
				},
				error: function(error)
				{
					reject(error.responseJSON);
				},
				complete: function()
				{
					tf.loadingIndicator.tryHide();
				}
			});

		});
	};

	function getAcceptedFileType(typFlag)
	{
		var FileTypeObject = {
			"0": { "name": "Fixed Record Length Text File (txt)", "ext": ".txt" },
			"1": { "name": "Tab Delmited Text File (tab, dat)", "ext": ".tab,.dat,.txt" },//RW-16831 add .txt
			"2": { "name": "Comma Delmited Text File (csv)", "ext": ".csv,.txt" },//RW-16832 add .txt
			"3": { "name": "Transfinder TFX File (tfx)", "ext": ".tfx" },
			"4": { "name": "DBF File (dbf)", "ext": ".dbf" },
			"5": { "name": "MS Excel Spread Sheet (xls)", "ext": ".xls,.xlsx" },
			"6": { "name": "Pipe Delimited File (|)", "ext": "" }
		};
		if (FileTypeObject[typFlag] != null)
		{
			return FileTypeObject[typFlag];
		}
		else
		{
			return null;
		}
	}

	function sendRequestToGetFileTFD(fileData)
	{
		var options = {};
		options.FileName = fileData.fileName ? fileData.fileName : "";
		options.TFDFileInfo = fileData.tfdfile ? fileData.tfdfile : { FileName: "", FileContent: "" };
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "ImportedTFDData"), {
			data: options
		}).then(function(response)
		{
			return response.Items[0];
		});
	}
})();