(function()
{
	createNamespace('TF').ListMoverForListFilterHelper = ListMoverForListFilterHelper;
	function ListMoverForListFilterHelper() { }
	ListMoverForListFilterHelper.processSelectedData = function(selectedData, filterField)
	{
		if (selectedData)
		{
			if (Array.isArray(selectedData))
			{
				selectedData = selectedData.map(function(item)
				{
					if (typeof filterField === 'string')
					{
						if (!filterField)
						{
							item = item;
						}

						var tmp = undefined;
						if (filterField)
						{
							// in form result grid, need to get record value
							if (item.UDGridID)
							{
								if (item.hasOwnProperty(filterField))
								{
									tmp = item[filterField];
								}
								else
								{
									//get the filter value from RecordValue
									let formItemFilterField = JSON.parse(item.RecordValue)[filterField];
									if (formItemFilterField && formItemFilterField.length > 0)
									{
										tmp = formItemFilterField[0].value;
									}
								}
							} else
							{
								tmp = item[filterField];
							}
						}
						else
						{
							 tmp = item;
						}

						item.FilterItem = tmp;
					}
					else if (typeof filterField === 'function')
					{
						item.FilterItem = filterField(item);
					}
					return item;
				});
			}
		}

		return selectedData;
	};
})();

(function()
{
	createNamespace('TF.Modal').ListMoverForListFilterControlModalViewModel = ListMoverForListFilterControlModalViewModel;

	function ListMoverForListFilterControlModalViewModel(selectedData, options)
	{
		tf.modalManager.useShortCutKey = true;
		var title = options.FullDisplayFilterTypeName || "Filter " + (options.DisplayFilterTypeName || options.OriginalName);
		var defaultOption = {
			title: title,
			description: `Select the ${options.DisplayFilterTypeName || options.OriginalName} that you would like to view.`,
			availableTitle: "Available",
			selectedTitle: "Selected",
			displayCheckbox: false,
			showRemoveColumnButton: true,
			type: options.GridType
		};

		options = $.extend({}, defaultOption, options);

		this.filterField = options.filterField;

		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverForListFilterControlViewModel = new TF.Control.ListMoverForListFilterControlViewModel(selectedData, options);
		this.data(this.ListMoverForListFilterControlViewModel);
		this.inheritChildrenShortCutKey = false;
	}

	ListMoverForListFilterControlModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverForListFilterControlModalViewModel.prototype.constructor = ListMoverForListFilterControlModalViewModel;

	ListMoverForListFilterControlModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.ListMoverForListFilterControlViewModel.apply().then(function(result)
		{
			result = TF.ListMoverForListFilterHelper.processSelectedData(result, self.filterField);
			result.sort(function(a, b) { return a.FilterItem ? a.FilterItem.localeCompare(b.FilterItem) : 0; });
			self.positiveClose(result);
		});
	};

	ListMoverForListFilterControlModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverForListFilterControlViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
			else
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};
})();
