(function()
{
	ko.components.register('distance-buffer-component', {

		template: '<div class="col-xs-24">' +
			'                        <div class="row">' +
			'                            <div class="form-group small-form-group col-xs-12">' +
			'                                <label>Distance</label>' +
			'                                <div class="input-group">' +
			'                                    <input name="walkoutDistance" type="text" class="form-control" data-bind="numericTextBox:{value:walkoutDistance,disable:walkoutDistanceDisable,decimals:3,format: \'#.###\'}"/>' +
			'                                </div>' +
			'                            </div>' +
			'                            <div class="form-group small-form-group col-xs-8">' +
			'                                <label></label>' +
			'                                <div class="input-group" data-bind="css:walkoutDistanceDisable() ? \'disabled\' : \'\'">' +
			'                                    <div name="distanceUnit" data-bind="typeahead:{source:obUnits,drowDownShow:true,notSort:true,selectedValue:obSelectedDistanceUnit}">' +
			'                                        <!-- ko customInput:{type:"Select",value:obSelectedDistanceUnitText,attributes:{class:"form-control"},disable:walkoutDistanceDisable} -->' +
			'                                        <!-- /ko -->' +
			'                                    </div>' +
			'                                    <div class="input-group-btn">' +
			'                                        <button data-bind="" type="button" class="btn btn-default btn-sharp">' +
			'                                            <span class="caret"></span>' +
			'                                        </button>' +
			'                                    </div>' +
			'                                </div>' +
			'                            </div>' +
			'                        </div>' +
			'                    </div>' +
			'                    <div class="col-xs-24" data-bind="visible:obBufferVisible()">' +
			'                        <div class="row">' +
			'                            <div class="form-group small-form-group col-xs-12">' +
			'                                <label>Buffer</label>' +
			'                                <div class="input-group">' +
			'                                    <input name="walkoutBuffer" type="text" class="form-control" data-bind="numericTextBox:{value:walkoutBuffer,disable:bufferDisable,decimals:3,format: \'#.###\'}" />' +
			'                                </div>' +
			'                            </div>' +
			'                            <div class="form-group small-form-group col-xs-8">' +
			'                                <label></label>' +
			'                                <div class="input-group">' +
			'                                    <div data-bind="typeahead:{source:obUnits,drowDownShow:true,notSort:true,selectedValue:obSelectedBufferUnit}">' +
			'                                        <!-- ko customInput:{type:"Select",value:obSelectedBufferUnitText,attributes:{class:"form-control"},disable:bufferDisable} -->' +
			'                                        <!-- /ko -->' +
			'                                    </div>' +
			'                                    <div class="input-group-btn">' +
			'                                        <button type="button" class="btn btn-default btn-sharp">' +
			'                                            <span class="caret"></span>' +
			'                                        </button>' +
			'                                    </div>' +
			'                                </div>' +
			'                            </div>' +
			'                        </div>' +
			'                    </div>',
		viewModel: function(params)
		{
			var self = this;
			this.obUnits = params.unites || ko.observableArray(["meters", "feet", "kilometers", "miles", "yards"]);
			this.walkoutDistance = params.walkoutDistance || ko.observable();
			this.bufferDisable = params.bufferDisable || ko.observable();
			this.obSelectedDistanceUnit = params.obSelectedDistanceUnit || ko.observable();
			this.obSelectedDistanceUnitText = ko.computed(function()
			{
				return this.obSelectedDistanceUnit();
			}, this);

			this.obBufferVisible = params.bufferVisible || ko.observable(true);
			this.walkoutBuffer = params.walkoutBuffer || ko.observable();
			this.obSelectedBufferUnit = params.obSelectedBufferUnit || ko.observable();
			this.obSelectedBufferUnitText = ko.computed(function()
			{
				return this.obSelectedBufferUnit();
			}, this);

			!this.obSelectedDistanceUnit() && this.obSelectedDistanceUnit(this.obUnits()[0]);
			!this.obSelectedBufferUnit() && this.obSelectedBufferUnit(this.obUnits()[0]);
			!this.walkoutDistance() && this.walkoutDistance(100);
			!this.walkoutBuffer() && this.walkoutBuffer(30);

			this.walkoutDistanceDisable = params.walkoutDistanceDisable || ko.observable(false);

		}
	});
})();

