﻿(function()
{
	createNamespace("TF.Modal").LoginModalViewModel = LoginModalViewModel;

	function LoginModalViewModel(contentTemplate, preLoginInfo)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.OutSizeClickEnable = true;
		this.forgotPasswordClick = this.forgotPasswordClick.bind(this);
		this.resetPasswordClick = this.resetPasswordClick.bind(this);
		this.cancelClick = this.cancelClick.bind(this);
		this.resendCodeClick = this.resendCodeClick.bind(this);
		this.backClick = this.backClick.bind(this);
		this.obCloseButtonVisible(false);
		this.obShowLogin = ko.observable(true);
		this.title('');
		this.sizeCss = 'modal-fullscreen';
		this.contentTemplate(contentTemplate);
		this.buttonTemplate('');
		const potentialClientKey = (location.hostname.split(".")[0] || "").trim().toLowerCase();
		const isVanityUrl = !!window.vanitySessionGuard?.vendorAccessInfoCache[potentialClientKey];
		const clientKey = isVanityUrl ? potentialClientKey : (preLoginInfo && preLoginInfo.samlKey ? preLoginInfo.clientKey : null);
		this.loginViewModel = new TF.RoutefinderLoginViewModel(clientKey);
		this.data(this.loginViewModel);
		this.validateIsForgetPassword();
		this.preLoginInfo = preLoginInfo;
		//IDP initiated
		if (this.preLoginInfo)
		{
			if (this.preLoginInfo.errorMessage)
			{
				this.loginViewModel.obLoginErrorMessage(this.preLoginInfo.errorMessage);
				this.loginViewModel.obUsername("");
			}
			else if (this.preLoginInfo.samlKey)
			{
				this.loginViewModel.obUsername(this.preLoginInfo.samlKey);
				this.positiveClick();
			}
		}
	};

	LoginModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	LoginModalViewModel.prototype.constructor = LoginModalViewModel;

	LoginModalViewModel.prototype.validateIsForgetPassword = function()
	{
		var self = this,
			signature = getQueryString("signature"),
			clientid = getQueryString("clientid");

		self.signature = signature;
		if (signature && clientid)
		{
			tf.promiseAjax.get(TF.Helper.ApiUrlHelper.getAuthInfoWithSignatureUrl(clientid),{
					paramData: {
						signature: signature,
					}
				})
				.then(function(apiResponse)
				{
					self.obShowLogin(false);
					var auth = apiResponse.Items[0];
					self.loginViewModel.obClientKeyRP(auth.clientid);
					self.loginViewModel.obUsernameRP(auth.username);
				})
				.catch(function(apiResponse)
				{
					if (apiResponse.StatusCode == 417)
					{
						tf.promiseBootbox.alert("The reset password link that you used has expired. If you would like to reset your password, click Forgot Password? on the Login page, and a new reset password link will be emailed to you.", "Reset Password Link Has Expired")
							.then(function()
							{
								var emptyInput = $("input:text:empty");
								if (emptyInput.length > 0)
								{
									$(emptyInput[0]).focus();
								}
								else
								{
									$("#password").focus();
								}
							});
					}
					else
					{
						self.loginViewModel.obLoginErrorMessageRP(apiResponse.Message || 'A valid Client ID and User Name are required to reset a password.');
					}
				});
		}
	};

	LoginModalViewModel.prototype.resetPasswordClick = function(viewModel, e)
	{
		var self = this;

		self.loginViewModel.$form.data("bootstrapValidator").validate().then(function(result)
		{
			if (result)
			{
				self.resetPasswordDetails();
			}
		});
	};

	LoginModalViewModel.prototype.resetPasswordDetails = function()
	{
		var clientKey = $.trim(this.loginViewModel.obClientKeyRP());
		var userName = $.trim(this.loginViewModel.obUsernameRP());
		var passwordRP = this.loginViewModel.obPasswordRP();
		var verifyPasswordRP = this.loginViewModel.obVerifyPasswordRP();
		if (clientKey === "" || userName === "")
		{
			this.loginViewModel.obLoginErrorMessageRP('A valid Client ID and User Name are required to reset a password.');
			return;
		}
		if ($.trim(passwordRP) === "" || $.trim(verifyPasswordRP) === "" || passwordRP != verifyPasswordRP)
		{
			this.loginViewModel.obLoginErrorMessageRP('Verify Password must match Password.');
			return;
		}
		if (this.signature)
		{
			var resetPasswordData = {
				paramData: { signature: this.signature },
				data: '"' + passwordRP + '"'
			};
			tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postResetPasswordUrl(clientKey), resetPasswordData)
				.then(function(apiResponse)
				{
					tf.promiseBootbox.alert("The Password for " + userName + " has been successfully Reset.", "Password Successfully Reset")
						.then(function()
						{
							this.obShowLogin(true);
							$("#password").focus();
							this.loginViewModel.bindValidator($("form.loginform"));
						}.bind(this));
				}.bind(this))
				.catch(function(apiResponse)
				{
					tf.promiseBootbox.yesNo({
						buttons: {
							yes: {
								label: "Try Again",
								className: "tf-btn-black btn-sm"
							},
							no: {
								label: "Cancel",
								className: "btn-default btn-sm"
							}
						},
						message: "The Password for " + userName + " could not be Reset. You can try again or cancel."
					},
						"Unable to Reset Password")
						.then(function(result)
						{
							if (!result)
							{
								this.obShowLogin(true);
								$("#password").focus();
								this.loginViewModel.bindValidator($("form.loginform"));
							}
						}.bind(this));
				}.bind(this));
		}
	};

	LoginModalViewModel.prototype.cancelClick = function(viewModel, e)
	{
		tf.promiseBootbox.yesNo("Are you sure that you want to cancel your password reset.",
			"Confirmation Message")
			.then(function(result)
			{
				if (result)
				{
					this.obShowLogin(true);
					$("#password").focus();
					this.loginViewModel.bindValidator($("form.loginform"));
				}
			}.bind(this));
	};

	LoginModalViewModel.prototype.forgotPasswordClick = function(viewModel, e)
	{
		// Currently, only when it is a SSO user, will password field be disabled.
		const isSSOUser = !this.loginViewModel.obEnablePassword();
		if (isSSOUser)
		{
			const ssoUserAlertMsg = "This is an SSO account please contact your administrator for password reset.";
			return tf.promiseBootbox.alert(ssoUserAlertMsg);
		}

		var clientKey = $.trim(this.loginViewModel.obClientKey());
		var userName = $.trim(this.loginViewModel.obUsername());
		if (clientKey === "" || userName === "")
		{
			this.loginViewModel.obLoginErrorMessage('A valid Client ID and User Name are required to reset a password.');
			return;
		}
		tf.storageManager.save("token", "", true);
		var forgetPasswordData = {
			paramData: {
				product: "rfweb",
				username: userName,
				vendor: "Transfinder"
			},
			headers: { "tf-referer": `${location.origin}${location.pathname}` },
		};
		tf.promiseAjax.get(pathCombine(tf.api.server(), clientKey, "passwords"), forgetPasswordData)
			.then(function(apiResponse)
			{
				tf.promiseBootbox.alert("An email has been sent to you with instructions for resetting your password.", "Password Reset Email Sent")
					.then(function()
					{
						$("#password").focus();
					}.bind(this));
			})
			.catch(function(apiResponse)
			{
				if (apiResponse.StatusCode == 404)
				{
					var message = apiResponse.Message || 'A valid Client ID and User Name are required to reset a password.';
					this.loginViewModel.obLoginErrorMessage(message);
				} else
				{
					tf.promiseBootbox.alert("An email could not be sent. Please contact your System Administrator to verify Routefinder's email configuration and settings.", "Password Reset Email Could Not be Sent")
						.then(function()
						{
							$("#password").focus();
						}.bind(this));
				}
			}.bind(this));
	};

	LoginModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this,
			[prefix] = (tf.storageManager.prefix || "").split(".");

		const isManuallyLogin = !!(viewModel && e && e.target);
		tf.storageManager.save("isManuallyLogin", isManuallyLogin, true);
		self.loginViewModel.obLoginConfigErrorMessageVisible(false);
		self.loginViewModel.resetErrorMessage();
		tf.storageManager.save("rememberMe", self.loginViewModel.obRememberMe(), true);
		const samlKey = this.preLoginInfo && this.preLoginInfo.samlKey;
		self.loginViewModel.apply(samlKey).then(function(result)
		{
			if (result)
			{
				tf.promiseAjax.get(pathCombine(tf.api.server(), $.trim(self.loginViewModel.obClientKey()), "authinfos"), {
					paramData: {
						prefix: prefix,
						isSimple: true
					},
					error: function(message, status)
					{
						if (message)
						{
							self.loginViewModel.obLoginErrorMessage(message.Message);
						}
					}
				}, {
					auth:
					{
						noInterupt: true
					}
				}).then(function()
				{
					var clientKey = $.trim(self.loginViewModel.obClientKey());
					var userName = $.trim(self.loginViewModel.obUsername());
					tf.promiseAjax.get(pathCombine(tf.api.server(), clientKey, "vendoraccessinfo"))
						.then(function(response)
						{
							var productNames = response.Items[0].Products || [];
							self.loginViewModel.obLoginErrorMessage('');
							if (productNames.indexOf("RoutefinderWeb") == -1 && productNames.indexOf("RoutefinderPlus") == -1)
							{
								self.loginViewModel.obLoginErrorMessage('Routefinder Plus is not enabled for this Client ID.  Contact <NAME_EMAIL> or 888-427-2403 to inquire about enabling this product.');
								return;
							}

							//set clientKey incase it is undefined when validate
							tf.storageManager.save("clientKey", clientKey, true);
							tf.storageManager.save("userName", userName, true);
							//set clientKey incase to use it when get all preference

							if (tf.authManager)
							{
								tf.authManager.clientKey = clientKey;
							}
							self.positiveClose(result);
						});
				}).catch(function()
				{
				});
			}
		}).catch(function(apiResponse)
		{
			var message = apiResponse.Message;
			if (typeof message == "string")
			{
				if (message === "Invalid Time")
				{
					message = "You cannot login. Your computer's current time does not match the server Routefinder Plus is installed on. Contact your System Administrator.";
				}
				if (message === "Invalid Configurations")
				{
					message = "Routefinder Plus is not properly configured.  You cannot login.  Contact <NAME_EMAIL> or 888-427-2403.";
				}

				if (apiResponse.StatusCode == 401)
				{
					var message = apiResponse.Message || 'Invalid Client ID, User Name and Password combination.';
				}
				self.loginViewModel.obLoginErrorMessage(message);
				self.preLoginInfo = null;
				tf.loadingIndicator.tryHide();
			}
		});
	};

	LoginModalViewModel.prototype.backClick = function()
	{
		this.loginViewModel.backFromMFAtoLogin();
		this.loginViewModel.bindValidator();
	};

	LoginModalViewModel.prototype.resendCodeClick = function()
	{
		this.loginViewModel.generatePin();
	};
})();