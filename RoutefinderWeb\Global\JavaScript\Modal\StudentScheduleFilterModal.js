(function()
{
	createNamespace('TF.Modal').StudentScheduleFilterModal = StudentScheduleFilterModal;

	function StudentScheduleFilterModal(filterSet)
	{
		TF.Modal.BaseModalViewModel.call(this);
		var self = this;
		self.title("Student Schedule Filter");
		self.contentTemplate('modal/StudentScheduleFilter');
		self.buttonTemplate('modal/positivenegative');

		self.toSchool = ko.observable(true);
		self.fromSchool = ko.observable(true);
		self.startDate = ko.observable();
		self.endDate = ko.observable();
		self.vehicles = ko.observableArray();
		TF.DayOfWeek.all.forEach(function(item)
		{
			self[item.toLowerCase()] = ko.observable(true);
		});

		self.mondayToFriday = ko.pureComputed({
			read: function()
			{
				return self.monday() && self.tuesday() && self.wednesday() && self.thursday() && self.friday();
			},
			write: function(v)
			{
				self.monday(v);
				self.tuesday(v);
				self.wednesday(v);
				self.thursday(v);
				self.friday(v);
			}
		});

		TF.DayOfWeek.allSortByValue.forEach(function(item, index)
		{
			var dayOfWeek = index, key = item.toLowerCase();
			self[key + "Enable"] = ko.pureComputed(function()
			{
				var start = self.startDate(), end = self.endDate();
				if (start == null || end == null)
				{
					return true;
				}

				start = new Date(start.getFullYear(), start.getMonth(), start.getDate());
				for (; start <= end; start.setDate(start.getDate() + 1))
				{
					if (start.getDay() == dayOfWeek)
					{
						return true;
					}
				}

				return false;
			});

			self[key + "Enable"].subscribe(function()
			{
				if (!self[key + "Enable"]())
				{
					self[key](false);
				}
			});
		});

		self.mondayToFridayEnable = ko.pureComputed(function()
		{
			for (var i = 1; i <= 5; i++)
			{
				var key = TF.DayOfWeek.allSortByValue[i].toLowerCase();
				if (!self[key + 'Enable']())
				{
					return false;
				}
			}

			return true;
		});

		self.allVehicles = {};
		self.initData(filterSet);
		self.data(self);
	}

	StudentScheduleFilterModal.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	StudentScheduleFilterModal.prototype.constructor = StudentScheduleFilterModal;

	StudentScheduleFilterModal.prototype.init = function(model, element)
	{
		return this.initValidator(element);
	};

	StudentScheduleFilterModal.prototype.initValidator = function(element)
	{
		var self = this,
			container = $(element),
			validateDates = function()
			{
				var endDate = self.endDate();
				if (!endDate) return true;

				var startDate = self.startDate();
				if (!startDate) return true;

				return startDate <= endDate;
			},
			validatorFields = {
				weekdays: {
					container: container.find(".days"),
					validators: {
						choice: {
							min: 1,
							message: 'Please choose one week day at least.'
						}
					}
				},
				types: {
					container: container.find(".types"),
					validators: {
						choice: {
							min: 1,
							message: 'Please choose one type at least.'
						}
					}
				},
				startDate: {
					trigger: "blur change",
					validators: {
						callback: {
							message: "must <= End Date",
							callback: validateDates
						}
					}
				},
				endDate: {
					trigger: "blur change",
					validators: {
						callback: {
							message: "must >= Start Date",
							callback: validateDates
						}
					}
				}
			};

		self.validator = container.bootstrapValidator({
			excluded: [".data-bv-excluded"],
			fields: validatorFields
		}).data("bootstrapValidator");
	};

	StudentScheduleFilterModal.prototype.initData = function(filterSet)
	{
		var self = this;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles"), { "@field": "Id, BusNum" }).then(function(response)
		{
			response.Items.forEach(function(item)
			{
				self.allVehicles[item.Id] = item;
			});

			self.applyFilterSet(filterSet);
		});
	};

	StudentScheduleFilterModal.prototype.selectVehicles = function()
	{
		var self = this;
		var listFilterTemplate = TF.ListFilterDefinition.ListFilterTemplate.BusfinderHistoricalVehicle;
		listFilterTemplate.DisplayFilterTypeName = "Vehicles";
		listFilterTemplate.FullDisplayFilterTypeName = 'Select Vehicles';
		return tf.modalManager.showModal(new TF.Modal.ListMoverForListFilterControlModalViewModel(self.vehicles(), listFilterTemplate))
			.then(function(selectedFilterItems)
			{
				if (selectedFilterItems !== false) self.vehicles(selectedFilterItems);
			});
	};

	StudentScheduleFilterModal.prototype.afterRender = function()
	{
		// do nothing
	};

	StudentScheduleFilterModal.prototype.getFilterSet = function()
	{
		var self = this,
			filterItems = [],
			filterSet = { FilterItems: filterItems },
			days = [];
		TF.DayOfWeek.all.forEach(function(day)
		{
			if (self[day.toLowerCase()]())
			{
				var dayValue = TF.DayOfWeek[day];
				days.push(dayValue);
			}
		});

		if (self.vehicles().length)
		{
			filterItems.push({
				FieldName: "VehicleId",
				Operator: "In",
				ValueList: JSON.stringify(self.vehicles().map(function(i) { return i.Id; }))
			});
		}

		if (days.length && days.length < TF.DayOfWeek.all.length)
		{
			filterItems.push({
				FieldName: "Days",
				Operator: "In",
				ValueList: JSON.stringify(days)
			});
		}

		if (self.startDate())
		{
			filterItems.push({
				FieldName: "StartDate",
				Operator: "GreaterThanOrEqualTo",
				Value: TF.toDateString(self.startDate())
			});
		}

		if (self.endDate())
		{
			filterItems.push({
				FieldName: "EndDate",
				Operator: "LessThanOrEqualTo",
				Value: TF.toDateString(self.endDate())
			});
		}

		var sessions = [];
		if (self.toSchool())
		{
			sessions.push(TF.SessionType.ToSchool);
		}

		if (self.fromSchool())
		{
			sessions.push(TF.SessionType.FromSchool);
		}

		if (sessions.length == 1)
		{
			filterItems.push({
				FieldName: "SessionId",
				Operator: "EqualTo",
				Value: sessions[0].toString()
			});
		}

		if (!filterSet.FilterItems.length)
		{
			return null;
		}

		return filterSet;
	};

	StudentScheduleFilterModal.prototype.applyFilterSet = function(filterSet)
	{
		if (!filterSet || !filterSet.FilterItems || !filterSet.FilterItems.length)
		{
			return;
		}

		var self = this, filterItems = filterSet.FilterItems;
		filterItems.forEach(function(item)
		{
			if (!item || (!item.Value && !item.ValueList)) return;
			try
			{
				switch (item.FieldName)
				{
					case "SessionId":
						var type = parseInt(item.Value);
						self.toSchool(type === TF.SessionType.ToSchool);
						self.fromSchool(type === TF.SessionType.FromSchool);
						break;
					case "StartDate":
						self.startDate(new Date(item.Value));
						break;
					case "EndDate":
						self.endDate(new Date(item.Value));
						break;
					case "Days":
						var days = JSON.parse(item.ValueList).map(function(i)
						{
							return parseInt(i);
						});
						if (days.length)
						{
							TF.DayOfWeek.all.forEach(function(day)
							{
								self[day.toLowerCase()](days.indexOf(TF.DayOfWeek[day]) > -1);
							});
						}
						break;
					case "VehicleId":
						var vehicles = [];
						JSON.parse(item.ValueList).forEach(function(i)
						{
							var vehicle = self.allVehicles[i];
							if (vehicle)
							{
								vehicles.push(vehicle);
							}
						});
						self.vehicles(vehicles);
						break;

				}
			} catch (ex) { }
		});
	};

	StudentScheduleFilterModal.prototype.validate = function()
	{
		return this.validator.validate();
	};

	StudentScheduleFilterModal.prototype.positiveClick = function()
	{
		var self = this;
		self.validate().then(function(valid)
		{
			if (valid)
			{
				self.positiveClose({ value: self.getFilterSet() });
			}
		});
	};
})();

