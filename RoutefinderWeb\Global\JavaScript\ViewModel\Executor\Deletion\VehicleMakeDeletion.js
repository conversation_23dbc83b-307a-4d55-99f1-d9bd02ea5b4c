﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleMakeDeletion = VehicleMakeDeletion;

	function VehicleMakeDeletion()
	{
		this.type = 'vehiclemake';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleMakeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleMakeDeletion.prototype.constructor = VehicleMakeDeletion;

	VehicleMakeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();