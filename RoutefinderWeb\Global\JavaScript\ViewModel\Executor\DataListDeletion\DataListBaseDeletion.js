﻿(function()
{
	createNamespace("TF.Executor").DataListBaseDeletion = DataListBaseDeletion;
	function DataListBaseDeletion(withoutDatabase)
	{
		this.ids = [];
		this.deleteIds = [];
		this.disable = false;
		this.item = null;
		this.apiPrefix = withoutDatabase ? tf.api.apiPrefixWithoutDatabase() : tf.api.apiPrefix();
	}

	DataListBaseDeletion.prototype.execute = function(ids, item)
	{
		this.item = item;
		if (!$.isArray(ids))
		{
			ids = [ids];
		}
		if (ids.length === 0)
		{
			return Promise.resolve();
		}
		if (ids.length === 1)
		{
			return this.deleteSingle(ids);
		} else
		{
			return this.deleteMultiple(ids);
		}
	};

	DataListBaseDeletion.prototype.deleteMultiple = function(ids)
	{
		this.ids = ids;
		return this.deleteMultipleVerify()
			.then(function()
			{
				return this.deleteConfirm();
			}.bind(this))
			.then(function()
			{
				return this.deleteSelectedItems();
			}.bind(this));
	};

	DataListBaseDeletion.prototype.deleteSingle = function(id)
	{
		this.ids = $.isNumeric(id) ? [id] : id;
		return this.deleteSingleVerify()
			.then(function()
			{
				return this.deleteConfirm();
			}.bind(this))
			.then(function()
			{
				return this.deleteSelectedItems();
			}.bind(this));
	};

	DataListBaseDeletion.prototype.getEntityStatus = function()
	{
		return tf.promiseAjax.get(pathCombine(this.apiPrefix, this.type, 'entitystatus') + '?' + $.param({ ids: this.ids }));
	};

	DataListBaseDeletion.prototype.deleteSingleVerify = function()
	{
		return this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				return tf.promiseBootbox.alert(i18n.t('deletion.single_lock'));
			} else
			{
				this.deleteIds = this.ids;
			}
		}.bind(this));
	};

	DataListBaseDeletion.prototype.deleteMultipleVerify = function()
	{
		return this.getEntityStatus().then(function(response)
		{
			var items = response.Items,
				allCount = this.ids.length;
			var lockedCount = items.filter(function(item)
			{
				return item.Status === 'Locked';
			}).length;
			var unLockedIds = items.filter(function(item)
			{
				return item.Status !== 'Locked';
			}).map(function(item) { return item.Id; });

			if (lockedCount === allCount)
			{
				return tf.promiseBootbox.alert(i18n.t('deletion.multiple_all_lock', { count: allCount.toString() }));
			}
			if (lockedCount > 0 && lockedCount < allCount)
			{
				return tf.promiseBootbox.yesNo(i18n.t('deletion.multiple_lock', { count: lockedCount.toString(), total: allCount }))
					.then(function(ans)
					{
						if (ans)
						{
							this.deleteIds = unLockedIds;
						}
					}.bind(this));
			};
			this.deleteIds = unLockedIds;
		}.bind(this));
	};

	DataListBaseDeletion.prototype.deleteConfirm = function()
	{
		if (this.deleteIds.length === 0)
		{
			return;
		}
		var singleOrMultiple = this.deleteIds.length > 1 ? 'multiple' : 'single';
		return this.getAssociatedData(this.deleteIds).then(function(response)
		{
			if (!response)
			{
				return;
			}
			//build second confirm message
			var deleteType = this.deleteType;
			var deleteRecordName = this.deleteRecordName;
			var associatedItems = response.filter(
				function(item)
				{
					return item.items.length > 0;
				});

			if (associatedItems.length > 0)
			{
				var isDisplayName = this.isDisplayName;
				var rejectMessage = associatedItems[0].message ? associatedItems[0].message : ((isDisplayName ? deleteType : "This " + deleteType.toLowerCase()) + " is associated with one or more records.  It cannot be deleted.");
				var rejectTitle = deleteType + " Cannot be Deleted";
				return tf.promiseBootbox.alert(rejectMessage, rejectTitle)
					.then(function()
					{
						this.deleteIds = [];
					}.bind(this));
			}
			else
			{
				var confirmMessage = "Are you sure you want to delete this " + deleteRecordName + "?";
				return tf.promiseBootbox.yesNo(confirmMessage, "Delete Confirmation")
					.then(function(ans)
					{
						//if confirm no , delete nothing
						if (!ans)
						{
							this.deleteIds = [];
						}
					}.bind(this));
			}
		}.bind(this));
	};

	DataListBaseDeletion.prototype.deleteSelectedItems = function()
	{
		if (this.deleteIds.length === 0)
		{
			return;
		}
		var ids = this.deleteIds,
			requestData = ids;
		if ($.isFunction(this.getDeletionData))
		{
			requestData = this.getDeletionData();
		}

		var deleteFunc = tf.promiseAjax.delete(pathCombine(this.apiPrefix, tf.dataTypeHelper.getEndpoint(this.type)),
			{
				paramData: { "@filter": "in(Id," + requestData.join(",") + ")" }
			});

		return deleteFunc.then(function()
		{
			this.publishData(ids);
			return ids;
		}.bind(this))
			.catch(function()
			{
			}.bind(this));
	};

	DataListBaseDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, this.type, pb.DELETE), ids);
	};

	DataListBaseDeletion.prototype.getAssociatedData = function()
	{
		return Promise.resolve([]);
	};
})();


