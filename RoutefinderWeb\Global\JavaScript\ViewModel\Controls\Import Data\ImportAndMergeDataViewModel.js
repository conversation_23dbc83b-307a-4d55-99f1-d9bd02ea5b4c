(function()
{
	createNamespace('TF.Control').ImportAndMergeDataViewModel = ImportAndMergeDataViewModel;

	function ImportAndMergeDataViewModel()
	{
		var self = this;

		self.fileName = "";
		self.tfdfile = null;

		self.obImportParameter = ko.observable("0");
		self.obFileAccept = ko.observable();
		self.obDataSources = ko.observableArray();
		self.selectedDataSource = ko.observable();
		self.selectedDataSourceName = ko.computed(function()
		{
			return self.selectedDataSource() ? self.selectedDataSource().Name : '';
		});

		self.disableDataSource = ko.computed(function()
		{
			return self.obImportParameter() !== '1';
		});

		//Events
		self.UploadedFileChangeEvent = self.UploadedFileChangeEvent.bind(self);

		self.onFileUploadComplete = new TF.Events.Event();
	};


	/**
	 * Initialize the advanced and merge data modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	ImportAndMergeDataViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self.$element = $(el);

		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "databases")).then(function(response)
		{
			self.obDataSources(response.Items);
			self.selectedDataSource(self.obDataSources()[0]);
		});
	};

	/**
	 * Open the select data file windows modal.
	 * @param {Object} type The file type.
	 * @return {void}
	 */
	ImportAndMergeDataViewModel.prototype.selectDataFile = function(result, importFileType)
	{
		var self = this;
		self.obFileAccept(getAcceptedFileType(result.type));
		self.fileName = result.fileName;
		self.tfdfile = result.tfdfile;

		self.$element.find(".input-file").val("");
		var acceptedFile = getAcceptedFileType(importFileType);
		if (acceptedFile != null)
		{
			self.obFileAccept(acceptedFile.ext);
		}

		self.$element.find(".input-file").trigger("click");
	};

	function getAcceptedFileType(typFlag)
	{
		var FileTypeObject = {
			"0": { "name": "Fixed Record Length Text File (txt)", "ext": ".txt" },
			"1": { "name": "Tab Delmited Text File (tab, dat)", "ext": ".tab,.dat" },
			"2": { "name": "Comma Delmited Text File (csv)", "ext": ".csv" },
			"3": { "name": "Transfinder TFX File (tfx)", "ext": ".tfx" },
			"4": { "name": "DBF File (dbf)", "ext": ".dbf" },
			"5": { "name": "MS Excel Spread Sheet (xls)", "ext": ".xls" },
			"6": { "name": "Pipe Delimited File (|)", "ext": "" }
		};
		if (FileTypeObject[typFlag] != null)
		{
			return FileTypeObject[typFlag];
		}
		else
		{
			return null;
		}
	}

	/**
	 * The event of upload a new file.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} e The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	ImportAndMergeDataViewModel.prototype.UploadedFileChangeEvent = function(viewModel, e)
	{
		var self = this, files = e.target.files;
		if (files.length > 0)
		{
			self.UploadedFile(files[0]);
		}
	};

	/**
	 * Upload a new file.
	 * @param {Object} file The file info.
	 * @return {void}
	 */
	ImportAndMergeDataViewModel.prototype.UploadedFile = function(file)
	{
		var self = this, fileModel = { FileName: file.name }, reader = new FileReader();
		reader.fileName = file.name;
		reader.onload = function(event)
		{
			fileModel.FileContent = event.target.result;
			var requestData = {
				ExternameFileInfo: fileModel,
				TFDFileInfo: self.tfdfile ? self.tfdfile : { FileName: "", FileContent: "" },
				FileName: self.fileName ? self.fileName : ""
			};
			self.onFileUploadComplete.notify(requestData);
		};
		reader.readAsDataURL(file);
	};
})();

