(function()
{
    var namespace = window.createNamespace("TF.DataModel");

	namespace.CustomizedDashboardDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	}

	namespace.CustomizedDashboardDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.CustomizedDashboardDataModel.prototype.constructor = namespace.CustomizedDashboardDataModel;

	namespace.CustomizedDashboardDataModel.prototype.mapping = [
		{ from: "DashboardId", to: "id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "Description", default: "" }
	];
})();