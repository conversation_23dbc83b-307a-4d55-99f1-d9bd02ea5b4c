(function()
{
	// the base class of map
	createNamespace("TF.Map").HackDrawPath = HackDrawPath;

	function HackDrawPath(GraphicsLayer)
	{
		var self = this;
		self.isDefsAdded = false;
		GraphicsLayer.prototype._symbolizeShape = function(a, u, g, d, f)
		{
			var m = u.getStroke(), z = u.getFill(), c = u.type, G, I, e = -1 !== c.indexOf("linesymbol"), r = -1 !== c.indexOf("fillsymbol") ? null : this._getVariable(g, "sizeInfo", !1), n = this._getVariable(g, "colorInfo", !1), h = this._getVariable(g, "opacityInfo", !1), E = e ? "none" !== u.style : u.outline && "none" !== u.outline.style, x = e ? null : this._getVariable(g, "sizeInfo", "outline"), r = (r = d ? x : x || r) ? g.getSize(f, {
				sizeInfo: r,
				resolution: this._map.getResolutionInMeters(),
				scale: this._map.getScale()
			}) : null;
			d && (n = h = null);
			(n || h) && "picturefillsymbol" !== c && (e ? (G = m && m.color,
				n && (G = g.getColor(f, {
					colorInfo: n
				}) || G),
				G && h && (G = this._applyOpacity(G, g, h, f))) : z && z.toCss && (I = z,
					n && (I = g.getColor(f, {
						colorInfo: n
					}) || I),
					I && h && (I = this._applyOpacity(I, g, h, f))));
			a.setStroke(!E || null == r && !G ? m : b.mixin({}, m, null != r ? {
				width: r
			} : null, G && {
				color: G
			})).setFill(I || z);
			e && this._applyLineMarker(a, u)
			self.changeLineStyle(a, u)
		}
	};

	HackDrawPath.prototype.changeLineStyle = function(svgObject, symbol)
	{
		if (svgObject.shape.type != 'path' || svgObject.rawNode.getAttribute("d").indexOf("Z") > 0)
		{
			return
		}
		var lineStyle = TF.Map.MapLineStyle.getByPattern(symbol.pattern)
		if (lineStyle && lineStyle.svg)
		{
			var points = svgObject.segments[0].args
			var additionPath = "";
			var insertPoints = getInsertPoints(points, 30)
			insertPoints.forEach(function(pointInfo)
			{
				additionPath += TF.Map.MapLineStyle.transform(pointInfo.angle, pointInfo.point, lineStyle.svg);
			})
			if (additionPath.length > 1)
			{
				svgObject.rawNode.setAttribute("d", svgObject.rawNode.getAttribute("d") + additionPath)
			}
		}
	}

	function getInsertPoints(points, stepLength)
	{
		var pointArray = []
		var insertPoints = []
		for (var i = 0; i < points.length - 1; i += 2)
		{
			pointArray.push([points[i], points[i + 1]])
		}

		var length = 0, sPoint, ePoint, calLength;
		for (var i = 0; i < pointArray.length - 1; i++)
		{
			var startPoint = pointArray[i]
			var endPoint = pointArray[i + 1]
			var len = calculateDistance(startPoint, endPoint)
			length += len
			if (length > stepLength)
			{
				sPoint = startPoint;
				ePoint = endPoint;
				calLength = stepLength - (length - len);
				var point = calculatePointByBuffer(calLength, sPoint, ePoint);

				var angle = 0;
				if (ePoint[0] != sPoint[0])
				{
					angle = Math.atan((sPoint[1] - ePoint[1]) / (ePoint[0] - sPoint[0]));
					if (ePoint[0] - sPoint[0] < 0)
					{
						angle += Math.PI;
					}
				}

				insertPoints.push({
					point: point,
					angle: angle
				});
				pointArray.splice(i + 1, 0, [point[0], point[1]]);
				length = 0;
			}

		}
		return insertPoints
	}

	function calculateDistance(p1, p2)
	{
		return Math.sqrt(Math.pow((p2[0] - p1[0]), 2) + Math.pow((p2[1] - p1[1]), 2));
	}

	function calculatePointByBuffer(buffer, sp, ep)
	{
		var ratio = buffer / calculateDistance(sp, ep);
		return [(1 - ratio) * sp[0] + ratio * ep[0], (1 - ratio) * sp[1] + ratio * ep[1]]
	}

})()