﻿(function()
{
	createNamespace("TF.Control").NewMergeDocumentViewModel = NewMergeDocumentViewModel;

	function NewMergeDocumentViewModel(model, dataType)
	{
		var self = this;
		var isCopy = !!model;
		self.dataType = dataType;
		self.endpoint = tf.dataTypeHelper.getEndpoint(self.dataType);
		self.displayName = tf.dataTypeHelper.getDisplayNameByDataType(self.dataType);
		self.singleName = tf.applicationTerm.getApplicationTermSingularByName(self.displayName);
		self.isMergeEmailMessage = self.dataType === 'mergeemailmessage';
		self.supportedMergeDocumentDataTypes = tf.dataTypeHelper.getSupportedMergeDocumentDataTypes() || [];
		model = model || new TF.DataModel.MergeDocumentModel({
			DataTypeId: self.supportedMergeDocumentDataTypes.length > 0 && self.supportedMergeDocumentDataTypes[0].id,
			TemplateTypeId: self.isMergeEmailMessage ? TF.MergeTemplateTypeHelper.emailTypeId : TF.MergeTemplateTypeHelper.initTypeId,
			Name: ""
		});

		self.obEntityDataModel = ko.observable(model);
		self.obAdvancedSettingExpended = ko.observable(false);

		self.obSelectedTemplate = ko.pureComputed(function()
		{
			if (self.pageOrientationDropDownList)
			{
				self.pageOrientationDropDownList.data('kendoDropDownList').select(self.obEntityDataModel().templateType().orientation());
			}

			if (self.removeValidateMessage)
			{
				self.removeValidateMessage(self.sizeFieldsSelector + "," + "[data-bv-for='templateName']");
			}
			return self.obEntityDataModel().templateType();
		});

		self.recalculatePageSettingTimer;
		self.recalculatePageSetting = function(options)
		{
			if (!self.obSelectedTemplate().hasPageSettings()) return {};

			if (self.recalculatePageSettingTimer)
			{
				clearTimeout(self.recalculatePageSettingTimer);
			}
			self.recalculatePageSettingTimer = setTimeout(function()
			{
				return self._recalculatePageSetting(options);
			}, 200);
		}.bind(self);

		self._recalculatePageSetting = function(options)
		{
			if (!self.obSelectedTemplate().hasPageSettings()) return {};

			self.prevTemplateData = self.prevTemplateData || new TF.DataModel.MergeTemplateTypeModel();
			var newTemplate = TF.MergeTemplateTypeCalculationHelper.recalculatePageSettingFields(
				self.obSelectedTemplate().toData(), options, self.prevTemplateData.toData());
			TF.MergeTemplateTypeHelper.resetTemplateData(newTemplate, self.prevTemplateData);
			TF.MergeTemplateTypeHelper.resetTemplateData(newTemplate, self.obSelectedTemplate());

			return newTemplate;
		}.bind(self);


		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		self.enableHighlightField = false;
		self.obSaveAsTemplateEnabled = ko.computed(function()
		{
			var isCustomTemplate = self.obSelectedTemplate().id() == TF.MergeTemplateTypeHelper.CustomItemID;
			if (self.pageOrientationDropDownList)
			{
				self.pageOrientationDropDownList.data('kendoDropDownList').enable(isCustomTemplate);
			}

			if (isCustomTemplate)
			{
				setTimeout(function()
				{
					self.enableHighlightField = true;
					self.validator.validate();
				}, 100);
			}
			else
			{
				$(self.el).find('.merge-template-filed-changed').removeClass('merge-template-filed-changed');
				self.enableHighlightField = false;
			}
			return isCustomTemplate;
		});

		self.previewLayout = new TF.Control.MergeDocumentPageLayout(self.obSelectedTemplate, true);
		self.obGearButtonVisibility = tf.authManager.authorizationInfo.isAdmin;
		self.isCopy = isCopy;
		self.obIsSmallModal = ko.observable(false);
		self.renderAdvancedSettingTemplate = self.renderAdvancedSettingTemplate.bind(self);
	}

	NewMergeDocumentViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self._renderDropDownList();
		self._initValidator(el);

		self.el = el;
		TF.MergeTemplateTypeHelper.bindMergeTemplateEvents(self);


		self.initRolesAccessControl();
	};

	NewMergeDocumentViewModel.prototype._initValidator = function(el)
	{
		var self = this,
			nameUniqueCheck = function(value)
			{
				if (!value || !value.trim()) return true;

				return tf.promiseAjax.get(
					pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endpoint),
					{
						paramData: {
							"@filter": `eq(Name, ${value})&${self.isMergeEmailMessage ? 'eq' : 'noteq'}(TemplateTypeId, ${TF.MergeTemplateTypeHelper.emailTypeId})`
						},
						async: false
					},
					{ overlay: false }).then(function(apiResponse)
					{
						return !(apiResponse.Items[0])
					});
			},
			templateNameUniqueCheck = function(value)
			{
				if (self.obSaveAsTemplateEnabled())
				{
					if (!value || !value.trim()) return true;

					return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergeTemplateTypes"),
						{
							data: { name: value },
							async: false
						},
						{ overlay: false }).then(function(apiResponse)
						{
							return !(apiResponse.Items[0] && apiResponse.Items[0].Id)
						});
				}
				else
				{
					return true;
				}
			};

		var validatorFields = {
			name: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					callback: {
						message: "must be unique",
						callback: nameUniqueCheck
					}
				}
			},
			templateName: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					callback: {
						message: "must be unique",
						callback: templateNameUniqueCheck
					}
				}
			},
			dataType: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					}
				}
			}
		};

		validatorFields = $.extend({},
			validatorFields,
			TF.MergeTemplateTypeHelper.getAdvancedSettingValidator(self)
		);

		setTimeout(function()
		{
			self.validator = ($(el).bootstrapValidator({
				excluded: [":disabled"],
				live: "enabled",
				fields: validatorFields
			})).data("bootstrapValidator");
		});
	};

	NewMergeDocumentViewModel.prototype._renderDropDownList = function()
	{
		var self = this;
		$("#dataTypeDropDownList").kendoDropDownList({
			change: function(e)
			{
				self.obEntityDataModel().dataTypeId(this.value());
			},
			dataSource: {
				data: self.supportedMergeDocumentDataTypes
			},
			value: self.obEntityDataModel().dataTypeId(),
			dataTextField: 'name',
			dataValueField: 'id'
		});

		$("#mergeTemplateTypeDropDownList").kendoDropDownList({
			change: function(e)
			{
				self.obEntityDataModel().templateTypeId(this.value());
				self.refreshMergeDocumentLayoutDisplay();
			},
			dataSource: {
				data: TF.MergeTemplateTypeHelper.getMergeTemplateTypesWithCustomItem(!self.isMergeEmailMessage),
			},
			value: self.obEntityDataModel().templateTypeId(),
			dataTextField: 'DisplayName',
			dataValueField: 'Id'
		});
	};

	NewMergeDocumentViewModel.prototype.refreshMergeDocumentLayoutDisplay = function()
	{
		// for refresh kendo related value
		const self = this, templateType = self.obEntityDataModel().templateType();
		let kendoBoxes = ['pageWidth', 'pageHeight', 'cellWidth', 'cellHeight', 'cellPadding', 'marginTop',
			'marginBottom', 'marginLeft', 'marginRight', 'columnSpacing', 'rowSpacing'];

		kendoBoxes.forEach(input =>
		{
			let inputWidget = $(self.el).find(`[name='${input}']`).data("kendoNumericTextBox");
			if (inputWidget)
			{
				inputWidget.value(templateType[input]());
			}
		});
	}


	NewMergeDocumentViewModel.prototype._renderAdvancedSettingControls = function()
	{
		var self = this;
		self.pageOrientationDropDownList = TF.MergeTemplateTypeHelper.initPageOrientationDropDownList(
			self.obSelectedTemplate,
			self.el,
			self.recalculatePageSetting,
			self);
		self.pageOrientationDropDownList.data('kendoDropDownList').enable(false);
	};

	NewMergeDocumentViewModel.prototype.renderAdvancedSettingTemplate = function()
	{
		this._renderAdvancedSettingControls();
	};

	NewMergeDocumentViewModel.prototype.dispose = function()
	{
		if (this.pageLevelViewModel)
		{
			this.pageLevelViewModel.dispose();
			this.pageLevelViewModel = null;
		}
	};

	NewMergeDocumentViewModel.prototype.apply = function(viewModel, e)
	{
		var self = this;
		if (self.obSaveAsTemplateEnabled())
		{
			var newTemplate = self._recalculatePageSetting();
			if (newTemplate.DataChanged)
			{
				tf.promiseBootbox.alert("The page setting has been updated, please review it.");
				return;
			}
		}

		return self.validate().then(function(valid)
		{
			return valid ? self.save() : pageLevelPrompt(self.el, self.pageLevelViewModel);
		});
	};

	NewMergeDocumentViewModel.prototype.save = function()
	{
		var self = this;
		return self.prepareData().then(result =>
		{
			const roleMergeDocuments = this.typeRoles.value().filter(item => item >= 0).map(item =>
			{
				return {
					RoleID: item
				};
			})
			const params = {
				data: [self.obEntityDataModel().toData()]
			}
			if (roleMergeDocuments.length > 0)
			{
				params.paramData = { "@relationships": "RoleMergeDocument" };
				params.data[0].RoleMergeDocuments = roleMergeDocuments;
			}

			return result && tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), self.endpoint),
				params).then(function(response)
				{
					var id = response.Items[0].Id;
					self.obEntityDataModel().id(id);
					PubSub.publish(topicCombine(pb.DATA_CHANGE, "mergedocument"));
					return true;
				}).catch(function(error)
				{
					tf.promiseBootbox.alert(error.Message, "Warning");
					return false;
				});
		});
	};

	NewMergeDocumentViewModel.prototype.prepareData = function()
	{
		var self = this;
		if (self.isCopy)
		{
			return Promise.resolve(true);
		}

		var preHandlerOfOpenAMergeDocumentEditPanel, customTemplate;
		if (self.obSaveAsTemplateEnabled())
		{
			customTemplate = self.obSelectedTemplate().toData();
			preHandlerOfOpenAMergeDocumentEditPanel = tf.promiseAjax.post(
				pathCombine(tf.api.apiPrefixWithoutDatabase(), 'mergeTemplateTypes'),
				{
					data: [customTemplate]
				});
		}
		else
		{
			preHandlerOfOpenAMergeDocumentEditPanel = Promise.resolve({
				Items: [{
					Id: self.obSelectedTemplate().id(),
					Width: self.obSelectedTemplate().pageWidth(),
					Height: self.obSelectedTemplate().pageHeight(),
				}]
			});
		}

		return preHandlerOfOpenAMergeDocumentEditPanel.then(function(ret)
		{
			if (!ret || !ret.Items || !ret.Items.length)
			{
				tf.promiseBootbox.alert("Build New Template Failed");
				return false;
			}

			var templateId = ret.Items[0].Id;
			self.obEntityDataModel().templateTypeId(templateId);
			TF.MergeTemplateTypeHelper.resetTemplateData(customTemplate || self.obEntityDataModel().templateType().toData(), self.obEntityDataModel());
			return true;
		});
	};

	NewMergeDocumentViewModel.prototype.validate = function(viewModel, e)
	{
		return this.validator ? this.validator.validate() : Promise.resolve(true);
	};

	NewMergeDocumentViewModel.prototype.openEditTemplate = function(type)
	{
		var self = this;
		tf.modalManager.showModal(new TF.Modal.ManageMergeTemplateTypeModalViewModel())
			.then(function()
			{
				var templateTypes = TF.MergeTemplateTypeHelper.getMergeTemplateTypesWithCustomItem(!self.isMergeEmailMessage);

				var ignoreCustomTemplate = (self.obSelectedTemplate().id() == 0);
				if (ignoreCustomTemplate)
				{
					templateTypes[0] = $.extend({}, templateTypes[0], self.obSelectedTemplate().toData());
				}
				else
				{
					templateTypes[0] = $.extend({}, templateTypes[0], self.obEntityDataModel().allTemplates[0].toData());
				}
				self.obEntityDataModel().refresh(templateTypes);
				var ddl = $("#mergeTemplateTypeDropDownList").data("kendoDropDownList");
				ddl.dataSource.data(templateTypes);

				if (ddl.value() === "")
				{
					var typeId = TF.MergeTemplateTypeHelper.initTypeId;
					self.obEntityDataModel().templateTypeId(typeId);
					ddl.value(typeId);
				}
			});
	};

	NewMergeDocumentViewModel.prototype.onToggleAdvancedSettings = function(ev)
	{
		var self = this, element = $(self.el).find(".merge-doc-template-advanced-settings-container");

		if (!element.is(":visible"))
		{
			self.previewLayout.hide();
		}
		element.slideToggle("slow", function()
		{
			if (element.is(":visible"))
			{
				self.obAdvancedSettingExpended(true);
				self.previewLayout.resetPadding();
				self.previewLayout.show();
				self.previewLayout.refresh();
			}
			else
			{
				self.obAdvancedSettingExpended(false);
			}
		});
	}

	NewMergeDocumentViewModel.prototype.isRolesAccessControlEnable = function()
	{
		return this.dataType === 'mergeemailmessage'
			? tf.authManager.hasMergeEmailMessageAccess("save")
			: tf.authManager.hasMergeDocumentAccess("save");
	}

	NewMergeDocumentViewModel.prototype.initRolesAccessControl = function()
	{
		var self = this;
		self.$typeRoles = $(self.el).find("#typeRoles");
		var typeRolesData = [
			{ text: "Administrator", value: -1 }
		];
		function disableAdministratorButton()
		{
			let $selectedValues = self.$typeRoles.parent().find(".k-input-values .k-chip");
			$selectedValues.filter((_, e) => $(e).find(".k-chip-label").text() === 'Administrator').addClass(TF.KendoClasses.STATE.DISABLED);
		}
		self.$typeRoles.kendoMultiSelect({
			enable: this.isRolesAccessControlEnable(),
			dataTextField: "text",
			dataValueField: "value",
			itemTemplate: '<input type="checkbox" style="margin-right: 5px"/> #= text #',
			downArrow: true,
			autoClose: false,
			dataSource: typeRolesData,
			value: [-1],
			select: function(e)
			{
				e.preventDefault();
				//to prevent list to auto scroll
				var offset = this.list.offset().top - this.ul.offset().top + 1;
				var dataItem = e.dataItem;
				if (dataItem.value === -1)
				{
					return;
				}
				const roles = self.typeRoles.value();
				roles.push(dataItem.value);
				self.typeRoles.value(roles);
				this.list.find(".k-list-scroller").scrollTop(offset);
				disableAdministratorButton();
				self.checkRoleAccessSelectedCheckboxes();
			},
			deselect: function(e)
			{
				e.preventDefault();
				//to prevent list to auto scroll
				var offset = this.list.offset().top - this.ul.offset().top + 1;
				var dataItem = e.dataItem;
				if (dataItem == null || dataItem.value === -1)
				{
					return;
				}
				this.list.find(".k-list-scroller").scrollTop(offset);
				var roles = self.typeRoles.value();
				roles = roles.filter(x => x !== dataItem.value);
				self.typeRoles.value(roles);

				disableAdministratorButton();
				self.checkRoleAccessSelectedCheckboxes();
			},
			close: function()
			{
				self.typeRoles.isOpen = false;
			},
			dataBound: function()
			{
				//RW-35992 Checkbox Status incorrect after filter and dataBound
				self.typeRoles && self.checkRoleAccessSelectedCheckboxes();
			}
		});
		self.typeRoles = self.$typeRoles.data("kendoMultiSelect");

		return self.setRolesDataSource(self.typeRoles, typeRolesData, self.gridType)
			.then(() =>
			{
				const selectedValues = [-1];
				self.typeRoles.value(selectedValues);
				if (typeRolesData.filter(x => x.value >= 0).length
					=== self.typeRoles.value().filter(x => x >= 0).length)
				{
					self.typeRoles.value(typeRolesData.map(x => x.value));
				}
				disableAdministratorButton();
				self.checkRoleAccessSelectedCheckboxes();
			});
	}

	NewMergeDocumentViewModel.prototype.checkRoleAccessSelectedCheckboxes = function()
	{
		var self = this;
		var elements = self.typeRoles.ul.find("li");

		elements.each(index =>
		{
			const element = $(elements[index]);
			element.css("background-color", "transparent");
			const input = element.find("input[type='checkbox']");
			input.prop("checked", element.hasClass(TF.KendoClasses.STATE.SELECTED));
			//always disable administrator
			if (index == 0 && element[0] && element[0].innerText.trim() == "Administrator")
			{
				input.prop("disabled", true);
			}

		});
	}

	NewMergeDocumentViewModel.prototype.setRolesDataSource = function(typeRolesControl, typeRolesData)
	{
		return tf.authManager.getAllRolesData()
			.then(rolesData =>
			{
				for (const role of rolesData)
				{
					typeRolesData.push({ text: role.Name, value: role.RoleID });
				}
				const rolesDataSource = new kendo.data.DataSource({
					data: typeRolesData
				});
				typeRolesControl.setDataSource(rolesDataSource);
			});
	}

	function pageLevelPrompt(el, plMsgBox)
	{
		var errors = $(el).find("small[data-bv-result='INVALID']");
		plMsgBox.clearError();
		for (var i = 0; i < errors.length; i++)
		{
			var errorMessage = $(errors[i]).parent().find("label").html() + plMsgBox.getMessage(errors[i].innerHTML);
			plMsgBox.popupErrorMessage(errorMessage)
				.then(function()
				{
					return false;
				});
		}
	}
})();

