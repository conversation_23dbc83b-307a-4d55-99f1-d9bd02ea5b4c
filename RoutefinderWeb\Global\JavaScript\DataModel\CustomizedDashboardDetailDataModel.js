(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.CustomizedDashboardDetailDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	}

	namespace.CustomizedDashboardDetailDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.CustomizedDashboardDetailDataModel.prototype.constructor = namespace.CustomizedDashboardDetailDataModel;

	namespace.CustomizedDashboardDetailDataModel.prototype.mapping = [
		{ "from": "Id", "default": 0 },
		{ "from": "DashboardId", "default": 0 },
		{ "from": "Layout", "default": "" }
	];
})();