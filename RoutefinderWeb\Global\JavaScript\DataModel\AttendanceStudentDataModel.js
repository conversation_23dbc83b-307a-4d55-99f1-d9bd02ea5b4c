﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.AttendanceStudentDataModel = function(studentEntity)
	{
		namespace.StudentDataModel.call(this, studentEntity);
	};

	namespace.AttendanceStudentDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.AttendanceStudentDataModel.prototype.constructor = namespace.AttendanceStudentDataModel;

	namespace.AttendanceStudentDataModel.createExtendMapping = function()
	{
		var mapping = [];

		mapping.push({ from: 'AttendanceInfo', default: new namespace.AttendanceItemDataModel(), subDataModelType: namespace.AttendanceItemDataModel });
		mapping.push({ from: 'ExceptionDescription', default: null });
		mapping.push({ from: 'IsRemoved', default: false });
		mapping.push({ from: 'Source', default: '1' });
		mapping.push({ from: 'AttendanceType', default: -1 });
		mapping.push({ from: 'XCoord', default: null });
		mapping.push({ from: 'YCoord', default: null });
		mapping.push({ from: 'Time', default: null });
		mapping.push({ from: 'Address', default: null });
		mapping.push({ from: 'CreatedOn', default: null });
		mapping.push({ from: 'Unplanned', default: false });
		mapping.push({ from: 'VendorId', default: null });
		mapping.push({ from: 'AssetId', default: null });
		mapping.push({ from: 'TagId', default: null });
		mapping.push({ from: 'StudentSchedules', default: [] });
		mapping.push({ from: 'NewException', default: false });
		return mapping;
	};

	namespace.AttendanceStudentDataModel.prototype.mapping = namespace.StudentDataModel.prototype.mapping.concat(namespace.AttendanceStudentDataModel.createExtendMapping());
})();
