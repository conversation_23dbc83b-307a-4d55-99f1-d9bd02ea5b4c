﻿(function()
{
	createNamespace('TF.Modal').EditDisabilityCodeModalViewModel = EditDisabilityCodeModalViewModel;
	EditDisabilityCodeModalViewModel.prototype = Object.create(TF.Modal.AddThreeFieldsModalViewModel.prototype);
	EditDisabilityCodeModalViewModel.prototype.constructor = EditDisabilityCodeModalViewModel;

	function EditDisabilityCodeModalViewModel(fieldName, disabilityCodeId)
	{
		TF.Modal.AddThreeFieldsModalViewModel.call(this, fieldName, disabilityCodeId);
		this.contentTemplate('modal/editdisabilitycodecontrol');
		this.editDisabilityCodeViewModel = new TF.Control.EditDisabilityCodeViewModel(fieldName, disabilityCodeId);
		this.data(this.editDisabilityCodeViewModel);
	}

	EditDisabilityCodeModalViewModel.prototype.positiveClick = function()
	{
		this.editDisabilityCodeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	EditDisabilityCodeModalViewModel.prototype.saveAndNewClick = function()
	{
		this.editDisabilityCodeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.editDisabilityCodeViewModel.obEntityDataModel(new TF.DataModel.DisabilityCodeDataModel());
				this.newDataList.push(result);
			}
		}.bind(this));
	};

	EditDisabilityCodeModalViewModel.prototype.dispose = function()
	{
		this.editDisabilityCodeViewModel.dispose();
	};

})();
