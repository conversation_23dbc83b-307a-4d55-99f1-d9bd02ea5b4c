﻿(function()
{
	createNamespace('TF.Control').EditDisabilityCodeViewModel = EditDisabilityCodeViewModel;
	EditDisabilityCodeViewModel.prototype = Object.create(TF.Control.AddThreeFieldsViewModel.prototype);
	EditDisabilityCodeViewModel.prototype.constructor = EditDisabilityCodeViewModel;

	function EditDisabilityCodeViewModel(fieldName, id)
	{
		TF.Control.AddThreeFieldsViewModel.call(this, fieldName, id);
		this.id = id;
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);

		this.obErrorMessageTitle = ko.observable("Error Occurred");
		this.obErrorMessageDescription = ko.observable("The following error occurred.");
	}

	EditDisabilityCodeViewModel.prototype.save = function()
	{
		return this.saveValidate()
		.then(function(result)
		{
			if (result)
			{
				var isNew = this.id ? false : true;
				return tf.promiseAjax[isNew ? "post" : "put"](pathCombine(tf.api.apiPrefix(), this.fieldName, isNew ? "" : this.id), { data: this.obEntityDataModel().toData() })
				.then(function()
				{
					PubSub.publish(topicCombine(pb.DATA_CHANGE, this.fieldName, pb.EDIT));
					return this.obEntityDataModel().toData();
				}.bind(this))
			}
			return Promise.reject();
		}.bind(this));

	};

	EditDisabilityCodeViewModel.prototype.saveValidate = function()
	{// without focusField, still could run correct.
		this.obValidationErrors.removeAll();
		this.obErrorMessageDivIsShow(false);
		var validator = this.$form.data("bootstrapValidator");
		return validator.validate()
		.then(function(valid)
		{
			if (!valid)
			{
				var messages = validator.getMessages(validator.getInvalidFields());
				var $fields = validator.getInvalidFields();
				var validationErrors = [];
				$fields.each(function(i, fielddata)
				{
					if (i == 0)
					{
						$(fielddata).focus();
					}
					var message = messages[i].replace('&lt;', '<').replace('&gt;', '>');
					if (message == " required")
					{
						message = " is required";
					}
					validationErrors.push({ name: ($(fielddata).attr('data-bv-error-name') ? $(fielddata).attr('data-bv-error-name') : $(fielddata).closest("div.form-group").find("label").text()), message: message, field: $(fielddata) });

				}.bind(this));
				if (validationErrors.length > 1)
				{
					this.obErrorMessageTitle("Errors Occurred");
					this.obErrorMessageDescription("The following errors occurred.");
				}
				else
				{
					this.obErrorMessageTitle("Error Occurred");
					this.obErrorMessageDescription("The following error occurred.");
				}
				this.obErrorMessageDivIsShow(true);
				this.obValidationErrors(validationErrors);
				return false;
			}
			else
			{
				return true;
			}
		}.bind(this));
	};
})();

