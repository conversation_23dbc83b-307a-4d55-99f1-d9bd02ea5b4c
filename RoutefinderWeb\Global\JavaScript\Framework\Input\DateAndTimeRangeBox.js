(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.DateAndTimeRangeBox = DateAndTimeRangeBox;
	var WeekDays = {
		"sunday": 1,
		"monday": 2,
		"tuesday": 3,
		"wednesday": 4,
		"thursday": 5,
		"friday": 6,
		"saturday": 7
	}
	const TODAY = "[Today]";

	function DateAndTimeRangeBox(initialValue, attributes, disable, noWrap, delayChange, element, allBindings)
	{
		this.dtBoxHelperStart = null;
		this.dtBoxHelperEnd = null;

		this.SelectedItem = ko.observable("Today")

		var DateRangeItems = ["Between", "Last Week", "This Week", "Today", "Yesterday"]

		DateRangeItems = DateRangeItems.sort(function(s, t)
		{
			let a = s.toLowerCase();
			let b = t.toLowerCase();
			if (a < b) return -1;
			if (a > b) return 1;
			return 0;
		})

		this.SelectItems = ko.observable(DateRangeItems)

		namespace.BaseBox.call(this, initialValue, attributes, disable, noWrap);
		this.delayChange = delayChange;
		this.allBindings = allBindings;

		this.visible = ko.observable(false)
		var timeNow = new Date()
		this.obStartDate = ko.observable(timeNow);
		this.obEndDate = ko.observable(timeNow);
		this.obStartTime = ko.observable(timeNow);
		this.obEndTime = ko.observable(timeNow);
		this.data = {
			monday: ko.observable(true),
			tuesday: ko.observable(true),
			wednesday: ko.observable(true),
			thursday: ko.observable(true),
			friday: ko.observable(true),
			saturday: ko.observable(false),
			sunday: ko.observable(false),
			startDate: this.obStartDate,
			endDate: this.obEndDate
		};

		this.dateNumber = ko.observable(0)

		this.initialize.call(this);

		this.today = toISOStringWithoutTimeZone(moment(timeNow));
		this.utcToday = moment.utc().add(tf.timezonetotalminutes, 'm').format("L LT");
		this.valueSubscribe();
		this.datetimeSubscribe();
	}

	DateAndTimeRangeBox.prototype = Object.create(namespace.BaseBox.prototype);

	DateAndTimeRangeBox.prototype.type = "DateAndTimeRange";

	DateAndTimeRangeBox.constructor = DateAndTimeRangeBox;

	DateAndTimeRangeBox.prototype.formatString = "L";

	DateAndTimeRangeBox.prototype.pickerIconClass = "k-i-calendar";

	DateAndTimeRangeBox.prototype.initialize = function()
	{
		var self = this;
		const name = self.attributes.NameSeed || "";
		var $element = $(`
			<div style="width : 100%" data-range class="date-time-range-wrapper gps-report-daterange">
				<div class="input-group" style="margin-bottom:15px">
					<div
						data-bind="typeahead:{source:SelectItems,format:function(obj){return obj;},drowDownShow:true,notSort:true,selectedValue:SelectedItem,noneable:${!!self.attributes.optionDefault}}">
						<input placeHolder="Select a date filter" type="text" class="form-control range-options" name="filter${name}" data-tf-input-type="Select" data-bind="value:SelectedItem,disable:disable,style:{cursor:disable()?\'\':\'pointer\',backgroundColor:disable()?\'\':\'#fff\'}" readonly />
					</div>
					<div class="input-group-btn">
						<button type="button" class="btn btn-default btn-sharp">
							<span class="caret"></span>
						</button>
					</div>
				</div>
				<div class="col-xs-24" style="padding:0px" data-bind="style: { display: visible() ? 'inline-block' : 'none'}">
					<div class="row" style="margin-left:0px;width:100%">
						<div class="col-xs-11" style="padding:0px">
							<div class="form-group">
								<label class="requirestar">Start Date</label>
								<!-- ko customInput:{type:"Date", value:obStartDate,attributes:{class:"form-control start-date", name:'startDate${name}'}} --><!-- /ko -->
							</div>
						</div>
						<div class="col-xs-2"style="padding:0px">&nbsp;</div>
						<div class="col-xs-11" style="padding:0px">
							<div class="form-group">
								<label class="requirestar">Start Time</label>
								<!-- ko customInput:{type:"Time",value:obStartTime,attributes:{class:"form-control start-time", name:'startTime${name}'}} --><!-- /ko -->
							</div>
						</div>
					</div>
					<div class="row" style="margin-left:0px;width:100%">
						<div class="col-xs-11" style="padding:0px">
							<div class="form-group" style="padding:0px">
								<label class="requirestar">End Date</label>
								<!-- ko customInput:{type:"Date", value:obEndDate,attributes:{class:"form-control end-date", name:'endDate${name}'}} --><!-- /ko -->
							</div>
						</div>
						<div class="col-xs-2"style="padding:0px">&nbsp;</div>
						<div class="col-xs-11" style="padding:0px">
							<div class="form-group">
								<label class="requirestar">End Time</label>
								<!-- ko customInput:{type:"Time",value:obEndTime,attributes:{class:"form-control end-time", name:'endTime${name}'}} --><!-- /ko -->
							</div>
						</div>
					</div>
					<div class="row" style="margin-left:0px;width:100%">
						<div class="form-group" style="padding:0px;margin-top:0px">
							<label class="requirestar">Day Of Week</label>
							<div data-bind='component: {
								name:"week-days-component",
								params:{
									data:data,
									name: \`weekDays${name}\`
								}
							}'>
							</div>
						</div>
					</div>
				</div>
			</div>`
		);
		ko.applyBindings(this, $element[0]);
		this.$element = $element

		this.$elementSelect = $element.find('input.range-options')
		this.$elementNumber = $element.find('input.range-num')
		this.$elementStartDate = $element.find('input.start-date')

		this.$elementEndDate = $element.find('input.end-date')

		this.$elementSelect.addClass("dropdown-list");
		this.$elementSelect.on("click", function(e)
		{
			if (!self.disable())
				$(e.currentTarget.parentElement).closest('.input-group').find('.caret:eq(0)').parent().click();
		});

		this.$elementSelect.on("change", function(e)
		{
			var selected = e.currentTarget.value
			self.dtype = selected;
			self.setDateRange(selected, true)
		});
	};

	DateAndTimeRangeBox.prototype.resetValidate = function(text)
	{
		var validateDivs = this.$element.parents().closest('.form-group').find('.help-block'),
			StartErrorDiv = $(validateDivs[0]),
			EndErrorDiv = $(validateDivs[1])

		StartErrorDiv.css("display", "none")
		EndErrorDiv.css("display", "none")

		if (!text)
		{
			StartErrorDiv.text("Start Date is required")
			EndErrorDiv.text("End Date is required")
		}
		else
		{
			StartErrorDiv.text(text)
			EndErrorDiv.text(text)
		}
	}

	DateAndTimeRangeBox.prototype.getDays = function()
	{
		let weekdays = [];
		Enumerable.From(WeekDays).ToArray().forEach(element =>
		{
			if (this.data[element.Key]())
			{
				weekdays.push(parseInt(element.Value))
			}
		});

		return weekdays.join(',');
	}

	DateAndTimeRangeBox.prototype.validDay = function(day, dayOfWeek)
	{
		return Enumerable.From(WeekDays).ToArray().some(element =>
		{
			if (!dayOfWeek)
			{
				const dateValue = new Date(this.today).getDay() + 1;
				return element.Key === day && dateValue === element.Value && dateValue !== 1 && dateValue !== 7
			}
			return element.Key === day && dayOfWeek.includes(element.Value)
		});
	}

	DateAndTimeRangeBox.prototype.setDateRange = function(dtype, isUpdate)
	{
		var value = {}
		if (isUpdate)
		{
			value = this.value()
		}

		this.resetValidate('')

		switch (dtype)
		{
			case "Between":
				this.setVisable(true)
				value.StartDate = moment(this.getDate(this.obStartDate())).format('YYYY-MM-DD')
				value.EndDate = moment(this.getDate(this.obEndDate())).format('YYYY-MM-DD')
				value.StartTime = moment(this.obStartTime()).format('1899-12-30 HH:mm:ss.SSS')
				value.EndTime = moment(this.obEndTime()).format('1899-12-30 HH:mm:ss.SSS')
				value.DayOfWeek = this.getDays();
				break
			case "Last Week":
				this.setVisable(false)
				value.StartDate = "[LastWeekStart]"
				value.EndDate = "[LastWeekEnd]"
				value.StartTime = '1899-12-30 00:00:00.000'
				value.EndTime = '1899-12-30 23:59:59.000'
				value.DayOfWeek = "1,2,3,4,5,6,7"
				break;
			case "This Week":
				this.setVisable(false)
				value.StartDate = "[ThisWeekStart]"
				value.EndDate = "[ThisWeekEnd]"
				value.StartTime = '1899-12-30 00:00:00.000'
				value.EndTime = '1899-12-30 23:59:59.000'
				value.DayOfWeek = "1,2,3,4,5,6,7"
				break;
			case "Yesterday":
				this.setVisable(false)
				value.StartDate = "[Yesterday]"
				value.EndDate = "[Yesterday]"
				value.StartTime = '1899-12-30 00:00:00.000'
				value.EndTime = '1899-12-30 23:59:59.000'
				value.DayOfWeek = "1,2,3,4,5,6,7"
				break;
			case "Today":
			case "default":
				this.setVisable(false)
				value.StartDate = "[Today]"
				value.EndDate = "[Today]"
				value.StartTime = '1899-12-30 00:00:00.000'
				value.EndTime = '1899-12-30 23:59:59.000'
				value.DayOfWeek = "1,2,3,4,5,6,7"
				break
		}

		value.SelectedItem = dtype;
		if (isUpdate)
		{
			this.value(value)
		}
		return value
	}

	DateAndTimeRangeBox.prototype.numkeyup = function(viewModel, e)
	{
		var numValue = e.currentTarget.value.replace(/[^\d]/g, '')
		this.$elementNumber.val(numValue)
	};

	DateAndTimeRangeBox.prototype.numblur = function(viewModel, e)
	{
		var numValue = e.currentTarget.value.replace(/[^\d]/g, '')
		numValue = numValue === '' ? '1' : numValue
		numValue = numValue < 1 ? '1' : numValue
		numValue = Number(numValue).toString()
		this.$elementNumber.val(numValue)
		var selected = this.$elementSelect.val()

		var rawvalue = this.value()
		if (selected == "Next X Days")
		{
			rawvalue.EndDate = "[Next X Days]|" + numValue.toString();

		}
		else if (selected == "Last X Days")
		{
			rawvalue.StartDate = "[Last X Days]|" + numValue.toString();
		}
		else if (selected == "Older than X Days")
		{
			rawvalue.StartDate = "[Older than X Days]|" + numValue.toString();
		}
		rawvalue.DateNum = numValue
		this.value(rawvalue)
	};

	DateAndTimeRangeBox.prototype.setVisable = function(visible)
	{
		this.visible(visible);
	}

	DateAndTimeRangeBox.prototype.valueSubscribe = function()
	{
		this.value.subscribe(function(newValue)
		{
			if (!this.updating)
			{
				this.onValueChange.notify(newValue);
			}
		}, this);
	};

	DateAndTimeRangeBox.prototype.datetimeSubscribe = function()
	{
		this.obStartDate.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.obEndDate.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.obStartTime.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.obEndTime.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.monday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.tuesday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.wednesday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.thursday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.friday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.saturday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
		this.data.sunday.subscribe(v =>
		{
			this.setDateRange(this.dtype, true)
		})
	};

	DateAndTimeRangeBox.prototype.convertStartEndDateTextToDate = function(dType, dateText)
	{
		if (dType === 'Between' && moment(dateText).isValid())
		{
			return this.getDate(moment(dateText).format('YYYY-MM-DDT00:00:00'));
		}
		else
		{
			return this.getDate(dateText);
		}
	};

	DateAndTimeRangeBox.prototype.afterRender = function()
	{
		
		this.$elementStartDate.parent().parent().css('width', '100%')
		this.$elementEndDate.parent().parent().css('width', '100%')
		var rawValue = this.value()
		this.SelectedItem(this.value().SelectedItem)
		this.setDateRange(this.value().SelectedItem, false)
		this.value(rawValue)
		this.dtype = this.value().SelectedItem
		this.$elementNumber.val(rawValue.DateNum)
		let { StartDate, EndDate, StartTime, EndTime, DayOfWeek } = this.value()
		this.obStartDate(this.convertStartEndDateTextToDate(this.dtype, StartDate));
		this.obEndDate(this.convertStartEndDateTextToDate(this.dtype, EndDate));
		this.obStartTime(moment(StartTime).format('1899-12-30 HH:mm:ss.SSS'))
		this.obEndTime(moment(EndTime).format('1899-12-30 HH:mm:ss.SSS'))
		this.data.monday(this.validDay('monday', DayOfWeek))
		this.data.tuesday(this.validDay('tuesday', DayOfWeek))
		this.data.wednesday(this.validDay('wednesday', DayOfWeek))
		this.data.thursday(this.validDay('thursday', DayOfWeek))
		this.data.friday(this.validDay('friday', DayOfWeek))
		this.data.saturday(this.validDay('saturday', DayOfWeek))
		this.data.sunday(this.validDay('sunday', DayOfWeek))
	};

	DateAndTimeRangeBox.prototype.getDate = function(date)
	{
		if (date === TODAY || !date)
		{
			return new Date(this.today);
		}

		return new Date(date);
	}

	DateAndTimeRangeBox.prototype.dispose = function()
	{
		ko.removeNode(this.$element[0]);

		namespace.BaseBox.prototype.dispose.call(this);
	};
})();
