(function()
{
	createNamespace("TF.Grid.Form").SaveFormNameViewModel = SaveFormNameViewModel;

	function SaveFormNameViewModel(dataType, defaultName)
	{
		var self = this;
		self.name = ko.observable(defaultName);
		self.dataType = dataType;
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	};

	/**
	 * Initialization
	 * @return {void}
	 */
	SaveFormNameViewModel.prototype.init = function(model, element)
	{
		var self = this;
		self.element = element;
		self.initValidation();
	};

	/**
	 * Initialize the validation.
	 * @return {void}
	 */
	SaveFormNameViewModel.prototype.initValidation = function()
	{
		var self = this,
			isValidating = false,
			validatorFields = {};

		validatorFields.name = {
			trigger: "change",//"change blur" To find a way for walidators,set the trriger null  -sailias,
			validators:
			{
				stringLength: {
					max: 50,
					message: 'The form name must be less than 50 characters'
				},
				notEmpty: {
					message: " required"
				},
				noUnsafeHtmlTagsAndHtmlEscapes: {
					message: `Please remove special character(s) from Form Name above`
				},
				callback: {
					message: " must be unique",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							return true;
						}

						value = value.trim();

						return self.checkIfNameIsUnique(value);
					}
				}
			}
		};

		$(self.element).bootstrapValidator(
			{
				excluded: [':hidden', ':not(:visible)'],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element).then(function()
					{
						isValidating = false;
					});
				}
			});

		self.pageLevelViewModel.load($(self.element).data("bootstrapValidator"));
	};

	SaveFormNameViewModel.prototype.checkIfNameIsUnique = function(name)
	{
		const self = this,
			endpoint = "udgrids";

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), endpoint),
			{
				paramData: {
					dataTypeId: tf.dataTypeHelper.getId(self.dataType),
					"@fields": "Name"
				},
			},
			{
				overlay: false
			})
			.then(function(response)
			{
				if (response && Array.isArray(response.Items))
				{
					var matchedItems = response.Items.filter(udg => tf.exagoBIHelper.removeUnsupportChars(udg.Name, false) == name);
					if (matchedItems.length < 1)
					{
						return true;
					}

					return false;
				}

				return false;
			});

	};
	/**
	 * Validate the save process.
	 * @return {Promise} 
	 */
	SaveFormNameViewModel.prototype.validate = function()
	{
		var self = this;
		return self.pageLevelViewModel.saveValidateExtend().then(function(result)
		{
			if (result)
			{
				return Promise.resolve(self.name());
			}
		});
	};

	/**
	 * Dispose
	 * @return {void}
	 */
	SaveFormNameViewModel.prototype.dispose = function()
	{
		var self = this;
		self.pageLevelViewModel.dispose();
	};
})();