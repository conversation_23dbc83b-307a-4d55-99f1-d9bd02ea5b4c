(function()
{
	String.prototype.endsWith = function(suffix)
	{
		return this.indexOf(suffix, this.length - suffix.length) !== -1;
	};

	String.prototype.tfTrimStart = function(s)
	{
		return this.replace(new RegExp("^" + s), '');
	};

	String.prototype.tfTrimEnd = function(s)
	{
		return this.replace(new RegExp(s + "*$"), '');
	};

	ko.subscribable.fn.subscribeChanged = function(callback)
	{
		var oldValue;
		this.subscribe(function(_oldValue)
		{
			if (_oldValue.constructor == Array)
				oldValue = $.extend(true, [], _oldValue);
			else oldValue = _oldValue;
		}, null, 'beforeChange');
		if (this().constructor == Array)
		{
			this.subscribe(function(newValue)
			{
				callback.call(this, newValue, oldValue);
			}, null, 'arrayChange');
		}
		else
		{
			this.subscribe(function(newValue)
			{
				callback.call(this, newValue, oldValue);
			});
		}

	};

	var TF = createNamespace("TF");

	TF.consoleOutput = function()
	{
		var type, obj;
		if (arguments.length > 1)
		{
			type = arguments[0];
			obj = arguments[1];
		}
		else
		{
			obj = arguments[0];
		}

		if (typeof obj == "string")
		{
			if (type)
			{
				obj = type + ": " + obj;
			}

			console.log(obj);
			return;
		}

		type = type || "log";
		console.log({ type: type, message: obj });
	};

	TF.extend = function()
	{
		var inlineOverride = function(o)
		{
			for (var m in o)
			{
				this[m] = o[m];
			}
		};

		return function(subFn, superFn, overrides)
		{
			if (typeof superFn == 'object')
			{
				overrides = superFn;
				superFn = subFn;
				subFn = function()
				{
					superFn.apply(this, arguments);
				};
			}
			var F = function() { },
				subFnPrototype,
				superFnPrototype = superFn.prototype;

			F.prototype = superFnPrototype;
			subFnPrototype = subFn.prototype = new F();
			subFnPrototype.constructor = subFn;
			subFn.superclass = superFnPrototype;
			if (superFnPrototype.constructor == Object.prototype.constructor)
			{
				superFnPrototype.constructor = superFn;
			}

			subFn.override = function(obj)
			{
				TF.override(subFn, obj);
			};

			subFnPrototype.override = inlineOverride;
			TF.override(subFn, overrides);
			return subFn;
		};
	}();

	TF.override = function(origclass, overrides)
	{
		if (overrides)
		{
			var p = origclass.prototype;
			for (var method in overrides)
			{
				p[method] = overrides[method];
			}
		}
	};

	TF.smartOverride = function(prototype, name, override)
	{
		var baseMethod = prototype[name];
		prototype[name] = function()
		{
			arguments = Array.from(arguments);
			arguments.splice(0, 0, baseMethod);
			return override.apply(this, arguments);
		};
	};

	TF.convertToObservable = function(item, ignoredProps)
	{
		for (var prop in item)
		{
			var ignoreThisProp = false;
			if (ignoredProps && ignoredProps.constructor == Array)
			{
				ignoredProps.some(function(p)
				{
					return ignoreThisProp = prop == p;
				});
			}
			if (!ignoreThisProp)
			{
				if (typeof (item[prop]) !== 'object')
				{
					item[prop] = ko.observable(item[prop]);
				}
			}
		}
	};

	TF.toDateString = function(date)
	{
		return date ? moment(date).format("YYYY-MM-DDT00:00:00.000") : null;
	};

	TF.arrayToDictionary = function(arr, keyNames = ['id'])
	{
		const dictionary = {};
		arr.forEach(item =>
		{
			let compositeKey = keyNames.map(key => item[key]).join('_');
			dictionary[compositeKey] = item;
		});
		return dictionary;
	};

	TF.chunkArray = function(array, size)
	{
		return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
			array.slice(index * size, index * size + size)
		);
	};

	TF.getErrorMessage = function(res)
	{
		var defaultError = "An error occurred.";

		if (!res)
		{
			return defaultError;
		}

		if (typeof res === "string")
		{
			return res;
		}

		if (typeof res.TransfinderMessage === "string")
		{
			return res.TransfinderMessage;
		}

		if (typeof res.Message === "string")
		{
			return res.Message;
		}

		if (!res.Message)
		{
			return defaultError;
		}

		res.Message.Message = res.Message.Message || defaultError;
		if (typeof res.Message.Message === "string")
		{
			return res.Message.Message;
		}

		return defaultError;
	};

	TF.showErrorMessageBox = function(res)
	{
		return tf.promiseBootbox.alert(TF.getErrorMessage(res), "Error");
	};

	TF.showWarningMessageBox = function(res)
	{
		return tf.promiseBootbox.alert(TF.getErrorMessage(res), "Warning");
	};

	TF.updateProcessStatus = function(result)
	{
		tf.loadingIndicator.resetProgressbar();
		tf.loadingIndicator.changeProgressbar(result.Percentage, result.Message);
	};

	TF.DayOfWeek = {
		Monday: 1,
		Tuesday: 2,
		Wednesday: 3,
		Thursday: 4,
		Friday: 5,
		Saturday: 6,
		Sunday: 0,
	};

	TF.DayOfWeek.allValues = [1, 2, 3, 4, 5, 6, 0];

	TF.DayOfWeek.all = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

	TF.DayOfWeek.allSortByValue = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

	TF.DayOfWeek.toString = function(value, shortTerm)
	{
		var str = TF.DayOfWeek.allSortByValue[value];
		return shortTerm ? str.substring(0, shortTerm) : str;
	};

	TF.momentOfClientTimezone = function()
	{
		return moment().utc().add(tf.timezonetotalminutes, 'm');
	};

	TF.isWeekendDay = function(dateParam)
	{
		let date = new Date(dateParam);
		let dayOfWeek = date.getDay();
		return dayOfWeek == 0 || dayOfWeek == 6;
	};

	TF.needCheckWeekendDays = function(dateParam)
	{
		let needCheckWeekendDaysArr = ['Between', 'Last X Days', 'Next X Days', 'On', 'Today', 'Tomorrow', 'Yesterday'];
		return needCheckWeekendDaysArr.includes(dateParam.SelectedItem);
	};

	TF.createEnum = function(displayNames, startValue)
	{
		var obj = {};
		obj.all = [];
		obj.allValues = [];

		let value = startValue || 0;
		let getValue = index =>
		{
			if (!Array.isArray(startValue))
			{
				return value + index;
			}

			let current = startValue[index];
			if (current != null)
			{
				value = current;
			}
			else
			{
				value++;
			}

			return value;
		};

		displayNames.forEach((item, index) =>
		{
			var name = item.replace(/ /g, ""), itemValue = getValue(index);
			obj[name] = itemValue;
			var displayName = item[0] == "$" ? item.substring(1) : item;
			obj.all.push({ displayName: displayName, name: name, id: itemValue });
			obj.allValues.push(itemValue);
		});

		obj.findById = id => obj.all.find(item => item.id === id);

		obj.find = key =>
		{
			key = (key || "").toLowerCase();
			key = key.replace(/ /g, "");
			return obj.all.find(item => item.name.toLowerCase() === key);
		};

		return obj;
	};

	TF.createEnumWithValues = function(displayNames, startValue, values)
	{
		let obj = {};
		obj.all = [];
		obj.allValues = [];
		startValue = startValue || 0;
		displayNames.forEach((item, index) =>
		{
			let name = item.replace(/ /g, ""),
				value = values ? values[index] : index + startValue,
				displayName = item[0] == "$" ? item.substring(1) : item;
			obj[name] = value;
			obj.all.push({ displayName: displayName, name: name, id: value });
			obj.allValues.push(value);
		});

		obj.findById = id => obj.all.find(item => item.id === id);
		obj.find = key =>
		{
			key = (key || "").toLowerCase();
			key = key.replace(/ /g, "");
			return obj.all.find(item => item.name.toLowerCase() === key);
		};

		return obj;
	};

	var fileSaver = document.createElement("a");
	TF.saveStringAs = function(content, contentType, fileName)
	{
		contentType = contentType || "text/plain";
		fileName = fileName || "download";
		var blob = new Blob([content], { type: contentType }), dataURI = URL.createObjectURL(blob), e = document.createEvent("MouseEvents");
		fileSaver.download = fileName;
		fileSaver.href = dataURI;
		e.initMouseEvent("click", true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
		fileSaver.dispatchEvent(e);
	};

	TF.testEmail = function(email)
	{
		let reg = /[,;]/;
		let emailList = [];
		let invalidList = [];
		let validList = [];
		if (email)
		{
			email.split(reg).map(function(item)
			{
				if (item.trim() != '')
				{
					emailList.push(item.trim());
				}
			});
			let emailRegExp = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
			emailList.forEach(function(element)
			{
				if ($.trim(element) && !emailRegExp.test(element))
				{
					invalidList.push(element);
				}
				if ($.trim(element) && emailRegExp.test(element))
				{
					validList.push(element);
				}
			});
		}
		return {
			invalidList: invalidList,
			validList: validList,
			emailList: emailList
		};
	}

	hackConsoleError();

	function hackConsoleError()
	{
		TF.smartOverride(console, 'error', function(base, message)
		{
			arguments = Array.from(arguments);
			arguments.splice(0, 1);
			if (typeof message == "string" && message.startsWith("[esri."))
			{
				TF.consoleOutput("error", arguments);
				return;
			}

			return base.apply(this, arguments);
		});
	}

	TF.testWebGL2 = function()
	{
		let isSupportWebGL2 = false;
		if (typeof WebGL2RenderingContext !== 'undefined')
		{
			const canvas = document.createElement('canvas');
			const gl = canvas.getContext('webgl2');
			isSupportWebGL2 = !!gl;
		}

		if (!isSupportWebGL2)
		{
			tf.promiseBootbox.alert('A problem was detected with your graphics driver. WebGL2 is disabled. Some of map features might not work. Please contact Transfinder Support at <a target="_blank" href="mailto: <EMAIL>"><EMAIL></a> or 888-427-2403.');
		}
	};

	function PromiseCache()
	{
		this.promises = {};
		this.data = {};
		this.rejects = {};
	}

	PromiseCache.prototype.get = function(key, getterPromise)
	{
		if (Object.keys(this.data).indexOf(key) > -1)
		{
			return Promise.resolve(this.data[key]);
		}

		let promise = this.promises[key];
		if (promise)
		{
			return promise;
		}

		if (!getterPromise)
		{
			return Promise.resolve();
		}

		promise = new Promise((resolve, reject) =>
		{
			this.rejects[key] = reject;
			getterPromise().then(r =>
			{
				this.data[key] = r;
				resolve(r);
			});
		}).catch(() => null).finally(() =>
		{
			delete this.rejects[key];
			delete this.promises[key];
		});

		this.promises[key] = promise;
		return promise;
	};

	PromiseCache.prototype.clear = function(key)
	{
		this.rejects[key] && this.rejects[key]();
		delete this.rejects[key];
		delete this.promises[key];
		delete this.data[key];
	};

	TF.PromiseCache = PromiseCache;

	function ColorHelper() { }

	ColorHelper.prototype.isDarkColor = function(color)
	{
		// Check the format of the color, HEX or RGB?
		if (color.match(/^rgb/))
		{
			// If HEX --> store the red, green, blue values in separate variables
			color = color.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);

			r = color[1];
			g = color[2];
			b = color[3];
		}
		else
		{
			// If RGB --> Convert it to HEX: http://gist.github.com/983661
			color = +("0x" + color.slice(1).replace(
				color.length < 5 && /./g, '$&$&'
			)
			);

			r = color >> 16;
			g = color >> 8 & 255;
			b = color & 255;
		}

		// HSP (Highly Sensitive Poo) equation from http://alienryderflex.com/hsp.html
		hsp = Math.sqrt(
			0.299 * (r * r) +
			0.587 * (g * g) +
			0.114 * (b * b)
		);

		// Using the HSP value, determine whether the color is light or dark (hsp <= 127.5 as dark color)
		return hsp <= 127.5;
	};

	TF.defaultFontColor = '#2E2E2E';
	TF.ColorHelper = ColorHelper;

	TF.retry = async (promiseFunc, { times = 3, intervalTime = 2000 } = {}) =>
	{
		try
		{
			return await promiseFunc();
		}
		catch (ex)
		{
			times--;
			if (times < 1)
			{
				return Promise.reject(ex);
			}
			return TF.timeoutPromise(() => TF.retry(promiseFunc, { times: times, intervalTime: intervalTime }), intervalTime);
		}
	};

	TF.timeoutPromise = (promiseFunc, ms) =>
	{
		return new Promise((resolve, reject) =>
		{
			setTimeout(async () =>
			{
				try
				{
					let result = await promiseFunc();
					resolve(result);
				}
				catch (ex)
				{
					reject(ex);
				}
			}, ms);
		});
	};

	TF.arcgisLayers = {
		sde:
		{
			Map_Turn: 0,
			CurbApproach: 1,
			TravelRegion: 2,
			MAP_GEOREGIONPOLYGON: 3,
			MAP_LMPOINT: 4,
			MAP_LMPOLYGON: 5,
			MAP_LMPOLYLINE: 6,
			MAP_MC: 7,
			MAP_NEZ: 8,
			MAP_PC: 9,
			MAP_REDIST: 10,
			MAP_RSPoolBY: 11,
			MAP_RSPoolPT: 12,
			MAP_TRIPPATH: 13,
			MAP_TRIPSTOPBOUNDARY: 14,
			MAP_WTPOLYGON: 15,
			MAP_WTPOLYLINE: 16,
			POPULATIONREGION: 17,
			MAP_R: 18,
			MAP_STREET: 19,
			MAP_PARCELPOLYGON: 20,
			MAP_ADDRESSPOINT: 21,
			SIGNPOST_FEATURE: 22,
			MAP_SCHOOLLOCATIONPOLYGON: 23,
			SIGNPOST_TABLE: 24
		},
		file:
		{
			Map_Turn: 0,
			MAP_STREET: 1,
			routing_ND_Junctions: 2,
			StreetPolygon: 3,
			STREETINTERSECTR: 4
		}
	};

	TF.RoutingDirectionsStyleName = {
		Navigation: 'navigation',
		Campus: 'campus',
	};

	TF.guid = function()
	{
		var S4 = function()
		{
			return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
		};
		return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
	};

	/* Code Copy From Extjs */
	Function.prototype.createInterceptor = function(fcn, scope)
	{
		var method = this;

		return !(typeof fcn === "function") ?
			this :
			function()
			{
				var me = this,
					args = arguments;
				fcn.target = me;
				fcn.method = method;
				return (fcn.apply(scope || me || window, args) !== false) &&
					!(args[0] && args[0].sender && args[0].sender.operator === "custom") ?
					method.apply(me || window, args) :
					null;
			};
	};

	Function.prototype.createSequence = function(fcn, scope)
	{
		var method = this;
		return (typeof fcn != "function") ?
			this :
			function()
			{
				var retval = method.apply(this || window, arguments);
				fcn.apply(scope || this || window, arguments);
				return retval;
			};
	};

	Function.prototype.createCallback = function()
	{
		// make args available, in function below
		var args = arguments,
			method = this;
		return function()
		{
			return method.apply(window, args);
		};
	};

	Function.prototype.interceptBefore = function(object, methodName, fn, scope)
	{
		var method = object[methodName] || function() { };
		return (object[methodName] = function()
		{
			var ret = fn.apply(scope || this, arguments);
			method.apply(this, arguments);
			return ret;
		});
	};

	Function.prototype.interceptAfter = function(object, methodName, fn, scope)
	{
		var method = object[methodName] || function() { };
		return (object[methodName] = function()
		{
			method.apply(this, arguments);
			return fn.apply(scope || this, arguments);
		});
	};
})();
