@import "fontsize";
@import (reference) "detailView.less";
@ThemeColor: #D74B3C;

.tf-contextmenu-wrap.tf-contextmenu-white.custom-dashboard-contextmenu {
	position: absolute;
}

.tabstrip-customdashboard {
	padding: 0px;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	font-family: "SourceSansPro-Regular";

	&.read-mode {
		overflow-y: auto;

		.dashboard-doc {
			.dashbaord-content {
				.right-container.grid-stack-container.container-fluid {
					height: auto;
				}
			}
		}
	}

	.dashboard-doc,
	.customize-panel {
		padding: 15px;
	}

	.dashboard-doc {
		display: flex;
		flex-direction: column;

		.dashboard-header {
			border-bottom: 1px solid #C4C4C4;
			padding-bottom: 10px;
			display: flex;
			flex-direction: row;
			align-items: center;

			.header-title {
				flex: 1;
				font-family: "SourceSansPro-SemiBold";
				font-size: 25px;
			}

			.header-control {
				display: flex;
				flex-direction: row;
				align-items: center;

				.selector-menu {
					display: flex;
					flex-direction: row;
					align-items: center;
					border-radius: 9px;
					height: 24px;
					line-height: 24px;
					width: auto;
					padding: 0 8px;
					margin: 0;
					cursor: pointer;

					&:hover {
						background-color: #f2f2f2;
					}

					.select-type {
						font-size: 15px;
					}

					.icon.bottom-caret {
						margin: 10px 0 10px 8px;
						width: 8px;
						height: 4px;

						&:before {
							border-top-color: #777777;
						}

						&:after {
							border-top-color: #fff;
						}
					}
				}

				.control-buttons {
					.button-icon {
						background-position: center center;
						background-size: 24px 24px;
						background-repeat: no-repeat;
						width: 24px;
						height: 24px;
						margin-left: 24px;
						cursor: pointer;
					}

					.fullscreen {
						background-image: url('../img/navigation-menu/icon-fullscreen-black.svg');
					}
				}
			}
		}
	}

	.dashboard-widgets {
		height: calc(~"100vh - 28px");

		.widgets-wrapper {
			float: left;
			width: calc(~"100% - 40px");
		}

		.non-date-element-container {
			float: right;
			width: 40px;
			padding-top: 12px;
			height: calc(~"100vh - 28px");
			background-color: #F8F8F8;
			border-left: 1px solid #F2F2F2;

			>div {
				position: relative;
				width: 34px;
				height: 34px;
				padding: 6px 6px 0 6px;
				margin: 2px 2px 0 2px;
				background-position: center;
				cursor: pointer;
				background-size: 24px;
				background-repeat: no-repeat;

				&.horizontal-line {
					background-image: url("../../global/Img/detail-screen/horizontal line.svg");
				}

				&.section-header {
					background-image: url('../../global/Img/detail-screen/section header.svg');
				}

				&.spacer {
					background-image: url('../../global/Img/detail-screen/spacer.svg');
				}

				&.vertical-line {
					background-image: url('../../global/Img/detail-screen/vertical line.svg');
				}

				&.image {
					background-image: url('../../global/Img/detail-screen/image.svg');
				}

				&.tab {
					background-image: url('../../global/Img/detail-screen/tab.png');
				}
			}
		}
	}

	.customize-panel {
		.customize-dashboard-panel {
			height: calc(100vh - 58px);

			.template-info {
				input.name {
					max-width: calc(~"100% - 225px");
					min-width: 360px;
					width: fit-content;
				}

				.buttons {
					float: right;
					padding: 0px;

					&.disabled {
						.iconbutton {
							opacity: 0.4;
							pointer-events: none;
						}
					}

					.iconbutton {
						margin-left: 20px;
						float: left;
						width: 24px;
						height: 24px;
						background-size: 24px 24px;
						cursor: pointer;

						&.layout {
							background-image: url("../../global/img/detail-screen/layout.svg");
						}

						&.save {
							background-image: url("../../global/img/detail-screen/save.svg");
						}

						&.settings {
							background-image: url("../../global/img/detail-screen/settings.svg");
						}

						&.close-detail {
							background-image: url("../../global/img/detail-screen/Close.svg");
						}

						&.font-size-slider {
							background-image: url("../../global/img/detail-screen/font_size_24x24-pos.svg");
						}
					}

					.type-selector {
						float: left;
						.fontSize(1, 2);
						position: relative;
						z-index: 3;

						.dropdown-menu {
							.dropdown-menu-template;
							left: auto;
							right: 0;
							top: 0;

							ul li {
								.dropdown-menu-li-template;
								cursor: pointer;

								&:hover {
									background-color: @systemLightColor;
								}
							}
						}
					}
				}
			}
		}
	}
}

.modal-dialog {
	.modal-content {
		.edit-dashboard-grid-modal {
			.show-quick-filter {
				input {
					margin-left: 10px;
				}
			}

			.form-group.text-input {
				height: 40px;
			}

			.form-group {
				&.filter {
					.k-dropdown {
						background-color: transparent;

						.k-dropdown-wrap {
							background-color: transparent;
							overflow: hidden;

							.k-input {
								border-right-style: solid;
								border-right-width: 1px;

								&:hover {
									cursor: pointer;
								}
							}

							.k-select {
								background-color: #eeeeee;
								width: 22px;
								border-left-style: solid;
								border-left-width: 1px;

								&:hover {
									cursor: pointer;
								}
							}
						}
					}
				}
			}
		}
	}
}

.k-list-container {
	.filter-item {
		.icon {
			background-size: 16px;
			float: left;
			width: 20px;
			height: 23px;
			background-position: center;
		}

		&.db-specific {
			.icon {
				background-image: url(../../global/img/dashboard/db-specific.svg);
			}
		}
	}
}

.manage-customdashboard {
	.grid-footer {
		background-color: transparent;
		float: right;
	}

	.grid-container {
		input.default-dashboard-radio {
			-webkit-appearance: none;
			-moz-appearance: none;
			appearance: none;
			border-radius: 50%;
			width: 16px !important;
			height: 16px !important;
			margin: 0 !important;
			border: 1px solid #333333;
			transition: 0.2s all linear;
			outline: none;
			position: relative !important;
			background-color: #DDDDDD;
			top: 3px;
		}

		input.default-dashboard-radio:checked::after {
			content: '';
			width: 12px;
			height: 12px;
			background: #000000;
			position: absolute;
			top: 1px;
			left: 1px;
			border-radius: 100%;
			-webkit-transition: all 0.2s ease;
			transition: all 0.2s ease;
		}
	}
}

.edit-dashboard-grid-modal {
	.sortorder-container {
		width: calc(~"100% - 50px");
		.fontSize(1, 1);
		float: left;

		.sortorder-button {
			position: absolute;
			width: 16px;
			height: 100%;
			background-size: 16px 16px;
			background-repeat: no-repeat;
			background-position: center center;
			cursor: pointer;
		}

		.sortorder-list {
			max-height: 685px;
			background-color: #f6f6f6;
			overflow-y: auto;

			.sortorder-item:first-child {
				border-top: none;
			}

			.sortorder-item-placeholder {
				width: 100%;
				border-bottom: 1px solid #979797;
				background-color: #f2f2f2;
			}

			.sortorder-item {
				position: relative;
				width: 100%;
				padding: 12px 28px 8px 18px;
				color: #333;
				border-top: 1px solid #979797;
				background-color: #fff;
				cursor: move;

				.easeTransform {
					transition: all 200ms ease;
				}

				&:hover {
					background-color: #f2f2f2;
				}

				&.onDrag {
					z-index: 10;
					background-color: #f2f2f2;
				}

				button {
					position: absolute;
					top: 13px;
					left: 3px;
					transform: rotate(-90deg);
					padding: 0;
					border: none;
					background-color: transparent;
					cursor: pointer;

					&:hover {
						background-color: transparent;
					}
				}

				.sortorder-content {
					>div {
						line-height: 21px;

						>div {
							display: inline-block;
							margin-left: 30px;
							margin-bottom: 5px;
							width: 150px;

							>div {
								display: inline;

								border-top: 1px solid transparent;
								border-bottom: 1px solid #007DEA;

								color: #007DEA;
								font-weight: 700;
								cursor: pointer;
							}
						}
					}

					&.abbreviation {
						display: block;
					}
				}

				.delete-btn {
					top: calc(~"50% - 8px");
					right: 10px;
					height: 16px;
					background-image: url("../../global/menu/Delete-Black.svg");
				}
			}
		}

		.add-new-sortorder {
			position: relative;
			padding: 15px 20px;
			color: #0C0;
			border-top: 1px solid #25CD1E;

			.addNew-btn {
				top: calc(~"50% - 8px");
				right: 10px;
				height: 16px;
				background-image: url("../../global/img/detail-screen/Add-Green.png");
				cursor: pointer;
			}
		}
	}

	.dropdown-menu {
		display: none;
		position: fixed;
		overflow-y: auto;

		.date-picker {
			border: none;
		}

		&[selecttype=SortAscending] {
			ul li .item-icon {
				display: block;
			}
		}

		ul {
			padding: 0;

			li {
				list-style: none;
				padding: 3px 7px;
				min-height: 30px;
				line-height: 24px;
				cursor: pointer;

				&:hover {
					background-color: #f2f2f2;
				}

				&>div {
					float: left;
				}

				.item-icon {
					width: 16px;
					height: 24px;
					margin-right: 7px;
					background-repeat: no-repeat;
					background-position: center center;
					background-size: 16px;
					display: none;

					&.On,
					&.Before,
					&.After,
					&.OnOrBefore,
					&.NotOn,
					&.OnOrAfter,
					&.Between {
						display: none;
					}

					&.asc {
						background-image: url(../../global/img/Filter/LessThan.png);
					}

					&.desc {
						background-image: url(../../global/img/Filter/GreaterThan.png);
					}

				}
			}
		}
	}
}

.dashboard-widget-panel {
	.widgets-wrapper {
		margin-top: 56px;

		.data-point-item {
			margin: 10px;
			padding: 0;

			&.map {
				.widget-icon {
					background-image: url(../img/dashboard/map.svg);
				}
			}

			&.grid {
				.widget-icon {
					background-image: url(../img/dashboard/grid.svg);
				}
			}

			&.visualization {
				.widget-icon {
					background-image: url(../img/dashboard/visualization.svg);
				}
			}

			&.data-card {
				.widget-icon {
					background-image: url(../img/dashboard/datacard.svg);
				}
			}

			.widget-icon {
				width: 76px;
				height: 76px;
				background-color: #262626;
				background-repeat: no-repeat;
				background-position: center;
				background-size: 72px 72px;
			}

			.widget-text {
				height: 20px;
			}
		}
	}
}

body {
	&.dashboard-fullscreen {
		.navigation-container {
			display: none;
		}

		.doc-selector {
			display: none;
		}
	}
}

.grid-stack-item {
	.grid-mask {
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		position: absolute;
	}
}