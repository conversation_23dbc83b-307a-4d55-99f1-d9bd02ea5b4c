(function()
{
	createNamespace("TF.Control").GeoFinderViewModel = GeoFinderViewModel;

	function GeoFinderViewModel(geoFinderTool, options)
	{
		var self = this;
		self.options = options;
		self.geoFinderTool = geoFinderTool;
		self.obAltsites = ko.observable(false);
		self.obGeoRegions = ko.observable(false);
		self.obStudents = ko.observable(false);
		self.obSchools = ko.observable(false);
		self.obTripStops = ko.observable(false);
		self.obTrips = ko.observable(false);
		self.obGPS = ko.observable(false);
		self.obAltsitesCount = ko.observable(0);
		self.obGeoRegionsCount = ko.observable(0);
		self.obStudentsCount = ko.observable(0);
		self.obSchoolsCount = ko.observable(0);
		self.obTripsCount = ko.observable(0);
		self.obTripStopsCount = ko.observable(0);

		this.obSelectedBoundarySets = ko.observableArray();
		this.schoolFormatter = this.schoolFormatter.bind(this);
		this.obStartDateValidFlag = ko.observable("");
		this.obEndDateValidFlag = ko.observable("");
		this.obGPSStartDate = ko.observable();
		this.obGPSEndDate = ko.observable();

		this.obGPSStartTime = ko.observable("YYYY-MM-DDT00:00:00");
		this.obGPSEndTime = ko.observable("YYYY-MM-DDT23:59:59");
		self.obMoChecked = ko.observable(false);
		self.obTuChecked = ko.observable(false);
		self.obWeChecked = ko.observable(false);
		self.obThChecked = ko.observable(false);
		self.obFrChecked = ko.observable(false);
		self.obSaChecked = ko.observable(false);
		self.obSuChecked = ko.observable(false);
		self.obMonToFriChecked = ko.observable(false);

		self.obMoDisabled = ko.observable(false);
		self.obTuDisabled = ko.observable(false);
		self.obWeDisabled = ko.observable(false);
		self.obThDisabled = ko.observable(false);
		self.obFrDisabled = ko.observable(false);
		self.obSaDisabled = ko.observable(false);
		self.obSuDisabled = ko.observable(false);
		self.obMonToFriDisabled = ko.computed(function()
		{
			return self.obMoDisabled() || self.obTuDisabled() || self.obWeDisabled() || self.obThDisabled() || self.obFrDisabled();
		});

		this.obGPSStartDate.subscribe(this.onDateChange, this);
		this.obGPSEndDate.subscribe(this.onDateChange, this);
		this.obGPSStartDate(moment().format());
		this.obGPSEndDate(moment().format());

		self.obIsLatest = ko.observable(false);
		self._lockWeekDayChange = false;
	}

	GeoFinderViewModel.prototype.init = async function(viewModal, e)
	{
		var self = this;
		self.element = e;
		setTimeout(function()
		{
			self.validationInitialize();
		}, 20);

		if (self.options.getResultsInPolygonPromise)
		{
			self.gridResults = await self.options.getResultsInPolygonPromise;
			self.obAltsitesCount(self.gridResults[0].Items.length);
			self.obGeoRegionsCount(self.gridResults[1].Items.length);
			self.obStudentsCount(self.gridResults[2].Items.length);
			self.obSchoolsCount(self.gridResults[3].Items.length);
			self.obTripsCount(self.gridResults[4].Items.length);
			self.obTripStopsCount(self.gridResults[5].Items.length);
		}

		self.obMoChecked.subscribe(self.setWeekdayGroup, self);
		self.obTuChecked.subscribe(self.setWeekdayGroup, self);
		self.obWeChecked.subscribe(self.setWeekdayGroup, self);
		self.obThChecked.subscribe(self.setWeekdayGroup, self);
		self.obFrChecked.subscribe(self.setWeekdayGroup, self);
		self.obMonToFriChecked.subscribe(self.setWeekdayInGroup, self);
	};

	GeoFinderViewModel.prototype.validationInitialize = function()
	{
		var self = this;
		$(this.element).bootstrapValidator({
			excluded: [":hidden", ":not(:visible)"],
			live: "enabled",
			message: "This value is not valid",
			fields: {
				endDate: {
					trigger: "blur change",
					validators: {
						callback: {
							message: "",
							callback: function(value)
							{
								if (value != "")
								{
									clearDateTimeAlerts();
									if (self.obGPSStartDate())
									{
										var startDate = new moment(self.obGPSStartDate());
										var endDate = new moment(value);
										if (endDate.isBefore(startDate))
										{
											return {
												message: "Start Date must be <= End Date",
												valid: false
											};
										}
										return true;
									}
								}
								return true;
							}
						}
					}
				},
				startDate: {
					trigger: "blur change",
					validators: {
						callback: {
							message: "",
							callback: function(value)
							{
								if (value != "")
								{
									clearDateTimeAlerts();
									if (self.obGPSEndDate())
									{
										var endDate = new moment(self.obGPSEndDate());
										var startDate = new moment(value);
										if (endDate.isBefore(startDate))
										{
											return {
												message: "Start Date must be <= End Date",
												valid: false
											};
										}
										return true;
									}
								}
								return true;
							}
						}
					}
				}
			}
		}).on("error.validator.bv", function(e, data)
		{
			data.element
				.data("bv.messages")
				.find(".help-block[data-bv-for=\"" + data.field + "\"]").hide()
				.filter("[data-bv-validator=\"" + data.validator + "\"]").show();
		}).on("success.field.bv", function(e, data)
		{
			var $parent = data.element.closest(".form-group");
			$parent.removeClass("has-success");
		});

		function clearDateTimeAlerts()
		{
			$(self.element).find("[name=startDate]").closest(".form-group").find("small[data-bv-validator=callback]").hide();
			$(self.element).find("[name=endDate]").closest(".form-group").find("small[data-bv-validator=callback]").hide();
		}
	};

	GeoFinderViewModel.prototype.setWeekdayInGroup = function()
	{
		var self = this;
		if (self._lockWeekDayChange === false)
		{
			self._lockWeekDayChange = true;
			if (self.obMonToFriChecked())
			{
				self.obMoChecked(true);
				self.obTuChecked(true);
				self.obWeChecked(true);
				self.obThChecked(true);
				self.obFrChecked(true);
			}
			else
			{
				self.obMoChecked(false);
				self.obTuChecked(false);
				self.obWeChecked(false);
				self.obThChecked(false);
				self.obFrChecked(false);
			}
			self._lockWeekDayChange = false;
		}
	};

	GeoFinderViewModel.prototype.setWeekdayGroup = function()
	{
		var self = this;
		if (self._lockWeekDayChange === false)
		{
			self._lockWeekDayChange = true;
			if (self.obMoChecked() && self.obTuChecked() && self.obWeChecked() && self.obThChecked() && self.obFrChecked())
			{
				self.obMonToFriChecked(true);
			}
			else
			{
				self.obMonToFriChecked(false);
			}
			self._lockWeekDayChange = false;
		}
	};

	GeoFinderViewModel.prototype.selectSchools = function()
	{
		var self = this;
		tf.modalManager.showModal(
			new TF.Modal.ListMoverSelectSchoolBoundarySetsModalViewModel(
				this.obSelectedBoundarySets(),
				{
					title: tf.applicationTerm.getApplicationTermPluralByName("School"),
					availableTitle: "Available",
					selectedTitle: "Selected",
					type: "school",
					// invalidIds: []
				}
			)
		)
			.then(function(selectedSchool)
			{
				if (selectedSchool)
				{
					this.obSelectedBoundarySets(selectedSchool);
					self.geoFinderTool.findInBoundarySets(this.obSelectedBoundarySets()).then(function(result)
					{
						self.obSchoolsCount(result.Items.length);
						self.gridResults[3] = result;
					});

				}
			}.bind(this));

	};

	GeoFinderViewModel.prototype.schoolFormatter = function(item)
	{
		return item.Name;
	};

	GeoFinderViewModel.prototype.onDateChange = function()
	{
		var startDate = moment(moment(this.obGPSStartDate()).format("L"));
		var endDate = moment(moment(this.obGPSEndDate()).format("L"));

		var weekdayChecks = [this.obSuChecked, this.obMoChecked, this.obTuChecked, this.obWeChecked, this.obThChecked, this.obFrChecked, this.obSaChecked];
		var weekdayDisabled = [this.obSuDisabled, this.obMoDisabled, this.obTuDisabled, this.obWeDisabled, this.obThDisabled, this.obFrDisabled, this.obSaDisabled];
		if (startDate._i !== "Invalid date" && endDate._i !== "Invalid date")
		{
			for (let i = 0; i < weekdayChecks.length; i++)
			{
				weekdayChecks[i](false);
			}

			var sundayDisabled = true;
			var saturdayDisabled = true;

			for (let i = startDate, j = 0; i <= endDate && j <= 8; i = i.add("day", 1))
			{
				if (i.weekday() === 0)
					sundayDisabled = false;

				if (i.weekday() === 6)
					saturdayDisabled = false;

				weekdayChecks[i.weekday()](true);
				j++;
			}

			for (let i = 0; i < weekdayChecks.length; i++)
			{
				weekdayDisabled[i](!weekdayChecks[i]());
			}

			if (!saturdayDisabled)
				weekdayDisabled[6](saturdayDisabled);

			if (!sundayDisabled)
				weekdayDisabled[0](sundayDisabled);
		}
		else 
		{
			for (let i = 0; i < weekdayChecks.length; i++)
			{
				weekdayDisabled[i](false);
			}
		}
	};

	GeoFinderViewModel.prototype.apply = function()
	{
		var self = this;
		var gridTypes = [];
		if (self.obAltsites())
		{
			gridTypes.push("altsite");
		}
		if (self.obGeoRegions())
		{
			gridTypes.push("georegion");
		}
		if (self.obStudents())
		{
			gridTypes.push("student");
		}
		if (self.obSchools())
		{
			gridTypes.push("school");
		}
		if (self.obTrips())
		{
			gridTypes.push("trip");
		}
		if (self.obTripStops())
		{
			gridTypes.push("tripstop");
		}
		if (self.obGPS())
		{
			gridTypes.push("gpsevent");
		}
		var availableDays = [];
		if (self.obMoChecked() || self.obMonToFriChecked()) { availableDays.push(1); }
		if (self.obTuChecked() || self.obMonToFriChecked()) { availableDays.push(2); }
		if (self.obWeChecked() || self.obMonToFriChecked()) { availableDays.push(3); }
		if (self.obThChecked() || self.obMonToFriChecked()) { availableDays.push(4); }
		if (self.obFrChecked() || self.obMonToFriChecked()) { availableDays.push(5); }
		if (self.obSaChecked()) availableDays.push(6);
		if (self.obSuChecked()) availableDays.push(0);

		return Promise.resolve({
			selectGridOptions: gridTypes,
			boundarySets: self.obSelectedBoundarySets(),
			gpsQueryCondition: {
				startDate: moment(self.obGPSStartDate()).format("YYYY-MM-DDTHH:mm:ss"),
				endDate: moment(self.obGPSEndDate()).format("YYYY-MM-DDTHH:mm:ss"),
				startTime: self.obGPSStartTime(),
				endTime: self.obGPSEndTime(),
				availableDays: availableDays,
				isLatest: self.obIsLatest()
			},
			gridResults: self.gridResults
		});
	};

	GeoFinderViewModel.prototype.dispose = function()
	{

	};

})();

