﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.TripstopDeletion = TripstopDeletion;

	function TripstopDeletion()
	{
		this.type = 'tripstop';
		namespace.BaseDeletion.apply(this, arguments);
	}

	TripstopDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);

	TripstopDeletion.prototype.constructor = TripstopDeletion;

	TripstopDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "student", "ids", "tripstop"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'student',
				items: response.Items[0]
			});
		});

		var p1 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "trip", "ids", "tripstop"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'trip',
				items: response.Items[0]
			});
		});

		//var p2 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "deltastuds", "ids", "tripstop"), {
		//	data: ids
		//}).then(function(response)
		//{
		//	associatedDatas.push({
		//		type: 'deltastuds',
		//		items: response.Items[0]
		//	});
		//});

		var p4 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
				"AttachedToType": tf.dataTypeHelper.getId("tripstop")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});

		return Promise.all([p0, p1, p4]).then(function()
		{
			return associatedDatas;
		});
	}

	TripstopDeletion.prototype.getDeletionData = function()
	{
		return {
			'Ids': this.deleteIds,
			'UserSettingsModel': new TF.DataModel.LocalStorageDataModel().toData()
		}
	}

	TripstopDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	TripstopDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "student", "tripstop");

		var p2 = this.getDataStatus(this.ids, "trip", "tripstop");

		var p3 = this.getDataStatus(this.ids, "deltastuds", "tripstop");

		return Promise.all([p0, p1, p2, p3]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};
})();