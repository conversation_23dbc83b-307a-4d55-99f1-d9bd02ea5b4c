﻿(function()
{
	createNamespace("TF.Modal").MessageModalViewModel = MessageModalViewModel;

	function MessageModalViewModel(messages)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Message Center');
		this.sizeCss = "modal-dialog-lg";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/messagecontrol');
		this.buttonTemplate('modal/negative');
		this.viewModel = new TF.Control.MessageViewModel(this, messages);
		this.data(this.viewModel);
	}

	MessageModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	MessageModalViewModel.prototype.constructor = MessageModalViewModel;

})();

