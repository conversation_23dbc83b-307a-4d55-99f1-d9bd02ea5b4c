(function()
{
	createNamespace("TF.Control").WizardModalViewModel = WizardModalViewModel;

	function WizardModalViewModel(wizardType, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.obBackdrop("static");
		this.sizeCss = "modal-dialog-md";
		this.wizard = new wizardType(options);
		this.data(this.wizard);
		this.wizard.title.subscribe(function()
		{
			this.title(this.wizard.title());
		}, this);
		this.title(this.wizard.title());
		this.contentTemplate("workspace/Wizard/wizard");
		this.buttonTemplate("workspace/Wizard/WizardModalButtons");
		this.obPositiveButtonLabel("Submit");
		this.obPrevButtonLabel = ko.observable("Back");
		this.obNextButtonLabel = ko.observable("Next");
		this.obPrevButtonVisible = ko.observable(true);
		this.obNextButtonVisible = this.wizard.canNext;
		this.obPositiveButtonVisible = ko.computed(function()
		{
			return !this.wizard.canNext();
		}, this);

		this.obNegativeButtonVisible = ko.observable(true);
		this.obNegativeButtonVisible = ko.observable(true);
		this.obPrevButtonDisable = ko.computed(function()
		{
			return !this.wizard.canPrevious();
		}, this);
		this.obNextButtonDisable = ko.computed(function()
		{
			return !this.wizard.canNext();
		}, this);
	};

	WizardModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	WizardModalViewModel.prototype.constructor = WizardModalViewModel;

	WizardModalViewModel.prototype.nextStep = function()
	{
		this.wizard.next();
	};

	WizardModalViewModel.prototype.prevStep = function()
	{
		this.wizard.previous();
	};

	WizardModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.wizard.apply().then(function(result)
		{
			if (result)
			{
				TF.Modal.BaseModalViewModel.prototype.positiveClick.apply(this, arguments);
			}
		}.bind(this));
	};

	WizardModalViewModel.prototype.negativeClick = function(viewModel, e, notNeedConfirm)
	{
		var self = this, promise = Promise.resolve(true);
		if (!notNeedConfirm)
		{
			promise = tf.promiseBootbox.yesNo("Are you sure you want to cancel?", self.data().name());
		}

		return promise.then(function(result)
		{
			if (result) TF.Modal.BaseModalViewModel.prototype.negativeClick.call(self, viewModel, e);
			return result;
		});
	};
})();