<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 22.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 24 24" style="enable-background:new 0 0 24 24;" xml:space="preserve">
<style type="text/css">
	.st0{fill:none;}
	.st1{fill:none;stroke:#9B9B9B;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
</style>
<line class="st0" x1="12" y1="-5" x2="12" y2="29"/>
<line class="st0" x1="-10" y1="12" x2="34" y2="12"/>
<title>Triangle 2 Copy</title>
<desc>Created with Sketch.</desc>
<polyline class="st1" points="15.5,1 6.5,12 15.5,23 "/>
</svg>
