(function()
{
	createNamespace('TF.Control').EditNoteViewModel = EditNoteViewModel;

	function EditNoteViewModel(type, typeId, note, disableControl)
	{
		this.type = type;
		this.typeId = typeId;
		this.note = ko.observable(note);
		this.oldNote = note;
	}

	EditNoteViewModel.prototype.save = function()
	{
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), this.type, "note", this.typeId), { data: "'" + this.note() + "'" })
		.then(function(e)
		{
			return { Id: this.typeId, Note: this.note() };
		}.bind(this))
	}


	EditNoteViewModel.prototype.apply = function()
	{
		return this.save()
		.then(function(data)
		{
			return data;
		});
	}

})();
