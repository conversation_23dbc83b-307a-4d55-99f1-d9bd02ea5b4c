﻿(function()
{
	createNamespace("TF.Modal.ResourceScheduler").ResourceSubstitutionModalViewModel = ResourceSubstitutionModalViewModel;

	function ResourceSubstitutionModalViewModel(fromResourceId, allResourceEntities, resourceType, allEventSources, currentDate)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Resource Substitution");
		this.sizeCss = "modal-dialog-lg";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/resourcescheduler/resourcesubstitution');
		this.buttonTemplate("modal/positivenegative");
		this.resourceSubstitutionViewModel = new TF.Control.ResourceScheduler.ResourceSubstitutionViewModel(fromResourceId, allResourceEntities, resourceType, allEventSources, currentDate);
		this.data(this.resourceSubstitutionViewModel);
	};

	ResourceSubstitutionModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ResourceSubstitutionModalViewModel.prototype.constructor = ResourceSubstitutionModalViewModel;

	ResourceSubstitutionModalViewModel.prototype.positiveClick = function()
	{
		return this.resourceSubstitutionViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();

