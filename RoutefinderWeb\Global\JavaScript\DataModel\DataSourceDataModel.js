(function()
{
	createNamespace("TF.DataModel").DataSourceDataModel = DataSourceDataModel;
	function DataSourceDataModel()
	{
		this.name = ko.observable();
		this.copyFrom = ko.observable(tf.datasourceManager.databaseId);
		this.excludeFieldTrips = ko.observable(false);
		this.resetFieldTripId = ko.observable(false);
		this.nextFieldTripId = ko.observable();
		this.excludeTripResources = ko.observable(false);
		this.activeDate = ko.observable();
		this.inactiveDate = ko.observable();

		this.excludeFieldTrips.subscribe(function()
		{
			if (!this.excludeFieldTrips()) this.resetFieldTripId(false);
		}, this);

		this.resetFieldTripId.subscribe(function()
		{
			this.nextFieldTripId(this.resetFieldTripId() ? 1 : null);
		}, this);
	}

	DataSourceDataModel.prototype.constructor = DataSourceDataModel;
})();
