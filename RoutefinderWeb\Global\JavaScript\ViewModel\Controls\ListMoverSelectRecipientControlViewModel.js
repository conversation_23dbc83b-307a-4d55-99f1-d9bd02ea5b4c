﻿(function()
{
	createNamespace('TF.Control').ListMoverSelectRecipientControlViewModel = ListMoverSelectRecipientControlViewModel;

	function ListMoverSelectRecipientControlViewModel(selectedData, options)
	{
		if (options.columnSources)
		{
			this.columnSources = options.columnSources;
		}

		options.serverPaging = true;
		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
	}

	ListMoverSelectRecipientControlViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectRecipientControlViewModel.prototype.constructor = ListMoverSelectRecipientControlViewModel;

	ListMoverSelectRecipientControlViewModel.prototype.columnSources = {
		user: [
			{
				FieldName: "LoginID",
				DisplayName: "Login",
				Width: "100px",
				type: "string",
				isSortItem: true
			},
			{
				FieldName: "LastName",
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName("Last Name"),
				Width: "100px",
				type: "string",
				isSortItem: true
			},
			{
				FieldName: "FirstName",
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName("First Name"),
				Width: "100px",
				type: "string"
			},
			{
				FieldName: "Email",
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName("Email"),
				Width: "120px",
				type: "string"
			},
			{
				FieldName: "Deactivated",
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName("Deactivated"),
				Width: "120px",
				type: "boolean"
			}]
	};

	ListMoverSelectRecipientControlViewModel.prototype.initGridScrollBar = function(container)
	{ //need check soon.
		var $gridContent = container.find(".k-grid-content");
		$gridContent.css(
			{
				"overflow-y": "auto"
			});


		if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
		{
			$gridContent.find("colgroup col:last").css(
				{
					width: 137
				});
		}
		else
		{
			$gridContent.find("colgroup col:last").css(
				{
					width: 120
				});
		}
	};

	ListMoverSelectRecipientControlViewModel.prototype.onBeforeLeftGridDataBound = function(leftSearchGrid)
	{
		TF.Control.KendoListMoverWithSearchControlViewModel.prototype.onBeforeLeftGridDataBound.call(leftSearchGrid);
		var self = this;
		if (self.options.emailCheck === false)
		{
			return;
		}

		var emailColumnIndex = (self.options.type === "contact" || self.options.type === "staff") ? 2 : 3;

		leftSearchGrid.$container.find(".k-grid-content table.k-grid-table tr").map(function(idx, row)
		{
			var $row = $(row);
			var $colMail = $row.find("td").eq(emailColumnIndex);
			if (!$colMail.text().trim() || (self.options.type === "user" && $row.find("td").eq(4).text() === "true"))
			{
				$row.addClass("disable").css("color", "grey").bind("select", function(e)
				{
					e.preventDefault();
				});
			}
		});
	};

	ListMoverSelectRecipientControlViewModel.prototype.onLeftGridChange = function(e, rowsData)
	{
		var isDisableRow = false;
		var $selectRows = this.leftSearchGrid.kendoGrid.select();
		$selectRows.map(function(idx, row)
		{
			var $row = $(row);
			if ($row.hasClass("disable"))
			{
				$row.removeClass(TF.KendoClasses.STATE.SELECTED);
				isDisableRow = true;
			}
		}.bind(this));

		if (isDisableRow)
			this._clearLeftSelection();
		else
			this._obLeftSelData(rowsData);

		if (this._obLeftSelData().length > 0)
			this._clearRightSelection();
	};

	ListMoverSelectRecipientControlViewModel.prototype.getLeftGridAllDataByCurrentFilter = function()
	{
		var self = this, searchOption = $.extend({}, self.leftSearchGrid.searchOption),
			url = self.leftSearchGrid.getApiRequestURL(self.leftSearchGrid.options.url);

		return tf.promiseAjax.post(pathCombine(url),
			{
				data: searchOption.data
			})
			.then(function(apiResponse)
			{
				return apiResponse.Items;
			});
	}

	ListMoverSelectRecipientControlViewModel.prototype.filterToRightData = function(data)
	{
		if (this.options.emailCheck === false)
		{
			return data;
		}
		return data.filter((row) =>
		{
			// only when the type is user, Deacivated status needs to be checked.
			if (row.Email && (this.options.type !== "user" || row.Deactivated === "false" || row.Deactivated === false))
				return true;
		});
	};

	ListMoverSelectRecipientControlViewModel.prototype.afterInit = function()
	{
		// If don't do that , the enter key will bind apply event with last model.
		// Ex: send emial in staff or student,add or edit scheduled report... and so on 
		tf.shortCutKeys.clearSpecialHashMap();
	};


	ListMoverSelectRecipientControlViewModel.prototype.apply = function()
	{
		return TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this).then(function(selectedData)
		{
			return selectedData;
		});
	};

	ListMoverSelectRecipientControlViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (!isArraySame(this.oldData, this.selectedData))
			{
				return tf.promiseBootbox.yesNo("You have made changes to the " + (this.options.typeDisplayName || "recipients") + ".  Are you sure you want to discard them?", "Confirmation Message").then(function(result)
				{
					if (result)
					{
						resolve(true);
					}
					else
					{
						//reject();
					}
				});
			}
			else
			{
				resolve(true);
			}
		}.bind(this));
	};

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var oldIds = oldData.map(function(item)
		{
			return item.Id;
		});
		var newIds = newData.map(function(item)
		{
			return item.Id;
		});
		var diffData1 = Enumerable.From(newIds).Where(function(x)
		{
			return !Array.contain(oldIds, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldIds).Where(function(x)
		{
			return !Array.contain(newIds, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();
