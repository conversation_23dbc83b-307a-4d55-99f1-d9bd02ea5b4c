(function()
{
	createNamespace("TF.Control").ListFromDataPreviewViewModel = ListFromDataPreviewViewModel;

	function ListFromDataPreviewViewModel(options)
	{
		var self = this;
		self.field = options.field;
	}

	ListFromDataPreviewViewModel.prototype.init = function(viewModel, element)
	{
		let self = this;
		self.listFromDataQuestionControl = new TF.Control.Form.ListFromDataQuestion(self.field, tf.dataTypeHelper.getId(self.field.dataType));	
		self.$element = $(element);		
		self.$element.find(".previewContainer").append(self.listFromDataQuestionControl.element);
		self.$element.find(".question-title").css("padding-bottom", "10px");	
	};

	ListFromDataPreviewViewModel.prototype.dispose = function()
	{
		this.obText = null;
		this.listFromDataQuestionControl.dispose();

	};
})();