(function()
{
	createNamespace("TF.Fragment").NewStopViewModel = NewStopViewModel;

	function NewStopViewModel(geoCandidate, tripViewModelList)
	{
		var tripStopDataModel = new TF.DataModel.TripStopDataModel();
		this.tripStopDataModel = tripStopDataModel;
		tripStopDataModel.street(geoCandidate.Address.StreetName);
		tripStopDataModel.name(geoCandidate.Address.StreetName);
		tripStopDataModel.geometryPoint(geoCandidate.Point);
		tripStopDataModel.xcoord(geoCandidate.Point.coordinates[0]);
		tripStopDataModel.ycoord(geoCandidate.Point.coordinates[1]);

		this.boundaryEnabled = ko.observable(true);
		this.boundaryType = ko.observable('1');
		this.cityName = null;
		this.distance = null;
		this.distanceUnits = null;
		this.buffer = null;
		this.bufferUnits = null;
		this._map = null;
		this._markerLayer = null;
		this.tripListItems = new TF.Control.ListItemsViewModel.ColorBarListItemsViewModel();
		this._approachPoints = null;
		this.selectedTripDataModel = ko.computed(function()
		{
			return this.tripListItems.selectedValues()[0];
		}, this);

		this.selectedTripViewModel = function()
		{
			for (var index in tripViewModelList)
			{
				if (tripViewModelList[index].tripDataModel == this.selectedTripDataModel())
				{
					return tripViewModelList[index];
				}
			}
		};

		for (var i = 0; i < tripViewModelList.length; i++)
		{
			this.tripListItems.add(new TF.Control.ListItem.ColorBarListItem(
				tripViewModelList[i].tripDataModel.name(),
				tripViewModelList[i].tripDataModel,
				false,
				"#" + tripViewModelList[i].colorTheme));
		}
	}

	NewStopViewModel.prototype.drawMiniMap = function(vm, el)
	{
		var tile = new OpenLayers.Layer.TMS("TMS albany", pathCombine(TileServer, "tms/"),
			{
				layername: 'general', type: 'png',
				tileSize: new OpenLayers.Size(256, 256),
				resolutions: [156543.03390625, 78271.516953125, 39135.7584765625,
							  19567.87923828125, 9783.939619140625, 4891.9698095703125,
							  2445.9849047851562, 1222.9924523925781, 611.4962261962891,
							  305.74811309814453, 152.87405654907226, 76.43702827453613,
							  38.218514137268066, 19.109257068634033, 9.554628534317017,
							  4.777314267158508, 2.388657133579254, 1.194328566789627,
							  0.5971642833948135, 0.29858214169740677, 0.14929107084870338],
				serverResolutions: [78271.516953125, 39135.7584765625,
									19567.87923828125, 9783.939619140625,
									4891.9698095703125, 2445.9849047851562,
									1222.9924523925781, 611.4962261962891,
									305.74811309814453, 152.87405654907226,
									76.43702827453613, 38.218514137268066,
									19.109257068634033, 9.554628534317017,
									4.777314267158508, 2.388657133579254,
									1.194328566789627, 0.5971642833948135, 0.29858214169740677],
				// zoomOffset:1,
				transitionEffect: 'resize'
			}
		)
		this._map = new OpenLayers.Map(el, {
			projection: new OpenLayers.Projection("EPSG:900913"),
			maxResolution: 78271.516964,
			units: 'm',
			numZoomLevels: 19,
			maxExtent: new OpenLayers.Bounds(-20037508.3428, -20037508.3428, 20037508.3428, 20037508.3428)
		});
		//createNamespace("tf.debug").currentMap = this.map;
		this._map.addLayers([tile]);

		var styleMap = new OpenLayers.StyleMap({
			default: {
				graphicOpacity: 1
			}
		});

		var lookup = {
			stop: {
				externalGraphic: "../../global/img/stop_4A4A4A.png",
				graphicHeight: 18,
				graphicWidth: 18,
				graphicYOffset: -(18 / 2),
				graphicXOffset: -(18 / 2),
				cursor: "default"
			},
			approachIndicator: {
				externalGraphic: "../../global/img/appoarchpoint_black.png",
				graphicHeight: 16,
				graphicWidth: 16,
				graphicYOffset: -16,
				graphicXOffset: -8,
				cursor: "pointer",
				rotation:"${angle}"
			},
			approachIndicatorSelected: {
				externalGraphic: "../../global/img/appoarchpoint_red.png",
				graphicHeight: 16,
				graphicWidth: 16,
				graphicYOffset: -16,
				graphicXOffset: -8,
				cursor: "pointer",
				rotation: "${angle}"
			}
		};
		

		styleMap.addUniqueValueRules("default", "type", lookup);

		var markerLayer = new OpenLayers.Layer.Vector("markerLayer", {
			styleMap: styleMap
		});
		this._markerLayer = markerLayer;
		this._map.addLayer(markerLayer);

		this.getApproachPoint();
	};

	NewStopViewModel.prototype.getApproachPoint = function()
	{
		this.tripStopDataModel
		tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "approachpoint"), {
			data: this.tripStopDataModel.geometryPoint()
		})
		.then(function(apiResponse)
		{
			this._approachPoints = apiResponse.Items;
			this.redraw();
		}.bind(this))
		
	};

	NewStopViewModel.prototype.redraw = function()
	{
		var olFeature = null;
		var olFeaturesToBeAdded = [];
		this._markerLayer.destroyFeatures();

		var olGeometryStop = tf.converter.GeoJson2OlGeometry(this.tripStopDataModel.geometryPoint());
		olFeature = new OpenLayers.Feature.Vector(olGeometryStop, { type: "stop" });
		olFeaturesToBeAdded.push(olFeature);

		if (this.tripStopDataModel.approachGeometryPoint())
		{
			olFeature = MapPageView.AddMarker2(this.tripStopDataModel.approachGeometryPoint(), { type: "approachIndicatorSelected" });
			olFeaturesToBeAdded.push(olFeature);
		}

		for (var i = 0; i < this._approachPoints.length; i++)
		{
			if (this.tripStopDataModel.approachGeometryPoint())
			{
				if (JSON.stringify(this.tripStopDataModel.approachGeometryPoint()) == JSON.stringify(this._approachPoints[i]))
				{
					continue;
				}
			}
			var olGeometryApproach = tf.converter.GeoJson2OlGeometry(this._approachPoints[i]);
			//var rotation = this.getOrientation(olGeometryApproach, olGeometryStop);
			olFeature = new OpenLayers.Feature.Vector(olGeometryApproach, { type: "approachIndicator", angle: this.getOrientation(olGeometryStop, olGeometryApproach) });
			olFeaturesToBeAdded.push(olFeature);
		}

		this._markerLayer.addFeatures(olFeaturesToBeAdded);

		var olGeometry = tf.converter.GeoJson2OlGeometry(this.tripStopDataModel.geometryPoint());
		this._map.setCenter([olGeometry.x, olGeometry.y], 20);

		
	};

	NewStopViewModel.prototype.getOrientation = function(pt1, pt2)
	{
		var x = pt2.x - pt1.x;
		var y = pt2.y - pt1.y;

		var rad = Math.acos(y / Math.sqrt(x * x + y * y));
		// negative or positive
		var factor = x > 0 ? 1 : -1;

		return Math.round(factor * rad * 180 / Math.PI);
	};

	NewStopViewModel.prototype.destroy = function()
	{
		this.map.destroy();
	}
})()

