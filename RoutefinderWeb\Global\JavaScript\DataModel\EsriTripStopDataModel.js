﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.EsriTripStopDataModel = function(esriTripStopEntity)
	{
		namespace.BaseDataModel.call(this, esriTripStopEntity);
	};

	namespace.EsriTripStopDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.EsriTripStopDataModel.prototype.constructor = namespace.EsriTripStopDataModel;

	namespace.EsriTripStopDataModel.prototype.mapping = [
		{ from: 'Street', to: "name", default: '' },
		{ from: 'City', default: '' },
		{ from: 'TripId', default: -1 },
		{ from: 'StopId', default: -1 },
		{ from: 'StopTime', default: '' },
		{ from: 'Sequence', default: 0 },
		{ from: 'X', default: 0 },
		{ from: 'Y', default: 0 }
	];

})();