﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.AttendanceItemDataModel = function(documentEntity)
	{
		namespace.BaseDataModel.call(this, documentEntity);
	}

	namespace.AttendanceItemDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.AttendanceItemDataModel.prototype.constructor = namespace.AttendanceItemDataModel;

	namespace.AttendanceItemDataModel.prototype.mapping = [
		{ from: "Value", default: 0 },
		{ from: "AltValue", default: 0 },
		{ from: "StopTime", default: null },
		{ from: "OrigStopTime", default: null },
		{ from: "ActualStopTime", default: null },
		{ from: "AttendanceType", default: null },
		{
			from: "On", default: false, afterChange: function(data)
			{
				if (this.on())
				{
					this.off(false);
				}
			}
		},
		{
			from: "Off", default: false, afterChange: function(data)
			{
				if (this.off())
				{
					this.on(false);
				}
			}
		}
	];

})();
