﻿(function()
{
	var namespace = createNamespace("TF.Map");

	namespace.ViewMap = ViewMap;

	function ViewMap()
	{
		namespace.StandardMap.call(this);

		//this.showBasemapToggle = false;
		//this.toggleDiv = null;
		//this.drawTool = null;
		//this.onDrawCompleteEvent = null;
		//this.onDrawLayerClickEvent = null;
		//this.drawLayer = null;
		//this.drawLayerId = 'viewMapDrawLayer';
		//this.drawDeactivate = this.drawDeactivate.bind(this);
		//this.layerHelper = null;
		//this.symbolHelper = null;
		//this.drawLayerClicked = this.drawLayerClicked.bind(this);
		//this.showDeleteGeosearchPolygonButton = null;
		//this.hideDeleteGeosearchPolygonButton = null;
		//this.geoSearchPolygon = null;
		//this.onMapMouseDownEvent = null;
		//this.isShownClearButton = false;
	};

	ViewMap.prototype = Object.create(namespace.StandardMap.prototype);

	ViewMap.prototype.createMap = function()
	{
		namespace.StandardMap.prototype.createMap.apply(this, arguments);

		//this.layerHelper = new TF.Map.Layer();
		//this.symbolHelper = new TF.Map.Symbol();
	};

	ViewMap.prototype.drawActivate = function(callback, cancelback)
	{

		//var type = 'polygon';

		//this.drawLayer == null ? null : this.drawLayer.clear();
		//this.onDrawLayerClickEvent == null ? null : this.onDrawLayerClickEvent.remove();

		//this.drawTools = this.drawTools ? this.drawTools : new this.ArcGIS.Draw(this.map);
		//this.drawTools.fillSymbol = this.symbolHelper.geosearchPolygon();
		//this.drawTools.lineSymbol = this.symbolHelper.geosearchLine();

		//this.onDrawCompleteEvent = callback ? this.drawTools.on('draw-complete', callback) : this.drawTools.on('draw-complete', this.drawDeactivate);

		//this.onMapMouseDownEvent = this.map.on('mouse-down', function(event)
		//{
		//	if (event.button == 2)
		//	{
		//		// right click and cancel the geosearch polygon
		//		this.onMapMouseDownEvent.remove();

		//		this.drawDeactivate();

		//		cancelback(this._status.CALCELLED);
		//	}

		//	if (this.isShownClearButton && event.button == 0)
		//	{
		//		this.isShownClearButton = false;
		//		this.hideDeleteGeosearchPolygonButton();
		//	}
		//}.bind(this));

		//this.map.setMapCursor('crosshair');
		//this.editing = true;

		//this.drawTools.activate(type);

	};

	ViewMap.prototype.drawDeactivate = function(evt)
	{
		//this.drawTools.deactivate();

		//this.onDrawCompleteEvent.remove();
		//this.onDrawCompleteEvent = null;

		//this.map.setMapCursor('default');
		//this.editing = false;
	};

	ViewMap.prototype.addDrawGraphic = function(geometry, callback)
	{
		//this.initDrawLayer(function(status)
		//{
		//	var symbol = this.symbolHelper.geosearchPolygon();
		//	var drawGraphic = new this.ArcGIS.Graphic(geometry, symbol);

		//	this.onDrawLayerClickEvent = this.drawLayer.on('click', this.drawLayerClicked);

		//	var graphicAddEvent = this.drawLayer.on('graphic-add', function()
		//	{
		//		graphicAddEvent.remove();
		//		this.geoSearchPolygon = drawGraphic;

		//		callback(this._status.SUCCESS);
		//	}.bind(this));

		//	this.drawLayer.add(drawGraphic);
		//}.bind(this));

	};

	ViewMap.prototype.initDrawLayer = function(callback)
	{
		//if (this.drawLayer)
		//{
		//	// clear the layer
		//	this.drawLayer.clear();

		//	callback(this._status.SUCCESS);
		//} else
		//{
		//	// create draw graphic layer
		//	var layerAddEvent = this.map.on('layer-add', function(error, layer)
		//	{
		//		layerAddEvent.remove();

		//		callback(this._status.SUCCESS);
		//	}.bind(this));

		//	this.drawLayer = this.layerHelper.createGraphicLayer(this.drawLayerId);

		//	this.map.addLayer(this.drawLayer);
		//}
	};

	ViewMap.prototype.drawLayerClicked = function(evt)
	{
		//this.isShownClearButton = true;
		//this.showDeleteGeosearchPolygonButton();
	};

	ViewMap.prototype.clearGeoSearchPolygon = function()
	{
		//this.drawLayer.remove(this.geoSearchPolygon);
		//this.map.removeLayer(this.drawLayer);
		//this.drawLayer = null;
		//this.geoSearchPolygon = null;
	};


})();