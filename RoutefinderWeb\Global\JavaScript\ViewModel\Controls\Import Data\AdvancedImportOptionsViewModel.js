(function()
{
	createNamespace('TF.Control').AdvancedImportOptionsViewModel = AdvancedImportOptionsViewModel;

	function AdvancedImportOptionsViewModel(TFDFields, shortCutKeyName)
	{
		var self = this;
		self.TFDFields = JSON.parse(JSON.stringify(TFDFields));

		self.options = { "0": "Import Field", "1": "Ignore Field", "2": "Use if Routefinder blank", "3": "Use if Import is not blank" };
		self.obEditButtonDisable = ko.observable(true);

		tf.shortCutKeys.bind("alt+e", self.editOptionsBtnClick.bind(self), shortCutKeyName);
	};

	/**
	 * Initialize the advanced import options modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.$element = $(el);
		self.$grid = self.$element.find(".advancedgrid-container");
		self.kendoGrid = null;

		self.initParameters();
		self.initGrid();
	};


	/**
	 * Initialize essential parameters.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.initParameters = function()
	{
		var self = this;
		self.gridColumns = [
			{
				field: "InputFieldName",
				title: "Name",
				width: "200px"
			},
			{
				field: "IsPrimaryKey",
				title: "Key Field (Match Records)",
				width: "200px",
				template: function(dataItem)
				{
					return dataItem.IsPrimaryKey ? "Yes" : "No";
				}
			},
			{
				field: "Ungeocode",
				title: "Changes Lose Geocoding",
				width: "200px",
				template: function(dataItem)
				{
					return dataItem.Ungeocode ? "Yes" : "No";
				}
			},
			{
				field: "FieldOption",
				title: "Import Field Options",
				width: "200px",
				template: function(dataItem)
				{
					return self.options[dataItem.FieldAction];
				}
			}
		];
	};

	/**
	 * Initialize the grid.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.initGrid = function()
	{
		var self = this,
			columns = self.gridColumns,
			grid = self.kendoGrid;

		if (grid) { grid.destroy(); }

		self.$grid.kendoGrid({
			dataSource: self.TFDFields,
			height: 200,
			scrollable: true,
			selectable: true,
			columns: columns,
			change: self.onDataRowSelect.bind(self),
			dataBound: self.onDataBound.bind(self)
		});
	};

	/**
	 * The event handler when a data row is selected.
	 * @param {Event} e The change event.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.onDataRowSelect = function(e)
	{
		var self = this,
			uid = self.kendoGrid.select().attr("data-kendo-uid"),
			dataRow = self.kendoGrid.dataSource.getByUid(uid);

		self.selectInputFieldName = dataRow.InputFieldName;

		self.obEditButtonDisable(false);
	};

	/**
	 * The event handler when the data is bound to the grid.
	 * @param {Event} e The dataBound event.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.onDataBound = function(e)
	{
		var self = this, dataList, $row, isAnySelected = false;

		self.kendoGrid = self.$grid.data("kendoGrid");
		dataList = self.kendoGrid.dataSource.data();

		$.each(dataList, function(index, item)
		{
			$row = $("[data-kendo-uid=\"" + item.uid + "\"]");
			$row.dblclick(function()
			{
				self.openImportFieldOptionModal(item.InputFieldName);
			});
		});
	};

	/**
	 * The event of edit button click.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} e The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.editOptionsBtnClick = function(viewModel, e)
	{
		var self = this;
		if (self.selectInputFieldName)
		{
			self.openImportFieldOptionModal(self.selectInputFieldName);
		}

		return false;
	};

	/**
	 * Open the modal can be update import field option.
	 * @param {Number} id The option id.
	 * @return {void}
	 */
	AdvancedImportOptionsViewModel.prototype.openImportFieldOptionModal = function(selectInputFieldName)
	{
		var self = this,
			fieldOptions = Enumerable.From(self.TFDFields).Where(function(c)
			{
				return c.InputFieldName === selectInputFieldName;
			}).ToArray();
		if (fieldOptions && fieldOptions.length > 0)
		{
			tf.modalManager.showModal(new TF.Modal.ImportFieldOptionControlModalViewModel(fieldOptions[0])).then(function(result)
			{
				if (result)
				{
					self.TFDFields = self.TFDFields.map(function(field)
					{
						if (field.InputFieldName === result.InputFieldName)
						{
							field.IsPrimaryKey = result.IsPrimaryKey;
							field.Ungeocode = result.Ungeocode;
							field.FieldAction = result.FieldOption;
						}
						return field;
					});
					var dataSource = new kendo.data.DataSource({ data: self.TFDFields });
					self.kendoGrid.setDataSource(dataSource);
				}
			});
		}
	};

})();

