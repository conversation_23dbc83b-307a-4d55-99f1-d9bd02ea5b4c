(function()
{
	createNamespace("TF.Modal").KendoListMoverWithDropdownListControlModalViewModel = KendoListMoverWithDropdownListControlModalViewModel;

	var _DataFiledName = 'Name';
	var defaultOptions = {
		_DataFiledName: _DataFiledName,
		_GridConifg: {
			gridSchema: {
				// model: {
				// 	fields: {
				// 		'FieldName': { type: "string" },
				// 		'DisplayName': { type: "string" }
				// 	}
				// },
			},
			gridColumns: [
				{
					field: "Name"
				}
			],
			height: 400,
			selectable: TF.isMobileDevice ? "row" : "multiple"
		}
	};
	defaultOptions._sortItems = function(a, b)
	{
		var x, y;
		x = a['StaffTypeName'] ? a['StaffTypeName'].toLowerCase() : '';
		y = b['StaffTypeName'] ? b['StaffTypeName'].toLowerCase() : '';
		return (x == y ? 0 : (x > y ? 1 : -1));
	};
	defaultOptions._convertImportData = function(items)
	{
		return items;
	};
	defaultOptions._getUnSelectedItems = function(allItems, selectedItems)
	{
		var unSelectedItems = allItems.filter(function(item)
		{
			var matchResult = [];
			matchResult = selectedItems.filter(function(selectedItem)
			{
				return selectedItem.StaffTypeId === item.StaffTypeId;
			});
			return matchResult.length === 0;
		});
		return unSelectedItems;
	};
	defaultOptions._fillDisplayName = function(items)
	{
		return items;
	};
	defaultOptions._convertOutputData = function(items)
	{
		return items;
	};
	defaultOptions._sortKendoGrid = function(kendoGrid, sortItemFun)
	{
		kendoGrid.dataSource.sort({ field: "StaffTypeName", dir: "asc" });
		// kendoGrid.dataSource.data().sort(sortItemFun);
	};

	function KendoListMoverWithDropdownListControlModalViewModel(allItems, selectedItems, options, selectedPrimaryStaffTypeId)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-lg";
		this.title(options.title);
		this.description = options.description;
		this.contentTemplate("modal/kendolistmoverwithdropdownlistcontrol");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Apply");
		options = $.extend(true, {}, defaultOptions, options);
		options._GridConifg.gridColumns[0].template = options.formatter; // special code for student dataEntry

		this.kendolistMoverControlViewModel = new TF.Control.KendoListMoverWithDropdownListControlViewModel(allItems, selectedItems, options, selectedPrimaryStaffTypeId, this.shortCutKeyHashMapKeyName);
		this.data(this.kendolistMoverControlViewModel);
	}

	KendoListMoverWithDropdownListControlModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	KendoListMoverWithDropdownListControlModalViewModel.prototype.constructor = KendoListMoverWithDropdownListControlModalViewModel;

	KendoListMoverWithDropdownListControlModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.kendolistMoverControlViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	KendoListMoverWithDropdownListControlModalViewModel.prototype.dispose = function()
	{
		this.kendolistMoverControlViewModel.dispose();
	};
})();
