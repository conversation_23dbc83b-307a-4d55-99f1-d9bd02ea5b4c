﻿(function()
{
	createNamespace("TF.Modal.ResourceScheduler").SelectEditItemModalViewModel = SelectEditItemModalViewModel;

	function SelectEditItemModalViewModel(items)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Select');
		this.sizeCss = "modal-dialog-sm";
		this.obNegativeButtonLabel("Close");
		this.obPositiveButtonLabel("Apply");
		this.contentTemplate('modal/resourcescheduler/selectedititem');
		this.buttonTemplate("modal/positivenegative");
		this.selectEditItemViewModel = new TF.Control.ResourceScheduler.SelectEditItemViewModel(items);
		this.data(this.selectEditItemViewModel);
	};

	SelectEditItemModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SelectEditItemModalViewModel.prototype.constructor = SelectEditItemModalViewModel;

	SelectEditItemModalViewModel.prototype.positiveClick = function()
	{
		return this.selectEditItemViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();

