(function()
{
	createNamespace("TF.Grid.Detail").BaseGridDetail = BaseGridDetail;

	function BaseGridDetail()
	{
		this.gridFilter = {};
		this.grids = this._getGrids();
		this.loadedGrids = [];
		this.displayFilterRow = true;
		this.canDragDelete = true;
		this.targetID = ko.observable();
	}

	BaseGridDetail.prototype.create = function() { };

	BaseGridDetail.prototype.hasData = function()
	{
		return Promise.resolve();
	};

	BaseGridDetail.prototype._getGrids = function()
	{
		return [];
	};

	BaseGridDetail.prototype.getAvailabilityOnGrid = function()
	{
		return this.grids.some(grid => !grid.isDisabled);
	};
	BaseGridDetail.prototype.changeQuickFilterBarStatus = function(display)
	{
		this.loadedGrids.forEach(function(grid)
		{
			grid.$container.toggleClass("hide-filter-row", !display);
		});
		this.displayFilterRow = display;
	};
	BaseGridDetail.prototype.resizeGridColumns = function(e, type)
	{
		var columnIndex = -1,
			columns;
		this.loadedGrids.forEach(grid =>
		{
			if (grid._gridType == type)
			{
				if (columnIndex < 0)
				{
					for (var i = 0; i < grid.kendoGrid.columns.length; i++)
					{
						if (grid.kendoGrid.columns[i].FieldName == e.column.FieldName)
						{
							columnIndex = i;
							break;
						}
					}
				}
				grid.kendoGrid.columns[columnIndex].width = e.newWidth;
				grid.kendoGrid.thead.parent().find("col").eq(columnIndex).width(e.newWidth);
				grid.kendoGrid.tbody.parent().find("col").eq(columnIndex).width(e.newWidth);
				grid.$container.width(this.getWidth(grid.kendoGrid.columns));
				if (!columns)
				{
					columns = grid.kendoGrid.columns;
				}
			}
		});
		if (columns)
		{
			this.grids.forEach((grid) =>
			{
				if (grid.type == type)
				{
					grid.selectedColumns = this.cloneColumn(grid.defaultLayoutColumns, columns);
				}
			});
			this.saveSelectedColumnToStorage(type, columns);
		}
	};

	BaseGridDetail.prototype.changeGridColumns = function(editColumnViewModels, needToRebuildRootGrid)
	{
		// 'needToRebuildRootGrid' is the flag to determine whether the root grid needs rebuild
		var self = this, changed = {};

		self.grids.forEach(function(grid)
		{
			var editColumnViewModel = editColumnViewModels.find(vm => vm.name == grid.name);
			if (editColumnViewModel)
			{
				const modelVisibility = editColumnViewModel.obIsEnabled ? editColumnViewModel.obIsEnabled() : true;
				const isColumnsChanged = grid.selectedColumns ? grid.selectedColumns.map(o => o.FieldName).toString() != editColumnViewModel.selectedColumns.map(o => o.FieldName).toString() : true;
				const isVisibilityChanged = modelVisibility == grid.isDisabled;

				if (isVisibilityChanged)
				{
					needToRebuildRootGrid = true;
				}

				if (isColumnsChanged || isVisibilityChanged)
				{
					grid.selectedColumns = self.cloneColumn(grid.defaultLayoutColumns, editColumnViewModel.selectedColumns);
					grid.availableColumns = self.cloneColumn(grid.defaultLayoutColumns, editColumnViewModel.availableColumns);
					changed[grid.type] = true;
					self.saveSelectedColumnToStorage(grid.type, grid.selectedColumns);
				}

				tf.storageManager.save(`${grid.type}_visibility`, modelVisibility ? 1 : 0);
				grid.isDisabled = !modelVisibility;
			}
		});

		self.loadedGrids.forEach(function(grid)
		{
			if (changed[grid._gridType])
			{
				var setting = self.grids.filter(function(c) { return c.type == grid._gridType; })[0];
				var deleteColumns = Enumerable.From(grid._obSelectedColumns()).Where(function(current) { return !Enumerable.From(setting.selectedColumns).Any(function(newCol) { return current.FieldName == newCol.FieldName; }); }).ToArray();
				deleteColumns.forEach(function(c)
				{
					grid.clearCustomFilterByFieldName(c.FieldName);
				});
				grid._obSelectedColumns(setting.selectedColumns.map(x => $.extend({}, x)));
				grid._availableColumns = setting.availableColumns.map(x => $.extend({}, x));
				var oldFilter = grid.kendoGrid.dataSource.filter;
				var originalFilter = grid.kendoGrid.dataSource.originalFilter;

				grid.$container.width(self.getWidth(setting.selectedColumns));
				if (grid.kendoGrid && grid.kendoGrid.element)
				{
					grid.rebuildGrid().then(function()
					{
						grid.kendoGrid.dataSource.originalFilter = originalFilter;
						grid.kendoGrid.dataSource.filter = oldFilter;
					});
				}
			}
		});

		if (self.loadedGrids.every(g => !g.kendoGrid.element))
		{
			self.loadedGrids.splice(0, self.loadedGrids.length);
		}

		if (needToRebuildRootGrid)
		{
			tf.loadingIndicator.showImmediately();
			self.root.rebuildGrid().then(() => tf.loadingIndicator.tryHide());
		}
	};

	BaseGridDetail.prototype.cloneColumn = function(all, source)
	{
		return source.map(s =>
		{
			var item = Enumerable.From(all).FirstOrDefault(null, x => x.FieldName == s.FieldName);
			var result = {};
			if (item)
			{
				for (var key in item)
				{
					result[key] = s[key];
				}
			}
			return result;
		});
	};

	BaseGridDetail.prototype.saveSelectedColumnToStorage = function(type, columns)
	{
		tf.storageManager.save(type + "_columns", JSON.stringify(columns.map(function(column) { return { FieldName: column.FieldName, width: column.width }; })), null, null, false);
	};

	BaseGridDetail.prototype.getSelectedColumnFromStorage = function(type, allColumns)
	{
		let columns = tf.storageManager.get(type + "_columns");
		if (columns && columns.length > 0)
		{
			return columns.map(function(column)
			{
				let colInAll = allColumns.filter(function(c)
				{
					return c.FieldName == column.FieldName;
				});
				return $.extend({}, colInAll.length > 0 ? colInAll[0] : {}, column);
			});
		}
		return allColumns;
	};

	BaseGridDetail.prototype.getAvailableColumnFromStorage = function(type, allColumns)
	{
		let filedNames = tf.storageManager.get(type + "_columns");
		if (filedNames && filedNames.length > 0)
		{
			filedNames = Enumerable.From(filedNames).Select("$.FieldName").ToArray();
			return allColumns.filter(function(c)
			{
				return filedNames.indexOf(c.FieldName) < 0;
			});
		}
		return [];
	};

	BaseGridDetail.prototype.getWidth = function(columns)
	{
		return columns.reduce(function(result, column)
		{
			return result + parseInt(column.width);
		}, 18);
	};

	BaseGridDetail.prototype.buildGrid = function(container, options)
	{
		let self = this,
			gridType = options.gridType,
			columns = options.columns,
			gridState = options.gridState,
			gridFilter = options.gridFilter,
			displayFilterRow = options.displayFilterRow,
			onFilter = options.onFilter;
		container.addClass("detail-grid");
		container.width(this.getWidth(columns));
		if (!displayFilterRow)
		{
			container.addClass("hide-filter-row");
		}
		let gridOptions = {
			gridDefinition:
			{
				Columns: columns,
			},
			kendoGridOption: {
				autoBind: false,
				resizable: true,
				reorderable: true,
				columnResize: function(e)
				{
					options.onColumnResized && options.onColumnResized(e);
				},
				columnResizing: function(e)
				{
					options.onColumnResizing && options.onColumnResizing(e);
				}
			},
			gridType: gridType,
			isSmallGrid: true,
			getCount: false,
			showBulkMenu: false,
			showLockedColumn: false,
			canDragDelete: this.canDragDelete,
			routeState: Math.random().toString(36).substring(7),
			isLastKey: true,
			isDetailGrid: true,
			url: pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(options.gridType)),
			onDataBound: function()
			{
				if (lightKendoGrid.kendoGrid)
				{
					let count = lightKendoGrid.kendoGrid.dataSource.total(),
						pageInfo = count + " item" + (count == 1 ? "" : "s");
					container.children(".k-pager").find(".pageInfo").html(pageInfo);
					container.find("td").each(function(i, td) { $(td).attr("title", $(td).text()); });
				}
				options.onDataBound && options.onDataBound();
			},
			columnReorder: function()
			{
				setTimeout(function()
				{
					if (lightKendoGrid.kendoGrid)
					{
						options.onColumnChange({
							selectedColumns: lightKendoGrid.kendoGrid.columns
						});
					}
				});
			},
			onDragRemoveColumn: function(name)
			{
				options.onColumnChange({
					selectedColumns: lightKendoGrid.kendoGrid.columns.filter(function(c)
					{
						return c.DisplayName != name;
					})
				});
			}
		};
		if (options.dataSource)
		{
			gridOptions.dataSource = options.dataSource;
			delete gridOptions.url;
		}
		if (options.selectable)
		{
			gridOptions.selectable = options.selectable;
		}
		let lightKendoGrid = new TF.Grid.LightKendoGrid(container, gridOptions, gridState);

		lightKendoGrid._obSelectedColumns(columns);
		return new Promise(function(resolve)
		{
			let intervale = setInterval(function()
			{
				// wait kendo grid init
				if (lightKendoGrid.kendoGrid)
				{
					clearInterval(intervale);
				}
				else
				{
					return;
				}

				options.onDataBound && options.onDataBound();
				lightKendoGrid.kendoGrid.dataSource.filter = function(filter)
				{
					if (filter)
					{
						if (options.dataSource)
						{
							lightKendoGrid.kendoGrid && lightKendoGrid.kendoGrid.dataSource.originalFilter(filter);
							setTimeout(function()
							{
								lightKendoGrid.setColumnCurrentFilterIcon();
								lightKendoGrid.setColumnCurrentFilterInput();
							}.bind(this), 50);
							lightKendoGrid.setFilterIconByKendoDSFilter();
						}
						else
						{
							gridFilter[options.gridType] = filter;
							let filterSet = lightKendoGrid.convertKendo2RequestFilterSet({}, filter);
							lightKendoGrid.setBooleanNotSpecifiedFilterItem(filterSet.FilterItems);
							onFilter && onFilter(filter, filterSet);
						}
					}
					else
					{
						return lightKendoGrid.kendoGrid.dataSource._filter;
					}
				};

				// apply filter or read directly
				if (gridFilter[options.gridType] && gridFilter[options.gridType].filters)
				{
					self.applyGridFilter(lightKendoGrid, gridFilter[options.gridType].filters);
				}
				else
				{
					lightKendoGrid.kendoGrid.dataSource.read();
				}

				// Unbind the kendo key down event, because the event stop immediate propagation.
				$(lightKendoGrid.kendoGrid.wrapper).parents(".k-selectable.table-blank-fullfill").off("keydown.kendoGrid");
				$(lightKendoGrid.kendoGrid.wrapper).on("mousedown.change-grid-fouse", function(e)
				{
					tf.shortCutKeys.changeHashKey(lightKendoGrid.options.routeState);
					e.stopImmediatePropagation();
				});
				$(lightKendoGrid.kendoGrid.wrapper).parents(".kendo-grid").off("mousedown.change-grid-fouse").on("mousedown.change-grid-fouse", function()
				{
					tf.shortCutKeys.changeHashKey();
				});
				$(lightKendoGrid.kendoGrid.wrapper).parents(".kendo-grid").next().off("mousedown.change-grid-fouse").on("mousedown.change-grid-fouse", function()
				{
					tf.shortCutKeys.changeHashKey();
				});

				resolve(lightKendoGrid);
				if (options.bulkMenu)
				{
					self.initBulkMenu(lightKendoGrid, gridType);
				}
			}, 100);
		});
	};

	BaseGridDetail.prototype.applyGridFilter = function(lightKendoGrid, filter)
	{
		if (filter)
		{
			lightKendoGrid.kendoGrid && lightKendoGrid.kendoGrid.dataSource.originalFilter(filter);
			setTimeout(function()
			{
				lightKendoGrid.setColumnCurrentFilterIcon();
				lightKendoGrid.setColumnCurrentFilterInput();
			}.bind(this), 50);
			lightKendoGrid.setFilterIconByKendoDSFilter();
		}
	}

	BaseGridDetail.prototype.dispose = function()
	{
		this.loadedGrids = null;
	};

	BaseGridDetail.prototype.initBulkMenu = function(lightKendoGrid, gridType)
	{
		var self = this;
		var kendoGrid = lightKendoGrid.kendoGrid;
		lightKendoGrid.$container.delegate("table.k-selectable tr:not('.k-detail-row')", "contextmenu", function(e, parentE)
		{
			e && e.stopPropagation();
			e && e.preventDefault();
			var element = parentE ? parentE : e;
			self.targetID(kendoGrid.dataItem(e.currentTarget));
			var $visualTarget = $("<div style=\"margin:-5px;\"></div>").css({
				position: "absolute",
				left: element.clientX,
				top: element.clientY
			});
			$("body").append($visualTarget);
			var menuViewModel = new TF.Grid.GridMenuViewModel(self, kendoGrid);
			menuViewModel.selectedCount = kendoGrid.select().length;
			self.bulkMenu = new TF.ContextMenu.BulkContextMenu(pathCombine("Workspace/grid", gridType, "bulkmenu"), menuViewModel);
			tf.contextMenuManager.showMenu($visualTarget, self.bulkMenu);
			return false;
		});
		lightKendoGrid.$container.delegate(".kendogrid-blank-fullfill .fillItem", "mousedown", function(e)
		{
			var uid = $(e.currentTarget).data("id");
			var items = lightKendoGrid.$container.find("table.k-selectable tr").filter(function(a, b)
			{
				return $(b).data("kendoUid") === uid;
			});
			if (items.length > 0) $(items[0]).trigger("mousedown", [e]);
		});
		lightKendoGrid.baseKeyPress = lightKendoGrid.baseKeyPress.createInterceptor(function()
		{
			self.hideBulkMenu();
		});
	};

	BaseGridDetail.prototype.hideBulkMenu = function()
	{
		var self = this;
		if (self.bulkMenu && !self.bulkMenu.disposed) self.bulkMenu.$container.trigger("contextMenuClose");
	};
})();