(function()
{
	createNamespace("TF.Modal").ManageDocumentContactModalViewModel = ManageDocumentContactModalViewModel;

	function ManageDocumentContactModalViewModel(model)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.customizeToggle = ko.observable(true);
		this.title("Email: " + model.Document.Name);
		this.sizeCss = "modal-dialog-lg manage-document-contact-modal";
		this.contentTemplate("workspace/controlpanel/modal/ManageDocumentContact");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Send");
		this.obResizable(false);
		this.ManageDocumentContactViewModel = new TF.Control.ManageDocumentContactViewModel(model, true);
		this.data(this.ManageDocumentContactViewModel);
	}

	ManageDocumentContactModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ManageDocumentContactModalViewModel.prototype.constructor = ManageDocumentContactModalViewModel;

	ManageDocumentContactModalViewModel.prototype.afterRender = function(el)
	{
		TF.Modal.BaseModalViewModel.prototype.afterRender.call(this, el);
		this.customizeToggle = ko.observable(false);
	};

	ManageDocumentContactModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		// to send email
		var self = this;
		return self.data().sendEmail().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		}.bind(self))
			.catch(function()
			{
			});
	};

	ManageDocumentContactModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		var self = this;
		if (self.data().generateMergeReportDataList.length !== 0)
		{
			return tf.promiseBootbox.confirm({
				message: "Are you sure you want to cancel sending the email?",
				title: "Confirmation Message"
			})
				.then(function(result)
				{
					if (result)
					{
						self.positiveClose(true);
						return true;
					}
					return false;
				});
		}
		self.positiveClose(true);
		return true;
	};
})();


