(function()
{
	createNamespace('TF.Modal').AdvancedImportOptionsModalViewModel = AdvancedImportOptionsModalViewModel;

	function AdvancedImportOptionsModalViewModel(TFDFields)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-lg";
		self.contentTemplate('modal/import data/advancedimportoptionscontrol');
		self.buttonTemplate('modal/positivenegative');
		self.advancedImportOptionsViewModel = new TF.Control.AdvancedImportOptionsViewModel(TFDFields, self.shortCutKeyHashMapKeyName);
		self.data(self.advancedImportOptionsViewModel);
		self.title("Advanced Import Options");
		self.obPositiveButtonLabel("OK");
		self.obNegativeButtonLabel("Cancel");
	};

	AdvancedImportOptionsModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	AdvancedImportOptionsModalViewModel.prototype.constructor = AdvancedImportOptionsModalViewModel;

	/**
	 * The event of OK button click.
	 * @return {void}
	 */
	AdvancedImportOptionsModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.positiveClose(self.advancedImportOptionsViewModel.TFDFields);
	};
})();
