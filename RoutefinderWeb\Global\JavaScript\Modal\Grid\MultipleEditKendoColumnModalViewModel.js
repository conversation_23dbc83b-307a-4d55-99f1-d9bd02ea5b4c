(function()
{
	createNamespace("TF.Modal.Grid").MultipleEditKendoColumnModalViewModel = MultipleEditKendoColumnModalViewModel;

	function MultipleEditKendoColumnModalViewModel(gridsOptions)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-lg";
		self.title("Show/Hide Grid Columns");
		self.contentTemplate("workspace/grid/multipleEditkendocolumn");
		self.buttonTemplate("modal/positivenegativeother");
		self.viewModel = new TF.Grid.MultipleEditKendoColumnViewModel(gridsOptions, this.shortCutKeyHashMapKeyName);
		self.data(self.viewModel);
		self.obPositiveButtonLabel("Apply");
		self.obOtherButtonLabel("Reset");
	}

	MultipleEditKendoColumnModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	MultipleEditKendoColumnModalViewModel.prototype.constructor = MultipleEditKendoColumnModalViewModel;

	MultipleEditKendoColumnModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			var invaildLength = result.filter(function(item) { return !item }).length;
			if (invaildLength <= 0)
			{
				this.positiveClose(result);
			}
			else if (invaildLength >= 2)
			{
				var flag = false;
				for (var i in result)
				{
					if (!result[i])
					{
						if (flag)
						{
							this.viewModel.viewModels[i].pageLevelViewModel.clearError();
						}
						else
						{
							flag = true;
						}
					}
				}
			}
		}.bind(this));
	};

	MultipleEditKendoColumnModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose([]);
	}

	MultipleEditKendoColumnModalViewModel.prototype.otherClick = function()
	{
		this.viewModel.reset();
	};

	MultipleEditKendoColumnModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};
})();

