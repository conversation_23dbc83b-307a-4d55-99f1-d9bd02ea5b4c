﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.GradeDataModel = function(gradeCodeEntity)
	{
		namespace.BaseDataModel.call(this, gradeCodeEntity);
	}

	namespace.GradeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.GradeDataModel.prototype.constructor = namespace.GradeDataModel;

	namespace.GradeDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Code", default: "" },
		{ from: "Name", default: "" },
		{ from: "FeedTo", default: null },
		{ from: "IsGraduatedGrade", default: false },
	];
})();