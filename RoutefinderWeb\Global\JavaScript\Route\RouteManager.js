(function()
{
	createNamespace("TF").RouteManager = RouteManager;

	function RouteManager()
	{
		this._skipRoute = false;
		this.skipNextRoute = function(value)
		{
			if (value == false)
			{
				this._skipRoute = false;
			}
			else
			{
				this._skipRoute = true;
			}
		};

		this.openDocument = function(documentData, isRouteChange)
		{
			if (isRouteChange)
			{
				return tf.documentManagerViewModel.onRouteChange(documentData);
			}
			else
			{
				return tf.documentManagerViewModel.openObjectDocument(documentData);
			}
		};

		this.loadSticky = function()
		{
			this.loadNormalWindow();
		};

		// Start up work status.
		this.isStartUpComplete = false;

		// The delay function array.
		this.delayFunctions = [];

		// This function will be executed when start up work complete.
		this.startUpComplete = function()
		{
			if (this.isStartUpComplete)
			{
				return;
			}

			// Set start up work status.
			this.isStartUpComplete = true;

			// Call functions.
			this.delayFunctions.forEach(func =>
			{
				func && func();
			});

			// Clear function array.
			this.delayFunctions = [];
		}

		this.runDelayFunction = function(func)
		{
			if (!func)
			{
				return;
			}

			// If start up work complete, run immediately
			if (this.isStartUpComplete)
			{
				return func();
			}

			// If start up still working, save in a temp array and run after start up.
			else
			{
				this.delayFunctions.push(func);
			}
		}

		this.loadNewWindow = function()
		{
			const self = this;
			$.sammy(function()
			{
				this.get("#/nw", async function(context)
				{
					if (context.params.GridLinkGuid)
					{
						// Create load grid function.
						const loadGrid = function()
						{
							return TF.Helper.KendoGridHelper.loadGridLink(context.params.GridLinkGuid)
								.then(gridLinkData =>
								{
									if (!gridLinkData) { return; }
									const newDocument = new TF.Document.DocumentData(
										TF.Document.DocumentData.Grid,
										{
											gridData: gridLinkData.gridData,
											gridType: gridLinkData.gridType,
											gridState: new TF.Grid.GridState(),
											predefinedGridData: gridLinkData.predefinedGridData
										});

									tf.documentManagerViewModel.add(newDocument, false, true);

								});
						};

						self.runDelayFunction(loadGrid);
					}
					else if (context.params.whiteboardId)
					{
						var docData = TF.Document.DocumentData.RoutingMapInfo.create();
						docData.data = docData.data || {};
						docData.data.whiteboardId = context.params.whiteboardId;
						tf.documentManagerViewModel.add(docData, false, false, "", true, false);
					}
				});

				this.get("#/.*", function()
				{
					// console.log("empty route");
				});
			}).run("#/");
		};

		this.loadNormalWindow = function()
		{
			var find = false;

			var isNewWindow = (window.location.hash || "").startsWith("#/newwindow/");
			this.loadNewWindow();


			function trimEx(str, char)
			{
				if (!str)
				{
					return str;
				}

				return char ? str.replace(new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'), '') : str.trim();
			}

			function pathMatch(path, callback)
			{
				if (find)
				{
					return;
				}
				if (!isNewWindow && !window.location.href.includes("newwindow"))
				{
					return;
				}
				var PATH_NAME_MATCHER = /:([\w\d]+)/g,
					PATH_REPLACER = "([^\/]+)",
					QUERY_STRING_MATCHER = /\?([^#]*)?$/,
					path_match,
					param_names = [];
				while ((path_match = PATH_NAME_MATCHER.exec(path)) !== null)
				{
					param_names.push(path_match[1]);
				}
				path = new RegExp(path.replace(PATH_NAME_MATCHER, PATH_REPLACER));
				var match = path.exec(location.href);
				var result = null;
				if (match)
				{
					result = {};
					param_names.forEach(function(name, i)
					{
						var value = (match[i + 1] || "").replace(QUERY_STRING_MATCHER, '');
						result[name] = trimEx(value, "/");
					});
				}
				find = !!result;
				if (find && callback)
				{
					callback(result);
				}
			}

			pathMatch('#/grid/:type(/:gridState)?', function(params)
			{
				var kendoGridStateRawString = params['gridState'];
				var gridState;
				if (kendoGridStateRawString != "")
				{
					gridState = JSON.parse(decodeURIComponent((kendoGridStateRawString.substring(1, kendoGridStateRawString.length) || '').replace(/\+/g, ' ')));
				} else if (getQueryString("gridState"))
				{
					gridState = JSON.parse(decodeURIComponent(getQueryString("gridState")));
				}
				else
				{
					gridState = new TF.Grid.GridState();
				}
				tf.documentManagerViewModel.add(new TF.Document.DocumentData(TF.Document.DocumentData.Grid,
					{ gridType: params['type'], gridState: gridState }, null));
			});

			pathMatch("#/newwindow/grid/:gridType", function(params)
			{
				var gridType = params.gridType, gridState = new TF.Grid.GridState();

				if (tf.authManager.hasNationalMaps() && tf.dataTypeHelper.getNationalMapsNotSupportDataTypes().includes(tf.dataTypeHelper.getId(gridType)))
				{
					return;
				}

				gridState.filteredIds = getQueryString("ids").split(",").map(x => x.trim()).filter(Boolean).map(Number);
				tf.documentManagerViewModel.add(new TF.Document.DocumentData(TF.Document.DocumentData.Grid,
					{
						gridType: gridType,
						gridState: gridState,
					}, this.routeState));
			});

			pathMatch("#/newwindow/customdashboard", function(params)
			{
				let dashboardId = Number(getQueryString("id"));
				tf.documentManagerViewModel.add(new TF.Document.DocumentData(TF.Document.DocumentData.CustomizedDashboardDetail, {
					type: "CustomizedDashboardDetail",
					id: dashboardId,
					isReadMode: true
				}, this.routeState));
			});

			pathMatch("#/customgrid/:guid", function(params)
			{
				const linkGuid = params.guid;
				if (linkGuid)
				{
					return TF.Helper.KendoGridHelper.loadGridLink(linkGuid, true, true)
						.then(gridLinkData =>
						{
							gridLinkData && tf.documentManagerViewModel.add(new TF.Document.DocumentData(
								TF.Document.DocumentData.Grid,
								{
									gridData: gridLinkData.gridData,
									gridType: gridLinkData.gridType,
									gridState: new TF.Grid.GridState(),
									predefinedGridData: gridLinkData.predefinedGridData
								}), false, true);

						});
				}
			});

			pathMatch('#/griddetailview/:guid', function(params)
			{
				const linkGuid = params.guid;

				if (linkGuid)
				{
					return TF.Helper.KendoGridHelper.loadGridLink(linkGuid, true, true).then(gridData =>
					{
						if (gridData)
						{
							const dataTypeName = tf.applicationTerm.getApplicationTermPluralByName(tf.dataTypeHelper.getDisplayNameByDataType(gridData.gridType));
							let filterIds = gridData.predefinedGridData.filteredIds;
							tf.documentManagerViewModel.add(new TF.Document.DocumentData(
								TF.Document.DocumentData.Grid,
								{
									gridType: gridData.gridType,
									gridState: filterIds ? { filteredIds: filterIds } : undefined,
									isTemporaryFilter: true,
									filterName: `${dataTypeName} (Selected Records)`,
									predefinedGridData: gridData.predefinedGridData
								}), null).then((routeState) =>
								{
									let existingDoc = tf.documentManagerViewModel.allDocuments().find(function(i)
									{
										return i.routeState == routeState;
									});
									// for opening detail view
									if (existingDoc && existingDoc.gridDocViewModel)
									{
										let openDetailView = () =>
										{
											if (existingDoc.gridViewModel && filterIds && filterIds[0])
											{
												existingDoc.gridViewModel.selectedGridRowById(filterIds[0]);
												existingDoc.gridViewModel.openDetailView(filterIds[0]);
												existingDoc.gridViewModel.searchGrid.onDataBoundEvent.unsubscribe(openDetailView);
											}
										};
										let afterGridDocRender = () =>
										{
											existingDoc.gridViewModel.searchGrid.onDataBoundEvent.subscribe(openDetailView);
											existingDoc.gridDocViewModel.afterRenderEvent.unsubscribe(afterGridDocRender);
										};
										existingDoc.gridDocViewModel.afterRenderEvent.subscribe(afterGridDocRender);
									}
								});
						}
					});
				}
			});

			pathMatch(TF.Document.DocumentData.RoutingMapInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.RoutingMapInfo.create());
			});

			pathMatch(TF.Document.DocumentData.ResourceSchedulerInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.ResourceSchedulerInfo.create());
			});

			pathMatch(TF.Document.DocumentData.CustomizedDashboardInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.CustomizedDashboardInfo.create());
			});

			pathMatch(TF.Document.DocumentData.CustomizedDashboardLibraryInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.CustomizedDashboardLibraryInfo.create());
			});

			pathMatch(TF.Document.DocumentData.FormLibraryInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.FormLibraryInfo.create());
			});

			pathMatch(TF.Document.DocumentData.CustomizedDashboardDetailInfo.matchPath, function(params)
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.CustomizedDashboardDetailInfo.create(params.id));
			});

			pathMatch(TF.Document.DocumentData.UserProfileInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.UserProfileInfo.create());
			});

			pathMatch(TF.Document.DocumentData.UserDefinedFieldInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.UserDefinedFieldInfo.create());
			});

			pathMatch(TF.Document.DocumentData.RequiredFieldInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.RequiredFieldInfo.create());
			});

			pathMatch(TF.Document.DocumentData.FieldTripConfigsInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.FieldTripConfigsInfo.create());
			});

			pathMatch(TF.Document.DocumentData.ExagoBIReportEditorInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.ExagoBIReportEditorInfo.create());
			});

			pathMatch(TF.Document.DocumentData.DocumentClassificationInfo.matchPath, function()
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.DocumentClassificationInfo.create());
			});

			pathMatch(TF.Document.DocumentData.ControlPanelInfo.matchPath, function(params)
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.ControlPanelInfo.create(params.type, params.type));
			});

			pathMatch(TF.Document.DocumentData.ControlPanelGridInfo.matchPath, function(params)
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.ControlPanelGridInfo.create(params.type));
			});

			pathMatch(TF.Document.DocumentData.DataEntryInfo.matchPath, function(params)
			{
				tf.documentManagerViewModel.add(TF.Document.DocumentData.DataEntryInfo.create(params.type, params.id));
			});

			// always need to add this document on initialization, which is the welcome page
			tf.documentManagerViewModel.initWelcomePage();

		};

		RouteManager.toEmptyRoute = function()
		{
			location.replace("#/");
		};
	}
	var RegularFormSubmitFix = function(app)
	{
		return app._checkFormSubmission = function()
		{
			return false;
		};
	};

	Sammy('body', function()
	{
		this.use(RegularFormSubmitFix);
	});
})();