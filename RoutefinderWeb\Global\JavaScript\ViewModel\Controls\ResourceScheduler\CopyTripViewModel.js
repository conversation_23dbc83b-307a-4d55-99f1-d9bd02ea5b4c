(function()
{
	createNamespace('TF.Control.ResourceScheduler').CopyTripViewModel = CopyTripViewModel;

	function CopyTripViewModel(trip, options)
	{
		this.trip = trip;
		this.options = options;
		this.obOriginalTripName = ko.observable(trip.Name);
		this.obOriginalTripType = ko.observable(TF.RoutingMap.RoutingPalette.RoutingDataModel.sessions[trip.Session].name);
		this.obReverse = ko.observable(trip.Session !== TF.Helper.TripHelper.Sessions.Shuttle && trip.Session !== TF.Helper.TripHelper.Sessions.Both);
		this.obAutoAssignStudent = ko.observable(true);
		this.obTripType = ko.observable(trip.Session);
		this.obName = ko.observable(trip.Name + ' Copy');
		this.getTripInfoPromise = Promise.resolve();
	}

	CopyTripViewModel.prototype.init = function()
	{
		this.getTripInfoPromise = TF.Helper.TripCopyHelper.getTripWithPathAndBoundary(this.trip.Id);
	}

	CopyTripViewModel.prototype.apply = async function()
	{
		this.options?.showLoadingIndicator();
		const tripPendingCopy = await this.getTripInfoPromise;
		// mark the trip id as 0 to verify the trip name under create mode
		const isValidName = await TF.RoutingMap.RoutingPalette.RoutingDataModel.validateUniqueName(this.obName(), 0, 0, [], tripPendingCopy.TripDateRanges).then(() => true).catch(() => false);
		if (!isValidName)
		{
			this.options?.hideLoadingIndicator();
			return false;
		}

		await TF.Helper.TripCopyHelper.copyTrip(tripPendingCopy, this.obName(), this.obTripType(), this.obReverse(), this.obAutoAssignStudent());
		return true;
	}
})();
