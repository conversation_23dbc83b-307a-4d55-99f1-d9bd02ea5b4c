(function()
{
	createNamespace('TF.Control').UpdateInformationViewModel = UpdateInformationViewModel;

	function UpdateInformationViewModel(compareResult, shortCutKeyName)
	{
		var self = this;

		self.hasAcceptedRecords = false;

		self.primaryKeys = compareResult.ImportAndMergeDataOptions.TFXTFDFields
			.filter(f => f.IsPrimary<PERSON>ey)
			.map(f => f.InputFieldName);

		self.inited = ko.observable(false);

		self.compareResult = compareResult;

		self.compareResult.CurrentRecordType = null;
		self.compareResult.CurrentIndex = self.compareResult.CurrentIndex || 1;
		self.ImportAndMergeDataOptions = self.compareResult.ImportAndMergeDataOptions;
		self.TFXFilePath = self.compareResult.TFXFilePath;
		self.fileName = self.compareResult.fileName;
		self.obRecordCount = ko.observable(0);
		self.obKeyFields = ko.observable();
		self.obSelectRecordNum = ko.observable(0);
		self.isGeocode = self.compareResult.isGeocode;
		self.isSchedule = self.compareResult.isSchedule;
		self.isResidence = self.compareResult.isResidence;
		self.isDistance = self.compareResult.isDistance;
		self.selectedResdictIds = self.compareResult.selectedResdictIds;
		self.selectedGeocodeSource = self.compareResult.selectedGeocodeSource;
		self.isUseStopPool = self.compareResult.isUseStopPool;
		self.isCreateDoorToDoor = self.compareResult.isCreateDoorToDoor;
		self.selectedStopPoolCategory = self.compareResult.selectedStopPoolCategory;

		tf.shortCutKeys.bind("alt+a", self.acceptBtnClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+s", self.skipBtnClick.bind(self), shortCutKeyName);
		tf.shortCutKeys.bind("alt+p", self.printBtnClick.bind(self), shortCutKeyName);

		// Events
		self.onFinish = new TF.Events.Event();

		self.obAcceptDisable = ko.observable(false);
		self.obSkipDisable = ko.observable(false);
		self.obPrintDisable = ko.observable(false);

		self.isRunning = ko.observable(false);
		self.isRunning.subscribe(function(val)
		{
			self.obAcceptDisable(val);
			self.obSkipDisable(val);
			self.obPrintDisable(val);
		}, self);

		self.FinshData = {
			importStatus: null,
			compareResult: null,
			isNeedContinue: false
		}

		self.obIsComparing = ko.observable(true);
		self.obComparingMessage = ko.observable("Detecting changes ...");
	};

	UpdateInformationViewModel.prototype.automationProcessStatus = function(result)
	{
		tf.loadingIndicator.changeProgressbar(result.Percentage, result.Message, true);
	};

	UpdateInformationViewModel.prototype.dispose = function()
	{
	};

	UpdateInformationViewModel.prototype.statusUpdated = function(result)
	{
		var compareResult = result.compareResult || result.CompareResult;
		if (compareResult == null)
		{
			this.automationProcessStatus(result);
			return;
		}

		if (tf.loadingIndicator.isShowing())
		{
			tf.loadingIndicator.tryHide();
		}
		if (!!compareResult.CurrentRecordType && !!compareResult.CurrentPrimaryKeys)
		{
			// make sure only valid RecordTypes are updated to local reference. 
			// The Api might not include RecordTypes in some frequent signalR response for performance reason
			this.compareResult.CurrentRecordType = compareResult.CurrentRecordType;
			this.obKeyFields(compareResult.CurrentPrimaryKeys);
		}

		this.obSelectRecordNum(compareResult.CurrentIndex);
		this.obRecordCount(compareResult.RecordsTotalCount);
		if (compareResult.CurrentCompareData)
		{
			resolveCoordValues(compareResult.CurrentCompareData);
			var dataSource = new kendo.data.DataSource({
				data: compareResult.CurrentCompareData
			});
			this.kendoGrid.setDataSource(dataSource);
		} else if (this.$grid && this.$grid.children(".disable-overlay").length == 0)
		{
			this.$grid.prepend($("<div />", {
				"class": "disable-overlay",
				css: {
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					position: "absolute",
					"z-index": 4
				}
			}));
		}
	};

	/**
	 * Initialize the update information modal.
	 * @param {Object} viewModel The viewmodel.
	 * @param {DOM} el The DOM element bound with the viewmodel.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.init = function(viewModel, el)
	{
		const self = this;
		let compareResult = this.compareResult;
		tf.loadingIndicator.showImmediately();
		this.isRunning(true);

		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "ImportedData", "ScheduledJobs"), {
			data: compareResult.ImportAndMergeDataOptions,
			paramData: {
				fileName: this.fileName,
				isCompare: true
			},
			headers: { 'ConnectionId': TF.getConnectionId() }
		}, { overlay: false }).then(response =>
		{
			const compareResult = response.Items && response.Items[0] || {};
			var result = {
				isInteractive: true,
				importStatus: {},
				compareResult: compareResult,
			};

			this.compareResult = compareResult;
			this.isRunning(false);
			self.obIsComparing(false);

			if (result.compareResult.IsFinish && !result.compareResult.CurrentCompareData)
			{
				if (tf.loadingIndicator.isShowing())
				{
					tf.loadingIndicator.tryHide();
				}

				this.compareResult.IsFinish = true;
				this.apply();
			}
			else
			{
				this.initCore(el);
				this.statusUpdated(result);
			}
		}).catch((ex) =>
		{
			if (tf.loadingIndicator.isShowing())
			{
				tf.loadingIndicator.tryHide();
			}

			if (ex && ex.Message === "UserCancelled")
			{
				// This error is due to the initial GetCompareData call (interactive mode) is cancelled by user (by a seqential 'usercancel' request)
				// We simply alert the information to user, do not show this as error
				tf.promiseBootbox.alert("Import was cancelled.", "Information").then(() =>
				{
					this.onFinish.notify(self.FinshData);
				})
			}
			else
			{
				tf.promiseBootbox.alert(ex.Message, "Error").then(() =>
				{
					this.onFinish.notify(self.FinshData);
				});
			}
		});
	};

	UpdateInformationViewModel.prototype.initCore = function(el)
	{
		this.inited(true);
		this.$element = $(el);
		this.$grid = this.$element.find(".update-information-container");
		this.kendoGrid = null;
		this.initParameters();
		this.initGrid();
	};

	/**
	 * Initialize essential parameters.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.initParameters = function()
	{
		var self = this;
		self.gridColumns = [
			{
				field: "FieldName",
				title: "Fields",
				width: "160px"
			},
			{
				field: "SourceData",
				title: "Routefinder Data",
				width: "160px"
			},
			{
				field: "ImportData",
				title: "Import Data",
				width: "160px"
			}
		];
	};

	/**
	 * Initialize the grid.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.initGrid = function()
	{
		var self = this,
			columns = self.gridColumns,
			grid = self.kendoGrid;

		if (grid) { grid.destroy(); }

		resolveCoordValues(self.compareResult.CurrentCompareData);
		self.$grid.kendoGrid({
			dataSource: self.compareResult.CurrentCompareData,
			height: 200,
			scrollable: true,
			columns: columns,
			selectable: false,
			dataBound: function(e)
			{
				if (!self.compareResult.CurrentRecordType)
				{
					return; // No recordType fetched for current record
				}

				var isInsert = self.compareResult.CurrentRecordType.IsInsert;
				self.$grid.children(".disable-overlay").remove();
				var dataList = e.sender.dataSource.data();
				dataList.forEach(item =>
				{
					let $row = $("[data-kendo-uid=\"" + item.uid + "\"]");
					let compareA = item.ImportData, compareB = item.SourceData;
					if ($.isNumeric(compareA) && $.isNumeric(compareB))
					{
						compareA = parseFloat(compareA);
						compareB = parseFloat(compareB);
						item.set("ImportData", compareA)
						item.set("SourceData", compareB)
					}
					if (typeof compareA == 'string' && typeof compareB == 'string')
					{
						compareA = compareA.toLowerCase();
						compareB = compareB.toLowerCase();
					}

					if (isInsert || (item.IsNeedsImport && compareA !== compareB))
					{
						$row.addClass("diff-row");
					}
					else if (!item.IsNeedsImport)
					{
						$row.addClass("ignore-row");
					}
					if (self.primaryKeys.indexOf(item.FieldName) !== -1)
					{
						$($row.find("td")[0]).css("color", "red");
					}
				});
			}
		});

		self.kendoGrid = self.$grid.data("kendoGrid");
	};

	/**
	 * The event of accept click status.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.acceptBtnClick = function()
	{
		var self = this;
		self.isRunning(true);
		let stepName = "accept";
		tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportedDataStepNameUrl(stepName), {
			data: self.compareResult,
		}, { overlay: false }).then(function(response)
		{
			// we do not do automation update in accept/skip steps, will do all automation after user click "finish" to run import to end
			Promise.resolve(null).then(function()
			{
				const acceptedRecord = response.Items && response.Items[0];

				if (!self.hasAcceptedRecords)
				{
					self.hasAcceptedRecords = Boolean(acceptedRecord);
				}

				if (self.compareResult.IsFinish)
				{
					self.isRunning(false);
					self.compareResult = acceptedRecord;
					self.compareResult.IsFinish = true;
					self.apply();
					return true;
				}

				if (acceptedRecord)
				{
					self.compareResult = acceptedRecord;
					if (self.compareResult.Residence)
					{
						tf.modalManager.showModal(new TF.Modal.SelectResidenceSchoolModalViewModel(self.compareResult.Residence))
							.then(function(result)
							{
								if (result)
								{
									self.compareResult.Residence.SelectSchool = result;
								}

								self.setNextChangeRecord(self.compareResult);
							});
					}
					else
					{
						self.setNextChangeRecord(self.compareResult);
					}
				}
				else
				{
					tf.promiseBootbox.alert("The request is error.", "Error");
					self.isRunning(false);
					return false;
				}

				self.isRunning(false);
			}).catch(function(ex)
			{
				tf.promiseBootbox.alert(ex.Message, "Error");
				self.isRunning(false);
				return false;
			});
		}).catch((ex) =>
		{
			self.isRunning(false);
			tf.promiseBootbox.alert(ex.Message, "Error");
		});

		return false;
	};

	/**
	 * The event of skip click status.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.skipBtnClick = function()
	{
		var self = this;
		self.isRunning(true);
		let stepName = "skip";
		tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportedDataStepNameUrl(stepName), {
			data: self.compareResult,
			headers: {
				"ConnectionId": TF.getConnectionId()
			}
		}, { overlay: false }).then(function(response)
		{
			if (self.compareResult.IsFinish)
			{
				self.isRunning(false);
				self.compareResult = response.Items[0];
				self.compareResult.IsFinish = true;
				self.apply();
				return true;
			}

			if (response.Items && response.Items[0])
			{
				self.compareResult = response.Items[0];
				self.setNextChangeRecord(self.compareResult);
			}
			else
			{
				tf.promiseBootbox.alert("The request is error.", "Error");
				self.isRunning(false);
				return false;
			}

			self.isRunning(false);
		}).catch(function(ex)
		{
			tf.promiseBootbox.alert(ex.Message, "Error");
			self.isRunning(false);
			return false;
		});

		return false;
	};

	/**
	 * The event of print click status.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.printBtnClick = function()
	{
		var self = this;

		self.BeforePrint();
		window.print();
		self.AfterPrint();
		return false;
	};

	/**
	 * Add the print html.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.BeforePrint = function()
	{
		var self = this;

		$("body").append("<div class='update-information update-information-print'>" + self.$element.html() + "</div>");
	};

	/**
	 * remove the print html.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.AfterPrint = function()
	{
		$(".update-information-print").remove();
	};


	UpdateInformationViewModel.prototype.finish = function()
	{
		var self = this;
		self.FinshData.isNeedContinue = true;
		self.FinshData.compareResult = self.compareResult;
		self.FinshData.compareResult.IsInteractiveCancel = false;
		self.onFinish.notify(self.FinshData);
	}

	/**
	 * Apply all changed record into db.
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.apply = function()
	{
		// When reach end of interactive import (either accept, skip, finish or no change at init time)
		// we need to do finish operation so as to close the interactive steps modal and let StudentImportOptionsViewModel (caller)
		// to handle remaining(continue) works 
		this.finish();
	};

	UpdateInformationViewModel.prototype.afterApply = function(response)
	{
		var self = this;

		if (response.Items && response.Items[0])
		{
			let importStatus = response.Items[0];
			self.FinshData.compareResult = self.compareResult;
			self.FinshData.importStatus = importStatus;

			let numOfImportedStudents = importStatus.TotalNumberOfImportedStudents;
			var duration = moment(response.Items[0].Duration, "HH:mm:ss");
			var startTime = new Date();
			let hasStudent = numOfImportedStudents > 0;
			if (hasStudent)
			{
				var automationPromise = Promise.resolve();

				return automationPromise.then(function()
				{
					var timeDiff = new Date() - startTime;
					var time = Math.floor(timeDiff / 1000) + (duration.minute() * 60) + duration.second();
					return TF.Control.StudentImportOptionsViewModel.prototype.logDurationTime(response.Items[0].LogFilePath, time).then(function()
					{
						self.onFinish.notify(self.FinshData);
					});
				});
			}
			else
			{
				self.onFinish.notify(self.FinshData);
			}
		}
		else
		{
			tf.promiseBootbox.alert("The request is error.", "Error");
		}
		self.isRunning(true);
	};

	/**
	 * Do cancel by client-user in interactive-mode. This will trigger the onFinish event so that the outer StudentImportOptionsViewModel
	 * will handle the cancel logic based on FinishData after UpdateInformationViewModel (modal) itself is closed
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.cancel = function()
	{
		var self = this;

		tf.promiseBootbox.yesNo("Are you sure you want to cancel?", "Confirm Message")
			.then(function(confirmResult)
			{
				if (confirmResult)
				{
					self.FinshData.isNeedContinue = true;
					self.FinshData.compareResult = self.compareResult;
					self.FinshData.compareResult.IsInteractiveCancel = true;
					self.FinshData.hasAcceptedRecords = self.hasAcceptedRecords;
					self.onFinish.notify(self.FinshData);
				}
			});
	};

	/**
	* Don't change other records into db and finish import.
	* @return {void}
	*/
	UpdateInformationViewModel.prototype.Revert = function()
	{
		var self = this;
		tf.promiseBootbox.yesNo("Are you sure you want to revert?", "Confirm Message")
			.then(function(confirmResult)
			{
				if (confirmResult)
				{
					let stepName = "revert";
					tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postImportedDataStepNameUrl(stepName), {
						data: self.compareResult,
						headers: {}
					}).then(res =>
					{
						self.FinshData.isReverted = true;
						self.FinshData.isNeedContinue = false;
						self.FinshData.compareResult = null;
						self.onFinish.notify(self.FinshData);

					}).catch(ex =>
					{
						console.error(ex.Message)
					});
				}
			});

	};

	/**
	 * Display next record which will be updated
	 * @return {void}
	 */
	UpdateInformationViewModel.prototype.setNextChangeRecord = function(compareResult)
	{
		var self = this;

		if (compareResult.IsFinish && !compareResult.CurrentCompareData)
		{
			// Here we comes to the end of interactive import mode, we should trigger the PositiveClose action on the container Modal
			// This should be done by self.finish() which will notify the onFinish event (rather than call apply method)
			self.finish();
			return;
		}

		self.obKeyFields(compareResult.CurrentPrimaryKeys);
		self.obSelectRecordNum(compareResult.CurrentIndex);
		resolveCoordValues(compareResult.CurrentCompareData);
		var dataSource = new kendo.data.DataSource({
			data: compareResult.CurrentCompareData
		});
		self.kendoGrid.setDataSource(dataSource);
	};

	var _coordFields = {
		"xcoord": true,
		"ycoord": true,
		"@alt_dly_pu xcoord": true,
		"@alt_dly_pu ycoord": true,
		"@alt_dly_do xcoord": true,
		"@alt_dly_do ycoord": true,
	};

	function resolveCoordValues(data)
	{
		data = data || [];
		data.forEach(i =>
		{
			if (_coordFields[i.FieldName.toLowerCase()])
			{
				i.ImportData = resolveCoordValue(i.ImportData);
				i.SourceData = resolveCoordValue(i.SourceData);
			}
		});
	}

	function resolveCoordValue(coord)
	{
		if (coord == null)
		{
			return "";
		}

		coord = coord.trim();
		if (coord.toLowerCase() == "null")
		{
			return "";
		}

		var f = parseFloat(coord);
		if (f === 0)
		{
			return "";
		}

		return coord;
	}
})();

