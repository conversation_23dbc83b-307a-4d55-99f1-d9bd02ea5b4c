(function()
{
	createNamespace("TF.Modal").ListFromDataPreviewModalViewModel = ListFromDataPreviewModalViewModel;

	function ListFromDataPreviewModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);

		this.contentTemplate('modal/ListFromDataPreview');
		this.buttonTemplate('modal/positive');
		this.title(options.title);
		this.sizeCss = "modal-dialog-sm";
		this.obPositiveButtonLabel("OK");
		this.viewModel = new TF.Control.ListFromDataPreviewViewModel(options);
		this.data(this.viewModel);
	}

	ListFromDataPreviewModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ListFromDataPreviewModalViewModel.prototype.constructor = ListFromDataPreviewModalViewModel;

	ListFromDataPreviewModalViewModel.prototype.positiveClick = function()
	{
		this.negativeClose(false);
	};

	ListFromDataPreviewModalViewModel.prototype.negativeClick = function()
	{
		this.negativeClose(false);
	};
})();