﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.ContractorDeletion = ContractorDeletion;

	function ContractorDeletion()
	{
		this.type = 'contractor';
		namespace.BaseDeletion.apply(this, arguments);
	}

	ContractorDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	ContractorDeletion.prototype.constructor = ContractorDeletion;

	ContractorDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var strIds = ids.join(",");
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles"), {
			paramData:{
				"@fields": "Id",
				"@filter": "in(ContractorId," + strIds + ")"
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'vehicle',
				items: response.Items[0]
			});
		});

		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "staff"), {
			paramData:{
				"@fields": "Id",
				"@filter": "in(ContractorId," + strIds + ")"
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'staff',
				items: response.Items[0]
			});
		});

		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
      			"AttachedToType": tf.dataTypeHelper.getId("contractor")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});

		return Promise.all([p0, p1, p2]).then(function()
		{
			return associatedDatas;
		});
	}

	ContractorDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	ContractorDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "vehicle", "bycontractorIds");

		var p2 = this.getDataStatus(this.ids, "staff", "bycontractorIds");

		return Promise.all([p0, p1, p2]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};
})();