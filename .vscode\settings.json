{"git.confirmSync": false, "editor.insertSpaces": false, "editor.renderWhitespace": "all", "editor.detectIndentation": false, "editor.formatOnSave": false, "editor.formatOnPaste": false, "html.format.wrapLineLength": 0, "javascript.format.placeOpenBraceOnNewLineForFunctions": true, "javascript.format.placeOpenBraceOnNewLineForControlBlocks": true, "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true, "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": false, "window.zoomLevel": 0, "files.exclude": {"build": true, "node_modules": true, "build-Copy": true, "RoutefinderWeb.Build": true, "RoutefinderWeb.Resources": true, ".nuget": true, "grunt-tasks": true}, "search.exclude": {"**/node_modules": true, "build": true, "build-Copy": true, "**/build": true, "**/ThirdParty": true, "**/*.css": true, ".nuget": true, "RoutefinderWeb.Build": true, "RoutefinderWeb.Resources": true, "grunt-tasks": true}, "emmet.showExpandedAbbreviation": "inMarkupAndStylesheetFilesOnly", "emmet.showAbbreviationSuggestions": false, "cSpell.words": ["<PERSON><PERSON>"], "java.format.onType.enabled": false}