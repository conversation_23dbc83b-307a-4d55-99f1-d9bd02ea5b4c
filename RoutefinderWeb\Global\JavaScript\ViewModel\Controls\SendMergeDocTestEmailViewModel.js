(function()
{
	createNamespace('TF.Control').SendMergeDocTestEmailViewModel = SendMergeDocTestEmailViewModel;

	function SendMergeDocTestEmailViewModel(option, obDisableControl)
	{
		this.option = option;
		this.dataType = this.option.dataType;
		this.endpoint = tf.dataTypeHelper.getEndpoint(this.dataType);
		this.displayName = tf.dataTypeHelper.getDisplayNameByDataType(this.dataType);
		this.singleName = tf.applicationTerm.getApplicationTermSingularByName(this.displayName);
		this.pluralName = tf.applicationTerm.getApplicationTermPluralByName(this.displayName);
		this.status = ko.observable("sendEmail");
		this.obdisabledSend = obDisableControl;
		if (this.option.modelType === 'SendTo')
		{
			this.disabledSendInMobile = ko.observable('disabled');
		}
		else
		{
			this.disabledSendInMobile = ko.observable('');
		}
		this.titleContent = ko.observable(option.modelType === 'SendTo' ? 'SEND TO' : 'EMAIL');
		this.obRecipientList = ko.observableArray([]);
		this.obSearchRecipient = ko.observable("");
		this.obRecipientSearchResultIsEmpty = ko.observable(false);
		this.obNewEmail = ko.observable("");
		this.obIsNewEditing = ko.observable(false);
		this.selectRecipientToClick = this.selectRecipientToClick.bind(this);
		this.deleteFileClick = this.deleteFileClick.bind(this);
		this.formatDropdownValue = this.formatDropdownValue.bind(this);
		this.retryClick = this.retryClick.bind(this);
		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obValidationErrors = ko.observableArray([]);
		this.obEntityDataModel = ko.observable(new TF.DataModel.SendMergeDocTestEmailDataModel());
		this.obEmails = ko.observableArray(option.emailAddress);
		this.allRecords = ko.observableArray([]);
		this.obCcEnable = ko.observable(false);
		this.obBccEnable = ko.observable(false);
		this.obSelectedRecord = ko.observable();
		// this.associatedAddress = ko.observable("");
		if (option.placeEmailTo)
		{
			tf.promiseAjax["post"](pathCombine(tf.api.apiPrefix(), this.option.type, "getEmails"),
				{
					data:
					{
						selectedIds: this.option.selectedIds,
						emailColumns: this.option.emailColumns
					}
				}, { overlay: false }).then(function(result)
				{
					var address = [];
					result.Items.filter(function(item)
					{
						return item !== '';
					}).map(function(item)
					{
						address.push(
							new TF.DataModel.ScheduledReportReceiptDataModel(
								{
									SelectedUserId: 0,
									EmailAddress: item
								})
						)
					});
					if (option.placeEmailTo == 'Bcc')
					{
						this.obEmailBccList(address);
						this.obBccEnable(true);
					}
					else
					{
						this.obEmailToList(address);
					}
				}.bind(this));
		}

		this.initFromEmailSource(option).then(function()
		{
			this.obEntityDataModel().emailAddress(option.emailAddress[0]);
		}.bind(this));
		this.obEntityDataModel().emailAddress.subscribe(function()
		{
			tf.storageManager.save("fromEmailAddress", this.obEntityDataModel().emailAddress());
		}.bind(this));
		this.obEmailToList = ko.observableArray([]);
		this.obEmailCcList = ko.observableArray([]);
		this.obEmailBccList = ko.observableArray([]);
		this.obEmailToErrorList = ko.observableArray([]);
		this.isNewEmailValid = ko.observable(true);
		this.obEmailDoneClickable = ko.observable(true);
		this.obEmailToString = ko.computed(function()
		{
			if (TF.isPhoneDevice)
			{
				var emailCount = this.obEmailToList().length;
				if (!this.obEmailToList() || emailCount === 0)
				{
					return "";
				}
				else if (emailCount === 1)
				{
					return this.EmailFormatter(this.obEmailToList()[0]);
				}
				else
				{
					return this.EmailFormatter(this.obEmailToList()[0]) + " & " + (emailCount - 1) + " others";
				}
			}
			return this.obEmailToList().map(function(item)
			{
				return this.EmailFormatter(item);
			}.bind(this)).join(";");
		}.bind(this));
		this.obEmailCcString = ko.computed(function()
		{
			if (TF.isPhoneDevice)
			{
				var emailCount = this.obEmailCcList().length;
				if (!this.obEmailCcList() || emailCount === 0)
				{
					return "";
				}
				else if (emailCount === 1)
				{
					return this.EmailFormatter(this.obEmailCcList()[0]);
				}
				else
				{
					return this.EmailFormatter(this.obEmailCcList()[0]) + " & " + (emailCount - 1) + " others";
				}
			}
			return this.obEmailCcList().map(function(item)
			{
				return this.EmailFormatter(item);
			}.bind(this)).join(";");
		}.bind(this));
		this.obEmailBccString = ko.computed(function()
		{
			if (TF.isPhoneDevice)
			{
				var emailCount = this.obEmailBccList().length;
				if (!this.obEmailBccList() || emailCount === 0)
				{
					return "";
				}
				else if (emailCount === 1)
				{
					return this.EmailFormatter(this.obEmailBccList()[0]);
				}
				else
				{
					return this.EmailFormatter(this.obEmailBccList()[0]) + " & " + (emailCount - 1) + " others";
				}
			}
			return this.obEmailBccList().map(function(item)
			{
				return this.EmailFormatter(item);
			}.bind(this)).join(";");
		}.bind(this));
		this.documentEntities = ko.observableArray([]);
		if (this.option.modelType === 'SendTo')
			this.initAttachments();

		this.setEmailSubject();
		this.pageLevelViewModel = new TF.PageLevel.EmailPageLevelViewModel(this);
		this.obSearchRecipient.subscribe(this.searchRecipientForMobile.bind(this));
	};

	SendMergeDocTestEmailViewModel.prototype.columnSources = {
		student: [
			{
				FieldName: "FullName",
				DisplayName: "Name",
				Width: "120px",
				type: "string",
				isSortItem: true
			}
		],
		school: [
			{
				FieldName: "School",
				DisplayName: "Code",
				Width: "100px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "Name",
				DisplayName: "Name",
				Width: "260px",
				type: "string"

			}
		],
		altsite: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		contractor: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		district: [
			{
				FieldName: "District",
				DisplayName: "Code",
				Width: "180px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		fieldtrip: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		staff: [
			{
				FieldName: "FullName",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		trip: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		vehicle: [
			{
				FieldName: "BusNum",
				DisplayName: "BusNum",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		busfinderDriver: [
			{
				FieldName: "DriverName",
				DisplayName: "Driver",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		busfinderVehicle: [
			{
				FieldName: "ExternalName",
				DisplayName: "Vehicle",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		georegion: [
			{
				FieldName: "Name",
				DisplayName: "Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		],
		tripstop: [
			{
				FieldName: "Name",
				DisplayName: "Trip Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}, {
				FieldName: "Street",
				DisplayName: "Street",
				Width: "260px",
				type: "string"
			}
		],
		contact: [
			{
				FieldName: "FullName",
				DisplayName: "Full Name",
				Width: "180px",
				type: "string",
				isSortItem: true
			}
		]
	};

	SendMergeDocTestEmailViewModel.prototype.typesNeedSecondaryIdentifier =
		[
			'tripstop'
		];

	SendMergeDocTestEmailViewModel.prototype._addSortItemIntoRequest = function(requestOption)
	{
		var self = this;

		// build sortItem
		var rawSortItemArray = [];
		if (self.columns && self.columns.length > 0)
		{
			self.columns.forEach(function(column, idx)
			{
				if (column.isSortItem)
				{
					var rawSortItem = {
						fieldName: column.FieldName,
						sortIdx: column.sortIdx ? column.sortIdx : idx + 1

					};
					rawSortItemArray.push(rawSortItem);
				}
			});
		}

		rawSortItemArray.sort(function(a, b)
		{
			return a.sortIdx - b.sortIdx;
		});

		if (rawSortItemArray.length > 0)
		{
			requestOption.data.sortItems = requestOption.data.sortItems || [];

			rawSortItemArray.forEach(function(sortItem)
			{
				sortItem = {
					Name: sortItem.fieldName,
					Direction: "Ascending",
					isAscending: true
				};
				requestOption.data.sortItems.push(sortItem);
			});
		}
	};
	SendMergeDocTestEmailViewModel.prototype.getUrl = function(gridType, options)
	{
		var prefix = tf.api.apiPrefix();
		if (options.dataSource)
		{
			prefix = pathCombine(tf.api.apiPrefixWithoutDatabase(), options.dataSource);
		}
		return pathCombine(prefix, "search", tf.dataTypeHelper.getEndpoint(gridType));
	};

	SendMergeDocTestEmailViewModel.prototype.getFields = function()
	{
		var fields = this.columns.map(function(item) { return item.FieldName; }).concat(['Id']);
		if (this.option.type === "staff")
		{
			fields = fields.concat(["StaffTypes"]);
		}
		return fields;
	};

	SendMergeDocTestEmailViewModel.prototype.setupRequestOption = function(requestOptions)
	{
		requestOptions.data.idFilter = {
			ExcludeAny: []
		};
		if (this.getFields)
		{
			requestOptions.data.fields = this.getFields();
		}
		return requestOptions;
	};

	SendMergeDocTestEmailViewModel.prototype.initRecordsDropdown = function(el)
	{
		var self = this;
		self.mergeDocId = self.option.Id;
		var p1 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.endpoint, self.mergeDocId))
			.then(function(data)
			{
				self.option.type = tf.dataTypeHelper.getKeyById(data.Items[0].DataTypeId);
				self.columns = self.option.columnSources || self.columnSources[self.option.type];
				var requestOption = {
					data: {},
					paramData: {}
				};
				requestOption.data.idFilter = {};
				var sortColumns = TF.FilterHelper.getSortColumns(self.columns);
				TF.FilterHelper.setSortItems(requestOption, sortColumns);

				requestOption = self.setupRequestOption(
					{
						data: {},
						paramData: {}
					});
				requestOption.data.idFilter = {};

				self._addSortItemIntoRequest(requestOption);
				return tf.promiseAjax.post(self.getUrl(self.option.type, { dataSource: tf.datasourceManager.databaseId }), requestOption)
					.then(function(response)
					{
						response.Items.forEach(i =>
						{
							i.DisplayName = self.formatDropdownValue(i);
						});

						self.allRecords(response.Items);
						const $kendoDropDownList = $(el).find(".merge-document-record").kendoDropDownList({
							dataTextField: "DisplayName",
							dataValueField: "Id",
							dataSource: response.Items,
							virtual: 'virtual',
							change: function(e)
							{
								const selectIdx = this.select();
								const selectItem = self.allRecords()[selectIdx];
								self.obSelectedRecord(selectItem);
							}
						}).data("kendoDropDownList");

						if (self.allRecords() && self.allRecords().length > 0)
						{
							$kendoDropDownList.select(0);
							const selectItem = self.allRecords()[0];
							self.obSelectedRecord(selectItem);
						}
					});
			});

		var promises = [];
		promises.push(p1);
		return Promise.all(promises);
	};

	SendMergeDocTestEmailViewModel.prototype.formatDropdownValue = function(obj)
	{
		var self = this,
			identifier = obj[self.columns[0].FieldName];
		if (self.typesNeedSecondaryIdentifier.indexOf(self.option.type) !== -1)
		{
			var secondaryIdentifier = ' | ' + obj[self.columns[1].FieldName];
			return identifier + secondaryIdentifier;
		}
		return identifier;
	};

	SendMergeDocTestEmailViewModel.prototype.initAttachments = function()
	{
		this.documentEntities.push(
			{
				FileName: tf.applicationTerm.getApplicationTermPluralByName(this.option.term) + (this.option.modelType === 'SendTo' ? '.csv' : '.xls'),
				Guid: ko.observable(''),
				DatabaseId: tf.datasourceManager.databaseId,
				FileProgress: ko.observable("0%"),
				DownLoadComplete: ko.observable(false),
				UploadFailed: ko.observable(false)
			});
		if (this.option.type == 'altsite' ||
			this.option.type == 'student' ||
			this.option.type == 'school' ||
			this.option.type == 'tripstop' ||
			this.option.type == 'trip')
		{
			this.documentEntities.push(
				{
					FileName: this.pluralName + ".KML",
					Guid: ko.observable(''),
					DatabaseId: tf.datasourceManager.databaseId,
					FileProgress: ko.observable("0%"),
					DownLoadComplete: ko.observable(false),
					UploadFailed: ko.observable(false)
				});
		}
	};

	SendMergeDocTestEmailViewModel.prototype.setEmailSubject = function()
	{
		this.obEntityDataModel().emailSubject(this.option.subject);
	};

	SendMergeDocTestEmailViewModel.prototype.EmailFormatter = function(item)
	{
		return item.emailAddress();
	};

	SendMergeDocTestEmailViewModel.prototype.initFromEmailSource = function(option)
	{
		if (option.emailAddress)
		{
			return Promise.resolve();
		}
		option.emailAddress = [];
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "clientconfigs"),
			{
				paramData:
				{
					clientId: tf.authManager.clientKey
				}
			}, { overlay: false })
			.then(function(data)
			{
				if (!!data.Items[0].EmailAddress)
				{
					option.emailAddress.push(data.Items[0].EmailAddress);
				}
			}.bind(this)).then(function()
			{
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "userprofiles?dbid=" + tf.datasourceManager.databaseId), {}, { overlay: false })
					.then(function(response)
					{
						if (!!response.Items[0].Email)
						{
							if (option.emailAddress.length <= 0 || option.emailAddress[0] !== response.Items[0].Email)
							{
								option.emailAddress.push(response.Items[0].Email);
							}
						}
					}.bind(this));
			}.bind(this)).then(function()
			{
				this.obEmails(option.emailAddress);
			}.bind(this));
	};

	SendMergeDocTestEmailViewModel.prototype.initModel = function(viewModel, el)
	{
		this.initRecordsDropdown(el);
		this._$form = $(el);
		var validatorFields = {},
			isValidating = false,
			self = this,
			updateErrors = function($field, errorInfo)
			{
				var errors = [];
				$.each(self.pageLevelViewModel.obValidationErrors(), function(index, item)
				{
					if ($field[0] === item.field[0])
					{
						if (item.rightMessage.indexOf(errorInfo) >= 0)
						{
							return true;
						}
					}
					errors.push(item);
				});
				self.pageLevelViewModel.obValidationErrors(errors);
			};
		var validator;
		if (!TF.isPhoneDevice)
		{
			this._$form.find("input[name='from']").focus();
		}
		validatorFields.FromAddress = {
			trigger: "blur change",
			validators:
			{
				notEmpty:
				{
					message: "required"
				},
				callback:
				{
					message: "invalid email",
					callback: function(value, validator, $field)
					{
						if (!value)
						{
							updateErrors($field, "email");
							return true;
						}
						else
						{
							updateErrors($field, "required");
						}
						if (!testEmail(value).valid)
						{
							return false;
						}
						return true;
					}
				}
			}
		};

		validatorFields["mailToList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						if (TF.isPhoneDevice)
						{
							return true;
						}
						// if (!value)
						// {
						// 	updateErrors($field, "email");
						// 	return true;
						// }
						// else
						if (value)
						{
							updateErrors($field, "required");
						}
						value = value.trim();
						if (value === "")
						{
							this.obEmailToList([]);
							return true;
						}
						var result = true;
						var reg = /[,;]/;
						var emailList = value.split(reg);
						var errorEmails = [];
						var oldList = this.obEmailToList();
						var newList = [];
						$.each(emailList, function(n, item)
						{
							item = item.trim();
							if (item == "")
							{
								return;
							}
							var isValid = testEmail(item).valid;
							if (!isValid)
							{
								errorEmails.push(item);
								result = false;
							}

							var to = Enumerable.From(oldList).Where(function(c)
							{
								return this.EmailFormatter(c).trim() == item.trim();
							}.bind(this)).ToArray();
							if (to.length > 0)
							{
								newList.push(to[0]);
							}
							else
							{
								newList.push(
									new TF.DataModel.ScheduledReportReceiptDataModel(
										{
											SelectedUserId: 0,
											EmailAddress: item
										})
								);
							}

						}.bind(this));

						this.obEmailToList(newList);
						this._$form.find("small[data-bv-for=mailToList][data-bv-validator=callback]").text(errorEmails.length == 1 ? errorEmails[0] + ' is not a valid email.' : errorEmails.length + ' emails are invalid.');

						return result;

					}.bind(this)
				}
			}
		};

		validatorFields["mailCcList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						if (TF.isPhoneDevice)
						{
							return true;
						}
						// if (!value)
						// {
						// 	updateErrors($field, "email");
						// 	return true;
						// }
						// else
						if (value)
						{
							updateErrors($field, "required");
						}
						value = value.trim();
						if (value === "")
						{
							this.obEmailCcList([]);
							return true;
						}
						var result = true;
						var reg = /[,;]/;
						var emailList = value.split(reg);
						var errorEmails = [];
						var oldList = this.obEmailCcList();
						var newList = [];
						$.each(emailList, function(n, item)
						{
							item = item.trim();
							if (item == "")
							{
								return;
							}
							var isValid = testEmail(item).valid;
							if (!isValid)
							{
								errorEmails.push(item);
								result = false;
							}

							var to = Enumerable.From(oldList).Where(function(c)
							{
								return this.EmailFormatter(c).trim() == item.trim();
							}.bind(this)).ToArray();
							if (to.length > 0)
							{
								newList.push(to[0]);
							}
							else
							{
								newList.push(
									new TF.DataModel.ScheduledReportReceiptDataModel(
										{
											SelectedUserId: 0,
											EmailAddress: item
										})
								);
							}

						}.bind(this));

						this.obEmailCcList(newList);
						this._$form.find("small[data-bv-for=mailCcList][data-bv-validator=callback]").text(errorEmails.length == 1 ? errorEmails[0] + ' is not a valid email.' : errorEmails.length + ' emails are invalid.');

						return result;

					}.bind(this)
				}
			}
		};

		validatorFields["mailBccList"] = {
			trigger: "blur change",
			validators:
			{
				callback:
				{
					callback: function(value, validator, $field)
					{
						if (TF.isPhoneDevice)
						{
							return true;
						}
						// if (!value)
						// {
						// 	updateErrors($field, "email");
						// 	return true;
						// }
						// else
						if (value)
						{
							updateErrors($field, "required");
						}
						value = value.trim();
						if (value === "")
						{
							this.obEmailBccList([]);
							return true;
						}
						var result = true;
						var reg = /[,;]/;
						var emailList = value.split(reg);
						var errorEmails = [];
						var oldList = this.obEmailBccList();
						var newList = [];
						$.each(emailList, function(n, item)
						{
							item = item.trim();
							if (item == "")
							{
								return;
							}
							var isValid = testEmail(item).valid;
							if (!isValid)
							{
								errorEmails.push(item);
								result = false;
							}

							var to = Enumerable.From(oldList).Where(function(c)
							{
								return this.EmailFormatter(c).trim() == item.trim();
							}.bind(this)).ToArray();
							if (to.length > 0)
							{
								newList.push(to[0]);
							}
							else
							{
								newList.push(
									new TF.DataModel.ScheduledReportReceiptDataModel(
										{
											SelectedUserId: 0,
											EmailAddress: item
										})
								);
							}

						}.bind(this));

						this.obEmailBccList(newList);
						this._$form.find("small[data-bv-for=mailBccList][data-bv-validator=callback]").text(errorEmails.length == 1 ? errorEmails[0] + ' is not a valid email.' : errorEmails.length + ' emails are invalid.');

						return result;

					}.bind(this)
				}
			}
		};

		$(el).bootstrapValidator(
			{
				excluded: [],
				live: 'enabled',
				message: 'This value is not valid',
				fields: validatorFields
			}).on('success.field.bv', function(e, data)
			{
				var $parent = data.element.closest('.form-group');
				$parent.removeClass('has-success');
				if (!isValidating)
				{
					isValidating = true;
					self.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
			});
		this.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
		this.obEntityDataModel().apiIsDirty(false);
		this.loadRecipientsForMobile();
		if (this.option.modelType === 'SendTo')
		{
			this.LoadAttachments();
			this.intervalID = this.GetProgress();
			this.setTimeoutID = setTimeout(function()
			{
				clearInterval(self.intervalID);
				tf.promiseAjax["get"](pathCombine(tf.api.apiPrefix(), "search", self.option.type, "export", "resetProgress"), {}, { overlay: false });
			}, 120000);
		}
	};

	SendMergeDocTestEmailViewModel.prototype.GetProgress = function()
	{
		var self = this;
		tf.promiseAjax["get"](pathCombine(tf.api.apiPrefix(), "search", self.option.type, "export", "getProgress"), {}, { overlay: false })
			.then(function(response)
			{
				if (response.Items.length > 0)
				{
					self.documentEntities().map(function(item)
					{
						item.FileProgress(response.Items[0] + '%');
					});
				}
			});
		return setInterval(function()
		{
			tf.promiseAjax["get"](pathCombine(tf.api.apiPrefix(), "search", self.option.type, "export", "getProgress"), {}, { overlay: false })
				.then(function(response)
				{
					if (response.Items.length > 0)
					{
						self.documentEntities().map(function(item)
						{
							item.FileProgress(response.Items[0] + '%');
						});
					}
				});
		}, 3000);
	};

	SendMergeDocTestEmailViewModel.prototype.LoadAttachments = function()
	{
		var self = this;

		var url = pathCombine(tf.api.apiPrefix(), "search", this.option.type, "export", "email");
		var requestOption =
		{
			data:
			{
				gridLayoutExtendedEntity: this.option.layout,
				term: tf.applicationTerm.getApplicationTermPluralByName(this.option.term),
				SelectedIds: this.option.selectedIds,
				sortItems: this.option.sortItems,
				documentType: this.option.modelType === 'SendTo' ? 'csv' : 'xls'
			}
		};

		if (this.option.type === "busfinderhistorical")
			self.option.setRequestOption(requestOption);

		return tf.promiseAjax.post(url, requestOption, { overlay: false })
			.then(function(response)
			{
				if (this.intervalID)
					clearInterval(this.intervalID);
				this.obdisabledSend(false);
				this.disabledSendInMobile('');

				if (response.StatusCode != 200)
				{
					this.documentEntities().map(function(item)
					{
						item.FileProgress('100%');
						item.DownLoadComplete(true);
						item.UploadFailed(true);
					}.bind(this));
				}
				else
				{
					if (response.Items && response.Items.length > 0)
					{
						var obj = response.Items[0];
						this.documentEntities().map(function(item)
						{
							item.FileProgress('100%');
							item.DownLoadComplete(true);
							item.UploadFailed(false);
							if (item.FileName === tf.applicationTerm.getApplicationTermPluralByName(this.option.term) + ".KML")
							{
								if (obj.kml)
								{
									item.Guid(obj.kml.Guid);
								}
							}
							else
							{
								if (obj.document)
								{
									item.Guid(obj.document.Guid);
								}
							}
						}.bind(this));
					}
				}
				return this.resetProgress();
			}.bind(this))
			.catch(function()
			{
				this.documentEntities().map(function(item)
				{
					item.FileProgress('100%');
					item.DownLoadComplete(true);
					item.UploadFailed(true);
				}.bind(this));
			}.bind(this));
	};

	SendMergeDocTestEmailViewModel.prototype.resetProgress = function()
	{
		if (this.option.modelType === 'SendTo')
		{
			if (this.setTimeoutID)
				window.clearTimeout(this.setTimeoutID);
			return tf.promiseAjax["get"](pathCombine(tf.api.apiPrefix(), "search", this.option.type, "export", "resetProgress"), {}, { overlay: false });
		}
		return Promise.resolve(true);
	};

	SendMergeDocTestEmailViewModel.prototype.focusField = function(viewModel, e)
	{
		$(viewModel.field).focus();
	};

	SendMergeDocTestEmailViewModel.prototype.CcEnableClick = function()
	{
		this.obCcEnable(true);
	};

	SendMergeDocTestEmailViewModel.prototype.BccEnableClick = function()
	{
		this.obBccEnable(true);
	};

	SendMergeDocTestEmailViewModel.prototype.getSelectedItemsForSystemUser = function(recipients)
	{
		var self = this,
			emails = recipients.map(function(item)
			{
				return (item.emailAddress() || "").trim().toLowerCase();
			}).filter(Boolean);

		return self.getSelectedItemsByEmails(emails);
	};

	SendMergeDocTestEmailViewModel.prototype.getSelectedItemsByEmails = function(emails)
	{
		var self = this;
		return !emails.length ? Promise.resolve([]) : Promise.all(tf.urlHelper.chunk(emails, 100).map(function(emailChunk)
		{
			var filterSyntax = emailChunk.join(","),
				paramData = { "@filter": String.format("in(Email,{0})", filterSyntax) };

			return tf.promiseAjax.get(self.getRequestUrl(), { paramData: paramData }).then(function(r)
			{
				return r.Items;
			}, function()
			{
				return [];
			});
		})).then(function(values)
		{
			return _.flattenDeep(values);
		});
	};

	SendMergeDocTestEmailViewModel.prototype.getRequestUrl = function()
	{
		return pathCombine(tf.api.apiPrefixWithoutDatabase(), "users");
	};

	SendMergeDocTestEmailViewModel.prototype.selectRecipientToClick = function(viewModel, e)
	{
		var addressList;
		var addressType = $(e.currentTarget).data('send-type');
		if (addressType === 'To')
		{
			addressList = this.obEmailToList();
		}
		else if (addressType === 'Cc')
		{
			addressList = this.obEmailCcList();
		}
		else
		{
			addressList = this.obEmailBccList();
		}
		this.getSelectedItemsForSystemUser(addressList).then(function(selectedItems)
		{
			const emailAddressList = selectedItems.map(x => x.Email.toLowerCase());

			tf.modalManager.showModal(new TF.Modal.ListMoverSelectRecipientControlModalViewModel(selectedItems)).then(function(result)
			{
				if (!result)
				{
					return;
				}
				var list = result.map(function(item)
				{
					var name = item.LoginId;
					if (item.FirstName != "" || item.LastName != "")
					{
						name = item.FirstName + " " + item.LastName;
					}
					if (!emailAddressList.includes(item.Email.toLowerCase()))
					{
						emailAddressList.push(item.Email.toLowerCase());
					}
					return new TF.DataModel.ScheduledReportReceiptDataModel(
						{
							SelectedUserId: item.Id,
							EmailAddress: item.Email,
							UserName: name
						});
				});
				$.each(addressList, function(n, item)
				{
					if (item.selectedUserId() == 0 && !emailAddressList.includes(item.emailAddress().toLowerCase()))
					{
						list.push(item);
					}
				});
				if (addressType === 'To')
				{
					this.obEmailToList(list);
				}
				else if (addressType === 'Cc')
				{
					this.obEmailCcList(list);
				}
				else
				{
					this.obEmailBccList(list);
				}
			}.bind(this));
		}.bind(this));
	};

	SendMergeDocTestEmailViewModel.prototype.retryClick = function(viewModel, e)
	{
		viewModel.FileProgress('0%');
		viewModel.DownLoadComplete(false);
		viewModel.UploadFailed(false);
		this.intervalID = this.GetProgress();
		this.obdisabledSend(true);
		this.disabledSendInMobile('disabled');

		return tf.promiseAjax["post"](pathCombine(tf.api.apiPrefix(), "search", this.option.type, "export", "email"),
			{
				data:
				{
					gridLayoutExtendedEntity: this.option.layout,
					term: tf.applicationTerm.getApplicationTermPluralByName(this.option.term),
					SelectedIds: this.option.selectedIds,
					sortItems: this.option.sortItems,
					documentType: this.option.modelType === 'SendTo' ? 'csv' : 'xls'
				}
			}, { overlay: false })
			.then(function(response)
			{
				if (this.intervalID)
					clearInterval(this.intervalID);
				this.obdisabledSend(false);
				this.disabledSendInMobile('');

				if (response.StatusCode != 200)
				{
					viewModel.FileProgress('100%');
					viewModel.DownLoadComplete(true);
					viewModel.UploadFailed(true);
				}
				else
				{
					if (response.Items && response.Items.length > 0)
					{
						var obj = response.Items[0];
						viewModel.FileProgress('100%');
						viewModel.DownLoadComplete(true);
						viewModel.UploadFailed(false);
						if (viewModel.FileName === tf.applicationTerm.getApplicationTermPluralByName(this.option.term) + ".KML")
						{
							if (obj.kml)
							{
								viewModel.Guid(obj.kml.Guid);
							}
						}
						else
						{
							if (obj.document)
							{
								viewModel.Guid(obj.document.Guid);
							}
						}
					}
				}
				this.resetProgress();
			}.bind(this))
			.catch(function()
			{
				this.documentEntities().map(function(item)
				{
					item.FileProgress('100%');
					item.DownLoadComplete(true);
					item.UploadFailed(true);
				}.bind(this));
			}.bind(this));;
	};

	SendMergeDocTestEmailViewModel.prototype.deleteFileClick = function(viewModel, e)
	{
		return tf.promiseAjax["delete"](pathCombine(tf.api.apiPrefix(), "search", this.option.type, "export", "email/delete"),
			{
				data: [viewModel.Guid()]
			}, { overlay: false })
			.then(function(response)
			{
				this.documentEntities.remove(
					function(item)
					{
						return item.FileName == viewModel.FileName;
					});
			}.bind(this));
	};

	SendMergeDocTestEmailViewModel.prototype.apply = function()
	{
		return this.trysave()
			.then(function(data)
			{
				return data;
			})
			.catch(()=>{});
	};

	SendMergeDocTestEmailViewModel.prototype.trysave = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(function(valid)
			{
				if (!valid)
				{
					if (TF.isPhoneDevice)
					{
						if (this.obEmailToList().length == 0 && this.obEmailCcList().length == 0 && this.obEmailBccList().length == 0)
						{
							var message = "At least one recipient is required.";
							tf.promiseBootbox.alert(message, "Warning");
							return Promise.reject();
						}
					}

					var validator = this._$form.data("bootstrapValidator");
					if (validator)
					{
						var messages = validator.getMessages(validator.getInvalidFields());
						var $fields = validator.getInvalidFields();
						var validationErrors = [];
						$fields.each(function(i, fielddata)
						{
							if (i == 0)
							{
								$(fielddata).focus();
							}
							validationErrors.push(
								{
									name: ($(fielddata).attr('data-bv-error-name') ? $(fielddata).attr('data-bv-error-name') : $(fielddata).closest("div.form-group").find("strong").text()),
									message: messages[i].replace('&lt;', '<').replace('&gt;', '>'),
									field: $(fielddata)
								});

						}.bind(this));
						this.obErrorMessageDivIsShow(true);
						this.obValidationErrors(validationErrors);
					}

					return Promise.reject();
				}
				else
				{
					return this.save();
				}
			}.bind(this));
	};


	SendMergeDocTestEmailViewModel.prototype._convertDocumentEntitiesToJSON = function(documentEntities)
	{
		var result = documentEntities.map(function(doc)
		{
			return {
				FileName: doc.FileName,
				Guid: doc.Guid(),
				DatabaseId: doc.DatabaseId,
				FileProgress: doc.FileProgress(),
				DownLoadComplete: doc.DownLoadComplete(),
				UploadFailed: doc.UploadFailed()
			};
		});

		return result;
	};

	SendMergeDocTestEmailViewModel.prototype.save = function()
	{
		return this.checkConfigure().then(function(result)
		{
			if (result)
			{
				this.obEntityDataModel().mailToList(this.obEmailToList().map(function(item)
				{
					return item.emailAddress();
				}));

				this.obEntityDataModel().mailCcList(this.obEmailCcList().map(function(item)
				{
					return item.emailAddress();
				}));

				this.obEntityDataModel().mailBccList(this.obEmailBccList().map(function(item)
				{
					return item.emailAddress();
				}));

				return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocumentemails"),
					{
						data:
							[{
								SpecificRecordIds: this.obSelectedRecord() ? [this.obSelectedRecord().Id] : [],
								DatabaseId: tf.datasourceManager.databaseId,
								DocumentId: this.mergeDocId,
								NoRecords: !this.obSelectedRecord(),
								IsTest: true,
								EmailMapping:
									[{
										RecordId: (this.obSelectedRecord() ? this.obSelectedRecord().Id : -1),
										To: this.obEntityDataModel().mailToList().join(";"),
										Cc: "",
										Bcc: "",
										ToContact: this.obEntityDataModel().mailToList().map(mail => { return { "Name": mail, "Email": mail }; }),
										CcContact: [],
										BccContact: [],
									}]
							}]
					}).then(function(response)
					{
						if (response.Items[0].Result)
						{
							return tf.promiseBootbox.alert("An email has been successfully sent.", "Email Successfully Sent")
								.then(function()
								{
									return true;
								}.bind(this));
						}
						else
						{
							return Promise.reject();
						}
					}.bind(this))
					.catch(function(e)
					{
						tf.promiseBootbox.okRetry(
							{
								message: "An email could not be sent. Verify your SMTP Server settings. If you continue to experience issues, contact <NAME_EMAIL> or 888-427-2403.",
								title: "Unable to Send the test email"
							}).then(function(confirm)
							{
								if (!confirm)
								{
									return this.save();
								}
							}.bind(this));
					}.bind(this));
			}
			else
			{
				tf.promiseBootbox.alert("The SMTP Server must be configured to send emails. This is not configured for your product. Contact your System Administrator to configure these settings. If you continue to experience issues, contact <NAME_EMAIL> or 888-427-2403.", "SMTP Server Settings Are Not Configured");
			}
		}.bind(this));
	};

	SendMergeDocTestEmailViewModel.prototype.checkConfigure = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "clientconfigs"),
			{
				paramData:
				{
					clientId: tf.authManager.clientKey
				}
			}, { overlay: false })
			.then(function(data)
			{
				if (data.Items && data.Items.length > 0)
				{
					let item = data.Items[0];
					let isValidCustomSMTP = !item.IsTransfinderEmail && item.SMTPHost && item.SMTPPort;
					if (item.IsTransfinderEmail || isValidCustomSMTP)
					{
						return true;
					}
				}
				return false;
			});
	};

	SendMergeDocTestEmailViewModel.prototype.close = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (this.obEntityDataModel() && this.obEntityDataModel().apiIsDirty())
			{
				resolve(tf.promiseBootbox.yesNo("Are you sure you want to cancel this " + (!!this.option.selectedIds ? "" : "test ") + "email?", "Confirmation Message"));
			}
			else
			{
				resolve(true);
			}
		}.bind(this));
	};

	//select recipients in mobile
	SendMergeDocTestEmailViewModel.prototype.selectRecipients = function(e)
	{
		var addressList;
		var addressType = $(e).data('send-type');
		if (addressType === 'To')
		{
			addressList = this.obEmailToList();
			this.status("selectToRecipients");
		}
		else if (addressType === 'Cc')
		{
			addressList = this.obEmailCcList();
			this.status("selectCcRecipients");
		}
		else
		{
			addressList = this.obEmailBccList();
			this.status("selectBccRecipients");
		}
		$(".mobile-modal-content-body").scrollTop(0);
		var self = this;
		var recipients = [];
		this.recipientListSource.forEach(function(item)
		{
			item.obSelected(false);
			var selected = Enumerable.From(addressList).Any(function(email)
			{
				return email.selectedUserId() == item.Id;
			});
			item.obSelected(selected);
			recipients.push(item);
		});
		addressList.forEach(function(item)
		{
			if (item.selectedUserId() === 0)
			{
				recipients.push(
					{
						Email: item.emailAddress(),
						Id: 0,
						obSelected: ko.observable(true),
						isNew: true,
						FirstName: '',
						LastName: '',
						show: ko.observable(true)
					});
			}
		});
		this.obSearchRecipient("");
		this.obIsNewEditing(false);
		this.obNewEmail("");
		this.isNewEmailValid(true);
		this.obRecipientList(Enumerable.From(recipients).OrderByDescending("$.obSelected()").ThenByDescending("$.isNew").ThenBy("$.Email").ToArray());
	};

	SendMergeDocTestEmailViewModel.prototype.selectRecipientsCancel = function()
	{
		if (!this.isNewEmailValid())
		{
			this.obIsNewEditing(false);
			this.obNewEmail("");
		}
		this.status("sendEmail");
	};

	SendMergeDocTestEmailViewModel.prototype.selectRecipientsDone = function()
	{
		if (!this.obEmailDoneClickable())
		{
			return;
		}
		var addressList = this.obRecipientList().filter(function(item)
		{
			return item.obSelected();
		}).map(function(item)
		{
			return new TF.DataModel.ScheduledReportReceiptDataModel(
				{
					SelectedUserId: item.Id,
					EmailAddress: item.Email,
					UserName: item.FirstName + " " + item.LastName
				});
		});
		if (this.status() === 'selectToRecipients')
		{
			this.obEmailToList(addressList);
		}
		else if (this.status() === 'selectCcRecipients')
		{
			this.obEmailCcList(addressList);
		}
		else
		{
			this.obEmailBccList(addressList);
		}
		this.status("sendEmail");
		this._$form.data("bootstrapValidator").validate();
	};

	SendMergeDocTestEmailViewModel.prototype.loadRecipientsForMobile = function(viewModel, e)
	{
		if (this.obRecipientList().length === 0 && TF.isPhoneDevice)
		{
			tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", "user"), {}, { overlay: false }).then(function(apiResponse)
			{
				apiResponse.Items = Enumerable.From(apiResponse.Items).Where("$.Email!=''").OrderBy("$.Email").ToArray();
				apiResponse.Items.forEach(function(item)
				{
					item.obSelected = ko.observable(false);
					item.isNew = false;
					item.show = ko.observable(true);
				});
				this.obRecipientList(apiResponse.Items);
				this.recipientListSource = apiResponse.Items.slice();
			}.bind(this));
		}
	};

	SendMergeDocTestEmailViewModel.prototype.selectRecipientForMobile = function(user, e)
	{
		user.obSelected(!user.obSelected());
		this.obRecipientList(Enumerable.From(this.obRecipientList()).OrderByDescending("$.obSelected()").ThenBy("$.Email").ToArray());
	};

	SendMergeDocTestEmailViewModel.prototype.addNewRecipientForMobile = function(user, e)
	{
		var self = this;
		self.obEmailDoneClickable(false);
		if (this.obIsNewEditing())
		{
			return;
		}
		this.obIsNewEditing(true);

		var newInput = self._$form.find(".new-input"),
			newInputError = newInput.next();
		newInputError.hide();

		function addNew()
		{
			function emailIsValid()
			{
				self.isNewEmailValid(true);
				self.obIsNewEditing(false);
				self.obNewEmail("");
				setTimeout(function()
				{
					self.obEmailDoneClickable(!self.obIsNewEditing() && self.isNewEmailValid());
				});
			}
			if (!self.obNewEmail() || !$.trim(self.obNewEmail()))
			{
				emailIsValid();
			}
			else if (!testEmail(self.obNewEmail()).valid)
			{
				var invalidList = testEmail(self.obNewEmail()).inValidList;
				newInputError.children().html("invalid email" + (invalidList.length > 1 ? "s" : "")).end().show();
				self.obIsNewEditing(false);
				self.isNewEmailValid(false);
			}
			else
			{
				var reg = /[,;]/;
				var emailList = self.obNewEmail().split(reg);
				emailList.forEach(function(mail, index)
				{
					if (mail && mail.trim() !== '')
					{
						self.obRecipientList.unshift(
							{
								Email: mail,
								Id: 0,
								obSelected: ko.observable(true),
								isNew: true,
								FirstName: '',
								LastName: '',
								show: ko.observable(true)
							});
					}
				});
				// self.obRecipientList(self.obRecipientList());
				emailIsValid();
			}
		}
		newInput.on("keydown", function(e)
		{
			newInputError.hide();
		});
		newInput.on("keyup", function(e)
		{
			if (e.keyCode === $.ui.keyCode.ENTER)
			{
				addNew();
			}
		});
		newInput.focusout(function(e)
		{
			//Safari has 'done' button on the keyboard, it's keycode is not 13.
			addNew();
		});
		newInput.focus();
		newInput.click();
		newInput.trigger("tap");
	};

	SendMergeDocTestEmailViewModel.prototype.deleteRecipientForMobile = function(recipient)
	{
		this.obRecipientList.remove(recipient);
	};

	SendMergeDocTestEmailViewModel.prototype.searchRecipientForMobile = function()
	{
		var self = this;
		var showCount = 0;
		this.obRecipientList().forEach(function(item)
		{
			if (!self.obSearchRecipient())
			{
				showCount++;
				item.show(true);
			}
			else if (item.Email.toLowerCase().indexOf(self.obSearchRecipient().toLowerCase()) >= 0)
			{
				showCount++;
				item.show(true);
			}
			else
			{
				item.show(false);
			}
		});
		this.obRecipientSearchResultIsEmpty(showCount === 0);
		$(".mobile-modal-content-body").scrollTop(0);
	};

	SendMergeDocTestEmailViewModel.prototype.emptySearchRecipient = function()
	{
		this.obSearchRecipient("");
	};

	SendMergeDocTestEmailViewModel.prototype.dispose = function()
	{
		var data = [];
		this.pageLevelViewModel.dispose();
		if (this.documentEntities)
		{
			this.documentEntities().forEach(function(item)
			{
				data.push(item.Guid());
			});
		}
		if (this.intervalID)
			clearInterval(this.intervalID);
		if (this.option.modelType === 'SendTo')
		{
			return this.resetProgress().then(function()
			{
				return tf.promiseAjax["delete"](pathCombine(tf.api.apiPrefix(), "search", this.option.type, "export", "email/delete"),
					{
						data: data
					}, { overlay: false })
					.then(function(response) { }.bind(this));
			}.bind(this));
		}
		else
		{
			return Promise.resolve(true);
		}
		//this._$form.data("bootstrapValidator").destroy();
	};

})();
