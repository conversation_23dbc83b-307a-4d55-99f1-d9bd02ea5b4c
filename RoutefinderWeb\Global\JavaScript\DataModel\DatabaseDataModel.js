﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.DatabaseDataModel = function(schoolEntity)
	{
		namespace.BaseDataModel.call(this, schoolEntity);
	}

	namespace.DatabaseDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.DatabaseDataModel.prototype.constructor = namespace.DatabaseDataModel;

	//not include all fields, it will depends on which field could be used.
	namespace.DatabaseDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "DatabaseName", default: "" },
		{ from: "Location", default: "" },
		{ from: "Subdirectory", default: 0 },
		{ from: "ActiveDate", default: null },
		{ from: "InactiveDate", default: null },
		{ from: "VehicleAutoAssignedBy", default: 0 },
		{ from: "IsAutoArchive", default: "" },
		{ from: "ActivePeriod", default: "" },
		{ from: "Status", default: "" },
		{ from: "Type", default: "" },
		{ from: "DbuserPwd", default: "" },
		{ from: "DbuserID", default: "" },
		{ from: "CopyFromDBID", default: 0 },
		{ from: "CopyFromDBName", default: "" },
		{ from: "CopyFromDBType", default: "" },
		{ from: "InitialCatalogName", default: "" },
		{ from: "DataSourceName", default: "" },
		{ from: "AdminLoginID", default: "" },
		{ from: "AdminPassword", default: "" },
		{ from: "DBType", default: "" },
		{ from: "SaveSQLCredentials", default: false },
		{ from: "Weekly", default: true },
		{ from: "SifautoExport", default: false },
		{ from: "Sifexport", default: false },
		{ from: "Sifimport", default: false }
	];

})();
