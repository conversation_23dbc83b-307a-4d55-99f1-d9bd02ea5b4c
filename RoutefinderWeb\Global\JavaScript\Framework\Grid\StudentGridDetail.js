(function()
{
	createNamespace("TF.Grid.Detail").StudentGridDetail = StudentGridDetail;
	function StudentGridDetail(kendoGrid)
	{
		this.root = kendoGrid;
		this.studentScheduleGridColumns = tf.helpers.kendoGridHelper.getGridColumnsFromDefinitionByType("studentschedule");
		TF.Grid.Detail.BaseGridDetail.call(this);
		this.addHotLinkTimer = null;
		this.afterSubGridCreate = null;
	}

	StudentGridDetail.prototype = Object.create(TF.Grid.Detail.BaseGridDetail.prototype);
	StudentGridDetail.prototype.constructor = TF.Grid.Detail.BaseGridDetail;

	StudentGridDetail.prototype.create = function(student, onFilter, onDataBound, onColumnChange, onColumnResizing, onColumnResized)
	{
		var self = this;
		var template = $("<div><div class='kendo-grid student-schedule'></div></div>");
		this.grids.forEach(function(grid)
		{
			grid.createGrid(student, onFilter, onDataBound, onColumnChange, onColumnResizing, onColumnResized, template)
				.then(function(grid)
				{
					grid.student = student;
					self.loadedGrids.push(grid);
					self.afterSubGridCreate && self.afterSubGridCreate(grid);

				});
		});
		return template;
	};

	StudentGridDetail.prototype._getGrids = function()
	{
		const self = this;

		let studentScheduleVisibility = Number(tf.storageManager.get("studentschedule_visibility"));
		studentScheduleVisibility = isNaN(studentScheduleVisibility) ? true : Boolean(studentScheduleVisibility);

		var studentScheduleGrid = {
			name: "Student Schedule Grid",
			type: "studentschedule",
			showHandler: true,
			isDisabled: !studentScheduleVisibility,
			selectedColumns: self.getSelectedColumnFromStorage("studentschedule", self.studentScheduleGridColumns),
			availableColumns: self.getAvailableColumnFromStorage("studentschedule", self.studentScheduleGridColumns),
			defaultLayoutColumns: self.studentScheduleGridColumns,
			createGrid: function(student, onFilter, onDataBound, onColumnChange, onColumnResizing, onColumnResized, template)
			{
				// create student schedule grid
				var gridType = studentScheduleGrid.type;
				var filterSet = {
					FilterItems: [{
						FieldName: "StudentID",
						Operator: "In",
						Value: student.Id,
						ValueList: "[" + student.Id + "]"
					}],
					LogicalOperator: "and"
				};
				var columns = self.grids.filter(function(c) { return c.type == gridType; })[0].selectedColumns.map(x => $.extend({}, x));

				return self.buildGrid(template.find(".student-schedule"), {
					gridType: gridType,
					columns: columns,
					gridState: {
						filterSet: filterSet
					},
					gridFilter: self.gridFilter,
					displayFilterRow: self.displayFilterRow,
					onFilter: function(filter, filterSet)
					{
						onFilter && onFilter(filter, gridType, function()
						{
							return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", tf.dataTypeHelper.getEndpoint(gridType)),
								{
									data: {
										filterSet: filterSet
									}
								}
							).then(response => response.Items.map(item => item.StudentID));
						});
					},
					onDataBound: function()
					{
						onDataBound && onDataBound(student.Id);
					},
					onColumnChange: function(data)
					{
						if (data)
						{
							data.name = studentScheduleGrid.name;
							data.availableColumns = Enumerable.From(studentScheduleGrid.defaultLayoutColumns.map(x => $.extend({}, x))).Where(function(all) { return !Enumerable.From(data.selectedColumns).Any(function(c) { return c.FieldName == all.FieldName; }); }).ToArray();
							onColumnChange && onColumnChange(data);
						}
					},
					onColumnResizing: function(data)
					{
						onColumnResizing && onColumnResizing(data, student.Id);
					},
					onColumnResized: function(data)
					{
						onColumnResized && onColumnResized(data, student.Id, studentScheduleGrid.type);
					}
				});
			}
		};
		return [studentScheduleGrid];
	};

	StudentGridDetail.prototype.hasData = function(studentIds, option)
	{
		function getStudentSchedules()
		{
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "StudentSchedules"),
				{
					paramData: {
						"@filter": "eq(DBID," + tf.datasourceManager.databaseId + ")&in(StudentID," + studentIds.join(",") + ")",
						"@fields": "StudentID",
						"@ignoreTotalCount": true
					}
				},
				option
			).then(function(response)
			{
				return response.Items.map(function(item) { return item.StudentID; });
			});
		}

		function getStudentNEZ()
		{
			if (studentIds.length > 0)
			{
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "nezStudents"), {
					paramData: {
						"databaseId": tf.datasourceManager.databaseId,
						"StudentIds": studentIds.join(","),
					}
				}, option).then(function(response)
				{
					return response.Items.map(function(item) { return item.StudentId; });
				});
			}
			else
			{
				return Promise.resolve([]);
			}
		}

		return Promise.all([getStudentSchedules(), getStudentNEZ()]).then(function(data)
		{
			return _.concat(data[0], data[1]);
		});
	};
})();
