﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleDeletion = VehicleDeletion;

	function VehicleDeletion()
	{
		this.type = 'vehicle';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleDeletion.prototype.constructor = VehicleDeletion;

	VehicleDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];
		var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "trip", "ids", "tripbyvehicleIds"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'trip',
				items: response.Items[0]
			});
		});
		var p1 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "fieldtripresourcegroup", "fieldtripids", "byvehicleIds"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'fieldtrip',
				items: response.Items[0]
			});
		});
		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
      			"AttachedToType": tf.dataTypeHelper.getId("vehicle")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});

		return Promise.all([p0, p1, p2]).then(function()
		{
			return associatedDatas;
		}.bind(this));
	};


	VehicleDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.all([]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));
	};

	VehicleDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		return Promise.all([p0]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};

})();