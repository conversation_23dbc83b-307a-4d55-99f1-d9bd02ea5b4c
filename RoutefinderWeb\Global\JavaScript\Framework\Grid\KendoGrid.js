(function()
{
	createNamespace("TF.Grid").KendoGrid = KendoGrid;
	var defaults = {
		obDocumentFocusState: true,
		layoutAndFilterOperation: true,
		canDragDelete: true
	};

	const DEFAULT_THEMATIC_LABEL = "All Other Values";
	const THEMATIC_COLOR_ATTRIBUTE = "custom-bkg-color";
	const SCHEDULE_GRIDS = ["studentattendanceschedule", "tripschedule", "tripstopschedule"];
	const THEMATIC_NOT_BIG_GRIDS = ["session", "formsent", "dashboards", "dashboardLibrary", "formLibrary", "scheduledmergedocument", "scheduledreport", "fieldtripinvoice"];

	function addPlugin()
	{
		var base, sub = arguments[0];
		for (var i = 1; i < arguments.length; i++)
		{
			base = Object.create(arguments[i].prototype);
			for (var attr in base)
			{
				sub.prototype[attr] = base[attr];
			}
		}
	}

	function KendoGrid($container, options, gridState, geoFields, lazyLoadScrollContainer)
	{
		var self = this;
		this.isFromRelated = ko.observable(false);
		options = $.extend(true,
			{}, defaults, options);
		this.hasTag = tf.authManager.hasTags() && tf.dataTypeHelper.getDataTypeConfig(options.gridType)?.enableTag;
		TF.Grid.LightKendoGrid.call(this, $container, options, gridState, geoFields, lazyLoadScrollContainer);

		this._columnsLockedTimesKey = "columnsLockedTimes";
		this._columnsUnLockedTimesKey = "columnsUnLockedTimes";
		this.initialFilter = true;
		this._fileDragoverTimeout = null;
		this.isMobileDevice = isMobileDevice();
		this.randomKey = (new Date()).getTime();
		this.obLayoutFilterOperation = ko.observable(options.layoutAndFilterOperation);
		$(window).on("orientationchange.gridStateTwoRowWhenOverflow" + this.randomKey, function()
		{
			setTimeout(function()
			{
				self._setgridStateTwoRowWhenOverflow();
			});
		});
		this.addHotLinkTimer = null;
		this.thematicFields = [];
		this.selectedGridThematicConfigs = null;
		this.tempGridThematicDataModel = null;
		this.predefinedGridData = this.options.predefinedGridData;

		const isThematicSupported = tf.dataTypeHelper.checkGridThematicSupport(this._gridType);
		this.obThematicSupported = ko.observable(isThematicSupported);
		this.obIsThematicApplied = ko.observable(false);

		this.dataFieldHelper = new TF.Map.Thematics.DataFieldHelper();
		this.lazyLoadScrollContainer = lazyLoadScrollContainer;
	}

	KendoGrid.prototype = Object.create(TF.Grid.LightKendoGrid.prototype);

	KendoGrid.constructor = KendoGrid;

	KendoGrid.prototype.HotLinkConfig = {
		altsite: ["Name"],
		contractor: ["Name"],
		district: ["Name"],
		form: ["Name"],
		fieldtrip: ["Name", { targetGrid: "school", fields: ["School", "SchoolName"], }],
		georegion: ["Name"],
		school: ["Name", "School", { targetGrid: "school", fields: ["FeedSchl"] }],
		staff: ["FirstName", "LastName"],
		student: ["FullName", "FirstName", "LastName",
			{ targetGrid: "school", fields: ["SchoolName", "ResSchName", "ResidSchool", "School"], },
			{ targetGrid: "trip", fields: ["FromSchoolTrip1TripName", "FromSchoolTrip2TripName", "FromSchoolTrip3TripName", "FromSchoolTrip4TripName", "ToSchoolTrip1TripName", "ToSchoolTrip2TripName", "ToSchoolTrip3TripName", "ToSchoolTrip4TripName"], },
		],
		tripstop: [
			{ targetGrid: "trip", fields: ["Name"], },
			{ targetGrid: "school", fields: ["Schoolname", "SchlCode"], }
		],
		trip: ["Name",
			{ targetGrid: "school", fields: ["Schools"], },
			{ targetGrid: "staff", fields: ["DriverName", "AideName"] },
			{ targetGrid: "vehicle", fields: ["VehicleName"] },
		],
		vehicle: ["BusNum", "LongName"]
	}

	KendoGrid.prototype.initParameter = function()
	{
		TF.Grid.LightKendoGrid.prototype.initParameter.apply(this, arguments);
		this.isFromRelated(this.options.isFromRelated);
		this._obDocumentFocusState = ko.observable(this.options.obDocumentFocusState);
		this._obSplitmapVisible = ko.observable(false);
		this._obSplitmapVisible.subscribe(this._splitmapVisibleChange, this);

		TF.Grid.KendoGridFilterMenu.call(this);
		TF.Grid.KendoGridThematicMenu.call(this);
		TF.Grid.KendoGridLayoutMenu.call(this);
		TF.Grid.GeoCodingMenu.call(this);
		TF.Grid.KendoOverFlowMenu.call(this);
		TF.Grid.KendoNewGridWithSelectedRecordsMenu.call(this);
		TF.Grid.KendoGridSummaryGrid.call(this);
		this._obSortedItems.subscribe(this._updateCurrentLayout, this);

		this.summarybarIconClick = this.summarybarIconClick.bind(this);
		this.addRemoveColumnClick = this.addRemoveColumnClick.bind(this);
	};

	KendoGrid.prototype.loadAndCreateGrid = function()
	{
		if (this.options.customGridType !== "dashboardwidget" && !this.options.isMiniGrid)
		{
			tf.loadingIndicator.showImmediately();
		}
		//console.log("kendoGrid +1");

		var gridState = {},
			classicSearchFilter = null;
		$.extend(gridState, this._gridState);

		this._obSelectedInvisibleColumns.subscribe(this._invisibleUDFColumnsChange, this);

		this.loadPresetData().then(function()
		{
			if (gridState.requestFrom === "bookmark" && !gridState.filteredIds && !gridState.gridFilterId)
			{
				$.extend(this._gridState, gridState);
				this.obSelectedGridFilterId(null);
			}

			if (tf.isViewfinder)
			{
				if (tf.userPreferenceManager.getUserSetting("shouldRetainGridFilter"))
				{
					if (this._hasStickyFilter())
					{
						this.options.kendoGridOption.autoBind = false;
					}
					classicSearchFilter = tf.storageManager.get(tf.storageManager.gridCurrentClassicSearchFilter(this.options.gridType));
					if (classicSearchFilter)
					{
						this.obClassicFilterSet(classicSearchFilter);
					}
					this.createGrid();
					this._applyStickyFilter();
				} else
				{
					this.createGrid();
				}
			} else
			{
				if (this._hasStickyFilter())
				{
					this.options.kendoGridOption.autoBind = false;
				}
				this.createGrid();
				this._applyStickyFilter();
			}

			this._setCustomizetimePickerborderradius();
			this.resizeHeightOnWindowResize();
			this._obCurrentGridLayoutExtendedDataModel()?.apiIsDirty(this._layoutFilterId != this._obCurrentGridLayoutExtendedDataModel().filterId());
		}.bind(this))
			.then(function()
			{
				this.obSummaryGridVisible.subscribe(this.fitContainer, this);
				this.obSummaryGridVisible.subscribe(this.createSummaryGrid, this);
				this._obSelectedColumns.subscribe(this._updateCurrentLayout, this);
				this._obSelectedColumns.subscribe(this.updateSummaryGridColumns, this);

				if (this._obDocumentFocusState())
				{
					this._obDocumentFocusState.subscribe(this._documentFocusStateChange, this);
				}
				this._dataChangeReceive = this._dataChangeReceive.bind(this);
				this._gridFilterDataModelsChange();
				PubSub.subscribe(topicCombine(pb.DATA_CHANGE, this._gridType), this._dataChangeReceive);

				this._delayHideLoadingIndicator(this._obSplitmapVisible() ? 2000 : null);
				this._gridLoadingEnd = true;
			}.bind(this));
	};

	KendoGrid.prototype._setCustomizetimePickerborderradius = function() //set CustomizetimePicker border-radius.
	{
		var $item, CustomizetimePickers = $(".form-control.datepickerinput");
		CustomizetimePickers.map(function(idx, item)
		{
			$item = $(item);
			$item.css({ "border-radius": "0", "float": "right" });
			$item.next().css("border-radius", "0");
		});
	}

	KendoGrid.prototype._documentFocusStateChange = function()
	{
		if (this._obDocumentFocusState())
		{
			//this._fitContainer(false);
			if (this._pendingRefresh)
			{
				this._pendingRefresh = false;
				this.rebuildGrid().then(function()
				{
					// rebuildGrid is a promise method
				});
			}
		}
	};

	KendoGrid.prototype._dataChangeReceive = function()
	{
		this._pendingRefresh = true;
		this._documentFocusStateChange();
	};

	KendoGrid.prototype.loadPresetData = function()
	{
		let self = this;
		return self.loadGridDefaults()
			.then(function()
			{
				return tf.UDFDefinition.load(self.options.gridType).then(result =>
				{
					if (result)
					{
						self.options.gridDefinition.Columns = self.options.gridDefinition.Columns.concat(tf.UDFDefinition.getAvailableWithCurrentDataSource(self.options.gridType));
						self.options.gridDefinition.InvisibleUDFColumns = tf.UDFDefinition.getInvisibleUDFs(self.options.gridType);
					}
				});
			})
			.then(function()
			{
				return self.loadLayout();
			})
			.then(function()
			{
				if (!self.options.withoutData)
				{
					var promise;
					if (self.options.isMiniGrid)
					{
						promise = Promise.resolve();
					}
					else
					{
						let promiseList = [];
						if (tf.authManager.hasFiltersRights())
						{
							promiseList.push(self.loadGridFilter());
						}
						promiseList.push(self.loadGridThematic(self.options.dataThematic?.Id));
						promiseList.push(self.loadTags());
						promise = Promise.all(promiseList);
					}

					return promise.then(function()
					{
						self._applyingLayout = true;
						self._setGridColumnConfiguration(false);
						self._applyingLayout = false;
						return;
					});
				}
				else
				{
					return Promise.resolve();
				}
			});
	};

	KendoGrid.prototype.loadGridDefaults = function()
	{
		if (this.options.noNeedDefaultLayout || !this.isBigGrid || this._gridType == "gpsevent" || this._gridType === "locationevent")
		{
			return Promise.resolve();
		}
		var gridName = this.options.gridType;
		if (this.options.kendoGridOption && this.options.kendoGridOption.entityType)
		{
			gridName = this.options.kendoGridOption.entityType + "." + gridName;
		}

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "griddefaults?gridName=" + gridName), {}, {
			overlay: this.options.customGridType !== "dashboardwidget"
		}).then(function(apiResponse)
		{
			var columns;
			if (apiResponse.Items.length === 0)
			{
				columns = Enumerable.From(this._gridDefinition.Columns).Where(function(c)
				{
					return c.hidden !== true;
				}).Select(function(c)
				{
					return c.FieldName;
				}).ToArray();
				if (!this.options.kendoGridOption.entityType)
				{ //minigrid will ignore the grid default table values
					return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "griddefaults"),
						{
							data: [{
								Id: 0,
								GridName: this.options.gridType,
								Columns: columns.join(","),
								ApiIsDirty: true,
								ApiIsNew: true
							}]
						});
				}
			}
			else
			{
				columns = apiResponse.Items[0].Columns.split(",");
				this._gridDefinition.Columns.forEach(function(c)
				{
					c.hidden = true;
				});
				columns.reverse().forEach(function(item)
				{
					for (var i = 0, l = this._gridDefinition.Columns.length; i < l; i++)
					{
						if (this._gridDefinition.Columns[i].FieldName == item)
						{
							this._gridDefinition.Columns[i].hidden = false;
							var c = this._gridDefinition.Columns.splice(i, 1);
							this._gridDefinition.Columns.unshift(c[0]);
							break;
						}
					}
				}.bind(this));
			}
		}.bind(this)).catch(function() { });
	};

	KendoGrid.prototype.loadTags = function(force = true)
	{
		if (!this.hasTag)
		{
			return Promise.resolve();
		}

		if (!force && this.loadTagsEvent)
		{
			return this.loadTagsEvent;
		}

		this.loadTagsEvent = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tags?@relationships=TagDataTypes")).then(function(response)
		{
			tf.tags = response.Items;
		});

		return this.loadTagsEvent;
	}

	KendoGrid.prototype.rebuildGrid = function()
	{
		this.isDuringRebuildGridProcess = true;
		return TF.Grid.LightKendoGrid.prototype.rebuildGrid.apply(this, arguments)
			.then(function()
			{
				this.changeSortModel(); //bind the function of chang sort model in colunm mousedown
				this.resizableBinding();
				this.lockUnlockColumn();
				this.initDragHeadEvent();
				this.initQuickFilterBar();
				this.createTooltips();
				this.createDropDocument();
				this.resolveFocus();
				this.isDuringRebuildGridProcess = false;

				return true;
			}.bind(this)).catch(function()
			{
				this.isDuringRebuildGridProcess = false;
			});
	};

	KendoGrid.prototype.refreshUDFs = function(newUDFs, options)
	{
		var self = this,
			appliedLayoutInitState = self.getCurrentLayoutInitState(),
			appliedLayoutInitColumns = appliedLayoutInitState && appliedLayoutInitState.LayoutColumns;

		self.options.gridDefinition.Columns = self.options.gridDefinition.Columns.filter(function(column)
		{
			return !column.UDFId;
		}).concat(newUDFs);
		self.options.gridDefinition.InvisibleUDFColumns = tf.UDFDefinition.getInvisibleUDFs(self._gridType);

		self.refreshGridColumnDefinition();

		/**
		 * self._setGridColumnConfiguration will remove deleted udf columns,
		 * so invoke self.isDeletedUDFsOccupiedByCurrentLayout(options.deletedUDFs) should before self._setGridColumnConfiguration
		 */
		var existingDeletedUDFs = self.isDeletedUDFsOccupiedByCurrentLayout(options.deletedUDFs);
		var invisibleUDFs = self.getInvisibleUDFColumnsForCurrentLayout();
		var existingInvisible2VisibleUDFs = self.isInvisible2VisibileInGridLayout(options.invisible2VisibleUDFs, invisibleUDFs)
		/**
		 * No need to modify self._obCurrentGridLayoutExtendedDataModel when existing deleted udf in current grid layout,
		 * because self._setGridColumnConfiguration will remove invalid columns and sync the lastest columns to UserPreference.
		 *
		 * Only sync to UserPreference, did not sync to gridLayout table.
		 */
		self._setGridColumnConfiguration(self.options.fromSearch, options.invisible2VisibleUDFs);

		if (existingDeletedUDFs
			|| self.isModifiedUDFsOccupiedByCurrentLayout(options.nameChangedUDFs, options.precisionChangedUDFs)
			|| existingInvisible2VisibleUDFs
			|| self.isUDFsOccupiedByCurrentFilter(options.nameChangedUDFs, options.deletedUDFs))
		{
			self.rebuildGrid();
		}

		const selectedLayout = self._obCurrentGridLayoutExtendedDataModel();
		if (existingDeletedUDFs && selectedLayout)
		{
			var selectedLayoutColumns = selectedLayout.layoutColumns();
			var appliedLayoutInitColumnsWithoutDeletedUDFs = appliedLayoutInitColumns.filter(getAppliedLayoutInitColumnsWithoutDeletedUDFsFn);

			if (_.isEqual(selectedLayoutColumns.map(mapFn), appliedLayoutInitColumnsWithoutDeletedUDFs.map(mapFn)))
			{
				// Only deleted udfs, so sync to gridLayout table
				// saveLayoutClick will mark _obCurrentGridLayoutExtendedDataModel as no change
				self.saveLayoutClick();
			}
			else
			{
				//Besides delete udfs, change retained columns' sequence
				self.saveLayoutWhichRemovedDeletedUDFs(appliedLayoutInitColumnsWithoutDeletedUDFs.map(function(item)
				{
					if (!item.UDFId)
					{
						delete item.UDFId;
					}
					return item;
				}));
			}
		}
		else if (existingInvisible2VisibleUDFs && selectedLayout)
		{
			const invisible2VisibleUDFs = options.invisible2VisibleUDFs || [];
			const newSelectedLayoutColumns = selectedLayout.layoutColumns();
			const newAppliedLayoutInitColumnsWithoutDeletedUDFs = appliedLayoutInitColumns.filter(getAppliedLayoutInitColumnsWithoutDeletedUDFsFn).concat(invisible2VisibleUDFs);

			if (_.isEqual(newSelectedLayoutColumns.map(mapFn), newAppliedLayoutInitColumnsWithoutDeletedUDFs.map(mapFn)))
			{
				self.saveLayoutClick();
			}
		}

		tf.documentManagerViewModel.obCurrentDocument()?.detailView?.refreshClick();

		function getAppliedLayoutInitColumnsWithoutDeletedUDFsFn(c)
		{
			var validUDFs = tf.UDFDefinition.getAvailableWithCurrentDataSource(self._gridType);
			return !options.deletedUDFs.some(function(d)
			{
				return d.UDFId === c.UDFId;
			}) && validUDFs.some(function(v)
			{
				return c.UDFId ? v.UDFId === c.UDFId : true;
			});
		}

		function mapFn(item)
		{
			return item.UDFId || item.FieldName;
		}

	};

	KendoGrid.prototype.isInvisible2VisibileInGridLayout = function(invisible2VisibleUDFs, invisibleUDFs)
	{
		return _.intersectionBy(invisibleUDFs, invisible2VisibleUDFs, function(item) { return item.UDFId; }).length > 0;
	};

	KendoGrid.prototype.isModifiedUDFsOccupiedByCurrentLayout = function(nameChangedUDFs, precisionChangedUDFs)
	{
		var self = this,
			currentLayout = self._obCurrentGridLayoutExtendedDataModel();
		return currentLayout
			&& (_.intersectionBy(currentLayout.layoutColumns(), nameChangedUDFs, "UDFId").length > 0
				|| _.intersectionBy(currentLayout.layoutColumns(), precisionChangedUDFs, "UDFId").length > 0);
	};

	KendoGrid.prototype.isDeletedUDFsOccupiedByCurrentLayout = function(deletedUDFs)
	{
		var self = this,
			currentLayout = self._obCurrentGridLayoutExtendedDataModel();
		return currentLayout && _.intersectionBy(currentLayout.layoutColumns(), deletedUDFs, "UDFId").length > 0;
	};

	/**
	 * Contains both modified and deleted udfs
	 */
	KendoGrid.prototype.isUDFsOccupiedByCurrentFilter = function(nameChangedUDFs, deletedUDFs)
	{
		var self = this,
			udfs = nameChangedUDFs.concat(deletedUDFs),
			appliedFilterWhereClause = self.obSelectedGridFilterDataModel() && self.obSelectedGridFilterDataModel().whereClause() || "";
		return (self.obHeaderFilters() && self.obHeaderFilters().some(function(item)
		{
			return udfs.some(function(udf)
			{
				return udf.OriginalName == item.FieldName;
			})
		})) || udfs.some(function(udf)
		{
			return appliedFilterWhereClause.indexOf(String.format("[{0}]", udf.OriginalName)) > -1;
		});
	};

	KendoGrid.prototype.createGrid = function()
	{
		TF.Grid.LightKendoGrid.prototype.createGrid.apply(this);

		this.changeSortModel(); //bind the function of chang sort model in colunm mousedown
		this.resizableBinding();
		this.options.canDragDelete && this.createDragDelete();
		this.lockUnlockColumn();
		this.options.reorderable && this.initDragHeadEvent();
		this.createTooltips();
		this.initQuickFilterBar();
		this.resolveFocus();
		if (!this.options.disableDrop)
		{
			this.createDropDocument();
		}
	};

	KendoGrid.prototype.createDropDocument = function()
	{
		this.$container.off("dragenter").on("dragenter", ".k-grid-content div,.k-grid-content tr,.k-grid-content-locked tr", this.handleFileDragEnter.bind(this));
		this.$container.off("dragleave").on("dragleave", ".k-grid-content div,.k-grid-content tr,.k-grid-content-locked tr", this.handleFileDragLeave.bind(this));
		this.$container.off("dragover").on("dragover", ".k-grid-content div,.k-grid-content tr,.k-grid-content-locked tr", this.handleFileDragOver.bind(this));
		this.$container.off("drop").on("drop", ".k-grid-content div,.k-grid-content tr,.k-grid-content-locked tr", this.handleFileDrop.bind(this));
	}

	KendoGrid.prototype.handleFileDrop = function(e)
	{
		e.stopPropagation();
		e.preventDefault();

		this.switchFileDragoverHighlight();

		if (this.options.onFileDrop)
		{
			//if target is virtual div, replace cur target with real data row DOM.
			var rowData = $(e.target).data();
			if (rowData.id)
			{
				e.currentTarget = this.$container.find(`[data-kendo-uid=${rowData.id}]`);
			}

			this.options.onFileDrop(e);
		}
	}

	/**
	 * Switch the drag-over highlighted row in grid, null if want to unhighlight all.
	 *
	 * @param {JQuery} $tr
	 */
	KendoGrid.prototype.switchFileDragoverHighlight = function($tr)
	{
		clearTimeout(this._fileDragoverTimeout);

		this._fileDragoverTimeout = setTimeout(function()
		{
			var highlightClassName = "k-state-dragover";
			this.$container.find("." + highlightClassName).removeClass(highlightClassName);

			if ($tr)
			{
				var uid = $tr.data().kendoUid || $tr.data().id;
				this.$container.find(`[data-kendo-uid=${uid}],[data-id=${uid}]`).addClass(highlightClassName);
			}

			this._refreshGridBlank();

		}.bind(this), 5);
	};

	KendoGrid.prototype.handleFileDragEnter = function(e)
	{
		e.stopPropagation();
		e.preventDefault();
	};

	KendoGrid.prototype.handleFileDragLeave = function(e)
	{
		e.stopPropagation();
		e.preventDefault();

		this.switchFileDragoverHighlight();
	};

	KendoGrid.prototype.handleFileDragOver = function(e)
	{
		e.stopPropagation();
		e.preventDefault();

		this.switchFileDragoverHighlight($(e.currentTarget));
	};

	KendoGrid.prototype._removeColumn = function(columnDisplayName)
	{
		var columns = this._obSelectedColumns();
		var avaColumns = this._availableColumns;
		for (var idx = 0; idx < columns.length; idx++)
		{
			if (columns[idx].DisplayName === columnDisplayName || (columns[idx].OriginalName != null && columns[idx].OriginalName === columnDisplayName))
			{
				var subColumn = columns.filter(column => column.ParentField === columns[idx].FieldName);
				avaColumns.push(columns[idx]);
				this.clearCustomFilterByFieldName(columns[idx].FieldName);
				columns.splice(idx, 1);
				if (subColumn && subColumn.length > 0)
				{
					avaColumns.push(subColumn[0]);
					this.clearCustomFilterByFieldName(subColumn[0].FieldName);
					subColumn.forEach(function(sColumn)
					{
						columns.splice(columns.indexOf(sColumn), 1);
					});
				}
				break;
			}
		}

		this._obSelectedColumns(columns);
		this._availableColumns = avaColumns;
	};

	//view-1301 Grid will freeze up if last remaining unlocked column is removed from grid
	KendoGrid.prototype._removingOnlyOneUnLockColumn = function(columnDisplayName)
	{
		var that = this;
		var lockedColumn = Enumerable.From(that._obSelectedColumns()).Where("$.locked").ToArray();
		if (lockedColumn.length + 1 === that._obSelectedColumns().length)
		{
			tf.promiseBootbox.confirm(
				{
					message: "All visible columns to the left of this column are locked. Removing this column will unlock those columns. Are you sure you want to remove this column?",
					title: "Remove Confirmation"
				}).then(function(ans)
				{
					if (ans === true)
					{
						that._removeColumn(columnDisplayName);
						that.tobeLockedColumns = [];
						that.rebuildGrid();
					}
				});
			return true;
		}
		return false;
	};

	KendoGrid.prototype.getKendoSortColumn = function()
	{
		if (Array.isArray(this.options.defaultSort) && this.options.defaultSort.length > 0)
		{
			return this.options.defaultSort;
		}

		const currentLayout = this._obCurrentGridLayoutExtendedDataModel();
		if (currentLayout && currentLayout.layoutColumns())
		{
			var list = Enumerable.From(currentLayout.layoutColumns()).Where(function(c)
			{
				return c.SortIndex != undefined;
			}).OrderBy(function(c)
			{
				return c.SortIndex;
			}).ToArray();

			return list.map((item) => (
				{
					field: item.UDFId ? tf.UDFDefinition.getFieldNameById(item.UDFId) : item.FieldName,
					dir: item.SortAscending ? "asc" : "desc"
				}
			));
		}

		return [];
	};

	KendoGrid.prototype.resolveFocus = function()
	{
		let focusToSummaryBar = () =>
		{
			let summary = this.$container?.next(".kendo-summarygrid-container").find('.k-grid-content.k-auto-scrollable table');
			if (summary.length && summary.is(':visible'))
			{
				summary.attr('tabindex', '0').focus();
				return true;
			}

			return false;
		};

		this.kendoGrid?.element.on('click focus', e =>
		{
			let target = $(e.target);
			if (target.hasClass('k-filtercell') || target.closest('.k-filtercell').length) return;

			if (this.options.isMiniGrid) return;

			if (focusToSummaryBar()) return;

			this.kendoGrid.table.focus();
		});

		this.kendoGrid?.table.on('click', e =>
		{
			focusToSummaryBar();
		});

		this.$container?.next(".kendo-summarygrid-container").on('click focus', e =>
		{
			let target = $(e.target);
			if (target.hasClass('k-filtercell') || target.closest('.k-filtercell').length) return;
			focusToSummaryBar();
		});
	};

	KendoGrid.prototype.initDragHeadEvent = function()
	{
		var self = this;
		var dragable = this.$container.data().kendoDraggable;
		dragable.setOptions(
			{
				container: $("body")
			});
		var dragIntervalEvent;
		dragable.bind('dragstart', function(e)
		{
			if (e.currentTarget.data("kendo-field") === "bulk_menu")
			{
				e.preventDefault();
				return;
			}
			var fields = self.kendoGrid.columns.filter(function(item)
			{
				return item.FieldName === e.currentTarget.data("kendo-field")
			});
			if (fields && fields.length && fields[0].ParentField)
			{
				e.preventDefault();
				return;
			}
			//$("body").css("overflow", "hidden");
			if (self.tobeLockedColumns.length == 0 || (self.tobeLockedColumns[0].field === 'bulk_menu'))
			{
				self.kendoGrid.columns.forEach(function(item)
				{
					item.lockable = false;
				});
			}
			dragIntervalEvent = setInterval(function()
			{
				var scrollleft = this.$container.find('.k-grid-header-wrap.k-auto-scrollable').scrollLeft();
				var gridContent = this.$container.find(".k-virtual-scrollable-wrap").length > 0 ? this.$container.find(".k-virtual-scrollable-wrap") : this.$container.find(".k-grid-content");
				gridContent.scrollLeft(scrollleft);
				if (this.$summaryContainer)
				{
					var $summaryGrid = this.$summaryContainer.find(".k-grid-content");
					$summaryGrid.scrollLeft(scrollleft);
				}
			}.bind(this), 10);
		}.bind(this)).bind('dragend', function()
		{
			//$("body").css("overflow", "visible");
			clearInterval(dragIntervalEvent);
			self.tobeLockedColumns = [];
			self.kendoGrid.columns.forEach(function(item)
			{

				item.lockable = true;
				if (item.locked)
				{
					self.tobeLockedColumns.push(item);
				}
			});
			if (self.obSummaryGridVisible())
			{
				self.fitContainer();
			}
		});
	};

	KendoGrid.prototype.columnReorderEvent = function(e)
	{
		var grid = this.kendoGrid;

		var childColumn = grid.columns.filter(function(column)
		{
			return column.ParentField === e.column.FieldName;
		})
		if (childColumn && childColumn.length > 0)
		{
			setTimeout(function()
			{
				grid.reorderColumn(e.newIndex, grid.columns[e.oldIndex], undefined, true);
				if (grid.columns.indexOf(e.column) < grid.columns.indexOf(childColumn[0]))
				{
					grid.reorderColumn(grid.columns.indexOf(e.column) + 1, grid.columns[grid.columns.indexOf(childColumn[0])], undefined, true);
				}
				else
				{
					grid.reorderColumn(grid.columns.indexOf(e.column), grid.columns[grid.columns.indexOf(childColumn[0])], undefined, true);
				}
			}, 0);
		}

		if (typeof e.column.reorderable != "undefined" && !e.column.reorderable)
		{
			setTimeout(function()
			{
				grid.reorderColumn(e.oldIndex, grid.columns[e.newIndex]);
			}, 0);
			return;
		}

		if (e.oldIndex === 0 && this.options.showLockedColumn)
		{
			setTimeout(function()
			{
				grid.reorderColumn(e.oldIndex, grid.columns[e.newIndex]);
			});
		}
		else
		{
			//use timeout because of columnReorder event fire before real reorded
			setTimeout(function()
			{
				this.saveState();
				this._clearInvisibleUDFColumns(true);
			}.bind(this));
		}
	};

	KendoGrid.prototype.columnHideEvent = function(e)
	{
		this.saveState();
	};

	KendoGrid.prototype.columnShowEvent = function(e)
	{
		this.saveState();
	};

	KendoGrid.prototype.setSplitmapMode = function(value)
	{
		this._obSplitmapVisible(value);

		if (this._obSplitmapVisible())
			this._showCannotSupportSelectAllModal();

		if (this.options.showEyeColumn)
		{
			this.options.eyeColumnVisible = value;
			if (value)
			{
				this.kendoGrid.showColumn(1);
			}
			else
			{
				this.kendoGrid.hideColumn(1);
			}
		}
	};

	KendoGrid.prototype._splitmapVisibleChange = function()
	{
		var self = this;
		setTimeout(function()
		{
			self.resetGridContainerHorizontalLayout();
		}, 0);
	};

	KendoGrid.prototype.addRemoveColumnClick = function(model, e)
	{
		var self = this;

		function changeColumn(editColumnViewModel)
		{
			//dedupe
			self._availableColumns = self._availableColumns.reduce((prev, current) => prev.some(x => x.FieldName === current.FieldName) ? prev : prev.concat(current), []);
			var allChildColumns = self._availableColumns.filter(column => !!column.ParentField).concat(self._obSelectedColumns().filter(column => !!column.ParentField));
			//dedupe
			allChildColumns = allChildColumns.reduce((prev, current) => prev.some(x => x.FieldName === current.FieldName) ? prev : prev.concat(current), []);

			if (allChildColumns && allChildColumns.length > 0)
			{
				allChildColumns.forEach(function(childColumn, index)
				{
					if ($.inArray(childColumn, editColumnViewModel.selectedColumns) < 0)
					{
						var parentColumn = editColumnViewModel.selectedColumns.filter(function(column)
						{
							return column.FieldName === childColumn.ParentField;
						});
						if (parentColumn && parentColumn.length > 0)
						{
							editColumnViewModel.selectedColumns.splice(editColumnViewModel.selectedColumns.indexOf(parentColumn[0]) + 1, 0, childColumn);
						}
					}
				});

				editColumnViewModel.availableColumns = editColumnViewModel.availableColumns.concat(allChildColumns);

			}

			self._obSelectedColumns(editColumnViewModel.selectedColumns);
			self._availableColumns = editColumnViewModel.availableColumns;
			self.removeHiddenColumnQuickFilter(self._availableColumns);
			self.removeHiddenColumnSummaryFunction(editColumnViewModel.selectedColumns); //Similar to the quick filters, when a column is hidden the summary function associated with it should be cleared.
			self.tobeLockedColumns = self._obSelectedColumns().filter((item) => item.locked === true);

			if (self.options.gridType !== "form")
			{ // not save forms grid columns
				self._clearInvisibleUDFColumns(true);
			}

			return self.rebuildGrid().then(function()
			{
				// rebuildGrid is a promise method
			});
		}

		// For phone device.
		if (TF.isPhoneDevice)
		{
			var cacheOperatorBeforeHiddenMenu = TF.menuHelper.needHiddenOpenedMenu(e);
			var cacheOperatorBeforeOpenMenu = TF.menuHelper.needOpenCurrentMenu(e);

			if (cacheOperatorBeforeHiddenMenu)
			{
				TF.menuHelper.hiddenMenu();
			}

			if (cacheOperatorBeforeOpenMenu)
			{
				var gridOptions = [{
					name: self.options.gridType + ' grid',
					availableColumns: self._availableColumns,
					selectedColumns: self._obSelectedColumns(),
					defaultLayoutColumns: self._defaultGridLayoutExtendedEntity.LayoutColumns,
					successCallback: changeColumn
				}];
				if (self.lightKendoGridDetail && self.lightKendoGridDetail.getSubGrids())
				{
					var subGrids = self.lightKendoGridDetail.getSubGrids(),
						func = function(vm)
						{
							self.lightKendoGridDetail.changeSubGridColumns([vm]);
						};
					subGrids.forEach(function(subGrid)
					{
						subGrid.successCallback = func;
					});
					gridOptions = gridOptions.concat(subGrids);
				}
				tf.contextMenuManager.showMenu(e.target, new TF.ContextMenu.TemplateContextMenu("workspace/grid/EditKendoColumnForMobile",
					new TF.Modal.Grid.EditKendoColumnModalViewModelForMobile(gridOptions)));
			}
		}
		// For detail (sub) grid.
		else if (self.lightKendoGridDetail && self.lightKendoGridDetail.getSubGrids() && !self.lightKendoGridDetail.detail.disableChangeColumn)
		{
			var gridsOption = [
				{
					availableColumns: self._availableColumns,
					selectedColumns: self._obSelectedColumns(),
					defaultLayoutColumns: self._defaultGridLayoutExtendedEntity.LayoutColumns
				}].concat(self.lightKendoGridDetail.getSubGrids());

			tf.modalManager.showModal(new TF.Modal.Grid.MultipleEditKendoColumnModalViewModel(gridsOption))
				.then(function([rootGrid, ...subGrids])
				{
					var mapFunc = (item) => item.FieldName,
						isColumnChanged = rootGrid && gridsOption[0].selectedColumns.map(mapFunc).toString() != rootGrid.selectedColumns.map(mapFunc).toString();

					if (subGrids.length)
					{
						let p = isColumnChanged ? changeColumn(rootGrid) : Promise.resolve();
						p.then(function()
						{
							self.lightKendoGridDetail.changeSubGridColumns(subGrids, isColumnChanged);
						});
					}
				})
				.catch(console.error);
		}
		// For general grid.
		else
		{
			tf.modalManager.showModal(new TF.Modal.Grid.EditKendoColumnModalViewModel(
				self._availableColumns,
				self._obSelectedColumns(),
				self._defaultGridLayoutExtendedEntity.LayoutColumns
			)).then(function(editColumnViewModel)
			{
				if (!editColumnViewModel)
				{
					return;
				}
				changeColumn(editColumnViewModel);
			});
		}
	};

	KendoGrid.prototype.raiseGridStateChange = function()
	{
		if (this.options.onGridStateChange)
		{
			this.options.onGridStateChange.notify();
		}
	};

	KendoGrid.prototype.exportCurrentGrid = function(selectedIds)
	{
		var self = this;

		var url = tf.dataTypeHelper.getExportFileEndpoint(this.options.gridType);
		this.getIdsWithCurrentFiltering()
			.then(function(ids)
			{
				tf.promiseBootbox.dialog(
					{
						closeButton: true,
						title: "Save As",
						message: "Select the file format that you would like to save the selected records in." +
							"<div class='col-xs-24'>" +
							"<br/><label class='remove-col-default-left-padding-15'>Type</label>" +
							"<div class='save-content'>" +
							"<input id='csvradio' type='radio' checked='checked' name='type' value='csv' />" +
							"<label for='csvradio'>Comma Separated Value (.csv)</label>" +
							"<br/><input id='xlsradio' type='radio' name='type' value='xlsx' />" +
							"<label for='xlsradio'>Excel Workbook (.xlsx)</label>" +
							"<div>" +
							"</div>",
						buttons:
						{
							save:
							{
								label: "Save",
								className: "btn tf-btn-black btn-sm",
								callback: function()
								{
									var fileFormat = $("#csvradio").is(':checked') ? 'csv' : 'xlsx';

									var gridLayoutExtendedEntity = self._obCurrentGridLayoutExtendedDataModel().toData();
									gridLayoutExtendedEntity.LayoutColumns = self._obSelectedColumns();

									if (self.options.gridType === "form" && self.options.exportColumns)
									{
										gridLayoutExtendedEntity.LayoutColumns = self.options.exportColumns;
									}

									var getDataUrl = url + '/';
									var getDataOption = {
										paramData:
										{
											fileFormat: fileFormat
										},
										data: {
											"gridLayoutExtendedEntity": gridLayoutExtendedEntity,
											"selectedIds": selectedIds ? selectedIds : ids,
											"sortItems": self.searchOption.data.sortItems
										}
									};

									if (self.options.gridType === "busfinderhistorical" ||
										self.options.gridType === "form")
									{
										self.options.setRequestOption(getDataOption);
									}

									tf.promiseAjax.post(getDataUrl, getDataOption).then(function(keyApiResponse)
									{
										const exportFileName = (self.options.gridType === "form" ? self.options.gridData.text : self.options.gridType);
										var fileUrl = `${url}?key=${keyApiResponse.Items[0]}&fileFormat=${fileFormat}&fileName=${encodeURIComponent(exportFileName)}`;
										window.open(fileUrl);
									})
								}
							},
							cancel:
							{
								label: "Cancel",
								className: "btn btn-link btn-sm"
							}
						}
					})
					.then(function(operation)
					{
					});
			});
	};

	KendoGrid.prototype.exportCurrentGridInMobile = function(selectedIds)
	{
		this.exportGridModalViewModel = new TF.Modal.ExportGridModalViewModel(selectedIds, this);
		tf.modalManager.showModal(this.exportGridModalViewModel);
	};

	KendoGrid.prototype.onShiftIPress = function(e, keyCombination)
	{
		if (this._obDocumentFocusState())
		{
			this.invertSelection();
		}
	};

	KendoGrid.prototype.onShiftOPress = function(e, keyCombination)
	{
		if (this._obDocumentFocusState())
		{
			this.omitSelection();
		}
	};

	KendoGrid.prototype.onShiftAPress = function(e, keyCombination)
	{
		if (this._obDocumentFocusState())
		{
			this.allSelection();
		}
	};

	KendoGrid.prototype.onDataBound = function()
	{
		if (!this.kendoGrid)
		{ //only for setdatasource directly
			this.kendoGrid = this.$container.data("kendoGrid");
		}

		const hasLocked = this._hasLockedColumns();
		this.toggleGridColumnLockStatus(hasLocked, false);

		TF.Grid.LightKendoGrid.prototype.onDataBound.apply(this);

		var $container = this.$container;
		var $lockedContent = $container.find(".k-grid-content-locked");
		if ($lockedContent &&
			this.obSummaryGridVisible && this.obSummaryGridVisible() && this.overlay !== false)
		{
			this.options.customGridType !== "dashboardwidget" && !this.options.isMiniGrid && tf.loadingIndicator.showImmediately();
			this.createSummaryGrid();
			this._delayHideLoadingIndicator();

			$lockedContent.height($lockedContent.next().height());
		}

		if (!tf.isViewfinder)
		{
			if (this.addHotLinkTimer)
			{
				clearTimeout(this.addHotLinkTimer);
			}
			this.addHotLinkTimer = setTimeout(() =>
			{
				this.addHotLinkTimer = null;
				this.addHotLink();
			}, 200);
		}

		if (this.isBigGrid || SCHEDULE_GRIDS.concat(THEMATIC_NOT_BIG_GRIDS).includes(this._gridType))
		{
			if (this.predefinedGridData)
			{
				this.predefinedGridData = null;
			}
			if (!this.selectedGridThematicConfigs)
			{
				this.selectedGridThematicConfigs = this.getSelectedGridThematicConigs();
			}

			if (this.lazyloadFields.udf.length === 0)
			{
				this.applyGridThematicConfigs();
			}
		}
	};

	KendoGrid.prototype.addHotLink = function()
	{
		let self = this;
		let hotLinkConfig = this.HotLinkConfig;

		// dashboard grid not need the hotlink
		if (self.options.customGridType === "dashboardwidget")
		{
			return;
		}

		if (self.options.gridType === "form" &&
			!tf.authManager.isAuthorizedForDataType(tf.dataTypeHelper.getKeyById(self.options.dataType), "read"))
		{
			hotLinkConfig.form = [];
		}

		// console.log(hotLinkConfig, self.options.gridType)

		if (hotLinkConfig[self.options.gridType])
		{
			const $table = (self.kendoGrid && self.kendoGrid.table) || self.$container.find("div[class^='k-grid-content'] table");

			hotLinkConfig[self.options.gridType].reduce((acc, item) =>
			{
				if (typeof item === "object")
				{
					return acc.concat(item)
				}

				var matched = acc.find(x => x.targetGrid === self.options.gridType);
				if (!matched)
				{
					return acc.concat({ targetGrid: self.options.gridType, fields: [item] })
				}

				matched.fields.push(item);
				return acc;
			}, []).forEach(item =>
			{
				Array.from($table.find(">tbody>tr.k-master-row")).forEach(tr =>
				{
					item.fields.forEach(field =>
					{
						var matchedTd = $(tr).find(`td[data-kendo-field="${field}"]`)
						if (matchedTd.length && !matchedTd.find("span").length)
						{
							if (self.options.gridType === "trip" && field === "Schools")
							{
								const schoolCodes = matchedTd.text().split(",").map(x => x.trim()).filter(Boolean);
								matchedTd.html(schoolCodes.map(code =>
								{
									return '<span class="hot-link">' + code + '</span>';
								}).join(",&nbsp;"));
							}
							else
							{
								let hotlinkHtml = $('<span class="hot-link"></span>');
								hotlinkHtml.text(matchedTd.text());
								matchedTd.html(hotlinkHtml);
							}
							matchedTd.find("span").attr('title', 'Press "Alt" and click to open link');
							const eventHandler = function(e)
							{
								e.preventDefault();
								e.stopPropagation();

								const record = self.kendoGrid.dataItem(e.currentTarget.closest("tr"));
								let currentDocument = null;
								if (!tf.isViewfinder)
								{
									currentDocument = tf.documentManagerViewModel.obCurrentDocument();
									currentDocument.detailViewType = item.targetGrid;
								}

								const fieldName = $(e.currentTarget).closest("td").attr("data-kendo-field");

								var p;

								if (self.options.gridType === item.targetGrid && !["FeedSchl"].includes(fieldName))
								{
									if (self.options.gridType === "form")
									{
										if (record.GroupGUID)
										{
											p = tf.udgHelper.getRecordIdsByGroupGUIDs([record.GroupGUID], null, self.options.dataType);
										}
										else
										{
											p = Promise.resolve(record.RecordID);
										}
									}
									else
									{
										p = Promise.resolve(record.Id);
									}
								}
								else
								{
									p = new Promise(function(resolve)
									{
										function getSchoolIdByCode(code)
										{
											return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"),
												{ paramData: { "@filter": `eq(schoolcode, ${code})`, } }
											).then(response =>
											{
												return response && response.Items && response.Items[0] && response.Items[0].Id;
											});
										}

										function getTripIdByName(name)
										{
											return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "trips"),
												{ paramData: { "@filter": `eq(Name, ${name})`, } }
											).then(response =>
											{
												return response && response.Items && response.Items[0] && response.Items[0].Id;
											});
										}

										switch (self.options.gridType)
										{
											case "fieldtrip":
												getSchoolIdByCode(record.School).then(resolve);
												break;
											case "tripstop":
												switch (item.targetGrid)
												{
													case "trip":
														getTripIdByName(record.Name).then(resolve);
														break;
													case "school":
														getSchoolIdByCode(record.SchlCode).then(resolve);
														break;
												}
												break;
											case "trip":
												switch (item.targetGrid)
												{
													case "school":
														const schoolCode = $(e.currentTarget).text();
														getSchoolIdByCode(schoolCode).then(resolve);
														break;
													case "staff":
														resolve(fieldName === "AideName" ? record.AideId : record.DriverId);
														break;
													case "vehicle":
														resolve(record.VehicleId)
														break;
												}
												break;
											case "student":
												switch (fieldName)
												{
													case "SchoolName":
													case "School":
														getSchoolIdByCode(record.School).then(resolve);
														break;
													case "ResidSchool":
													case "ResSchName":
														getSchoolIdByCode(record.ResidSchool).then(resolve);
														break;
													case "FromSchoolTrip1TripName":
														getTripIdByName(record.FromSchoolTrip1TripName).then(resolve);
														break;
													case "FromSchoolTrip2TripName":
														getTripIdByName(record.FromSchoolTrip2TripName).then(resolve);
														break;
													case "FromSchoolTrip3TripName":
														getTripIdByName(record.FromSchoolTrip3TripName).then(resolve);
														break;
													case "FromSchoolTrip4TripName":
														getTripIdByName(record.FromSchoolTrip4TripName).then(resolve);
														break;
													case "ToSchoolTrip1TripName":
														getTripIdByName(record.ToSchoolTrip1TripName).then(resolve);
														break;
													case "ToSchoolTrip2TripName":
														getTripIdByName(record.ToSchoolTrip2TripName).then(resolve);
														break;
													case "ToSchoolTrip3TripName":
														getTripIdByName(record.ToSchoolTrip3TripName).then(resolve);
														break;
													case "ToSchoolTrip4TripName":
														getTripIdByName(record.ToSchoolTrip4TripName).then(resolve);
														break;
												}
												break;
											case "school":
												getSchoolIdByCode(record.FeedSchl).then(resolve);
												break;
											default:
												throw new Error("No Implement.");
										}
									});
								}

								p.then((recordId) =>
								{
									if (self.options.gridType === "form")
									{
										var documentData = new TF.Document.DocumentData(TF.Document.DocumentData.Grid,
											{
												gridType: tf.dataTypeHelper.getKeyById(self.options.dataType),
												isTemporaryFilter: true,
												gridState: {
													gridFilterId: null,
													filteredIds: Array.isArray(recordId) ? recordId : [recordId]
												}
											});
										documentData.data.tabName = tf.dataTypeHelper.getNameById(self.options.dataType);
										if (e.altKey)
										{
											tf.documentManagerViewModel.add(documentData, false, true);
										}
										else
										{
											let dataItem = self.kendoGrid.dataItem(tr);
											self._onGridItemClick(dataItem, e)
										}

									} else
									{
										if (e.altKey)
										{
											if (tf.isViewfinder)
											{
												self.onAltPressClick.notify({ recordId: recordId, type: item.targetGrid });
											} else
											{
												currentDocument.switchDetailViewByHotLink(recordId, item.targetGrid);
											}
										}
										else
										{
											let dataItem = self.kendoGrid.dataItem(tr);
											self._onGridItemClick(dataItem, e)
										}
									}

								});
							};

							Array.from(matchedTd[0].querySelectorAll(".hot-link")).forEach(hotlink =>
							{
								hotlink.addEventListener("mousedown", eventHandler, { capture: true });
								hotlink.addEventListener("click", function(e)
								{
									e.preventDefault();
									e.stopPropagation();
								}, { capture: true });
								hotlink.addEventListener("mouseup", function(e)
								{
									e.preventDefault();
									e.stopPropagation();
								}, { capture: true });
							});
						}
					})
				});
			})
		}
	};

	KendoGrid.prototype._hasStickyFilter = function()
	{
		var filterData = this.getQuickFilter().data;
		if ((filterData.filterSet && filterData.filterSet.FilterItems && filterData.filterSet.FilterItems.length > 0)
			|| (filterData.filterSet && filterData.filterSet.FilterSets && filterData.filterSet.FilterSets.length > 0)
			|| (filterData.idFilter && ((filterData.idFilter.ExcludeAny && filterData.idFilter.ExcludeAny.length > 0) || filterData.idFilter.IncludeOnly)))
		{
			return true;
		}
		return false;
	};

	KendoGrid.prototype._applyStickyFilter = function()
	{
		if (this.initialFilter)
		{
			this.initialFilter = false;

			var filterData = this.getQuickFilter().data;

			if (filterData.callOutFilterName)
			{
				this.obCallOutFilterName(filterData.callOutFilterName);
			}

			if (filterData.idFilter && filterData.idFilter.ExcludeAny && filterData.idFilter.ExcludeAny.length > 0)
			{
				var excludeIds = filterData.idFilter.ExcludeAny;
				this.obTempOmitExcludeAnyIds(excludeIds);
			}

			if (filterData.idFilter && filterData.idFilter.IncludeOnly)
			{
				var includeIds = filterData.idFilter.IncludeOnly;
				this._gridState.filteredIds = includeIds;
			}

			if ((filterData.filterSet && filterData.filterSet.FilterItems && filterData.filterSet.FilterItems.length > 0) ||
				(filterData.filterSet && filterData.filterSet.FilterSets && filterData.filterSet.FilterSets.length > 0))
			{
				var kendoFilterItems = this.convertRequest2KendoFilterSet(filterData.filterSet);

				// sticky filter read, then set the filter icon after the element render, this settimeout is wait for the element render.
				this.kendoGrid.dataSource.filter(kendoFilterItems);
				setTimeout(function()
				{
					this.kendoGridFilterHelper.setColumnCurrentFilterIcon(this.$container, this.kendoGrid);
					this.setColumnCurrentFilterInput();
				}.bind(this), 50);
				this.setFilterIconByKendoDSFilter();
			}
			else if (filterData.idFilter && ((filterData.idFilter.ExcludeAny && filterData.idFilter.ExcludeAny.length > 0) || filterData.idFilter.IncludeOnly))
			{
				this.kendoGrid.dataSource.read();
			}
		}
	};

	KendoGrid.prototype._delayHideLoadingIndicator = function(delayTime)
	{
		delayTime = delayTime || 200;
		setTimeout(function()
		{
			tf.loadingIndicator.tryHide();
			//console.log("kendoGrid -1");
		}, delayTime);
	};

	KendoGrid.prototype.summarybarIconClick = function()
	{
		this.toggleSummaryBar();
		tf.gaHelper.send('Area', 'Summary Bar');
		setTimeout(function()
		{
			this.saveState();
		}.bind(this));
	};

	KendoGrid.prototype.refreshClick = function()
	{
		tf.loadingIndicator.showImmediately();
		this.lightKendoGridDetail && this.lightKendoGridDetail.refresh();
		this.getThematicList();
		var self = this,
			stickGridConfig = {
				isLayoutApplied: self.obLayoutFilterOperation() && self.obSelectedGridLayoutName(),
				isFilterApplied: self.obLayoutFilterOperation() && self.obSelectedGridFilterName() !== 'None',
				stickLayoutModel: self._obCurrentGridLayoutExtendedDataModel(),
				stickLayoutAssociateFilter: null,
				stickFilterModel: self.obSelectedGridFilterDataModel()
			},
			unsyncedDBLayoutTmp = null;

		if (self.obGridFilterDataModels() && self.obGridFilterDataModels().length > 0)
		{
			var stickLayoutAssociateFilterId = stickGridConfig.stickLayoutModel._entityBackup.FilterId;
			self.obGridFilterDataModels().map(function(filter)
			{
				if (filter.id() === stickLayoutAssociateFilterId)
					stickGridConfig.stickLayoutAssociateFilter = filter;
			});
		}

		self._syncGridConfigExceptAppliedLayoutAndPromptDeletedFilter(stickGridConfig)
			.then(function(unsyncedDBLayout)
			{
				unsyncedDBLayoutTmp = unsyncedDBLayout;
				return self._getStickGridConfigModifiedStatus(stickGridConfig, unsyncedDBLayoutTmp);
			})
			.then(function(modifiedStatus)
			{
				return self._refreshGridByConfigModifiedStatus(modifiedStatus, stickGridConfig, unsyncedDBLayoutTmp);
			}).then(function()
			{
				setTimeout(function()
				{
					tf.loadingIndicator.tryHide();
				}, 1500);
			});
	};

	KendoGrid.prototype._setGridState = function()
	{
		const args = arguments;
		const preWorkList = [this.loadGridFilter()];

		if (this.obThematicSupported())
		{
			const currentLayout = this._obCurrentGridLayoutExtendedDataModel();
			preWorkList.push(this.loadGridThematic(currentLayout.thematicId()));
		}

		return Promise.all(preWorkList).then(() =>
		{
			TF.Grid.LightKendoGrid.prototype._setGridState.apply(this, args);
		});
	};

	KendoGrid.prototype._syncGridConfigExceptAppliedLayoutAndPromptDeletedFilter = function(stickGridConfig)
	{
		var self = this;
		return Promise.all([self.loadGridFilter(), self.loadTags()])// sync filter and prompt deleted filter
			.then(function()
			{
				return self._syncLayoutExceptApplied.bind(self)(stickGridConfig);
			});
	};

	// excpted applied layout because set knockout object obGridLayoutExtendedDataModels will trigger function 'resetLayout' and clear current layout
	KendoGrid.prototype._syncLayoutExceptApplied = function(stickGridConfig)
	{
		var unsyncedDBLayout = null,
			self = this,
			dataTypeId = tf.dataTypeHelper.getId(self.options.gridType)
		if (dataTypeId == null)
		{
			return Promise.resolve();
		}

		const paramData = {
			"DataTypeID": dataTypeId,
			"@sort": "Name"
		};
		if (self.options.gridData)
		{
			paramData["UDGridId"] = self.options.gridData.value;
		}
		paramData["@relationships"] = "AutoExport,Dashboard";
		const settings = { "paramData": paramData };

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridlayouts"), settings)
			.then(function(apiResponse)
			{
				var gridLayoutExtendedDataModels =
					TF.DataModel.BaseDataModel.create(TF.DataModel.GridLayoutExtendedDataModel, apiResponse.Items);

				/////////////////////////////////
				// updated layout except applied
				var stickLayoutId = stickGridConfig.stickLayoutModel ? stickGridConfig.stickLayoutModel.id() : null;
				let layout = gridLayoutExtendedDataModels.find(x => x.id() == stickLayoutId);
				if (layout != undefined)
				{
					stickGridConfig.stickLayoutModel.autoExportExists(layout.autoExportExists());
					stickGridConfig.stickLayoutModel.autoExports(layout.autoExports());
					stickGridConfig.stickLayoutModel.dashboardExists(layout.dashboardExists());
					stickGridConfig.stickLayoutModel.dashboards(layout.dashboards());
					unsyncedDBLayout = layout;
				}
				//////////////////////////////////

				return Promise.resolve(unsyncedDBLayout);
			});
	};

	KendoGrid.prototype._getStickGridConfigModifiedStatus = function(stickGridConfig, unsyncedDBLayout)
	{
		var self = this;
		var modifiedStatus = {
			isDeletedLayout: false,
			isUpdatedLayout: false,
			isUpdatedLayoutOnlyFilterWhereCluase: false,
			isDeletedFilter: false,
			isUpdatedFilter: false
		};

		if (stickGridConfig.isLayoutApplied)
		{
			// Compare current layout and newLayouts
			if (!unsyncedDBLayout)
				modifiedStatus.isDeletedLayout = true;
			else
			{
				var stickBackUpLayoutColumns = stickGridConfig.stickLayoutModel._entityBackup.LayoutColumns;
				var stickBackUpLayoutName = stickGridConfig.stickLayoutModel._entityBackup.Name;
				var stickBackUpLayoutShowSummaryBar = stickGridConfig.stickLayoutModel._entityBackup.ShowSummaryBar;

				if (typeof stickBackUpLayoutColumns === "string")
				{
					try
					{
						stickBackUpLayoutColumns = JSON.parse(stickBackUpLayoutColumns);
					}
					catch (error)
					{
						stickBackUpLayoutColumns = [];
						console.log(error);
					}
				}

				modifiedStatus.isUpdatedLayout = (!TF.Grid.LayoutHelper.compareLayoutColumns(stickBackUpLayoutColumns, unsyncedDBLayout.layoutColumns()) ||
					stickBackUpLayoutName !== unsyncedDBLayout.name() ||
					stickBackUpLayoutShowSummaryBar !== unsyncedDBLayout.showSummaryBar()
				);

				var stickBackupFilterId = stickGridConfig.stickLayoutModel._entityBackup.FilterId;
				if (!modifiedStatus.isUpdatedLayout)
					modifiedStatus.isUpdatedLayout = (stickBackupFilterId !== unsyncedDBLayout.filterId());

				if (!modifiedStatus.isUpdatedLayout &&
					!TF.Grid.FilterHelper.isDrillDownFillter(stickBackupFilterId))
				{
					var syncedLayoutFilterModels = self.obGridFilterDataModels().filter(function(filter, idx)
					{
						return filter.id() === stickBackupFilterId; // ?????????????
					});

					if (syncedLayoutFilterModels && syncedLayoutFilterModels.length > 0)
					{
						var syncedLayoutFilterModel = syncedLayoutFilterModels[0];
						modifiedStatus.isUpdatedLayout = !TF.Grid.FilterHelper.compareFilterWhereClause(
							stickGridConfig.stickLayoutAssociateFilter.whereClause(), syncedLayoutFilterModel.whereClause()
						);

						// This status for case of : applyed layout only filter whereClause updated by another user
						modifiedStatus.isUpdatedLayoutOnlyFilterWhereCluase = modifiedStatus.isUpdatedLayout;
					}
				}
			}
		}
		else if (stickGridConfig.isFilterApplied)
		{
			if (stickGridConfig.stickFilterModel)
			{
				var stickFilterId = stickGridConfig.stickFilterModel.id();
				if (!TF.Grid.FilterHelper.isDrillDownFillter(stickFilterId))
				{
					var syncedFilterModels = self.obGridFilterDataModels().filter(function(filter, idx)
					{
						return filter.id() === stickFilterId;
					});

					if (!syncedFilterModels || syncedFilterModels.length === 0)
						modifiedStatus.isDeletedFilter = true;
					else
					{
						var syncedFilterModel = syncedFilterModels[0];
						modifiedStatus.isUpdatedFilter = !TF.Grid.FilterHelper.compareFilterWhereClause(
							stickGridConfig.stickFilterModel.whereClause(), syncedFilterModel.whereClause()
						);
					}
				}
			}
		}

		return Promise.resolve(modifiedStatus);
	};

	KendoGrid.prototype._handleUnsyncedLayout = function(unhandledStickLayoutId, unsyncedDBLayout)
	{
		var self = this;
		var layoutCnt = self.obGridLayoutExtendedDataModels().length;
		for (var idx = layoutCnt - 1; idx >= 0; idx--)
		{
			if (self.obGridLayoutExtendedDataModels()[idx].id() === unhandledStickLayoutId)
			{
				if (unsyncedDBLayout)
					self.obGridLayoutExtendedDataModels.splice(idx, 1, unsyncedDBLayout);
				else
					self.obGridLayoutExtendedDataModels.splice(idx, 1);
				break;
			}
		}
	};

	KendoGrid.prototype._refreshGridByConfigModifiedStatus = function(modifiedStatus, stickGridConfig, unsyncedDBLayout)
	{
		var self = this;
		var stickLayoutId;
		var message;

		if (modifiedStatus.isDeletedLayout)
		{
			return self.applyLayout(self._obCurrentGridLayoutExtendedDataModel());
		}
		else if (modifiedStatus.isUpdatedLayout || modifiedStatus.isUpdatedLayoutOnlyFilterWhereCluase)
		{
			if (modifiedStatus.isUpdatedLayoutOnlyFilterWhereCluase)
			{
				message = 'The applied Filter has been modified. The latest definition will be applied.';
			}
			else
			{
				message = 'The applied Layout has been modified. The latest definition will be applied.';
			}

			return tf.promiseBootbox.alert(message, 'Warning', 40000)
				.then(function()
				{
					stickLayoutId = stickGridConfig.stickLayoutModel.id();
					self._handleUnsyncedLayout(stickLayoutId, unsyncedDBLayout);
					self._obCurrentGridLayoutExtendedDataModel(unsyncedDBLayout);

					var isNoConfirm = true;
					return self.applyLayout(self._obCurrentGridLayoutExtendedDataModel(), isNoConfirm);
				});
		}
		else if (modifiedStatus.isDeletedFilter)
		{
			// ***this status has been handled by loadGridFilter function, so no need to handle here***

			// message = 'The Filter that was applied has been deleted. The system default Filter will be applied to this grid.';
			// return tf.promiseBootbox.alert(message, 'Warning')
			// .then(function() {
			//		return self.loadPresetData();
			// });
		}
		else if (modifiedStatus.isUpdatedFilter)
		{
			self.clearQuickFilterCompent();
			message = 'The applied Filter has been modified.  The latest definition will be applied.';
			return tf.promiseBootbox.alert(message, 'Warning', 40000)
				.then(function()
				{
					return self.applyGridFilter(self.obSelectedGridFilterDataModel());
				});
		}
		else
		{
			return self._triggerRefreshClick();
		}
	};

	KendoGrid.prototype._triggerRefreshClick = function()
	{
		var self = this;
		TF.Grid.LightKendoGrid.prototype.refreshClick.apply(self);
	};

	KendoGrid.prototype.toggleSummaryBar = function()
	{
		this.obSummaryGridVisible(!this.obSummaryGridVisible());
		this.lockSummaryFirstColumn();
	};

	KendoGrid.prototype.resizableBinding = function()
	{
		var self = this;
		if (self.options.resizable === false)
		{
			return;
		}

		self.kendoGrid.resizable.bind("start", function(e)
		{
			self.resizeTh = $(e.currentTarget).data("th");
			self.resizeIdx = self.resizeTh.index();
			self.isColumnLocked = $(self.resizeTh.parents()[3]).hasClass("k-grid-header-locked");
		});

		self.kendoGrid.resizable.bind("resize", function(e)
		{
			var thead;
			var tbody;
			var summaryThead, summaryTbody;
			var minTableWidth = 0;
			var minColumnWidth = 20;
			if (self.isColumnLocked)
			{
				thead = self.kendoGrid.lockedHeader.find("table").closest("table");
				tbody = self.kendoGrid.lockedTable.closest("table");
				var tbodyWidth = tbody.width();
				self.$container.find(".k-grid-header-locked,.k-grid-content-locked").width(tbodyWidth);
				if (self.summaryKendoGrid)
				{
					self.$summaryContainer.find(".k-grid-header-locked,.k-grid-content-locked").width(tbodyWidth);
					summaryThead = self.summaryKendoGrid.lockedHeader.find("table").closest("table");
					summaryTbody = self.summaryKendoGrid.lockedTable.closest("table");
					self.lockSummaryFirstColumn();
				}
				if (thead.children("thead").children("tr").filter(":first").find("th").eq(self.resizeIdx).data("kendoField") == "bulk_menu")
				{
					thead.children("colgroup").find("col").eq(self.resizeIdx).width('30px');
					tbody.children("colgroup").find("col").eq(self.resizeIdx).width('30px');
					self.lockGridFirstColumn();
					return;
				}
			}
			else
			{
				thead = self.kendoGrid.thead.closest("table");
				tbody = self.kendoGrid.tbody.closest("table");
				if (self.summaryKendoGrid)
				{
					summaryThead = self.summaryKendoGrid.thead.closest("table");
					summaryTbody = self.summaryKendoGrid.tbody.closest("table");
				}
			}

			if (self.resizeTh.width() >= minColumnWidth)
			{
				minTableWidth = tbody.width();
			}
			if (self.resizeTh.width() < minColumnWidth)
			{
				// the next line is ONLY needed if Grid scrolling is enabled
				thead.width(minTableWidth).children("colgroup").find("col").eq(self.resizeIdx).width(minColumnWidth + 'px');
				tbody.width(minTableWidth).children("colgroup").find("col").eq(self.resizeIdx).width(minColumnWidth + 'px');
			}
			if (self.summaryKendoGrid)
			{
				self.$summaryContainer.find(".k-grid-content>table,.k-grid-header-wrap>table").width(tbody.width());
				var width = tbody.children("colgroup").find("col").eq(self.resizeIdx).width();
				[summaryThead, summaryTbody].forEach(function(table)
				{
					table.children("colgroup").find("col").eq(self.resizeIdx).width(width);
				});
			}

			self._refreshGridBlank();
		});

		self.kendoGrid.resizable.bind("resizeend", function(e)
		{
			self._refreshGridBlank();
			if (self.obSummaryGridVisible())
			{
				self.fitContainer();
			}
		});
	};

	KendoGrid.prototype.lockGridFirstColumn = function() //lock the first column
	{
		var theadTable = this.kendoGrid.lockedHeader.find("table");
		if (this.isColumnLocked && theadTable.children("thead").children("tr").filter(":first").find("th").eq(this.resizeIdx).data("kendoField") == "bulk_menu")
		{
			var tbodyTable = this.kendoGrid.lockedTable;
			if (theadTable.parent().find("col").length === 1)
			{
				theadTable.attr("style", "width:30px");
				tbodyTable.attr("style", "width:30px");
			}
			else //if the lock area has more than one column,keep the width of lock area donnot change
			{
				var width = 0;
				for (var i = 0; i < theadTable.parent().find("col").length; i++)
				{
					var colItem = theadTable.parent().find("col")[i];
					width += parseInt(colItem.style.width.substring(0, colItem.style.width.length - 2));
				}
				theadTable.attr("style", "width:" + width + "px");
				tbodyTable.attr("style", "width:" + width + "px");
			}
			theadTable.parent().find("col").eq(this.resizeIdx).attr("style", "width:30px");
			tbodyTable.parent().find("col").eq(this.resizeIdx).attr("style", "width:30px");
		}
	};

	KendoGrid.prototype.createTooltips = function()
	{
		var self = this, tooltipFields = self.options.gridDefinition.Columns.filter(c => c.tooltip);
		if (tooltipFields.length == 0)
		{
			return;
		}
		var selector = tooltipFields.map(field => `td[data-kendo-field="${field.FieldName}"]`).join(',');
		$(self.kendoGrid.element)
			.kendoTooltip({
				filter: selector,
				position: "top",
				width: 250,
				content: function(e)
				{
					var dataItem = self.kendoGrid.dataItem(e.target.closest('tr'));
					var content = dataItem && dataItem[e.target.data('kendoField')];
					return content;
				},
				show()
				{
					if (!this.content.text())
					{
						this.content.parent().css("visibility", "hidden");
					}
					else this.content.parent().css("visibility", "visible");
				}
			});
	}

	//chang sort model {if shift press, the sort model is multiple; else, sort model is single}
	KendoGrid.prototype.changeSortModel = function()
	{
		var self = this;
		this.kendoGrid.element.find("div.k-grid-header [data-kendo-role=columnsorter]").on("mousedown", function(e)
		{
			if ((self.kendoGrid.options.sortable.mode != "multiple" && e.shiftKey) || (self.kendoGrid.options.sortable.mode != "single" && !e.shiftKey))
			{
				var sortIdx = $(e.currentTarget).index();
				var isLockedColumn = $($(e.currentTarget).parents()[3]).hasClass("k-grid-header-locked");
				self.sortModel = e.shiftKey ? "multiple" : "single";
				self.rebuildGrid().then(function()
				{
					// rebuildGrid is a promise method
					var thead;
					if (isLockedColumn)
					{
						thead = self.kendoGrid.lockedHeader.find("table").closest("table");
					}
					else
					{
						thead = self.kendoGrid.thead.closest("table");
					}
					thead.children("thead").children("tr").filter(":first").find("th").eq(sortIdx).click();
				});

			}
		});
	};

	KendoGrid.prototype.columnResizeEvent = function(e)
	{
		TF.Grid.LightKendoGrid.prototype.columnResizeEvent.apply(this, arguments);
		setTimeout(function()
		{
			this.saveState();
		}.bind(this));
	};

	KendoGrid.prototype._lockUnLockedByColumnIndex = function(index, options)
	{
		const lockOptions = $.extend({}, { toggleGridColumnLockStatus: true }, options);
		var self = this;
		var column = self.kendoGrid.columns[index];

		if (column.locked)
		{
			tf.loadingIndicator.show();
			//console.log("kendoGrid +1");
			//Unlock Columns
			//find all locked columns, and unlock all the locked columns to the right of the clicked column (including the clicked column)
			var lockCount = self.countLockedColumns(self.kendoGrid.columns);
			var unlockColumn = [];
			if (index != lockCount - 1) //not the right-most locked column, unlock all locked columns to its right
			{
				for (var idx = lockCount - 1; idx > index; idx--)
				{
					var tmp = self.kendoGrid.columns[idx];
					unlockColumn.push({ field: tmp.field, udfId: tmp.UDFId });
				}
			}
			else //is the right-most locked column, unlock all
			{
				for (var idx = index; idx >= self.permanentLockCount(); idx--)
				{
					var tmp = self.kendoGrid.columns[idx];
					unlockColumn.push({ field: tmp.field, udfId: tmp.UDFId });
				}
			}
			for (var i = 0; i < self.tobeLockedColumns.length; i++)
			{
				for (var j = 0; j < unlockColumn.length; j++)
				{
					if (unlockColumn[j].field === self.tobeLockedColumns[i].field || (unlockColumn[j].udfId && unlockColumn[j].udfId === self.tobeLockedColumns[i].udfId))
					{
						self.tobeLockedColumns.splice(i, 1);
						i--;
						break;
					}
				}
			}

			if (lockOptions.toggleGridColumnLockStatus)
			{
				self.rebuildGrid().then(function()
				{
					// rebuildGrid is a promise method
					tf.loadingIndicator.tryHide();
					//console.log("kendoGrid -1");

					// if there is no locked column left, display message
					const hasLocked = self._hasLockedColumns();
					self.toggleGridColumnLockStatus(hasLocked, true);
				});
			}
			else
			{
				tf.loadingIndicator.tryHide();
			}
		}
		else
		{
			//Lock columns
			//store all the to be locked columns, and lock all the columns to the left of the clicked column (including the clicked column)
			for (var idx = self.permanentLockCount(); idx <= index; idx++)
			{
				var tmp = self.kendoGrid.columns[idx];
				if (!tmp.locked)
				{
					self.tobeLockedColumns.push(tmp);
				}
			}
			var tempColumns = self.tooManyLockedColumns(self.tobeLockedColumns);

			self.tobeLockedColumns = tempColumns;

			if (lockOptions.toggleGridColumnLockStatus)
			{
				self.rebuildGrid().then(function()
				{
					// rebuildGrid is a promise method
					self.toggleGridColumnLockStatus(true, true);
				});
			}
		}

		setTimeout(function()
		{
			self.saveState();
		});
	};

	/**
	 * Toggle the status whether there is locked column in the grid.
	 *
	 * @param {Boolean} lock
	 */
	KendoGrid.prototype.toggleGridColumnLockStatus = function(hasLocked, showMessage)
	{
		const hasLockedClass = "not-only-first-column-locked";
		const $rows = this.$container.find(".gridrow .full-height");

		$rows.toggleClass(hasLockedClass, hasLocked);
		this.obHasAnyLockedColumn(hasLocked);

		if (showMessage)
		{
			const alertOption = hasLocked
				? {
					title: "Columns Locked",
					message: "Grid columns have been locked. Right click a grid column header to change the locked columns or right click the right-most locked column to remove the lock from all columns.",
					key: this._columnsLockedTimesKey
				}
				: {
					title: "Columns Unlocked",
					message: "Grid columns are no longer locked.  Right click on a grid column header to lock the column and all columns to the left of it.",
					key: this._columnsUnLockedTimesKey
				};

			this.gridAlert.show(this._buildLockedUnLockedAlertOption(alertOption));
		}
	};

	KendoGrid.prototype._hasLockedColumns = function()
	{
		var self = this;
		return (self.countLockedColumns(self.kendoGrid.columns) - self.permanentLockCount()) > 0;
	};

	KendoGrid.prototype.lockUnlockColumn = function()
	{
		let self = this, exceptFields = ["bulk_menu", "map_checkbox"];

		$(self.$container).off("mousedown", "th[role='columnheader']").on("mousedown", "th[role='columnheader']", function(e)
		{
			let header = self.$container.find("th[role='columnheader']");

			if (self.isFromDashboard || self.options.isMiniGrid)
			{
				return;
			}

			if (e.which == 3)
			{
				if (exceptFields.indexOf($(this).attr("data-kendo-field")) >= 0)
				{
					return false;
				}

				var index = parseInt($(this).attr("data-" + kendo.ns + "index"));
				if (index === header.length - 1)
				{
					self.gridAlert.show(
						{
							alert: "Warning",
							title: "Warning",
							message: "There should be at least one non locked column",
							key: self._columnsUnLockedTimesKey
						});
					return;
				}
				self._lockUnLockedByColumnIndex.bind(self)(index);
			}
		});

		$(self.$container).off("contextmenu", "th[role='columnheader']").on("contextmenu", "th[role='columnheader']", function(e)
		{
			// Fix issue RW-39469 RCM menu does not open when click on column header except from "Action"
			if (!self.options.miniGridEditMode || exceptFields.indexOf($(this).attr("data-kendo-field")) >= 0)
			{
				return false;
			}
		});
	};

	KendoGrid.prototype._buildLockedUnLockedAlertOption = function(options)
	{
		var key = options.key,
			title = options.title,
			message = options.message;

		var times = tf.storageManager.get(key) || 0;
		var gridAlertOption = {
			title: title
		};
		if (times === 0)
		{
			tf.storageManager.save(key, 1);
			gridAlertOption.delay = 10 * 1000;
			gridAlertOption.message = message;
		}
		else if (times === 1)
		{
			tf.storageManager.save(key, 2);
			gridAlertOption.message = message;
		}
		else if (times === 2)
		{
			tf.storageManager.save(key, 3);
			gridAlertOption.message = message;
		}
		else if (times > 2)
		{
			gridAlertOption.width = 150;
			gridAlertOption.delay = 2 * 1000;
		}

		return gridAlertOption;
	};

	KendoGrid.prototype.countLockedColumns = function(columns)
	{
		var count = 0;
		for (var i in columns)
		{
			if (columns[i].locked)
			{
				count += 1;
			}
		}
		return count;
	};

	KendoGrid.prototype.tooManyLockedColumns = function(tobeLockedColumns)
	{
		var containerWidth = this.$container.find(".k-grid-header").width() - 50;
		var lockedWidth = 0;
		var tempColumns = [];
		//get total width of the to be locked columns' width
		for (var i = 0; i < tobeLockedColumns.length; i++)
		{
			lockedWidth += parseInt(tobeLockedColumns[i].width);
			if (lockedWidth > containerWidth)
			{
				return tempColumns;
			}
			tempColumns.push(tobeLockedColumns[i]);
		}

		return tempColumns;
	};

	KendoGrid.prototype.clearKendoGridQuickFilter = function(lazyRebuildGrid)
	{
		var self = this;
		self.isFromRelated(false);
		self.listFilters = TF.ListFilterHelper.initListFilters();
		TF.CustomFilterHelper.clearCustomFilter();
		self.kendoGrid && self.kendoGrid.dataSource.filter({}, lazyRebuildGrid);
		tf.storageManager.delete(self._storageFilterDataKey, true);
		self.inited = false;
		return Promise.resolve(true);
	};

	KendoGrid.prototype.removeHiddenColumnQuickFilter = function(hiddenColumns)
	{
		var self = this;
		if (!self.kendoGrid.dataSource.filter ||
			!self.kendoGrid.dataSource.filter() ||
			self.kendoGrid.dataSource.filter().filters.length === 0 ||
			hiddenColumns.length === 0)
			return;

		var displayColumnQuickFilters = self.kendoGrid.dataSource.filter().filters.filter(function(filter)
		{
			if (filter.filters)
			{
				var hiddenColumnQuickFilters = hiddenColumns.filter(function(hiddenColumn)
				{
					return (filter.filters[0].field === hiddenColumn.FieldName);
				});
				return (hiddenColumnQuickFilters.length === 0);
			}
			else
			{
				var hiddenColumnQuickFilters = hiddenColumns.filter(function(hiddenColumn)
				{
					return (filter.field === hiddenColumn.FieldName);
				});
				return (hiddenColumnQuickFilters.length === 0);
			}
		});

		if (displayColumnQuickFilters.length === 0)
			return self.clearKendoGridQuickFilter();
		else
		{
			self.kendoGrid.dataSource.filter().filters = displayColumnQuickFilters;
			return Promise.resolve(true);
		}
	};

	KendoGrid.prototype.removeHiddenColumnSummaryFunction = function(selectColumns)
	{
		var self = this;
		if (!self.obAggregationMap() || selectColumns.length === 0)
			return;
		var aggregations = self.obAggregationMap();
		for (var key in aggregations)
		{
			if (aggregations[key])
			{
				var keyExist = false;
				selectColumns.map(function(selectColumn)
				{
					if (key === selectColumn["FieldName"])
					{
						keyExist = true;
						return;
					}
				});
				if (!keyExist)
				{
					delete aggregations[key];
				}
			}
		}
		self.obAggregationMap(aggregations);
	};

	KendoGrid.prototype._setgridStateTwoRowWhenOverflow = function()
	{
		//show hide comma when filter and layout toggle split to tow row on tablet
		if (TF.isMobileDevice)
		{
			$(".grid-staterow-wrap").css("visibility", "hidden");
			setTimeout(function()
			{
				$(".grid-staterow-wrap").each(function()
				{
					var width = 0;
					var gridStaterowWrap = $(this);
					gridStaterowWrap.find(".grid-staterow-status").each(function()
					{
						width += $(this).width();
					});
					var comma = gridStaterowWrap.find(".comma-split");
					var gridStaterow = gridStaterowWrap.children();
					if (width > gridStaterowWrap.width())
					{
						comma.hide();
						gridStaterow.addClass('overflow');
					}
					else
					{
						comma.show();
						gridStaterow.removeClass('overflow');
					}
					gridStaterowWrap.css("visibility", "visible");
				});
			});
		}
	};

	KendoGrid.prototype.bindNeedFileds = function(type, fields)
	{
		fields = TF.Grid.LightKendoGrid.prototype.bindNeedFileds.call(this, type, fields);
		if (type === 'student' || type === 'altsite' || type === 'school' || type === "georegion")
		{
			if (!Enumerable.From(fields).Contains('Xcoord'))
			{
				fields = fields.concat(['Xcoord']);
			}
			if (!Enumerable.From(fields).Contains('Ycoord'))
			{
				fields = fields.concat(['Ycoord']);
			}
		}

		this.selectedGridThematicConfigs = this.getSelectedGridThematicConigs();

		if (this.thematicFields.length > 0)
		{
			var needToAddFields = [];
			this.thematicFields.forEach(function(needField)
			{
				if (!Enumerable.From(fields).Contains(needField))
				{
					needToAddFields.push(needField);
				}
			});

			fields = fields.concat(needToAddFields);
		}

		return fields;
	};

	KendoGrid.prototype.getCurrentGridLayout = function()
	{
		return this._obCurrentGridLayoutExtendedDataModel();
	};

	/**
	 * Apply grid thematic configs.
	 *
	 * @param {*} selectedGridThematicConfigs
	 * @return {*}
	 */
	KendoGrid.prototype.applyGridThematicConfigs = function(selectedGridThematicConfigs)
	{
		const self = this;

		selectedGridThematicConfigs = selectedGridThematicConfigs || self.selectedGridThematicConfigs;

		const isToApplyThematic = Boolean(selectedGridThematicConfigs);
		const $table = (self.kendoGrid && self.kendoGrid.table) || self.$container.find("div[class^='k-grid-content'] table");
		const $trList = $table.find(">tbody>tr.k-master-row");

		self.obIsThematicApplied(isToApplyThematic);

		// Clear thematic marks.
		if (!isToApplyThematic)
		{
			$trList.attr(THEMATIC_COLOR_ATTRIBUTE, null);
			return;
		}

		// Apply thematic configurations.
		const $leadingTable = self.$container.find("div[class^='k-grid-content-locked'] table");
		const $leadingRows = $leadingTable.find(">tbody>tr.k-master-row");
		const $fullfillRows = self.$container.find(".kendogrid-blank-fullfill>tbody>tr.k-master-row");

		const dataType = self._gridType;
		const dataItems = self.kendoGrid.dataItems();
		if (self.lazyloadFields.udf?.length && !$.isEmptyObject(self.alreadyLoadId.udf))
		{
			dataItems.forEach(item =>
			{
				self.lazyloadFields.udf.forEach(udf =>
				{
					item[udf.OriginalName] = self.alreadyLoadId.udf[item.Id]?.[udf.UDFId];
				});
			});
		}

		const dataItemColorDict = TF.Helper.ThematicHelper.getDataItemColorDict(dataType, dataItems, selectedGridThematicConfigs, self._gridDefinition.Columns);
		const colorLightnessDict = new Map();

		Array.from($trList).forEach((tr, idx) =>
		{
			const $tr = $(tr);
			const uid = $tr.data().kendoUid;
			const color = dataItemColorDict[uid];
			let isLightColor;

			if (colorLightnessDict.has(color))
			{
				isLightColor = colorLightnessDict.get(color);
			}
			else
			{
				isLightColor = TF.isLightness(color);
				colorLightnessDict.set(color, isLightColor);
			}

			$tr.attr(THEMATIC_COLOR_ATTRIBUTE, color);

			self.updateRowColor($tr, color, isLightColor);
			self.updateRowColor($leadingRows.eq(idx), color, isLightColor);
			self.updateRowColor($fullfillRows.eq(idx), color, isLightColor);
		});
	}

	KendoGrid.prototype.updateRowColor = function($row, bkgColor, isLightColor)
	{
		if ($row)
		{
			$row.removeClass("k-alt");
			$row.css("background-color", bkgColor);
			$row.find("td").toggleClass("thematic-light", !isLightColor);
		}
	}

	KendoGrid.prototype.getSelectedGridThematicConigs = function()
	{
		var self = this, customDisplaySettings, quickFilters, gridThematicConfigs;

		if (self.tempGridThematicDataModel)
		{
			customDisplaySettings = JSON.parse(self.tempGridThematicDataModel.customDisplaySetting());
			quickFilters = JSON.parse(self.tempGridThematicDataModel.quickFilters());
			gridThematicConfigs = [];
		}
		else if (self.obSelectedGridThematicDataModel && self.obSelectedGridThematicDataModel())
		{
			customDisplaySettings = JSON.parse(self.obSelectedGridThematicDataModel().customDisplaySetting());
			quickFilters = JSON.parse(self.obSelectedGridThematicDataModel().quickFilters());
			gridThematicConfigs = [];
		}

		if (customDisplaySettings)
		{
			customDisplaySettings.forEach(cds =>
			{
				var gridThematicConfig = [];
				if (cds.DisplayLabel === DEFAULT_THEMATIC_LABEL)
				{
					gridThematicConfig.push(
						{
							"field": DEFAULT_THEMATIC_LABEL,
							"value": DEFAULT_THEMATIC_LABEL,
							"color": cds.Color,
							"typeId": null,
							"imperialValue": DEFAULT_THEMATIC_LABEL
						});
					gridThematicConfigs.push(gridThematicConfig);
					return;
				}

				for (var i = 0; i < quickFilters.length; i++)
				{
					if (quickFilters[i].field)
					{
						gridThematicConfig.push(
							{
								"field": quickFilters[i].field,
								"value": cds[`Value${i + 1}`],
								"color": cds.Color,
								"typeId": quickFilters[i].typeId,
								"imperialValue": cds[`AdditionalValue${i + 1}`]
							});

						if (!Enumerable.From(self.thematicFields).Contains(quickFilters[i].field))
						{
							self.thematicFields.push(quickFilters[i].field);
						}
					}
				}

				gridThematicConfigs.push(gridThematicConfig);
			});

			return gridThematicConfigs;
		}

		return null;
	}

	/**
	 * Show or hide a semi-transparent loading mask layer on the grid and the icon row.
	 * @param {boolean} disabled
	 */
	KendoGrid.prototype.disable = function(disabled)
	{
		kendo.ui.progress(this.kendoGrid.element.parent(), disabled);
		kendo.ui.progress(this.kendoGrid.element.closest(".document-grid").find(".iconrow"), disabled);
	}

	KendoGrid.prototype.dispose = function()
	{
		// VIEW-7199: When resizing the browser, the record count cannot be displayed.
		// Originally, only the main grid used lightkendoGrid. However the detailView mini-grid also uses lightkendoGrid now.
		// This means that if you open a student grid with a detailView that includes a document mini-grid, 
		// it will bind ResizeEvent twice. 1st time, bind the studentGridResizeEvent. After that it would unbind the studentGridResizeEvent and bind the documentGridResizeEvent.
		// In Plus, the studentGridResizeEvent is triggered by other way. So only add code to handle the case in viewfinder.
		const toUnbindResizeEvent = !tf.isViewfinder || (tf.pageManager.getPageId(this._gridType) == tf.pageManager.pageType());
		if (toUnbindResizeEvent)
		{
			TF.LightKendoGridHelper.unbindResizeEvent();
		}

		if (this.kendoGrid && this.kendoGrid._draggableInstance)
		{
			kendo.ui.DropTarget.destroyGroup(this.kendoGrid._draggableInstance.options.group);
		}
		this.obGridFilterDataModelsFromDataBase = null;
		if (this.isFromDashboard)
		{
			//RW-42839: Fix console error when quick click apply after selecting layout.
			this.obGridFilterDataModels([]);
		}
		else
		{
			this.obGridFilterDataModels = null;
		}
		this.obGridFilterDataModelsFromRelatedFilter = null;
		if (this.summaryKendoGrid)
		{
			this.summaryKendoGrid.destroy();
		}
		this.summaryKendoGrid = null;
		PubSub.unsubscribe(this._dataChangeReceive);
		PubSub.unsubscribe(this.loadGridFilter);
		this._reminderSubscription && (PubSub.unsubscribe(this._reminderSubscription));
		$(window).off("orientationchange.gridStateTwoRowWhenOverflow" + this.randomKey, null);
		if (this.addHotLinkTimer)
		{
			clearTimeout(this.addHotLinkTimer);
			this.addHotLinkTimer = null;
		}
		if (this.thematicFields.length > 0)
		{
			this.thematicFields = [];
		}
		if (this.selectedGridThematicConfigs)
		{
			this.selectedGridThematicConfigs = null;
		}
		if (this.tempGridThematicDataModel)
		{
			this.tempGridThematicDataModel = null;
		}
		TF.Grid.LightKendoGrid.prototype.dispose.apply(this);
	};

	addPlugin(KendoGrid, TF.Grid.KendoGridFilterMenu, TF.Grid.KendoGridLayoutMenu, TF.Grid.GeoCodingMenu, TF.Grid.KendoGridSummaryGrid, TF.Grid.KendoOverFlowMenu, TF.Grid.KendoNewGridWithSelectedRecordsMenu, TF.Grid.KendoGridThematicMenu);
})();
(function()
{
	createNamespace("TF.Grid").GridHelper = {
		convertToOldGridDefinition: function(definition)
		{
			var def = {
				TypeCode: (definition.DBType || definition.type).replace(/datetime/i, "DateTime")
					.replace(/\b(\w)|\s(\w)/g, function(m)
					{
						return m.toUpperCase();
					}),
				AllowSorting: definition.sortable,
				AllowFiltering: definition.filterable,
				FieldName: (definition.field ? definition.field : definition.FieldName),
				DisplayName: definition.title ? definition.title : (definition.DisplayName ? definition.DisplayName : definition.FieldName),
				PersistenceName: definition.DBName || definition.field || (definition.UDFId ? definition.OriginalName : definition.FieldName),
				PositiveValue: definition.positiveValue,
			};

			if (definition.UDFId)
			{
				def.UDFId = definition.UDFId;
			}

			if (definition.questionType === 'List' ||
				definition.questionType === 'ListFromData' ||
				(definition.questionType === 'SystemField' && !!definition.ListFilterTemplate))
			{
				def.UDFPickListOptions = definition.ListFilterTemplate.AllItems ?
					definition.ListFilterTemplate.AllItems.map(x => ({ PickList: x })) : [];

				if (definition.ListFilterTemplate.GridType && definition.ListFilterTemplate.GridType === "Form")
				{
					def.isFromForm = true;
					if (definition.ListFilterTemplate.requestOptions)
					{
						def.requestOptions = definition.ListFilterTemplate.requestOptions;
					}

				}
			}
			else if (definition.UDFPickListOptions)
			{
				def.UDFPickListOptions = definition.UDFPickListOptions;
			}

			if (definition.DBIDs)
			{
				def.DBIDs = definition.DBIDs;
			}

			return def;
		},
		_getDataSourceSpecificFields: function(gridType)
		{
			switch (gridType)
			{
				case "altsite":
					return ["altsiteid"];
				case "contact":
					return ["id"];
				case "contractor":
					return ["contractor_id"];
				case "district":
					return ["districtid"];
				case "document":
					return ["documentid", "documentclassificationid"];
				case "fieldtrip":
					return ["DistrictDepartmentID", "FieldTripActivityID", "FieldTripAccountID", "FieldTripClassificationID", "FieldTripDestinationID", "FieldTripEquipmentID", "FieldTripID", "FieldTripStageID"];
				case "georegion":
					return ["GeoRegionTypeId", "GeoRegionId"];
				case "school":
					return ["SchoolID"];
				case "staff":
					return ["ContractorID", "StaffID"];
				case "student":
					return ["Stud_ID", "Dly_Pu_Site", "Dly_Do_Site", "Dly_Pu_TripStop", "Dly_Do_TripStop", "Dly_Do_TranTripStop", "Dly_Pu_TranTripStop", "Dly_Pu_TripID", "Dly_Do_TripID", "Dly_Pu_TranTripID", "Dly_Do_TranTripID"];
				case "trip":
					return ["TripID", "AideID", "DriverID", "VehicleID", "TravelScenarioId"];
				case "tripstop":
					return ["TripStopID", "TripID"];
				case "vehicle":
					return ["VehicleID", "ContractorID"];
			}
		},
		checkFilterContainsDataBaseSpecificFields: function(gridType, whereClause)
		{
			var self = this, dbSpecificFields = self._getDataSourceSpecificFields(gridType);

			if (!dbSpecificFields) return false;

			if (!whereClause || whereClause.length == 0)
			{
				return false;
			}

			whereClause = whereClause.toLowerCase();
			return dbSpecificFields.some(function(field)
			{
				return new RegExp("\\b" + field.toLowerCase() + "\\b", "g").test(whereClause);
			});
		},
		_convertToOldGridDefinition: function(gridDefinition)
		{
			var self = this;
			return gridDefinition.gridDefinition().Columns.map(function(definition)
			{
				return self.convertToOldGridDefinition(definition);
			});
		},
		_grid: null,
		get GridInfo()
		{
			var self = this;
			if (this._grid)
			{
				return this._grid;
			}
			this._grid = [
				{
					gridType: "altsite",
					tableName: "alt_site",
					authName: "alternateSite",
					type: 'baseGrid',
					plural: tf.applicationTerm.getApplicationTermPluralByName('Alternate Site'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Alternate Site'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.altsiteGridDefinition);
					}
				},
				{
					gridType: "contractor",
					tableName: "Contractor",
					authName: "contractor",
					type: 'baseGrid',
					plural: tf.applicationTerm.getApplicationTermPluralByName('Contractor'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Contractor'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.contractorGridDefinition);
					}
				},
				{
					gridType: "district",
					tableName: "District",
					authName: "district",
					type: 'baseGrid',
					plural: tf.applicationTerm.getApplicationTermPluralByName('District'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('District'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.districtGridDefinition);
					}
				},
				{
					gridType: "fieldtrip",
					tableName: "fieldtrip",
					authName: "fieldtrip",
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Field Trip'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Field Trip'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.fieldTripGridDefinition);
					}
				},
				{
					gridType: "georegion",
					tableName: "georegion",
					authName: "geoRegions",
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Geo Regione'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Geo Regione'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.georegionGridDefinition);
					}
				},
				{
					gridType: "school",
					tableName: "School",
					authName: "school",
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('School'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('School'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.schoolGridDefinition);
					}
				},
				{
					gridType: "student",
					tableName: "Student",
					authName: "student",
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Student'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Student'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.studentGridDefinition);
					}
				},
				{
					gridType: "tripstop",
					tableName: "tripstop",
					authName: "trip",
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Trip Stop'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Trip Stop'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.tripStopGridDefinition);
					}
				},
				{
					gridType: "trip",
					tableName: "trip",
					authName: 'trip',
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Trip'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Trip'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.tripGridDefinition);
					}
				},
				{
					gridType: "staff",
					tableName: "staff",
					authName: 'staff',
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Staff'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Staff'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.staffGridDefinition);
					}
				},
				{
					gridType: "vehicle",
					tableName: "vehicle",
					authName: 'vehicle',
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Vehicle'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Vehicle'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.vehicleGridDefinition);
					}
				},
				{
					gridType: "contact",
					tableName: "contact",
					authName: 'contact',
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Contact'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Contact'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.contactGridDefinition);
					}
				}, {
					gridType: "document",
					tableName: "document",
					authName: 'document',
					type: "baseGrid",
					plural: tf.applicationTerm.getApplicationTermPluralByName('Document'),
					singular: tf.applicationTerm.getApplicationTermSingularByName('Document'),
					get apiGridDefinition()
					{
						return self._convertToOldGridDefinition(tf.documentGridDefinition);
					}
				}];
			return this._grid;
		}
	};
})();
