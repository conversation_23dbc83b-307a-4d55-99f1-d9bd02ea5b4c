(function()
{
	createNamespace("TF.Control").KendoListMoverWithSearchControlViewModel = KendoListMoverWithSearchControlViewModel;

	KendoListMoverWithSearchControlViewModel.defaults = {
		description: "",
		availableTitle: "",
		selectedTitle: "",
		height: 400,
		showEnabled: false,
		displayCheckbox: true,
		filterCheckboxText: "",
		mustSelect: false,
		showBulkMenu: false,
		showLockedColumn: false,
		columnWidth: "150px",
		selectable: "multiple",
		disableDropIndicator: true,
		getUrl: (gridType, options) =>
		{
			if (gridType === "gpsevent")
			{
				return TF.Helper.VehicleEventHelper.getRequestURL();
			}

			if (gridType === "parceladdresspoint")
			{
				return `${tf.api.apiPrefix()}/search/parcelpoints`;
			}

			if (gridType === "street")
			{
				return TF.StreetHelper.getGridRequestURL();
			}

			let prefix = options.dataSource ?
				pathCombine(tf.api.apiPrefixWithoutDatabase(), options.dataSource) :
				tf.api.apiPrefix();

			if (tf.dataTypeHelper.getDataTypeConfig(gridType).hasDBID === false)
			{
				prefix = tf.api.apiPrefixWithoutDatabase();
			}

			return pathCombine(prefix, "search", tf.dataTypeHelper.getEndpoint(gridType));
		},
		getParamData: function(searchData)
		{
			return searchData.paramData;
		},
		getData: function(searchData)
		{
			return searchData.data;
		},
		setRequestOption: function(option)
		{
			return option;
		},
		filterSetField: "",
		overlay: true,
		serverPaging: false,
		getSelectableRecords: null
	};

	function KendoListMoverWithSearchControlViewModel(selectedData, options)
	{
		if (typeof (options.serverPaging) == "undefined" && (options.type === "student" || options.type === "recordcontact"))
		{
			options.serverPaging = true;
		}
		this.options = $.extend({}, KendoListMoverWithSearchControlViewModel.defaults, options);

		// advance mode setting default values
		this.showAdvancedMode = false;
		this.obRegardlessCriteria = ko.observable(options.obRegardlessCriteria ? options.obRegardlessCriteria() : false);

		this.leftSearchGrid = null;
		this.rightSearchGrid = null;
		this.headerFilters = null;

		this.isChanged = false;

		this.description = this.options.description;
		this.availableTitle = this.options.availableTitle;
		this.selectedTitle = this.options.selectedTitle;
		// If the grid is reading data from server(virtual scroll), only get ids.
		this.selectedData = this.options.serverPaging ? Enumerable.From(selectedData.slice()).Select("$.Id").ToArray() : selectedData.slice();
		this.oldData = this.options.serverPaging ? Enumerable.From(selectedData.slice()).Select("$.Id").ToArray() : selectedData.slice();
		this.customStorageKey = this.options.customStorageKey || "custom_";
		this.customColumnSources = this.options.columnSources;
		this.columns = this.options.columnSources || this.getColumnSources(this.options.type);

		if (!this.columns && this.options.type === 'Genders')
		{
			this.columns = TF.ListFilterDefinition.ColumnSource.Gender;
		}

		if (!this.columns && this.options.type === 'Grades')
		{
			this.columns = TF.ListFilterDefinition.ColumnSource.Grade;
		}

		this.columns && this.columns.forEach(col =>
		{
			col.OriginalName = options.OriginalName || col.OriginalName;
			col.UDFId = options.UDFId || col.UDFId;
		});
		this.fixedLeftIds = options.fixedLeftIds || [];
		this.fixedRightIds = options.fixedRightIds || [];

		this.originalColumns = [];
		this.allRecords = [];

		this.obLeftCount = ko.observable(0);
		this.obRightCount = ko.observable(0);
		this.obLeftGridMoveableIds = ko.observableArray([]);
		this.obRightGridMoveableIds = ko.observableArray([]);

		this.obDisplayCheckbox = ko.observable(this.options.displayCheckbox);
		this.obDisplayAddButton = ko.observable(this.options.displayAddButton);
		this.obGuid = ko.observable(kendo.guid());

		this._obLeftSelData = ko.observableArray();
		this._obRightSelData = ko.observableArray();
		this.obAvailableCount = ko.observable(0);
		this.leftGridExcludeId = [];
		this.obShowEnabled = ko.observable(this.options.showEnabled);
		this.obFilterCheckboxText = ko.observable(this.options.filterCheckboxText);
		this.obSelectedData = ko.observableArray(this.selectedData);
		this.obSelectedData.subscribe(() =>
		{
			this.obDisableControl && this.obDisableControl(this.obSelectedData().length === 0)
		});
		this.obshowRemoveColumnButton = ko.observable(options.showRemoveColumnButton);
		this.obEditCurrentDefinitionColumns = ko.observable(options.editCurrentDefinitionColumns);
		this.obEditCustomDefinitionColumns = ko.observable(options.editCustomDefinitionColumns);
		this.showRawImageColumn = options.showRawImageColumn;

		this.obLeftGridSelected = ko.computed(function()
		{
			return this._obLeftSelData() && this._obLeftSelData().length > 0;
		}, this);
		this.obRightGridSelected = ko.computed(function()
		{
			return this._obRightSelData() && this._obRightSelData().length > 0;
		}, this);
		this.obSelectedCount = ko.computed(function()
		{
			return this.obSelectedData().length;
		}, this);
		this.obShowEnabledCopmuter = ko.computed(function()
		{
			return this.obShowEnabled();
		}, this);
		this.obIsInoperableSelected = ko.observable(false);

		this.obErrorMessageDivIsShow = ko.observable(false);
		this.obErrorMessage = ko.observable("");
		this.obValidationErrors = ko.observableArray([]);
		this.obErrorMessageTitle = ko.observable("Error Occurred");
		this.obErrorMessageDescription = ko.observable("The following error occurred.");

		this._dataChangeReceive = this._dataChangeReceive.bind(this);
		PubSub.unsubscribe(topicCombine(pb.DATA_CHANGE, "listmover"));
		PubSub.subscribe(topicCombine(pb.DATA_CHANGE, "listmover"), this._dataChangeReceive);
		this.useMaxRecordCount = false;

		this.showActiveTripsCheckbox = false;
		this.obActiveTripsChecked = ko.observable(false);
	}

	KendoListMoverWithSearchControlViewModel.prototype._dataChangeReceive = function()
	{
		this.leftSearchGrid.refresh();
	};

	KendoListMoverWithSearchControlViewModel.prototype.filterMenuClick = function(viewModel, e)
	{
		return TF.Helper.KendoListMoverHelper.filterMenuClick.call(this, viewModel, e);
	};

	KendoListMoverWithSearchControlViewModel.prototype.createFilterMenu = function(viewModel, e)
	{
		var self = this;
		var leftGrid = this.leftSearchGrid;
		var gridType = this.options.type.toLowerCase();
		var allColumns = TF.Grid.FilterHelper.getGridDefinitionByType(gridType).Columns.slice(0);
		var gridDefinition = {
			Columns: allColumns.map(function(definition)
			{
				return TF.Grid.GridHelper.convertToOldGridDefinition(definition);
			})
		};

		if (!leftGrid.isFilterInited)
		{
			leftGrid.obSelectedGridFilterId = ko.observable();
			leftGrid.obSelectedGridFilterClause = ko.observable();
			leftGrid.reminderMenuEnable = ko.observable(false);
			leftGrid.obGridFilterDataModelsFromDataBase = ko.observableArray([]);
			leftGrid.isFilterInited = true;

			leftGrid.createNewFilterClick = function()
			{
				return tf.modalManager.showModal(
					new TF.Modal.Grid.ModifyFilterModalViewModel(
						gridType,
						"new",
						null,
						null,
						gridDefinition
					)
				).then(function(result)
				{
					if (!result)
					{
						return;
					}
					leftGrid.obGridFilterDataModelsFromDataBase.push(result.savedGridFilterDataModel);
					if (result.applyOnSave)
					{
						applyGridFilter(result.savedGridFilterDataModel);
					}
				});
			};

			leftGrid.manageFilterClick = function()
			{
				tf.modalManager.showModal(
					new TF.Modal.Grid.ManageFilterModalViewModel({
						obAllFilters: leftGrid.obGridFilterDataModelsFromDataBase,
						editFilter: saveAndEditGridFilter,
						applyFilter: applyGridFilter
					})
				).then(function()
				{
					if (!Enumerable.From(leftGrid.obGridFilterDataModelsFromDataBase()).Any(function(c) { return c.id() == leftGrid.obSelectedGridFilterId(); }))
					{
						leftGrid.clearGridFilterClick();
					}
				});
			};

			leftGrid.clearGridFilterClick = function()
			{
				applyGridFilter(null);
			};

			leftGrid.gridFilterClick = function(viewModel)
			{
				applyGridFilter(viewModel);
			};

			let paramData = {
				"@filter": String.format("(eq(dbid, {0})|isnull(dbid,))&eq(datatypeId,{1})&eq(IsValid,true)",
					tf.datasourceManager.databaseId, tf.dataTypeHelper.getId(gridType))
			};
			if (this.options.withRelationShip)
			{
				paramData["@relationships"] = "Reminder,AutoExport";
			}
			tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"), {
				paramData: paramData
			}).then(function(data)
			{
				data.Items.sort(function(a, b)
				{
					return a.Name > b.Name ? 1 : -1;
				});
				var gridFilterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, data.Items);
				leftGrid.obGridFilterDataModelsFromDataBase(gridFilterDataModels);
			});
		}

		function saveAndEditGridFilter(isNew, gridFilterDataModel)
		{
			return tf.modalManager.showModal(
				new TF.Modal.Grid.ModifyFilterModalViewModel(
					gridType,
					isNew,
					gridFilterDataModel,
					null,
					gridDefinition
				)
			).then(function(result)
			{
				if (!result)
				{
					return true;
				}
				if (isNew !== "new")
				{
					gridFilterDataModel.update(result.savedGridFilterDataModel.toData());
				}
				else
				{
					leftGrid.obGridFilterDataModelsFromDataBase.push(result.savedGridFilterDataModel);
				}

				if (result.applyOnSave ||
					(result.savedGridFilterDataModel.id() == leftGrid.obSelectedGridFilterId()))
				{
					applyGridFilter(result.savedGridFilterDataModel);
				}
			});
		}

		function applyGridFilter(filter)
		{
			if (filter)
			{
				leftGrid.obSelectedGridFilterId(filter.id());
				leftGrid.obSelectedGridFilterClause(filter.whereClause());
			} else
			{
				leftGrid.obSelectedGridFilterId(null);
				leftGrid.obSelectedGridFilterClause("");
			}

			self.getAllRecords().then(function()
			{
				self.setDataSource();
			});
		}

		tf.contextMenuManager.showMenu(
			e.target,
			new TF.ContextMenu.TemplateContextMenu(
				"workspace/grid/FilterContextMenu",
				new TF.Grid.GridMenuViewModel((function()
				{
					var gridViewModel = {};
					gridViewModel.obShowSplitmap = ko.observable(false);
					return gridViewModel;
				})(), leftGrid)
			));
	};

	KendoListMoverWithSearchControlViewModel.prototype.onEnableCheckboxClick = function()
	{
		this.obShowEnabled(!this.obShowEnabled());

		if (this.leftSearchGrid)
		{
			if (this.options.serverPaging)
			{
				this.leftSearchGrid.triggerRefreshClick();
				this.createKendoDropTargetEvent();
			}
			else
			{
				this.getAllRecords().then(function()
				{
					this.setDataSource("left");
					this.createKendoDropTargetEvent();
				}.bind(this));
			}
		}
		return true;
	};

	KendoListMoverWithSearchControlViewModel.prototype.onAddClick = function(viewModel, el)
	{

	};

	KendoListMoverWithSearchControlViewModel.prototype.addRawImageColumn = function(allColumns)
	{
		var rawImage = {
			FieldName: "RawImage",
			DisplayName: " ",
			Width: "35px",
			sortable: false,
			locked: false,
			type: "nofilter",
			isSortItem: false,
			template: function(arg)
			{
				var url = "data:image/jpeg;base64," + arg.RawImage;
				return "<img style=\"width:20px; height:20px;\" src=\"" + url + "\" class=\"img-circle\"/>";
			},
			filterable: false
		};
		allColumns.push(rawImage);
	};

	KendoListMoverWithSearchControlViewModel.prototype.addRemoveColumnClick = function(viewModel, el)
	{
		function initHiddenLockedField(column)
		{
			if (typeof (column.hidden) == "undefined")
			{
				column.hidden = false;
			}
			if (typeof (column.locked) == "undefined")
			{
				column.locked = false;
			}
		}
		var listMoverColumns = this.columns || [];
		var availableColumns = [];
		var selectedColumns = [];
		var allColumns = [];

		if (this.obEditCustomDefinitionColumns())
		{
			allColumns = $.extend(true, [], this.customColumnSources);
		}
		else if (this.obEditCurrentDefinitionColumns())
		{
			allColumns = $.extend(true, [], this.getColumnSources(this.options.type));
		}
		else
		{
			var gridType = this.options.type.toLowerCase();
			allColumns = $.extend(true, [], TF.Grid.FilterHelper.getGridDefinitionByType(gridType).Columns.slice(0));
		}
		allColumns = TF.Helper.KendoListMoverHelper.removeOnlyForFilterColumn(allColumns);
		if (this.showRawImageColumn)
		{
			this.addRawImageColumn(allColumns);
		}
		availableColumns = allColumns.slice(0);
		allColumns.forEach(function(item)
		{
			item.hidden = true;
			initHiddenLockedField(item);
		});

		for (var i = 0, l = listMoverColumns.length; i < l; i++)
		{
			var existsColumn = null;
			for (var j = 0, jl = allColumns.length; j < jl; j++)
			{
				if (allColumns[j].FieldName == listMoverColumns[i].FieldName)
				{
					existsColumn = listMoverColumns[i];
					var tempColumn = Enumerable.From(availableColumns).Where("$.FieldName=='" + allColumns[j].FieldName + "'").FirstOrDefault();
					tempColumn.FieldName = "";
					allColumns[j] = existsColumn;
					break;
				}
			}
			var columnClone = $.extend(
				{}, listMoverColumns[i]);
			if (!columnClone.DisplayName || $.trim(columnClone.DisplayName) == "")
			{
				if (columnClone.FieldName !== "RawImage")
				{
					columnClone.DisplayName = columnClone.FieldName;
				}
				else
				{
					columnClone.DisplayName = "Image";
				}
			}
			initHiddenLockedField(columnClone);
			selectedColumns.push(columnClone);
			if (!existsColumn)
			{
				allColumns.unshift(columnClone);
			}
		}
		var self = this;
		availableColumns = Enumerable.From(availableColumns).Where("$.FieldName!=''").ToArray();
		tf.modalManager.showModal(
			new TF.Modal.Grid.EditKendoColumnModalViewModel(
				availableColumns,
				selectedColumns,
				self.defaultColumns || self.originalColumns
			)
		).then(function(editColumnViewModel)
		{
			if (!editColumnViewModel)
			{
				return;
			}
			var enumerable = Enumerable.From(self.originalColumns);
			// reset column setting to default
			editColumnViewModel.selectedColumns = editColumnViewModel.selectedColumns.map(function(item)
			{
				var oc = enumerable.Where("$.FieldName=='" + item.FieldName + "'").FirstOrDefault();
				return oc || item;
			});

			self.columns = editColumnViewModel.selectedColumns;
			self.saveCurrentSelectedColumns(self.options.type, self.columns);
			self.leftSearchGrid._gridDefinition.Columns = editColumnViewModel.selectedColumns;
			self.leftSearchGrid.rebuildGrid();
			if (self.options.updateColumnGrid == null || self.options.updateColumnGrid == 'right')
			{
				self.rightSearchGrid._gridDefinition.Columns = editColumnViewModel.selectedColumns;
				self.rightSearchGrid.rebuildGrid();
			}

			// columns is changed, need to request data again.
			if (!self.options.serverPaging)
			{
				self.getAllRecords().then(function()
				{
					self.setDataSource();
				});
			}
		});
	};

	KendoListMoverWithSearchControlViewModel.prototype.refreshClick = function()
	{
		var self = this;
		this.saveCurrentSelectedColumns(this.options.type, this.columns);
		this.leftSearchGrid._gridDefinition.Columns = this.columns;
		tf.loadingIndicator.show();
		this.leftSearchGrid.rebuildGrid();
		this.rightSearchGrid._gridDefinition.Columns = this.columns;
		this.rightSearchGrid.rebuildGrid();
		if (!this.options.serverPaging)
		{
			this.getAllRecords().then(function()
			{
				return self.setDataSource();
			}).finally(() =>
			{
				tf.loadingIndicator.tryHide();
			});
		}
		else
		{
			tf.loadingIndicator.tryHide();
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype._initGrids = function(el)
	{
		this.$form = $(el);
		this.availableColGridContainer = this.$form.find(".availablecolumngrid-container");
		this.selectedColGridContainer = this.$form.find(".selectedcolumngrid-container");
		var stickyColumns = this.getCurrentSelectedColumns(this.options.type);
		if (stickyColumns && (!this.columns || this.columns.length == 0))
		{
			this.columns = stickyColumns;
		}
		this.columns.map(function(item)
		{
			if (item.FieldName == "RawImage")
			{
				item.template = function(arg)
				{
					var url = "data:image/jpeg;base64," + arg.RawImage;
					return "<img style=\"width:20px; height:20px;\" src=\"" + url + "\" class=\"img-circle\"/>";
				};
			}
		});
		this.originalColumns = this.getColumnSources(this.options.type) || this.originalColumns;
		tf.shortCutKeys.createSpecialHashMap(tf.shortCutKeys._currentHashKey);
		this.initRightGrid();
		this.initLeftGrid();
		this.leftSearchGrid && (this.leftSearchGrid.isListMoverWithSearch = true)
		this.rightSearchGrid && (this.rightSearchGrid.isListMoverWithSearch = true)
		this.getAllRecords().then(function()
		{
			this.setDataSource();
			this.afterInit();
			if (this.obDisableControl) this.obDisableControl(this.obSelectedData().length === 0)
		}.bind(this));
	};

	KendoListMoverWithSearchControlViewModel.prototype.init = function(viewModel, el)
	{
		this._initGrids(el);
	};

	KendoListMoverWithSearchControlViewModel.prototype.saveCurrentSelectedColumns = function(gridType, columns)
	{
		if (this.obEditCustomDefinitionColumns())
		{
			gridType = this.customStorageKey + gridType;
		}

		return tf.storageManager.save(tf.storageManager.listMoverCurrentSelectedColumns(gridType, tf.authManager.authorizationInfo.authorizationTree.username), columns);
	};

	KendoListMoverWithSearchControlViewModel.prototype.getCurrentSelectedColumns = function(gridType)
	{
		if (this.obEditCustomDefinitionColumns())
		{
			gridType = this.customStorageKey + gridType;
		}

		return tf.storageManager.get(tf.storageManager.listMoverCurrentSelectedColumns(gridType, tf.authManager.authorizationInfo.authorizationTree.username));
	};

	KendoListMoverWithSearchControlViewModel.prototype.createKendoDropTargetEvent = function()
	{
		var self = this;
		this.createKendoDropTargetEventTimeout = setTimeout(function()
		{
			this.rightSearchGrid && this.rightSearchGrid.$container.find("tbody tr").kendoDropTarget(
				{
					dragenter: function(e)
					{
						if (self.options.disableDropIndicator)
						{
							return;
						}
						if (!self._isDragItem(e))
							return;

						var targetItem = $(e.dropTarget[0]);
						targetItem.addClass("drag-target-insert-after-cursor");

						this._appendDropTargetCursorTriangle(targetItem);
					}.bind(this),
					dragleave: function(e)
					{
						$(e.dropTarget[0]).removeClass("drag-target-insert-after-cursor");
						this._removeDropTargetCursorTriangle();
					}.bind(this),
					drop: this._selectedDrop.bind(this)
				});
		}.bind(this), 1000);
	};

	KendoListMoverWithSearchControlViewModel.prototype.onLeftGridChange = function(e, rowsData)
	{
		var rowsDataFiltered = this._onLeftGridChangeSelectable(e, rowsData);
		this._obLeftSelData(rowsDataFiltered);
		if (this._obLeftSelData().length > 0)
		{
			this._clearRightSelection();
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype._onLeftGridChangeSelectable = function(e, rowsData)
	{
		var self = this;
		if (self.options.getSelectableRecords && !self.changeLeftSelect)
		{
			rowsData = self.options.getSelectableRecords(rowsData, self.selectedData);
			clearTimeout(self.changeSelectIdsTimeout);
			self.changeSelectIdsTimeout = setTimeout(function(rowsData)
			{
				self.changeLeftSelect = true;
				self.leftSearchGrid.getSelectedIds(rowsData.map(function(r) { return r.Id; }));
				self.changeLeftSelect = false;
			}.bind(self, rowsData));
			return rowsData;
		}
		return rowsData;
	};

	KendoListMoverWithSearchControlViewModel.prototype.onRightGridChangeCheck = function(e, selectedItems)
	{
		this.obIsInoperableSelected(this.isOperableCheck(selectedItems));
	};

	KendoListMoverWithSearchControlViewModel.prototype.isOperableCheck = function(selectedItems) { };

	KendoListMoverWithSearchControlViewModel.prototype.onRightGridChange = function(e, rowsData)
	{
		this._obRightSelData(rowsData);

		if (this._obRightSelData().length > 0)
		{
			this._clearLeftSelection();
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.initLeftGrid = function()
	{
		const self = this;
		tf.loadingIndicator.show();
		self._leftShow = true;
		var options = {
			gridDefinition:
			{
				Columns: this.columns
			},
			isBigGrid: true,
			kendoGridOption: this.options.leftKendoGridOption || this.leftKendoGridOption,
			showOmittedCount: false,
			showSelectedCount: true,
			setRequestURL: this.setLeftGridRequestURL.bind(this),
			setRequestOption: this.setLeftRequestOption.bind(this),
			gridType: this.options.type,
			udGridID: this.options.UDGridID,
			isSmallGrid: true,
			url: this.options.getUrl(this.options.type, this.options),
			showBulkMenu: this.options.showBulkMenu,
			showLockedColumn: this.options.showLockedColumn,
			height: this.options.height,
			sort: this.options.sort,
			reorderable: true,
			showLoadingForIds: true,
			totalRecordCount: this.options.totalRecordCount,
			disableAutoComplete: this.options.disableAutoComplete,
			columnReorder: function(e)
			{
				var selectedGrid = $(".selectedcolumngrid-container").data("kendoGrid");
				selectedGrid.reorderColumn(e.newIndex, selectedGrid.columns[e.oldIndex]);
			},
			onDataBound: function()
			{
				this.onBeforeLeftGridDataBound(this.leftSearchGrid);
				this.obAvailableCount(this.leftSearchGrid.obFilteredRecordCount());
				this.obLeftCount(this.leftSearchGrid.kendoGrid.dataSource._total);
				this.bindSearchGridDraggable(this.leftSearchGrid, false);
				this.bindSearchGridDraggable(this.rightSearchGrid, true);
				this.bindLeftGridDropTarget();
				this.bindRightGridDropTarget();
				this.createKendoDropTargetEvent();
				this.onLeftDataBound();
				// this.initGridScrollBar(this.availableColGridContainer);
				this._updateContentTableWidth(this.leftSearchGrid);
				this.updateGridFooter(this.availableColGridContainer);
				if (self._leftShow)
				{
					self._leftShow = false;
					tf.loadingIndicator.tryHide();
				}
			}.bind(this),
			aggregateSearchDataSource: this.options.dataSource,
			dataSourceId: this.options.dataSource,
			delayShortCutKeysBinding: true,
			idSeparator: this.options.idSeparator || false,
			defaultFilterData: this.options.defaultFilterData,
			baseRecordType: this.options.baseRecordType,
		};
		this.initGridOption(options, "left");
		this.leftSearchGrid = new TF.Grid.LightKendoGrid(this.availableColGridContainer, options);
		this.leftSearchGrid.getKendoColumn = this._getLeftColumns;
		this.leftSearchGrid.onRowsChanged.subscribe(this.onLeftGridChange.bind(this));
		this.leftSearchGrid.onDoubleClick.subscribe(this.onLeftDBClick.bind(this));
		this.leftSearchGrid.onIdsChanged.subscribe(() =>
		{
			if (!this.leftSearchGrid)
			{
				return;
			}
			let selectableIds = this.leftSearchGrid.obAllIds().filter(id => !this.fixedLeftIds.includes(id));
			if (this.options.getSelectableRecords)
			{
				const rightSelectedItems = this.rightSearchGrid.kendoGrid.dataSource.data();
				const selectableRecords = this.options.getSelectableRecords(this.allRecords, rightSelectedItems);
				selectableIds = selectableIds.filter(id => selectableRecords.some(o => o.Id === id));
			}
			this.obLeftGridMoveableIds(selectableIds);
			this.toAllRightDisableByCondition();
		});
		this.leftSearchGrid.$container.on("click", function(e)
		{
			tf.shortCutKeys.changeHashKey(options.routeState);
			e.stopPropagation();
			e.preventDefault();
		});

		// this._cancelKendoGridSelectedArea(this.leftSearchGrid.kendoGrid);
	};

	KendoListMoverWithSearchControlViewModel.prototype.updateGridFooter = function($grid)
	{
		if (this.options.noPermission)
		{
			tf.helpers.kendoGridHelper.updateGridFooter($grid, 0, 0);
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype._updateContentTableWidth = function(searchGrid)
	{
		var $table = searchGrid.$container.find(".k-grid-content table"),
			width = $table.width();

		$table.width(width);
	};

	KendoListMoverWithSearchControlViewModel.prototype.getAllRecords = function()
	{
		var self = this;

		if (self.options.getAllRecords)
		{
			return self.options.getAllRecords().then(result =>
			{
				self.allRecords = result;
				return self.allRecords;
			});
		}

		if (self.options.serverPaging || this.options.noPermission)
		{
			return Promise.resolve();
		}

		var requestOption = self.setLeftRequestOption({
			data: {},
			paramData: {}
		});
		if (this.options.GridType === 'Role')
		{
			requestOption.data.idFilter = { ExcludeAny: [-999] };
		}
		else
		{
			requestOption.data.idFilter = {};
		}

		if (self.options.GridType !== 'Form')
		{
			self._addSortItemIntoRequest(requestOption);
		}

		if (self.options.UDFId)
		{
			requestOption.data.fields = self.columns.map((c) => c.DisplayName);
			requestOption.data.sortItems.map((item) => item.Name = tf.UDFDefinition.getOriginalName(item.Name));
		}

		if (this.leftSearchGrid && this.leftSearchGrid.obSelectedGridFilterClause)
		{
			requestOption.data.filterClause = this.leftSearchGrid.obSelectedGridFilterClause();
		}
		var promise;
		if (self.options.queryBySelectedColumns)
		{
			requestOption.data.fields = self.columns.map((c) => c.OriginalName || c.FieldName).concat('Id');
		}

		if (self.options.getRequiredColumns)
		{
			requestOption.data.fields = requestOption.data.fields.concat(self.options.getRequiredColumns());
		}

		switch (self.options.GridType)
		{
			case 'GPSEventType':
				promise = TF.Helper.VehicleEventHelper.getEventTypes(0, self.options.parentPageName === "locationevent");
				break;
			case 'Grades':
			case 'StaffTypes':
			case 'GeneralDataListsDisabilityCode':
			case 'Genders':
			case 'schoollocation':
			case 'Tags':
			case 'StudentTags':
			case 'auditlog':
				promise = tf.promiseAjax.get(self.options.getUrl(self.options.type, self.options));
				break;
			case 'Form':
				let filterField = self.options.filterField;
				let udGridId = self.options.UDGridID;
				const params = { databaseId: tf.datasourceManager.databaseId, formId: udGridId, questionGuid: filterField };
				promise = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), `udgridrecords/udgridfielddistinctvalues`), {paramData: params});
				break;
			default:
				promise = tf.promiseAjax.post(self.setLeftGridRequestURL(self.options.getUrl(self.options.type, self.options)), requestOption);
				break;
		}

		return promise
			.then(function(response)
			{
				if (!self.options) { return; }
				// Rewrite the schools field value to avoid the frontend filter issue.
				if ((self.dataType === 'trip' || self.options.type === 'trip') && !self.options.serverPaging)
				{
					response.Items.forEach(trip =>
					{
						trip.Schools = trip.Schools.replace(/!/g, ", ").trim().tfTrimEnd(',');
					});
				}

				// Preprocess StaffTypes
				if (self.options.GridType === 'StaffTypes')
				{
					response.Items.forEach(x => x.Id = x.StaffTypeId);
					response.Items.sort((x, y) => (x.StaffTypeName.toLowerCase() > y.StaffTypeName.toLowerCase() ? 1 : -1));
				}

				// Preprocess Tags
				if (self.options.GridType === 'Tags')
				{
					const fromGridTypeId = tf.dataTypeHelper.getDataTypeConfig(self.options.fromGridType).id;
					response.Items = response.Items.filter(x => !x.TagDataTypes || x.TagDataTypes.length == 0 || x.TagDataTypes.some(c => c.DataTypeId == fromGridTypeId));
					response.Items.sort((x, y) => (x.Name.toLowerCase() > y.Name.toLowerCase() ? 1 : -1));
				}

				// Preprocess Trip Student Tags
				if (self.options.GridType === 'StudentTags')
				{
					response.Items = response.Items.filter(x => !x.TagDataTypes || x.TagDataTypes.length == 0 || x.TagDataTypes.some(c => c.DataTypeId == tf.dataTypeHelper.getId("student")));
					response.Items.sort((x, y) => (x.Name.toLowerCase() > y.Name.toLowerCase() ? 1 : -1));
				}

				// Preprocess Genders
				if (self.options.GridType === 'Genders')
				{
					self.selectedData.forEach(x => x.Id = x.ID);
					response.Items.forEach(x => x.Id = x.ID);
					response.Items.sort((x, y) => (x.Name.toLowerCase() > y.Name.toLowerCase() ? 1 : -1));
				}

				// Preprocess GPSEventType
				if (self.options.GridType === 'GPSEventType' && TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled)
				{
					self.selectedData.forEach(x => x.Id = x.ID);
					response.Items.forEach(x => x.Id = x.ID);
				}

				// Preprocess Audit Log
				if (self.options.GridType === 'auditlog') 
				{
					const isDataSources = self.options.type === "Data Sources";

					self.selectedData.forEach(x => {
						x.Id = isDataSources ? x.DBID : x.ID;
					});
					response.Items.forEach(x => {
						x.Id = isDataSources ? x.DBID : x.ID;
					});
				}

				// Preprocess Form
				if (self.options.GridType === 'Form')
				{
					let records = [];
					let valueFieldName = self.options.filterField;
					response.Items?.forEach((x) => 
					{
						// prepare field data: from {key: "", value: ""} to {Id: (key), _guid: (value)}
						let fieldData =  JSON.parse(x);
						if (!fieldData || fieldData.length === 0) return;
						fieldData = fieldData[0];
						let item = {'Id': fieldData.key};
						item[valueFieldName] = fieldData.value;
						records.push(item);
					});

					self.processAllRecords(records);

					return Promise.resolve();
				}

				/**
				 * preprocess response of UDF list
				 */
				if (self.options.UDFId && response && response.Items)
				{
					response.Items = response.Items.filter(function(item)
					{
						return !!item[self.options.OriginalName];
					}).map(function(item)
					{
						var result = $.extend({}, item);
						result[tf.UDFDefinition.getFieldNameById(self.options.UDFId)] = item[self.options.OriginalName];
						return result;
					});

					response.Items = _.uniqBy(response.Items, self.options.OriginalName);
				}

				if (!self.obshowRemoveColumnButton() && !self.options.skipEmptyAndDuplicateCheck)
				{
					// remove empty items
					response.Items = (response.Items || []).filter(function(item)
					{
						return self.columns.some(function(c) { return !!item[c.FieldName]; });
					});

					// remove duplicated value
					response.Items = _.uniqBy(response.Items || [], function(item)
					{
						return self.columns.map(function(c)
						{
							return item[c.FieldName] || "";
						}).join("_*&^@_");// to avoid accident
					});
				}

				return self._getSourceFromResponse(response, self.selectedData).then(function(records)
				{
					self.processAllRecords(records);
				});
			});
	};

	KendoListMoverWithSearchControlViewModel.prototype.processAllRecords = function(records)
	{
		let self = this;
		self.allRecords = records;
		if (self.options.invalidIds &&
			self.options.invalidIds.length > 0)
		{
			self.allRecords = self.allRecords.filter(function(item)
			{
				return !Enumerable.From(self.options.invalidIds).Any("$=='" + item.Id + "'");
			});
		}
		// change all records data to use selected data, because some time selected data is temp changed by user
		self.selectedData.forEach(function(data)
		{
			for (var i = 0; i < self.allRecords.length; i++)
			{
				if (self.allRecords[i].Id == data.Id)
				{
					for (var key in self.allRecords[i])
					{
						if (self.allRecords[i].hasOwnProperty(key) && data.hasOwnProperty(key))
						{
							self.allRecords[i][key] = data[key];
						}
					}
				}
			}
		});
	}

	KendoListMoverWithSearchControlViewModel.prototype._getSourceFromResponse = function(response)
	{
		var self = this;
		var data = $.isArray(response.Items[0]) ? response.Items[0] : response.Items;
		self.changeSourceDataToGridAcceptData(data);
		var records = data;
		if (self.options.modifySource)
		{
			records = self.options.modifySource(records);
		}
		return Promise.resolve(records);
	};

	KendoListMoverWithSearchControlViewModel.prototype._addSortItemIntoRequest = function(requestOption)
	{
		var self = this;

		// build sortItem
		var rawSortItemArray = [];
		if (self.columns && self.columns.length > 0)
		{
			self.columns.forEach(function(column, idx)
			{
				if (column.isSortItem)
				{
					var rawSortItem = {
						fieldName: column.FieldName,
						sortIdx: column.sortIdx ? column.sortIdx : idx + 1
					};
					rawSortItemArray.push(rawSortItem);
				}
			});
		}

		rawSortItemArray.sort(function(a, b)
		{
			return a.sortIdx - b.sortIdx;
		});

		if (rawSortItemArray.length > 0)
		{
			requestOption.data.sortItems = requestOption.data.sortItems || [];

			rawSortItemArray.forEach(function(sortItem)
			{
				sortItem = {
					Name: sortItem.fieldName,
					Direction: "Ascending",
					isAscending: true
				};
				requestOption.data.sortItems.push(sortItem);
			});
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.changeSourceDataToGridAcceptData = function(data)
	{
		var self = this;
		data.forEach(function(item)
		{
			self.columns.forEach(function(define)
			{
				if (item[define.FieldName] !== null &&
					item[define.FieldName] !== undefined)
				{
					switch (define.type)
					{
						case "datetime":
							item[define.FieldName] = new Date(item[define.FieldName]);
							break;
						case "date":
							item[define.FieldName] = moment(moment(new Date(item[define.FieldName])).format("L")).toDate();
							break;
						case "time":
							let time = item[define.FieldName];
							item[define.FieldName] = moment(moment(time.length === 8 && time.includes(":") ? `1899/12/30 ${time}` : time).format("1899/12/30 HH:mm:00")).toDate();
							break;
						case "boolean":
							item[define.FieldName] = item[define.FieldName].toString();
							break;
					}
				}
			});
		});
	};

	KendoListMoverWithSearchControlViewModel.prototype.setDataSource = function(gridType)
	{
		if (this.options.serverPaging)
		{
			return;
		}
		const self = this, promise = new Promise(resolve =>
		{
			this.setDataSourceTimeout = setTimeout(function()
			{
				var leftData = [];
				self._updateUdfColumns(self.allRecords);
				const { leftTempData, rightData } = self.composeGridData();

				leftData = leftTempData;
				if (self.options.filterSelectableRecords && rightData.length)
				{
					leftData = self.options.filterSelectableRecords(leftTempData, rightData);
				}

				if (gridType === "left" || !gridType)
				{
					TF.fixGeometryErrorInKendo(leftData);
					self.leftSearchGrid.kendoGrid.dataSource.data(leftData);
					self.leftSearchGrid.kendoGrid.dataSource.options.data = leftData;
					self.leftSearchGrid.kendoGrid.dataSource.transport.data = leftData;
					self.leftSearchGrid.kendoGrid.dataSource._ranges[0].start = 0;
					self.leftSearchGrid.kendoGrid.dataSource._ranges[0].end = leftData.length;
					self.leftSearchGrid.kendoGrid.setDataSource(self.leftSearchGrid.kendoGrid.dataSource);
				}
				if (gridType === "right" || !gridType)
				{
					self.selectedData.forEach(function(item, i)
					{
						var d = rightData.find(x => (x.Id || x.id) == (item.Id || item.id));
						if (d)
						{
							self.selectedData[i] = d;
						}
					});

					if (self.options.filterField)
					{
						self.selectedData = TF.ListMoverForListFilterHelper.processSelectedData(self.selectedData, self.options.filterField);
						self.selectedData.sort(function(a, b) { return a.FilterItem ? a.FilterItem.localeCompare(b.FilterItem) : 0; });
					}

					if (self.options.sortRightLeftGrid)
					{
						var sortName = self.options.sort.Name;
						self.options.sortRightLeftGrid(self.selectedData, sortName);
					}
					TF.fixGeometryErrorInKendo(self.selectedData);
					self.rightSearchGrid.kendoGrid.dataSource.data(self.selectedData);
					self.rightSearchGrid.kendoGrid.dataSource.options.data = self.selectedData;
					self.rightSearchGrid.kendoGrid.dataSource.transport.data = self.selectedData;
					self.rightSearchGrid.kendoGrid.dataSource._ranges[0].start = 0;
					self.rightSearchGrid.kendoGrid.dataSource._ranges[0].end = self.selectedData.length;
					self.rightSearchGrid.kendoGrid.setDataSource(self.rightSearchGrid.kendoGrid.dataSource);
				}
				self._changeLeftGridSelectable();
				resolve();
			});
		});
		return promise;
	};

	KendoListMoverWithSearchControlViewModel.prototype.composeGridData = function()
	{
		const self = this;
		var leftTempData = [];
		var rightData = [];

		self.allRecords.filter(function(item)
		{
			if (Enumerable.From(self.selectedData).Any("$.Id=='" + item.Id + "'"))
			{
				rightData.push(item);
			}
			else
			{
				leftTempData.push(item);
			}
		});

		return { leftTempData, rightData };
	}


	KendoListMoverWithSearchControlViewModel.prototype._updateUdfColumns = function(allRecords)
	{
		let udfs = _.uniqBy(this.columns.filter(c => !!c.OriginalName), "UDFId");
		if (!udfs.length)
		{
			return;
		}

		allRecords.forEach(item =>
		{
			udfs.forEach(udf =>
			{
				item[udf.FieldName] = item[udf.OriginalName];
			});
		});
	}

	KendoListMoverWithSearchControlViewModel.prototype.initGridOption = function(options, gridType)
	{
		var self = this;
		if (this.options.noPermission)
		{
			options.noRecords = {
				template: this.options.noPermission
			};
			options.dataSource = [];
		}
		else if (this.options.serverPaging === false)
		{
			options.kendoGridOption = {
				filterable:
				{
					extra: false,
					mode: "row"
				},
				dataSource:
				{
					pageSize: 100,
					serverPaging: false,
					serverFiltering: false,
					serverSorting: false,
					transport: null
				},
				pageable: true
			};
			var originalDataBound = options.onDataBound;
			options.onDataBound = function()
			{
				if (originalDataBound)
				{
					originalDataBound();
				}
				self._changePageInfoDisplay(gridType);
			}.bind(this);

			// options.dataSource = this.allRecords;
			// options.kendoGridOption.dataSource.data = this.allRecords;
		}
		options.routeState = self.obGuid() + "_" + "ListMover" + gridType + "KendoGrid";
	};

	KendoListMoverWithSearchControlViewModel.prototype._changePageInfoDisplay = function(gridType)
	{
		var self = this;
		if (this.options.changePage != null)
		{
			return;
		}
		this.changePageInfoDisplayTimeout = setTimeout(function()
		{
			if (!this.allRecords)
			{
				return;
			}
			this.maxReocrdCount = !this.maxReocrdCount ? this.allRecords.length : (this.maxReocrdCount < this.allRecords.length ? this.allRecords.length : this.maxReocrdCount);
			var grid = gridType == "left" ? this.leftSearchGrid : this.rightSearchGrid;

			var $pageInfo = grid.$container.children(".k-pager").find(".pageInfo");
			var gridTotal = grid.kendoGrid.dataSource.total();
			var pageInfoText = gridTotal + " of " + this.maxReocrdCount;
			if (self.options.getSelectableRecords && !this.useMaxRecordCount)
			{
				var rightSelectedItems = self.rightSearchGrid.kendoGrid.dataSource.data();
				var selectableRecords = self.options.getSelectableRecords(self.allRecords, rightSelectedItems);
				var totalSameCount = selectableRecords.length;
				var additionText = "";
				if (totalSameCount == 0)
				{
					totalSameCount = this.allRecords.length;
				}
				if (gridType == "left" && !self.options.hideTotalFromLeftGrid)
				{
					gridTotal = self.options.getSelectableRecords(self._getFilterData(grid.kendoGrid.dataSource), rightSelectedItems).length;
					additionText = " (" + this.allRecords.length + " Total)";
				}
				pageInfoText = gridTotal + " of " + totalSameCount + additionText;
			}
			$pageInfo.html(pageInfoText);
		}.bind(this));
	};

	KendoListMoverWithSearchControlViewModel.prototype.onBeforeLeftGridDataBound = function(leftSearchGrid)
	{

	};

	// KendoListMoverWithSearchControlViewModel.prototype.initGridScrollBar = function(container)
	// {
	// 	var $gridContent = container.find(".k-grid-content");
	// 	$gridContent.css(
	// 	{
	// 		"overflow-y": "auto"
	// 	});
	// };

	KendoListMoverWithSearchControlViewModel.prototype.onLeftDBClick = function(e, rowData)
	{
		if (rowData)
		{
			var selectable = true;
			if (this.options.getSelectableRecords)
			{
				selectable = this.options.getSelectableRecords([rowData], this.selectedData).length > 0;
			}

			if (selectable)
			{
				this._obLeftSelData([rowData]);
				this._moveItem(true);
			}
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.setLeftGridRequestURL = function(url)
	{
		if (this.options.dataSource)
		{
			if (this.options.type === "gpsevent")
			{
				return TF.Helper.VehicleEventHelper.getRequestURL();
			}

			if (this.options.type === "street")
			{
				return TF.StreetHelper.getGridRequestURL();
			}

			if (this.options.type === "parceladdresspoint")
			{
				return `${tf.api.apiPrefix()}/search/parcelpoints`;
			}

			return pathCombine(tf.api.apiPrefixWithoutDatabase(), tf.dataTypeHelper.getDataTypeConfig(this.options.type).hasDBID === false ? "" : this.options.dataSource, "search", tf.dataTypeHelper.getEndpoint(this.options.type));
		}
		return url;
	};

	KendoListMoverWithSearchControlViewModel.prototype.setLeftRequestOption = function(requestOptions)
	{
		// delete requestOptions.paramData.take;
		// delete requestOptions.paramData.skip;

		var excludeIds = this.obSelectedData ? this.obSelectedData() : [];
		if (this.options && this.options.gridOptions && this.options.gridOptions.excludeIds && this.options.gridOptions.excludeIds.length > 0)
		{
			excludeIds = excludeIds.concat(this.options.gridOptions.excludeIds);
		}
		excludeIds = excludeIds.map(function(item)
		{
			if ($.isNumeric(item))
			{
				return item;
			}
			return item.Id;
		});

		requestOptions.data.idFilter = {
			ExcludeAny: excludeIds
		};

		if (this.options && this.options.gridOptions && this.options.gridOptions.filter && !this.obShowEnabledCopmuter())
		{
			requestOptions.data.filterSet = requestOptions.data.filterSet ||
			{
				FilterItems: [],
				FilterSets: [],
				LogicalOperator: "and"
			};
			if ($.isArray(this.options.gridOptions.filter))
			{
				this.options.gridOptions.filter.forEach(function(f)
				{
					requestOptions.data.filterSet.FilterItems.push(f);
				});
			}
			else
			{
				requestOptions.data.filterSet.FilterItems.push(this.options.gridOptions.filter);
			}
		}

		if (this.options && this.options.filterSetField && this.obShowEnabledCopmuter())
		{
			requestOptions.data.filterSet = requestOptions.data.filterSet ||
			{
				FilterItems: [],
				FilterSets: [],
				LogicalOperator: "and"
			};
			if (this.options.filterSetField === "InProgress")
			{
				requestOptions.data.filterSet.FilterItems.push(
					{ "FieldName": "InProgress", "Operator": "EqualTo", "Value": "true", "TypeHint": "Bit" });
			}
			else
			{
				requestOptions.data.filterSet.FilterItems.push(
					{
						"FieldName": this.options.filterSetField,
						"Operator": "EqualTo",
						"Value": true
					});
			}
		}
		if (this.getFields)
		{
			requestOptions.data.fields = this.getFields()?.map(f => tf.UDFDefinition.getOriginalName(f));
		}

		if (this.options && this.options.setRequestOption)
		{
			var setRequestOptionResult = this.options.setRequestOption(requestOptions);
			if (setRequestOptionResult)
			{
				requestOptions = setRequestOptionResult;
			}
		}

		this.addRequestOptions("left", requestOptions);
		this.resolveSortItems && this.resolveSortItems(requestOptions);
		this.disableGetCount(requestOptions);
		if (!['route', 'tags'].includes((this.dataType || this.options?.type).toLowerCase()))
		{
			requestOptions.paramData.convertMeasurement = true;
		}
		return requestOptions;
	};

	// serverPaging means use skip and take param when call api.
	// API now has a bug: it should sort before use skip and take.
	// So here we ensure there is a sort item when use skip and take.
	// Once API fixes the bug, this method can be removed.
	KendoListMoverWithSearchControlViewModel.prototype.resolveSortItems = function(requestOptions)
	{
		if (this.options?.serverPaging)
		{
			var sortItems = requestOptions.data.sortItems || [];

			if (!sortItems.length)
			{
				var fields = this.getColumnSources(this.options.type);
				sortItems.push({ Name: fields[0].FieldName, Direction: "Ascending", isAscending: true });
				requestOptions.data.sortItems = sortItems;
			}
			else
			{
				sortItems = sortItems.filter(s => (s.Name || "").toLowerCase() !== "id");
				requestOptions.data.sortItems = sortItems;
			}
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.leftKendoGridOption = {
		filterable:
		{
			extra: false,
			mode: "row"
		},
		dataSource:
		{
			pageSize: 100
		},
		// selectable: "row",
		pageable: true
	};

	KendoListMoverWithSearchControlViewModel.prototype.addRequestOptions = function(gridType, requestOptions)
	{
		if (this.options?.type === "recordcontact" && this.options?.gridOptions?.filter)
		{
			let filterItems = [];

			if ($.isArray(this.options.gridOptions.filter))
			{
				filterItems = this.options.gridOptions.filter;
			}
			else
			{
				filterItems.push(this.options.gridOptions.filter);
			}

			const dataTypeIdFilterItem = filterItems.find(f => f.FieldName && f.FieldName.toLowerCase() === "datatypeid");

			if (dataTypeIdFilterItem != null)
			{
				requestOptions.paramData.dataTypeId = dataTypeIdFilterItem.Value;
			}

			requestOptions.data.filterSet = requestOptions.data.filterSet || {
				FilterItems: [],
				FilterSets: [],
				LogicalOperator: "and"
			};

			if (gridType === "right")
			{
				if ($.isArray(this.options.gridOptions.filter))
				{
					this.options.gridOptions.filter.some(f => f.FieldName && f.FieldName.toLowerCase() === "recordid") && requestOptions.data.filterSet.FilterItems.push(this.options.gridOptions.filter.find(f => f.FieldName && f.FieldName.toLowerCase() === "recordid"));
				}
				else
				{
					this.options.gridOptions.filter && this.options.gridOptions.filter.FieldName && this.options.gridOptions.filter.FieldName.toLowerCase() === "recordid" && requestOptions.data.filterSet.FilterItems.push(this.options.gridOptions.filter);
				}
			}

			if (this.options.gridOptions.contactEmailFilter)
			{
				requestOptions.data.filterSet.FilterItems.push(this.options.gridOptions.contactEmailFilter);
			}
		}

		if (this.options?.serverPaging && this.options?.type !== "recordcontact")
		{
			requestOptions.data.fields = (requestOptions.data.fields || []).concat(this.columns.filter(c => c.hidden).map(c => c.FieldName));
			this.options?.filterField && delete requestOptions.data.fields;
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.initRightGrid = function()
	{
		const self = this;
		tf.loadingIndicator.show();
		self._rightShow = true;
		var options = {
			gridDefinition:
			{
				Columns: this.columns
			},
			isBigGrid: true,
			kendoGridOption: this.rightKendoGridOption(),
			showOmittedCount: false,
			showSelectedCount: true,
			setRequestOption: this.setRightRequestOption.bind(this),
			gridType: this.options.type,
			isSmallGrid: true,
			url: this.options.getUrl(this.options.type, this.options),
			showBulkMenu: this.options.showBulkMenu,
			showLockedColumn: this.options.showLockedColumn,
			height: this.options.height,
			sort: this.options.sort,
			totalRecordCount: this.options.totalRecordCount,
			columnReorder: function(e)
			{
				var availableGrid = $(".availablecolumngrid-container").data("kendoGrid");
				availableGrid.reorderColumn(e.newIndex, availableGrid.columns[e.oldIndex]);
			},
			onDataBound: function()
			{
				this.onRightDataBound(this.rightSearchGrid);
				this.obRightCount(this.rightSearchGrid.kendoGrid.dataSource._total);
				this._updateContentTableWidth(this.rightSearchGrid);
				this.updateGridFooter(this.selectedColGridContainer);
				if (self._rightShow)
				{
					self._rightShow = false;
					tf.loadingIndicator.tryHide();
				}
			}.bind(this),
			dataSourceId: this.options.dataSource,
			delayShortCutKeysBinding: true,
			idSeparator: this.options.idSeparator || false,
			baseRecordType: this.options.baseRecordType,
		};

		this.initGridOption(options, "right");
		this.rightSearchGrid = new TF.Grid.LightKendoGrid(this.selectedColGridContainer, options);
		this.rightSearchGrid.getKendoColumn = this._getRightColumns;
		this.rightSearchGrid.onRowsChangeCheck.subscribe(this.onRightGridChangeCheck.bind(this));
		this.rightSearchGrid.onRowsChanged.subscribe(this.onRightGridChange.bind(this));
		this.rightSearchGrid.onDoubleClick.subscribe(this.onRightDBClick.bind(this));
		this.rightSearchGrid.onIdsChanged.subscribe(() =>
		{
			this.obRightGridMoveableIds(this.rightSearchGrid.obAllIds().filter(id => !this.fixedRightIds.includes(id)));
		});

		this.rightSearchGrid.$container.on("click", function(e)
		{
			tf.shortCutKeys.changeHashKey(options.routeState);
			e.stopPropagation();
			e.preventDefault();
		});

		// this._cancelKendoGridSelectedArea(this.rightSearchGrid.kendoGrid);
	};

	KendoListMoverWithSearchControlViewModel.prototype.onLeftDataBound = function()
	{
		var self = this,
			checkSelectable = this.options.getSelectableRecords,
			checkUnselectable = this.fixedLeftIds && this.fixedLeftIds.length;

		if (!checkSelectable && !checkUnselectable)
		{
			return;
		}

		self.leftSearchGrid.$container.find(".k-grid-content table.k-grid-table tr").map(function(idx, row)
		{
			const $row = $(row);
			const dataItem = self.leftSearchGrid.kendoGrid.dataItem($row);
			const selectable = !checkSelectable || self.options.getSelectableRecords([dataItem], self.selectedData).length;
			const unselectable = checkUnselectable && self.fixedLeftIds.includes(dataItem.Id);

			if (!selectable || unselectable)
			{
				$row.addClass("disSelectable");
			}
		});
	};

	KendoListMoverWithSearchControlViewModel.prototype.toAllRightDisableByCondition = function()
	{
		if (this.options.getSelectableRecords && !this.obRegardlessCriteria())
		{
			// make all right disable when not same
			var allLeftData = this._getFilterData(this.leftSearchGrid.kendoGrid.dataSource);
			var selectedDataCount = this.selectedData.length;
			var compare = selectedDataCount > 0 ? this.selectedData : [allLeftData[0]];
			var leftSameCount = this.options.getSelectableRecords(allLeftData, compare).length;

			let disableMoveAll = this.options.singleTypeData ? leftSameCount === 0 : ((leftSameCount != allLeftData.length && selectedDataCount == 0)
				|| (selectedDataCount > 0 && leftSameCount == 0))
			if (disableMoveAll)
			{
				this.obLeftGridMoveableIds([]);
			}
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.onRightDataBound = function(rightSearchGrid)
	{
		const checkIncluding = this.allRecords && this.allRecords.length;
		const checkExcluding = this.fixedRightIds && this.fixedRightIds.length;

		if (checkIncluding || checkExcluding)
		{
			rightSearchGrid.$container.find(".k-grid-content table.k-grid-table tr")
				.map((idx, row) =>
				{
					var dataItem = rightSearchGrid.kendoGrid.dataItem(row);
					if (!!dataItem && ((checkIncluding && this.allRecords.every(o => o.Id !== dataItem.Id))
						|| (checkExcluding && this.fixedRightIds.includes(dataItem.Id))))
					{
						$(row).addClass("disSelectable");
					}
				});
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.onRightDBClick = function(e, rowData)
	{
		if (rowData)
		{
			this._obRightSelData([rowData]);
			this._moveItem();
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.setRightRequestOption = function(requestOptions)
	{
		if (!this.options.serverPaging)
		{
			delete requestOptions.paramData.take;
			delete requestOptions.paramData.skip;
		}

		var includeIds = this.obSelectedData ? this.obSelectedData() : [];
		includeIds = includeIds.map(function(item)
		{
			if ($.isNumeric(item))
			{
				return item;
			}
			return item.Id;
		});
		requestOptions.data.idFilter = { IncludeOnly: includeIds };
		if (this.getFields)
		{
			requestOptions.data.fields = this.getFields()?.map(f => tf.UDFDefinition.getOriginalName(f));
		}

		this.addRequestOptions("right", requestOptions);
		this.resolveSortItems(requestOptions);
		this.disableGetCount(requestOptions);
		if (!['route', 'tags'].includes((this.dataType || this.options?.type).toLowerCase()))
		{
			requestOptions.paramData.convertMeasurement = true;
		}
		return requestOptions;
	};

	KendoListMoverWithSearchControlViewModel.prototype.disableGetCount = function(requestOptions)
	{
		if (this.dataType === "gpsevent" || this.options?.type === "recordcontact")
		{
			requestOptions.paramData.getCount = false;
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.rightKendoGridOption = function()
	{
		return {
			filterable: false,
			dataSource:
			{
				pageSize: 100,
				serverPaging: this.options.serverPaging,
				serverFiltering: false,
				serverSorting: this.options.serverPaging
			},
			pageable: true
		};
	};

	KendoListMoverWithSearchControlViewModel.prototype._getLeftColumns = function()
	{
		var self = this;
		var currentColumns = this._gridDefinition.Columns.map(x => $.extend({}, x));
		var columns = currentColumns.map(function(definition)
		{
			var column = definition;
			column.field = definition.FieldName;
			column.title = definition.DisplayName;
			column.width = definition.Width || KendoListMoverWithSearchControlViewModel.defaults.columnWidth;
			column.hidden = definition.hidden; // Overwrite the value of hidden attribute which setting in api.
			// column.filterable = {
			// 	cell:
			// 	{
			// 		showOperators: false,
			// 		operator: "contains"
			// 	}
			// };
			self.setColumnFilterableCell(column, definition, "listmover");
			if (column.filterable &&
				column.filterable.cell)
			{
				column.filterable.cell.showOperators = false;
			}
			if (definition.AllowSorting === false)
			{
				column.sortable = false;
			}
			if (definition.template !== undefined)
			{
				column.template = definition.template;
			}

			return column;
		});
		return columns;
	};

	KendoListMoverWithSearchControlViewModel.prototype._getRightColumns = function()
	{
		var self = this;
		var currentColumns = this._gridDefinition.Columns.map(x => $.extend({}, x));
		var columns = currentColumns.map(function(definition)
		{
			var column = definition;
			column.field = definition.FieldName;
			column.title = definition.DisplayName;
			column.width = definition.Width || KendoListMoverWithSearchControlViewModel.defaults.columnWidth;
			column.hidden = definition.hidden;
			self.setColumnFilterableCell(column, definition, "listmover");
			column.filterable = false;
			if (definition.AllowSorting === false)
			{
				column.sortable = false;
			}
			if (definition.template !== undefined)
			{
				column.template = definition.template;
			}

			return column;
		});
		return columns;
	};

	KendoListMoverWithSearchControlViewModel.prototype.bindSearchGridDraggable = function(searchGrid, autoScroll)
	{
		var option = {
			filter: ".k-grid-content tbody > tr:not(.disSelectable)",
			threshold: 100,
			autoScroll: autoScroll,
			holdToDrag: TF.isMobileDevice,
			cursorOffset: { top: -10, left: -10 },
			hint: function(e)
			{
				if (e.hasClass(TF.KendoClasses.STATE.SELECTED))
				{
					var selectedColumns = searchGrid.$container.find(`tr.${TF.KendoClasses.STATE.SELECTED}`);
					return this._getHintElements(e, selectedColumns);
				}
				return this._getHintElements(e);
			}.bind(this),
			dragstart: function(e)
			{
				if (e.currentTarget.hasClass("disable"))
				{
					e.preventDefault();
				}
			},
			dragend: function()
			{
				$(".list-mover-drag-hint").hide();
			}.bind(this)
		};
		searchGrid.$container.kendoDraggable(option);
	};

	KendoListMoverWithSearchControlViewModel.prototype.bindLeftGridDropTarget = function()
	{
		var self = this;

		this.leftSearchGrid.$container.kendoDropTarget(
			{
				drop: function(e)
				{
					if (!self._isDragItem(e))
						return;

					e.draggable.hint.hide();
					if (!e.draggable.element.hasClass("availablecolumngrid-container"))
					{
						if (!e.draggable.currentTarget.hasClass(TF.KendoClasses.STATE.SELECTED))
						{
							var selectedUid = e.draggable.currentTarget.data().kendoUid;
							var selectedItem = this.rightSearchGrid.kendoGrid.dataItems().filter(function(dataItem)
							{
								return dataItem.uid === selectedUid;
							});
							this._obRightSelData(selectedItem);
						}

						if (this._obRightSelData())
						{
							this._obLeftSelData([]);
							this._moveItem();
						}
					}
				}.bind(this)
			});
	};

	KendoListMoverWithSearchControlViewModel.prototype.bindRightGridDropTarget = function()
	{
		var self = this;
		this.rightSearchGrid.$container.kendoDropTarget(
			{
				dragenter: function(e)
				{
					if (self.options.disableDropIndicator)
					{
						return;
					}
					if (!self._isDragItem(e))
						return;

					var selectedColItems = this.rightSearchGrid.$container.find("tr");
					var targetItem;
					var insertBeforeTarget;
					if (e.draggable.hint.offset().top < $(".selectedcolumngrid-container .k-grid-content").offset().top)
					{
						targetItem = $(selectedColItems[1]);
						targetItem.addClass("drag-target-insert-before-cursor"); // modify dropTarget element
						insertBeforeTarget = true;
					}
					else
					{
						targetItem = $(selectedColItems[selectedColItems.length - 1]);
						targetItem.addClass("drag-target-insert-after-cursor");
					}

					this._appendDropTargetCursorTriangle(targetItem, insertBeforeTarget);
				}.bind(this),
				dragleave: function()
				{
					this.clearSelectCursor();
					this._removeDropTargetCursorTriangle();
				}.bind(this),
				drop: this._selectedDrop.bind(this)
			});
	};

	KendoListMoverWithSearchControlViewModel.prototype._getFilterData = function(datasource)
	{
		return (new kendo.data.Query(datasource.data()).filter(datasource.filter())).data;
	};

	KendoListMoverWithSearchControlViewModel.prototype.filterToRightData = function(data)
	{
		return data;
	};

	KendoListMoverWithSearchControlViewModel.prototype.toRightClick = function()
	{
		this._moveItem(true, this.fixedLeftIds);
	};

	KendoListMoverWithSearchControlViewModel.prototype.toLeftClick = function()
	{
		this._moveItem(true);
	};

	KendoListMoverWithSearchControlViewModel.prototype.getDataWithCurrentFiltering = function()
	{
		var self = this, searchOption = $.extend({}, self.leftSearchGrid.searchOption),
			url = self.leftSearchGrid.getApiRequestURL(self.leftSearchGrid.options.url);

		return tf.promiseAjax.post(pathCombine(url),
			{
				data: searchOption.data
			})
			.then(function(apiResponse)
			{
				return apiResponse.Items;
			});
	}

	KendoListMoverWithSearchControlViewModel.prototype.toAllRightClick = function()
	{
		var self = this;

		self.isChanged = true;

		let p = self.options.serverPaging ?
			(self.getLeftGridAllDataByCurrentFilter ? self.getLeftGridAllDataByCurrentFilter() : self.leftSearchGrid.getIdsWithCurrentFiltering().then((data) => data.map((id) => ({ Id: id })))) :
			self._getFilterData(self.leftSearchGrid.kendoGrid.dataSource);

		Promise.resolve(p).then((data) =>
		{
			data = self.filterToRightData(data);
			if (self.options.getSelectableRecords)
			{
				data = self.options.getSelectableRecords(data, self.selectedData.length > 0 ? self.selectedData : [data[0]]);
			}

			self._obLeftSelData(data.filter(item => this.fixedLeftIds.indexOf(item.Id) == -1));
			self._obRightSelData(this.fixedLeftIds.map(id => { return { Id: id }; }));
			self._moveItem(false);
		});

		self.updateKendoGridDataSourceSkip(this.leftSearchGrid.kendoGrid);
	};

	KendoListMoverWithSearchControlViewModel.prototype.toAllLeftClick = function()
	{
		this.isChanged = true;

		let rightCandidates = [];
		const fixedSelectionOnRight = this.fixedRightIds.slice();

		if (!this.options.serverPaging)
		{
			const rightItems = this._getFilterData(this.rightSearchGrid.kendoGrid.dataSource);

			for (let i = 0; i < rightItems.length; i++)
			{
				let item = rightItems[i];
				if (!fixedSelectionOnRight.includes(item.Id))
				{
					rightCandidates.push(item);
				}
			}

			this._obLeftSelData([]);
			this._obRightSelData(rightCandidates);
			this._moveItem(false);
		}
		else
		{
			this.rightSearchGrid.getIdsWithCurrentFiltering().then(ids =>
			{
				if (this.options.getSelectableRecords)
				{
					ids = this.options.getSelectableRecords(ids, this.selectedData.length > 0 ? this.selectedData : [ids[0]]);
				}

				this._obLeftSelData([]);
				this._obRightSelData(ids.filter(item => fixedSelectionOnRight.indexOf(item) == -1).map(id => { return { Id: id }; }));
				this._moveItem(false);
			});
		}

		this.updateKendoGridDataSourceSkip(this.rightSearchGrid.kendoGrid);
	};

	KendoListMoverWithSearchControlViewModel.prototype._moveItem = function(enableSelected, excludedIds)
	{
		this.isChanged = true;

		const isVirtualScroll = this.options.serverPaging;

		// If the grid is reading data from server(virtual scroll), get the ids from grid. Because there are only 100 items in the grid's datasource.
		if (this.obLeftGridSelected())
		{
			let additionalData = [];
			const { allIds, kendoGrid, searchOption, getSelectedIds } = this.leftSearchGrid;
			let leftSelectedIds = getSelectedIds();
			if (excludedIds)
			{
				leftSelectedIds = leftSelectedIds.filter(item => excludedIds.indexOf(item) == -1);
			}

			if (leftSelectedIds.length > 0 && enableSelected)
			{
				if (isVirtualScroll)
				{
					additionalData = leftSelectedIds;
					this.adjustSearchParamData(additionalData, allIds, kendoGrid, searchOption.paramData);
				}
				else
				{
					var data = this.filterToRightData(kendoGrid.dataSource.data());
					data = data.filter((item) => leftSelectedIds.includes(item.Id));
					this._obLeftSelData(data);

					additionalData = this._obLeftSelData();
				}
			}
			else
			{
				if (isVirtualScroll)
				{
					additionalData = enableSelected ? leftSelectedIds : Enumerable.From(this._obLeftSelData()).Select("$.Id").ToArray();
					this.adjustSearchParamData(additionalData, allIds, kendoGrid, searchOption.paramData);
				}
				else
				{
					additionalData = this._obLeftSelData();
				}
			}

			// split selectedData into multiple parts to prevent Maximum Call Stack Size Exceeded error
			const split_len = 50000;
			while (additionalData.length)
			{
				const chunk = additionalData.splice(0, split_len);
				this.selectedData = Array.extend(this.selectedData, chunk);
			}
		}

		if (this.obRightGridSelected())
		{
			const { allIds, kendoGrid, searchOption, getSelectedIds } = this.rightSearchGrid;
			let rightSelectedIds = getSelectedIds();

			if (rightSelectedIds.length > 0 && enableSelected)
			{
				this._obRightSelData(kendoGrid.dataSource.data().filter((item) => rightSelectedIds.includes(item.Id)));
			}
			else if (isVirtualScroll)
			{
				rightSelectedIds = rightSelectedIds.length > 0 ? rightSelectedIds : Enumerable.From(this._obRightSelData()).Select("$.Id").ToArray();
			}

			if (isVirtualScroll)
			{
				this.adjustSearchParamData(rightSelectedIds, allIds, kendoGrid, searchOption.paramData);
				this.selectedData = this.selectedData.filter((rightData) => !rightSelectedIds.includes(rightData));
			}
			else
			{
				this.selectedData = this.selectedData.filter((rightData) => this._obRightSelData().every(item => rightData.Id !== item.id));
			}
		}

		if (isVirtualScroll)
		{
			this.obSelectedData(this.selectedData);
			this.leftSearchGrid.triggerRefreshClick();
			this.rightSearchGrid.rebuildGrid();
		}
		else
		{
			this.obSelectedData(Enumerable.From(this.selectedData).Select("$.Id").ToArray());
			this.setDataSource();
		}

		this._clearLeftSelection();
		this._clearRightSelection();

		this.createKendoDropTargetEvent();
	};

	KendoListMoverWithSearchControlViewModel.prototype.getSelectedDataByIds = function(selectedData)
	{
		var self = this,
			url = self.leftSearchGrid.getApiRequestURL(self.leftSearchGrid.options.url);
		return tf.promiseAjax.post(pathCombine(url),
			{
				data: {
					sortItems: self.leftSearchGrid.searchOption.data.sortItems,
					fields: self.leftSearchGrid.searchOption.data.fields,
					filterClause: '',
					filterSet: null,
					idFilter: {
						ExcludeAny: [],
						IncludeOnly: selectedData || null,
					}
				}
			})
			.then(function(apiResponse)
			{
				return apiResponse.Items;
			});
	}

	KendoListMoverWithSearchControlViewModel.prototype.apply = function()
	{
		var self = this;
		var firstFieldName = self.columns[0].FieldName;
		this.saveCurrentSelectedColumns(self.options.type, self.columns);
		let p = Promise.resolve(self.selectedData);

		if (self.options.serverPaging && !self.options.onlyReturnId)
		{
			p = self.getSelectedDataByIds(self.selectedData);
		}

		return p.then(function(result)
		{
			self.selectedData = result;
			self.selectedData = self.selectedData.sort(function(a, b)
			{
				if (typeof a[firstFieldName] === "string")
				{
					return a[firstFieldName].toUpperCase() > (b[firstFieldName] || "").toUpperCase();
				}
				return 0;
			});
			return self.selectedData;
		})

	};

	KendoListMoverWithSearchControlViewModel.prototype.cancel = function() { };

	KendoListMoverWithSearchControlViewModel.prototype.clearSelectCursor = function()
	{
		var selectedColItems = this.rightSearchGrid.$container.find("tr");
		selectedColItems.removeClass("drag-target-insert-before-cursor");
		selectedColItems.removeClass("drag-target-insert-after-cursor");
	};

	KendoListMoverWithSearchControlViewModel.prototype._selectedDrop = function(e)
	{
		var self = this;
		if (!self._isDragItem(e))
			return;

		this.clearSelectCursor();
		e.draggable.hint.hide();
		if (e.draggable.currentTarget.hasClass(TF.KendoClasses.STATE.SELECTED))
		{
			if (!this.obLeftGridSelected() &&
				!this.obRightGridSelected())
			{
				this.rightSearchGrid.kendoGrid.clearSelection();
				return;
			}

			if (this.obLeftGridSelected())
			{
				this._moveItem();
			}
		}
		else
		{
			var selectedUid = e.draggable.currentTarget.data().kendoUid;

			if (e.draggable.element.hasClass("availablecolumngrid-container"))
			{
				var selectedItem = this.leftSearchGrid.kendoGrid.dataItems().filter(function(dataItem)
				{
					return dataItem.uid === selectedUid;
				});
				selectedItem = Enumerable.From(selectedItem).Distinct(function(c) { return c.uid; }).ToArray();
				this._obLeftSelData(selectedItem);
				this._obRightSelData([]);
				this._moveItem();
			}
		}

		this._removeDropTargetCursorTriangle();
	};

	KendoListMoverWithSearchControlViewModel.prototype._removeDropTargetCursorTriangle = function()
	{
		$("#left-triangle").remove();
		$("#right-triangle").remove();
	};

	KendoListMoverWithSearchControlViewModel.prototype._appendDropTargetCursorTriangle = function(targetItem, insertBeforeTarget)
	{
		var leftTriangle = $("<div id='left-triangle'></div>").addClass("drag-target-cursor-left-triangle");
		var rightTriangle = $("<div id='right-triangle'></div>").addClass("drag-target-cursor-right-triangle");

		leftTriangle.css("left", -1 + "px");
		rightTriangle.css("left", targetItem.width() - 14 + "px");

		if (insertBeforeTarget)
		{
			leftTriangle.css("top", "-6px");
			rightTriangle.css("top", "-6px");
		}

		targetItem.find("td:first").append(leftTriangle);
		targetItem.find("td:visible:last").append(rightTriangle);
	};

	KendoListMoverWithSearchControlViewModel.prototype._getHintElements = function(item, selectedColumns)
	{
		var hintElements = $("<div class='k-grid list-mover-drag-hint' style=''><table style='width:100%'><tbody></tbody></table></div>"),
			maxWidth = this.rightSearchGrid.kendoGrid.element.width(), tooLong = false;
		hintElements.css(
			{
				"background-color": "#FFFFCE",
				"opacity": 0.8,
				"cursor": "move",
				"overflow": "hidden"
			});
		if (selectedColumns === undefined)
		{
			tooLong = $(item).width() > maxWidth;
			hintElements.width(tooLong ? maxWidth : $(item).width());
			hintElements.find("tbody").append("<tr>" + (tooLong ? $(item.html())[0].outerHTML : item.html()) + "</tr>");
		}
		else
		{
			for (var i = 0; i < selectedColumns.length; i++)
			{
				if (selectedColumns[i].tagName === "SPAN") continue;
				tooLong = $(selectedColumns[i]).width() > maxWidth;
				hintElements.width(tooLong ? maxWidth : $(selectedColumns[i]).width());
				hintElements.find("tbody").append("<tr>" + (tooLong ? $($(selectedColumns[i]).html())[0].outerHTML : $(selectedColumns[i]).html()) + "</tr>");
			}
		}

		return hintElements;
	};

	KendoListMoverWithSearchControlViewModel.prototype._clearLeftSelection = function()
	{
		this._obLeftSelData([]);
		if (this.leftSearchGrid != null)
		{
			this.leftSearchGrid.clearSelection();
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype._clearRightSelection = function()
	{
		this._obRightSelData([]);
		this.rightSearchGrid.clearSelection();
	};

	KendoListMoverWithSearchControlViewModel.prototype._isDragItem = function(e)
	{
		var isDragItem = (!$(e.target).hasClass("k-resize-handle") &&
			!$(e.draggable.element).hasClass("k-grid-header-wrap"));
		return isDragItem;
	};

	KendoListMoverWithSearchControlViewModel.prototype._changeLeftGridSelectable = function()
	{
		var self = this;
		if (!self.options.getSelectableRecords) { return }

		var kendoOptions = self.leftSearchGrid.kendoGrid.getOptions();

		var newSelectable = "multiple";
		if (self.selectedData.length == 0 && !self.obRegardlessCriteria())
		{
			newSelectable = "row";
		}
		if (newSelectable != kendoOptions.selectable)
		{
			self.leftSearchGrid.kendoGrid.options.selectable = newSelectable;
			self.leftSearchGrid.kendoGrid._selectable();
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.columnSources = {
	};

	KendoListMoverWithSearchControlViewModel.prototype.getColumnSources = function(dataType, isForRequests)
	{
		if (dataType === "StudentTags")
		{
			dataType = "Tags";
		}

		return this.columnSources[dataType];
	}

	/**
	 *
	 *
	 * @param {Array} movedIds
	 * @param {Array} allIds
	 * @param {Number} currentIndex
	 * @param {Object} searchParamData
	 */
	KendoListMoverWithSearchControlViewModel.prototype.adjustSearchParamData = function(movedIds, allIds, kendoGrid, searchParamData)
	{
		const $rows = kendoGrid.element.find("tr");
		if ($rows.length === 0) return;

		const { skip, take } = searchParamData;
		const rowHeight = $rows.height();
		const scrollTop = kendoGrid.element.find(".k-virtual-scrollable-wrap").scrollTop();
		const currentIndex = Math.ceil(scrollTop / rowHeight) + skip;
		let countBeforeCurrentIndex = 0;

		for (let i = 0; i < currentIndex; i++)
		{
			let id = allIds[i];
			if (movedIds.includes(id))
			{
				countBeforeCurrentIndex++;
			}
		}

		// If items before current index are moved, should adjust skip.
		if (countBeforeCurrentIndex > 0)
		{
			// might not be the best practice, should be changed to use public method or property if any other approache is found.
			const newSkipValue = Math.max(Math.floor((skip - countBeforeCurrentIndex) / take), 0) * take;
			this.updateKendoGridDataSourceSkip(kendoGrid, newSkipValue);
		}
	};

	KendoListMoverWithSearchControlViewModel.prototype.updateKendoGridDataSourceSkip = function(kendoGrid, skip)
	{
		kendoGrid.dataSource._skip = skip || 0;
	}

	KendoListMoverWithSearchControlViewModel.prototype.afterInit = function() { };

	KendoListMoverWithSearchControlViewModel.prototype.dispose = function()
	{
		// this.leftSearchGrid.destroy();
		// this.rightSearchGrid.destroy();
		// this.leftSearchGrid = null;
		// this.rightSearchGrid = null;
		clearTimeout(this.changePageInfoDisplayTimeout);
		clearTimeout(this.createKendoDropTargetEventTimeout);
		clearTimeout(this.changeSelectIdsTimeout);
		clearTimeout(this.setDataSourceTimeout);
		tf.shortCutKeys.resetUsingGolbal(2);
		tf.shortCutKeys.clearSpecialHashMap();
		PubSub.unsubscribe(topicCombine(pb.DATA_CHANGE, "listmover"));
	};
})();
