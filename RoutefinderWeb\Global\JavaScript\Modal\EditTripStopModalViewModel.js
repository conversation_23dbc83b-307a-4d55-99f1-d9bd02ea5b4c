﻿(function()
{
	createNamespace("TF.Modal").EditTripStopModalViewModel = EditTripStopModalViewModel;

	function EditTripStopModalViewModel(tripStopViewModel, tripViewModel, successCallback, failCallback)
	{
		TF.Modal.BaseModalViewModel.call(this, successCallback, failCallback);
		this.title('New ' + tf.applicationTerm.getApplicationTermSingularByName("Stop"));
		this.contentTemplate('controls/EditStop');
		this.buttonTemplate('modal/positivenegative');
		this.data(new TF.Fragment.EditStopViewModel(tripStopViewModel, tripViewModel));
	};

	EditTripStopModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	EditTripStopModalViewModel.prototype.constructor = EditTripStopModalViewModel;
})();


