﻿(function()
{
	createNamespace("TF.Modal.ResourceScheduler").EditFieldTripModalViewModel = EditFieldTripModalViewModel;

	function EditFieldTripModalViewModel(fieldTripEntity, drivers, aides, vehicles)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Edit Field Trip');
		this.sizeCss = "modal-dialog-sm";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/resourcescheduler/editfieldtrip');
		this.buttonTemplate("modal/positivenegative");
		this.editFieldTripViewModel = new TF.Control.ResourceScheduler.EditFieldTripViewModel(fieldTripEntity, drivers, aides, vehicles);
		this.data(this.editFieldTripViewModel);

		this.editFieldTripViewModel.initializedEvent.subscribe(() =>
		{
			this.obDisableControl(!this.editFieldTripViewModel.obStatusEditable() && !this.editFieldTripViewModel.obResourceEditable());
		});
	};

	EditFieldTripModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	EditFieldTripModalViewModel.prototype.constructor = EditFieldTripModalViewModel;

	EditFieldTripModalViewModel.prototype.positiveClick = function()
	{
		return this.editFieldTripViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(!!result);
			}
		}.bind(this));
	};
})();

