(function()
{
	createNamespace('TF.Modal.Event').EventFieldViewModel = EventFieldViewModel;
	function EventFieldViewModel(options, type, parent)
	{
		this.options = options;
		this.component = null;
		this.parent = parent;
		this.dataSource = [
			{
				text: 'Vehicle Name',
				value: 'Vehicle'
			},
			{
				text: type === 'georegion' ? 'GeoRegion Name' : 'School Location',
				value: 'LocationName'
			},
			{
				text: 'On-Time Status',
				value: 'OnTime'
			},
			{
				text: 'School Code',
				value: 'SchoolCode'
			},
			{
				text: 'School Name',
				value: 'SchoolName'
			},
			{
				text: 'Event Type',
				value: 'EventType'
			},
			{
				text: 'Event Time',
				value: 'EventTime'
			},
			{
				text: 'Event Date',
				value: 'EventDate'
			},
			{
				text: 'Trip',
				value: 'TripName'
			},
			{
				text: 'Driver',
				value: 'Driver'
			}
		];
		if (type === 'georegion')
		{
			this.dataSource.splice(2, 3);
		}
		this.dataSource = this.dataSource.sort((a, b) => a.text.localeCompare(b.text));
		this.defaultValue = this.dataSource[0].value;
	}
	EventFieldViewModel.prototype.apply = function()
	{
		let self = this;
		let selectedItem = this.dataSource.find(x => x.value == self.component.value());
		return Promise.resolve(selectedItem);
	}

	EventFieldViewModel.prototype.init = function(viewModel, el)
	{
		this.$element = $(el);
		let self = this;
		this.component = this.$element.find('input.dropdown-list').kendoDropDownList({
			dataTextField: 'text',
			dataValueField: 'value',
			value: self.options.dataField || self.defaultValue,
			dataSource: self.dataSource,
		}).data('kendoDropDownList');
		this.$element.on("keyup", function(e)
		{
			if (e.keyCode === $.ui.keyCode.ENTER)
			{
				self.parent.positiveClick();
			}
		});
		setTimeout(() =>
		{
			self.$element.find('.dropdown-list').focus();
		}, 50);
	};
})();