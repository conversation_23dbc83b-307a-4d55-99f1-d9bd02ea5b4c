(function()
{
	createNamespace('TF.Modal').ListMoverSelectUserControlModalViewModel = ListMoverSelectUserControlModalViewModel;

	function ListMoverSelectUserControlModalViewModel(selectedData, options)
	{
		var defaults = {
			title: "Select Users ",
			description: "You may select one or more of the Routefinder Pro user accounts.",
			availableTitle: "Available",
			selectedTitle: "Selected",
			type: "user",
			displayCheckbox: false,
			filterSetField: "AccountEnabled",
			showEnabled: true,
			typeDisplayName:"users",
			getUrl: function()
			{
				return pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", "user");
			},
			checkResult: function(result)
			{
				return Promise.resolve(true);
			}
		};
		this.options = options = $.extend(true, defaults, options);
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverSelectRecipientControlViewModel = new TF.Control.ListMoverSelectRecipientControlViewModel(selectedData, options);
		this.data(this.ListMoverSelectRecipientControlViewModel);

		this.ListMoverSelectRecipientControlViewModel.onBeforeLeftGridDataBound = function(leftSearchGrid){};
	}

	ListMoverSelectUserControlModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectUserControlModalViewModel.prototype.constructor = ListMoverSelectUserControlModalViewModel;

	ListMoverSelectUserControlModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverSelectRecipientControlViewModel.apply().then(function(result)
		{
			var promise = [];
			if (this.options.checkResult)
			{
				promise.push(this.options.checkResult(result));
			}
			return Promise.all(promise).then(function(checkResult)
			{
				if (checkResult && checkResult.length > 0 && checkResult[0] === false)
				{
					return;
				}
				if (result)
				{
					this.positiveClose(Enumerable.From(result).OrderBy("$.LastName").ToArray());
				}
			}.bind(this));
		}.bind(this));
	};

	ListMoverSelectUserControlModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverSelectRecipientControlViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();
