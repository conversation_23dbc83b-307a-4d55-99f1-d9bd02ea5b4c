﻿(function()
{
	createNamespace('TF.Control').AdjustAttendanceTripStopTimeViewModel = AdjustAttendanceTripStopTimeViewModel;

	function AdjustAttendanceTripStopTimeViewModel(stop)
	{
		this.stop = stop;
		this.stopTime = ko.observable(toISOStringWithoutTimeZone(moment(stop.attendanceInfo().stopTime())));
	}

	AdjustAttendanceTripStopTimeViewModel.prototype.apply = function()
	{
		return Promise.resolve(this.stopTime());
	};

	AdjustAttendanceTripStopTimeViewModel.prototype.dispose = function()
	{
	};

})();