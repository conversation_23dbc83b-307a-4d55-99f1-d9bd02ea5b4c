﻿(function()
{
	createNamespace('TF.Modal').DistrictPoliciesModalViewModel = DistrictPoliciesModalViewModel;

	function DistrictPoliciesModalViewModel(name, type, id)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/datasource/districtpolicies');
		this.sizeCss = "modal-dialog-lg";
		this.buttonTemplate('modal/positivenegative');
		this.districtPoliciesViewModel = new TF.Control.DistrictPoliciesViewModel(name, type, id);
		this.data(this.districtPoliciesViewModel);

		this.title(tf.applicationTerm.getApplicationTermSingularByName("District") + " Policies (" + name + ")");
	}

	DistrictPoliciesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	DistrictPoliciesModalViewModel.prototype.constructor = DistrictPoliciesModalViewModel;

	DistrictPoliciesModalViewModel.prototype.positiveClick = function()
	{
		this.districtPoliciesViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	DistrictPoliciesModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.districtPoliciesViewModel.tryGoAway()
		.then(function(result)
		{
			if (result == true)
			{
				this.negativeClose();
			}
		}.bind(this));
	};

	DistrictPoliciesModalViewModel.prototype.dispose = function()
	{
		this.districtPoliciesViewModel.dispose();
	};

})();
