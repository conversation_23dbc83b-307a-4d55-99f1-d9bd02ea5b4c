(function()
{
	createNamespace('TF.Control').GeoFinderSettingViewModel = GeoFinderSettingViewModel;

	function GeoFinderSettingViewModel(findType)
	{
		var self = this;
		this.obFindType = ko.observable(findType);
		this.searchCriteria = ko.observable(0);
		this.walkoutType = ko.observable(0);
		this.walkoutDistance = ko.observable(1000);
		this.walkoutBuffer = ko.observable(200);
		this.driveToTime = ko.observable(2);

		// units
		this.obUnits = ko.observableArray(["meters", "feet", "kilometers", "miles", "yards"]);
		if (tf.measurementUnitConverter.isImperial())
		{
			this.obSelectedDistanceUnit = ko.observable(this.obUnits()[1]);
			this.obSelectedBufferUnit = ko.observable(this.obUnits()[1]);
		}
		else
		{
			this.obSelectedDistanceUnit = ko.observable(this.obUnits()[0]);
			this.obSelectedBufferUnit = ko.observable(this.obUnits()[0]);
		}
		
		this.obSelectedDistanceUnitText = ko.computed(function()
		{
			return this.obSelectedDistanceUnit();
		}, this);
		this.obSelectedBufferUnitText = ko.computed(function()
		{
			return this.obSelectedBufferUnit();
		}, this);

		self.obTravelScenarios = ko.observableArray();
		self.obSelectedTravelScenario = ko.observable();
		self.obCurrentTravelScenariosName = ko.computed(this.currentTravelScenariosNameComputer, this);

		this.obSelectedDistanceUnit.subscribe(function() { self.initValidation(); });
		this.obSelectedBufferUnit.subscribe(function() { self.initValidation(); });
		this.walkoutType.subscribe(function() { self.initValidation(); });
		this.searchCriteria.subscribe(function() { self.initValidation(); });
	}

	GeoFinderSettingViewModel.prototype.init = function(viewModal, e)
	{
		this.element = e;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "travelscenarios"))
			.then(function(data)
			{
				this.obTravelScenarios(data.Items);
				this.obSelectedTravelScenario(this.obTravelScenarios()[0]);
				this.initValidation();
			}.bind(this));
	};

	GeoFinderSettingViewModel.prototype.initValidation = function()
	{
		var self = this;
		self.$form = $(self.element);
		var validatorFields = {};

		if (self.$form.data("bootstrapValidator"))
		{
			self.$form.data("bootstrapValidator").destroy();
		}

		validatorFields.walkoutDistance = {
			trigger: "blur change",
			validators: {
				greaterThan: {
					value: 0,
					message: " must be > 0",
					inclusive: false
				},
				lessThan: {
					value: TF.RoutingMap.RoutingPalette.BaseTripStopEditModal.unitMax(self.obSelectedDistanceUnit()),
				}
			}
		};
		validatorFields.walkoutBuffer = {
			trigger: "blur change",
			validators: {
				greaterThan: {
					value: 0,
					message: " must be > 0",
					inclusive: false
				},
				lessThan: {
					value: TF.RoutingMap.RoutingPalette.BaseTripStopEditModal.unitMax(self.obSelectedBufferUnit()),
				}
			}
		};

		if (self.walkoutType() == 0 || self.obFindType() == "driveto")
		{
			validatorFields.walkoutBuffer.validators.notEmpty = { message: "required" };
		}
		if ((self.searchCriteria() == 0 && self.obFindType() == "driveto") || self.obFindType() == "walkout")
		{
			validatorFields.walkoutDistance.validators.notEmpty = { message: "required" };
		}

		self.validator = self.$form.bootstrapValidator({
			excluded: [":disabled"],
			live: "enabled",
			message: "This value is not valid",
			fields: validatorFields
		}).data("bootstrapValidator");
		self.validator.validate();
	};

	GeoFinderSettingViewModel.prototype.apply = function()
	{
		var self = this;
		return self.validator.validate().then(function(valid)
		{
			if (valid)
			{
				return Promise.resolve({
					walkoutType: self.walkoutType(),
					driveToTime: self.driveToTime(),
					walkoutDistance: self.walkoutDistance(),
					walkoutDistanceUnit: self.obSelectedDistanceUnitText(),
					walkoutBuffer: self.walkoutBuffer(),
					walkoutBufferUnit: self.obSelectedBufferUnitText(),
					travelScenario: self.obSelectedTravelScenario(),
					searchCriteria: self.searchCriteria()
				});
			}
			return valid;
		});
	};

	GeoFinderSettingViewModel.prototype.currentTravelScenariosNameComputer = function()
	{
		return this.obSelectedTravelScenario() ? this.obSelectedTravelScenario().Name : "";
	};

	GeoFinderSettingViewModel.prototype.dispose = function()
	{

	};

})();

