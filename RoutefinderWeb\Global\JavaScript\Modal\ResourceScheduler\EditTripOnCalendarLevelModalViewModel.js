(function()
{
	createNamespace("TF.Modal.ResourceScheduler").EditTripOnCalendarLevelModalViewModel = EditTripOnCalendarLevelModalViewModel;

	function EditTripOnCalendarLevelModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);

		var title = options.title;

		if (!title)
		{
			switch (options.trips.length)
			{
				case 0:
					title = "Edit Trip";
					break;
				case 1:
					title = "Edit Trip - " + options.trips[0].Name;
					break;
				default:
					title = "Edit Trip (Batch)";
					break;
			}
		}

		this.title(title);
		this.helpKey = "resourceschedulerpopup";
		this.sizeCss = "modal-dialog-sm";
		this.contentTemplate('modal/resourcescheduler/EditTripOnCalendarLevel');
		this.buttonTemplate("modal/positivenegative");
		this.viewModel = new TF.Control.ResourceScheduler.EditTripOnCalendarLevelViewModel(options);
		this.data(this.viewModel);
	};

	EditTripOnCalendarLevelModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	EditTripOnCalendarLevelModalViewModel.prototype.constructor = EditTripOnCalendarLevelModalViewModel;

	EditTripOnCalendarLevelModalViewModel.prototype.positiveClick = function()
	{
		return this.viewModel.apply()
			.then(function(result)
			{
				if (result)
				{
					this.positiveClose(result);
				}
			}.bind(this));
	};
})();