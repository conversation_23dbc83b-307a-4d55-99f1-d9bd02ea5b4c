(function()
{
	createNamespace("TF.Modal").SettingsModalViewModel = SettingsModalViewModel;

	/**
	 * Constructor of SettingsModalViewModel
	 * @returns {void}
	 */
	function SettingsModalViewModel()
	{
		var self = this;

		TF.Modal.BaseModalViewModel.call(self);
		self.title('Settings');
		self.sizeCss = "modal-sm";
		self.contentTemplate('modal/Routing Map/RoutingMapPanel/LocatePalette/SettingsControl');
		self.buttonTemplate('modal/positivenegative');
		self.obPositiveButtonLabel("Apply");

		self.description("When you find a point an arrow will display, Select the color and symbol you would like the arrow to display in.");

		//Events
		self.init = self.init.bind(self);
	};

	SettingsModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SettingsModalViewModel.prototype.constructor = SettingsModalViewModel;

	SettingsModalViewModel.prototype.init = function(model, e)
	{
		var self = this;
		self._$form = $(e);

		var cookieName = "LocatePanelSettingsColor", color = "#ffffff";
		if ($.cookie(cookieName))
		{
			color = JSON.parse($.cookie(cookieName)).activeColor;
		}
		else
		{
			$.cookie(cookieName, JSON.stringify({activeColor: color}));
		}

		self._$form.find("[name=color]").kendoColorPicker(
			{
				buttons: false,
				value: color,
				cookieName: cookieName
			});
	};

	SettingsModalViewModel.prototype.positiveClick = function()
	{
		this.positiveClose();
	};
})();

