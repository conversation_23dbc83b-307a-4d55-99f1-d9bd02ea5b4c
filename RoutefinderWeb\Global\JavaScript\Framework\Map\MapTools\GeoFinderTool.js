(function()
{
	createNamespace("TF.Map").GeoFinderTool = GeoFinderTool;

	function GeoFinderTool(routingMapTool)
	{
		var self = this;
		self.routingMapTool = routingMapTool;
		self._arcgis = tf.map.ArcGIS;
		self.map = self.routingMapTool.routingMapDocumentViewModel._map;
		self.stopTool = new TF.RoutingMap.RoutingPalette.StopTool(null, self.map, null);
		self.gridOptions = ["altsite", "georegion", "student", "school", "trip", "tripstop"];
		self.gridTypes = ["altsite", "georegion", "student", "school", "trip", "tripstop", "gpsevent"];
		self.currentDrawStatus = "";
		self.initToolbar();
	}

	GeoFinderTool.prototype.initToolbar = function()
	{
		var self = this;
		// self._initElements();
		//self._initHelper();
		self._initLayers();
		self._initDrawTool();
	};

	GeoFinderTool.prototype._initLayers = function()
	{
		var self = this;
		self.initDrawPolygonLayer();
		self._geoFinderPolygonSymbol = {
			type: "simple-fill",
			color: [18, 89, 208, 0.3],
			outline: {
				color: [18, 89, 208, 0.6],
				width: 1,
			}
		};
		//self.initDeleteMarkerLayer();
	};

	GeoFinderTool.prototype._initDrawTool = function()
	{
		var self = this;
		self._sketchVM = new self._arcgis.SketchViewModel({
			view: self.map.mapView,
			layer: self._drawPolygonLayer,
			updateOnGraphicClick: false,
			defaultUpdateOptions: { // set the default options for the update operations
				toggleToolOnClick: false // only reshape operation will be enabled
			},
			polygonSymbol: self._geoFinderPolygonSymbol
		});
		self._sketchVM._clearTempDrawing = function() { self._sketchVM.cancel(); };
		self._sketchVM.stopPreview = function() { };
		self._sketchVM.stopAndClear = function() { };
		self.onCreateHandler = self._sketchVM.on("create", function(e)
		{
			// if (e.state == "start")
			// {
			// 	if (self._drawDeleteMarkerLayer) { self._drawDeleteMarkerLayer.visible = false; }
			// }
			if (e.state == "complete")
			{
				self._drawCompleteHandler(e);
			}
			else if (e.state == "cancel")
			{
				const hasPopups = self.routingMapTool.routingMapDocumentViewModel.gridMapPopup || (self.routingMapTool.routingMapDocumentViewModel.hasMapPopups && self.routingMapTool.routingMapDocumentViewModel.hasMapPopups());
				if (hasPopups)
				{
					self.routingMapTool.routingMapDocumentViewModel.enableMouseEvent();
				}
			}
		});
	};

	GeoFinderTool.prototype._drawCompleteHandler = function(e)
	{
		var self = this;
		if (e.graphic)
		{
			const hasPopups = self.routingMapTool.routingMapDocumentViewModel.gridMapPopup || (self.routingMapTool.routingMapDocumentViewModel.hasMapPopups && self.routingMapTool.routingMapDocumentViewModel.hasMapPopups());
			if (hasPopups)
			{
				this.routingMapTool.routingMapDocumentViewModel.enableMouseEvent();
			}

			if (self.type == "polygon")
			{
				tf.modalManager.showModal(
					new TF.Modal.GeoFinderModalViewModel(self, { getResultsInPolygonPromise: self.findInPolygon() })
				).then(function(res)
				{
					if (res)
					{
						self.openGrid(res.gridResults, res);
					}
				})
			}
			else
			{
				tf.modalManager.showModal(
					new TF.Modal.GeoFinderSettingModalViewModel(self.type)
				).then(function(data)
				{
					if (data)
					{
						var type = self.type == "walkout" ? data.walkoutType : (data.searchCriteria == 0 ? 2 : 3);
						tf.loadingIndicator.showImmediately();
						self.stopTool.generateWalkoutZone(e.graphic, data.walkoutDistance, data.walkoutDistanceUnit, data.walkoutBuffer, data.walkoutBufferUnit, type, null, null, data.driveToTime, data.travelScenario).then(function(result)
						{
							tf.loadingIndicator.tryHide();
							if (result && result.walkoutZone)
							{
								self.addWalkoutPolygon(result.walkoutZone.geometry);
								tf.modalManager.showModal(
									new TF.Modal.GeoFinderModalViewModel(self, { getResultsInPolygonPromise: self.findInPolygon() })
								).then(function(res)
								{
									if (res)
									{
										self.openGrid(res.gridResults, res);
									}
								});
							} else
							{
								tf.promiseBootbox.alert(`Failed to find in ${self.type == "walkout" ? "walkout" : "drive to"}.`, "Error");
							}
						})
					}
					else
					{
						self._drawPolygonLayer.remove(e.graphic);
					}
				})
			}

		}
	}

	GeoFinderTool.prototype._getDrawnPolygons = function()
	{
		const self = this, geometries = [];
		self._drawPolygonLayer.graphics.items.forEach(function(g)
		{
			if (g.geometry.type == "polygon")
			{
				geometries.push(g.geometry);
			}
		});

		return geometries;
	}

	GeoFinderTool.prototype._getUnionGeometry = function()
	{
		var self = this, geometries = [];
		self._drawPolygonLayer.graphics.items.forEach(function(g)
		{
			if (g.geometry.type == "polygon")
			{
				geometries.push(g.geometry);
			}
		});
		var polygon = self._arcgis.geometryEngine.union(geometries);
		return polygon;
	}

	GeoFinderTool.prototype.findInBoundarySets = function(boundarySets)
	{
		const self = this, polygon = self._getUnionGeometry(), paramData = { multiple: polygon.rings.length > 1 };
		if (boundarySets.length > 0)
		{
			var boundarySetsIdsText = "";
			boundarySets.forEach(function(b) { boundarySetsIdsText += b.Id + ","; });
			paramData.redistrictIds = boundarySetsIdsText.slice(0, -1);
		}

		return tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postGetSchoolByPolygonAndRedistrictIdsFromUrl(), {
			paramData: paramData,
			data: '"' + self._getPolygonText(polygon) + '"'
		}).then(function(result)
		{
			return Promise.resolve(result);
		})
	}

	GeoFinderTool.prototype.findInPolygon = function()
	{
		const self = this, promises = [];
		const polygons = self._getDrawnPolygons().map(p => self._getPolygonText(p)).join("#");

		self.gridOptions.forEach(function(gridOption)
		{
			if (tf.authManager.isAuthorizedForDataType(gridOption, 'read'))
			{
				promises.push(tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postGetByPolygonFromUrl(gridOption), {
					data: `"${polygons}"`
				}));
			} else
			{
				promises.push(Promise.resolve({ Items: [] }));
			}

		});
		return Promise.all(promises).then(function(results)
		{
			return Promise.resolve(results);
		});
	};

	function resolveTime(time)
	{
		if (!time) return time;
		var index = time.indexOf("T");
		if (index > -1)
		{
			time = "1899-01-01" + time.substring(index);
		}

		return time;
	}

	GeoFinderTool.prototype.openGrid = function(results, res)
	{
		var self = this;
		res.selectGridOptions.forEach(function(gridType, index)
		{
			if (gridType == "gpsevent")
			{
				self.fetchGPSData(res.gpsQueryCondition).then(function(results)
				{
					var timeRange = {
						dayOfTheWeek: [false, false, false, false, false, false, false],
						startDate: res.gpsQueryCondition.startDate,
						endDate: res.gpsQueryCondition.endDate,
						startTime: resolveTime(res.gpsQueryCondition.startTime),
						endTime: resolveTime(res.gpsQueryCondition.endTime)
					};
					res.gpsQueryCondition.availableDays.forEach(function(d)
					{
						timeRange.dayOfTheWeek[d] = true;
					});

					tf.storageManager.delete(tf.storageManager.gridCurrentQuickFilter(gridType));
					var docData = new TF.Document.DocumentData(TF.Document.DocumentData.Grid,
						{
							gridType: gridType,
							isTemporaryFilter: true,
							autoShow: index == 0,
							gridState: new TF.Grid.GridState({ filteredIds: results, timeRange: timeRange })
						});
					docData.isLazyLoading = index > 0;
					tf.documentManagerViewModel.add(docData, false, true);
				})
			} else
			{
				tf.storageManager.delete(tf.storageManager.gridCurrentQuickFilter(gridType));
				var result = results[self.gridTypes.indexOf(gridType)];
				var docData = new TF.Document.DocumentData(TF.Document.DocumentData.Grid,
					{
						gridType: gridType,
						isTemporaryFilter: true,
						autoShow: index == 0,
						gridState: new TF.Grid.GridState({ filteredIds: result.Items.map(function(i) { return i.Id }) })
					});
				docData.isLazyLoading = index > 0;
				tf.documentManagerViewModel.add(docData, false, true);
			}

		})
	}

	GeoFinderTool.prototype.fetchGPSData = function(gpsQuery)
	{
		const self = this, polygons = self._getDrawnPolygons().map(p => self._getPolygonText(p)).join("#");;
		const dayOfWeek = [];
		for (let i = 0; i < 7; i++)
		{
			dayOfWeek[i] = (gpsQuery.availableDays.indexOf(i) >= 0);
		}
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), tf.datasourceManager.databaseId, "search", "vehicles"), {
			data: {
				fields: ["Gpsid"]
			}
		}).then(function(r)
		{
			const url = TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled ? "search/plusvehicleeventids" : "gpsevents";
			const action = TF.Helper.VehicleEventHelper.isGpsConnectPlusEnabled ? "" : "getAllInPolygon";
			return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), url, action), {
				paramData: { isLatestPerVehicle: gpsQuery.isLatest },
				data: {
					StartTime: gpsQuery.startDate.split("T")[0] + "T" + gpsQuery.startTime.split("T")[1],
					EndTime: gpsQuery.endDate.split("T")[0] + "T" + gpsQuery.endTime.split("T")[1],
					PolygonText: polygons,
					DayOfWeek: dayOfWeek,
					externalIds: r.Items.filter(function(i) { return i.Gpsid }).map(function(i) { return i.Gpsid })
				}
			}).then(function(result)
			{
				return Promise.resolve(result.Items);
			})
		})

	}

	GeoFinderTool.prototype._getPolygonText = function(polygon)
	{
		var polygonText = "";
		if (polygon.rings.length > 1)
		{
			polygonText = "MULTIPOLYGON(";
			polygonText = getText(polygonText, polygon);
			polygonText += ")";

		}
		else if (polygon.rings.length == 1)
		{
			polygonText = "POLYGON";
			polygonText = getText(polygonText, polygon);
		}
		function getText(polygonText, polygon)
		{
			polygon.rings.forEach(function(r, rIndex)
			{
				polygonText += "((";
				r.forEach(function(point, index)
				{
					polygonText += point[0] + " " + point[1] + ",";
				});
				polygonText = polygonText.slice(0, -1);
				polygonText += ")),";
			});
			return polygonText.slice(0, -1);
		}
		return polygonText;
	}

	GeoFinderTool.prototype._activateDrawTool = function(type)
	{
		if (type == "polygon")
		{
			this._sketchVM.create("polygon", { mode: "click" });
		} else
		{
			this._sketchVM.create("point", { mode: "click" });
		}
		if (this.routingMapTool.routingMapDocumentViewModel.sketchTool)
		{
			this.routingMapTool.routingMapDocumentViewModel.sketchTool.currentDrawTool = this._sketchVM;
			this.routingMapTool.routingMapDocumentViewModel.sketchTool.currentDrawStatus = "geofinder-" + type;
		}
	};

	GeoFinderTool.prototype.initDrawPolygonLayer = function()
	{
		var self = this,
			GraphicsLayer = self._arcgis.GraphicsLayer;

		self._drawPolygonLayer = new GraphicsLayer({ id: "geoFinderDrawPolygonLayer" });
		self.map.add(self._drawPolygonLayer, 1);
	};

	GeoFinderTool.prototype.addWalkoutPolygon = function(geometry)
	{
		var self = this;
		var graphic = new self._arcgis.Graphic({
			geometry: geometry,
			symbol: self._geoFinderPolygonSymbol
		});
		self._drawPolygonLayer.add(graphic);
	};

	GeoFinderTool.prototype.startGeoFinder = function(type)
	{
		this.type = type;
		this.routingMapTool.startSketch("geoFinderTool");
		switch (type)
		{
			case "polygon":
				this._activateDrawTool("polygon");
				break;
			case "walkout":
				this._activateDrawTool("walkout");
				break;
			case "driveto":
				this._activateDrawTool("driveto");
				break;
			default:
				break;
		}
	}

	GeoFinderTool.prototype.endGeoFinder = function()
	{
		this.routingMapTool && this.routingMapTool.stopSketch("geoFinderTool");
		this._sketchVM && this._sketchVM.cancel();
		this._drawPolygonLayer && this._drawPolygonLayer.removeAll();
	};

	GeoFinderTool.prototype.clear = function()
	{
		this._drawPolygonLayer.removeAll();
	}

	GeoFinderTool.prototype.dispose = function()
	{
		this.endGeoFinder();
		this.onCreateHandler && this.onCreateHandler.remove();
		this._sketchVM && this._sketchVM.destroy();
		this.stopTool && this.stopTool.dispose();
		tfdispose(this);
	};
})();