﻿(function()
{
	createNamespace('TF.Control').MessageViewViewModel = MessageViewViewModel;

	function MessageViewViewModel(option)
	{
		const self = this,
			messages = option.messages,
			isStartup = option.isStartup;
			
		self.obIsStartup = ko.observable(isStartup === true);
		self.obMessages = ko.observableArray(messages);
		self.obShowIndex = ko.observable(0);
		self.orientationIndex = this.obShowIndex();
		ko.computed(function()
		{
			self.setTitle();
		}, self);

		// Hold observable variables from caller
		self.obActionButtonVisible = option.obActionButtonVisible;
		self.obActionButtonLabel = option.obActionButtonLabel;
		self.obActionButtonUrl = option.obActionButtonUrl;
		self.obActionBtnForeColor = option.obActionBtnForeColor;
		self.obActionBtnBackColor = option.obActionBtnBackColor;
	}

	MessageViewViewModel.prototype.init = function(viewModel, e)
	{
		this.element = $(e);
		this.setTitle();
		this.setContent();
	};

	MessageViewViewModel.prototype.setTitle = function()
	{
		if (this.element)
		{
			this.title = this.element.closest(".modal-content").find(".panel-heading");
			this.title.html("Transfinder Messages (" + (this.obShowIndex() + 1) + " of " + this.obMessages().length + ")");
		}
	};

	MessageViewViewModel.prototype.setContent = function(prevIndex)
	{
		const self = this,
			currentMsgIndex = self.obShowIndex(),
			currentMsgItem = self.obMessages()[currentMsgIndex],
			userId = tf.authManager.authorizationInfo.authorizationTree.userId,
			curMsgUniqueKey = currentMsgItem.uniqueKey,
			curMsgType = currentMsgItem.type,
			msgActionBtnVisible = currentMsgItem.showActionButton,
			msgActionBtnLabel = currentMsgItem.actionButtonSubject,
			msgActionBtnUrl = currentMsgItem.actionButtonUrl,
			msgActionBtnForeColor = currentMsgItem.actionButtonForeColor,
			msgActionBtnBackColor = currentMsgItem.actionButtonBackColor;

		let delayMS = 500; // Initial delay
		if (prevIndex !== undefined)
		{
			delayMS = 100; // non-initial content switching, set delay time to small value
			const prevMsgItem = self.obMessages()[prevIndex],
				prevMsgUniqueKey = prevMsgItem.uniqueKey;
				prevMsgType = prevMsgItem.type;
			if (prevMsgType === "video")
			{
				// For VIDEO message (prev message), we pause its video player
				const msgVideoElm = $(`video.msg-video[id=${prevMsgUniqueKey}]`)[0];
				if (msgVideoElm)
				{
					msgVideoElm.pause();
				}
			} else if (prevMsgType === "raw")
			{
				// For HTML message (prev message), we simply clear the HTML content of its iframe
				// so that it will get reloaded when user switch back to this message
				const prevMsgFrame = $(`iframe[id=${prevMsgUniqueKey}]`)[0];
				prevMsgFrame.contentWindow.document.body.innerHTML = "";
			}
		}

		// Update the observable properties for ActionButton
		self.obActionButtonVisible(msgActionBtnVisible);
		self.obActionButtonLabel(msgActionBtnLabel);
		self.obActionButtonUrl(msgActionBtnUrl);
		self.obActionBtnForeColor(msgActionBtnForeColor);
		self.obActionBtnBackColor(msgActionBtnBackColor);

		// Update the message content related UI elements
		setTimeout(function()
		{
			if (curMsgType === "raw")
			{
				let msgFrame = $(`iframe[id=${curMsgUniqueKey}]`)[0],
					msgFrameDoc = msgFrame.contentWindow.document;
				
				msgFrameDoc.body.innerHTML = `<style>body{margin:0;}</style>${currentMsgItem.Content}<span id="${curMsgUniqueKey}"><span>`;
			}
			else if (curMsgType === "video")
			{
				const msgVideoElm = $(`video.msg-video[id=${curMsgUniqueKey}]`)[0],
					msgVideoPlayElm = $(`div.msg-video-play[id=pb_${curMsgUniqueKey}]`)[0],
					videoUrl = currentMsgItem.videoUrl,
					autoplay = !!currentMsgItem.videoAutoPlay,
					loop = currentMsgItem.videoLoop === true;

				// Check if we need to initialize the video player (in case this video message is viewed for first time)
				if (msgVideoElm.src !== videoUrl)
				{
					msgVideoElm.autoplay = autoplay;
					msgVideoElm.loop = loop;
					msgVideoElm.src = videoUrl;

					msgVideoElm.onloadeddata = () =>
					{
						if (msgVideoElm.autoplay)
						{
							msgVideoElm.play();
						}
					};

					msgVideoElm.onplaying = () =>
					{
						$(msgVideoPlayElm).hide();
					};

					msgVideoElm.onpause = () =>
					{
						$(msgVideoPlayElm).show();
					};

					msgVideoElm.onended = () =>
					{
						$(msgVideoPlayElm).show();
					};

					msgVideoPlayElm.onclick = () =>
					{
						msgVideoElm.play();
						$(msgVideoPlayElm).hide();
					};
				}
				else
				{
					// The video player has already set src to target video url (means the message has been viewed before)
					// We simply resume its playing incase it's autoplay is true
					if (autoplay)
					{
						msgVideoElm.play();
					}
				}
			}
		}.bind(this), delayMS);

		if (!currentMsgItem.hasReadInCurrentLoad)
		{
			tf.messageCenterDataHelper.saveStructuredMessageReadHistory(currentMsgItem.uniqueKey, userId).then(result =>
				{
					if (result)
					{
						currentMsgItem.markReadInCurrentLogin();
		
						// Update total unread message count in NavigationMenu
						const newUnreadMsgCount = tf.navigationMenu.obMessageData().filter(function(item) { return item.hasRead() === false; }).length;
						tf.navigationMenu.obMessageCount(newUnreadMsgCount);
					}
				});
		}

	};

	MessageViewViewModel.prototype.viewLeft = function(model, e)
	{
		this.view("Left", e);
	};

	MessageViewViewModel.prototype.viewRight = function(model, e)
	{
		this.view("Right", e);
	};

	MessageViewViewModel.prototype.view = function(direction, e)
	{
		if ($(e.target).hasClass("disabled"))
		{
			return;
		}
		var details = this.element.find(".detail");
		var move = direction == "Right" ? "Left" : "Right";
		var removeClass = "active slideOutLeft slideInLeft slideOutRight slideInRight";
		details.eq(this.obShowIndex()).removeClass(removeClass).addClass("slideOut" + move);

		let prevIndex = this.obShowIndex();
		if (direction === "Right")
		{
			this.obShowIndex(prevIndex === this.obMessages().length - 1 ? 0 : prevIndex + 1);
		} else
		{
			this.obShowIndex(prevIndex > 0 ? prevIndex - 1 : this.obMessages().length - 1);
		}

		details.eq(this.obShowIndex()).removeClass(removeClass).addClass("slideIn" + move);
		this.setTitle();
		this.setContent(prevIndex);
	};

	MessageViewViewModel.prototype.dispose = function()
	{
		const self = this,
			$msgFrames = self.element.find("iframe.content"),
			$msgVideos = self.element.find("video.msg-video"),
			$msgVideoPlays = self.element.find("div.msg-video-play");
		
		if ($msgFrames && $msgFrames.length > 0)
		{
			for (let i = 0; i < $msgFrames.length; ++i)
			{
				const msgFrame = $msgFrames[i];
				msgFrame.src = "";
			}
		}

		if ($msgVideos && $msgVideos.length > 0)
		{
			for (let i = 0; i < $msgVideos.length; ++i)
			{
				const msgVideo = $msgVideos[i];
				msgVideo.src = "";
				msgVideo.onloadeddata = msgVideo.onplaying = msgVideo.onpause = msgVideo.onended = null;
			}
		}

		if ($msgVideoPlays && $msgVideoPlays.length > 0)
		{
			for (let i = 0; i < $msgVideoPlays.length; ++i)
			{
				const videoPlayBtn = $msgVideoPlays[i];
				videoPlayBtn.onclick = null;
			}
		}
	};
})();