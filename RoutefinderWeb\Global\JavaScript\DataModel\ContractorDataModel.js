﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.ContractorDataModel = function(contractorEntity)
	{
		namespace.BaseDataModel.call(this, contractorEntity);

		this.equipments = ko.observable("");
	}

	namespace.ContractorDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ContractorDataModel.prototype.constructor = namespace.ContractorDataModel;

	// namespace.ContractorDataModel.prototype.mapping = [
	// 	{ from: "Id", default: 0 },
	// 	{ from: "DBID", default: function() { return tf.datasourceManager.databaseId; } },
	// 	{ from: "Name", default: "" },
	// 	{ from: "Comments", default: "" },
	// 	{ from: "MailStreet1", default: "" },
	// 	{ from: "MailStreet2", default: "" },
	// 	{ from: "MailCity", default: function() { return tf.setting.userProfile.Mailcity; } },
	// 	{ from: "MailState", default: function() { return tf.setting.userProfile.MailState; } },
	// 	{ from: "MailZip", default: function() { return tf.setting.userProfile.Mailzip; } },
	// 	{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
	// 	{ from: "LastUpdatedId", default: 0 },
	// 	{ from: "LastUpdatedName", default: "" },
	// 	{ from: "LastUpdatedType", default: 0 },
	// 	{ from: "System1", default: "" },
	// 	{ from: "System2", default: "" },
	// 	{ from: "System3", default: "" },
	// 	{ from: "System4", default: "" }
	// ];

	namespace.ContractorDataModel.prototype.mapping = [
		{ "from": "Comments", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "Id", "default": 0 },
		{ "from": "Lastupdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastupdatedId", "default": 0 },
		{ "from": "Lastupdatedtype", "default": 0 },
		{ "from": "MailCity", "default": function() { return tf.setting.userProfile.MailCityName; } },
		{ "from": "MailCityId", "default": function() { return tf.setting.userProfile.MailCity; } },
		{ "from": "MailState", "default": function() { return tf.setting.userProfile.MailStateName; } },
		{ "from": "MailStateId", "default": function() { return tf.setting.userProfile.MailState; } },
		{ "from": "MailStreet1", "default": "" },
		{ "from": "MailStreet2", "default": "" },
		{ "from": "MailZip", "default": function() { return tf.setting.userProfile.MailZipName; } },
		{ "from": "MailZipId", "default": function() { return tf.setting.userProfile.MailPostalCode; } },
		{ "from": "Name", "default": "" },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
	];

})();
