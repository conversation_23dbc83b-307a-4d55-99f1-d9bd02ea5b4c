﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleEquipmentCodeDeletion = DataListVehicleEquipmentCodeDeletion;

	function DataListVehicleEquipmentCodeDeletion()
	{
		this.type = 'vehicleequipment';
		this.deleteType = 'Code';
		this.deleteRecordName = 'Vehicle Equipment Code';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleEquipmentCodeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleEquipmentCodeDeletion.prototype.constructor = DataListVehicleEquipmentCodeDeletion;

	DataListVehicleEquipmentCodeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();