.switch-grid-menu.tf-contextmenu-white.field-trip-configs-switch-grid-menu {
	width: 280px;
}

.tabstrip-fieldtripconfigs {
	.email-notification-wrapper,
	.general-options-wrapper {
		padding: 0 25px;

		> :nth-child(odd) {
			padding-bottom: 5px;
		}

		> :nth-child(even) {
			padding-bottom: 15px;
		}

		.row {
			margin: 0;
			font-size: 12px;

			>div {
				padding-left: 0;
				padding-right: 10%;

				label {
					font-size: 15px;
					font-weight: bold
				}

				label:nth-child(even) {
					cursor: pointer;
				}
			}

			.textinput {
				margin-left: 15px;
				display: inline-block;
				width: 80px;
				vertical-align: top;
			}

			input[type=checkbox] {
				cursor: pointer;
				margin: 5px 10px 0px 0px;
				vertical-align: top;
			}

			.sub-title {
				font-size: 16px;
				font-weight: bold;
			}

			.text-content {
				font-family: Arial, Helvetica, sans-serif;
			}

			&.save {
				border-top: 1px solid #C4C4C4;
				background-color: #f2f2f2;
				padding-bottom: 0px;
				margin-left: 0;
				line-height: 37px;
				height: 40px;

				button {
					border-radius: 0;
					padding: 0;
					height: 30px;
					width: 108px;
					border: none;
					background: none;
					color: #333;
					margin-left: 10px;

					&:hover {
						background-color: #333333;
						color: #ffffff;
					}

					&.actived {
						background-color: #333333;
						color: #ffffff;
					}
				}
			}
		}
	}

	.splitter-wrapper {
		border: 1px solid #ccc;
	}

	.grid-wrapper {
		padding: 0 15px;

		.kendo-grid {
			height: calc(~"100vh - 430px");
		}

		.k-grid-edit,
		.k-grid-delete {
			margin: 0 6px;
		}
	}
}

.fieldtrip-deadline-type{
	display: flex;
	align-items: center;
	gap: 3px;
	margin-bottom: 5px;

	input{
		margin: 0;
	}

	label{
		margin-right: 10px;
	}
}

.fieldtrip-strict-destinations {
	padding-bottom: 15px;
}