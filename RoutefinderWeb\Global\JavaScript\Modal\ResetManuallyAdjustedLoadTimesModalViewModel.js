﻿(function()
{
	createNamespace("TF.Modal").ResetManuallyAdjustedLoadTimesModalViewModel = ResetManuallyAdjustedLoadTimesModalViewModel;

	function ResetManuallyAdjustedLoadTimesModalViewModel(studentsCount, studentsManuallyAdjustedCount, tripStopsCount, tripStopsManuallyAdjustedCount)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Reset Manually Adjusted ' + tf.applicationTerm.getApplicationTermPluralByName("Load Time"));
		this.sizeCss = "modal-dialog-lg";
		this.obNegativeButtonLabel("Close");
		this.contentTemplate('modal/resetmanuallyadjustedloadtimes');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("OK");
		this.obNegativeButtonLabel("Cancel");
		this.obStudentLoadTimes = ko.observable();
		this.obTotalStopTimes = ko.observable();
		this.obStudentsCount = ko.observable(studentsCount);
		this.obStudentsManuallyAdjustedCount = ko.observable(studentsManuallyAdjustedCount);
		this.obTripStopsCount = ko.observable(tripStopsCount);
		this.obTripStopsManuallyAdjustedCount = ko.observable(tripStopsManuallyAdjustedCount);
	};

	ResetManuallyAdjustedLoadTimesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ResetManuallyAdjustedLoadTimesModalViewModel.prototype.constructor = ResetManuallyAdjustedLoadTimesModalViewModel;

	ResetManuallyAdjustedLoadTimesModalViewModel.prototype.positiveClose = function(returnData)
	{
		this.hide();
		this.resolve({ checkStudentLoadTimes: this.obStudentLoadTimes(), checkTotalStopTimes: this.obTotalStopTimes() });
	};
})();

