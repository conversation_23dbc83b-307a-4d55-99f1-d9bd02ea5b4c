﻿(function()
{
	createNamespace("TF.Modal.Navigation").OpenDataSourceModalViewModel = OpenDataSourceModalViewModel;

	function OpenDataSourceModalViewModel(isDashboard)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-lg";
		this.title('Data Source');
		this.helpKey = "datasource";
		const prePath = isDashboard ? "en-US/Html/" : "";
		this.contentTemplate(prePath + 'Navigation/OpenDataSource');
		this.buttonTemplate(prePath + 'modal/positivenegativeother');
		this.obPositiveButtonLabel("Open");
		this.obOtherButtonLabel("Archives");
		this.obDisableControl(true);
		this.openDataSourceViewModel = new TF.Navigation.OpenKendoDataSourceViewModel(this, isDashboard);
		this.data(this.openDataSourceViewModel);
	}

	OpenDataSourceModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	OpenDataSourceModalViewModel.prototype.constructor = OpenDataSourceModalViewModel;

	OpenDataSourceModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.openDataSourceViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this)).catch(function()
		{
		});
	};

	OpenDataSourceModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		if (tf.datasourceManager.databaseId)
		{
			this.negativeClose();
		}
		else
		{
			tf.promiseBootbox.alert("No Data Source selected. Please select one before closing.");
		}
	}

	OpenDataSourceModalViewModel.prototype.otherClick = function()
	{
		const func = this.openDataSourceViewModel.refreshGrid.bind(this.openDataSourceViewModel);
		tf.modalManager.showModal(new TF.Modal.Navigation.ManageArchiveModalViewModel(func));
	}

})();