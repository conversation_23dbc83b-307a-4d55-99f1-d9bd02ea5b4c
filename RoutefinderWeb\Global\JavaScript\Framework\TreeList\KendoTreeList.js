(function()
{
	createNamespace("TF.TreeList").KendoTreeList = KendoTreeList;

	function KendoTreeList($container, options)
	{
		const self = this;
		self.STR_DROPHINTTEMPLATE = "<div name='self-drop-hint' class='k-drop-hint k-drop-hint-h'><div class='k-drop-hint-start'></div><div class='k-drop-hint-line'></div><div class='k-drop-hint-end drop-hint-end'></div>";
		self.STR_VISIBILITY = "visibility";
		self.STR_APPENDTO = 'appendTo';
		self.$element = $container;
		self.options = options;
		self.init();
	}

	KendoTreeList.prototype.constructor = KendoTreeList;

	KendoTreeList.prototype.init = function()
	{
		const self = this;
		self.initParameter();
		self.createKendoTreeList();
		self.bindingEvent();
	};

	KendoTreeList.prototype.initParameter = function()
	{
		const self = this;
		self.dataSource = self.options.dataSource;
		self.toolbar = self.options.toolbar || null;
		self.editable = self.options.editable || {
			move: {
				reorderable: true
			}
		};
		self.columns = self.options.columns || [];
		self.hasMaxLevel = self.options.hasMaxLevel || false;
		self.maxLevel = self.options.maxLevel || -1;
		self.canChangeLevel = self.options.canChangeLevel || false;
		self.scrollable = (self.options.scrollable === null || self.options.scrollable === undefined) ? true : self.options.scrollable;
		self.selectable = self.options.selectable || "single";
		self.resizable = self.options.resizable || false;
		self.height = self.options.height || 472;
		self.rowTemplate = self.options.rowTemplate || null;
		self.altRowTemplate = self.options.altRowTemplate || null;
		self.dataBound = self.options.dataBound || null;
		self.dragstart = self.options.dragstart || null;
		self.dragend = self.options.dragend || null;
		self.dropCallback = self.options.drop || null;
		self.checkDuplicatesCallback = self.options.checkDuplicatesCallback || null;
		self.columnResize = self.options.columnResize || null;
	};

	KendoTreeList.prototype.createKendoTreeList = function()
	{
		const self = this;
		self.$element.kendoTreeList({
			dataSource: self.dataSource,
			toolbar: self.toolbar,
			columnResize: self.columnResize,
			editable: {
				move: {
					reorderable: true
				}
			},
			columns: self.columns,
			scrollable: self.scrollable,
			selectable: self.selectable,
			resizable: self.resizable,
			height: self.height,
			rowTemplate: self.rowTemplate,
			altRowTemplate: self.altRowTemplate,
			dataBound: self.dataBound,
			dragstart: self.dragstart,
			dragend: self.dragend
		});
	};

	KendoTreeList.prototype.bindingEvent = function()
	{
		const self = this;
		var treeList = self.$element.data("kendoTreeList");
		treeList._dragging.options.hintText = function(row)
		{
			return $(row.find("td")[1]).text();
		};
		treeList.bind("drag", self.onDrag.bind(this));
		treeList.bind("dragend", self.onDragEnd.bind(this));
		treeList.bind("drop", self.onDrop.bind(this));
	};

	KendoTreeList.prototype.onDrag = function(e)
	{
		const self = this;
		self.$element.find("div[name='self-drop-hint']").remove();
		const sourceDataItem = e.source, targetDataItem = e.sender.dataItem(e.target);
		if (targetDataItem)
		{
			let correctStatus = e.status;
			const sourceLevel = self.dataSource.level(sourceDataItem), targetLevel = self.dataSource.level(targetDataItem);
			if (!self.canChangeLevel && e.status !== 'cancel')
			{
				if (sourceDataItem.parentId === targetDataItem.Sequence)
				{
					correctStatus = 'cancel';
				}
				else
				{
					correctStatus = self.generateCorrectDragStatus(sourceLevel, targetLevel, e.status);
					if (correctStatus !== 'cancel' && self.checkDuplicatesCallback && !self.checkDuplicatesCallback(sourceDataItem, targetDataItem))
					{
						correctStatus = 'cancel';
					}
				}

				if (e.status !== correctStatus)
				{
					e.setStatus(correctStatus);
				}
			}

			if (targetLevel === (sourceLevel - 1) && sourceDataItem.parentId !== targetDataItem.Sequence && correctStatus !== 'cancel')
			{
				const firstTD = e.target.closest('tr').find('td:first-child');
				const currHeight = firstTD.outerHeight();
				const currPosition = firstTD.position();
				currPosition.top = currPosition.top + currHeight;
				self.dropHint = $(self.STR_DROPHINTTEMPLATE).css(self.STR_VISIBILITY, 'hidden').appendTo(self.$element);
				self.dropHint.css(currPosition)[self.STR_APPENDTO](firstTD);
				self.dropHint.css(self.STR_VISIBILITY, 'visible');
			}
		}
		else if (e.status !== 'cancel')
		{
			e.setStatus('cancel');
		}

		const $dropHint = self.$element.find(".k-drop-hint");
		const $treeList = self.$element;
		if ($dropHint.find(".k-drop-hint-end").length === 0)
		{
			$dropHint.append('<div class="k-drop-hint-end drop-hint-end"></div>');
		}
		if ($dropHint.width() !== $treeList.width())
		{
			// If set scrollable to false for kendoTreeList, the kendoTreeList will not have k-grid-content class.
			const dropHintWidth = $treeList.find(".k-grid-content")[0] ? $treeList.find(".k-grid-content")[0].clientWidth - 1 : $treeList.width() - 1;
			$dropHint.width(dropHintWidth);
		}

		const $dragClue = self.$element.find(".k-drag-clue");
		if ($dragClue && $dragClue.length > 0 && !$dragClue.hasClass('custom-clue'))
		{
			$dragClue.find(".k-drag-status").hide();
			$dragClue.addClass("custom-clue");
			$dragClue.width($treeList.width() - 40);
		}
		$dropHint.addClass('drop-hint');
		self.$element.find(".k-drop-hint-start").addClass('drop-hint-start');
		self.$element.find(".k-drop-hint-line").addClass('drop-hint-line');
	};

	KendoTreeList.prototype.onDragEnd = function(e)
	{
		const self = this;
		const sourceDataItem = e.source, targetDataItem = e.destination;
		if (targetDataItem)
		{
			const sourceLevel = self.dataSource.level(sourceDataItem), targetLevel = self.dataSource.level(targetDataItem);
			if (targetLevel === (sourceLevel - 1) && sourceDataItem.parentId === targetDataItem.Sequence)
			{
				// find row for target section item
				var row = self.$element.data("kendoTreeList").content.find(`tr[data-kendo-uid=${targetDataItem.uid}]`);
				self.$element.data("kendoTreeList").expand(row);
			}
		}
		self.$element.find("div[name='self-drop-hint']").remove();
	};

	KendoTreeList.prototype.onDrop = function(e)
	{
		const self = this;
		self.dropCallback && self.dropCallback(e);
	};

	KendoTreeList.prototype.generateCorrectDragStatus = function(sourceLevel, targetLevel, status)
	{
		if (targetLevel === sourceLevel)
		{
			if (status === "plus")
			{
				return 'cancel';
			}
			else
			{
				return status;
			}
		}
		else if (targetLevel === (sourceLevel - 1))
		{
			return 'plus';
		}
		else
		{
			return 'cancel';
		}
	};
})();
