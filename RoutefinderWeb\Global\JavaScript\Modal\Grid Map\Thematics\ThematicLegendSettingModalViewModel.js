(function()
{
	createNamespace("TF.Modal").ThematicLegendSettingModalViewModel = ThematicLegendSettingModalViewModel;
	var defaults = {
		legendTitleIsShow: true,
		legendDescriptionIsShow: true,
		isTripNameShow: true,
		isTripDateIntervalShow: false,
		isMapCanvasUnassignStudent: false,
	}
	/**
	 * Constructor of SettingsModalViewModel
	 * @returns {void}
	 */
	function ThematicLegendSettingModalViewModel(options)
	{
		var self = this;
		options = $.extend({}, defaults, options);
		TF.Modal.BaseModalViewModel.call(self);
		self.title('Legend Settings');
		self.sizeCss = "modal-sm legend-setting";
		self.buttonTemplate('modal/positivenegative');
		self.contentTemplate('Modal/Grid Map/Thematics/LegendSetting');
		self.obPositiveButtonLabel("Apply");
		self.obDisplayLegendName = ko.observable(options.legendTitleIsShow);
		self.obDisplayLegendDescription = ko.observable(options.legendDescriptionIsShow);
		self.obTripNameShow = ko.observable(options.isTripNameShow);
		self.obTripDateIntervalShow = ko.observable(options.isTripDateIntervalShow);
		const isRoutingPaletteShowing = !!tf.documentManagerViewModel.obCurrentDocument()?.routingPaletteViewModel?.obShow();
		self.tripNameVisible = isRoutingPaletteShowing;
		self.tripDateIntervalVisible = isRoutingPaletteShowing && tf.authManager.hasTripDates();
	}

	ThematicLegendSettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ThematicLegendSettingModalViewModel.prototype.constructor = ThematicLegendSettingModalViewModel;

	ThematicLegendSettingModalViewModel.prototype.positiveClick = function()
	{
		var self = this, data = {
			nameChecked: self.obDisplayLegendName(),
			descriptionChecked: self.obDisplayLegendDescription(),
			isTripNameShow: self.obTripNameShow(),
			isTripDateIntervalShow: self.obTripDateIntervalShow()
		}
		self.positiveClose(data);
	};

})();