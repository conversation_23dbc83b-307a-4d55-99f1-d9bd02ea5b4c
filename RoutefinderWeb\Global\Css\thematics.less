@import "z-index";
@systemColor: #D0503C;

.modal-dialog-lg.manageThematic {
	font-family: "SourceSansPro-Regular";

	.titleNotify {
		font-family: "SourceSansPro-Regular";
	}
}

.modal-body .thematic-name {
	margin-bottom: 5px;

	&.has-error {
		input {
			border-color: #E71931;

			&.delay-show {
				border-color: #bcbcbc;
			}
		}
	}

	label {
		width: 100%;
		font-weight: bold;
		color: #333333;
		font-size: 14px;
		font-family: "SourceSansPro-SemiBold";
		padding-top: 3px;
	}

	input {
		width: 100%;
		font-size: 15px;
		border: none;
		border-bottom: 1px solid #bcbcbc;
		outline-color: transparent;
		color: #333333;
		padding-bottom: 4px;
		height: 24px;
	}

	.help-block {
		font-family: "SourceSansPro-Italic";
	}

	small.help-block {
		font-size: 10px;
		padding-bottom: 6px;
		color: #E71931 !important;

		&.always-show {
			display: block !important;
		}

		&.always-hide {
			display: none !important;
		}
	}
}

.modal-body.saveNewThematic-modal {
	padding-top: 20px;
	line-height: normal;

	.thematic-name {
		height: 64px;
	}
}

.modal-body.editThematic-modal {
	padding: 0;
	line-height: normal;

	.edit-thematic {
		padding-bottom: 10px;
		color: #333;
		font-family: "SourceSansPro-Regular";

		&.new-mode .row {
			margin-top: 30px;
		}

		.thematic-name {
			height: 99px;
			padding: 20px 15px 15px 30px;
			background-color: #f2f2f2;
			margin-left: -15px;

			input {
				background-color: #f2f2f2;
			}
		}

		.row {
			margin: 0;
			margin-top: 20px;
		}

		.grid {
			height: 330px;
			padding-bottom: 0;

			&.edit {
				height: 403px;
			}
		}

		.fields-main .field-item .field-filter .custom-filter {
			&.active .k-filter-menu .k-textbox {
				&[data-tf-input-type="Time"] {
					width: calc(~"100% - 22px");
				}
			}

			.k-filter-menu .input-group .datepickerbutton {
				margin-top: 0.3em;
			}

			.k-filter-menu .k-dropdown {
				width: 100%;
			}

			>div {
				padding: 3px;
			}

			.custom-filter-buttons {
				margin-top: 3px;
			}
		}

		.fields-main .field-item .field-filter .text-input input {
			width: 100%;
		}

		.applycheckbox-form-group {
			padding-left: 15px;
		}
	}
}

.manage-thematics {
	.grid-footer {
		background-color: transparent;
		float: right;
	}
}

.map-panel .esriMapContainer svg[id*="esri.Map"] {
	z-index: 2;
}

.used-for-focus {
	height: 0;
	width: 0;
	border: 0;
	padding: 0;
	position: absolute;
}

.thematic-grid.k-grid.combination .k-grid-footer .k-grid-footer-wrap {
	height: 72px;

	td {
		background-color: #ecf2f9;
	}

	.combination td {
		background-color: #fff;
	}
}

.symbols-panel {
	display: none;
	position: absolute;
	width: 399px;
	height: 310px;
	top: 74px;
	background: #fff;
	overflow-y: auto;
	border: 1px solid #e4e4e4;
	box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.3);
	padding: 16px 0 0 0;
	overflow-x: hidden;
	z-index: 1;

	.none-item-container {
		margin-bottom: 6px;
		float: left;
		margin-left: 6px;
		border: 1px solid transparent;
		box-sizing: content-box;
		cursor: pointer;

		&:hover {
			background-color: #cacaca;
			border-radius: 3px;
		}

		&.selected {
			border: 1px solid @systemColor;
			border-radius: 3px;
		}

		.none-item {
			cursor: pointer;
			padding: 4px;
			font-size: 10px;
		}
	}

	.default-container {
		.symbol-item {
			margin-left: 5px;
			display: flex;
			height: 21px;
			width: 24px;
			float: left;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			box-sizing: content-box;
			border: 1px solid transparent;

			&.selected {
				border: 1px solid @systemColor;
				border-radius: 3px;
			}

			&:hover {
				background-color: #cacaca;
				border-radius: 3px;
			}
		}
	}

	.category-container {
		float: left;
		margin-left: -2px;
		margin-right: -2px;
		margin-top: 10px;

		&:first-child {
			margin-top: 0;
		}

		.category-header {
			font-family: "SourceSansPro-SemiBold";
			font-size: 10px;
			text-transform: uppercase;
			color: @systemColor;
			margin-bottom: 6px;
			padding-left: 12px;
		}

		.symbol-item {
			display: flex;
			height: 32px;
			width: 32px;
			float: left;
			margin: 0px 7px 10px 7px;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			box-sizing: content-box;
			border: 1px solid transparent;

			&.selected {
				border: 1px solid @systemColor;
				border-radius: 3px;
			}

			&:hover {
				background-color: #cacaca;
				border-radius: 3px;
			}
		}
	}
}

.filter-menu-list-container {
	width: 200px;
	max-height: 300px;
	overflow: auto;

	.k-list.k-reset {
		overflow: auto;
	}
}

.custom-filter {
	width: 173px !important;

	.k-input {
		width: calc(~"100% - 22px") !important;
	}
	.k-input-inner {
		width: calc(~"100% - 22px") !important;
	}
	.datepickerbutton {
		margin-top: 7px !important;
	}
}

@import "thematicsMenu";
@import "thematicsQuickFilter";
@import "thematicsGrid";
@import "thematicsAdjustValue";
@import "thematicsLegend";