(function()
{
	createNamespace('TF.Modal').AdjustValueDisplayModalViewModel = AdjustValueDisplayModalViewModel;

	/**
	 * Constructor of AdjustValueDisplayModalViewModel
	 * @returns {void}
	 */
	function AdjustValueDisplayModalViewModel(displayDetail, removeNone)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.buttonTemplate('modal/positivenegative');
		self.contentTemplate('Modal/Grid Map/Thematics/AdjustValueDisplay');
		self.sizeCss = "modal-dialog-sm adjust-value-display";
		self.title(displayDetail.title ? displayDetail.title : 'Symbol (' + displayDetail.name + ')');
		self.obPositiveButtonLabel("Apply");
		self.obNegativeButtonLabel("Close");
		self.removeNone = !!removeNone;
		self.originDetail = displayDetail;
		self.selectedColor = (typeof (displayDetail.color) === 'string') ? displayDetail.color : "#d9dde4";
		self.selectedSymbol = (typeof (displayDetail.symbol) === 'string') ? displayDetail.symbol : "66";
		self.selectedSize = (typeof (displayDetail.size) === 'string') ? displayDetail.size : "12";
		self.selectedBorderSize = (typeof (displayDetail.bordersize) === 'string') ? displayDetail.bordersize : "1";
		self.selectedBorderColor = (typeof (displayDetail.bordercolor) === 'string') ? displayDetail.bordercolor : "#00ACC1";
		self.borderIsShow = displayDetail.borderishow;
		self.symbolName = "";
		self.symbolColorPicker = null;
		self.borderColorPicker = null;
		self.dropDownDataSource = self.getDropDownDataSource();
		self.obSymbolSize = ko.observable(parseFloat(self.selectedSize));
		self.obBorderSize = ko.observable(parseFloat(self.selectedBorderSize));
		self.obBorderIsShow = ko.observable(self.borderIsShow);
		self.obOtherFields = ko.observable(true);
		self.defaultSymbol = displayDetail.defaultSymbol;

		//Event
		self.init = self.init.bind(self);
		self.updateSymbolSize = self.updateSymbolSize.bind(self);
		self.setSymbolSizeSliderColor = self.setSymbolSizeSliderColor.bind(self);
		self.symbolSliderRendered = self.symbolSliderRendered.bind(self);
		self.borderSliderRendered = self.borderSliderRendered.bind(self);
		self.updateBorderSize = self.updateBorderSize.bind(self);
		self.setBorderSizeSliderColor = self.setBorderSizeSliderColor.bind(self);
		self.setSymbolThumbnail = self.setSymbolThumbnail.bind(self);
		self.showBorderDetial = self.showBorderDetial.bind(self);
	}

	AdjustValueDisplayModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AdjustValueDisplayModalViewModel.prototype.constructor = AdjustValueDisplayModalViewModel;

	AdjustValueDisplayModalViewModel.prototype.getDropDownSVG = function(svgType, size)
	{
		var svgSymbol = TF.Helper.AdjustValueSymbolHelper.getSVGSymbolString(svgType, "#000000", size);
		return svgSymbol;
	}

	AdjustValueDisplayModalViewModel.prototype.getDropDownDataSource = function()
	{
		var self = this, dDataSourcSVG, dDataSource = [];
		dDataSourcSVG = thematicSymbolPath;
		for (var i = 0; i < dDataSourcSVG.length; i++)
		{
			var obj = {};
			obj.value = dDataSourcSVG[i].id;
			obj.category = dDataSourcSVG[i].category;
			obj.valueTemplate = TF.Helper.AdjustValueSymbolHelper.getSVGSymbolString(obj.value, "#000000", "18");
			obj.template = TF.Helper.AdjustValueSymbolHelper.getSVGSymbolString(obj.value, "#000000", "24");
			dDataSource.push(obj);
		}
		return dDataSource;
	}

	AdjustValueDisplayModalViewModel.prototype.init = function(model, e)
	{
		var self = this, cookieSymbolColor = "SymbolColor", cookieBorderColor = "BorderColor", content;
		self.$form = $(e);
		self.symbolColorPicker = self.$form.find("#symbol-color-selector [name=color]").kendoColorPicker(
			{
				buttons: false,
				value: self.selectedColor,
				cookieName: cookieSymbolColor,
				change: function(e)
				{
					self.selectedColor = e.sender.element[0].value;
					var newCookie = { activeColor: self.selectedColor }, cookie = JSON.parse($.cookie("SymbolColor") || "{}");
					if (cookie && cookie.colorArray && cookie.colorArray.length > 0)
					{
						newCookie.colorArray = cookie.colorArray;
					}
					$.cookie(cookieSymbolColor, JSON.stringify(newCookie));
					self.setSymbolSizeSliderColor();
					TF.Helper.AdjustValueSymbolHelper.changeSymbolColor(self.$form.find('#symbol-thumbnail-container .point svg'), self.selectedColor);
				}
			}).data("kendoColorPicker");
		self.borderColorPicker = self.$form.find("#border-color-selector [name=color]").kendoColorPicker(
			{
				buttons: false,
				value: self.selectedBorderColor,
				cookieName: cookieBorderColor,
				change: function(e)
				{
					self.selectedBorderColor = e.sender.element[0].value;
					var newCookie = { activeColor: self.selectedBorderColor }, cookie = JSON.parse($.cookie("BorderColor") || '{}');
					if (cookie && cookie.colorArray && cookie.colorArray.length > 0)
					{
						newCookie.colorArray = cookie.colorArray;
					}
					$.cookie(cookieBorderColor, JSON.stringify(newCookie));
					self.setBorderSizeSliderColor();
					TF.Helper.AdjustValueSymbolHelper.changeSymbolBorderColor(self.$form.find('#symbol-thumbnail-container .point svg'), self.selectedBorderColor);
				}
			}).data("kendoColorPicker");

		if (Number(self.selectedSymbol) >= 0)
		{
			for (var i = 0; i < self.dropDownDataSource.length; i++)
			{
				if (self.dropDownDataSource[i].value === Number(self.selectedSymbol))
				{
					content = self.dropDownDataSource[i].valueTemplate;
				}
			}
		}
		else
		{
			// -1 indicates None
			if (self.selectedSymbol === "-1")
			{
				content = "None";
			}
			else if (self.selectedSymbol === "default")
			{
				content = self.defaultSymbol;
			}
		}
		self.$form.find(".currentSymbol").html(content);
		self.$form.find("#symbol-selector .symbol-container").on("click", self.openSymbolsPanel.bind(self));
		self.setSymbolThumbnail();
		self.showBorderDetial(self.obBorderIsShow());
		self.obBorderIsShow.subscribe(self.showBorderDetial.bind(self));
		self.showOtherFields();

		//If color picker is opened, do not close modal when back drop is clicked.
		$("body").on("mousedown.adjustModal", function(e)
		{
			if ($(e.target).closest(".symbols-panel").length <= 0)
			{
				self.$form.parent().find(".symbols-panel").hide();
			}
		});
	};

	AdjustValueDisplayModalViewModel.prototype.openSymbolsPanel = function()
	{
		var self = this, symbolPanel = self.$form.parent().find(".symbols-panel");
		symbolPanel.show();
		if (symbolPanel.children().length <= 0)
		{
			self.addSymbolsToPanel(symbolPanel);
		}
		symbolPanel.find(".symbol-item, .none-item, .none-item-container").removeClass("selected");
		if (Number(self.selectedSymbol) >= 0)
		{
			symbolPanel.find(".symbol-item[value=" + Number(self.selectedSymbol) + "]").addClass("selected");
		}
		else
		{
			// -1 indicates None
			if (self.selectedSymbol === "-1")
			{
				symbolPanel.find(".none-item").parent().addClass("selected");
			}
			else
			{
				symbolPanel.find(".symbol-item[value=" + self.selectedSymbol + "]").addClass("selected");
			}
		}
	};

	AdjustValueDisplayModalViewModel.prototype.addSymbolsToPanel = function(symbolPanel)
	{
		var self = this, groupedSource = {}, symbols, categoryContainer;
		$.each(self.dropDownDataSource, function(index, item)
		{
			if (!groupedSource[item.category])
			{
				groupedSource[item.category] = [];
			}
			groupedSource[item.category].push(item);
		});

		if (!self.removeNone)
		{
			symbolPanel.append($("<div class='none-item-container'><span class='none-item' value=-1>None</span></div>"));
		}

		if (self.defaultSymbol)
		{
			symbolPanel.append($("<div class='default-container'><span class='symbol-item' value='default'>" + self.defaultSymbol + "</span></div>"));
		}

		$.each(Object.keys(groupedSource), function(index, key)
		{
			symbols = groupedSource[key];
			categoryContainer = $("<div class='category-container'></div>");
			categoryContainer.append("<div class='category-header'>" + key + "</div>");
			symbolPanel.append(categoryContainer);
			$.each(symbols, function(index, symbol)
			{
				categoryContainer.append($("<div class='symbol-item' value=" + symbol.value + ">" + symbol.template + "</div>"));
			});
		});
		symbolPanel.find(".symbol-item, .none-item").on("click.selectsymbol", function(e)
		{
			var $target = $(e.target).closest(".symbol-item, .none-item"), content;
			self.selectedSymbol = $target.attr("value");

			if (Number(self.selectedSymbol) >= 0)
			{
				for (var i = 0; i < self.dropDownDataSource.length; i++)
				{
					if (self.dropDownDataSource[i].value === Number(self.selectedSymbol))
					{
						content = self.dropDownDataSource[i].valueTemplate;
					}
				}
			}
			else
			{
				// -1 indicates None
				if (self.selectedSymbol === "-1")
				{
					content = "None";
				}
				else if (self.selectedSymbol === "default")
				{
					content = self.defaultSymbol;
				}
			}
			self.$form.find(".currentSymbol").html(content);
			self.setSymbolThumbnail();
			self.showOtherFields();
			self.$form.parent().find(".symbols-panel").hide();
		});
	};

	/**
	 * The positiveClick function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.positiveClick = function()
	{
		var self = this;
		self.positiveClose(self.dataSave());
	};

	/**
	 * Override the close modal event.
	 * @param {object} viewModel data model.
	 * @param {object} e element.
	 * @return {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		this.negativeClose();
	};

	/**
	 * The dataSave function save the data in the modal.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.dataSave = function()
	{
		var self = this,
			detialData = {
				symbol: self.selectedSymbol,
				size: self.selectedSize,
				color: self.selectedColor,
				borderishow: self.borderIsShow,
				bordersize: self.selectedBorderSize,
				bordercolor: self.selectedBorderColor

			};
		if (self.selectedSymbol !== self.originDetail.symbol || self.selectedColor !== self.originDetail.color || self.selectedSize !== self.originDetail.size
			|| self.selectedBorderSize !== self.originDetail.bordersize || self.selectedBorderColor !== self.originDetail.bordercolor || self.borderIsShow != self.originDetail.borderishow)
		{
			detialData.changed = true;
		}
		else
		{
			detialData.changed = false;
		}
		return detialData;
	};

	/**
	 * The updateSymbolSize function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.updateSymbolSize = function()
	{
		var self = this, borderSize;
		self.selectedSize = String(self.obSymbolSize());

		if (self.selectedSymbol === "default")
		{
			return;
		}

		if (self.obBorderIsShow())
		{
			borderSize = self.selectedBorderSize
		}
		TF.Helper.AdjustValueSymbolHelper.changeSymbolSize(self.$form.find("#symbol-thumbnail-container .point svg"), self.selectedSize, borderSize);

		// remove the tabindex on slider
		setTimeout(() =>
		{
			self.$form.find("div[tabindex='0']").attr("tabindex", "-1");
		});
	};

	/**
	 * The updateBorderSize function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.updateBorderSize = function()
	{
		var self = this;

		if (self.selectedSymbol === "default")
		{
			return;
		}

		self.selectedBorderSize = String(self.obBorderSize());
		TF.Helper.AdjustValueSymbolHelper.changeSymbolSize(self.$form.find("#symbol-thumbnail-container .point svg"), self.selectedSize, self.selectedBorderSize);
	};

	/**
	 * The setSymbolSizeSliderColor function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.setSymbolSizeSliderColor = function()
	{
		var self = this;
		self.$form.find("#symbol-size-slider .slider-selection").css("background", self.selectedColor);
	};

	AdjustValueDisplayModalViewModel.prototype.symbolSliderRendered = function()
	{
		this.$form.find("#symbol-size-slider").on("mousedown.closeColorPicker", e =>
		{
			this.closeColorPicker();
		});
	};

	AdjustValueDisplayModalViewModel.prototype.borderSliderRendered = function()
	{
		this.$form.find("#border-size-slider").on("mousedown.closeColorPicker", e =>
		{
			this.closeColorPicker();
		});
	};

	AdjustValueDisplayModalViewModel.prototype.closeColorPicker = function()
	{
		this.symbolColorPicker && this.symbolColorPicker.close();
		this.borderColorPicker && this.borderColorPicker.close();
	}

	/**
	 * The setBorderSizeSliderColor function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.setBorderSizeSliderColor = function()
	{
		var self = this;
		self.$form.find("#border-size-slider .slider-selection").css("background", self.selectedBorderColor);
	};

	/**
	 * The setSymbolThumbnail function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.setSymbolThumbnail = function()
	{
		var self = this, $container = $("#symbol-thumbnail-container .point");
		if ($container[0] === undefined)
		{
			return;
		}
		if ($container[0].children.length > 0)
		{
			$container.empty();
		}
		var borderColor, borderSize, symbolString, enableBorder = self.obBorderIsShow();
		if (enableBorder)
		{
			borderColor = self.selectedBorderColor;
			borderSize = self.selectedBorderSize;
		}
		symbolString = self.selectedSymbol === "default" ? self.defaultSymbol : TF.Helper.AdjustValueSymbolHelper.getSVGSymbolString(self.selectedSymbol, self.selectedColor, self.selectedSize, borderColor, borderSize);
		$container.append(symbolString);
	};

	/**
	 * Whether show BorderDetial function.
	 * @param {bool} borderEnable border will be show or not.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.showBorderDetial = function(borderEnable)
	{
		var self = this;
		self.setSymbolThumbnail();
		self.borderIsShow = borderEnable;
	};

	/**
	 * Whether show other fields except symbol dropdown.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.showOtherFields = function()
	{
		// -1 indicates None, when select None or default symbol, disabled color and border feature
		var self = this, showOtherFields = (["-1", "default"].indexOf(self.selectedSymbol) < 0) ? true : false;
		if (showOtherFields)
		{
			self.obOtherFields(true);
		}
		else
		{
			self.obOtherFields(false);
			self.obBorderIsShow(false);
		}
	};

	/**
	 * The dispose function.
	 * @returns {void}
	 */
	AdjustValueDisplayModalViewModel.prototype.dispose = function()
	{
		var self = this;
		self.$form.find("#symbol-color-selector [name=color]").data("kendoColorPicker").destroy();
		self.$form.find("#border-color-selector [name=color]").data("kendoColorPicker").destroy();
		self.$form.find(".symbol-item, .none-item").off(".selectsymbol");
		self.$form.find("#symbol-size-slider").off(".closeColorPicker");
		self.$form.find("#border-size-slider").off(".closeColorPicker");
		$("body").off("mousedown.adjustModal");
	};
})();