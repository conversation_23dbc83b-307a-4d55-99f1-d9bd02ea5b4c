(function()
{
	createNamespace('TF.Control').StateReportSettingViewModel = StateReportSettingViewModel;

	function StateReportSettingViewModel()
	{
		var self = this;
		self.obResidentCountyCode = ko.observable()
		self.obResidentDistrictCode = ko.observable();
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		self.grid = null;
		self.datasource = [];
		self.obIsProcessing = ko.observable(false);
		self.lastProcessStatus = null;
		self.lastProgressValue = 0;
		self.lastMaxProgressValue = 0;
		self.lastProgressReportedTime = new Date().getTime();
		PubSub.subscribe("DRTRSProcessStatusHub", self._processStatusUpdated.bind(self));
	}

	StateReportSettingViewModel.prototype.constructor = StateReportSettingViewModel;

	StateReportSettingViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;
		self._$form = $(el);
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "databases?authedOnly=true&@sort=Name"))
			.then(function(apiResponse)
			{
				var dataSources = apiResponse.Items;
				dataSources.forEach(d =>
				{
					self.datasource.push(
						{
							DBID: d.DBID,
							IncludeDb: false,
							RouteOnly: false,
							AMPMBUS: false,
							District: "",
							Name: d.Name
						}
					)
				});

				var datasouce = new kendo.data.DataSource(
					{
						data: _.sortBy(self.datasource, _item => _item.Name.toLowerCase()),
						scheme: {
							model: {
								id: "DBID",
								fields: {
									DBID: { editable: false },
									IncludeDb: { editable: false, },
									RouteOnly: { editable: false, },
									AMPMBUS: { editable: false, },
									District: { type: "string", editable: true },
									Name: { editable: false, },
								}
							}
						}
					}
				)
				var kendoOption = {
					dataSource: datasouce,
					height: 300,
					edit: function(e)
					{
						var editcell = e.container.data('kendoEditable')
						var field = editcell.options.fields.field;
						if (field == "Name")
						{
							e.sender.closeCell(true);
							return
						}
						row = editcell.element.closest('tr'),
							dataItem = self.grid.dataItem(row),
							isCheckBox = editcell.element.find("[type='checkbox']").length;
						if (isCheckBox)
						{
							if (field != "IncludeDb")
							{
								dataItem[field] = !dataItem[field] && dataItem["IncludeDb"];
							}
							else
							{
								dataItem[field] = !dataItem[field];
							}
							if (field == "IncludeDb" && !dataItem[field])
							{
								dataItem["RouteOnly"] = false;
								dataItem["AMPMBUS"] = false;
							}
							self.datasource = self.datasource.map(d =>
							{
								if (d['DBID'] == dataItem['DBID'])
									return dataItem.toJSON();
								else return d;
							})
							self.grid.refresh();
						}
						if (field != "District" || !e.model["IncludeDb"])
						{
							e.sender.closeCell(true);
							return;
						}

					},
					save: function(e)
					{
						_.extend(e.model, e.values);
						self.datasource = self.datasource.map(d =>
						{
							if (d['DBID'] == e.model['DBID'])
								return e.model.toJSON();
							else return d;
						})
						self.grid.refresh();
					},
					dataBound: function(e)
					{
						e.sender.tbody.find('[type=checkbox]').each(function(index, checkBox)
						{
							checkBox.onclick = evt =>
							{
								var col = $(this).closest('td');
								self.grid.editCell(col);
							}
						});
					},
					editable: {
						mode: "incell"
					},
					columns: [{
						field: "IncludeDb",
						title: "Include DB",
						template: `<input type='checkbox' #= IncludeDb ? \'checked="checked"\' : "" #  ></input>`
					},
					{
						field: "RouteOnly",
						title: "Route Only",
						template: `<input type='checkbox' #= IncludeDb? RouteOnly ? \'checked="checked"\' : "": "disabled"  # ></input>`
					},
					{
						field: "AMPMBUS",
						title: "AM/PM BUS",
						template: `<input type='checkbox' #= IncludeDb? AMPMBUS ? \'checked="checked"\' : "": "disabled"  # ></input>`
					}, {
						field: "District",
						title: "District",
					}, {
						title: "Database Name",
						field: "Name",
						width: "200px",
						attributes: (data) =>
						{
							return { title: data.Name };
						}
					}
					]
				}
				self.grid = $(el).find('.DRTRSgrid').kendoGrid(kendoOption).data("kendoGrid")

				setTimeout(function()
				{
					self._updateValidator();
					self.pageLevelViewModel.load(this._$form.data("bootstrapValidator"));
					self._$form.find('[name="ResidentCountyCode"]').focus();
				}.bind(self), 0);
			});
	}

	StateReportSettingViewModel.prototype._updateValidator = function()
	{
		var self = this,
			validatorFields = {};
		validatorFields["ResidentCountyCode"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				stringLength: {
					min: 2,
					max: 2,
					message: 'The Resident County Code must be 2 characters long.'
				},
				digits: {
					message: "The Resident County Code can only consist of digit."
				}
			}
		}
		validatorFields["ResidentDistrictCode"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				stringLength: {
					min: 4,
					max: 4,
					message: 'The Resident District Code must be 4 characters long.'
				},
				digits: {
					message: "The Resident District Code can only consist of digit."
				}
			}
		}
		return self._$form.bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('error.validator.bv', function(e, data)
		{
			data.element
				.data('bv.messages')
				.find('.help-block[data-bv-for="' + data.field + '"]').hide()
				.filter('[data-bv-validator="' + data.validator + '"]').show();
		});
	}

	StateReportSettingViewModel.prototype._processStatusUpdated = function(e, status)
	{
		let self = this,
			statusMessage = status.StatusMessage, latestProgressValue = status.CurrentProgressValue,
			updateSimpleStatus = statusMessage !== self.lastProcessStatus && (latestProgressValue === undefined || latestProgressValue === null);

		if (updateSimpleStatus)
		{
			// If status message changed or if no progress-value supplied, we do simple status reporting
			// we only display message (with no progress-value on loadingIndicator) and reset local progress-value 
			self.lastProcessStatus = statusMessage;
			self.lastProgressValue = 0;
			self.lastMaxProgressValue = 100;
			tf.loadingIndicator.changeProgressbar(0, self.composeImportProgressMessage(status, null, null), true);
		}
		else
		{
			self.updateProgressSmoothly(status);
		}
	};

	StateReportSettingViewModel.prototype.composeImportProgressMessage = function(status, curProgressValue, maxProgressValue)
	{
		let curDataSource = status.DataSourceName, statusMessage = status.StatusMessage,
			progressMessage;

		if (curProgressValue === undefined || curProgressValue === null)
		{
			return statusMessage;
		}

		switch (statusMessage)
		{
			case "PROCESS_STUDENTS_STATUS":
				progressMessage = `[${curDataSource}] ${curProgressValue} / ${maxProgressValue} students.`;
				break;
			case "PROCESS_ROUTES_STATUS":
				progressMessage = `[${curDataSource}] (${curProgressValue} / ${maxProgressValue} routes.`
				break;
			default:
				progressMessage = statusMessage;
				break;
		}

		return progressMessage;
	};

	/**
	 * We will send progress value in certain interval (not every unit increment). This method will help increasing the progress-value at the unit of 1
	 * So that the client won't see obvious progress-value gaps between updates
	 * @param {For} status 
	 */
	StateReportSettingViewModel.prototype.updateProgressSmoothly = function(status)
	{
		var self = this,
			millionSecondsBetweenServerProgressReport = new Date().getTime() - self.lastProgressReportedTime, // determine how quick we should incrementing progress-value locally
			latestProgressValue = status.CurrentProgressValue, maxProgressValue = status.MaxProgressValue,
			millionSecondsBetweenClientProgressReport = self.lastProgressValue >= latestProgressValue ? 0 : millionSecondsBetweenServerProgressReport / (latestProgressValue - self.lastProgressValue) + 1;

		self.lastProgressReportedTime = new Date().getTime(); // update the timestamp of last updated time of local progress-value
		window.clearInterval(self.importProgressUpdateIntervalHandle); // clear previous local progress anitmation interval

		if (document.hidden !== false)
		{
			// if browser page tab is inactive or document.hidden is not supported, we simply update progress with the server-api pushed value immediately without smoothing animation
			self.lastProgressValue = latestProgressValue;
			let latestPercentage = Math.floor((self.lastProgressValue / maxProgressValue) * 100),
				latestProgressMessage = self.composeImportProgressMessage(status, self.lastProgressValue, maxProgressValue);
			tf.loadingIndicator.changeProgressbar(latestPercentage, latestProgressMessage, true);
			return;
		}

		self.importProgressUpdateIntervalHandle = window.setInterval(function()
		{
			self.lastProgressValue += 1;
			if (!self.obIsProcessing() || self.lastProgressValue > latestProgressValue)
			{
				window.clearInterval(self.importProgressUpdateIntervalHandle);
				self.lastProgressValue = latestProgressValue;

				if (!self.obIsProcessing())
				{
					tf.loadingIndicator.tryHide(); // processing has finished or terminated, should hide loadingIndicator
				}
			}

			var calculatedPercentage = Math.floor((self.lastProgressValue / maxProgressValue) * 100),
				composedProgressMessage = self.composeImportProgressMessage(status, self.lastProgressValue, maxProgressValue);
			tf.loadingIndicator.changeProgressbar(calculatedPercentage, composedProgressMessage, true);

		}, millionSecondsBetweenClientProgressReport);
	};


	StateReportSettingViewModel.prototype.apply = function()
	{
		var self = this;
		return self.pageLevelViewModel.saveValidate().then(valid =>
		{
			if (valid)
			{
				var settings = {
					Items: self.datasource.filter(d => d.IncludeDb).map(item =>
					{
						return {
							RouteOnlyChecked: item.RouteOnly,
							AMPMBusChecked: item.AMPMBUS,
							District: item.District,
							DatabaseId: item.DBID
						}
					}),
					ResidentCountyCode: self.obResidentCountyCode(),
					ResidentDistrictCode: self.obResidentDistrictCode()
				}
				if (!settings.Items || settings.Items.length == 0)
				{
					tf.promiseBootbox.alert("Please select at least one datasource.")
					return Promise.resolve(false);
				}
				tf.loadingIndicator.resetProgressbar();
				tf.loadingIndicator.setSubtitle("Processing Started");
				tf.loadingIndicator.show(true);
				self.obIsProcessing(true);
				return TF.SignalRHelper.ensureConnection("DRTRSProcessStatusHub").then(() =>
				{
					return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "drtrsprocesses"), {
						data: settings
					})
				}).then(result =>
				{
					return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'drtrsprocesses'), {
						paramData: { contextId: result.Items[0].Id },
						headers: {
							'ConnectionId': TF.getConnectionId()
						}
					}).then(res =>
					{
						self.obIsProcessing(false);
						return Promise.resolve([result.Items[0], res.Items[0]])
					}).catch(err => 
					{
						self.obIsProcessing(false);
						return Promise.reject(err);
					});
				})

			}
		})

	}
	StateReportSettingViewModel.prototype.dispose = function()
	{
		var self = this;
		if (self.grid)
		{
			self.grid.destroy();
		}
		PubSub.unsubscribe("DRTRSProcessStatusHub");
	}
})()