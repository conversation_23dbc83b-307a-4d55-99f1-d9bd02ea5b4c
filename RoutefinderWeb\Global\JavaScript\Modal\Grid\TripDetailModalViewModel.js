(function()
{
	createNamespace("TF.Modal.Grid").TripDetailModalViewModel = TripDetailModalViewModel;

	function TripDetailModalViewModel(student, tripId, tripStopDetailInfo)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.sizeCss = "modal-dialog-md";
		self.title("Schedule Details for " + student.Name + " Weekly Schedule");
		self.contentTemplate("Modal/TripDetail");
		self.buttonTemplate("modal/positive");
		self.obPositiveButtonLabel("OK");
		self.model = new TF.Control.TripDetailViewModel(student, tripId, tripStopDetailInfo);
		self.data(self.model);
	}

	TripDetailModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	TripDetailModalViewModel.prototype.constructor = TripDetailModalViewModel;

	TripDetailModalViewModel.prototype.positiveClose = function()
	{
		var self = this;
		self.hide();
		self.resolve(true);
	};

	TripDetailModalViewModel.prototype.negativeClose = function()
	{
		var self = this;
		self.hide();
		self.resolve(false);
	};
})();