(function()
{
	ko.bindingHandlers.customSelect = {
		init: function(element, valueAccessor)
		{
			var button = $(element).children(".input-group-btn").children("button");
			let label = $(element).children('.input-group-label');
			[button,label].forEach(el => {
				el.on("click", function(event)
				{
					event.stopPropagation();
					event.preventDefault();
					showSelect($(element), valueAccessor().source(), valueAccessor().selectCallback);
				});
			});
		}
	};

	function refreshSelected($menu, source)
	{

		let $lis = $menu.children('li');
		$lis.removeClass('active selected');
		let selectedTxt = source.find(x => x.selected);
		if (selectedTxt)
		{
			$lis.each((idx, val) =>
			{
				let $val = $(val);
				if ($val.text() == selectedTxt.text)
				{
					$val.addClass('active selected');
				}
			});
		}
	}

	function attachMouseDownEvent($element, $existMenu)
	{
		$("body").on("mousedown.customSelect", function(evt)
		{
			if (!$element[0].contains(evt.target))
			{
				$existMenu.hide();
				releaseMouseDownEvent();
			}
		});

		$(window).on("scroll.customSelect", function() { $existMenu.hide(); releaseMouseDownEvent(); }).on("resize.customSelect", function() { $existMenu.hide(); releaseMouseDownEvent(); });
	}

	function releaseMouseDownEvent()
	{
		$("body").off("mousedown.customSelect");
		$(window).off("scroll.customSelect").off("resize.customSelect");
	}

	function showSelect($element, source, selectCallback)
	{
		let $menu = null;
		let $existMenu = $element.find('.typeahead.dropdown-menu');
		var pos = $.extend({}, $element.offset(), {
			height: $element[0].offsetHeight,
			width: $element[0].offsetWidth
		});
		if ($existMenu.length == 0)
		{
			$menu = createMenu(source, selectCallback);
			attachMouseDownEvent($element, $menu);
			$menu.addClass("typeahead dropdown-menu");			
			$menu.appendTo($element).css({
				top: pos.height,
				left: 0,
				width: $element[0].offsetWidth
			}).show();
		}
		else
		{
			$existMenu.css({
				top: pos.height,
				left: 0,
				width: $element[0].offsetWidth
			});
			if ($existMenu.is(':hidden'))
			{
				attachMouseDownEvent($element, $existMenu);
				refreshSelected($existMenu, source);
			}
			$existMenu.toggle();
		}
	}

	function createMenu(source, selectCallback)
	{
		var ul = $('<ul role="listbox" class="dropdown-menu"></ul>');
		var activeChildren = null;
		source.forEach(function(data)
		{
			var li = $("<li></li>");
			if (data.disabled)
			{
				li.addClass("disabled-element");
			}

			var text = $("<a href='#' role='option'></a>");
			if (data.text == "-")
			{
				text = $("<div></div>");
				li.addClass("divider");
			} else
			{
				text.text(data.text);
				if (data.selected)
				{
					li.addClass("active selected");
				}
				if (!data.children)
				{
					text.on("click", function(evt)
					{
						$(evt.target).closest(".typeahead").hide();
						selectCallback(data);
					});
				}
			}
			li.append(text);

			if (data.children && data.children.length > 0)
			{
				li.append("<span class='k-icon k-i-arrow-e'></span>");
				var children = createMenu(data.children, selectCallback);
				li.append(children);
				li.on("mouseover", function()
				{
					activeChildren?.hide();
					children.css({
						top: li.offset().top,
						position: 'fixed',
						left: ul[0].offsetWidth + ul.offset().left
					});
					children.show();
					activeChildren = children;
				});
			} else
			{
				li.on("mouseover", function()
				{
					activeChildren?.hide();
				});
			}
			ul.append(li);
		});
		return ul;
	}

	ko.virtualElements.allowedBindings.customSelect = true;
})();
