@media print {
	body>.printable {
		display: block;
		.edit-title-group .iconbutton, .edit-title-group .divider,.edit-bottombar,.doc-selector {
			display: none;
		}
	}

	#loadingindicator{
		display:none !important;
	}

	.dataentry-container {
		display: initial;
	}

	.docs .doc {
		position: static;
	}

	//
	// .row
	// {
	// 	page-break-before: auto;
	// 	page-break-after: auto;
	// }

	.doc[style*="visibility: hidden"],
	.doc[style*="visibility:hidden"] {
		display: none !important;
	}

	.docs,
	.doc:not([style*='visibility: hidden'])>.doc.document-grid .bottompart {
		height: auto !important;
	}

	.doc-selector {
		position: static !important;
	}

	.doc:not([style*='visibility: hidden'])>.doc.document-grid .k-grid {
		/*overflow: visible;*/
		height: auto !important;
	}

	.doc:not([style*='visibility: hidden']) .doc.document-grid .kendo-grid-container .k-grid-header {
		padding: 0 !important;
	}

	/*.doc:not([style*='visibility: hidden']) > .doc.document-grid .kendo-grid-container .k-grid-header-wrap,*/
	.doc:not([style*='visibility: hidden'])>.doc.document-grid .kendo-grid-container .k-grid-content,
	.doc:not([style*='visibility: hidden'])>.doc.document-grid .kendo-grid-container .k-grid-content-locked {
		height: auto !important;
	}

	/*.doc:not([style*='visibility: hidden']) > .doc.document-grid .kendo-summarygrid-container{ //fix summarybar
	margin-top: 0 !important;
}*/
	#main {
		position: relative !important;
	}

	.doc.document-grid .kendo-grid-container .k-grid-header {
		position: absolute !important;
	}

	.doc.document-grid .kendo-grid-container .k-grid-content,
	.doc.document-grid .kendo-grid-container .k-grid-content-locked {
		margin-top: 71px;
	}

	.doc.document-grid .bottompart {
		position: static !important;
		margin-top: 100px;
		width: 100%;
	}

	.view-form-grid .doc.document-grid .bottompart {
		position: static !important;
		margin-top: 47px;
		width: 100%;
	}

	.toppart.gridTop {
		position: absolute;
		width: 100% !important;
	}

	.scroll-in-ie-locked-area {
		z-index: 2;
	}

	.scroll-in-ie-locked-content-area {
		z-index: 1;
		background-color: #f6f6f6;
	}
		.custom-detail-view .form {
			min-height: auto !important;
		}
}
