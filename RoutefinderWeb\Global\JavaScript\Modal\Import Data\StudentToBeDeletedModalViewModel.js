(function()
{
	createNamespace("TF.Modal").StudentToBeDeletedModalViewModel = StudentToBeDeletedModalViewModel;

	function StudentToBeDeletedModalViewModel(allItems, selectedItems)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-lg";
		self.title("Students to be deleted");
		self.contentTemplate("modal/kendolistmoverwithaddcontrol");
		self.buttonTemplate("modal/positivenegative");
		self.obPositiveButtonLabel("OK");
		self.obNegativeButtonLabel("Cancel");
		var options = {
			type: "student",
			showButtons: false,
			showRemoveColumnButton: true,
			_DataFiledName: "Name",
			_notUseStickyColumns: true,
			availableTitle: "Students to Keep",
			selectedTitle: "Students to Delete",
			_GridConifg: {
				gridSchema: {
				},
				gridColumns: [
					{
						field: "Name",
						FieldName: "Student Name"
					}
				],
				height: 400,
				sortable: {
					mode: "single",
					allowUnsort: true
				},
				selectable: "multiple"
			},
			_sortItems: function(a, b)
			{
				var x, y;
				x = a['Name'] ? a['Name'].toLowerCase() : '';
				y = b['Name'] ? b['Name'].toLowerCase() : '';
				return (x == y ? 0 : (x > y ? 1 : -1));
			},
			_convertImportData: function(items)
			{
				return items;
			},
			_getUnSelectedItems: function(allItems, selectedItems)
			{
				var unSelectedItems = allItems.filter(function(item)
				{
					var matchResult = [];
					matchResult = selectedItems.filter(function(selectedItem)
					{
						return selectedItem.Name === item.Name;
					});
					return matchResult.length === 0;
				});
				return unSelectedItems;
			},
			_fillDisplayName: function(items)
			{
				return items;
			},
			_sortKendoGrid: function(kendoGrid, sortItemFun)
			{
				kendoGrid.dataSource.sort({ field: "Name", dir: "asc" });
			},
			_convertOutputData: function(items)
			{
				return items;
			}
		};;

		this.kendolistMoverControlViewModel = new TF.Control.KendoListMoverWithAddControlViewModel(allItems, selectedItems, options, this.shortCutKeyHashMapKeyName);
		this.data(this.kendolistMoverControlViewModel);
	}

	StudentToBeDeletedModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	StudentToBeDeletedModalViewModel.prototype.constructor = StudentToBeDeletedModalViewModel;

	/**
	 * The event of OK button click.
	 * @return {void}
	 */
	StudentToBeDeletedModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.kendolistMoverControlViewModel.apply().then(function(result)
		{
			if (result)
			{
				self.positiveClose(result);
			}
		});
	};
})();
