@systemColor: #2686b8;

.tf-rating {
	.tf-rating-label-container {
		display: flex;
		justify-content: space-between;

		.tf-rating-label {
			max-width: 50%;
			word-wrap: break-word;
		}
	}

	.tf-rating-container {
		display: flex;
		flex-wrap: nowrap;
		height: 40px;
		line-height: 34px;
		background-color: #f7f7f7;

		.tf-rating-item {
			flex-direction: row;
			flex-grow: 1;
			text-align: center;
			background-color: #f7f7f7;
			border: solid 1px #e7e7e7;
			border-collapse: collapse;

			&:hover {
				background-color: #f1f1f1;
			}

			&.tf-rating-item-selected {
				background-color: #333333;
				border-color: #333333;
				color: white;

				label.tf-rating-label {
					color: white !important;
				}
			}

			.tf-rating-value {
				display: none;
			}

			.tf-rating-label {
				vertical-align: middle;
				width: 100%;
				cursor: pointer;
			}
		}
	}
}

.question-content.readonly {
	.tf-rating .tf-rating-container {
		.tf-rating-item:not(.tf-rating-item-selected):hover {
			background-color: #f7f7f7;
		}
	}
}

.tf-ratingmatrix {
	.tf-rating-container {
		margin-bottom: 15px;
	}

	.tf-matrix-label {
		text-align: left;
		background-color: #ffffff;

		p {
			margin-right: 15px;
			margin-top: 5px;
		}
	}

	.tf-matrix-label.two-lines {
		white-space: nowrap;
		line-height: 16px;

		p {
			margin-bottom: 2px;
			margin-right: 15px;
			margin-top: 2px;
		}
	}

	.tf-matrix-label.tf-matrix-label-mobile {
		white-space: nowrap;
	}

	.tf-matrix-label.three-mobile-lines {
		line-height: 12px;

		p {
			margin-bottom: 1px;
			margin-right: 10px;
			margin-top: 1px;
		}
	}

	.tf-matrix-label.two-mobile-lines {
		line-height: 18px;

		p {
			margin-bottom: 2px;
			margin-right: 10px;
			margin-top: 2px;
		}
	}
}