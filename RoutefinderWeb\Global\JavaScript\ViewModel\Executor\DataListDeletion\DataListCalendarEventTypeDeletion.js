﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListCalendarEventTypeDeletion = DataListCalendarEventTypeDeletion;

	function DataListCalendarEventTypeDeletion()
	{
		namespace.DataListBaseDeletion.apply(this, arguments);
		this.type = 'calendareventtypes';
		this.deleteType = 'Type';
		this.deleteRecordName = 'Calendar Event Type';
	}

	DataListCalendarEventTypeDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListCalendarEventTypeDeletion.prototype.constructor = DataListCalendarEventTypeDeletion;

	DataListCalendarEventTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var self = this;
		var associatedDatas = [];
		var isSystemDefined = false;

		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), this.type),
			{
				paramData: {
					ID: ids[0],
					"@relationships": 'CalendarEventType'
				}
			}).then(function(response)
			{
				isSystemDefined = response.Items[0].System;

				if (isSystemDefined)
				{
					const rejectMessage = "<pre  class='titleNotify'>" + "This " + self.deleteRecordName.toLowerCase() + " is system defined.  It cannot be deleted. </pre>";
					const rejectTitle = self.deleteRecordName + " Cannot be Deleted";
					return tf.promiseBootbox.alert(rejectMessage, rejectTitle)
						.then(() =>
						{
							self.deleteIds = [];
							return Promise.resolve(false);
						});
				}

				response.Items = response.Items[0].CalendarEvents;
				associatedDatas.push({
					type: self.type,
					items: response.Items
				});
				return associatedDatas;
			});
	}

	DataListCalendarEventTypeDeletion.prototype.deleteSelectedItems = function()
	{
		if (this.deleteIds.length === 0)
		{
			return;
		}
		var ids = this.deleteIds,
			singleOrMultiple = ids.length > 1 ? 'multiple' : 'single',
			successMessage = i18n.t('deletion.delete_success_' + singleOrMultiple),
			errorMessage = i18n.t('deletion.delete_failed_' + singleOrMultiple),
			requestData = ids;
		if ($.isFunction(this.getDeletionData))
		{
			requestData = this.getDeletionData();
		}
		return tf.promiseAjax.delete(pathCombine(tf.api.apiPrefixWithoutDatabase(), tf.dataTypeHelper.getEndpoint(this.type)),
			{
				paramData: { "@filter": "in(ID," + requestData.join(",") + ")" }
			})
			.then(function()
			{
				this.publishData(ids);
				return ids;
			}.bind(this))
			.catch(function()
			{
			}.bind(this));
	};

	DataListCalendarEventTypeDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

})();