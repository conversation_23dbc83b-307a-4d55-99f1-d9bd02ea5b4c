﻿(function()
{
	createNamespace('TF.Modal').ResetLoadTimesModalViewModel = ResetLoadTimesModalViewModel;

	function ResetLoadTimesModalViewModel(affected)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/ResetLoadTimesControl');
		this.buttonTemplate('modal/positivenegative');
		this.viewModel = new TF.Control.ResetLoadTimesViewModel(affected);
		this.data(this.viewModel);
		this.sizeCss = "modal-dialog-lg";
		this.title("Reset Manually Adjusted " + tf.applicationTerm.getApplicationTermPluralByName("Load Time"));
		this.obPositiveButtonLabel("OK");
	}

	ResetLoadTimesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ResetLoadTimesModalViewModel.prototype.constructor = ResetLoadTimesModalViewModel;

	ResetLoadTimesModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(data)
		{
			if (data)
			{
				this.positiveClose(data);
				return;
			}
			this.hide();
			this.resolve(false);
		}.bind(this));
	};

	ResetLoadTimesModalViewModel.prototype.negativeClose = function(returnData)
	{
		TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
	};

	ResetLoadTimesModalViewModel.prototype.dispose = function()
	{
		this.viewModel.dispose();
	};

})();
