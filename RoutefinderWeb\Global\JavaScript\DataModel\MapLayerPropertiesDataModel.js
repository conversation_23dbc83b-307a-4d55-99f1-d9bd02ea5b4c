﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.MapLayerPropertiesDataModel = function(mapLayerPropertiesEntity)
	{
		namespace.BaseDataModel.call(this, mapLayerPropertiesEntity);
	};

	namespace.MapLayerPropertiesDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.MapLayerPropertiesDataModel.prototype.constructor = namespace.MapLayerPropertiesDataModel;

	namespace.MapLayerPropertiesDataModel.prototype.mapping = [
		{ from: 'DefinitionQuery', default: '' },
		{ from: 'LabelFeatures', default: false }
	];
})();