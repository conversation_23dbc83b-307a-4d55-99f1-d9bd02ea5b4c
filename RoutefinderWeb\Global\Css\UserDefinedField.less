@systemColor: #D0503C;

.insert-field-icon() {
	background-image: url(../../global/Img/dashboard/textwidget-add-button.svg);
	width: 18px;
	background-size: contain;
	height: 18px;

	svg {
		display: none;
	}
}

.tabstrip-dashboards,
.tabstrip-customized-dashboards {
	padding: 15px;
	font-family: "SourceSansPro-SemiBold";
	height: 100%;
	overflow: auto;

	.page-title {
		font-size: 27px;
		color: @systemColor;
	}
}

.tabstrip-userdefinedfields,
.tabstrip-fieldtripconfigs,
.tabstrip-requiredfields {
	padding: 15px;
	font-family: "SourceSansPro-SemiBold";
	height: 100%;
	overflow: auto;

	.page-title {
		font-size: 24px;
		color: @systemColor;
		font-family: Arial;
		font-weight: bold;
	}
}

.switch-grid-menu.tf-contextmenu-white {
	width: 195px;

	ul {
		.menu-item {
			line-height: 28px;
			height: 35px;
			padding: 6px 8px;

			&:hover {
				background-color: lightgray !important;
			}
		}
	}
}

.switch-grid-menu.tf-contextmenu-white.data-list-switch-grid-menu {
	width: 300px;
}

.system-defined-field {

	.k-grid-copyandnew,
	.k-grid-edit,
	.k-grid-delete,
	.k-grid-export {
		display: none;
	}
}

.doc-sigunature-defined-field {
	.k-grid-copyandnew {
		visibility: hidden;
	}
}

.general-field {
	.k-grid-view {
		display: none;
	}
}

.grid-wrapper,
.rule-grid-wrapper {
	padding: 15px;

	.grid-header {
		display: flex;
		height: 30px;
		padding-left: 10px;
		padding-right: 10px;
		margin-bottom: 10px;

		.header-title {
			flex: 1;
			font-weight: bold;
			font-size: 16px;
			position: relative;

			.selected-label {
				cursor: pointer;
			}

			.icon.bottom-caret {
				margin: 0px 10px;
				position: absolute;
				top: 10px;
				cursor: pointer;

				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					border-left: 10px solid transparent;
					border-top: 10px solid #777777;
					border-right: 10px solid transparent;
				}

				&::after {
					content: '';
					position: absolute;
					left: 4px;
					top: 0;
					border-left: 6px solid transparent;
					border-top: 6px solid #fff;
					border-right: 6px solid transparent;
				}
			}
		}

		.header-add-button {
			width: 120px;
			line-height: 28px;
			color: #333333;
			border: 1px solid #999;
			text-align: center;
			cursor: pointer;
			border-radius: 5px;
			font-size: 14px;
			padding-left: 15px;
			padding-right: 15px;
			margin-left: 3px;
		}
	}

	.treelist-container {
		margin-top: 20px;
	}

	.kendo-grid {
		table {
			width: 100%;
		}

		.k-grid-footer {
			background-color: transparent;
			border: none;
			text-align: right;
			height: 30px;
			line-height: 30px;
			color: #666;
			display: block !important;
		}
	}
}

div.k-window {
	z-index: 26000 !important;
}

.k-overflow-tools[style*="visibility: visible;"] {
	position: initial;
}

.k-editor-overflow-popup li a.k-overflow-button {
	text-align: left;
}

.group-udf-container {
	height: calc(100% - 111px);
	width: 100%;
	overflow: auto;
}

.group-udf-panel-title {
	height: 56px;
	line-height: 56px !important;
	border-bottom: 2px solid #F2F2F2 !important;
	margin-bottom: 15px;
}

.Edit-UDF-Modal {

	.k-checkbox-row {
		margin-left: 1.5em;
		display: flex;

		.k-checkbox-block {
			width: 50%;

			.k-checkbox-label {
				width: auto;
				line-height: 20px;
				margin-left: 4px;
			}
		}
	}


	.k-checkbox-block-one-question {
		margin-left: 8px;
	}

	.editor-wrapper {
		width: 100%;
		margin-top: 0px;
		height: auto;

		.k-combobox {
			width: 120px !important;
		}

		.k-editor-toolbar.k-toolbar-resizable {
			display: flex;
			flex-wrap: wrap;

			span.k-icon.disabled {
				opacity: .5;
			}
		}

		.editor-options-wrap {
			margin-bottom: 0px;
		}


		.text-editor-wrapper,
		.html-editor-wrapper,
		table.k-editor.k-editor-widget {
			height: 200px;

			.k-editor {
				height: 100%;
				box-sizing: border-box;

				.k-dropdownlist .k-input-inner {
					padding-top: 0;
					padding-bottom: 0;
				}
			}
		}

		.text-editor-wrapper table.k-editor.k-editor-widget {
			border-top-style: solid;

			.k-editor-toolbar {
				li {

					.k-dropdown,
					.k-combobox,
					.k-colorpicker {
						padding: 1px 0px;
						margin: 1px 0px;
					}

					.k-dropdown .k-dropdown-wrap {
						.k-input {
							line-height: 26px;
							height: 26px;
							font-size: 13px;
						}

					}

					.k-combobox .k-dropdown-wrap {
						font-size: 13px;
					}

					.k-colorpicker .k-tool-icon {
						top: -4px;
					}
				}
			}
		}

		.k-colorpicker>.k-select {
			opacity: 1;
			display: block;
		}


		.html-editor-wrapper {
			border: none;
			display: none;

			#ThankYouMessageHtmlEditor,
			#ThankYouMessageHtmlEditor_forms,
			#MessageBodyHtmlEditor,
			#QuestionBodyHtmlEditor,
			#ThankYouMessageHtmlEditor_div {
				padding: 0;
				width: 100%;
				border: 1px solid #C5C5C5;
				height: 100%;
				outline: none;
				resize: none;
			}

			#ThankYouMessageHtmlEditor_div {
				overflow: auto;
				border: 0px;
			}
		}
	}

	.thank-you-message-editor-wrapper.editor-wrapper {

		.text-editor-wrapper,
		.html-editor-wrapper,
		table.k-editor.k-editor-widget {
			height: 510px;
		}

		span.k-icon.disabled {
			opacity: .5;
		}

		.k-combobox {
			width: 132px !important;
		}
	}

	.notinput-required-message,
	.notinput-required-messagebody {
		height: 0;
		padding: 0 !important;
		border: none;
	}

	.default-value,
	.data-source {
		&.allow-multiple {
			.dropdown {
				display: none;
			}

			.listmover {
				display: table;
			}
		}

		.listmover {
			display: none;
		}

		button.list-default-value-button,
		button.list-mover-button {
			position: absolute;
			height: 20px;
			width: 22px;
			right: 1px;
			top: 1px;
			background-repeat: no-repeat;
			background-position: center;
			background-color: #eee;
			border: none;
			border-left: 1px solid #ccc;
			background-image: url(../img/detail-screen/editor-icon-ellipsis.svg);
			background-size: 16px 16px;
			z-index: 10;

			&:hover {
				border-color: #adadad;
			}

			&:active {
				box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
			}
		}
	}

	.List-UDF-Fields,
	.case-udf-container {
		.kendo-grid {
			.k-grid-content {
				.k-grid-content-expander {
					width: 100% !important;
				}

				.k-grid-table {
					.k-table-tbody {
						.k-table-row {
							.k-table-td:not(:last-child) {
								border-width: 0 1px 0 0;
							}
						}
					}
				}
			}
		}
	}

	.grid-wrapper,
	.rule-grid-wrapper {
		padding: 0;

		.grid-header {
			padding-left: 0;
			padding-right: 0;
		}

		.kendo-grid {
			padding: 0;
			height: 266px;
			margin-top: 20px;

			table {
				box-sizing: content-box;
			}
		}

		.k-grid-footer {
			background-color: transparent;
			border: none;
			text-align: right;
			height: 30px;
			line-height: 30px;
			color: #666;
			display: block;
		}
	}

	.checkbox label {
		font-weight: bold
	}

	.checkbox label {
		&.disabled {
			cursor: default;
		}
	}

	.checkbox label .can-be-checked:hover {
		cursor: pointer;
	}

	.name,
	.default-value-wrapper {
		height: 40px;
	}

	.form-group {

		.k-dropdowntree,
		.k-dropdown {
			>.k-input {
				background-color: #fff;
				height: 20px;
				min-height: 20px;
				line-height: 20px;
				padding-top: 0;
				padding-bottom: 0;
			}

			.k-clear-value {
				.k-icon {
					margin-top: -3px;
					width: 16px;
					height: 16px;
				}
			}

			>.k-select {
				height: 20px;
				min-height: 20px;
				background-color: #eee;
				line-height: 20px;
				border-left: solid 1px #BFBFBF;

				.k-icon {
					display: inline-block;
					width: 0;
					height: 0;
					vertical-align: middle;
					border-top: 4px solid;
					border-right: 4px solid transparent;
					border-left: 4px solid transparent;
				}
			}
		}

		.text-editor-wrapper .k-dropdown {
			.k-select {
				height: 100%;
			}

			&.k-hover {
				background-color: #bdb4af;

				>span {
					background-color: transparent;
				}
			}
		}
	}

	.form-control {
		border-radius: 0;

		.information-container {
			display: flex;
			height: 70px;
			justify-content: center;
			align-items: center;
			margin: 0;
			padding: 0;

			.information {
				flex: 0 0 300px;
				margin: 0;
				padding: 0;
				font-size: 1.0em;
				text-align: center;
			}
		}
	}

	.left-panel.form-group {
		margin-bottom: 0;

		.ip-gepfence,
		.ip-geofence-display {
			display: flex;
		}

		.date-time-combo {
			display: flex;
			justify-content: space-between;

			div.date-time {
				width: 49%;
				position: relative;

				.datepickerbutton {
					height: auto;
				}

				.disable-mask {
					position: absolute;
					inset: 0;
					opacity: .1;
					background-color: gray;
					z-index: 9999;
				}
			}
		}

		.iprange-count {
			padding-left: 16px;
			width: 130px;
			margin: auto;
		}

		.geofence-count {
			padding-left: 10px;
			width: 160px;
			margin: auto;
			margin-right: -30px;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}

		.form-group {
			margin-bottom: 14px;

			.checkbox {
				margin: 0;
			}

			.add-button {
				width: 180px;
				line-height: 24px;
				color: #333333;
				border: 1px solid #999;
				text-align: center;
				cursor: pointer;
				border-radius: 5px;
				font-size: 14px;

				&.disabled {
					cursor: not-allowed;
					background-color: #eee;
				}

				&.geofence-button {
					margin-left: 10px;
				}
			}

			.icon-button-area {
				display: flex;
				align-items: flex-end;

				.c-picker {
					margin-right: 10px;
				}

				a {
					display: flex;
					align-items: center;
					padding: 0 2px;

					&.disabled {
						opacity: .4;
						cursor: default;
					}

					i {
						display: block;
						width: 20px;
						height: 20px;
						background-size: contain;
					}

					.share-icon {
						background-image: url(../img/icons/link.svg);
					}

					.qr-code-icon {
						background-image: url(../img/icons/qr-code.svg);
					}
				}
			}

			.input-group-addon.datepickerbutton .k-i-clock {
				color: black;
			}

			&.date-range {
				.checkbox {
					width: 100px;
				}

				&.start-date {
					margin-bottom: 2px;
				}
			}
		}

		.one-response-per-recipient-block {
			.one-response-message {
				color: #ff0000;
				position: absolute;
				bottom: -15px;
				font-size: 12px;
				width: 250px;
			}

			box-sizing: border-box;
		}

		.rfid-scan-config-block {
			box-sizing: border-box;

			.checkbox {
				&.submit-on-rfid-scan {
					margin-left: 17px;
				}
			}
		}
	}

	.right-panel.form-group {
		margin-bottom: 0;

		.form-group {
			margin-bottom: 14px;

			.checkbox {
				margin: 0;
			}
		}
	}

	.row.udfGroup {
		margin-bottom: 18px;

		.k-grid-norecords {
			display: none;
		}

		.k-grid-norecords-template {
			display: none;
		}

		.kendo-treelist .k-alt {
			background-color: #ECF2F9;
		}

		.kendo-treelist .drop-hint {
			width: initial;
			gap: 0px;
		}

		.kendo-treelist .drop-hint-start {
			border-left-color: #F35800;
			margin-right: -1px;
		}

		.kendo-treelist .drop-hint-end {
			border-right-color: #F35800;
			margin-left: -1px;
		}

		.kendo-treelist .drop-hint-line {
			background-color: #F35800;
			height: 2px;
			width: 80%;
		}

		.kendo-treelist {
			.section {
				background-color: #d9d9d9;
				border-bottom: 1px solid #ACACAC;

				.question-name {
					text-align: center;
					font-weight: bold;

					.k-icon {
						float: left;
						margin-top: 3px;
					}
				}
			}
		}
	}

	&.my-record {
		.left-panel.form-group {
			.form-group {
				margin-bottom: 8px;
			}
		}
	}

	.checkbox {
		display: flex;
		margin-left: 20px;

		label {
			padding-left: 0;

			input[type=checkbox] {
				position: initial;
				margin-right: 5px;
			}

			.one-quesiton {
				margin-top: 3px;
			}
		}
	}

	.List-UDF-Fields .checkbox {
		margin-left: 0px;
	}

	&>.k-tabstrip-wrapper {
		margin-top: -1em;
	}

	&>.k-tabstrip-wrapper>.tabstrip-group-udf,
	// The class .k-tabstrip-wrapper is not always present, 
	// it would be destroyed when the tab is disposed (happened when detailview opened and try to switch the selected records by clicking the grid row)
	&>.tabstrip-group-udf {
		border: 0;
		padding: 1em;
		min-width: 800px;

		&.k-widget .k-grid-header-table * {
			box-sizing: border-box;
		}

		.k-grid-content-expander {
			width: 100% !important;
		}

		.group-udf-tab {
			padding-left: 2em;

			.row {
				width: calc(~'100% + 15px'); //Make the right part nearer the scroll bar
				box-sizing: border-box;

				.left-panel {
					box-sizing: border-box;

					textarea.form-control[data-bv-field="name"] {
						padding-bottom: 0;
					}

					&.filter-panel {
						padding-right: 0;
						margin-top: -3%;
					}

					&.margin-bottom-25 {
						margin-bottom: 25px !important;
					}
				}

				.right-panel {
					box-sizing: border-box;
					padding-left: 30px;
					padding-right: 0;
				}
			}
		}

		.questions-tab {
			overflow-x: hidden;

			.row.udfGroup {
				width: 100%;
				margin-bottom: 0px;
			}
		}

		.rules-tab {
			overflow-x: hidden;

			.row.rules-group {
				width: 100%;
				margin-bottom: 0px;
			}

			.invalid-word {
				font-weight: bold;
				color: rgb(255, 80, 80);
			}

			.invalid-checkbox {
				accent-color: rgb(255, 0, 0);
				opacity: 0.5;
				cursor: not-allowed;
			}
		}

		.thank-you-tab {
			>div.row:only-child {
				height: 100%;
			}

			.k-editor {
				.k-editor-toolbar {
					[title="Insert field"] .k-icon {
						.insert-field-icon();
					}

					.k-tool-group {
						.k-i-createfield {
							background-image: url(../../global/Img/dashboard/textwidget-add-button.svg);
							background-size: contain;
							width: 18px;
							height: 18px;
						}
					}

					&.k-toolbar {
						.k-combobox {
							.k-input-inner {
								height: 18px;
							}
						}
					}
				}
			}

			div.custom-message {
				box-sizing: border-box;
				height: 100%;

				.thank-you-message-editor-wrapper {
					height: 89% !important;
					position: relative;
				}

				.text-editor-overlay {
					position: absolute;
					height: 90%;
					width: 100%;
					background-color: grey;
					opacity: 10%;
					z-index: 999;
				}

				.allow-user-row,
				.custom-message-setting {
					width: 100%;
					display: flex;
					margin-top: 15px;

					.checkbox-row {
						display: flex;
						margin-left: 30px;

						.checkbox {
							margin-top: 0 !important;

							.allow-user-check {
								margin-top: 3px !important;
							}

							&:first-child {
								margin-left: 0 !important;
							}

						}
					}
				}

				.custom-message-setting {
					.checkbox-row {
						margin-left: 20px;
					}

					.checkbox {
						margin-right: 15px;
					}

					.k-numerictextbox.display-message-length {
						margin-top: -2px;
					}

					.display-message-length {
						width: 100px;
						height: 22px;
						padding-left: 0;
						margin-left: 10px;

						input {
							width: 60px;
							padding: 0;
							margin-left: 0px;
						}
					}
				}
			}
		}
	}

	&>.tabstrip-group-udf {
		padding-top: 0;
	}

	.group-udf-bottom {
		margin: 16px 20px;
	}

	.systemfield-info-message {
		color: #ff0000;
		font-size: 12px;
		font-weight: normal;
	}

	.date-info-message {
		color: #ff0000;
		font-size: 12px;
		font-weight: normal;
	}
}

.forms .Edit-UDF-Modal .tabstrip-group-udf {
	min-width: 520px;

	.form-name,
	.form-desc,
	.form-filter {
		width: calc(100% - 10px);
	}
}

.forms .thank-you-tab .editor-row {
	width: auto !important;
}

.rule-grid-container {
	.k-grid-header th.k-header:first-child {
		border-left-width: 0px;
	}
}

.form-rule-modal {
	min-width: 750px;
}

.list-question-value {
	.question-title {
		display: none;
	}

	.k-multiselect {
		max-height: 300px !important;
		overflow: auto;
	}

	.list-question {
		margin-left: 50px;
		max-height: 300px !important;

		.text-question.question {
			width: 200px;
		}
	}

	.list-from-data-question {
		margin-top: 15px;
		margin-left: 15px;
		margin-right: 15px;
	}

	input[type=checkbox] {
		left: 45px !important;
	}

	label[for],
	textarea {
		font-size: 14px !important;
	}
}

.add-form-rule-Modal {
	.rule-name-container {
		display: flex;
		flex-wrap: wrap;

		.rule-name {
			width: 88%;
			height: 32px;
		}

		.rule-enable {
			position: initial !important;
			margin-left: calc(~"12% - 80px") !important;
			margin-top: 5px !important;
			border-radius: 2px;

			input {
				position: initial !important;
				margin-right: 5px;
			}
		}
	}

	.if-block,
	.then-block {
		border: solid 1px rgb(204, 204, 204);
		border-radius: 2px;
		background-color: #f2f2f2;
		padding: 10px;
		margin-bottom: 20px;
		max-height: 450px;
		overflow-y: auto;
		overflow-x: hidden;

		small.help-block {
			font-size: 83% !important;
		}

		.condition-list-value {
			background-color: #ffffff;
		}

		.condition-list-value[disabled] {
			background-color: inherit !important;
			cursor: not-allowed !important;
		}

		.condition-list-value[readonly] {
			background-color: #eeeeee !important;
			border-color: #ccc !important;
		}

		.condition-list-value-btn.condition-list-value-btn-disable {
			color: inherit;
			background-color: inherit;
			cursor: not-allowed;
			box-shadow: none;
			border-color: #ccc !important;

			.glyphicon-option-horizontal {
				cursor: not-allowed !important;
			}
		}

		.disabled-btn {
			color: darkgray;
		}

		.dropdown-list {
			height: 26px;

			.k-dropdown-wrap {
				height: 24px;
			}
		}

		.k-dropdown.form-control,
		.k-dropdowntree.form-control {
			background-color: lightgrey;
			cursor: not-allowed;
		}

		.dropdown-list.dropdown-list {
			border: solid 1px #ccc;
		}

		.dropdown-list.oversize-questions {
			width: 100%;
			position: absolute;
		}

		.dropdown-tree.oversize-questions {
			border: 3px;
			height: 26px;
			background-color: transparent;
			overflow: hidden;
			text-overflow: ellipsis;
			border: 1px solid #bfbfbf;
			background-color: #fff;
			padding-left: 0;

			.k-select {
				height: 100%;
			}

			.k-hover,
			.k-focus {
				background-color: white;
			}

			.k-input {
				margin-top: 2px;
				padding-right: 2px;
				line-height: 1.2;
			}

			.k-clear-value {
				display: none;
			}

			.k-icon.k-i-arrow-60-down {
				width: 16px;
				height: 16px;
				border-top-width: 0;
				border-left-width: 2px;
				margin-top: 5px;
			}
		}

		.k-dropdowntree.oversize-questions,
		.k-dropdown.oversize-questions {
			.k-dropdown-wrap.k-disabled>.k-input {
				color: #333
			}
		}

		.k-dropdown .k-dropdown-wrap .k-input {
			background-color: #fff;
			height: 24px;
			line-height: 24px;
			cursor: pointer;
		}

		.k-dropdown .k-dropdown-wrap {
			background-color: #eee;
			border-color: #ccc;
		}

		.k-dropdown .k-dropdown-wrap {
			border: none;
		}

		.k-dropdown .k-dropdown-wrap .k-select {
			height: 24px;
			min-height: 24px;
			background-color: #eee;
			line-height: 20px;
			border-left: solid 1px #BFBFBF;
			cursor: pointer;

			.k-icon {
				margin-top: 4px;
			}
		}

		.k-dropdownlist .k-input-button {
			z-index: 1;
			box-sizing: border-box;
			width: 22px;
			position: absolute;
			top: 0;
			right: 0;
			height: 26px;
			min-height: 26px;
			background-color: #eee;
			line-height: 26px;
			border-width: 0;
			border-left: solid 1px #bfbfbf;
		}

		.k-dropdowntree.oversize-questions {
			.k-input-button .k-svg-icon {
				width: 16px;
				height: 16px;
				margin: auto;
				position: absolute;
				border: 0;
			}
		}

		input.form-control.k-input-inner {
			background-color: #fff;

			&.disabled {
				background-color: #eee;
			}
		}

		.if-line .wrapper-container {
			display: flex;
			flex-direction: row;
			height: 100%;
			width: 100%;

			.wrapper-left {
				width: 30px;
				padding-left: 10px;
			}

			@wrapper-line: 2px #e3e3e3 solid;

			.wrapper-right {
				height: 100%;
				margin-left: 10px;
				width: ~'calc(100% - 15px)';
			}

			.up-wrapper-first {
				height: ~'calc(100% - 20px)';
				margin-top: 10px;
				border-top: @wrapper-line;
				border-left: @wrapper-line;
			}

			.middle-wrapper {
				border-left: @wrapper-line;
			}

			.down-wrapper-last {
				height: ~'calc(100% - 30px)';
				margin-bottom: 10px;
				border-left: @wrapper-line;
				border-bottom: @wrapper-line;
			}
		}

		.if-line .if-label-row>.input-group {
			margin-top: -15px;
		}

		.if-label-line,
		.if-line,
		.then-label-line,
		.then-line {
			display: flex;
			flex-wrap: wrap;
			position: relative;

			.if-label-row,
			.then-label-row {
				.block-text {
					padding-left: 10px;
					font-size: 16px;
					font-weight: bold !important;
				}

				flex: 0 0 80px;
				padding-left: 10px;
				display: flex;
				//justify-content: center;
				align-items: center;

				span {
					vertical-align: middle;
					font-weight: normal;
				}
			}

			.if-label-row {
				margin: 0;

				input,
				button {
					height: 28px;
				}
			}

			.if-condition-row,
			.then-action-row {
				flex: 1 1 auto;

				.rule-label {
					font-weight: bold;
				}

				input,
				button {
					height: 26px;
				}
			}

			/*.then-action-contents {
				margin-bottom: 10px !important;
			}
			.action-value-row {
				margin-top: -25px;
			}
			.action-indicator {
				margin-bottom: 5px !important;
			}*/

			div.requirestar::after {
				content: "*";
				color: red;
			}

			.delete-button {
				flex: 0 0 50px;

				.formrule-delete-button {
					height: 16px !important;
					background-image: url('../../global/img/menu/x.svg');
					background-repeat: no-repeat;
					background-position: center center;
					background-size: 24px;
					cursor: pointer;
					color: transparent;
					padding-bottom: 8px;
				}
			}

			.mapping-target-source-container {
				flex-basis: 746px;
				margin-left: 95px;
				margin-right: 50px;
				position: initial;
				height: auto;

				.record-grid-container * {
					box-sizing: border-box !important;
				}

				.record-grid-container {
					.k-alt>td {
						background: #ecf2f9;
					}

					.k-alt.k-selected>td {
						background: #FFFFCE;
					}
				}

				.record-grid-container>table .k-selected:not(.disSelectable) {
					color: #2e2e2e;
					background: #FFFFCE !important;

					>.k-table-td {
						background-color: #FFFFCE;
					}
				}

				.record-grid-container {

					td.k-selected:hover,
					tr.k-selected:hover td {
						color: #2e2e2e;
						background: #FFFFCE !important;

						>.k-table-td {
							background-color: #FFFFCE;
						}
					}
				}

				.btn-default.btn-sharp {
					height: 26px;
					margin-left: 0 !important;
					border-left: 0 !important;
				}

				.formrule-delete-button.empty-icon {
					background-image: unset;
					cursor: unset;
				}

				.column-question,
				.column-value {
					&.disabled {
						opacity: 0.4;
						pointer-events: none;

						div,
						input,
						button,
						span,
						textarea {
							pointer-events: none;
						}
					}
				}
			}
		}

		.data-source-row {
			margin-left: 0;
			margin-right: 4px;

			.data-source-group {
				padding-left: 0px !important;
			}

			.copy-btn {
				margin-top: 20px;
			}

			.btn {
				height: 26px;
				border: 1px solid #999;
			}
		}

		.if-label-line,
		.then-label-line {
			.form-group {
				margin-bottom: 5px;
			}

			.delete-empty-button {
				flex: 0 0 50px;
			}
		}

		.add-condition-botton-row,
		.add-action-botton-row {
			margin-left: 80px;

			span {
				font-weight: bold;
			}
		}

		.help-block {
			position: absolute;
			top: 26px;
			width: 100%;
		}

		.notification-for-no-data {
			margin-left: 80px;
			margin-top: -18px;
			font-size: 12px;
		}

		.disabled-summary-input[readonly] {
			background-color: lightgrey !important;
			cursor: not-allowed !important;
			opacity: 0.3;
			border: 1px solid #666;
			color: #000;
		}

		.then-default-template {
			color: #333333;
			opacity: 0.5;
		}
	}

	.if-block,
	.then-block {
		.dropdown-tree.oversize-questions {

			.k-input-inner,
			.k-input {
				width: calc(100% - 30px);
				max-width: 100%;
				position: absolute;
				text-overflow: ellipsis;
			}
		}
	}

	.overhead-notification-message {
		margin-left: 15px;
		display: block;
	}
}

.forms .thank-you-message-editor-wrapper.editor-wrapper {
	height: 100%;

	.text-editor-wrapper {
		height: calc(100% - 60px);
	}

	table.k-editor.k-editor-widget {
		height: 100%;
	}

	.html-editor-wrapper {
		height: calc(100% - 64px);
	}

	.msglabel {
		line-height: 1.9;
	}
}

.typeahead.udf-type-typeahead-height {
	max-height: 250px !important;
}

.modal-dialog .modal-body.list-mover-modal,
.tabstrip-fieldtripconfigs .grid-wrapper {
	.k-grid-content .k-grid-content-expander {
		width: 100% !important;
	}
}

.tabstrip-automation .grid-wrapper {
	.kendo-grid.grid-container {
		.k-grid-content-expander {
			width: calc(~"100% - 17px") !important;
		}
	}
}

.align-with-datasource-ddl-above {
	width: calc(~"100% - 22px");
}

.udfGroup .item-drag-icon,
.List-UDF-Fields .item-drag-icon,
.case-udf-container .item-drag-icon {
	background-image: url(../img/map/thematics/DarkUpDn.svg);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 8px;
	height: 20px;
	width: 30px;
	cursor: move;
}

.udfGroup .doc-sigunature-defined-field .item-drag-icon {
	cursor: not-allowed;
}

.list-mover-drag-hint .item-drag-icon {
	background-image: url(../img/map/thematics/UpDn.svg);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 8px;
	height: 20px;
	width: 30px;
	margin-top: 2px;
	cursor: move;
}

.radio-inline,
.checkbox-inline {
	position: relative;

	.checkbox-label {
		font-weight: bold;
	}

	.checkbox-label-disabled {
		font-weight: normal;
		color: gray;
	}
}

.location-marker {
	.checkbox-inline {
		display: inline-block;
		color: #333;
		margin-bottom: 15px;
	}
}

.map-options {
	//display: flex;
	padding-left: 0px;

	.symbol-option {
		display: flex;
		margin-bottom: 10px;
	}

	.drawing-option {
		display: flex;
	}

	#symbol-selector {
		margin-right: 20px;
		display: flex;
	}

	#symbol-color-selector {
		display: flex;
	}

	.symbol-container {
		margin-top: 3px;
		margin-right: 5px;
	}

	.k-colorpicker {
		margin-left: 10px;
		margin-right: 5px;
	}

	.drawing-shape {
		padding-right: 20px;
	}
}

.customize-setting {
	display: flex;
	align-items: center;

	.k-tool-icon.k-backColor {
		display: block;
		position: absolute;
		top: 20px;
		height: 19px;
		width: 20px;
		z-index: 1;
		background-position-x: -241px;
		background-color: white;
		pointer-events: none;
		cursor: pointer;
	}

	.input-group.colorbox {
		width: 30px;
	}

	&.disabled {
		.input-group.colorbox {
			cursor: default;
		}
	}

	.thumb,
	.thumb>img,
	.thumb-blank-container {
		width: 20px;
		height: 20px;
	}

	.thumb-blank-container.k-insertImage {
		background-position-y: -97px;
	}

	.thumb .thumb-text,
	.thumb:hover .thumb-text {
		color: unset;
		background-color: unset;
	}

	.thumb:hover .thumb-text {
		height: 100%;
		width: 100%;
	}

	.k-picker-wrap {
		border: none;
	}

	.input-group.colorbox .k-picker-wrap {
		box-shadow: 0 0 3px 0 #0000004d;
	}

	&.read-only {
		cursor: auto;

		.input-group.colorbox {
			cursor: not-allowed;
		}
	}
}

.udf-checkbox-container,
.signature-checkbox-container {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;

	.udf-checkbox,
	.signature-checkbox {
		margin: 0;
	}
}

.symbols-panel.pin-icon-selector {
	top: 610px;
	left: 360px;
	z-index: 9999999;
}

.pin-icon-disabled {
	opacity: 0.3;
	cursor: not-allowed;
}

.map-home-location .map {
	margin-top: 8px;
	height: 200px;
}

.map-container {
	position: relative;
}

.doc.wrapper {
	.off-map-tool {
		.home {
			&.on:after {
				content: "";
				display: block;
				width: 18px;
				height: 18px;
				position: absolute;
				top: 20px;
				left: 20px;
				background: url(../img/icons/checkmark.png) center no-repeat;
				background-size: 12px 10px;
			}
		}
	}
}

.cutoff-setting {
	opacity: 1;

	.disableMask {
		display: none;
	}

	&.disable {
		opacity: 0.5;

		.disableMask {
			display: block;
		}
	}
}

.List-UDF-Fields .preview-button {
	width: 100px;
	line-height: 28px;
	color: #333333;
	border: 1px solid #999;
	text-align: center;
	cursor: pointer;
	border-radius: 5px;
	font-size: 14px;
	padding-left: 15px;
	padding-right: 15px;
	margin-left: 3px;

	&.disabled {
		cursor: not-allowed;
		opacity: 0.5;
	}
}

.previewContainer {

	.form-question {
		padding-bottom: 15px;
	}

	.k-multiselect .k-clear-value {
		top: 5px !important;
		margin-right: 8px;
	}

	.list-from-data-question {
		.k-combobox {
			width: 100%;

			.k-dropdown-wrap {
				height: 24px;

				&:hover {
					border-color: #007cc0;
					background-color: #fff;
					color: #333;
				}

				&.k-hover {
					.k-clear-value {
						padding-bottom: 3px;
					}
				}
			}
		}

		.k-multiselect.k-hover {
			.k-clear-value {
				line-height: 14px;
			}
		}
	}
}

.Add-Geofence-Modal {
	.map {
		height: 600px;
	}
}

.form-geofence-modal {
	min-width: 500px;
	overflow-x: hidden;
}

.modal-dialog .modal-body {
	.add-IPAddress-Modal {
		max-height: 350px;
		overflow-x: hidden;

		.title-label {
			text-align: center;
			height: 5px;
		}

		.ipAddress-line-title {
			margin-left: 30px;
		}

		.ipAddress-input {
			height: 28px;
			width: 150px;
			margin-left: auto;
		}

		.ipAddress-add-button-row {
			height: 28px;
		}

		.ipAddress-add-button {
			width: 120px;
			line-height: 28px;
			color: #333333;
			border: 1px solid #999;
			text-align: center;
			cursor: pointer;
			border-radius: 5px;
			font-size: 14px;
			padding-left: 15px;
			padding-right: 15px;
			margin-left: auto;
			margin-right: 0px;
			margin-bottom: 10px;
		}

		.ipAddress-block {
			position: relative;
			height: 55px;
		}

		.ipAddress-line {
			margin-left: 20px;
		}

		.ipAddress-row {
			align-items: center;

			&.delete-button {
				margin-top: 4px;
			}
		}

		.ipAddress-input {
			height: 28px;
			width: 180px;
			margin-left: auto;
		}

		.ipAddress-delete-button {
			height: 16px !important;
			opacity: 0.6;
			background: url('../../global/img/menu/Delete-Black.svg') no-repeat center center;
			cursor: pointer;
			color: transparent;
		}

		.help-block-row {
			position: absolute;
			top: 23px;
			left: 25px;
			max-width: 80%;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			margin-left: 10px;
		}

		.help-block {
			color: #ff0000;
			float: left;
		}
	}
}

.k-drag-clue.custom-clue {
	background-color: #FFFFCE;
	opacity: 0.8;
}

.one-question-block {
	.k-checkbox-label {
		width: 100% !important;
		line-height: unset !important;
	}
}

.name-block,
.desc-block {
	textarea {
		padding-right: 0px !important;
	}
}

.action-email-summary-input {
	background: white;
	cursor: auto !important;
}

.copyFormUrl-container {
	display: flex;

	.url-input {
		margin-right: 10px;
		background: none !important;
		cursor: auto;
	}

	.copyFormUrl-button {
		margin: auto;
		width: 128px;
		line-height: 24px;
		color: #333333;
		border: 1px solid #999;
		text-align: center;
		cursor: pointer;
		border-radius: 5px;
		font-size: 14px;

		&.disabled {
			cursor: not-allowed;
			color: #999;
		}
	}

	.qr-download-button {
		margin-top: 15px;
		width: 125.3px;
		height: 24px;
		line-height: 24px;
		color: #333333;
		border: 1px solid #999;
		text-align: center;
		cursor: pointer;
		border-radius: 5px;
		font-size: 14px;

		&.disabled {
			cursor: not-allowed;
			color: #999;
		}
	}
}

.flex-row {
	display: flex;
	justify-content: space-between;
}

.c-picker {
	label {
		padding-left: 10px;
	}

	margin-right: -10px;
}

.ip-geofence-block {
	margin-top: 10px;
	margin-bottom: 23px !important;
}

.require-public-block {

	label {
		width: auto !important;

		&.read-only {
			cursor: auto;
		}
	}

	margin-top: 10px;
	padding-right: 0;
	box-sizing: border-box;
}

.Edit-UDF-Modal .right-panel {
	.row {
		//padding-left: 10px !important;
		width: calc(100%) !important;
	}
}

.qr-img {
	display: block;
	margin-top: 15px;
	margin-right: 10px;
	padding-right: 8px;
	width: 100%;
	height: 200px;

	.qr-label {
		float: left;
		font-weight: bold;
	}

	.invalid-qr {
		border: solid 1px lightgray;
		background-color: #eee;
		height: 148px;
		width: 148px;
		text-align: center;
		vertical-align: middle;
		display: table-cell;
		color: #9d9d9d;
		box-sizing: border-box;
	}
}

.role-block {
	.k-multiselect {

		span.k-clear-value {
			display: none;
		}

		li.k-button.k-disabled {
			padding: 1.4px 14px;

			>span.k-select {
				display: none;
			}
		}

		.dropdown-icon {
			position: absolute;
			right: 1px;
			text-align: center;
			background-color: #eee;
			border-left: solid 1px #BFBFBF;
			padding-top: 4px;
			height: calc(~'100% - 6px');
			width: 20px;
			cursor: pointer;
		}
	}
}

.tabstrip-group-udf .k-content {
	height: 625px;
	overflow-x: hidden;
}

#typeRoles-list {
	.k-selected {
		background-color: #FFFFCC !important;
	}
}

.section-select-wrapper {
	height: 220px;
	width: 70%;

	.k-multiselect {
		margin-right: -12px;

		li.k-button.k-disabled {
			padding: 1.4px 14px;

			>span.k-select {
				display: none;
			}
		}

		span.k-clear-value {
			display: none;
		}

		.dropdown-icon {
			position: absolute;
			right: 1px;
			text-align: center;
			background-color: #eee;
			border-left: solid 1px #BFBFBF;
			padding-top: 4px;
			height: calc(~'100% - 6px');
			width: 20px;
			cursor: pointer;
		}
	}
}

.k-toolbar .k-overflow-anchor {
	border-left-width: 0px;
}

.rollup-udf-container {
	.input-group {
		font-size: 12px;
	}

	.k-disabled,
	.k-disabled>.k-input {
		opacity: 1;
		color: #333;
		background-color: rgba(0, 0, 0, 0.04) !important;
		cursor: not-allowed;
	}
}

.rollup-datatype-dropdownlist {
	ul {
		li {

			&:hover,
			&.k-selected.k-focus {
				background-color: #DDEDFB !important;
			}

			&.separator {
				border-top: 1px solid #ccc !important;
				height: 0px;
				min-height: 0px !important;
				pointer-events: none;
				border-bottom: 0px !important;
			}
		}
	}
}

.rollup-datatype-dropdowntree {
	>div.k-treeview {
		padding: 0;

		ul.k-treeview-lines {
			li.normal-item {
				span {
					background-color: transparent !important;
					display: block;

					&:hover {
						background-color: transparent !important;
					}
				}

				&:hover,
				&.k-selected.k-focus {
					background-color: #DDEDFB !important;
				}
			}

			li.form-item {
				ul li {
					span {
						display: block;
						background-color: transparent !important;

						&:hover {
							background-color: transparent !important;
						}
					}

					&:hover,
					&[aria-selected=true] {
						background-color: #DDEDFB !important;
					}
				}
			}
		}
	}
}

.system-defined-field-editable {

	.k-grid-copyandnew,
	.k-grid-delete,
	.k-grid-export {
		visibility: hidden;
	}
}

.modal-body .checkbox.one-question-block {
	margin-left: 0px;
}

.forms {
	.qr-label {
		font-weight: 700;
		display: inline-block;
		height: 22px;
		margin-bottom: 15px;
		margin-right: 25px;
		width: auto;
	}
}

.special-configs {
	width: 100%;

	.not-show-in-formfinder {
		display: hidden;
		width: 24px;
	}
}

.k-animation-container {
	div.has-section.k-treeview {

		ul.k-treeview-group {
			.k-item {
				span.k-in {
					text-overflow: ellipsis;
					overflow: hidden;
					width: calc(100% - 10px);
				}
			}

			ul.k-treeview-group>li {
				margin-left: -45px;

				span.k-treeview-leaf {
					text-overflow: ellipsis;
					overflow: hidden;
					padding-left: 20px;
				}
			}
		}
	}

	div.no-section.k-treeview {

		ul.k-treeview-group>li {
			margin-left: -40px;

			span.k-treeview-leaf {
				padding-left: 20px;
				text-overflow: ellipsis;
				overflow: hidden !important;
				text-wrap: nowrap;
			}
		}
	}
}

.udf-include-commas[disabled] {
	cursor: not-allowed !important;
}

.form-rule-modal {
	.btn.tf-btn-black.btn-sm.other {
		background-color: transparent !important;
		color: #333333 !important;
		border: 0 !important;
		margin-right: -20px !important;
	}

	.k-button-md.k-icon-button .k-button-icon {
		min-height: unset;
		min-width: unset;
	}
}

.typeahead>li a.btn-link {
	color: #337ab7;
	font-weight: normal;
	border-radius: 0;
}

.formrule-send-email {
	.notinput-required-message-body {
		height: 0;
		padding: 0 !important;
		border: none;
	}

	.k-editor-widget {
		border-top-style: solid !important;
	}

	.text-editor-wrapper {
		span.k-icon.disabled {
			opacity: .5;
		}

		.k-editor-widget {
			margin-top: 1px !important;
		}

		.k-editor {
			.k-editor-toolbar {
				[title="Insert field"] .k-icon {
					.insert-field-icon();
				}
			}
		}
	}

	.html-editor-wrapper {
		border: none;
		display: none;

		#ruleMessageBodyHtmlEditor {
			padding: 0;
			width: 100%;
			border: 1px solid #C5C5C5;
			height: 100%;
			outline: none;
			resize: none;
		}
	}

	.text-editor-wrapper,
	.html-editor-wrapper,
	table.k-editor.k-editor-widget {
		height: 320px !important;

		.k-colorpicker {

			&>.k-select {
				opacity: 1;
				display: block;
			}
		}

		.k-editor {
			height: 100%;

			.k-combobox .k-input-inner[type=text] {
				height: 23px;
			}
		}
	}
}

.rule-email-select {
	.radio label {
		font-weight: bold;
	}

	.useraccount-label {
		padding-bottom: 10px;
		padding-top: 10px;
	}
}

.Edit-UDF-Modal {
	.udf-img {
		position: relative;

		&.fix-height {
			height: 170px;
		}

		&.margin-top {
			margin-top: 60px;
		}

		.thumb {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			top: 30px;
			display: flex;
			justify-content: center;
			align-items: center;

			img {
				width: auto;
				height: auto;
				max-width: 146px;
				max-height: 146px;
			}
		}
	}
}