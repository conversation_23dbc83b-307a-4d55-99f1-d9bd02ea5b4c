﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.EthnicCodeDeletion = EthnicCodeDeletion;

	function EthnicCodeDeletion()
	{
		this.type = 'ethniccode';
		namespace.BaseDeletion.apply(this, arguments);
	}

	EthnicCodeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	EthnicCodeDeletion.prototype.constructor = EthnicCodeDeletion;

	EthnicCodeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();