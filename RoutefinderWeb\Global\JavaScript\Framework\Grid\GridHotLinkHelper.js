(function()
{
	function GridHotLinkHelper()
	{

	}

	/**
	 * set hot-link by hotLinkConfig and grid infomation
	 * @param {string} GridType 
	 * @param {*} $table 
	 * @param {any} hotLinkConfig 
	 * @param {any} kendoGrid 
	 */
	GridHotLinkHelper.prototype.setHotLink = function(GridType, $table, hotLinkConfig, kendoGrid)
	{
		if (hotLinkConfig[GridType] && !tf.isViewfinder)
		{
			hotLinkConfig[GridType].reduce((acc, item) =>
			{
				if (typeof item === "object")
				{
					return acc.concat(item)
				}

				var matched = acc.find(x => x.targetGrid === GridType);
				if (!matched)
				{
					return acc.concat({ targetGrid: GridType, fields: [item] })
				}

				matched.fields.push(item);
				return acc;
			}, []).forEach(item =>
			{
				Array.from($table.find(">tbody>tr.k-master-row")).forEach(tr =>
				{
					item.fields.forEach(field =>
					{
						var matchedTd = $(tr).find(`td[data-kendo-field="${field}"]`)
						if (matchedTd.length && !matchedTd.find("span").length)
						{
							let hotlinkHtml = $('<span class="hot-link"></span>');
							hotlinkHtml.text(matchedTd.text());
							matchedTd.html(hotlinkHtml);
							matchedTd.find("span").attr('title', 'Press "Alt" and click to open link');
							const eventHandler = function(e)
							{
								e.preventDefault();
								e.stopPropagation();

								const record = kendoGrid.dataItem(e.currentTarget.closest("tr"));
								const currentDocument = tf.documentManagerViewModel.obCurrentDocument();
								currentDocument.detailViewType = item.targetGrid;
								var p = item.targetGrid === "trip" ? Promise.resolve(record.TripId) : Promise.resolve(record.Id);

								p.then((recordId) => 
								{
									if (e.altKey)
									{
										currentDocument.switchDetailViewByHotLink(recordId, item.targetGrid);
									}
								})
							};
							Array.from(matchedTd[0].querySelectorAll(".hot-link")).forEach(hotlink =>
							{
								hotlink.addEventListener("mousedown", eventHandler, { capture: true });
								hotlink.addEventListener("click", function(e)
								{
									e.preventDefault();
									e.stopPropagation();
								}, { capture: true });
								hotlink.addEventListener("mouseup", function(e)
								{
									e.preventDefault();
									e.stopPropagation();
								}, { capture: true });
							});
						}



					})


				});
			});

		}
	}


	createNamespace("TF.Grid").GridHotLinkHelper = new GridHotLinkHelper();

})();