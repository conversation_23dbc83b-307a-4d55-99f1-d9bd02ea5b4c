@import "z-index";

.thematic-menu {
	position: absolute;
	margin-right: 20px;
	right: 90px;
	height: auto !important;
	width: auto;
	font-family: "SourceSansPro-Regular";
	font-size: 15px;
	display: none;
	cursor: default;
	z-index: @MAP_TOOLKIT_MENU;

	&.left-align {
		max-width: 390px;

		.thematic-menu-content ul li {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}

	.thematic-menu-content {
		margin: 0;
		color: #333;
		background-color: #ffffff;
		padding-top: 20px;
		padding-bottom: 10px;
		z-index: @MAP_TOOLKIT_MENU+2;

		.horizontal-line {
			height: 1px;
			background-color: #cccccc;
			margin: 5px 10px;
		}

		ul {
			padding-left: 0;
			margin: 0;

			li {
				list-style: none;
				height: 30px;
				line-height: 30px;
				padding-left: 15px;
				padding-right: 30px;
				cursor: pointer;
				text-align: left;

				.check {
					float: right;
					height: 30px;
					width: 30px;
					display: none;
					background-image: url("../Img/Routing Map/check-black.png");
					background-position: center;
					background-size: 12px;
					background-repeat: no-repeat;
				}

				&:hover {
					background-color: #DDEDFB;
				}

				&.select {
					font-weight: bold;

					.check {
						display: block;
					}
				}

				&.no-select {
					pointer-events: none;
					opacity: 0.5;
				}
			}
		}

		.thematic-menu-manage,
		.thematic-menu-clear {
			ul li {
				&.padding-right {
					padding-right: 40px;
				}
			}
		}

		.thematic-menu-items,
		.thematic-menu-legend {
			overflow-y: auto;
			overflow-x: hidden;

			ul li {
				padding-left: 0;
				position: relative;
				display: flex;
				justify-content: space-between;

				&:hover {
					background-color: #DDEDFB;
				}
			}
		}
	}

	.thematic-menu-manage,
	.thematic-menu-items,
	.thematic-menu-legend {
		.text {
			padding-left: 15px;
			width: 100%;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}

	&.active {
		display: block;
	}
}
