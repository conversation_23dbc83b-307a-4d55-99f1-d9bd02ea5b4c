﻿(function()
{
	createNamespace("TF.Modal").SendEmailOfRuleModalViewModel = SendEmailOfRuleModalViewModel;

	function SendEmailOfRuleModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title("Send To");

		this.contentTemplate("modal/SendEmailOfRuleControl");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Apply");
		this.sendEmailOfRuleViewModel = new TF.Control.SendEmailOfRuleViewModel(options);
		this.data(this.sendEmailOfRuleViewModel);
	}

	SendEmailOfRuleModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SendEmailOfRuleModalViewModel.prototype.constructor = SendEmailOfRuleModalViewModel;

	SendEmailOfRuleModalViewModel.prototype.positiveClick = function()
	{
		this.sendEmailOfRuleViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	SendEmailOfRuleModalViewModel.prototype.negativeClick = function()
	{
		this.sendEmailOfRuleViewModel.close().then(function(result)
		{
			if (result)
			{
				this.negativeClose();
			}
		}.bind(this));
	};

	SendEmailOfRuleModalViewModel.prototype.dispose = function()
	{
		$("#pageMenu .show-menu-button").css("z-index", "1999");
		this.sendEmailOfRuleViewModel.dispose();
	};
})();
