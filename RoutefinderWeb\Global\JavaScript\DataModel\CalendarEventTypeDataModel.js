﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.CalendarEventTypeDataModel = function(CalendarEventTypeDataModel)
	{
		namespace.BaseDataModel.call(this, CalendarEventTypeDataModel);
	}

	namespace.CalendarEventTypeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.CalendarEventTypeDataModel.prototype.constructor = namespace.CalendarEventTypeDataModel;

	namespace.CalendarEventTypeDataModel.prototype.mapping = [
		{ from: "ID", to: "id", default: 0 },
		{ from: "Description", to: "description", default: "" },
		{ from: "Insession" },
		{ from: "System", default: false },
		{ from: "Closed"},
		{ from: "Color", default: "#007DEA" , toMapping: function(v) { return (TF.Color.toHTMLColorFromLongColor(v) || ''); }}
	];
})();