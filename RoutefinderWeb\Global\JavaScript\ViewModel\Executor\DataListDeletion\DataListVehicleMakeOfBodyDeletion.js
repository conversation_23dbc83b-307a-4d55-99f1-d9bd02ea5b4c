﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListVehicleMakeOfBodyDeletion = DataListVehicleMakeOfBodyDeletion;

	function DataListVehicleMakeOfBodyDeletion()
	{
		this.type = 'vehiclemakeofbody';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Vehicle Make of Body';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListVehicleMakeOfBodyDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListVehicleMakeOfBodyDeletion.prototype.constructor = DataListVehicleMakeOfBodyDeletion;

	DataListVehicleMakeOfBodyDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehicles?MakeBodyId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListVehicleMakeOfBodyDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "vehiclemakeofbody", pb.DELETE), ids);
	}

	DataListVehicleMakeOfBodyDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};
})();