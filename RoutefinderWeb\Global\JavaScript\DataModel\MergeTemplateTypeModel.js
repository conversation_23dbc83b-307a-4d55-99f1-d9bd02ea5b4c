(function()
{

	createNamespace("TF.DataModel").MergeTemplateTypeModel = MergeTemplateTypeModel;

	function MergeTemplateTypeModel(entity)
	{
		TF.DataModel.BaseDataModel.call(this, entity);
		if (this.pageWidth() === undefined &&
			(entity == null || this.id() == 0))
		{
			this.pageWidth(8.5);
			this.pageHeight(11);

			this.marginTop(0.5);
			this.marginBottom(0);
			this.marginLeft(0.16);
			this.marginRight(0.16);

			this.cellWidth(3.995);
			this.cellHeight(1.05);
			this.cellPadding(0.157);

			this.columns(2);
			this.rows(10);
			this.columnSpacing(0.19);
			this.rowSpacing(0);

			this.hasHeader(false);
			this.hasFooter(false);

			this.headerHeight(0.5);
			this.footerHeight(0.5);
			this.roleMergeDocuments([]);
		}

		this.isPortrait = ko.pureComputed(function()
		{
			return this.pageHeight() >= this.pageWidth();
		}, this);

		this.orientation = ko.pureComputed(function()
		{
			return this.isPortrait() ? TF.MergeTemplateTypeHelper.PageOrientation.PortraitID : TF.MergeTemplateTypeHelper.PageOrientation.LandscapeID;
		}, this);

		this.isEmail = ko.pureComputed(function()
		{
			return TF.MergeTemplateTypeHelper.emailTypeId === this.id();
		}, this);

		this.hasPageSettings = ko.pureComputed(function()
		{
			return this.pageWidth() != null
				&& this.pageHeight() != null
				&& this.marginTop() != null
				&& this.marginRight() != null
				&& this.marginBottom() != null
				&& this.marginLeft() != null
				&& this.cellWidth() != null
				&& this.cellHeight() != null
				&& this.columnSpacing() != null
				&& this.rowSpacing() != null
				&& this.cellPadding() != null
				&& this.rows() != null
				&& this.columns() != null;
		}, this);
	}

	MergeTemplateTypeModel.prototype = Object.create(TF.DataModel.BaseDataModel.prototype);

	MergeTemplateTypeModel.prototype.constructor = MergeTemplateTypeModel;

	MergeTemplateTypeModel.prototype.rotate = function()
	{
		var width = this.pageWidth();
		this.pageWidth(this.pageHeight());
		this.pageHeight(width);
	};

	MergeTemplateTypeModel.prototype.mapping = [
		{ from: "Id", to: "id" },
		{ from: "Name", to: "name" },
		{ from: "PageWidth", to: "pageWidth" },
		{ from: "PageHeight", to: "pageHeight" },

		{ from: "MarginTop", to: "marginTop" },
		{ from: "MarginRight", to: "marginRight" },
		{ from: "MarginBottom", to: "marginBottom" },
		{ from: "MarginLeft", to: "marginLeft" },

		{ from: "CellWidth", to: "cellWidth" },
		{ from: "CellHeight", to: "cellHeight" },

		{ from: "ColumnSpacing", to: "columnSpacing" },
		{ from: "RowSpacing", to: "rowSpacing" },
		{ from: "CellPadding", to: "cellPadding" },

		{ from: "Rows", to: "rows" },
		{ from: "Columns", to: "columns" },

		{ from: "HeaderHeight", to: "headerHeight" },
		{ from: "FooterHeight", to: "footerHeight" },

		{ from: "HasHeader", to: "hasHeader", default: false },
		{ from: "HasFooter", to: "hasFooter", default: false },

		{ from: "SystemDefined", to: "systemDefined" },
		{ from: "RoleMergeDocuments", to: "roleMergeDocuments" },
	];
})();
