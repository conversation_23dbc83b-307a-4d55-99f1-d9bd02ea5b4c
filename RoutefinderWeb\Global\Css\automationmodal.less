.automation-modal.document-dataentry {
	.description {
		margin-top: -5px;
	}

	.row-area-header,
	.row-area-header>h2 {
		font-size: 18px;
	}

	.schedule-option {
		margin-top: 3px;
	}

	.schedule-days-inline-gap {
		margin-top: 10px;
	}

	.required-form-group.form-group {
		height: 42px;
	}

	.required-form-group.form-group.export-file-location {
		height: 52px;
	}

	.form-group-validate.form-group {
		margin-bottom: 0px;
	}

	.upload-item {
		&>.fileInput {
			display: none;
		}

		&>label {
			background-color: #ccc;
			height: 22px;
			width: 100%;

			&>span {
				cursor: pointer;
			}

			&>input {
				height: 22px;
				width: 88%;
			}
		}
	}

	.row>.col-xs-24>.checkbox {
		display: inline-block;
	}

	.row>.archive-setting-file-location-section-output-type>div.checkbox {
		display: block;
		margin-left: 0;
		margin-top: 3px;

		span.archive-count-tip {
			font-weight: normal;
		}
	}

	.checkboxes {
		margin-top: -8px;

		.col-xs-24>.checkbox {
			margin: 0;
			margin-top: 5px;
			margin-right: 5px;
		}
	}

	div.checkbox {
		line-height: inherit !important;
		margin-left: 0px;
	}

	.col-xs-24 {
		>.checkbox:not(:first-child) {
			margin-left: 10px;
		}
	}

	.dropdown-datatype {
		.k-select {
			height: 20px;
			min-height: 20px;
			background-color: #eeeeee;
			line-height: 20px;
			border-left: solid 1px #BFBFBF;
		}

		.k-icon.k-i-arrow-60-down {
			display: inline-block;
			width: 0;
			height: 0;
			vertical-align: middle;
			border-top: 4px solid;
			border-right: 4px solid transparent;
			border-left: 4px solid transparent
		}

		.k-input {
			line-height: 15px;
		}

		.k-clear-value {
			display: none;
		}
	}
}

.scheduled-report-modal,
.scheduled-merge-document-modal {
	&.view-finder.document-dataentry {
		.form-group [data-bv-for="recurEvery"] {
			width: 300%;
		}

		.checkbox,
		.radio {
			line-height: 1.4;
		}

		input[type="checkbox"],
		input[type="radio"] {
			margin-top: 3px;
		}

		.schedule-days-inline-gap {
			margin-top: 4px;
		}

		.schedule-option {
			margin-top: 3px;
		}
	}

	.form-group .input-group input[type="text"] {
		border-color: #ccc !important;
	}

	.adjust-padding {
		padding-left: 0px !important;
	}
}

.document-dataentry {
	.btn.btn-default.btn-sharp.success {
		color: green;
	}
}

.automation-extend {
	.import-file-createdate {
		color: #848484;
		font-size: 12px;
		margin-bottom: 5px;
		height: 18px;
	}

	.form-group {
		.k-dropdownlist {
			.k-input-inner {
				height: 20px;
			}
		}
	}
}

.automation-data-type-label {
	font-size: 16px;
}

.divide-line {
	background-color: #E4E4E4;
	height: 1px;
	margin-top: 15px;
	margin-bottom: 15px;
}

.output-group {
	margin-bottom: 0px;

	h2 {
		margin-top: 10px;
	}
}

.adjust-hidden {
	width: 0px;
	height: 0px;
	border: none;
}

.data-source-selector {
	margin-bottom: 30px;
}

.location-input {
	input {
		background-color: #EEEEEE;
		color: #5E5E5E;
	}
}

.overwrite-setting {
	margin-top: 15px !important;
	margin-bottom: 0px !important;
}