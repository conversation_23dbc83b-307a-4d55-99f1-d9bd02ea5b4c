﻿(function()
{
	createNamespace("TF.Modal.Grid").ModifyReminderFilterModalViewModel = ModifyReminderFilterModalViewModel;

	ModifyReminderFilterModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ModifyReminderFilterModalViewModel.prototype.constructor = ModifyReminderFilterModalViewModel;
	function ModifyReminderFilterModalViewModel(gridType, isNew, gridFilterDataModel, headerFilters, gridMetadata)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-lg";
		this.modalClass = 'savefilter-modal';
		this.title('Save filter');
		if (isNew)
		{
			this.title('Save As New Filter');
		}
		else
		{
			this.title('Save Filter');
		}
		this.description("Enter the filter statement in the area below.  You may use the Field, Operator, Value and Logical fields to help you build your statement.  To add a Field Operator, Value or Logical value to your statement, place your cusor in your statement where you would like the value added.  Then select or enter a value.  The value will be added where your cursor was placed.");

		this.contentTemplate('workspace/grid/modifyreminderfilter');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel = ko.observable("Save");
		this.modifyReminderFilterViewModel = new TF.Grid.ModifyReminderFilterViewModel(gridType, isNew, gridFilterDataModel, headerFilters, gridMetadata);
		this.data(this.modifyReminderFilterViewModel);
	};

	ModifyReminderFilterModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.modifyReminderFilterViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this))
	};

	ModifyReminderFilterModalViewModel.prototype.dispose = function()
	{
		this.modifyReminderFilterViewModel.dispose();
	};
})();

