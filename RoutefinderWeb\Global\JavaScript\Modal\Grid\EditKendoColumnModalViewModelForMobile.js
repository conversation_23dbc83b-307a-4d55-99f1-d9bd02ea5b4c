(function()
{
	createNamespace("TF.Modal.Grid").EditKendoColumnModalViewModelForMobile = EditKendoColumnModalViewModelForMobile;

	var firstOpenKey = "edit-column-mobile-first-load";

	function EditKendoColumnModalViewModelForMobile(gridOptions)
	{
		var self = this;
		self.gridOptions = ko.observableArray();
		self.activeIndex = ko.observable(0);
		self.activeOption = ko.computed(function()
		{
			return self.gridOptions()[self.activeIndex()];
		});
		gridOptions.forEach(function(options, idx)
		{
			if (options.availableColumns == null)
			{
				return;
			}
			options.selectedColumns.forEach(function(item, i)
			{
				item.obSelected = ko.observable(true);
				item.orderIndex = i;
			});
			options.availableColumns.forEach(function(item)
			{
				item.obSelected = ko.observable(false);
				item.orderIndex = 1000;
			});

			var option = {};
			option.selectedListViewId = 'editColumnMobileListView' + idx;
			option.unselectedListViewId = 'editColumnMobileNotSelectListView' + idx;
			option.selectedColumns = ko.observableArray([]);
			option.availableColumns = ko.observableArray([]);
			option.allColumns = ko.observableArray(options.selectedColumns.concat(options.availableColumns));
			option.successCallback = options.successCallback;
			option.orignalSelectedColumns = options.selectedColumns.slice();
			option.name = options.name;
			option.label = options.name.toUpperCase();
			option.index = idx;
			self.gridOptions.push(option);
		});
		this.description = "You can add to, remove, and reorder the columns in this grid. Tap any column to move it into the selected columns group.  Tap any selected column to move it back into the list of available columns.  To adjust the column display order, arrange the columns from top to bottom in the left to right order you would like them to display in the grid.";
		this.obDescription = ko.observable(this.description);
		this.isFirstLoad = tf.storageManager.get(firstOpenKey) || true;
	}

	EditKendoColumnModalViewModelForMobile.prototype = Object.create(TF.ContextMenu.BaseGeneralMenuViewModel.prototype);

	EditKendoColumnModalViewModelForMobile.prototype.constructor = EditKendoColumnModalViewModelForMobile;


	EditKendoColumnModalViewModelForMobile.prototype.setSelectedAndAvailable = function(option)
	{
		option.selectedColumns(Enumerable.From(option.allColumns()).Where("$.obSelected()").OrderBy("$.orderIndex").ToArray());
		option.availableColumns(Enumerable.From(option.allColumns()).Where("$.obSelected()==false").OrderBy("$.DisplayName.toLowerCase()").ToArray());
	};

	EditKendoColumnModalViewModelForMobile.prototype.toggleActiveItem = function(model, e)
	{
		this.activeIndex(model.index);
	}

	var prevTarget;
	EditKendoColumnModalViewModelForMobile.prototype.toggleSelectItem = function(model, e)
	{
		var option = this.activeOption();
		if (prevTarget)
		{
			prevTarget.finish();
		}
		model.obSelected(!model.obSelected());
		var durection = 200;
		var target = $(e.currentTarget);
		var toDivId = option.selectedListViewId;
		var fromDivId = option.unselectedListViewId;
		prevTarget = target;
		if (model.obSelected())
		{
			model.orderIndex = option.selectedColumns().length + 1;
		}
		else
		{
			toDivId = option.unselectedListViewId;
			fromDivId = option.selectedListViewId;
			model.orderIndex = option.allColumns().length;
		}
		var $toDiv = $("#" + toDivId),
			$fromDiv = $("#" + fromDivId);
		$toDiv.finish();
		$fromDiv.finish();
		var newHeight = $toDiv.outerHeight() + target.outerHeight() - 1;
		var newFromHeight = $fromDiv.outerHeight() - target.outerHeight() + 1;
		$toDiv.animate(
			{
				height: newHeight
			}, durection, function()
		{
			$toDiv.css('height', 'auto');
		});
		$fromDiv.animate(
			{
				height: newFromHeight
			}, durection, function()
		{
			$fromDiv.css('height', 'auto');
		});
		target.animate(
			{
				height: 0,
				opacity: 0,
				paddingTop: 0,
				paddingBottom: 0
			},
			durection,
			function()
			{
				this.setSelectedAndAvailable(option);
				this.setSortable(option);

				var $gridIconWrapper = $('.document-grid.grid-map-container .grid-icons');
				if (option.selectedColumns().length < 1)
				{
					var message = "At least one record must be selected.";
					tf.promiseBootbox.alert(message, "Warning");
				}
				else
				{
				}
			}.bind(this));
	};

	EditKendoColumnModalViewModelForMobile.prototype.initModel = function(model, element)
	{
		var self = this;
		self.$element = $(element);
		self.$description = self.$element.find(".mobile-modal-grid-description");
		self.$container = self.$element.find(".scroll-container");
		self.gridOptions().forEach(function(option)
		{
			self.setSelectedAndAvailable(option);
		});
		if (self.isFirstLoad !== true)
		{
			self.lessDescriptionClick();
		} else
		{
			self.moreDescriptionClick();
		}
		tf.storageManager.save(firstOpenKey, "loaded");
		setTimeout(function()
		{
			self.setDescription();
			self.gridOptions().forEach(function(option)
			{
				self.setSortable(option);
			});
		}, 0);
	};

	EditKendoColumnModalViewModelForMobile.prototype.setDescription = function()
	{
		this.$container.css("height", "calc(100% - " + this.$description.outerHeight() + "px)");

	};

	EditKendoColumnModalViewModelForMobile.prototype.moreDescriptionClick = function()
	{
		this.$description.removeClass('more').addClass('less');
		this.obDescription(this.description);
		this.setDescription();
	};

	EditKendoColumnModalViewModelForMobile.prototype.lessDescriptionClick = function()
	{
		this.$description.removeClass('less').addClass('more');
		var $testWidth = $("<div></div>").css(
			{
				"position": "absolute",
				"left": 10000
			}).width($(document).width() - 30);
		$("body").append($testWidth);
		var description = "";
		for (var i = 0; i <= this.description.length; i++)
		{
			$testWidth.html(this.description.substring(0, i) + "more...");
			if ((/\W/g).test(this.description[i]))
			{
				if ($testWidth.height() > 38) //two line
				{
					break;
				}
				else
				{
					description = this.description.substring(0, i);
				}
			}
		}
		$testWidth.remove();
		this.obDescription(description);
		this.setDescription();
	};

	EditKendoColumnModalViewModelForMobile.prototype.setSortable = function(option)
	{
		var self = this,
			id = '#' + option.selectedListViewId;
		if ($(id).uiSortable)
		{
			$(id).sortable("destroy");
		}
		$(id).sortable(
			{
				handle: ".drag-handler",
				placeholder: "ui-sortable-placeholder",
				stop: function(event, ui)
				{
					var ans = {};
					$(id).find("[fieldName]").each(function(index, item)
					{
						ans[$(item).attr("fieldName")] = index;
					});
					option.allColumns().forEach(function(item)
					{
						if (item.obSelected())
						{
							item.orderIndex = ans[item.FieldName];
						}
					});
				}
			});
	};

	EditKendoColumnModalViewModelForMobile.prototype.dispose = function()
	{
		var self = this;
		self.gridOptions().forEach(function(option)
		{
			self.setSelectedAndAvailable(option);
			if (Enumerable.From(option.orignalSelectedColumns).Select("$.FieldName").ToArray().join(",") != Enumerable.From(option.selectedColumns()).Select("$.FieldName").ToArray().join(","))
			{
				option.successCallback(
					{
						selectedColumns: option.selectedColumns(),
						availableColumns: option.availableColumns(),
						name: option.name
					});
			}
		})
	};

})();
