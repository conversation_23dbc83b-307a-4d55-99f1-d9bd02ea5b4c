(function()
{
	createNamespace("TF.Events").Event = Event;

	function Subscription(ev, key)
	{
		this.event = ev;
		this.key = key;
	}

	Subscription.prototype.dispose = function()
	{
		this.event.unsubscribe(this.key);
	};

	/***
	 * A simple publisher-subscriber implementation.
	 * @class Event
	 * @constructor
	 */
	function Event()
	{
		this.handlers = [];
	};

	Event.prototype.subscribe = function(fn, caller, one)
	{
		var key = TF.generateUUID(), handler = { key: key, func: fn, caller: caller, one: one };
		this.handlers.push(handler);
		return new Subscription(this, key);
	};

	Event.prototype.one = function(fn, caller)
	{
		return this.subscribe(fn, caller, true);
	};

	Event.prototype.unsubscribe = function(fn)
	{
		var indexes = [];
		if (typeof fn === "string")
		{
			var index = this.handlers.findIndex(function(i) { return i.key === fn; });
			if (index > -1)
			{
				indexes.push(index);
			}
		}
		else
		{
			for (var i = this.handlers.length - 1; i >= 0; i--)
			{
				if (this.handlers[i].func === fn)
				{
					indexes.push(i);
				}
			}
		}

		indexes.forEach(function(i)
		{
			this.handlers.splice(i, 1);
		}.bind(this));
	};

	Event.prototype.hasSubscribed = function(fn)
	{
		if (typeof fn === "string")
		{
			return this.handlers.some(function(i) { return i.key === fn; });
		}

		return this.handlers.some(function(i) { return i.func === fn; });
	};

	Event.prototype.dispose = function()
	{
		this.unsubscribeAll();
	};

	Event.prototype.unsubscribeAll = function()
	{
		this.handlers = [];
	};

	/***
		 * Fires an event notifying all subscribers.
		 * @method notify
		 * @param args {Object} Additional data object to be passed to all this.handlers.
		 * @param e {EventData}
		 *      Optional.
		 *      An <code>EventData</code> object to be passed to all this.handlers.
		 *      For DOM events, an existing W3C/jQuery event object can be passed in.
		 * @param scope {Object}
		 *      Optional.
		 *      The scope ("this") within which the handler will be executed.
		 *      If not specified, the scope will be set to the <code>Event</code> instance.
		 */
	Event.prototype.notify = function(args, e, scope)
	{
		e = e || new TF.Events.EventData();
		var returnValue, removingHandlers = [];
		for (var i = 0; i < this.handlers.length && !(e.isPropagationStopped() || e.isImmediatePropagationStopped()); i++)
		{
			var handler = this.handlers[i],
				func = handler.func,
				caller = scope || handler.caller || this;
			if (arguments.length > 3 && arguments[3] instanceof Array)
			{
				var appendArgs = arguments[3];
				appendArgs.unshift(e, args);
				returnValue = func.apply(caller, appendArgs);
			}
			else
			{
				returnValue = func.call(caller, e, args);
			}

			if (handler.one)
			{
				removingHandlers.push(handler);
			}
		}

		removingHandlers.forEach(i =>
		{
			const index = this.handlers.indexOf(i);
			if (index > -1)
			{
				this.handlers.splice(index, 1);
			}
		});

		return returnValue;
	};

	Event.prototype.fire = function()
	{
		var e = new TF.Events.EventData(), returnValue, removingHandlers = [];
		for (var i = 0; i < this.handlers.length && !(e.isPropagationStopped() || e.isImmediatePropagationStopped()); i++)
		{
			var handler = this.handlers[i],
				func = handler.func,
				caller = handler.caller || this;
			returnValue = func.apply(caller, [e].concat(Array.prototype.slice.call(arguments)));

			if (handler.one)
			{
				removingHandlers.push(handler);
			}
		}

		removingHandlers.forEach(i =>
		{
			const index = this.handlers.indexOf(i);
			if (index > -1)
			{
				this.handlers.splice(index, 1);
			}
		});

		return returnValue;
	};
})();
