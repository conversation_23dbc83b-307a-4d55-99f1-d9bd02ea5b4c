.document-dataentry.field-trip-template {
	height: calc(100vh - 200px);

	.checkbox-container {
		input {
			top: 20px;
			cursor: pointer;
		}
		label {
			font-weight: normal;
			cursor: pointer;
			padding-top: 18px;
			padding-left: 3px;
			font-size: 13px;
		}
	}

	.tab {
		.tab-header {
			display: flex;
			> div {
				display: inline-block;
				background-color: #ebe9e9;
				border: 1px solid #ccc;
				border-right: none;
				cursor: pointer;
				text-align: center;
				flex-basis: 10%;
				padding: 5px 10px;
				
				&.destination {
					flex-basis: 15%;
				}

				&.document {
					flex-basis: 15%;
				}

				&.udf {
					flex-basis: 240px;
				}				

				&.tab-placeholder {
					background: none;
					border-top: none;
					border-bottom: 1px solid #ccc;
					flex-basis: 50%;
					cursor: pointer;
					pointer-events: none;
				}

				&.active {
					border-bottom: none;
					background-color: #fff;
				}
			}
		}

		.tab-panel {
			> div {
				display: none;
				padding: 10px;
				border: 1px solid #ccc;
				border-top: none;
				margin-bottom: 20px;

				&:first-child {
					display: block;
				}
			}

			.notes {
				textarea {
					height: 300px;
				}
			}

			.document,
			.billing {
				.header {
					margin-bottom: 6px;
					.text {
						font-weight: bold;

						> span {
							margin-left: 3px;
						}
					}

					.button-bar {
						display: inline-block;
						vertical-align: middle;
						padding-left: 12px;

						div {
							display: inline-block;
							opacity: 0.4;
							width: 16px;
							height: 16px;
							margin-right: 5px;
						}

						.edit {
							background: url('../../global/img/grid/editor_pencil.png') no-repeat center center;
							&.enable {
								opacity: 0.7;
								cursor: pointer;
							}
						}

						.delete {
							background: url('../../global/img/menu/Delete-Black.svg') no-repeat center center;
							&.enable {
								opacity: 0.7;
								cursor: pointer;
							}
						}

						.add {
							cursor: pointer;
							opacity: 1;
							background: url('../../global/img/add.png') no-repeat center center;
						}
					}
				}
			}

			.billing .k-grid-content {
				height: 266px;
			}

			.document .k-grid-content {
				height: 366px;
			}


			.udf .main {
				button.list-default-value-button,
				button.list-mover-button {
					position: absolute;
					height: 20px;
					width: 22px;
					right: 1px;
					top: 1px;
					background-repeat: no-repeat;
					background-position: center;
					background-color: #eee;
					border: none;
					border-left: 1px solid #ccc;
					background-image: url(../img/detail-screen/editor-icon-ellipsis.svg);
					background-size: 16px 16px;
					z-index: 10;
		
					&:hover {
						border-color: #adadad;
					}
		
					&:active {
						box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
					}
				}
				.image-default {
					width: 200px;
					height: 200px;
					
					img {
						display: block;
						margin: 0 auto;
						width: 146px;
						height: 146px;
						object-fit: contain;
					}
				}

				.udf-caption-area {
					display: flex;
					border: 1px solid #999 !important;
			
					&.disabled {
						cursor: not-allowed;
					}
				}
			
				.image-remove {
					position: absolute;
					right: 0px;
					top: 0px;
					padding: 8px;
					background-repeat: no-repeat;
					height: 13px;
					width: 13px;
					background-size: 10px 10px;
					background-position: center;
					background-color: rgba(255, 255, 255, .5);
					background-image: url('../img/black-del.png');
					cursor: pointer;
					z-index: 3;
				}

				input.udf-caption {
					width: 100%;
					background-color: transparent;
					color: #333;
					border: none;
					outline: none;
			
					&[disabled] {
						cursor: not-allowed;
					}
				}
			
				.udf-caption-counter {
					display: none;
				}

				.udf-caption:focus + .udf-caption-counter {
					display: block;
					color: #7d7d7d;
					width: 40px;
				}
			}
		}
	}
	
	.radio-container {
		input[type="radio"] {
			position: relative;
			cursor: pointer;
		}

		label {
			font-weight: normal;
			cursor: pointer;
		}
	}

	.form-group.tf-validate.tf-textarea {
		height: 79px;
	}
}

.document-dataentry.field-trip-template-document {
	height: 300px;
}