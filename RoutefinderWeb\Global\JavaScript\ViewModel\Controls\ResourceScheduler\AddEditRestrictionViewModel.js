﻿(function()
{
	createNamespace('TF.Control.ResourceScheduler').AddEditRestrictionViewModel = AddEditRestrictionViewModel;

	function AddEditRestrictionViewModel(resourceId, resourceType, startDate, endDate, restrictionEntity, pageLevelViewModel)
	{
		this.momentHelper = new TF.Document.MomentHelper();
		this.dateTypes = ["allfuturedays", "daterange"];

		var startDateStr, endDateStr, dateType,
			description = "", startTime = "12:00 AM", endTime = "12:00 AM",
			allDay = true;

		if (!endDate)
		{
			endDate = startDate;
			dateType = this.dateTypes[0];
		} else
		{
			dateType = this.dateTypes[1];
		}

		startDateStr = this.momentHelper.toString(startDate, "YYYY-MM-DD");
		endDateStr = this.momentHelper.toString(endDate, "YYYY-MM-DD");

		if (restrictionEntity)
		{
			description = restrictionEntity.Name;
			startTime = this.momentHelper.toString(restrictionEntity.StartTime, "hh:mm A");
			endTime = this.momentHelper.toString(restrictionEntity.EndTime, "hh:mm A");
			startDateStr = this.momentHelper.toString(restrictionEntity.StartTime, "YYYY-MM-DD");
			endDateStr = this.momentHelper.toString(restrictionEntity.EndTime, "YYYY-MM-DD");

			if (restrictionEntity.Day === 7)
			{
				dateType = this.dateTypes[0];
				startDateStr = this.momentHelper.toString(startDate, "YYYY-MM-DD");
				endDateStr = this.momentHelper.toString(endDate, "YYYY-MM-DD");
			}
			else
			{
				dateType = this.dateTypes[1];
			}

			allDay = (startTime === "12:00 AM" && endTime === "12:00 AM");
		}

		this.resourceId = resourceId;
		this.resourceType = resourceType;
		this.startDate = startDate;
		this.endDate = endDate;
		this.restrictionEntity = restrictionEntity;

		this.obDescription = ko.observable(description);
		this.obStartTime = ko.observable(startTime);
		this.obEndTime = ko.observable(endTime);
		this.obShowStartTimeNullError = ko.observable(false);
		this.obShowEndTimeNullError = ko.observable(false);
		this.obAllDayChecked = ko.observable(allDay);
		this.obDateType = ko.observable(dateType);
		this.obStartDate = ko.observable(startDateStr);
		this.obEndDate = ko.observable(endDateStr);
		this.obShowStartDateNullError = ko.observable(false);
		this.obShowEndDateNullError = ko.observable(false);

		this.obDateType.subscribe(function()
		{
			this.updateDatePickerState();
		}.bind(this));

		this.obAllDayChecked.subscribe(this.allDayChecked.bind(this));

		this.obStartTime.subscribe(function() { this.timeChanged(true); }.bind(this));
		this.obEndTime.subscribe(function() { this.timeChanged(false); }.bind(this));

		this.obStartDate.subscribe(function() { this.dateChanged(true); }.bind(this));
		this.obEndDate.subscribe(function() { this.dateChanged(false); }.bind(this));

		this.updating = false;
		this.pageLevelViewModel = pageLevelViewModel || new TF.PageLevel.BasePageLevelViewModel();
	}

	AddEditRestrictionViewModel.prototype.init = function(viewModel, el)
	{
		let validatorFields = {}, isValidating = false, self = this;
		self.$form = $(el);
		validatorFields.description = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: " required"
				}
			}
		}

		$(el).bootstrapValidator({
			excluded: [':hidden', ':not(:visible)'],
			live: 'enabled',
			message: 'This value is not valid',
			fields: validatorFields
		}).on('success.field.bv', function(e, data)
		{
			if (!isValidating)
			{
				isValidating = true;
				self.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		// this.$form.find("input[name=status]").focus();
		self.pageLevelViewModel.load(self.$form.data("bootstrapValidator"));

		// this.load();
		self.updateDatePickerState();
	};

	AddEditRestrictionViewModel.prototype.allDayChecked = function()
	{
		if (this.obAllDayChecked())
		{
			this.obStartTime("12:00 AM");
			this.obEndTime("12:00 AM");
		}
	};

	AddEditRestrictionViewModel.prototype.validateTimeCompare = function()
	{
		var startTime = this.momentHelper.toString(this.obStartTime(), "hh:mm A"),
			endTime = this.momentHelper.toString(this.obEndTime(), "hh:mm A");

		return this.momentHelper.minutesDiff(startTime, endTime) > 0;
	};

	AddEditRestrictionViewModel.prototype.validateDateCompare = function()
	{
		return this.momentHelper.toDate(this.obStartDate()) <= this.momentHelper.toDate(this.obEndDate());
	};

	AddEditRestrictionViewModel.prototype.updateDatePickerState = function()
	{
		var dateEnabled = this.obDateType() === "daterange";
		dateEnabled ? this.enableDatePicker() : this.disableDatePicker();
	};

	AddEditRestrictionViewModel.prototype.autoCheckedAllDay = function()
	{
		var startTime = this.momentHelper.toString(this.obStartTime(), "hh:mm A"),
			endTime = this.momentHelper.toString(this.obEndTime(), "hh:mm A");

		this.obAllDayChecked(startTime === "12:00 AM" && endTime === "12:00 AM");
	};

	AddEditRestrictionViewModel.prototype.keepEndTimeGreaterThanStart = function(isChangingStartTime)
	{
		if (this.validateTimeCompare()) return;

		if (isChangingStartTime)
		{
			this.obEndTime(this.momentHelper.toString(this.obStartTime(), "hh:mm A"));
		}
		else
		{
			this.obStartTime(this.momentHelper.toString(this.obEndTime(), "hh:mm A"));
		}
	};

	AddEditRestrictionViewModel.prototype.timeChanged = function(isChangingStartTime)
	{
		if (this.updating) return;

		const isStartTimeNull = !this.obStartTime();
		this.obShowStartTimeNullError(isStartTimeNull);

		const isEndTimeNull = !this.obEndTime();
		this.obShowEndTimeNullError(isEndTimeNull);

		if (!isStartTimeNull && !isEndTimeNull)
		{
			this.updating = true;
			this.keepEndTimeGreaterThanStart(isChangingStartTime);
			this.autoCheckedAllDay();
			setTimeout(function() { this.updating = false; }.bind(this), 0);
		}
	};

	AddEditRestrictionViewModel.prototype.keepEndDateGreaterThanStart = function(isChangingStartDate)
	{
		if (this.validateDateCompare()) return;

		if (isChangingStartDate)
		{
			this.obEndDate(this.momentHelper.toString(this.obStartDate(), "YYYY-MM-DD"));
		}
		else
		{
			this.obStartDate(this.momentHelper.toString(this.obEndDate(), "YYYY-MM-DD"));
		}
	};

	AddEditRestrictionViewModel.prototype.dateChanged = function(isChangingStartDate)
	{
		if (this.updating) return;

		const isStartDateNull = !this.obStartDate();
		this.obShowStartDateNullError(isStartDateNull);

		const isEndDateNull = !this.obEndDate();
		this.obShowEndDateNullError(isEndDateNull);

		if (!isStartDateNull && !isEndDateNull)
		{
			this.updating = true;
			this.keepEndDateGreaterThanStart(isChangingStartDate);
			setTimeout(function() { this.updating = false; }.bind(this), 0);
		}


	};

	/**
	 * Set date options.
	 * @param options dateType: "allfuturedays", "daterange",
	 * startDate, endDate
	 */
	AddEditRestrictionViewModel.prototype.setDateOptions = function(options)
	{
		if (options.dateType)
		{
			this.obDateType(options.dateType);
		}

		if (options.dateType === "daterange")
		{
			this.obStartDate(options.startDate);
			this.obEndDate(options.endDate);
		}
	};

	AddEditRestrictionViewModel.prototype.isValid = function()
	{
		return !this.obShowStartTimeNullError() && !this.obShowEndTimeNullError() &&
			(this.obDateType() !== "daterange" || (!this.obShowStartDateNullError() && !this.obShowEndDateNullError()));
	};

	AddEditRestrictionViewModel.prototype.apply = function()
	{
		let self = this;
		return self.pageLevelViewModel.saveValidate()
			.then(function(result)
			{
				if (!result || !self.isValid()) return Promise.resolve(false);

				const dateFormat = "YYYY-MM-DD";
				const timeFormat = "HH:mm:ss";
				const startTimeStr = self.momentHelper.toString(self.obStartTime(), timeFormat);
				const endTimeStr = self.momentHelper.toString(self.obEndTime(), timeFormat);
				let startDateStr = moment(self.obStartDate()).format(dateFormat);
				let endDateStr = moment(self.obEndDate()).format(dateFormat);

				const entity = {
					Description: self.obDescription(),
					ResourceType: self.resourceType === "vehicles" ? 3 : 1,
					ResourceID: self.resourceId,
				};

				switch (self.obDateType())
				{
					case "allfuturedays":
						entity.Day = 7;
						entity.BeginTime = `${moment().format(dateFormat)}T${startTimeStr}`;
						entity.EndTime = `9999-12-31T${endTimeStr}`;
						break;
					case "daterange":
						entity.Day = 0;
						entity.BeginTime = `${startDateStr}T${startTimeStr}`;
						entity.EndTime = `${endDateStr}T${endTimeStr}`;
						break;
				}

				if (self.restrictionEntity)
				{
					return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), "schedresources"), {
						paramData: {
							Id: self.restrictionEntity.Id
						},
						data: [
							{ "op": "replace", "path": "/Description", "value": entity.Description },
							{ "op": "replace", "path": "/BeginTime", "value": entity.BeginTime },
							{ "op": "replace", "path": "/EndTime", "value": entity.EndTime },
							{ "op": "replace", "path": "/Day", "value": entity.Day }
						]
					}).then(function()
					{
						return true;
					});
				}
				else
				{
					return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "schedresources"), {
						data: [entity]
					}).then(function()
					{
						return true;
					});
				}
			});
	};

	AddEditRestrictionViewModel.prototype.enableDatePicker = function()
	{
		var self = this,
			$startDate = document.querySelectorAll('input[name=startDate]'),
			$endDate = document.querySelectorAll('input[name=endDate]'),
			$dateTimeButton = null;
		self.enableElement($startDate);
		self.enableElement($endDate);

		if ($startDate && $startDate.length > 0)
		{
			$dateTimeButton = $startDate[0].parentElement.querySelectorAll(".datepickerbutton");
			self.enableElement($dateTimeButton);
		}
		if ($endDate && $endDate.length > 0)
		{
			$dateTimeButton = $endDate[0].parentElement.querySelectorAll(".datepickerbutton");
			self.enableElement($dateTimeButton);
		}
	};

	AddEditRestrictionViewModel.prototype.disableDatePicker = function()
	{
		var self = this,
			$startDate = document.querySelectorAll('input[name=startDate]'),
			$endDate = document.querySelectorAll('input[name=endDate]'),
			$dateTimeButton = null,
			$icon = null;
		self.disableElement($startDate);
		self.disableElement($endDate);

		if (($startDate && $startDate.length === 0) ||
			($endDate && $endDate.length === 0))
		{
			window.setTimeout(self.disableDatePicker.bind(self), 100);
			return;
		}

		if ($startDate && $startDate.length > 0)
		{
			$dateTimeButton = $startDate[0].parentElement.querySelectorAll(".datepickerbutton");
			$icon = $startDate[0].parentElement.querySelectorAll(".k-i-calendar");
			self.disableElement($dateTimeButton);
			self.disableElement($icon);
		}
		if ($endDate && $endDate.length > 0)
		{
			$dateTimeButton = $endDate[0].parentElement.querySelectorAll(".datepickerbutton");
			$icon = $endDate[0].parentElement.querySelectorAll(".k-i-calendar");
			self.disableElement($dateTimeButton);
			self.disableElement($icon);
		}
	};

	AddEditRestrictionViewModel.prototype.enableElement = function($element)
	{
		if ($element && $element.length > 0)
		{
			$element[0].disabled = false;
			$($element[0]).css("pointer-events", "auto");
		}
	};

	AddEditRestrictionViewModel.prototype.disableElement = function($element)
	{
		if ($element && $element.length > 0)
		{
			$element[0].disabled = true;
			$($element[0]).css("pointer-events", "none");
		}
	};
})();