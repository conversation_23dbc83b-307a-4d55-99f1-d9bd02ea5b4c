(function()
{
	const namespace = window.createNamespace("TF.DataModel");

	namespace.TagDataModel = function(tag)
	{
		namespace.BaseDataModel.call(this, tag);
	}

	namespace.TagDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);
	namespace.TagDataModel.prototype.constructor = namespace.TagDataModel;

	namespace.TagDataModel.prototype.mapping = [
		{ from: "Id", to: "id", default: 0 },
		{ from: "Name", to: "name", default: "" },
		{ from: "Color", to: "color", default: "#D0503C" },
		{ from: "Description", to: "description", default: null },
		{ from: "TagDataTypes", to: "tagDataTypes", default: [] },
	];
})();