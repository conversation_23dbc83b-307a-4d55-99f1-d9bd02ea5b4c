ko.bindingHandlers.panelContentDrag = {
	init: function(element, valueAccessor)
	{
		var $element = $(element);
		var displayArray = valueAccessor() ? valueAccessor().displayArray : null;
		if (displayArray)
		{
			displayArray.subscribe(function(newValue)
			{
				var oldValue = $element.data("oldValue") || [];
				if (newValue && newValue.length != oldValue.length && $element.height() != 0)
				{
					$element.height("auto");
					$element.closest(".content-container").height("auto");
					$element.data("oldValue", newValue);
				}
			});
		}
		ko.bindingHandlers.panelContentDrag.makeTitleDraggable($element);
	},
	update: function()
	{
	},
	makeTitleDraggable: function($element)
	{
		var dragInfo = {};
		var header = $element.closest(".content-container").prev(".item-header");

		// toggle slide up down
		header.on("click", function(e)
		{
			if (dragInfo.dragging)
			{
				return;
			}
			var $content = $(e.currentTarget).next();
			$content.css("overflow", "hidden");

			if ($content.height() == 0)
			{
				slideDown();
			} else
			{
				slideUp();
			}

			function slideDown()
			{
				$content.css("height", "auto");
				$content.find(".pannel-item-content").css("height", "auto");
				var height = $content.height();
				$content.height(0);
				$content.animate({ height: height }, 500, function()
				{
					$content.css("height", "auto");
					$content.find(".pannel-item-content").css("height", "auto");
				});
			}

			function slideUp()
			{
				$content.animate({ height: 0 }, 500);
			}
		});
		// var $sliderList = $element.closest(".slider-list");
		// function findPrevContentContainer(header)
		// {
		// 	var prevElement = header.prev();
		// 	if (prevElement.length == 0)
		// 	{
		// 		return null;
		// 	}
		// 	if (prevElement.is(".content-container:not(.disable-resize)"))
		// 	{
		// 		return prevElement;
		// 	}
		// 	return findPrevContentContainer(prevElement);
		// }

		// function setStartHeight()
		// {
		// 	$sliderList.find(".content-container").each(function(i, item)
		// 	{
		// 		$(item).data("startHeight", $(item).height());
		// 	});
		// }

		// function initStartDragging()
		// {
		// 	dragInfo.dragging = true;
		// 	setStartHeight();
		// }

		// header.bind("mousedown", function(e)
		// {
		// 	dragInfo = { startY: e.pageY, dragging: false, diffY: 0 };
		// 	var dragTarget = $(e.currentTarget);
		// 	var direction;
		// 	var lastPageY;
		// 	var changeTarget = findPrevContentContainer(dragTarget);
		// 	if (!changeTarget)
		// 	{
		// 		return;
		// 	}
		// 	$(document).bind("mousemove.sliderItem", function(e)
		// 	{
		// 		dragInfo.diffY = e.pageY - dragInfo.startY;
		// 		if (lastPageY == null)
		// 		{
		// 			lastPageY = e.pageY;
		// 		}
		// 		if (Math.abs(dragInfo.diffY) > 2 && dragInfo.dragging == false)
		// 		{
		// 			initStartDragging();
		// 		}

		// 		if (dragInfo.dragging)
		// 		{
		// 			var newDirection = lastPageY == e.pageY ? direction : (lastPageY - e.pageY < 0 ? "down" : "up");
		// 			if (newDirection != direction)
		// 			{
		// 				changeTarget = findPrevContentContainer(dragTarget);
		// 				setStartHeight();
		// 				dragInfo.startY = e.pageY;
		// 				dragInfo.diffY = e.pageY - dragInfo.startY;
		// 			}
		// 			direction = newDirection;
		// 			lastPageY = e.pageY;

		// 			var newHeight = changeTarget.data("startHeight") + dragInfo.diffY;
		// 			var headAndFooterHeight = changeTarget.children(".item-content").outerHeight() - changeTarget.find(".pannel-item-content").outerHeight() - 1;

		// 			var maxHeight = headAndFooterHeight;
		// 			changeTarget.find(".pannel-item-content").children().each(function(index, item)
		// 			{
		// 				maxHeight += $(item).outerHeight();
		// 			});

		// 			var end = false;
		// 			if (newHeight < 0)
		// 			{
		// 				newHeight = 0;
		// 				end = true;
		// 			} else if (newHeight >= maxHeight)
		// 			{
		// 				newHeight = maxHeight;
		// 				end = true;
		// 				changeTarget.next().css("height", "auto").find(".pannel-item-content").css("height", "auto");
		// 			}
		// 			changeTarget.find(".pannel-item-content").height(newHeight - headAndFooterHeight);
		// 			changeTarget.height(newHeight);

		// 			var nextChangeTarget = findPrevContentContainer(changeTarget);
		// 			if (end && nextChangeTarget && Math.abs(dragInfo.diffY) > 0)
		// 			{
		// 				changeTarget = nextChangeTarget;
		// 				dragInfo.startY = e.pageY;
		// 			}
		// 		}
		// 	});

		// 	$(document).bind("mouseup.sliderItem", function()
		// 	{
		// 		$(document).unbind("mousemove.sliderItem");
		// 		$(document).unbind("mouseup.sliderItem");
		// 		setTimeout(function()
		// 		{
		// 			dragInfo.dragging = false;
		// 		}, 10);
		// 	});
		// });
	}
};