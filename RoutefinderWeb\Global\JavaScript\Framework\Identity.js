(function()
{
	createNamespace('TF').Identity = Identity;

	function Identity(authenticatingPointer, authenticationSuccessPointer)
	{
		this._authenticatingPointer = authenticatingPointer;
		this._authenticationSuccessPointer = authenticationSuccessPointer;
		this._token = null;
	};

	Identity.prototype = {

		_onAuthenticated: function(result, successCallback)
		{
			if (result && result.access_token)
			{
				this._token = result.access_token;
				successCallback();
				this._authenticationSuccessPointer();
			}
		},

		_onAuthenticationFailed: function()
		{
			this._token = null;
		},

		applyAuthentication: function(jqueryAjaxOptions)
		{
			// Add the authentication headers.
			if (jqueryAjaxOptions.headers == null)
			{
				jqueryAjaxOptions.headers = {};
			}

			//jqueryAjaxOptions.headers['Authorization'] = 'Bearer ' + this._token;
			jqueryAjaxOptions.headers['Authorization'] = 'M qweasdzxc';
		},

		authenticate: function(successCallback)
		{
			var internalSuccessCallback = function(result)
			{
				this._onAuthenticated(result, successCallback);
			}.bind(this);

			this._authenticatingPointer(this._token, internalSuccessCallback, $.proxy(this._onAuthenticationFailed, this));
		},

		isAuthenticated: function()
		{
			return true;
			//return this._token != null;
		},

		_something: function()
		{
			var authenticated = false;

			var uri = new URI(this._url);
			uri.removeQuery('cid');
			uri.addQuery('cid', customerId);

			var ajaxOptions = {
				async: false,
				successPointer: function(args)
				{
					authenticated = true;
				},
				url: uri.toString()
			};

			$.ajax(ajaxOptions);
			if (authenticated)
			{
				this._userId = userId;
				this._secret = window.btoa(password);
			}
		}
	};

	function ClientResponse(result)
	{
		this.active = result.active;
		this.attendance = result.attendance;
		this.client = result.client;
		this.mobile = result.mobile;
		this.url = result.url;
	};
})();