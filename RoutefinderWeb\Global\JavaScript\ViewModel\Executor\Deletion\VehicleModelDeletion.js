﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleModelDeletion = VehicleModelDeletion;

	function VehicleModelDeletion()
	{
		this.type = 'vehiclemodel';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleModelDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleModelDeletion.prototype.constructor = VehicleModelDeletion;

	VehicleModelDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();