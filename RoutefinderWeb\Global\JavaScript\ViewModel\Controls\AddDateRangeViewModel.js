(function()
{
	createNamespace('TF.Control').AddDateRangeViewModel = AddDateRangeViewModel;
	function AddDateRangeViewModel(options)
	{
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.trip = options;
		this.tripName = options.Name;
		this.tripType = TF.RoutingMap.RoutingPalette.RoutingDataModel.sessions[options.Session].name;
		this.obRecurringTypes = ko.observable(TF.Helper.TripDateRangeHelper.recurringTypes);
		this.obDateRanges = ko.observableArray([]);
		this.tripDateRanges = options.TripDateRangeSettings || [];
	}
	const MinDate = '1970-01-01';
	const MaxDate = '2100-01-01';

	AddDateRangeViewModel.prototype.init = function(viewModel, el)
	{
		this.$form = $(el);
		this.initValidate();
		this.calendar = new Calendar(this.$form.find(".calendar"), this.obDateRanges, this.trip);
		this.calendar.init();
		this.setInitValueForDataModel(this.tripDateRanges);
		this.nowDate = moment(utcToClientTimeZone(moment().utc()).format("YYYY-MM-DD"));
		this.obDateRanges(this.tripDateRanges.map(x => this.initDataModel(x)).sort((a, b) => this.getRankScore(a) - this.getRankScore(b)));
		this.calendar.setDates();
	}

	AddDateRangeViewModel.prototype.afterRender = function()
	{
		this.obDateRanges().forEach((item) =>
		{
			this.addValidator(item.index);
		})
	}

	AddDateRangeViewModel.prototype.getRankScore = function(range)
	{
		let score = 0;

		if ((!range.StartDate || moment(range.StartDate).isBefore(this.nowDate) || moment(range.StartDate).isSame(this.nowDate)) && (!range.EndDate || moment(range.EndDate).isAfter(this.nowDate) || moment(range.EndDate).isSame(this.nowDate)))
		{
			score = 0; // Current
		}
		else if (!range.EndDate || moment(range.EndDate).isAfter(this.nowDate))
		{
			score = 1; // Future
		}
		else
		{
			score = 2; // Expired
		}
		return score;
	}

	AddDateRangeViewModel.prototype.setInitValueForDataModel = function(tripDateRanges)
	{
		this.maxId = 0;
		let today = moment(moment().format("YYYY-MM-DD"));
		var firstExpired = false;
		tripDateRanges.forEach((dateRange) =>
		{
			let endDate = this.getEnd(dateRange.EndDate);
			dateRange.index = ++this.maxId;
			dateRange.expired = endDate.isBefore(today);
			if (dateRange.expired && !firstExpired)
			{
				firstExpired = true;
				dateRange.firstExpired = true;
			}
		})
	}

	AddDateRangeViewModel.prototype.initDataModel = function(data)
	{
		const dataModel = new TF.Helper.TripDateRangeHelper.TripDateRangeDataModel(data);
		this.calendar.listenDateRange(dataModel);
		return dataModel;
	}

	AddDateRangeViewModel.prototype.validateDates = function(value, validator, $field)
	{
		let index = $field.attr("name").split("_")[1];
		let dateRanges = this.obDateRanges();
		let dateRange = Enumerable.From(dateRanges).FirstOrDefault(null, c => c.startName === `startDate_${index}`);
		if (!dateRange)
		{
			return true;
		}

		if (!dateRange.StartDate() || !dateRange.EndDate())
		{
			return true;
		}

		let startDate1 = dateRange.StartDate() ? moment(dateRange.StartDate()) : moment(MinDate);
		let endDate1 = dateRange.EndDate() ? moment(dateRange.EndDate()) : moment(MaxDate);

		if (endDate1.isBefore(startDate1))
		{
			return {
				message: "End cannot be before Start.",
				valid: false
			}
		}

		let isOverlapping = dateRanges.filter(c => c.startName !== `startDate_${index}` && !(!c.StartDate() && !c.EndDate())).some(item =>
		{
			let startDate2 = item.StartDate() ? moment(item.StartDate()) : moment(MinDate);
			let endDate2 = item.EndDate() ? moment(item.EndDate()) : moment(MaxDate);
			return (endDate1.isSame(startDate2) || endDate1.isAfter(startDate2)) &&
				(startDate1.isSame(endDate2) || startDate1.isBefore(endDate2)) &&
				(startDate2.isBefore(endDate2) || startDate2.isSame(endDate2));
		});

		if (isOverlapping)
		{
			return {
				message: "Date Intervals cannot overlap.",
				valid: false
			}
		}

		return true;
	}

	AddDateRangeViewModel.prototype.initValidate = function()
	{
		let isValidating = false,
			updateErrors = (e, status) =>
			{
				// move end error to start, only for callback error
				let name = $(e).attr('name'),
					index = name.split("_")[1],
					$start = this.$form.find(`input[name="startDate_${index}"]`);

				if (!name.startsWith("endDate"))
				{
					return;
				}

				let startCallbackHelpBlock = $start.closest('.start-date').find(".help-block[data-bv-validator='callback']");
				let endCallbackHelpBlock = $(e).closest('.end-date').find(".help-block:visible");
				const errorMessage = endCallbackHelpBlock.html();
				if ((status === 'error' && errorMessage == "Required field") || status === 'success')
				{
					startCallbackHelpBlock.hide();
					return;
				}

				if (status === 'error')
				{
					startCallbackHelpBlock.show().html(errorMessage);
					endCallbackHelpBlock.hide();
				}
			};

		this.$form.bootstrapValidator(
			{
				excluded: [],
				live: "enabled",
				message: "This value is not valid",
				fields: {}
			})
			.on("success.field.bv", (e, data) =>
			{
				if (!isValidating)
				{
					isValidating = true;
					this.pageLevelViewModel.saveValidate(data.element);
					isValidating = false;
				}
				updateErrors(e.target, 'success');
			})
			.on('error.field.bv', e =>
			{
				updateErrors(e.target, 'error');
			});

		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	}

	AddDateRangeViewModel.prototype.addValidator = function(index)
	{
		this.$form.bootstrapValidator("addField", `startDate_${index}`, {
			trigger: "blur change",
			validators: {
				notEmpty:
				{
					message: "Required field"
				},
				callback: {
					message: "",
					callback: (value, validator, $field) => this.validateDates(value, validator, $field)
				}
			}
		})

		this.$form.bootstrapValidator("addField", `endDate_${index}`, {
			trigger: "blur change",
			validators: {
				notEmpty:
				{
					message: "Required field"
				},
				callback: {
					message: "",
					callback: (value, validator, $field) => this.validateDates(value, validator, $field)
				}
			}
		})
		this.$form.bootstrapValidator("addField", `recurEvery_${index}`, {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Required field"
				},
				regexp: {
					regexp: "^[0-9]*[1-9][0-9]*$",
					message: "The value must be >= 1."
				}
			}
		})
		this.$form.bootstrapValidator("addField", `weekday_${index}`, {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "Required field"
				},
			}
		})
	}

	AddDateRangeViewModel.prototype.removeValidator = function(index)
	{
		this.$form.bootstrapValidator("removeField", `startDate_${index}`);
		this.$form.bootstrapValidator("removeField", `endDate_${index}`);
		this.$form.bootstrapValidator("removeField", `recurEvery_${index}`);
		this.$form.bootstrapValidator("removeField", `weekday_${index}`);
	}

	AddDateRangeViewModel.prototype.addDateRange = function()
	{
		const minId = Math.min(...this.obDateRanges().map(x => x.Id));
		const index = ++this.maxId;
		const dateRange = this.initDataModel({ Id: minId <= 0 ? minId - 1 : 0, index: index, StartDate: null, EndDate: null, RecurBy: 0, RecurEvery: 1, Monday: true, Tuesday: true, Wednesday: true, Thursday: true, Friday: true });
		this.insertBeforeExpired(dateRange);
		this.addValidator(index);
		this.$form.find(`[name='startDate_${index}']`)[0].scrollIntoView({
			behavior: 'smooth',
			block: 'center'
		});
	}

	AddDateRangeViewModel.prototype.insertBeforeExpired = function(dateRange)
	{
		let position = 0, dateRanges = this.obDateRanges();
		for (var i = 0; i < dateRanges.length; i++)
		{
			position = i;
			if (dateRanges[i].firstExpired)
			{
				position--;
				break;
			}
		}
		dateRanges.splice(position + 1, 0, dateRange);
		this.obDateRanges(dateRanges);
	}

	AddDateRangeViewModel.prototype.deleteDateRange = function(item)
	{
		this.obDateRanges(this.obDateRanges().filter(x => x.Id !== item.Id));
		this.removeValidator(item.index);
		this.calendar.setDates();
	}

	AddDateRangeViewModel.prototype.apply = async function()
	{
		if (!await this.pageLevelViewModel.saveValidate())
		{
			return false;
		}

		const result = {
			TripDateRangeSettings: this.getDataModel(),
			TripDateRanges: this.calendar.getMergedTripDateRanges(this.trip.Id)
		};

		if (this.alertWhenNoValidDate(result))
		{
			return false;
		}

		return result;
	}

	AddDateRangeViewModel.prototype.alertWhenNoValidDate = function(tripDateRanges)
	{
		var inputButNoValidDates = tripDateRanges.length == 0 && this.obDateRanges().length > 0 && this.obDateRanges().filter(x => x.StartDate()).length > 0;
		if (inputButNoValidDates)
		{
			tf.promiseBootbox.alert("There are no valid date intervals, please check your settings.");
			return true;
		}
	}

	AddDateRangeViewModel.prototype.getDataModel = function()
	{
		const tripId = this.trip.Id;
		return this.obDateRanges().filter(x => x.StartDate() && x.EndDate()).map(dateRange =>
		{
			return dateRange.toData(tripId);
		})
	}

	AddDateRangeViewModel.prototype.getStart = function(start)
	{
		return start ? moment(start) : moment(MinDate);
	}

	AddDateRangeViewModel.prototype.getEnd = function(end)
	{
		return end ? moment(end) : moment(MaxDate);
	}
	class Calendar
	{
		constructor(element, dateRanges, trip)
		{
			this.element = element;
			this.kendoCalendar = null;
			this.dateRanges = dateRanges;
			this.trip = trip;
		}

		init()
		{
			this.element.kendoCalendar({
				month: {
					content: "<div class=\"date-text\">#= data.value #</div><div class=\"events-group\"><div class=\"events-point\"></div></div>"
				},
				width: "100%",
				height: "100%",
				selectable: "multiple",
				selectDates: [],
			});
			this.kendoCalendar = this.element.data().kendoCalendar;
		}

		setSelectDates(dates)
		{
			this.kendoCalendar.selectDates(dates);
		}

		getMergedTripDateRanges = function(tripId)
		{
			return TF.Helper.TripDateRangeHelper.getMergedTripDateRanges(this.trip, tripId, this.getSetDates());
		}

		getSetDates()
		{
			return TF.Helper.TripDateRangeHelper.getSetDates(this.dateRanges());
		}

		setDates()
		{
			this.setSelectDates(this.getSetDates());
		}

		listenDateRange(dateRange)
		{
			this.setDates = this.setDates.bind(this);
			dateRange.StartDate.subscribe(this.setDates);
			dateRange.EndDate.subscribe(this.setDates);
			dateRange.Weekdays.subscribe(this.setDates, this.element.closest(".add-date-range").data('bootstrapValidator'));
			dateRange.RecurEvery.subscribe(this.setDates);
			dateRange.RecurBy.subscribe(this.setDates);
		}
	}

})();
