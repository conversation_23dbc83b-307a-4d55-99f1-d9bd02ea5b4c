(function()
{
	createNamespace("TF.ImportAndMergeData").ImportAndMergeDataWizardModal = ImportAndMergeDataWizardModal;

	function ImportAndMergeDataWizardModal()
	{
		TF.Control.WizardModalViewModel.call(this, TF.ImportAndMergeData.ImportAndMergeDataWizard);
		var self = this;
		self.sizeCss = "modal-dialog-xlg";
		self.contentTemplate("modal/import data/ImportAndMergeDataWizard");
		self.buttonTemplate("modal/import data/ImportAndMergeDataWizardModalButtons");
		self.obPositiveButtonLabel("Import");
		self.obNextButtonLabel = self.wizard.nextButtonLabel;

		self.obCloseable = ko.pureComputed(function()
		{
			return !self.wizard.importing();
		});

		self.obNegativeButtonLabel = ko.pureComputed(function()
		{
			return self.wizard.imported() ? "Close" : "Cancel";
		});

		self.obDisableControl = ko.pureComputed(function()
		{
			return self.wizard.importing() || self.wizard.imported();
		});

		self.obPrevButtonDisable = ko.pureComputed(function()
		{
			return !self.wizard.canPrevious() || self.wizard.importing() || self.wizard.imported();
		});

		self.obNextButtonDisable = ko.pureComputed(function()
		{
			return !self.wizard.canNext() || self.wizard.importing() || self.wizard.imported() || self.wizard.obNextButtonDisable();
		});
	};

	ImportAndMergeDataWizardModal.prototype = Object.create(TF.Control.WizardModalViewModel.prototype);

	ImportAndMergeDataWizardModal.prototype.constructor = ImportAndMergeDataWizardModal;

	ImportAndMergeDataWizardModal.prototype.positiveClick = function(viewModel, e)
	{
		this.wizard.apply().then(result =>
		{
			if (result?.close)
			{
				this.hide();
			}
		});
	};

	ImportAndMergeDataWizardModal.prototype.negativeClick = function(viewModel, e, param)
	{
		if (this.wizard.imported())
		{
			this.hide();
			return;
		}

		TF.Control.WizardModalViewModel.prototype.negativeClick.call(this, viewModel, e, param);
	};
})();