(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.StringBoxWithCounter = StringBoxWithCounter;

	function StringBoxWithCounter(initialValue, attributes, disable, noWrap, delayChange, events)
	{
		this.keypress = this.keypress.bind(this);
		namespace.StringBox.call(this, initialValue, attributes, disable, noWrap, delayChange, events);
	}

	StringBoxWithCounter.prototype = Object.create(namespace.StringBox.prototype);

	StringBoxWithCounter.constructor = StringBoxWithCounter;

	StringBoxWithCounter.prototype.type = "StringWithCounter";

	StringBoxWithCounter.prototype.initialize = function()
	{
		var required = "", events = "";
		if (this.attributes && this.attributes[required])
		{
			required = " required ";
		}
		this.eventObject = {};
		for (var i in this.events)
		{
			this.eventObject[i] = this.events[i];
			events += i + ":eventObject." + i + ",";
		}
		let $element = $(`<div style="position:relative;">
			<textarea ${required} class="form-control" style="resize:vertical !important" data-tf-input-type='${this.type}' data-bind="value:obRawValue, disable:disable,css:{disabled:disable},event:{keypress:keypress,keyup:keyup, ${events}}" />
			<small style="position: absolute;top: -17px;right: 0px;display:none">0/${this.attributes.maxlength}</small>
		</div>`);
		this.$input = $element.find("textarea");

		this.applyAttribute(this.$input, this.attributes);
		ko.applyBindings(this, $element[0]);
		this.$element = $element;
	};

	/*
	Intialize text counter event
	 */
	StringBoxWithCounter.prototype.afterRender = function()
	{
		tf.udgHelper.bindCounterBoxEvent(this.$input, this.attributes.maxlength, this.$element.find("small"), true);
	}


	StringBoxWithCounter.prototype.keypress = function(viewModel, e)
	{
		if (e.keyCode == 13)
		{
			var rawValue = this.$input.val();
			this.obRawValue(this.convertValueType(rawValue));
		}
		return true;
	};
})();
