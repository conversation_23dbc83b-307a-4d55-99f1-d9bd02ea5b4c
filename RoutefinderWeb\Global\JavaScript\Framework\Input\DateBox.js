// Kendo Date Picker
(function()
{
	var namespace = window.createNamespace("TF.Input");
	namespace.DateBox = DateBox;

	var DEFAULT_MIN_DATE = "1/1/1753";
	var DEFAULT_MAX_DATE = "12/31/9999";

	function DateBox(initialValue, attributes, disable, noWrap, delayChange, element, allBindings)
	{
		var attr = {
			...attributes,
			minDate: DEFAULT_MIN_DATE,
			maxDate: DEFAULT_MAX_DATE,
			format: attributes.format ? attributes.format : 'MM/DD/YYYY',
			pickerFormat: "MM/dd/yyyy"
		}
		namespace.DateTimeBox.call(this, initialValue, attr, disable, noWrap, delayChange, element);
		this.$element.data('kendoDatePicker', this.kendoDateTimePicker);
	}
	DateBox.prototype = Object.create(namespace.DateTimeBox.prototype);

	DateBox.prototype.type = "Date";

	DateBox.prototype.constructor = DateBox;

	DateBox.prototype.formatString = "MM/DD/YYYY";

	DateBox.prototype.getInvalidCharacterRegex = function()
	{
		return /[^0-9A-Za-z|\/]/g;
	};
})();
