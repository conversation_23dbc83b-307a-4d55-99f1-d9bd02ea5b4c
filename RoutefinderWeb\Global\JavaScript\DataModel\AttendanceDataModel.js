﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.AttendanceDataModel = function(documentEntity)
	{
		namespace.BaseDataModel.call(this, documentEntity);
	}

	namespace.AttendanceDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.AttendanceDataModel.prototype.constructor = namespace.AttendanceDataModel;

	namespace.AttendanceDataModel.prototype.mapping = [
		{ from: "AttendanceId", default: 0 },
		{ from: "TripId", default: 0 },
		{ from: "TripStopId", default: 0 },
		{ from: "TripHistoryId", default: 0 },
		{ from: "AttendanceDate", default: 0 },
		{ from: "Info1", default: "" },
		{ from: "Info2", default: "" },
		{ from: "AttendanceValue", default: 0 },
		{ from: "AttendanceAltValue", default: 0 },
	];

})();
