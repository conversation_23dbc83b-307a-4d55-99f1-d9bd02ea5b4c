(function()
{
	createNamespace("TF.Control").SelectDatesViewModel = SelectDatesViewModel;

	function SelectDatesViewModel(options)
	{
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		this.startDate = ko.observable(null);
		this.endDate = ko.observable(null);
		this.days = ko.observable(null);
		this.type = this.getType(options.key);
		this.name = options.name;
	}

	SelectDatesViewModel.prototype.getType = function(type)
	{
		switch (type)
		{
			case "DateRangeOverlapWith":
				return "between";
			case "LastXDays":
			case "NextXDays":
				return "days";
			default:
				return "onOrOther";
		}
	};

	SelectDatesViewModel.prototype.afterRender = async function(el)
	{
		this.$form = $(el);
		this.initValidation();
	};

	SelectDatesViewModel.prototype.initValidation = function()
	{
		let validateDates = () =>
		{
			let startDate = moment(this.startDate()), endDate = moment(this.endDate());
			if (startDate > endDate)
			{
				return {
					valid: false,
					message: "start date/time <= end date/time",
				};
			}

			return true;
		};

		var validatorFields = {
			startDate: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					callback: {
						callback: validateDates
					}
				}
			},
			endDate: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					callback: {
						callback: validateDates
					}
				}
			}
		};

		let isValidating = false;
		this.$form.bootstrapValidator({
			excluded: [".data-bv-excluded"],
			fields: validatorFields,
			live: 'enabled'
		}).on('success.field.bv', (e, data) =>
		{
			if (!isValidating && this.pageLevelViewModel)
			{
				isValidating = true;
				this.pageLevelViewModel.saveValidate(data.element);
				isValidating = false;
			}
		});

		this.pageLevelViewModel.load(this.$form.data("bootstrapValidator"));
	};

	SelectDatesViewModel.prototype.apply = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(async (valid) =>
			{
				if (!valid)
				{
					return false;
				}
				var value = '';
				switch (this.type)
				{
					case "between":
						value = `${moment(this.startDate()).format("YYYY-MM-DDTHH:mm:ss.SSS")}|${moment(this.endDate()).format("YYYY-MM-DDTHH:mm:ss.SSS")}`;
						break;
					case "days":
						value = this.days();
						break;
					default:
						value = moment(this.startDate()).format("YYYY-MM-DDTHH:mm:ss.SSS");
						break;
				}
				return Promise.resolve(value);
			});

	};

	SelectDatesViewModel.prototype.dispose = function()
	{
		this.pageLevelViewModel.dispose();
	};
})();