﻿/* Popover Styles */

.popover-wrapper {
	position: absolute;
	margin-bottom: 13px;
	color: #333333;
	width: 450px;
	z-index: 11001;

	.enlarge {
		font-weight: bold;
		font-size: 13px;
	}

	.line-height {
		line-height: 30px;
	}

	.background-color-wihte {
		background-color: #FFFFFF;
	}

	.background-color-alt {
		background-color: #F9F9F9;
	}

	.header-group {
		border-bottom: 1px solid;
		min-height: 45px;

		&.min-height-0 {
			min-height: 0px;
		}

		.header {
			background-color: #666666;
			color: #ffffff;
			font-weight: bold;
			height: 26px;
			line-height: 26px;
			padding-left: 10px;
		}

		.title {
			margin: 0;
			padding: 0px 0px 0px 8px;
			font-size: 10pt;
			height: 25px;
			line-height: 25px;
			float: left;

			.text {
				font-weight: bold;
			}
		}

		.iconbutton {
			padding: 12pt !important;
		}

		.right-iconbutton {
			float: right;
			margin-right: 5px;
			background-color: black;
			display: block;
			height: 16px;
			width: 16px;
			background-repeat: no-repeat;
			background-position: center;
			background-size: 16px;
			cursor: pointer;
		}

		.subtitle {
			font-size: small;
			color: gray;
		}

		.buttons {
			margin: 6px 0 -13px 0;
			padding-left: 0px;

			.button {
				padding: 5px;
				font-size: 12pt;
				opacity: 0.6;
				margin: 4px 4px 4px 0;

				&:hover {
					cursor: pointer;
					border: none;
				}
			}
		}
	}

	.header-group.tripstop-student {
		height: 25px;
		min-height: 25px;

		.title {
			padding: 0px 0px 0px 8px;
			font-size: 10pt;
			height: 25px;
			line-height: 25px;
			float: left;

			.text {
				font-weight: bold;
				float: left;
				max-width: 300px;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				display: table-cell;
			}
		}

		.buttons {
			margin: 0;
			padding: 0;
			float: right;

			.button {
				padding: 0 !important;
				margin: 4px 4px 4px 0;
			}
		}
	}

	.content-group {
		margin-top: 5px;

		label {
			color: #333333;
			font-weight: bold;
		}

		.value {
			color: grey;
			font-size: small;
		}

		.note {
			/*margin-top: 20px;*/
		}

		.week-day-content {
			margin: 2px 0;

			.week-day {
				float: left;
			}

			.stop-time {
				float: right;
				cursor: pointer;
			}
		}

		.student-group {
			padding-top: 5px;
			min-height: 40px;
			border-bottom: 1px solid #BDBDBD;

			.title {
				line-height: 30px;
			}

			.exception {
				color: #B6B6B6;
			}

			.shuttles-color {
				background-color: #FFCC00;
				width: 8px;
				height: 40px;
				float: left;
				margin-left: -15px;
				margin-top: -5px;
			}
		}

		.student-group.tripstop-student {
			padding: 2px 8px 3px 8px;
			height: 35px;
			min-height: 35px;
			border-bottom: 1px solid #BDBDBD;

			.image {
				padding: 0;
				float: left;
			}

			.title {
				float: left;
				font-size: 9pt;
				padding: 0 5px 0 5px;

				.student-name {
					max-width: 210px;
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
					display: table-cell;
				}
			}

			.info {
				float: right;
				padding: 0;

				.text-right {
					line-height: 13px;
					font-size: 7pt;

					.school-name {
						max-width: 115px;
						text-overflow: ellipsis;
						overflow: hidden;
						white-space: nowrap;
						display: table-cell;
						text-align: left;
					}
				}
			}
		}

		.tripstop-group {
			line-height: 35px;
			min-height: 35px;
			border-bottom: 1px solid #F2F2F2;

			.right-text {
				display: block;
			}

			.iconbutton {
				display: none;
			}

			.button {
				padding: 12px;
				margin-top: 5px;
			}

			&:hover {
				background-color: #DDF4FF;

				.right-text {
					display: none;
				}

				.iconbutton {
					display: inline-block;
				}
			}
		}

		.box-description {
			height: 50px;
		}

		.sub-description-normal {
			color: #B6B6B6;
		}
	}

	.bottom-group {
		.bottom {
			background-color: #CCCCCC;
			height: 26px;
			line-height: 26px;
			padding-left: 10px;

			.trip-info-note {
				color: #333;

				&.info {
					opacity: 1;
				}
			}
		}
	}

	.bottom-group.tripstop-student {
		.bottom {
			font-size: 9pt;
		}
	}

	.max-height {
		max-height: 320px;
		overflow: auto;
	}

	.image {
		width: 30px;
		height: 30px;
		border-radius: 50%;
	}
}

.arrow_box_top {
	position: relative;
	background: #ffffff;
	border: 1px solid #000000;
}

.arrow_box_top:after, .arrow_box_top:before {
	bottom: 100%;
	left: 80%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.arrow_box_top:after {
	border-color: rgba(255, 255, 255, 0);
	border-bottom-color: #ffffff;
	border-width: 10px;
	margin-left: -10px;
}

.arrow_box_top:before {
	border-color: rgba(0, 0, 0, 0);
	border-bottom-color: #000000;
	border-width: 11px;
	margin-left: -11px;
}
