﻿(function()
{
	createNamespace("TF.Fragment").EditTripSymbolViewModel = EditTripSymbolViewModel;

	function EditTripSymbolViewModel()
	{
		var self = this;

		this.tripStyleData = [
			{ text: "STYLE_SOLID", value: "solid" },
			{ text: "STYLE_DASH", value: "dash" },
			{ text: "STYLE_DASHDOT", value: "dashdot" },
			{ text: "STYLE_DASHDOTDOT", value: "longdashdotdot" },
			{ text: "STYLE_DOT", value: "dot" },
			{ text: "STYLE_LONGDASH", value: "longdash" },
			{ text: "STYLE_LONGDASHDOT", value: "longdashdot" },
			{ text: "STYLE_NULL", value: "none" },
			{ text: "STYLE_SHORTDASH", value: "shortdash" },
			{ text: "STYLE_SHORTDASHDOT", value: "shortdashdot" },
			{ text: "STYLE_SHORTDASHDOTDOT", value: "shortdashdotdot" },
			{ text: "STYLE_SHORTDOT", value: "shortdot" }
		];

		var tripSymbolData = new TF.DataModel.TripSymbolDataModel();
		this.obEntityDataModel = ko.observable(tripSymbolData);

		this.tripColor = self.obEntityDataModel().color();
		this.tripStyle = self.obEntityDataModel().style();
		this.tripWidth = self.obEntityDataModel().width();
		this.tripAlpha = self.obEntityDataModel().alpha();

		this.tripStyleChange = function()
		{
			self.tripStyle = self.tripStyleObjects[0].value;
		};

		this.tripWidthChange = function()
		{
			self.tripWidth = self.obEntityDataModel().width();
		};

		this.tripAlphaChange = function()
		{
			self.tripAlpha = self.tripAlphaObjects[0].value;
		};
	};

	EditTripSymbolViewModel.prototype.init = function()
	{
		var self = this;

		this.colorPickerObjects = $("#colorpicker");
		this.tripStyleObjects = $("#tripStyle");
		this.tripAlphaObjects = $("#tripTransparency");

		this.colorPickerObjects.kendoColorPicker({
			palette: "websafe"
		});

		this.tripStyleObjects.kendoDropDownList({
			dataTextField: "text",
			dataValueField: "value",
			dataSource: self.tripStyleData,
			index: 0,
			valueTemplate: "<img src='../../global/img/map/linestyle/#: text #.png' />",
			template: "<span><img src='../../global/img/map/linestyle/#: text #.png' /></span>",
			change: self.tripStyleChange
		});

		this.tripAlphaObjects.kendoSlider({
			precision: 1,
			smallStep: 0.1,
			largeStep: 0.5,
			min: 0,
			max: 1,
			value: self.obEntityDataModel().alpha(),
			showButton: false,
			tooltip: {
				format: "{0:#.#}"
			},
			dragHandleTitle: "drag to change the alpha.",
			change: self.tripAlphaChange
		});
	};

	EditTripSymbolViewModel.prototype.ColorPickerChange = function()
	{
		this.tripColor = this.colorPickerObjects[0].value.toUpperCase();
	};

})();