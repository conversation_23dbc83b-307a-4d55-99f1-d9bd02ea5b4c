(function()
{
	createNamespace("TF.Modal.Support").RestoreUpdateMapModalViewModel = RestoreUpdateMapModalViewModel;

	function RestoreUpdateMapModalViewModel(updateType)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-sm";
		const updateTypeLabel = { "Cover": { "Title": "New Maps", "Label": "Create" }, "Retain": { "Title": "Update Maps", "Label": "Update" }, "Restore": { "Title": "Restore Maps", "Label": "Restore" } }
		this.title(updateTypeLabel[updateType].Title);
		this.contentTemplate('Navigation/RestoreMapData');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel(updateTypeLabel[updateType].Label);
		this.file = ko.observable();
		this.updateType = updateType
	};

	RestoreUpdateMapModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	RestoreUpdateMapModalViewModel.prototype.constructor = RestoreUpdateMapModalViewModel;

	RestoreUpdateMapModalViewModel.prototype.positiveClick = function()
	{
		if (!this.file() || this.file().length == 0)
		{
			tf.promiseBootbox.alert("Please select a valid map zip file.");
			return;
		}

		tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", "streets", "count"),
		{
			paramData:
			{
				"databaseId": tf.datasourceManager.databaseId
			},
			data: 
			{
				filterSet:
				{
					FilterItems: [{
						FieldName: 'Lock',
						Operator: 'EqualTo',
						TypeHint: 'String',
						Value: 'true'
					}], FilterSets: [], LogicalOperator: "and"
				},
			}
		}).then(response =>
		{
			var count = response.Items[0];
			if (count > 1000)
			{
				return tf.promiseBootbox.dialog(
				{
					closeButton: true,
					title: "Confirmation",
					message: "There are more than 1,000 streets locked. Many locked streets will slow the Map Update process. Are you sure you want to continue?",
					buttons:
					{
						save:
						{
							label: "Yes",
							className: "btn tf-btn-black btn-sm"
						},
						cancel:
						{
							label: "No",
							className: "btn btn-link btn-sm"
						}
					}
				})
				.then(function(operation)
				{
					return operation.toLowerCase() === "save";
				});
			}
			return true;
		}).then(yesOrNo =>
		{
			if (yesOrNo)
			{
				tf.loadingIndicator.setSubtitle("Uploading maps...");
				tf.loadingIndicator.show();
				const updateMapTool = new TF.Map.RestoreAndUpdateMapTool();
				updateMapTool.updateMapWithAllProcesses(this.file(), this.updateType).then((ans) =>
				{
					if (ans == "success")
					{
						this.positiveClose();
					}
				});
			}
			else
			{
				this.positiveClose();
			}
		});
	};
})();