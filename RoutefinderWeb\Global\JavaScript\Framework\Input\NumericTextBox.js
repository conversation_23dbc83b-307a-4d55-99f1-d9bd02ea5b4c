ko.bindingHandlers.numericTextBox = {
	init: function(element, valueAccessor, allBindings, viewModel, bindingContext)
	{
		let defaults = {
			min: 0,
			step: 1,
			decimals: 0,
			format: "0.",
			disable: false,
		};
		let option = $.extend(defaults, valueAccessor());
		let numberBox = $(element).kendoNumericTextBox(option).data("kendoNumericTextBox");
		ko.bindingHandlers.numericTextBox.setValue(element, valueAccessor);

		numberBox.bind("change", () =>
		{
			valueAccessor().value(numberBox.value());
		});
		if (option.dynamic)
		{
			numberBox.bind("spin", () =>
			{
				valueAccessor().value(numberBox.value());
			});
		}
	},
	update: function(element, valueAccessor, allBindings, viewModel, bindingContext)
	{
		ko.bindingHandlers.numericTextBox.setValue(element, valueAccessor);
	},
	setValue: function(element, valueAccessor)
	{
		let kendoNumericTextBox = $(element).data("kendoNumericTextBox");
		let disable = valueAccessor().disable, disableType = typeof disable;
		kendoNumericTextBox.enable(disableType == "function" ? !disable() : (disableType == "boolean" ? !disable : true));
		kendoNumericTextBox.value(valueAccessor().value());
		kendoNumericTextBox.focus();
	}
};
