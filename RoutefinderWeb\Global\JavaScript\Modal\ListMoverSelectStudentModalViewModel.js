(function()
{
	createNamespace('TF.Modal').ListMoverSelectStudentModalViewModel = ListMoverSelectStudentModalViewModel;

	function ListMoverSelectStudentModalViewModel(selectedData, options, needLoadTimeColumn, needFilterCheckBox)
	{
		//options.mustSelect = false;
		options.showRemoveColumnButton = true;
		options.showRawImageColumn = options.showRawImageColumn === false ? false : true;
		selectedData = selectedData.map(function(item) { return item; });
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverSelectStudentViewModel = new TF.Control.ListMoverSelectStudentViewModel(selectedData, options, needLoadTimeColumn, needFilterCheckBox);
		this.data(this.ListMoverSelectStudentViewModel);
	}

	ListMoverSelectStudentModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectStudentModalViewModel.prototype.constructor = ListMoverSelectStudentModalViewModel;

	ListMoverSelectStudentModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverSelectStudentViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ListMoverSelectStudentModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverSelectStudentViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};

})();
