(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.StreetDataModel = function(recordEntity)
	{
		namespace.BaseDataModel.call(this, recordEntity);
	}

	namespace.StreetDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.StreetDataModel.prototype.constructor = namespace.StreetDataModel;

	namespace.StreetDataModel.prototype.mapping = [
		{ "from": "OBJECTID", "default": 0 },
		{ "from": "Street", "default": "" },
		{ "from": "Fromleft", "default": "" },
		{ "from": "Toleft", "default": "" },
		{ "from": "Fromright", "default": "" },
		{ "from": "Toright", "default": "" },
		{ "from": "RoadClass", "default": "" },
		{ "from": "Speedleft", "default": "" },
		{ "from": "Speedright", "default": "" },
		{ "from": "TraversableByVehicle", "default": "Address Point" },
		{ "from": "TraversableByWalkers", "default": null },
		{ "from": "ProhibitCrosser", "default": null },
		{ "from": "Lock", "default": "" },
		{ "from": "FromElevation", "default": "" },
		{ "from": "ToElevation", "default": null },
		{ "from": "HeightClearance", "default": null },
		{ "from": "PostedLeft", "default": null },
		{ "from": "PostedRight", "default": null },
		{ "from": "WeightLimit", "default": null },
		{ "from": "LastUpdatedBy", "default": 0 },
		{ "from": "LastUpdated", "default": null },
		{ "from": "CreatedBy", "default": 0 },
		{ "from": "CreatedOn", "default": null },
	];
})();