﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.VehicleFuelTypeDeletion = VehicleFuelTypeDeletion;

	function VehicleFuelTypeDeletion()
	{
		this.type = 'vehiclefueltype';
		namespace.BaseDeletion.apply(this, arguments);
	}

	VehicleFuelTypeDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	VehicleFuelTypeDeletion.prototype.constructor = VehicleFuelTypeDeletion;

	VehicleFuelTypeDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		return Promise.all([]).then(function()
		{
			return associatedDatas;
		});
	}
})();