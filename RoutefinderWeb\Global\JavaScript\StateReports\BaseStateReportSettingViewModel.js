(function()
{
	createNamespace('TF.Control').BaseStateReportSettingViewModel = BaseStateReportSettingViewModel;

	function BaseStateReportSettingViewModel(options)
	{
		const { dataType, customFields } = options;

		this.dataType = dataType;
		this.customFields = customFields || {};
		this.specifyRecordsType = ko.observable(TF.Enums.SpecifyRecordsType.AllRecords);
		this.filterId = ko.observable();
		this.filters = ko.observableArray([]);
		this.records = ko.observableArray([]);

		this.shouldSelectRecords = ko.computed(() =>
		{
			return this.specifyRecordsType() == TF.Enums.SpecifyRecordsType.SpecificRecords;
		});

		this.shouldSelectFilter = ko.computed(() =>
		{
			return this.specifyRecordsType() == TF.Enums.SpecifyRecordsType.Filter;
		});

		this.specifyRecordsType.subscribe(() =>
		{
			if (this.specifyRecordsType() != TF.Enums.SpecifyRecordsType.SpecificRecords)
			{
				this.records([]);
				this.clearValidatorField('records');
			}

			if (this.specifyRecordsType() != TF.Enums.SpecifyRecordsType.Filter)
			{
				this.filterId(null);
				this.clearValidatorField('filterId');
			}
		});

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
	}

	BaseStateReportSettingViewModel.prototype.constructor = BaseStateReportSettingViewModel;

	BaseStateReportSettingViewModel.prototype.afterRender = function(el)
	{
		this.element = $(el);

		this.initFilterData();
		this.initValidator();

		this.pageLevelViewModel.load(this.element.data("bootstrapValidator"));
	};

	BaseStateReportSettingViewModel.prototype.initFilterData = function()
	{
		const dataTypeId = tf.dataTypeHelper.getId(this.dataType);
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"),
			{
				paramData: {
					"@filter": `(eq(dbid,${tf.datasourceManager.databaseId})|isnull(dbid,))&eq(datatypeId,${dataTypeId})`,
					"@relationships": "OmittedRecord"
				}
			})
			.then(data =>
			{
				let filterList = data.Items;
				filterList = data.Items.filter(i => i.IsValid);
				this.filters(filterList);
			});
	};

	BaseStateReportSettingViewModel.prototype.clearValidatorField = function(field)
	{
		let validator = this.element.data("bootstrapValidator");
		if (!validator)
		{
			return;
		}

		let excluded = validator.options.excluded;
		validator.options.excluded = null;
		validator.updateStatus(field, 'NOT_VALIDATED');
		validator.options.excluded = excluded;
	};

	BaseStateReportSettingViewModel.prototype.initValidator = function()
	{
		var fields = $.extend(this.customFields, {
			filterId: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					}
				}
			},
			records: {
				trigger: "change",
				validators: {
					notEmpty: {
						message: "At least one record must be selected"
					}
				}
			}
		});

		this.element.bootstrapValidator({
			excluded: [':disabled'],
			live: 'enabled',
			fields: fields
		});
	};

	// needs to be overriden
	BaseStateReportSettingViewModel.prototype.apply = function()
	{

	};

	BaseStateReportSettingViewModel.prototype.selectRecordClick = function()
	{
		tf.modalManager.showModal(
			new TF.Modal.ListMoverSelectRecordControlModalViewModel(
				this.records(),
				{
					title: "Select Records ",
					type: this.dataType,
					dataSource: tf.datasourceManager.databaseId,
					showRemoveColumnButton: true,
					allowApplyZeroRecord: true,
					availableTitle: 'Available',
					selectedTitle: 'Selected',
					mustSelect: true,
					forceFitColumns: true,
					enableColumnReorder: true
				}
			)
		).then(records =>
		{
			if (!Array.isArray(records))
			{
				return;
			}

			this.records(records);
		});
	};

	BaseStateReportSettingViewModel.prototype.dispose = function()
	{
		if (this.pageLevelViewModel)
		{
			this.pageLevelViewModel.dispose();
			this.pageLevelViewModel = null;
		}
	}
})()