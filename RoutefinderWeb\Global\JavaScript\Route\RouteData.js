﻿(function()
{
	//createNamespace("TF.Route").RouteData = RouteData;
	//function RouteData(hash, documentType, documentData)
	//{
	//	this.hash = hash;
	//	this.documentType = documentType;
	//	this.documentData = documentData;
	//};

	//createNamespace("TF.Route").BaseRouteData = BaseRouteData;
	//function BaseRouteData(hash)
	//{
	//	this.hash = hash;
	//}


	//createNamespace("TF.Route").MapRouteData = MapRouteData;
	//function MapRouteData(hash, mapObjects)
	//{
	//	TF.Route.BaseRouteData.call(this, hash);
	//	this.mapObjects = mapObjects;
	//}


	//createNamespace("TF.Route").GridRouteData = GridRouteData;
	//function GridRouteData(hash, gridType)
	//{
	//	TF.Route.BaseRouteData.call(this, hash);
	//	this.gridType = gridType;
	//}


	//createNamespace("TF.Route").DataEntryRouteData = DataEntryRouteData;
	//function DataEntryRouteData(hash, dataEntryObjects)
	//{
	//	TF.Route.BaseRouteData.call(this, hash);
	//	this.dataEntryObjects = dataEntryObjects;
	//}
})();