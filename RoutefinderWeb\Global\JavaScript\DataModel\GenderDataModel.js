﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.GenderDataModel = function(GenderDataModel)
	{
		namespace.BaseDataModel.call(this, GenderDataModel);
	}

	namespace.GenderDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.GenderDataModel.prototype.constructor = namespace.GenderDataModel;

	namespace.GenderDataModel.prototype.mapping = [
		{ from: "ID", to: "id", default: 0 },
		{ from: "Code", to: "name", default: "" },
		{ from: "Name", to: "description", default: "" },
		{ from: "IsSystemField", default: false }
	];
})();