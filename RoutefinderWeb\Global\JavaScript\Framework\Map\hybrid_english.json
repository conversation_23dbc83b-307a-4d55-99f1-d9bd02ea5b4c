{"layers": [{"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-opacity": {"base": 1.0, "stops": [[12, 0.4], [16, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 3.3], [17, 5.33333], [18, 5.33333]]}}, "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Railroad", "type": "line", "id": "Railroad/2"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#dbdbd0", "line-opacity": {"base": 1.0, "stops": [[12, 0.4], [16, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 1.3], [17, 2.7], [18, 2.7]]}}, "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Railroad", "type": "line", "id": "Railroad/1"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "icon-padding": 1, "icon-image": "Railroad/0", "icon-allow-overlap": true, "icon-rotation-alignment": "map", "symbol-spacing": 20.0}, "paint": {}, "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Railroad", "type": "symbol", "id": "Railroad/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-opacity": {"base": 1.0, "stops": [[12, 0.4], [16, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 3.3], [17, 5.33333], [18, 5.33333]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Ferry", "type": "line", "id": "Ferry/Rail ferry/2"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#dbdbd0", "line-opacity": {"base": 1.0, "stops": [[12, 0.4], [16, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 1.3], [17, 2.7], [18, 2.7]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Ferry", "type": "line", "id": "Ferry/Rail ferry/1"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "icon-padding": 1, "icon-image": "Railroad/0", "icon-allow-overlap": true, "icon-rotation-alignment": "map", "symbol-spacing": 20.0}, "paint": {}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Ferry", "type": "symbol", "id": "Ferry/Rail ferry/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-opacity": {"base": 1.0, "stops": [[13, 0.25], [17, 0.2]]}, "line-width": {"base": 1.2, "stops": [[13, 3.33], [15, 4], [16, 4.67], [17, 7.3], [18, 10]]}}, "filter": ["all", ["==", "_symbol", 10], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road", "type": "line", "id": "Road/4WD/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-opacity": {"base": 1.0, "stops": [[13, 0.25], [17, 0.2]]}, "line-width": {"base": 1.2, "stops": [[13, 3.33], [15, 4], [16, 4.67], [17, 7.3], [18, 10]]}}, "filter": ["all", ["==", "_symbol", 8], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road", "type": "line", "id": "Road/Service/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-opacity": {"base": 1.0, "stops": [[13, 0.25], [17, 0.2]]}, "line-width": {"base": 1.2, "stops": [[13, 3.33], [15, 4], [16, 4.67], [17, 7.3], [18, 10]]}}, "filter": ["all", ["==", "_symbol", 7], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road", "type": "line", "id": "Road/Local/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#fdfdfd", "line-opacity": {"base": 1.0, "stops": [[13, 0.25], [17, 0.2]]}, "line-width": {"base": 1.2, "stops": [[13, 1.33], [15, 2], [16, 2.67], [17, 5.3], [18, 8]]}}, "filter": ["all", ["==", "_symbol", 10], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road", "type": "line", "id": "Road/4WD/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#FDFDFD", "line-opacity": {"base": 1.0, "stops": [[13, 0.25], [17, 0.2]]}, "line-width": {"base": 1.2, "stops": [[13, 1.33], [15, 2], [16, 2.67], [17, 5.3], [18, 8]]}}, "filter": ["all", ["==", "_symbol", 8], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road", "type": "line", "id": "Road/Service/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#FDFDFD", "line-opacity": {"base": 1.0, "stops": [[13, 0.25], [17, 0.2]]}, "line-width": {"base": 1.2, "stops": [[13, 1.33], [15, 2], [16, 2.67], [17, 5.3], [18, 8]]}}, "filter": ["all", ["==", "_symbol", 7], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road", "type": "line", "id": "Road/Local/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 5], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Minor/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 6], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Minor, ramp or traffic circle/1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[10, "#ffffff"], [12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[10, 0.2], [11, 0.3], [12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[10, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 5], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Minor/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[10, "#ffffff"], [12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[10, 0.2], [11, 0.3], [12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[10, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 6], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Minor, ramp or traffic circle/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 3], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Major/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 4], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Major, ramp or traffic circle/1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[10, "#ffffff"], [12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[10, 0.2], [11, 0.3], [12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[10, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 3], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Major/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[10, "#ffffff"], [12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[10, 0.2], [11, 0.3], [12, 0.45], [13, 0.35], [17, 0.3]]}, "line-width": {"base": 1.2, "stops": [[10, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 4], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road", "type": "line", "id": "Road/Major, ramp or traffic circle/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#4d0800", "line-opacity": {"base": 1.0, "stops": [[6, 0.15], [9, 0.2], [12, 0.23], [13, 0.35], [17, 0.25]]}, "line-width": {"base": 1.2, "stops": [[6, 3.33], [13, 4], [15, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Road", "type": "line", "id": "Road/Highway/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#4d0800", "line-opacity": {"base": 1.0, "stops": [[6, 0.15], [9, 0.2], [12, 0.23], [13, 0.35], [17, 0.25]]}, "line-width": {"base": 1.2, "stops": [[6, 3.33], [13, 4], [15, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Road", "type": "line", "id": "Road/Freeway Motorway/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#4d0800", "line-opacity": {"base": 1.0, "stops": [[6, 0.1], [13, 0.12], [17, 0.25]]}, "line-width": {"base": 1.2, "stops": [[6, 3.33], [13, 4], [15, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Road", "type": "line", "id": "Road/Freeway Motorway Highway, ramp or traffic circle/1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[6, "#ffb994"], [10, "#ff9961"]]}, "line-opacity": {"base": 1.0, "stops": [[6, 0.15], [9, 0.2], [12, 0.23], [13, 0.35], [17, 0.25]]}, "line-width": {"base": 1.2, "stops": [[6, 1.33], [13, 2], [15, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Road", "type": "line", "id": "Road/Highway/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[6, "#ffb994"], [10, "#ff9961"]]}, "line-opacity": {"base": 1.0, "stops": [[6, 0.15], [9, 0.2], [12, 0.23], [13, 0.35], [17, 0.25]]}, "line-width": {"base": 1.2, "stops": [[6, 1.33], [13, 2], [15, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Road", "type": "line", "id": "Road/Freeway Motorway/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[6, "#ffb994"], [10, "#ff9961"]]}, "line-opacity": {"base": 1.0, "stops": [[6, 0.1], [13, 0.12], [17, 0.25]]}, "line-width": {"base": 1.2, "stops": [[6, 1.33], [13, 2], [15, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Road", "type": "line", "id": "Road/Freeway Motorway Highway, ramp or traffic circle/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 5], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Minor/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 6], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Minor, ramp or traffic circle/1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 5], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Minor/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 6], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Minor, ramp or traffic circle/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 3], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Major/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#191919", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [14, 4], [15, 4.67], [16, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 4], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Major, ramp or traffic circle/1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 3], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Major/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"base": 1.2, "stops": [[12, "#fffaf0"], [13, "#ffdb8c"]]}, "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [14, 2], [15, 2.67], [16, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 4], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Major, ramp or traffic circle/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#4d0800", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [13, 4], [15, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 8, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Highway/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#4d0800", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [13, 4], [15, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 8, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Freeway Motorway/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#4d0800", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 3.33], [13, 4], [15, 6], [17, 10]]}}, "filter": ["all", ["==", "_symbol", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Freeway Motorway Highway, ramp or traffic circle/1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": "#ff9961", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [13, 2], [15, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 8, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Highway/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": "#ff9961", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [13, 2], [15, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 8, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Freeway Motorway/0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": "#ff9961", "line-opacity": {"base": 1.0, "stops": [[12, 0.35], [13, 0.25], [17, 0.15]]}, "line-width": {"base": 1.2, "stops": [[12, 1.33], [13, 2], [15, 4], [17, 8]]}}, "filter": ["all", ["==", "_symbol", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel", "type": "line", "id": "Road tunnel/Freeway Motorway Highway, ramp or traffic circle/0"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-dasharray": [6.0, 3.0], "line-opacity": 0.55, "line-width": {"base": 1.2, "stops": [[10, 1.6], [11, 2.5], [18, 4]]}}, "filter": ["all", ["==", "_symbol", 8], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Disputed admin2"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#000000", "line-dasharray": [6.0, 3.0], "line-opacity": {"base": 1.0, "stops": [[3, 0.7], [10, 0.55]]}, "line-width": {"base": 1.2, "stops": [[3, 1.6], [11, 2.5], [18, 4]]}}, "filter": ["all", ["==", "_symbol", 7], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Disputed admin1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": {"stops": [[1, "#ffffff"], [2, "#000000"]]}, "line-dasharray": [6.0, 3.0], "line-opacity": {"base": 1.0, "stops": [[1, 0.55], [4, 0.7], [10, 0.55]]}, "line-width": {"base": 1.2, "stops": [[1, 1.33], [2, 1.6], [11, 2.5], [18, 4]]}}, "filter": ["all", ["==", "_symbol", 6], ["!in", "Viz", 3], ["!in", "DisputeID", 8, 16, 90, 96, 0]], "source": "<PERSON><PERSON>", "minzoom": 1, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Disputed admin0"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": {"stops": [[2, "#000000"], [10, "#4e4e4e"], [11, "#cccccc"]]}, "line-opacity": {"base": 1.0, "stops": [[2, 0.55], [4, 0.7], [10, 0.55]]}, "line-width": {"base": 1.2, "stops": [[2, 3.33], [6, 5], [18, 12]]}}, "filter": ["all", ["==", "_symbol", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Admin0/1"}, {"layout": {"line-join": "round"}, "paint": {"line-color": "#ffffff", "line-dasharray": [2, 2], "line-opacity": 0.55, "line-width": {"base": 1.2, "stops": [[10, 1.5], [13, 2], [18, 2.75]]}}, "filter": ["all", ["==", "_symbol", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Admin2"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": "#ffffff", "line-dasharray": [4, 3], "line-opacity": {"base": 1.0, "stops": [[3, 0.55], [4, 0.7], [10, 0.55]]}, "line-width": {"base": 1.0, "stops": [[3, 1.33], [6, 1.75], [18, 4]]}}, "filter": ["all", ["==", "_symbol", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Admin1"}, {"layout": {"line-cap": "round", "line-join": "round"}, "paint": {"line-color": "#ffffff", "line-opacity": {"base": 1.0, "stops": [[1, 0.55], [4, 0.7], [10, 0.55]]}, "line-width": {"base": 1.2, "stops": [[1, 1.33], [6, 2], [18, 4]]}}, "filter": ["all", ["==", "_symbol", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 1, "source-layer": "Boundary line", "type": "line", "id": "Boundary line/Admin0/0"}, {"layout": {"text-letter-spacing": 0.01, "icon-allow-overlap": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Water point", "text-field": "{_name_global}", "text-size": {"stops": [[9, 7], [15, 11.3]]}, "text-max-width": 5}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 3], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Water point", "type": "symbol", "id": "Water point/Stream or river"}, {"layout": {"text-letter-spacing": 0.01, "icon-allow-overlap": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Water point", "text-field": "{_name_global}", "text-size": {"stops": [[9, 7], [15, 11.3]]}, "text-max-width": 5}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 2], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Water point", "type": "symbol", "id": "Water point/Lake or reservoir"}, {"layout": {"text-letter-spacing": 0.15, "icon-allow-overlap": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Water point", "text-field": "{_name_global}", "text-size": {"stops": [[9, 7], [15, 12]]}, "text-max-width": 5}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Water point", "type": "symbol", "id": "Water point/Bay or inlet"}, {"layout": {"text-letter-spacing": 0.1, "icon-allow-overlap": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Water point", "text-field": "{_name_global}", "text-size": 15, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Water point", "type": "symbol", "id": "Water point/Sea or ocean"}, {"layout": {"text-letter-spacing": 0.01, "icon-allow-overlap": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Water point", "text-field": "{_name_global}", "text-size": {"stops": [[11, 7], [15, 10]]}, "text-max-width": 5}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 4], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water point", "type": "symbol", "id": "Water point/Canal or ditch"}, {"layout": {"text-letter-spacing": 0.05, "icon-allow-overlap": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Water point", "text-field": "{_name_global}", "text-size": {"stops": [[11, 7], [15, 11.3]]}, "text-max-width": 5}, "paint": {"text-color": "#d9d1ba", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 7], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water point", "type": "symbol", "id": "Water point/Island"}, {"layout": {"text-letter-spacing": 0.07, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 1, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 10, "text-max-width": 8}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 14, "source-layer": "Water line/label", "type": "symbol", "id": "Water line/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[6, 10], [7, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#d9fff2", "text-halo-width": 1, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Marine park/label", "type": "symbol", "id": "Marine park/label/Default"}, {"layout": {"text-letter-spacing": 0.01, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 10.0, "text-max-width": 5, "symbol-spacing": 1000}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 2], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Canal or ditch"}, {"layout": {"text-letter-spacing": 0.01, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 1, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 11.333333, "text-max-width": 5, "symbol-spacing": 1000}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 7], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Small river"}, {"layout": {"text-letter-spacing": 0.01, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 13.333333, "text-max-width": 7, "symbol-spacing": 1000}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 4], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Large river"}, {"layout": {"text-letter-spacing": 0.01, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 11.333333, "text-max-width": 5}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 6], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Small lake or reservoir"}, {"layout": {"text-letter-spacing": 0.01, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 13.333333, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 3], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Large lake or reservoir"}, {"layout": {"text-letter-spacing": 0.15, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 12.0, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Bay or inlet"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 11.3, "text-max-width": 5}, "paint": {"text-color": "#d9d1ba", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Small island"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 12.5, "text-max-width": 7}, "paint": {"text-color": "#d9d1ba", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 5], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Water area/label", "type": "symbol", "id": "Water area/label/Large island"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": 11, "text-max-width": 8, "symbol-spacing": 1000}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 7, "source-layer": "Water area large scale/label", "maxzoom": 11, "type": "symbol", "id": "Water area large scale/label/River"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": 11, "text-max-width": 8}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 7, "source-layer": "Water area large scale/label", "maxzoom": 11, "type": "symbol", "id": "Water area large scale/label/Lake or lake intermittent"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[5, 10], [6, 11]]}, "text-max-width": 8}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 5, "source-layer": "Water area medium scale/label", "maxzoom": 7, "type": "symbol", "id": "Water area medium scale/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[3, 9], [4, 10]]}, "text-max-width": 8}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Water area small scale/label", "maxzoom": 5, "type": "symbol", "id": "Water area small scale/label/Default"}, {"layout": {"symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name_global}", "text-size": 10.0, "text-max-width": 8}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Marine area/label", "type": "symbol", "id": "Marine area/label/Default"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": 11, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 4], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Marine waterbody/label", "maxzoom": 10, "type": "symbol", "id": "Marine waterbody/label/small"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": 11, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 3], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Marine waterbody/label", "maxzoom": 10, "type": "symbol", "id": "Marine waterbody/label/medium"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[2, 11], [6, 13], [9, 15]]}, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 2], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Marine waterbody/label", "maxzoom": 10, "type": "symbol", "id": "Marine waterbody/label/large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[2, 11], [4, 13], [9, 15]]}, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Marine waterbody/label", "maxzoom": 10, "type": "symbol", "id": "Marine waterbody/label/x large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Italic"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[0, 10], [1, 12], [2, 13], [9, 15]]}, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "source-layer": "Marine waterbody/label", "maxzoom": 10, "type": "symbol", "id": "Marine waterbody/label/2x large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 10, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-offset": [0, -0.6], "text-size": {"base": 1.2, "stops": [[15, 11], [17, 11.3]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 15, "source-layer": "Ferry/label", "type": "symbol", "id": "Ferry/label/Rail ferry"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 10, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-offset": [0, -0.6], "text-size": {"base": 1.2, "stops": [[15, 11], [17, 11.3]]}, "text-max-width": 8, "symbol-spacing": 1000}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 15, "source-layer": "Railroad/label", "type": "symbol", "id": "Railroad/label/Default"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[13, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 5], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Local"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[12, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 4], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Minor"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[12, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 3], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Major, alt name"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[12, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Major"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[11, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Freeway Motorway, alt name"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[11, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 7], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Highway"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[11, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Road tunnel/label", "type": "symbol", "id": "Road tunnel/label/Freeway Motorway"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon blue white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#5B708F"}, "filter": ["all", ["==", "_label_class", 66], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon blue white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon red white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#B66D58"}, "filter": ["all", ["==", "_label_class", 68], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon red white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon green white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#718574"}, "filter": ["all", ["==", "_label_class", 70], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon green white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon brown white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#81695E"}, "filter": ["all", ["==", "_label_class", 72], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon brown white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon blue white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#5B708F"}, "filter": ["all", ["==", "_label_class", 65], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon blue white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon red white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#B66D58"}, "filter": ["all", ["==", "_label_class", 67], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon red white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon green white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#718574"}, "filter": ["all", ["==", "_label_class", 69], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon green white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle hexagon brown white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#81695E"}, "filter": ["all", ["==", "_label_class", 71], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle hexagon brown white"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Octagon green white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 74], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Octagon green white (Alt)"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon orange black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 64], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon orange black (Alt)"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon green white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 62], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon green white (Alt)"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon red white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 60], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon red white (Alt)"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 56], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon green yellow (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFF73"}, "filter": ["all", ["==", "_label_class", 54], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon green yellow (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon green white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 52], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon green white (Alt)"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon yellow black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 50], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon yellow black (Alt)"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon blue white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 48], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon blue white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.3], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 46], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon inverse white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.3], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 44], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon inverse white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle green yellow (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFF73"}, "filter": ["all", ["==", "_label_class", 42], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle green yellow (Alt)"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle green white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 40], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle green white (Alt)"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle yellow black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 38], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle yellow black (Alt)"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon blue white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 58], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon blue white (Alt)"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle red white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 36], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle red white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle blue white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 34], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle blue white (Alt)"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 32], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/V-shaped white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "filter": ["all", ["==", "_label_class", 30], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/V-shaped white black (Alt)"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped blue white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 28], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped blue white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped red white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 26], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped red white (Alt)"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped yellow black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 24], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped yellow black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped green leaf (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "filter": ["all", ["==", "_label_class", 22], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped green leaf (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped white green (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 20], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped white green (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 18], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Secondary Hwy red white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 16], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Secondary Hwy red white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Secondary Hwy green white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 14], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Secondary Hwy green white (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Secondary Hwy white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 12], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Secondary Hwy white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Shield white black (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.0], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "filter": ["all", ["==", "_label_class", 10], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Shield white black (Alt)"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Shield blue white (Alt)/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.0], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 8], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Shield blue white (Alt)"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Octagon green white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 73], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Octagon green white"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon orange black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 63], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon orange black"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon green white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 61], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon green white"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon red white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 59], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon red white"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 55], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon green yellow/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFF73"}, "filter": ["all", ["==", "_label_class", 53], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon green yellow"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon green white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 51], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon green white"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon yellow black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 49], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon yellow black"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon blue white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 47], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon blue white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.3], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 45], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Pentagon inverse white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.3], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 43], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Pentagon inverse white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle green yellow/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFF73"}, "filter": ["all", ["==", "_label_class", 41], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle green yellow"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle green white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 39], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle green white"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle yellow black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 37], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle yellow black"}, {"layout": {"text-letter-spacing": 0.03, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Regular"], "icon-image": "Road/Hexagon blue white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 57], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Hexagon blue white"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle red white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 35], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle red white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle blue white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 33], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle blue white"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Rectangle white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.2], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 31], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Rectangle white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/V-shaped white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "filter": ["all", ["==", "_label_class", 29], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/V-shaped white black"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped blue white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 27], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped blue white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped red white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 25], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped red white"}, {"layout": {"text-letter-spacing": 0.02, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped yellow black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 23], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped yellow black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped green leaf/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "filter": ["all", ["==", "_label_class", 21], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped green leaf"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped white green/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 19], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped white green"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/U-shaped white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 17], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/U-shaped white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Secondary Hwy red white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 15], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Secondary Hwy red white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Secondary Hwy green white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#FFFFFF"}, "filter": ["all", ["==", "_label_class", 13], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Secondary Hwy green white"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Secondary Hwy white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.1], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#000000"}, "filter": ["all", ["==", "_label_class", 11], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Secondary Hwy white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Shield white black/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.0], "text-rotation-alignment": "viewport", "text-size": 9.333333, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "filter": ["all", ["==", "_label_class", 9], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Shield white black"}, {"layout": {"symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 30, "text-font": ["Arial Regular"], "icon-image": "Road/Shield blue white/{_len}", "text-field": "{_name}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.0], "text-rotation-alignment": "viewport", "text-size": 8.666667, "text-max-width": 8}, "paint": {"text-color": "#FDFDFD"}, "filter": ["all", ["==", "_label_class", 7], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Shield blue white"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[13, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 5], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Local"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[12, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 4], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Minor"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[12, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 3], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Major, alt name"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[12, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 2], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 12, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Major"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"base": 1.2, "stops": [[11, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 1], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Freeway Motorway, alt name"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[11, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 75], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Highway"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "symbol-placement": "line", "text-padding": 2, "text-font": ["Arial Bold"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[11, 10], [16, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 0], ["!in", "Viz", 3]], "source": "<PERSON><PERSON>", "minzoom": 11, "source-layer": "Road/label", "type": "symbol", "id": "Road/label/Freeway Motorway"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Cemetery/label", "type": "symbol", "id": "Cemetery/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Freight/label", "type": "symbol", "id": "Freight/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Water and wastewater/label", "type": "symbol", "id": "Water and wastewater/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Port/label", "type": "symbol", "id": "Port/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Industry/label", "type": "symbol", "id": "Industry/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Government/label", "type": "symbol", "id": "Government/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Finance/label", "type": "symbol", "id": "Finance/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Emergency/label", "type": "symbol", "id": "Emergency/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Indigenous/label", "type": "symbol", "id": "Indigenous/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 25, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Military/label", "type": "symbol", "id": "Military/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Transportation/label", "type": "symbol", "id": "Transportation/label/Default"}, {"layout": {"text-letter-spacing": 0.08, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#d9d1ba", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 13, "source-layer": "Beach/label", "type": "symbol", "id": "Beach/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Golf course/label", "type": "symbol", "id": "Golf course/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Zoo/label", "type": "symbol", "id": "Zoo/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#e8cc99", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Retail/label", "type": "symbol", "id": "Retail/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#e8cc99", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Landmark/label", "type": "symbol", "id": "Landmark/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 25, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Openspace or forest/label", "type": "symbol", "id": "Openspace or forest/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 25, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Park or farming/label", "type": "symbol", "id": "Park or farming/label/Default"}, {"layout": {"text-letter-spacing": 0.08, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "center", "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[9, 10], [15, 11], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Point of interest", "type": "symbol", "id": "Point of interest/Park"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 50, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#e8cc99", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Education/label", "type": "symbol", "id": "Education/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#f2c2c2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Medical/label", "type": "symbol", "id": "Medical/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 25, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[6, 10], [7, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Admin1 forest or park/label", "type": "symbol", "id": "Admin1 forest or park/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 25, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[6, 10], [7, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#def2b6", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Admin0 forest or park/label", "type": "symbol", "id": "Admin0 forest or park/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 15, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11], [14, 11.3], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Airport/label", "type": "symbol", "id": "Airport/label/Airport property"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "center", "icon-image": "Exit/Default/{_len}", "text-field": "{_name_global}", "icon-rotation-alignment": "viewport", "text-offset": [0.0, 0.3], "text-rotation-alignment": "viewport", "text-size": {"base": 1.2, "stops": [[15, 9], [17, 10]]}, "text-max-width": 8}, "paint": {"text-color": "#343434"}, "source": "<PERSON><PERSON>", "minzoom": 15, "source-layer": "Exit", "type": "symbol", "id": "Exit/Default"}, {"layout": {"text-letter-spacing": 0.04, "symbol-avoid-edges": true, "icon-size": {"stops": [[11, 0.85], [16, 1.3]]}, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom", "icon-image": "Point of interest/Bus station", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-offset": [0, -0.5], "text-size": {"base": 1.2, "stops": [[9, 10], [15, 11], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 2], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Point of interest", "type": "symbol", "id": "Point of interest/Bus station"}, {"layout": {"text-letter-spacing": 0.04, "symbol-avoid-edges": true, "icon-size": {"stops": [[11, 0.85], [16, 1.3]]}, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom", "icon-image": "Point of interest/Rail station", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-offset": [0, -0.5], "text-size": {"base": 1.2, "stops": [[9, 10], [15, 11], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 3], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Point of interest", "type": "symbol", "id": "Point of interest/Rail station"}, {"layout": {"text-letter-spacing": 0.08, "symbol-avoid-edges": true, "text-padding": 20, "text-font": ["Arial Regular"], "text-anchor": "center", "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[9, 10], [15, 11], [17, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 9, "source-layer": "Point of interest", "type": "symbol", "id": "Point of interest/General"}, {"layout": {"text-letter-spacing": 0.2, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": 11, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Admin2 area/label", "maxzoom": 12, "type": "symbol", "id": "Admin2 area/label/small"}, {"layout": {"text-letter-spacing": 0.2, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": 12.5, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "Admin2 area/label", "maxzoom": 12, "type": "symbol", "id": "Admin2 area/label/large"}, {"layout": {"symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"stops": [[3, 9], [10, 11]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 5], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Admin1 area/label", "maxzoom": 10, "type": "symbol", "id": "Admin1 area/label/x small"}, {"layout": {"symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"stops": [[3, 9], [10, 11]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 4], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Admin1 area/label", "maxzoom": 10, "type": "symbol", "id": "Admin1 area/label/small"}, {"layout": {"symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"stops": [[3, 9], [10, 13]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 3], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Admin1 area/label", "maxzoom": 10, "type": "symbol", "id": "Admin1 area/label/medium"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"stops": [[3, 10], [10, 13]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 2], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Admin1 area/label", "maxzoom": 10, "type": "symbol", "id": "Admin1 area/label/large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"stops": [[3, 10], [10, 16]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Admin1 area/label", "maxzoom": 10, "type": "symbol", "id": "Admin1 area/label/x large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-field": "{_name}", "text-size": {"stops": [[3, 11], [10, 16]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 3, "source-layer": "Admin1 area/label", "maxzoom": 10, "type": "symbol", "id": "Admin1 area/label/2x large"}, {"layout": {"text-transform": "uppercase", "text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "center", "text-field": "{_name}", "text-size": {"stops": [[5, 11], [10, 12.5]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 5], "source": "<PERSON><PERSON>", "minzoom": 5, "source-layer": "Admin0 point", "maxzoom": 10, "type": "symbol", "id": "Admin0 point/x small"}, {"layout": {"text-transform": "uppercase", "text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "center", "text-field": "{_name}", "text-size": {"stops": [[4, 10], [10, 12.5]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 4], "source": "<PERSON><PERSON>", "minzoom": 4, "source-layer": "Admin0 point", "maxzoom": 10, "type": "symbol", "id": "Admin0 point/small"}, {"layout": {"text-transform": "uppercase", "text-letter-spacing": 0.05, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "center", "text-field": "{_name}", "text-size": {"stops": [[2, 9.5], [10, 15.5]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 3], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Admin0 point", "maxzoom": 10, "type": "symbol", "id": "Admin0 point/medium"}, {"layout": {"text-transform": "uppercase", "text-letter-spacing": 0.1, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "center", "text-field": "{_name}", "text-size": {"stops": [[2, 9.5], [10, 15.5]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 2], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Admin0 point", "maxzoom": 10, "type": "symbol", "id": "Admin0 point/large"}, {"layout": {"text-transform": "uppercase", "text-letter-spacing": 0.12, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "center", "text-field": "{_name}", "text-size": {"stops": [[2, 9.5], [10, 18]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Admin0 point", "maxzoom": 10, "type": "symbol", "id": "Admin0 point/x large"}, {"layout": {"text-transform": "uppercase", "text-letter-spacing": 0.15, "symbol-avoid-edges": true, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "center", "text-field": "{_name}", "text-size": {"stops": [[2, 12], [10, 18]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[5, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Admin0 point", "maxzoom": 10, "type": "symbol", "id": "Admin0 point/2x large"}, {"layout": {"text-letter-spacing": 0.08, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "Neighborhood", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": 11.3, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 14, "source-layer": "Neighborhood", "maxzoom": 17, "type": "symbol", "id": "Neighborhood"}, {"layout": {"text-letter-spacing": 0.08, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City large scale", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": 11.3, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 5], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "City large scale", "maxzoom": 17, "type": "symbol", "id": "City large scale/town small"}, {"layout": {"text-letter-spacing": 0.09, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City large scale", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11.3], [12, 12]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[10, "#ffffff"], [13, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 4], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "City large scale", "maxzoom": 17, "type": "symbol", "id": "City large scale/town large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City large scale", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 11.3], [12, 12]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[10, "#ffffff"], [13, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 3], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "City large scale", "maxzoom": 17, "type": "symbol", "id": "City large scale/small"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City large scale", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[10, 12], [12, 12.67]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[10, "#ffffff"], [11, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 2], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "City large scale", "maxzoom": 17, "type": "symbol", "id": "City large scale/medium"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City large scale", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": 15, "text-max-width": 8}, "paint": {"text-color": "#ffffcc", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 1], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "City large scale", "maxzoom": 17, "type": "symbol", "id": "City large scale/large"}, {"layout": {"text-letter-spacing": 0.1, "symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City large scale", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-size": 15, "text-max-width": 8}, "paint": {"text-color": "#ffffcc", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_label_class", 0], "source": "<PERSON><PERSON>", "minzoom": 10, "source-layer": "City large scale", "maxzoom": 17, "type": "symbol", "id": "City large scale/x large"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/town small non capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.67, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 17], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/town small non capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/town large non capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 15], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/town large non capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/small non capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 12], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/small non capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/medium non capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 9], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/medium non capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/other capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.67, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 18], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/other capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/town large other capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 14], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/town large other capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/small other capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 11], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/small other capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/medium other capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 8], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/medium other capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/town small admin0 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.67, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 16], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/town small admin0 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/town large admin0 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 13], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/town large admin0 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Regular"], "text-anchor": "bottom-left", "icon-image": "City small scale/small admin0 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 10], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/small admin0 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/medium admin0 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 7], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/medium admin0 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/large other capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 5], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/large other capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/x large admin2 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 12.0, "text-max-width": 8}, "paint": {"text-color": {"stops": [[2, "#ffffff"], [6, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 2], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/x large admin2 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/large non capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffff", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 6], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/large non capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/large admin0 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 10.666667, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 4], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/large admin0 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/x large non capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 12.0, "text-max-width": 8}, "paint": {"text-color": {"stops": [[2, "#ffffff"], [6, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 3], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/x large non capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/x large admin1 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 12.0, "text-max-width": 8}, "paint": {"text-color": {"stops": [[2, "#ffffff"], [6, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 1], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/x large admin1 capital"}, {"layout": {"symbol-avoid-edges": true, "icon-padding": 1, "text-padding": 1, "text-font": ["Arial Bold"], "text-anchor": "bottom-left", "icon-image": "City small scale/x large admin0 capital", "icon-allow-overlap": true, "text-field": "{_name}", "text-offset": [0.13, -0.13], "text-size": 12.0, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["==", "_symbol", 0], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "City small scale", "maxzoom": 10, "type": "symbol", "id": "City small scale/x large admin0 capital"}, {"layout": {"text-letter-spacing": 0.08, "symbol-avoid-edges": true, "text-padding": 20, "text-font": ["Arial Regular"], "text-field": "{_name_global}", "text-size": {"base": 1.2, "stops": [[15, 11], [17, 11.3]]}, "text-max-width": 8}, "paint": {"text-color": "#b2b2b2", "text-halo-width": 1.33, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 15, "source-layer": "Building/label", "type": "symbol", "id": "Building/label/Default"}, {"layout": {"text-letter-spacing": 0.05, "symbol-avoid-edges": true, "icon-padding": 1, "text-transform": "uppercase", "text-font": ["Arial Regular"], "text-anchor": "center", "icon-image": "Continent", "icon-allow-overlap": true, "text-field": "{_name_global}", "text-padding": 1, "text-size": {"stops": [[0, 9], [1, 12]]}, "text-max-width": 8}, "paint": {"text-color": "#ffffd9", "text-halo-width": 1.2, "text-halo-color": "#000000"}, "source": "<PERSON><PERSON>", "minzoom": 0, "source-layer": "Continent", "maxzoom": 2, "type": "symbol", "id": "Continent"}, {"layout": {"text-letter-spacing": 0.08, "icon-allow-overlap": true, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Disputed label point", "text-field": "{_name}", "text-optional": true, "text-size": 11.5, "text-max-width": 7}, "paint": {"text-color": "#d9d1ba", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 1], ["in", "DisputeID", 0]], "source": "<PERSON><PERSON>", "minzoom": 6, "source-layer": "Disputed label point", "type": "symbol", "id": "Disputed label point/Island"}, {"layout": {"text-letter-spacing": 0.1, "icon-allow-overlap": true, "text-font": ["Arial Italic"], "text-anchor": "center", "icon-image": "Disputed label point", "text-field": "{_name}", "text-optional": true, "text-size": 11, "text-max-width": 7}, "paint": {"text-color": "#9ecccc", "text-halo-width": 1, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 0], ["in", "DisputeID", 1006]], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Disputed label point", "maxzoom": 10, "type": "symbol", "id": "Disputed label point/Waterbody"}, {"layout": {"text-letter-spacing": 0.1, "icon-allow-overlap": true, "text-transform": "uppercase", "text-font": ["Arial Bold"], "text-anchor": "center", "icon-image": "Disputed label point", "text-field": "{_name}", "text-optional": true, "text-size": {"stops": [[2, 9.5], [10, 15.5]]}, "text-max-width": 8}, "paint": {"text-color": {"stops": [[2, "#cecdcd"], [7, "#ffffd9"]]}, "text-halo-width": 1.2, "text-halo-color": "#000000"}, "filter": ["all", ["==", "_label_class", 2], ["in", "DisputeID", 1021]], "source": "<PERSON><PERSON>", "minzoom": 2, "source-layer": "Disputed label point", "type": "symbol", "id": "Disputed label point/Admin0"}], "glyphs": "https://basemaps.arcgis.com/arcgis/rest/services/World_Basemap_v2/VectorTileServer/resources/fonts/{fontstack}/{range}.pbf", "version": 8, "sprite": "https://www.arcgis.com/sharing/rest/content/items/30d6b8271e1849cd9c3042060001f425/resources/styles/../sprites/sprite", "sources": {"esri": {"url": "https://basemaps.arcgis.com/arcgis/rest/services/World_Basemap_v2/VectorTileServer", "type": "vector"}}}