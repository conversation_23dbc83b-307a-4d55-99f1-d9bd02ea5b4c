(function()
{
	createNamespace("TF.Grid").FilterHelper = FilterHelper;

	function FilterHelper()
	{ }

	FilterHelper.isFilterMenuOpen = function($element)
	{
		return $element.closest('.tf-contextmenu-wrap').hasClass('filtermenu-wrap');
	};

	FilterHelper.getFilterById = function(filterId, option)
	{
		// filter no need to validate if it is none or it is special filter build for dashboard
		if (!filterId || filterId <= 0)
		{
			return Promise.resolve({ IsValid: true });
		}

		const overlay = option && option.hasOwnProperty("overlay") ? { overlay: option.overlay } : null;
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'gridfilters'),
			{
				paramData: { id: filterId }
			}, overlay)
			.then(function(apiResponse)
			{
				if (apiResponse && apiResponse.Items && apiResponse.Items.length == 1)
				{
					return apiResponse.Items[0];
				}
				else
				{
					return null;
				}
			});
	};

	FilterHelper.validFilterId = function(filterId, url)
	{
		let filter;
		return FilterHelper.getFilterById(filterId).then(res =>
		{
			filter = res;
			if (filter)
			{
				if (!filter.IsValid)
				{
					var param = {
						data: JSON.stringify(filter.WhereClause),
					};
					return tf.promiseAjax.post(url, param);
				}

				return true;
			}
			else
			{
				return false;
			}
		}).then(response =>
		{
			if (filter && !filter.IsValid)
			{
				let result = response?.Items[0];
				if (result)
				{
					filter.IsValid = true;
					filter.Changed = true;
				}

				return filter;
			}
			else
			{
				return response;
			}
		});
	};

	FilterHelper.isDrillDownFillter = function(filterId)
	{
		return (typeof filterId === 'number' && filterId < 0);
	};

	FilterHelper.saveQuickFilter = function(gridType, quickFilters)
	{
		//it's better used only in KendoGridFilterMenu
		if (gridType === 'student')
		{
			setRequestOption(quickFilters);
		}
		return tf.storageManager.save(tf.storageManager.gridCurrentQuickFilter(gridType), quickFilters, undefined, undefined, false);
	};

	FilterHelper.clearQuickFilter = function(gridType)
	{
		//it's better used only in KendoGridFilterMenu
		return TF.Grid.FilterHelper.saveQuickFilter(gridType, new TF.SearchParameters(null, null, null, null, null, null, null));
	};

	function setRequestFilterSet(filterSet)
	{
		if (!filterSet)
		{
			return;
		}

		let items = filterSet.FilterItems;
		if (items)
		{
			let geoField = items.find(x => x.FieldName === 'Geo');
			if (geoField != null)
			{
				// True means equal to 4, False means not equal to 4
				let operator = (geoField.Operator || "").toLowerCase();
				if (operator == "equalto" && geoField.Value == '4')
				{
					geoField.Value = 'true'
				}
				else
				{
					geoField.Value = 'false'
				}
			}
		}

		let sets = filterSet.FilterSets;
		if (sets)
		{
			sets.forEach(set => setRequestFilterSet(set));
		}
	}

	function setRequestOption(quickFilters)
	{
		if (quickFilters == null || quickFilters.data == null)
		{
			return;
		}
		let filterSet = quickFilters.data.filterSet;
		setRequestFilterSet(filterSet);
	}

	FilterHelper.compareFilterWhereClause = function(leftFilterWhereClause, rightFilterWhereClause)
	{
		return leftFilterWhereClause === rightFilterWhereClause;
	};

	FilterHelper.getGridDefinitionByType = function(gridType, options)
	{
		switch ((gridType || '').toLowerCase())
		{
			case 'altsite':
			case 'alternatesite':
				return tf.altsiteGridDefinition.gridDefinition();
			case 'busfinderhistorical':
				return tf.busfinderHistoricalGridDifinition.gridDefinition();
			case 'contractor':
				return tf.contractorGridDefinition.gridDefinition();
			case 'contact':
				return tf.contactGridDefinition.gridDefinition();
			case 'staff':
			case 'driver':
				return tf.staffGridDefinition.gridDefinition();
			case 'district':
				return tf.districtGridDefinition.gridDefinition();
			case 'document':
				return tf.documentGridDefinition.gridDefinition();
			case 'disability_codes':
				return tf.disabilityGridDefinition.gridDefinition();
			case 'ethnic_codes':
				return tf.ethnicGridDefinition.gridDefinition();
			case 'equipment':
				return tf.equipmentGridDefinition.gridDefinition();
			case 'fieldtrip':
				return tf.fieldTripGridDefinition.gridDefinition();
			case 'forms':
				return tf.formsGridDefinition.gridDefinition();
			case 'form':
				return tf.formGridDefinition.gridDefinition([], { ShowLogColumn: options?.ShowLogColumn });
			case 'georegion':
				return tf.georegionGridDefinition.gridDefinition();
			case 'route':
				return tf.routeGridDefinition.gridDefinition();
			case 'report':
				return tf.reportGridDefinition.gridDefinition();
			case 'reportlibrary':
				return tf.ReportLibraryGridDefinition.gridDefinition();
			case 'recordcontact':
				return tf.recordContactGridDefinition.gridDefinition();
			case 'student':
				return tf.studentGridDefinition.gridDefinition();
			case 'school':
				return tf.schoolGridDefinition.gridDefinition();
			case 'stopstudent':
				return tf.studentGridDefinition.gridDefinition();			
			case 'scheduledreport':
				return tf.scheduledReportGridDefinition.gridDefinition();
			case 'studentattendanceschedule':
				return tf.studentScheduleGridDefinition.gridDefinition();
			case 'trip':
				return tf.tripGridDefinition.gridDefinition();
			case 'tripstop':
				return tf.tripStopGridDefinition.gridDefinition();
			case 'tripschedule':
				return tf.tripScheduleGridDefinition.gridDefinition();
			case 'tripstopschedule':
				return tf.tripStopScheduleGridDefinition.gridDefinition();
			case 'vehicle':
				return tf.vehicleGridDefinition.gridDefinition();
			case 'session':
				return tf.sessionGridDefinition.gridDefinition();
			case 'auditlog':
				return tf.auditLogGridDefinition.gridDefinition();
			case 'gpsevent':
				return tf.plusGPSEventGridDefinition.gridDefinition();
			case 'dashboards':
				return tf.customizedDashboardGridDefinition.gridDefinition();
			case 'dashboardLibrary':
				return tf.customizedDashboardLibraryGridDefinition.gridDefinition();
			case 'formLibrary':
				return tf.formLibraryGridDefinition.gridDefinition();
			case 'boundaryset':
				return tf.boundarySetGridDefinition.gridDefinition();
			case 'populationregion':
				return tf.populationRegionGridDefinition.gridDefinition();
			case 'street':
				return tf.streetGridDefinition.gridDefinition();
			case 'parceladdresspoint':
				return tf.parcelAddressPointGridDefinition.gridDefinition();
			case 'mapincident':
				return tf.mapIncidentGridDefinition.gridDefinition();
		}

		return '';
	};

	FilterHelper.mergeOnlyForFilterColumns = function(gridType, columns)
	{
		const isLoggedForm = TF.GridDefinition.FormGridDefinition.isLoggedForm(gridType, columns);
		var needMergeGridDefinition = TF.Grid.FilterHelper.getGridDefinitionByType(gridType, { ShowLogColumn: isLoggedForm });
		if (!needMergeGridDefinition)
			return columns;

		var onlyForFilterColumns = [];
		needMergeGridDefinition.Columns.forEach(function(column)
		{
			if (column.onlyForFilter)
			{
				column = $.extend(
					{}, column, TF.Grid.GridHelper.convertToOldGridDefinition(column));
				onlyForFilterColumns.push(column);
			}
		});

		columns = Enumerable.From(columns.concat(onlyForFilterColumns)).Distinct(function(c) { return c.FieldName; }).ToArray();

		// Remove onlyForGrid columns
		var toBeRemoved = {};
		needMergeGridDefinition.Columns.forEach(function(column)
		{
			toBeRemoved[column.FieldName] = column.onlyForGrid ? true : false;
		});

		columns = columns.filter(function(column)
		{
			return !toBeRemoved[column.FieldName];
		});

		return columns;
	};

	FilterHelper.getAvailableFilters = function(databaseId, datatypeId)
	{
		return Promise.all([
			this.getStaticFilters(databaseId, datatypeId),
			this.getNormalFilters(databaseId, datatypeId),
		]).then(([staticFilters, normalFilters]) => {
			return [].concat(staticFilters || [], normalFilters || []);
		});
	}

	FilterHelper.getNormalFilters = function(databaseId, datatypeId)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"),
			{
				paramData: {
					"@filter": String.format("(eq(dbid, {0})|isnull(dbid,))&eq(datatypeId,{1})&eq(IsValid,true)", databaseId, datatypeId),
					"@sort": "Name"
				}
			})
			.then(function(data)
			{
				return data?.Items || [];
			}).catch(() => []);
	}

	FilterHelper.getStaticFilters = function(databaseId, datatypeId)
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "staticfilters"),
			{
				paramData: {
					datatypeId,
				}
			},
			{
				overlay: false
			}).then((staticFiltersResponse) =>
			{
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), databaseId, 'reminders'), {
					paramData: {
						"@filter": String.format("(in(StaticFilterName,{0}))", (staticFiltersResponse.Items || []).join(',')),
					}
				}, { overlay: false }).then(remindersResponse =>
				{
					return (staticFiltersResponse.Items || []).map((item, index) =>
					{
						return {
							Id: -(index + 1),
							Name: item,
							WhereClause: item,
							Reminders: (remindersResponse.Items || []).filter(r => r.StaticFilterName === item),
							IsStatic: true
						}
					})
				}).catch(() => []);
			}).catch(() => []);
	}
})();
