﻿(function()
{
	createNamespace('TF').Ajax = Ajax;

	function Ajax(loadingIndicator, cacheAjaxRequestsArray)
	{
		this.loadingIndicator = loadingIndicator;
		this.ajaxRequests = [];
		this.cacheAjaxRequestsArray = !!cacheAjaxRequestsArray;
		this.alerting = false;
		this.requestCount = 0;
	}

	Ajax.prototype = {

		_onBeforeSend: function(xmlHttpRequest, settings, overlay, externalPointer, context)
		{
			if (overlay == true)
			{
				this.loadingIndicator.setSubtitle(settings.loadingSubtitle);
				this.loadingIndicator.show();
			}

			if (externalPointer)
			{
				externalPointer(xmlHttpRequest, settings);
			}

			if (!!context)
			{
				context.pageRequests = context.pageRequests || [];
				context.pageRequests.push(xmlHttpRequest);
			}
		},

		_onComplete: function(xmlHttpRequest, status, overlay, externalPointer, context)
		{
			if (overlay == true)
			{
				this.loadingIndicator.tryHide();
			}
			if (externalPointer)
			{
				externalPointer();
			}
			this._clearAjax(xmlHttpRequest);


			if (context && context.pageRequests)
			{
				context.pageRequests.splice(context.pageRequests.indexOf(xmlHttpRequest), 1)
			}
		},

		_onError: function(xmlHttpRequest, status, error, externalPointer, auth)
		{
			var self = this;
			var jsonObj = {};
			try
			{
				jsonObj = JSON.parse(xmlHttpRequest.responseText);
			}
			catch (error) { }

			function handleSessionLogoff()
			{
				tf.authManager.logOffTag = true;

				const token = tf.entStorageManager.get("token", true);
				// if tf.authManager.token equal token, it means not other account have logged in in same browser,so we clear token and log in to the app again.
				// if tf.authManager.token not equal token, it means other account have logged in in same browser,so we only reload the app.
				if (tf.authManager.token === token)
				{
					tf.entStorageManager.save("token", "", true);
				}

				if (tf.isFromViewfinderApp && window.webkit && window.webkit.messageHandlers)
				{
					window.webkit.messageHandlers.cordova_iab.postMessage(JSON.stringify({ message: 'CLOSE' }));
					return;
				}

				location.reload();
			}

			if (!self.alerting && !(auth && auth.noInterupt) && xmlHttpRequest.status === 401 && jsonObj.Message === "Access denied.")
			{
				handleSessionLogoff();
				return;
			}

			if (!self.alerting && !(auth && auth.noInterupt) && (error === "Unauthorized" || ["Missing Token", "Invalid Token"].includes(TF.getErrorMessage(jsonObj)) || (xmlHttpRequest.status === 401 && TF.getErrorMessage(jsonObj) == "Your account has been deactivated!")))
			{
				self.alerting = true;
				if (tf.documentManagerViewModel)
				{
					tf.documentManagerViewModel.closeWhiteboardInMapCanvasDoc();
					tf.documentManagerViewModel.removeTripStopScheduleCache();
				}
				tf.authManager.onLoginSessionExpiredEvent && tf.authManager.onLoginSessionExpiredEvent.notify();
				tf.promiseBootbox.alert("Login session expired")
					.then(function()
					{
						handleSessionLogoff();
					});
			}

			if (externalPointer)
			{
				externalPointer(xmlHttpRequest.responseJSON, xmlHttpRequest.status);
			}
		},

		_onSuccess: function(data, xmlHttpRequest, status, externalPointer)
		{
			if (externalPointer)
			{
				externalPointer(data, xmlHttpRequest);
			}
		},

		get: function(url, settings, option)
		{
			var settings = this._applyDefaults(url, settings, option);

			if (settings.paramData)
			{
				settings.data = settings.paramData;
			}
			var ajaxRequest = $.ajax(settings);

			ajaxRequest.requestUrl = url;
			if (this.cacheAjaxRequestsArray)
			{
				this.ajaxRequests[this.ajaxRequests.length] = ajaxRequest;
			}
			return ajaxRequest;
		},

		post: function(url, settings, option)
		{
			var settings = this._applyDefaults(url, settings, option);
			this._handleData(settings);
			settings.type = "POST";
			if (option && option.isCopyRequest)
			{
				settings.async = false;
			}
			var ajaxRequest = $.ajax(settings);
			ajaxRequest.requestUrl = url;
			if (this.cacheAjaxRequestsArray)
			{
				this.ajaxRequests[this.ajaxRequests.length] = ajaxRequest;
			}
			return ajaxRequest;
		},

		put: function(url, settings, option)
		{
			var settings = this._applyDefaults(url, settings, option);
			this._handleData(settings);
			settings.type = "PUT";
			var ajaxRequest = $.ajax(settings);
			if (this.cacheAjaxRequestsArray)
			{
				this.ajaxRequests[this.ajaxRequests.length] = ajaxRequest;
			}
			return ajaxRequest;
		},

		delete: function(url, settings, option)
		{
			var settings = this._applyDefaults(url, settings, option);
			this._handleData(settings);
			settings.type = "DELETE";
			var ajaxRequest = $.ajax(settings);
			if (this.cacheAjaxRequestsArray)
			{
				this.ajaxRequests[this.ajaxRequests.length] = ajaxRequest;
			}
			return ajaxRequest;
		},

		patch: function(url, settings, option)
		{
			var settings = this._applyDefaults(url, settings, option);
			this._handleData(settings);
			settings.type = "PATCH";
			var ajaxRequest = $.ajax(settings);
			if (this.cacheAjaxRequestsArray)
			{
				this.ajaxRequests[this.ajaxRequests.length] = ajaxRequest;
			}
			return ajaxRequest;
		},

		_applyDefaults: function(url, externalSettings, option)
		{
			option = $.extend({ overlay: true, auth: null, authorization: true }, option);

			var settings = {
				url: url,
				dataType: 'json',
				contentType: 'application/json; charset=utf-8',
				cache: false,
				traditional: !!window.UsingNET8API,
				loadingSubtitle: "Loading"
			};

			if (externalSettings)
			{
				// Preserve any function pointers set by external code.
				var beforeSend = externalSettings.beforeSend;
				var complete = externalSettings.complete;
				var successCallback = externalSettings.success;
				var errorCallback = externalSettings.error;

				// Override default settins with external settings.
				$.extend(settings, externalSettings);
			}

			// Replace the function pointers on the settings object with wrappers.
			settings.beforeSend = function(xmlHttpRequest, settings)
			{
				this._onBeforeSend(xmlHttpRequest, settings, option.overlay, beforeSend, option.context);
				if (tf.stopfinderUtil && tf.stopfinderUtil.isStopfinderRequest(settings.url))
				{
					if (tf.stopfinderUtil.stopfinderToken && tf.stopfinderUtil.stopfinderToken.token)
					{
						xmlHttpRequest.setRequestHeader('Token', tf.stopfinderUtil.stopfinderToken.token);
					}
					xmlHttpRequest.setRequestHeader('Transfinder', tf.api.serverWithoutVersion());
					xmlHttpRequest.setRequestHeader('DBId', option.DBID || tf.datasourceManager.databaseId);
					xmlHttpRequest.setRequestHeader('Admin', "true");
					xmlHttpRequest.setRequestHeader('ClientKey', tf.authManager.clientKey || '');
					xmlHttpRequest.setRequestHeader('X-StopfinderAdmin-Version', '2.0');
					xmlHttpRequest.setRequestHeader('X-RoutefinderAPI-Version', "2");
				}
				else
				{
					var token = tf.entStorageManager.get("token", true);
					if (token)
					{
						xmlHttpRequest.setRequestHeader('Token', token);

						// if tf.authManager.token === token, then token not was updated in Local Storage, so there is not other account login in same browser.
						// if tf.authManager.token !== token, then token was updated in Local Storage, so there is other account login in same browser. so we need to log out current account.
						if (tf.authManager && tf.authManager.token && tf.authManager.token !== token)
						{
							xmlHttpRequest.setRequestHeader('Token', "");
						}
					}

					if (tf.authManager && tf.authManager.surveyToken)
					{
						xmlHttpRequest.setRequestHeader('SurveyToken', tf.authManager.surveyToken);
						xmlHttpRequest.setRequestHeader('authorizeBySurveyToken', true);
					}

					xmlHttpRequest.setRequestHeader('Prefix', tf.appPrefix);
					xmlHttpRequest.setRequestHeader('UserDate', moment().format("YYYY-MM-DDTHH:mm:ss.SSS"));
					if (settings.headers)
					{
						for (var key in settings.headers)
						{
							xmlHttpRequest.setRequestHeader(key, settings.headers[key]);
						}
					}
				}
			}.bind(this);

			settings.complete = function(xmlHttpRequest, status)
			{
				this._onComplete(xmlHttpRequest, settings, option.overlay, complete, option.context)
			}.bind(this);

			settings.error = function(xmlHttpRequest, status, error)
			{
				if (!tf.stopfinderUtil || !tf.stopfinderUtil.isStopfinderRequest(settings.url))
				{
					this._onError(xmlHttpRequest, status, error, errorCallback, option.auth);
				}
			}.bind(this);

			settings.success = function(data, status, xmlHttpRequest)
			{
				this._onSuccess(data, xmlHttpRequest, status, successCallback);
			}.bind(this);

			return settings;
		},

		_handleData: function(settings)
		{
			if (!(typeof settings.data == 'string' || settings.data instanceof String))
			{
				settings.data = JSON.stringify(settings.data, settings.handleData);
			}
			if (settings.paramData && jQuery.param(settings.paramData))
			{
				settings.url = settings.url + (settings.url.indexOf("?") > -1 ? "&" : "?") + jQuery.param(settings.paramData);
			}
			return;
		},
		_clearAjax: function(xmlHttpRequest)
		{
			if (this.cacheAjaxRequestsArray)
			{
				for (var i = 0; i < this.ajaxRequests.length; i++)
				{
					if (this.ajaxRequests[i] == xmlHttpRequest)
					{
						this.ajaxRequests[i] = null;
					}
				}
			}
		},
		abort: (context) =>
		{
			if (context && context.pageRequests && context.pageRequests.length > 0)
			{
				context.pageRequests.forEach(request =>
				{
					request.abort();
				})
				context.pageRequests = [];
			}
		},
		setAlertingValue: function()
		{
			this.alerting = true;
		}
	};
})();
