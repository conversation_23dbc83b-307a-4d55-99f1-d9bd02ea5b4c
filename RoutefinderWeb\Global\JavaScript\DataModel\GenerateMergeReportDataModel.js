﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.GenerateMergeReportDataModel = function()
	{
		namespace.BaseDataModel.call(this);
	};

	namespace.GenerateMergeReportDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.GenerateMergeReportDataModel.prototype.constructor = namespace.GenerateMergeReportDataModel;

	namespace.GenerateMergeReportDataModel.prototype.mapping = [
		{ from: "Id", default: 0, required: true },
		{ from: "FilterDataSource", default: null },
		{ from: "SpecifyRecordOption", default: null },
		{ from: "FilterName", default: "" },
		{ from: "FilterSpec", default: "" },
		{ from: "OutputTo", default: "" },
		{ from: "SelectedRecordIds", default: [] },
	];

})();
