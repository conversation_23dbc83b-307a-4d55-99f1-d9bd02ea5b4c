(function()
{
	createNamespace("TF.Modal").KendoListMoverWithAddControlModalViewModel = KendoListMoverWithAddControlModalViewModel;

	var _DataFiledName = 'Code';
	var defaultOptions = {
		_DataFiledName: _DataFiledName,
		_GridConifg: {
			gridSchema: {
				// model: {
				// 	fields: {
				// 		'FieldName': { type: "string" },
				// 		'DisplayName': { type: "string" }
				// 	}
				// },
			},
			gridColumns: [
				{
					field: "Code",
					FieldName: "Code"
				}
			],
			height: 400,
			sortable: {
				mode: "single",
				allowUnsort: true
			},
			selectable: TF.isMobileDevice ? "row" : "multiple"
		}
	};
	defaultOptions._sortItems = function(a, b)
	{
		var x, y;
		x = a['Code'] ? a['Code'].toLowerCase() : '';
		y = b['Code'] ? b['Code'].toLowerCase() : '';
		return (x == y ? 0 : (x > y ? 1 : -1));
	};
	defaultOptions._convertImportData = function(items)
	{
		return items;
	};
	defaultOptions._getUnSelectedItems = function(allItems, selectedItems)
	{
		var unSelectedItems = allItems.filter(function(item)
		{
			var matchResult = [];
			matchResult = selectedItems.filter(function(selectedItem)
			{
				return selectedItem.Code === item.Code;
			});
			return matchResult.length === 0;
		});
		return unSelectedItems;
	};
	defaultOptions._fillDisplayName = function(items)
	{
		return items;
	};
	defaultOptions._convertOutputData = function(items)
	{
		return items;
	};
	defaultOptions._sortKendoGrid = function(kendoGrid, sortItemFun)
	{
		kendoGrid.dataSource.sort({ field: "Code", dir: "asc" });
		// kendoGrid.dataSource.data().sort(sortItemFun);
	};

	function KendoListMoverWithAddControlModalViewModel(allItems, selectedItems, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-dialog-lg";
		this.title(options.title);
		this.description = options.description;
		this.contentTemplate("modal/kendolistmoverwithaddcontrol");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Apply");
		options = $.extend(true, {}, defaultOptions, options);

		options._GridConifg.gridColumns[0].template = options.formatter; // special code for student dataEntry
		options._GridConifg.gridColumns[1] = { FieldName: "Description", field: "Description" };

		options.showRemoveColumnButton = false;
		options.showButtons = true;

		this.kendolistMoverControlViewModel = new TF.Control.KendoListMoverWithAddControlViewModel(allItems, selectedItems, options, this.shortCutKeyHashMapKeyName);
		this.data(this.kendolistMoverControlViewModel);
	}

	KendoListMoverWithAddControlModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	KendoListMoverWithAddControlModalViewModel.prototype.constructor = KendoListMoverWithAddControlModalViewModel;

	KendoListMoverWithAddControlModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.kendolistMoverControlViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};
})();
