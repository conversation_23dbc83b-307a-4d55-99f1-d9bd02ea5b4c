(function()
{
	createNamespace("TF.Control").FindTripAssignmentViewModel = FindTripAssignmentViewModel;

	function FindTripAssignmentViewModel(selectedCount, obDisableControl)
	{
		var self = this;
		this.obSpecifyRecords = ko.observableArray([
			{ value: 'allInFilter', text: 'All records in filter' },
			{ value: 'selected', disable: selectedCount == 0, text: 'Current ' + selectedCount + ' selected ' + TF.getSingularOrPluralTitle("record", selectedCount) }
		]);
		var selectedSpecifyRecord = self.obSpecifyRecords()[selectedCount > 0 ? 1 : 0];
		self.obSelectedSpecifyRecords = ko.observable(selectedSpecifyRecord.value);
		this.obDisableControl = obDisableControl;
		this.obPastDate = ko.observable(true);
		this.obCurrentDate = ko.observable(true);
		this.obFutureDate = ko.observable(true);
		this.obScheduleTo = ko.computed(() =>
		{
			return this.obPastDate() || this.obCurrentDate() || this.obFutureDate();
		});
		this.obScheduleTo.subscribe(value =>
		{
			this.obDisableControl(!value);
		});
	}

	FindTripAssignmentViewModel.prototype.apply = function()
	{
		return {
			specifyRecords: this.obSelectedSpecifyRecords(),
			scheduleTo: {
				pastDates: this.obPastDate(),
				currentDate: this.obCurrentDate(),
				futureDates: this.obFutureDate()
			}
		};
	};

})();