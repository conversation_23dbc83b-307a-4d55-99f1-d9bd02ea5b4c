﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.StudentDeletion = StudentDeletion;

	function StudentDeletion()
	{
		this.type = 'student';
		namespace.BaseDeletion.apply(this, arguments);
	}

	StudentDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);
	StudentDeletion.prototype.constructor = StudentDeletion;

	StudentDeletion.prototype.getAssociatedData = function(ids)
	{
		var associatedDatas = [];

		//var p0 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "deltastuds", "ids", "student"), {
		//	data: ids
		//}).then(function(response)
		//{
		//	associatedDatas.push({
		//		type: 'deltastuds',
		//		items: response.Items[0]
		//	});
		//});

		var p2 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "schools"), {
			paramData: {
				"@studentIds": ids.join(","),
				"@fields": "Id"
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'school',
				items: response.Items
			});
		});
		var p3 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "district", "ids", "student"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'district',
				items: response.Items
			});
		});
		var p4 = tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "altsite", "ids", "altsitebystudentIds"), {
			data: ids
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'altsite',
				items: response.Items[0]
			});
		});
		var p5 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "tripstops"), {
			paramData: {
				"studentIDs": ids.join(","),
				"@fields": "Id"
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'tripstop',
				items: response.Items[0]
			});
		});
		var p6 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "documentrelationships"), {
			paramData: {
				"dbid": tf.datasourceManager.databaseId,
				"@fields": "DocumentID",
				"@filter": "in(AttachedToID," + ids.toString() + ")",
				"AttachedToType": tf.dataTypeHelper.getId("student")
			}
		}).then(function(response)
		{
			associatedDatas.push({
				type: 'document',
				items: response.Items[0]
			});
		});

		return Promise.all([p2, p3, p4, p5]).then(function()
		{
			return associatedDatas;
		});
	}

	StudentDeletion.prototype.getDeletionData = function()
	{
		return {
			'Ids': this.deleteIds,
			'UserSettingsModel': new TF.DataModel.LocalStorageDataModel().toData()
		}
	}

	StudentDeletion.prototype.getEntityPermissions = function(ids)
	{
		this.associatedDatas = [];

		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			this.associatedDatas.push(this.type);
		}

		return Promise.resolve(this.associatedDatas);
	};

	StudentDeletion.prototype.deleteSingleVerify = function()
	{
		this.associatedDatas = [];

		var p0 = this.getEntityStatus().then(function(response)
		{
			if (response.Items[0].Status === 'Locked')
			{
				this.associatedDatas.push(this.type);
			}
		}.bind(this));

		var p1 = this.getDataStatus(this.ids, "deltastuds", "student");


		return Promise.all([p0, p1]).then(function()
		{
			return this.associatedDatas;
		}.bind(this));

	};
})();