﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListZipCodesDeletion = DataListZipCodesDeletion;

	function DataListZipCodesDeletion()
	{
		this.type = 'dataentry/list/tripalias';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Trip Alias';
		namespace.DataListBaseDeletion.apply(this, arguments);
	}

	DataListZipCodesDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListZipCodesDeletion.prototype.constructor = DataListZipCodesDeletion;

	DataListZipCodesDeletion.prototype.getAssociatedData = function(ids)
	{//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "trip", ids[0], "tripalias"))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'trip',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	}

	DataListZipCodesDeletion.prototype.publishData = function(ids)
	{//need to refresh the grid
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "tripalias", pb.DELETE), ids);
	}

})();