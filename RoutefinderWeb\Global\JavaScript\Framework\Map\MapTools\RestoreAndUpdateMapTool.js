(function()
{
	createNamespace("TF.Map").RestoreAndUpdateMapTool = RestoreAndUpdateMapTool;

	function RestoreAndUpdateMapTool()
	{
		this.travelScenarios = [];
		this.lockData = new TF.RoutingMap.TravelScenariosPalette.TravelScenariosLockData();
	}

	RestoreAndUpdateMapTool.prototype = Object.create(TF.Map.RestoreAndUpdateMapTool.prototype);
	RestoreAndUpdateMapTool.prototype.constructor = RestoreAndUpdateMapTool;

	RestoreAndUpdateMapTool.prototype.changeStreetApproveStatus = function(value)
	{
		PubSub.publish("StreetApproveStatusChange"); //used for direction palette reload travel scenarios.
		return tf.promiseAjax.put(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tfsysinfo", "StreetApprove"), {
			data: {
				InfoID: "StreetApprove",
				InfoValue: value ? "1" : "0"
			}
		});
	};

	RestoreAndUpdateMapTool.prototype.getLockInfo = function()
	{
		return this.lockData.getLockInfo();
	};

	RestoreAndUpdateMapTool.prototype.notifyPublised = function()
	{
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mapcanvasapprovestatus"));
	};

	RestoreAndUpdateMapTool.prototype.rebuildForNationalMaps = function()
	{
		this.rebuildGeocode("address point");
	};

	RestoreAndUpdateMapTool.prototype.rebuild = function()
	{
		this.publishStreetsMessageId = this.showGlobalToast({ message: "Publishing streets..." });
		this.changeStreetApproveStatus(false);
		this.updateVectorTileService();
		this.rebuildGeocode("address point");
		let rebuildStreetGeocodePromise = this.rebuildGeocode("street");
		let publishStreetPromise = this.publishTravelScenario();
		Promise.all([rebuildStreetGeocodePromise, publishStreetPromise]).then((result) =>
		{
			if (result[1] == "fail")
			{
				this.showGlobalToast({ message: "Streets failed to publish.", id: this.publishStreetsMessageId, type: 'error' });
				return;
			}
			this.showGlobalToast({ message: "Streets published successfully.", id: this.publishStreetsMessageId });
			this.changeStreetApproveStatus(true).then(() => this.notifyPublised());
			if (!result[0])
			{
				return;
			}
			TF.CreateMmpkService.instance.isActived().then(isActived =>
			{
				if (isActived)
				{
					let createMmpkMessageId,
						createMmpkMessageSubscription = TF.CreateMmpkService.instance.messageOutput.subscribe((e, data) =>
						{
							createMmpkMessageId = this.showGlobalToast({
								type: data.type, message: data.content, id: createMmpkMessageId
							});

							if (data.finished)
							{
								createMmpkMessageSubscription.dispose();
							}
						});
					TF.CreateMmpkService.instance.execute();
				}
			});
		});
	};

	RestoreAndUpdateMapTool.prototype.updateVectorTileService = function()
	{
		const url = arcgisUrls.getTFUtilitiesGPServiceTaskPath("PublishVectorBaseMap");
		let messageId = this.showGlobalToast({ message: "Updating All Vector Tile Basemap..." }),
			updateList = "Landmarks,MC,Parcel,PC,Railroads,Streets,Water",
			uniqueSuffix = Math.floor(Date.now() / 1000),
			params = {
				"FolderPath": arcgisUrls.LinuxFolderRootPath ? arcgisUrls.LinuxFolderRootPath : arcgisUrls.FileGDBPath,
				"UniqueSuffix": uniqueSuffix,
				"UpdateList": updateList
			},
			gpJobComplete = result =>
			{
				if (result.jobStatus.indexOf("failed") >= 0)
				{
					this.showGlobalToast({ type: "error", message: "Updating All Vector Tile Basemap failed.", id: messageId });
				}
				else
				{
					this.showGlobalToast({ message: "Published All Vector Tile Basemap successfully.", id: messageId });
					tf.AGSServiceUtil.updateVectorNameAndDeleteOldServices(updateList, uniqueSuffix).then(result =>
					{
						if (!result)
						{
							this.showGlobalToast({ message: "Updated Vector Tile Basemap Failed.", id: messageId });
						}
					});
				}
			};

		return tf.map.ArcGIS.geoprocessor.submitJob(url, params).then(jobTask =>
		{
			const options = {
				interval: TF.Map.BaseMap.longJobInterval,
				statusCallback: job =>
				{
					const customProgressMessages = job.messages.filter(m => m.type === "informative" && m.description?.startsWith("CustomJobProgress:"))
					if (customProgressMessages.length > 0)
					{
						const latestProgress = customProgressMessages[customProgressMessages.length - 1].description.split(":")[1].trim();
						this.showGlobalToast({
							message: `Updating All Vector Tile Basemap (${latestProgress}%)...`,
							id: messageId
						});
					}
				}
			};
			return jobTask.waitForJobCompletion(options).then(jobResult =>
			{
				return gpJobComplete(jobResult);
			}).finally(() =>
			{
				if (TF.RoutingMap.MapEditSaveHelper.isHandledService(url))
				{
					TF.RoutingMap.MapEditSaveHelper.setSaving(false);
				}
			});
		}).catch(() =>
		{
			this.showGlobalToast({ message: "Updating All Vector Tile Basemap failed.", id: messageId, type: 'error' });
		});
	};

	RestoreAndUpdateMapTool.prototype.generatePublishSets = function(setting)
	{
		return {
			updateFileGDBUrl: arcgisUrls.getTFUtilitiesGPServiceTaskPath("UpdateFileGDB"),
			firstSet: {
				mode: "CopyAndRebuild",
				nowActiveFolder: setting["InActiveServiceFolderName"],
				nowInActiveFolder: setting["ActiveServiceFolderName"]
			},
			secondSet: {
				mode: "DeleteAndCopy",
			}
		};
	};

	RestoreAndUpdateMapTool.prototype.publishTravelScenario = function()
	{
		let params = {},
			publishSets = null,
			setting = {};
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tfsysinfo"), { paramData: { "@filter": "in(InfoID,InActiveServiceFolderName,ActiveServiceFolderName,ARCGISSERVER,FileGDBPath)" } }, { overlay: false })
			.then(response =>
			{
				if (response.Items && response.Items.length)
				{
					response.Items.forEach(obj =>
					{
						setting[obj.InfoID] = obj.InfoValue;
					});

					if (setting["InActiveServiceFolderName"] && setting["ActiveServiceFolderName"])
					{
						publishSets = this.generatePublishSets(setting);
					}
				}

				if (!publishSets) return "fail";

				params["folder_path"] = arcgisUrls.LinuxFolderRootPath || arcgisUrls.FileGDBPath;
				params["service_folder"] = setting["InActiveServiceFolderName"];
				params["Delete Dataset"] = "DEL";
				params["mode"] = publishSets.firstSet.mode;

				return this.copyAndStreetPublish(publishSets.updateFileGDBUrl, params, true)
					.then(ans =>
					{
						if (ans === "success")
						{
							return this.updateInActiveServiceFolderName(publishSets.firstSet.nowActiveFolder, publishSets.firstSet.nowInActiveFolder).then(() =>
							{
								params["service_folder"] = setting["ActiveServiceFolderName"];
								params["mode"] = publishSets.secondSet.mode;
								return this.copyAndStreetPublish(publishSets.updateFileGDBUrl, params, false).then((result) =>
								{
									this.unLock();
									return result;
								});
							});
						}
						this.unLock();
						return "fail";
					});
			})
			.catch(() =>
			{
				this.unLock();
				return "fail";
			});
	};

	RestoreAndUpdateMapTool.prototype.copyAndStreetPublish = function(url, params, isShowToast)
	{
		var self = this;
		return tf.map.ArcGIS.geoprocessor.submitJob(url, params).then(jobTask =>
		{
			const options = {
				interval: TF.Map.BaseMap.longJobInterval,
				statusCallback: job =>
				{
					if (!isShowToast)
					{
						return;
					}
					const customProgressMessages = job.messages.filter(m => m.type === "informative" && m.description?.startsWith("CustomJobProgress:"))
					if (customProgressMessages.length > 0)
					{
						const latestProgress = customProgressMessages[customProgressMessages.length - 1].description.split(":")[1].trim();
						self.showGlobalToast({
							message: `Publishing Streets (${latestProgress}%)...`,
							id: self.publishStreetsMessageId
						});
					}
				}
			};
			return jobTask.waitForJobCompletion(options).then(res => 
			{
				if (res.jobStatus === "job-succeeded")
				{
					if (isShowToast)
					{
						self.showGlobalToast({ message: "Streets published successfully.", id: self.publishStreetsMessageId });
					}
					return "success";
				}
				return "fail";
			}).finally(() =>
			{
				if (TF.RoutingMap.MapEditSaveHelper.isHandledService(url))
				{
					TF.RoutingMap.MapEditSaveHelper.setSaving(false);
				}
			});
		}).catch(() =>
		{
			return "fail";
		});
	};


	RestoreAndUpdateMapTool.prototype.rebuildGeocode = function(geocodeType)
	{
		const rebuildGeocodeUrl = arcgisUrls.getTFUtilitiesGPServiceTaskPath("RepublishGeocodeService");
		const serviceUrl = geocodeType === "street" ? arcgisUrls.StreetGeocodeService : arcgisUrls.AddressPointGeocodeService;
		const serviceName = serviceUrl.slice(serviceUrl.lastIndexOf("/rest/services") + 15, serviceUrl.lastIndexOf("/GeocodeServer"));
		let messageId = this.showGlobalToast({ message: `Rebuilding ${geocodeType} geocode...` });

		let param = { "ServiceName": serviceName, "IsRun": "True", MasterFolderPath: arcgisUrls.LinuxFolderRootPath ? arcgisUrls.LinuxFolderRootPath + '/' + arcgisUrls.MasterFileFolderName : arcgisUrls.FileGDBPath + "\\" + arcgisUrls.MasterFileFolderName };

		return tf.map.ArcGIS.geoprocessor.submitJob(rebuildGeocodeUrl, param).then(jobTask =>
		{
			const options = {
				interval: TF.Map.BaseMap.longJobInterval,
				statusCallback: job =>
				{
					const customProgressMessages = job.messages.filter(m => m.type === "informative" && m.description?.startsWith("CustomJobProgress:"))
					if (customProgressMessages.length > 0)
					{
						const latestProgress = customProgressMessages[customProgressMessages.length - 1].description.split(":")[1].trim();
						this.showGlobalToast({
							message: `Rebuilding ${geocodeType} geocode (${latestProgress}%)...`,
							id: messageId
						});
					}
				}
			};
			return jobTask.waitForJobCompletion(options).then(res => 
			{
				if (res.jobStatus == "job-succeeded")
				{
					this.showGlobalToast({ message: `Rebuilt ${geocodeType} geocode successfully.`, id: messageId });
					return true;
				}
				else
				{
					this.showGlobalToast({ type: "error", message: `Rebuilt ${geocodeType} geocode failed.`, id: messageId });
					return false;
				}
			}).finally(() =>
			{
				if (TF.RoutingMap.MapEditSaveHelper.isHandledService(rebuildGeocodeUrl))
				{
					TF.RoutingMap.MapEditSaveHelper.setSaving(false);
				}
			});
		}).catch(() =>
		{
			this.showGlobalToast({ type: "error", message: `Rebuilt ${geocodeType} geocode failed.`, id: messageId });
			return false;
		});
	};

	RestoreAndUpdateMapTool.prototype.updateInActiveServiceFolderName = function(activeServiceFolderName, inActiveServiceFolderName)
	{
		return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), "tfsysinfo"), {
			data: [
				{ "Id": "ActiveServiceFolderName", "op": "replace", "path": "/InfoValue", "value": activeServiceFolderName },
				{ "Id": "InActiveServiceFolderName", "op": "replace", "path": "/InfoValue", "value": inActiveServiceFolderName },
			]
		}, { overlay: false });
	};

	RestoreAndUpdateMapTool.prototype.showGlobalToast = function(info)
	{
		return TF.TFNotification.getInstance().show(info);
	};

	RestoreAndUpdateMapTool.prototype.restoreOrUpdate = function(uploadFiles, type)
	{
		let restoreUrl = arcgisUrls.getTFUtilitiesGPServiceTaskPath("MapUpdateOrRestore"),
			params = {
				updateZipFile: JSON.stringify(uploadFiles),
				updateType: type
			};
		if (tf.authManager.hasNationalMaps())
		{
			params["is_national_maps"] = true;
		}

		return tf.map.ArcGIS.geoprocessor.submitJob(restoreUrl, params).then(jobTask =>
		{
			return jobTask.waitForJobCompletion({ interval: TF.Map.BaseMap.longJobInterval }).then(result =>
			{
				if (result.jobStatus === "job-succeeded")
				{
					const gpResultPromise = result.fetchResultData("result");
					const gpMessagePromise = result.fetchResultData("message");
					return Promise.all([gpResultPromise, gpMessagePromise]).then(data => 
					{
						return { "Result": data[0].value == "True", "Message": data[1].value };
					});
				}
				return { "Result": false, "Message": "There is an error when run MapUpdateOrRestore GP Task." };
			}).finally(() =>
			{
				if (TF.RoutingMap.MapEditSaveHelper.isHandledService(restoreUrl))
				{
					TF.RoutingMap.MapEditSaveHelper.setSaving(false);
				}
			});
		}).catch(err => ({ "Result": false, "Message": err }));
	};

	RestoreAndUpdateMapTool.prototype.lock = function()
	{
		this._changeAllTravelScenarioLockStatus(true);
	};

	RestoreAndUpdateMapTool.prototype.unLock = function()
	{
		this._changeAllTravelScenarioLockStatus(false);
	};

	RestoreAndUpdateMapTool.prototype.getAllTravelScenarios = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase("v2"), "travelscenarios")).then((data) =>
		{
			this.travelScenarios = data.Items.map(c => c.Id);
		});
	};
	RestoreAndUpdateMapTool.prototype._changeAllTravelScenarioLockStatus = function(isLocked)
	{
		const option = {
			ids: this.travelScenarios,
			extraInfo: "",
			type: "travelScenarios",
			databaseId: "-999",//All the datasources use the same travelscenaio data.
			isLock: isLocked
		};
		tf.lockData.setLock(option);
		this._clearAllSelfLock(option, isLocked);
	};

	RestoreAndUpdateMapTool.prototype._clearAllSelfLock = function(option, isLocked)
	{
		var selfLockRoutes = JSON.parse(tf.storageManager.get("selfLockRoutes", true));
		if (isLocked)
		{
			selfLockRoutes["travelScenariosLock"] = option;
		}
		else
		{
			delete selfLockRoutes["travelScenariosLock"];
		}
		tf.storageManager.save("selfLockRoutes", JSON.stringify(selfLockRoutes), true);
	};

	RestoreAndUpdateMapTool.prototype.upload = function(uploadFiles)
	{
		let promiseAll = [];
		let fileIndex = 0;
		uploadFiles.forEach(uploadFile =>
		{
			fileIndex++;
			let data = new FormData();
			data.append("f", "json");
			data.append("file", uploadFile.rawFile, fileIndex + ".zip");
			promiseAll.push(tf.map.ArcGIS.esriRequest(arcgisUrls.getTFUtilitiesGPServiceTaskPath("uploads/upload"),
				{
					body: data
				}));
		});
		return new Promise((resolve, reject) =>
		{
			Promise.all(promiseAll)
				.then(results =>
				{
					let uploadItems = [];
					results.forEach(result =>
					{
						if (result && result.data && result.data.success)
						{
							uploadItems.push(result.data.item);
						}
						else
						{
							resolve(result);
							this.unLock();
						}
					});
					resolve(uploadItems);
					return
				}, reject);
		});
	};

	RestoreAndUpdateMapTool.prototype.restartService = function(servicesNames)
	{
		return Promise.all(servicesNames.map(n => tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase("v2"), "arcgis", "Restart"), { paramData: { serviceName: n } }))).then(() => "success");
	};

	RestoreAndUpdateMapTool.prototype.updateMapWithAllProcesses = function(uploadFiles, updateType)
	{
		return this.getLockInfo().then((lockedByInfo) =>
		{
			if (lockedByInfo)
			{
				tf.promiseBootbox.alert(`Map edits are currently being saved by ${lockedByInfo.UserName} and cannot be updated map until they are finished. This will take several minutes to complete. Please try again.`);
				tf.loadingIndicator.tryHide();
				return "fail";
			}
			return tf.AGSServiceUtil.isLockedGPServiceExecuting().then((isExecuting) =>
			{
				if (isExecuting)
				{
					tf.promiseBootbox.alert(`Map edit or update tasks are currently executing and cannot be updated map until they are finished. This will take several minutes to complete. Please try again.`);
					tf.loadingIndicator.tryHide();
					return "fail";
				}
				this.getAllTravelScenarios().then(() => this.lock());
				return this.upload(uploadFiles).then(result =>
				{
					tf.loadingIndicator.subtitle("Importing maps...");
					return this.restoreOrUpdate(result, updateType)
						.then(restoreResult => 
						{
							tf.loadingIndicator.tryHide();
							if (!restoreResult.Result)
							{
								this.unLock();
								let errorMessage;
								if (restoreResult.Message.messages)
								{
									errorMessage = restoreResult.Message.messages.reduce((accumulator, current) => 
									{
										if (current.type == "error")
										{
											accumulator = accumulator + current.description;
										}
										return accumulator;
									}, "");
								}
								errorMessage = errorMessage ? errorMessage : restoreResult.Message ? restoreResult.Message : "Update maps failed."
								tf.promiseBootbox.alert(errorMessage);
								return "fail";
							}

							tf.promiseBootbox.alert(restoreResult.Message);
							return this.restartService(['TFUtilitiesGPService']);
						})
						.then(result =>
						{
							if (result == "fail")
							{
								return "fail";
							}

							if (tf.authManager.hasNationalMaps())
							{
								this.rebuildForNationalMaps();
							}
							else
							{
								this.rebuild();
								tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "TripStops/resetIsCenterOfStreet"), {}, { overlay: false }).catch(() => { });
							}

							TF.Helper.ParcelAddressPointHelper.setEditRecords({ Rebuild: false, Ids: [] });
							return "success";
						}).catch(error =>
						{
							this.unLock();
							tf.loadingIndicator.tryHide();
							console.log(error);
							tf.promiseBootbox.alert("Update maps failed.");
							return "fail";
						});
				}).catch(error =>
				{
					this.unLock();
					tf.loadingIndicator.tryHide();
					console.log(error);
					tf.promiseBootbox.alert("Upload failed.");
					return "fail";
				});
			});
		});
	};

})();