(function()
{
	createNamespace('TF.Modal').ExecuteCreateAndUpdateRuleLogModalViewModel = ExecuteCreateAndUpdateRuleLogModalViewModel;

	function ExecuteCreateAndUpdateRuleLogModalViewModel(infos, openGrid)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.title("Log");
		self.sizeCss = "modal-dialog-md";
		self.viewModel = new TF.Control.ExecuteCreateAndUpdateRuleLogViewModel(infos, openGrid, self);
		self.contentTemplate('modal/ExecuteCreateAndUpdateRuleLogControl');
		self.buttonTemplate('modal/positive');
		self.obPositiveButtonLabel("Close");

		self.data(this.viewModel);
	}

	ExecuteCreateAndUpdateRuleLogModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	ExecuteCreateAndUpdateRuleLogModalViewModel.prototype.constructor = ExecuteCreateAndUpdateRuleLogModalViewModel;

	ExecuteCreateAndUpdateRuleLogModalViewModel.prototype.positiveClick = function()
	{
		this.positiveClose();
	}
})()