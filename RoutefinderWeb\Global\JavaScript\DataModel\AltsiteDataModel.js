﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.AltsiteDataModel = function(altsiteEntity)
	{
		namespace.BaseDataModel.call(this, altsiteEntity);
	};

	namespace.AltsiteDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.AltsiteDataModel.prototype.constructor = namespace.AltsiteDataModel;

	// namespace.AltsiteDataModel.prototype.mapping = [
	// 	{ from: "DBID", default: function() { return tf.datasourceManager.databaseId; } },
	// 	{ from: "Name", default: "" },
	// 	{ from: "Contact", default: "" },
	// 	{ from: "ContactTitle", default: "" },
	// 	{ from: "Phone", default: function() { return tf.setting.userProfile.AreaCode; } },
	// 	{ from: "PhoneExt", default: "" },
	// 	{ from: "MailStreet1", default: "" },
	// 	{ from: "MailStreet2", default: "" },
	// 	{ from: "MailCity", default: function() { return tf.setting.userProfile.Mailcity; } },
	// 	{ from: "MailState", default: function() { return tf.setting.userProfile.MailState; } },
	// 	{ from: "MailZip", default: function() { return tf.setting.userProfile.Mailzip; } },
	// 	{ from: "GeoCounty", default: "" },
	// 	{ from: "GeoCity", default: "" },
	// 	{ from: "GeoStreet", default: "" },
	// 	{ from: "GeoZip", default: "" },
	// 	{ from: "Public", default: true },
	// 	{ from: "Id", default: 0, required: true },
	// 	{ from: "Xcoord", default: 0 },
	// 	{ from: "Ycoord", default: 0 },
	// 	{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
	// 	{ from: "LastUpdatedId", default: 0 },
	// 	{ from: "LastUpdatedName", default: "" },
	// 	{ from: "LastUpdatedType", default: 0 },
	// 	{ from: "System1", default: "" },
	// 	{ from: "System2", default: "" },
	// 	{ from: "System3", default: "" },
	// 	{ from: "System4", default: "" },
	// 	{ from: "StudentIds", default: [] },
	// 	{ from: "Comments", default: "" }
	// ];

	namespace.AltsiteDataModel.prototype.mapping = [
		{ "from": "Comments", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "GeoCity", "default": "" },
		{ "from": "GeoConfidence", "default": null },
		{ "from": "GeoCounty", "default": "" },
		{ "from": "GeoStreet", "default": "" },
		{ "from": "GeoZip", "default": "" },
		{ "from": "Id", "default": 0 },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "MailCity", "default": function() { return tf.setting.userProfile.MailCityName; } },
		{ "from": "MailCityId", "default": function() { return tf.setting.userProfile.MailCity; } },
		{ "from": "MailState", "default": function() { return tf.setting.userProfile.MailStateName; } },
		{ "from": "MailStateId", "default": function() { return tf.setting.userProfile.MailState; } },
		{ "from": "MailStreet1", "default": "" },
		{ "from": "MailStreet2", "default": "" },
		{ "from": "MailZip", "default": function() { return tf.setting.userProfile.MailZipName; } },
		{ "from": "MailZipId", "default": function() { return tf.setting.userProfile.MailPostalCode; } },
		{ "from": "Name", "default": "" },
		{ "from": "Public", "default": false },
		{ "from": "SiteOwnerName", "default": null },
		{ "from": "StudentIds", "default": [] },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
		{ "from": "Xcoord", "default": 0 },
		{ "from": "Ycoord", "default": 0 }
	];
})();
