.export-report {
	.radio-group {
		display: flex;
		flex-direction: row;

		.radio {
			margin-top: 5px;
			margin-bottom: 15px;
			margin-right: 30px;

			span {
				cursor: pointer;
			}
		}
	}

	.modal-button {
		color: #fff;
		background-color: #4B4B4B;
		height: 26px;
		line-height: 26px;
		min-width: 116px;
		font-size: 14px;
		text-align: center;
		cursor: pointer;
	}

	&.pde1049.data-validation {
		.kendo-grid.grid-container {
			margin-bottom: 30px;

			&.vehicle-grid {
				height: 300px;
			}

			&.error-grid {
				height: 150px;
			}

			tr {
				&:nth-child(even) td {
					background-color: #ecf2f9;
				}


				&.error {
					td {
						// important to override selection style
						background-color: red !important;
					}
				}

				&.warning {
					td {
						background-color: yellow;
					}
				}

				td.error {
					font-weight: bold;
				}
			}

			.k-edit-cell {
				input {
					border-color: #bfbfbf;
					background-color: #fff;
					padding: 0;
				}
			}

			.k-grid-footer {
				background-color: transparent;
				border: none;
				text-align: right;
				height: 30px;
				line-height: 30px;
				color: #666;
			}
		}

		.modal-button.update,
		.modal-button.error-print,
		.modal-button.error-view {
			float: right;
			margin-top: 10px;
		}

		.modal-button.error-view {
			margin-right: 10px;
		}

		.modal-button.refresh {
			width: 100px;
			margin-top: 18px;
		}

		.title {
			margin-bottom: 15px;
		}

		.search {
			margin-bottom: 10px;
		}

	}
}