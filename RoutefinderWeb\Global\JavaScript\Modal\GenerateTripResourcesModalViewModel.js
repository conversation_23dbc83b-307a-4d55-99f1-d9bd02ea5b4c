(function()
{
	createNamespace('TF.Modal').GenerateTripResourcesModalViewModel = GenerateTripResourcesModalViewModel;

	function GenerateTripResourcesModalViewModel(tripIds)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/generateTripResourcesControl');
		this.buttonTemplate('modal/positivenegative');
		this.obEnableEnter(false);
		this.viewModel = new TF.Control.GenerateTripResourcesViewModel(tripIds);
		this.data(this.viewModel);
		this.sizeCss = "modal-dialog-lg";
		this.obPositiveButtonLabel("Generate");
		this.title("Generate Trip Calendar Record");
		this.controlTabKey = true;
	}

	GenerateTripResourcesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	GenerateTripResourcesModalViewModel.prototype.constructor = GenerateTripResourcesModalViewModel;

	GenerateTripResourcesModalViewModel.prototype.positiveClick = function()
	{
		this.viewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	GenerateTripResourcesModalViewModel.prototype.dispose = function()
	{
		tfdispose(this);
	};
})();
