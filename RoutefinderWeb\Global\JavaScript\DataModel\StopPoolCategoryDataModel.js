(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.StopPoolCategoryDataModel = function(StopPoolCategoryEntity)
	{
		namespace.BaseDataModel.call(this, StopPoolCategoryEntity);
	};

	namespace.StopPoolCategoryDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.StopPoolCategoryDataModel.prototype.constructor = namespace.StopPoolCategoryDataModel;

	namespace.StopPoolCategoryDataModel.prototype.mapping = [
		{ from: "Id", default: 0, required: true },
		{ from: "Color", default: "#0000ff" },
		{ from: "Name", default: "" }
	];
})();
