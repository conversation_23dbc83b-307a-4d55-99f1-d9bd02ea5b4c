﻿(function()
{
	createNamespace("TF.Grid").KendoGridSummaryGrid = KendoGridSummaryGrid;

	function KendoGridSummaryGrid()
	{
		this.obSummaryGridVisible = ko.observable(false);
		this.summaryHeight = 83;
		if (TF.isMobileDevice)
		{
			this.summaryHeight = 65;
		}
		this.obSummaryGridVisible.subscribe(this.fitContainer, this);
		this.obSummaryGridVisible.subscribe(this._updateCurrentLayout, this);
		this.obSummaryGridVisible.subscribe(this.hiddenScrollX, this);
		this.$summaryContainer = this.$container.next(".kendo-summarygrid-container");
		this.summaryKendoGrid = null;
		this.obAggregationMap = ko.observable({});
		this.obAggregationMap.subscribe(this._updateCurrentLayout, this);
		this.bindOnClearFilterEvent();
	}

	KendoGridSummaryGrid.prototype.createSummaryGrid = function(forceCreate)
	{
		var self = this, scrollLeft = 0;
		if (!forceCreate && (!this.obSummaryGridVisible || !this.obSummaryGridVisible()))
		{
			return;
		}
		self.summaryKendoGrid = self.$summaryContainer.data("kendoGrid");
		if (self.summaryKendoGrid)
		{
			scrollLeft = self.$summaryContainer.find(".k-grid-content").scrollLeft();
			self.summaryKendoGrid.destroy();
			self.$summaryContainer.empty();
		}


		var data = {};
		self.kendoGrid.getOptions().columns.forEach(function(item)
		{
			data[item.field] = "";
		});

		self.$summaryContainer.kendoGrid({
			dataSource: {
				data: [data],
				schema: {
					model: {
						fields: self.getSummaryField()
					}
				}
			},
			height: 40,
			filterable: {
				extra: false,
				mode: "row"
			},
			columns: self.getSummaryColumns(),
			dataBound: function()
			{
				self.resetGridContainerHorizontalLayout();
			}
		});
		self.summaryKendoGrid = self.$summaryContainer.data("kendoGrid");
		self.initSummaryGridDataSource();
		self.reRenderSummaryGrid();
		self.$summaryContainer.find(".k-grid-content").scrollLeft(scrollLeft);
	};

	KendoGridSummaryGrid.prototype.createSummaryFilterClearAll = function()
	{
		var tr = this.$summaryContainer.find("div.k-grid-header-locked").find("tr.k-filter-row");
		if (tr != undefined)
		{
			var td = tr.children("td:first");
			td.text("");
			var div = $('<div class="summary-filter-clear-all"></div>');
			td.append(div);
			td.attr('title', 'Summary Bar');

			var that = this;
			div.click(function()
			{
				var buttons = that.$summaryContainer.find("tr.k-filter-row").find("button.k-button");
				if (buttons != undefined)
				{
					buttons.trigger("click");
				}
			});
		}
	};

	KendoGridSummaryGrid.prototype.initSummaryGridDataSource = function()
	{
		var aggregations = this.obAggregationMap(), filter = [];
		for (var key in aggregations)
		{
			if (aggregations[key])
			{
				this.onSummaryDropDownChange(aggregations[key], key);
				filter.push({ field: key, operator: "eq", value: aggregations[key] });
			}
		}
		this.summaryKendoGrid.dataSource.filter(filter);
	};

	KendoGridSummaryGrid.prototype.bindScrollXEvent = function()
	{
		var self = this;
		var timeoutEvent = null;
		this.$summaryContainer.find(".k-grid-content").off("scroll.summarybar").on("scroll.summarybar", function(e)
		{
			var $target = $(e.target),
				grid = self.$container.find(".k-virtual-scrollable-wrap").length > 0 ? self.$container.find(".k-virtual-scrollable-wrap") : self.$container.find(".k-grid-content");
			grid.scrollLeft($target.scrollLeft());
			clearTimeout(timeoutEvent);
			timeoutEvent = setTimeout(function()
			{
				var $target = $(e.target);
				grid.scrollLeft($target.scrollLeft());
			}, 50);
		});

	};

	KendoGridSummaryGrid.prototype.hiddenScrollX = function()
	{
		if (this.obSummaryGridVisible && this.obSummaryGridVisible())
		{
			this.$container.find(".k-grid-content,.k-virtual-scrollable-wrap").scrollLeft(0);
			this.$container.addClass("summarybar-showing");
		} else
		{
			this.$container.removeClass("summarybar-showing");
		}
	};

	KendoGridSummaryGrid.prototype.updateSummaryGridColumns = function()
	{
		if (this.summaryKendoGrid)
		{
			var scrollLeft = this.$summaryContainer.find(".k-grid-content").scrollLeft();
			var kendoOptions = this.summaryKendoGrid.getOptions();
			kendoOptions.columns = this.getSummaryColumns();
			this.summaryKendoGrid.setOptions(kendoOptions);
			this.reRenderSummaryGrid();
			this.$summaryContainer.find(".k-grid-content").scrollLeft(scrollLeft);
			this.lockSummaryFirstColumn();
		}
	};

	KendoGridSummaryGrid.prototype.lockSummaryFirstColumn = function() //lock the first column
	{
		var theadTable = this.kendoGrid.lockedHeader && this.kendoGrid.lockedHeader.find("table");
		if (this.isColumnLocked && theadTable.children("thead").children("tr").filter(":first").find("th").eq(this.resizeIdx).data("kendoField") == "bulk_menu")
		{
			var summaryHeaderTable = this.summaryKendoGrid.lockedHeader.find("table");
			var summaryBodyTable = this.summaryKendoGrid.lockedTable;
			if (summaryHeaderTable.parent().find("col").length === 1)
			{
				summaryHeaderTable.attr("style", "width:30px");
				summaryBodyTable.attr("style", "width:30px");
			}
			else//if the lock area has more than one column,keep the width of lock area donnot change
			{
				var width = 0;
				for (var i = 0; i < summaryHeaderTable.parent().find("col").length; i++)
				{
					var colItem = summaryHeaderTable.parent().find("col")[i];
					width += parseInt(colItem.style.width.substring(0, colItem.style.width.length - 2));
				}
				summaryHeaderTable.attr("style", "width:" + width + "px");
				summaryBodyTable.attr("style", "width:" + width + "px");
			}
			summaryHeaderTable.parent().find("col").eq(this.resizeIdx).attr("style", "width:30px");
			summaryBodyTable.parent().find("col").eq(this.resizeIdx).attr("style", "width:30px");
		}
	};

	KendoGridSummaryGrid.prototype.reRenderSummaryGrid = function()
	{
		this.bindScrollXEvent();
		this.createSummaryFilterClearAll();
		this.fitSummaryGrid();
	};

	KendoGridSummaryGrid.prototype.getSummaryColumns = function()
	{
		var self = this,
			columns = this.kendoGrid.getOptions().columns;
		return columns.map(function(col)
		{
			var item = $.extend({}, col);
			item.dataType = item.type;
			item.type = "string";
			if (item.field === "bulk_menu" || item.disableSummary == true)
			{
				item.title = "";
				item.headerTemplate = null;
				item.template = null;
				item.filterable = false;
				item.command = null;
				return item;
			}
			item.filterable = self.getSummaryHeader(item);

			item.template = function(dataItem)
			{
				var value = dataItem[item.field + "_SummaryValue"];
				if (typeof (value) !== "undefined" && value)
				{
					return value;
				}
				return "";
			};
			return item;
		});
	};

	KendoGridSummaryGrid.prototype.getSummaryField = function()
	{
		var fields = {};
		this._gridDefinition.Columns.forEach(function(definition)
		{
			var field = {};
			field.type = "string";
			fields[definition.FieldName] = field;
		});
		return fields;
	};

	KendoGridSummaryGrid.prototype.getSummaryHeader = function(column)
	{
		var self = this;
		var defaultOptions = [{ text: "Count", value: "Count" }, { text: "Distinct count", value: "DistinctCount" }],
			dateTimeOptions = [{ text: "Max", value: "Max" }, { text: "Min", value: "Min" }, { text: "Range", value: "Range" }],
			numberOptions = [{ text: "Average", value: "Average" }, { text: "Sum", value: "Sum" }],
			options;
		switch (column.dataType)
		{
			case 'datetime':
			case 'date':
			case "time":
				options = dateTimeOptions.concat(defaultOptions);
				break;
			case 'integer':
			case 'number':
				options = numberOptions.concat(dateTimeOptions).concat(defaultOptions);
				break;
			default:
				options = defaultOptions;
				break;
		}
		return {
			cell: {
				showOperators: false,
				template: function(args)
				{
					args.element.kendoDropDownList({
						dataTextField: "text",
						dataValueField: "value",
						valuePrimitive: true,
						dataSource: options,
						optionLabel: {
							text: "",
							value: ""
						},
						popup: {
							position: "bottom left",
							origin: "top left"
						},
						animation: {
							open: {
								effects: "slideIn:up"
							}
						},
						change: function(e)
						{
							var dropdown = this;
							$.proxy(self.onSummaryDropDownChange(dropdown.value(), column.FieldName), self);
							self.setObAggregationMap(column.FieldName, dropdown.value());
						}
					});
				}
			}
		};
	};

	KendoGridSummaryGrid.prototype.loadSummary = function(fieldName, operator, includeEmptyValue)
	{
		var apiPrefix = tf.api.apiPrefix();
		var param = {
			FieldName: tf.UDFDefinition.getOriginalName(fieldName),//Convert udf name to original name.
			AggregateOperator: operator,
			IncludeEmptyValue: !!includeEmptyValue,
			databaseId: tf.datasourceManager.databaseId
		};

		if (this.options.customGridType === "dashboardwidget" && this.options.widgetDBID)
		{
			param.databaseId = this.options.widgetDBID;
		}

		if (this.options.gridType === "dashboards")
		{
			apiPrefix = tf.api.apiPrefixWithoutDatabase();
			param.userId = tf.userEntity.Id;
		}

		if (this.options.gridType === "mapincident"
			|| this.options.gridType === "street"
			|| this.options.gridType === "parceladdresspoint")
		{
			apiPrefix = tf.api.apiPrefixWithoutDatabase();
		}

		if (this.options.gridType === "session")
		{
			param.databaseId = 0;
		}

		if (tf.stopfinderUtil.isStopfinderType(this.options.gridType))
		{
			param.timeOffset = tf.timezonetotalminutes;
			if (this.options.formSentId)
			{
				param.formSentId = this.options.formSentId;
			}
		}

		var url;
		if (this.options.setRequestURL)
		{
			url = this.options.setRequestURL(apiPrefix);
		}
		else
		{
			url = pathCombine(apiPrefix, "search", tf.dataTypeHelper.getEndpoint(this.options.gridType));
		}

		if (!this.searchOption || !this.searchOption.data)
		{
			return Promise.resolve();
		}

		return tf.promiseAjax.post(pathCombine(url, "aggregate"), {
			paramData: param,
			data: this.searchOption.data,
			traditional: true
		}, { overlay: this.options.customGridType !== "dashboardwidget" });
	};

	KendoGridSummaryGrid.prototype.onSummaryDropDownChange = function(operator, fieldName)
	{
		if (this.options && this.options.miniGridEditMode)
		{
			return;
		}

		if (!operator)
		{
			this.setDataSource(fieldName, operator, "");
			return;
		}

		const column = this.kendoGrid.getOptions().columns.find(function(definition)
		{
			return definition.field === fieldName;
		});

		if (!column)
		{
			return;
		}

		if (this.options.localDataSource)
		{
			let isDateType = ["date", "time", "datetime"].includes(column.type),
				value = this.getSummaryValueFromLocalData(fieldName, operator, column.includeEmptyValue, isDateType);
			this.updateDataSource(fieldName, operator, value, column);
		}
		else
		{
			(this.options.customGridType !== "dashboardwidget" && !this.options.isMiniGrid) && tf.loadingIndicator.showImmediately();
			this.loadSummary(fieldName, operator, column.includeEmptyValue).then(apiResponse =>
			{
				tf.loadingIndicator.tryHide();
				let value = "";
				if (apiResponse != null)
				{
					value = tf.stopfinderUtil.isStopfinderType(this.options.gridType) ? apiResponse : apiResponse.Items[0];
				}
				this.updateDataSource(fieldName, operator, value, column);

			}).catch(() => { tf.loadingIndicator.tryHide() });
		}
	};

	KendoGridSummaryGrid.prototype.updateDataSource = function(fieldName, operator, value, column)
	{
		if ($.isArray(value))
		{
			value = value.map(item =>
			{
				return this.getFormatedValue(column, item, operator);
			});

			value = value.join(' - ');
			value = value === " - " ? "" : value;
		} else
		{
			value = this.getFormatedValue(column, value, operator);
		}
		this.setDataSource(fieldName, operator, String(value));
	};

	KendoGridSummaryGrid.prototype.getSummaryValueFromLocalData = function(fieldName, operator, includeEmptyValue, isDateType)
	{
		let columnValues = [], result, data = this.kendoGrid.dataSource.view();

		if (!includeEmptyValue)
		{
			data = data.filter(c => c[fieldName] !== null && c[fieldName] !== undefined && c[fieldName] !== "");
		}

		if (data.length === 0)
		{
			return operator == "Count" || operator == "DistinctCount" ? 0 : '';
		}

		if (isDateType)
		{
			columnValues = data.map(a => moment(a[fieldName]));
		}
		else
		{
			columnValues = data.map(a => a[fieldName]);
		}

		switch (operator)
		{
			case "Count":
				result = columnValues.length;
				break;
			case "DistinctCount":
				result = isDateType ? [...new Set(columnValues.map(i => i.toString()))].length : [...new Set(columnValues)].length;
				break;
			case "Max":
				result = isDateType ? moment.max(columnValues) : Math.max(...columnValues);
				break;
			case "Min":
				result = isDateType ? moment.min(columnValues) : Math.min(...columnValues);
				break;
			case "Range":
				result = isDateType ? [moment.min(columnValues), moment.max(columnValues)] : [Math.min(...columnValues), Math.max(...columnValues)];
				break;
			case "Average":
				result = columnValues.reduce((sum, item) => sum + item, 0) / columnValues.length;
				break;
			case "Sum":
				result = columnValues.reduce((sum, item) => sum + item, 0);
				break;
			default:
				break;
		}

		return result;
	};

	KendoGridSummaryGrid.prototype.getFormatedValue = function(column, item, operator)
	{
		let value = $.isArray(item) ? item[0] : item;
		if (!column || !column.format)
		{
			return value;
		}

		if (["Count", "DistinctCount"].includes(operator))
		{
			return value;
		}

		if (column.formatSummaryValue)
		{
			return column.formatSummaryValue(item);
		}

		if (["date", "time", "datetime"].includes(column.type))
		{
			if (column.isUTC)
			{
				let dt = utcToClientTimeZone(value);
				value = dt.isValid() ? dt.format("MM/DD/YYYY hh:mm A") : value;
			}

			let momentValue = moment(value);
			let validDateValue = value;
			if (column.type === "time")
			{
				validDateValue = convertToMoment(value);
				momentValue = moment(validDateValue);
			}

			if (value !== 0 && new Date(validDateValue) != 'Invalid Date' && momentValue.isValid() === true && validDateValue !== 0)
			{
				value = momentValue.toDate();
				return kendo.format(column.format, value);
			}

			return "";
		}

		// for system number question: add commas
		let columnFormat = column.format;
		let questionFieldOpt = column.questionFieldOptions;
		if (questionFieldOpt?.TypeId === 13 && questionFieldOpt?.SystemFieldType === "Number")
		{
			columnFormat = String.format("{0:n{0}}", column.Precision);
		}

		value = tf.measurementUnitConverter.aggregateConvert(value, operator, column);

		return kendo.format(columnFormat, value);
	};

	KendoGridSummaryGrid.prototype.setDataSource = function(fieldName, operator, value)
	{
		var data = this.summaryKendoGrid.dataSource.data();
		if (!data || (data.length === 0 && !data[0]))
		{
			data = [{}];
		}
		data[0][fieldName] = operator;
		data[0][fieldName + "_SummaryValue"] = value;
		this.summaryKendoGrid.dataSource.data(data);
		this.fitSummaryGrid();
	};

	KendoGridSummaryGrid.prototype.bindOnClearFilterEvent = function()
	{
		this.$summaryContainer.delegate(".k-filter-row button", "click", function(e)
		{
			var field = $(e.target).closest("[data-kendo-field]").attr("data-kendo-field");
			this.onSummaryDropDownChange("", field);
			this.setObAggregationMap(field, "");
		}.bind(this));
	};

	KendoGridSummaryGrid.prototype.setObAggregationMap = function(field, operator)
	{
		this.obAggregationMap()[field] = operator;
		this.obAggregationMap(this.obAggregationMap());
	};

	KendoGridSummaryGrid.prototype.fitSummaryGrid = function()
	{
		this.$summaryContainer.find('.k-grid-content,.k-grid-content-locked').height(this.summaryHeight);
	};

})();
