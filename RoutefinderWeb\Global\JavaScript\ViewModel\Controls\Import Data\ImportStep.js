(function()
{
	createNamespace("TF.ImportAndMergeData").ImportStep = ImportStep;

	function ImportStep(data)
	{
		TF.Control.BaseWizardStepViewModel.call(this, data);
		this.template = "modal/import data/Import";
		this.name = ko.observable("Import Data");
		if (this.data.type == TF.ImportAndMergeData.ImportAndMergeDataType.TransfinderDataSource)
		{
			this.name("Import Data (Step 6 of 6)");
		}

		this.description("Processing Data");
		this.importing = ko.observable(false);
		this.imported = ko.observable(false);
		this.resultObject = {};
		this.result = ko.observable("");
		this.invokeImport();
		this.timeoutList = [];
	}

	ImportStep.prototype = Object.create(TF.Control.BaseWizardStepViewModel.prototype);

	ImportStep.prototype.constructor = ImportStep;

	ImportStep.prototype.refreshImportStatus = function(operationId, resolve, importDataOptions)
	{
		var self = this;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "operations", operationId), null, { overlay: false }).then(function(response)
		{
			var status = response.Items[0].Status;
			if (status.Finished)
			{
				self.finishImport(operationId);
				resolve({ result: status, options: importDataOptions });
				return;
			}

			TF.updateProcessStatus({ Percentage: self.calculateProcessPercentage(status) * 100, Message: "Importing Data..." });
			let timeout = this.setTimeout(function() { self.refreshImportStatus(operationId, resolve, importDataOptions); }, 5000);
			self.timeoutList.push(timeout);
		}).catch(err =>
		{
			self.finishImport(operationId);
			self.result(err.Message);
			// try to release db lock.
			TF.retry(() =>
			{
				return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), "databases"), {
					paramData: {
						ids: `${importDataOptions.SourceDBID},${importDataOptions.TargetDBID}`
					},
					data: [
						{
							Op: "replace",
							Path: "Locked",
							Value: false
						},
						{
							Op: "replace",
							Path: "LockReason",
							Value: null
						},
						{
							Op: "replace",
							Path: "LockTime",
							Value: null
						}
					]
				})
			});
		});
	};

	ImportStep.prototype.calculateProcessPercentage = function(status)
	{
		var currentTableResult = status.CurrentTableResult;
		if (!currentTableResult || !status.TotalTableCount)
		{
			return 0;
		}

		var currentPercentage = Math.min(0.99, currentTableResult.TotalSourceCount ? currentTableResult.ProcessedCount / currentTableResult.TotalSourceCount : 0);
		return Math.min(0.99, (currentTableResult.Index + currentPercentage) / status.TotalTableCount);
	};

	ImportStep.prototype.finishImport = function(operationId)
	{
		tf.loadingIndicator.tryHide();
		this.timeoutList.forEach(e => { clearTimeout(e) });
		tf.promiseAjax.delete(pathCombine(tf.api.apiPrefixWithoutDatabase(), "operations", operationId), null, { overlay: false });
	};

	ImportStep.prototype.refreshImport = function()
	{
		var self = this,
			finishResolve,
			finishPromise = new Promise(function(resolve)
			{
				finishResolve = resolve;
			});
		self.refreshImportStatus(self.data.operationId, finishResolve, self.data.operationOptions);
		return finishPromise;
	};

	ImportStep.prototype.invokeImport = async function()
	{
		this.importing(true);
		this.refreshImport().then(function(result)
		{
			this.importing(false);
			this.afterImport(result.result, result.options).then(function()
			{
				this.imported(true);
			}.bind(this));
		}.bind(this));
	};

	ImportStep.prototype.execute = function()
	{
		return Promise.resolve(true);
	};

	ImportStep.prototype.afterImport = async function(result, importDataOptions)
	{
		var self = this, promise = Promise.resolve(),
			affectedStudents = result.AffectedStudents || [],
			affectedStudentIds = affectedStudents.map(function(i) { return i.Id; }),
			dbid = tf.datasourceManager.databaseId;

		//RW-34081 Move the UpdateStudentSystemAddress function to API.
		$.each(result.TableResults, function(name, tableResult)
		{
			var lowCaseName = tableResult.Name.toLowerCase();
			if (lowCaseName == "student" && affectedStudents.length)
			{
				var config = importDataOptions.TableConfigs.find(function(c)
				{
					return TF.ImportAndMergeData.ImportDataType.findById(c.DataType != null ? c.DataType : c.dataType).name.toLowerCase() == "student";
				});

				if (config.isGeocode)
				{
					promise = promise.then(function()
					{
						return self.findStudentGeocode(config, affectedStudents);
					});
				}

				if (config.needFindPopulationRegion)
				{
					promise = promise.then(function()
					{
						return self.findPopulationRegion(affectedStudents);
					});
				}

				if (config.needFindSchedule)
				{
					tf.loadingIndicator.show(true);
					var process = { Percentage: 0, Message: "Finding Schedule..." };
					TF.updateProcessStatus(process);
					promise = promise.then(function()
					{
						var findScheduleForStudentViewModel = new TF.Control.FindScheduleForStudentViewModel(
							{
								studentIds: affectedStudentIds,
								interactive: false,
								createDoorToDoorStops: config.createDoorToDoor,
								useStopsInStopPool: config.useStopPool,
								selectedStopPoolCategoryId: config.stopPoolId,
								autoAssign: true,
							}
						);
						return findScheduleForStudentViewModel.autoSchedule(affectedStudentIds, config.createDoorToDoor, config.useStopPool, config.stopPoolId, dbid, process)
							.then(function()
							{
								tf.loadingIndicator.tryHide();
							});
					});
				}

				var residenceIds = config.residenceIds || [];
				if (config.needFindSchoolResidence && residenceIds.length)
				{
					promise = promise.then(function()
					{
						return TF.AutomationHelper.queryRedistSchool(residenceIds, dbid).then(function(residenceSchools)
						{
							tf.loadingIndicator.show(true);
							var progressStatus = { Percentage: 0, Message: 'Finding Residence...' };
							TF.updateProcessStatus(progressStatus);
							var index = 0;
							return TF.seriesRun(affectedStudents, 20, function(subCollection)
							{
								var promises = [], newData = [];
								subCollection.forEach(function(student)
								{
									promises.push(TF.AutomationHelper.findSchoolResidenceByResidenceSchools(student, residenceSchools).then(function(result)
									{
										TF.updateProcessStatus({ Percentage: index / affectedStudentIds.length * 100, Message: 'Finding Residence...' });
										index++;
										if (result)
										{
											newData.push({ "Id": student.Id, "op": "replace", "path": "/ResidSchool", "value": student.ResidSchool });
										}
									}));
								});
								return Promise.all(promises).then(function()
								{
									if (newData.length > 0)
									{
										return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("student")), {
											data: newData,
										});
									}
								})
							}, true).then(() =>
							{
								tf.loadingIndicator.tryHide();
							});
						});
					});
				}

				return;
			}
		});

		tf.UDFDefinition.loadAll();

		promise.then(function()
		{
			self.resultObject = result;
			self.showResult();
		}.bind(self));
	};

	ImportStep.prototype.findStudentGeocode = function(config, students)
	{
		var needGeocodeStudents = Enumerable.From(students).Where((c) => { return !c.Xcoord; }).ToArray();
		if (config.isGeocode && needGeocodeStudents.length > 0)
		{
			tf.loadingIndicator.show(true);
			TF.updateProcessStatus({ Percentage: 0, Message: "Geocoding..." });
			return TF.AutomationHelper.findGeoCode(needGeocodeStudents, config.geocodeSource).then(() =>
			{
				let data = TF.AutomationHelper.getUpdateStudentGeoInfo(needGeocodeStudents);
				return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("student")), {
					data: data,
				}).then(() =>
				{
					tf.loadingIndicator.tryHide();
				});
			});
		}
	};

	ImportStep.prototype.findPopulationRegion = function(students)
	{
		let needFindStudents = students.filter(x => !!x.Xcoord);
		if (needFindStudents.length === 0)
		{
			return;
		}

		tf.loadingIndicator.show(true);
		TF.updateProcessStatus({ Percentage: 0, Message: "Finding Population Region..." });

		return tf.promiseAjax.patch(TF.Helper.ApiUrlHelper.patchStudentPopulationRegionsUrl(), {
			data: needFindStudents.map(stud => stud.Id),
			paramData: {
				batchPopulatePopulationRegion: true
			}
		}).then(() =>
		{
			tf.loadingIndicator.tryHide();
		});
	};

	ImportStep.prototype.updateImportStudentsSysAddresses = function(students)
	{
		var promises = [];
		students.forEach(s =>
		{
			if (s.Xcoord && s.Ycoord)
			{
				var location = new tf.map.ArcGIS.Point({ x: s.Xcoord, y: s.Ycoord, spatialReference: { wkid: 4326 } });
				promises.push(TF.locationToAddress(location));
			} else
			{
				promises.push(Promise.resolve(false));
			}
		});
		return Promise.all(promises).then((addresses) =>
		{
			var data = [];
			students.forEach((s, index) =>
			{
				if (addresses[index] && s.Xcoord && s.Ycoord)
				{
					data.push({
						"Id": s.Id, "op": "replace", "path": "/" + TF.Grid.GeocodeTool.getAddressFieldNameByGridType("street", "student"), "value": addresses[index].Street
					});
					data.push({
						"Id": s.Id, "op": "replace", "path": "/" + TF.Grid.GeocodeTool.getAddressFieldNameByGridType("zip", "student"), "value": addresses[index].Postal
					});
					data.push({
						"Id": s.Id, "op": "replace", "path": "/" + TF.Grid.GeocodeTool.getAddressFieldNameByGridType("city", "student"), "value": addresses[index].City
					});
					data.push({
						"Id": s.Id, "op": "replace", "path": "/" + TF.Grid.GeocodeTool.getAddressFieldNameByGridType("state", "student"), "value": addresses[index].Region
					});
				}
			});
			return tf.promiseAjax.patch(pathCombine(tf.api.apiPrefix(), tf.dataTypeHelper.getEndpoint("student")), {
				data: data,
			});
		})

	}

	ImportStep.prototype.showResult = function()
	{
		var result = this.getResultMessage(false);
		this.result(result);
	};

	ImportStep.prototype.downloadLog = function()
	{
		var result = this.getResultMessage(true);
		TF.saveStringAs(result, "text/plain", "importDataLog_" + moment().format("YYYYMMDDHHmm") + ".txt");
	};

	ImportStep.prototype.getResultMessage = function(includeError)
	{
		var self = this, resultBuilder = [], result = self.resultObject;
		resultBuilder.push("Imported " + result.TotalTableCount + " table(s)");
		resultBuilder.push("Processed " + result.ProcessedRowCount + " record(s)");
		if (result.Error && includeError)
		{
			resultBuilder.push("Error: " + result.Error);
		}

		$.each(result.TableResults, function(name, tableResult)
		{
			if (tableResult.Messages && tableResult.Messages.length)
			{
				resultBuilder.push("=========================================================");
				resultBuilder.push("Messages:");
				resultBuilder = resultBuilder.concat(tableResult.Messages);
			}

			if (tableResult.Errors && tableResult.Errors.length && includeError)
			{
				resultBuilder.push("=========================================================");
				resultBuilder.push("Errors:");
				resultBuilder = resultBuilder.concat(tableResult.Errors);
			}

			resultBuilder.push("=========================================================");
			resultBuilder.push("Imported " + name + " Table:");
			resultBuilder.push("Processed " + tableResult.TotalSourceCount + " record(s)");
			resultBuilder.push("Inserted " + tableResult.InsertedCount + " record(s)");
			resultBuilder.push("Failed to insert " + tableResult.FailedCount + " record(s)");
			resultBuilder.push("Updated " + tableResult.UpdatedCount + " record(s)");
			resultBuilder.push("Deleted " + tableResult.DeletedCount + " record(s)");
			if (tableResult.SkippedDeletingCount > 0) 
			{
				resultBuilder.push("Skipped deleting " + tableResult.SkippedDeletingCount + " record(s). Record(s) are locked.");
			}
			resultBuilder.push("Skipped inserting " + tableResult.SkippedCount + " record(s)");
		});

		return resultBuilder.join("\r\n");
	}
})();