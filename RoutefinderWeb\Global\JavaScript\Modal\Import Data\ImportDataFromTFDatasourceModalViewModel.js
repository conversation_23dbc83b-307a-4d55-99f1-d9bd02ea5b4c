(function()
{
	createNamespace('TF.Modal').ImportDataFromTFDatasourceModalViewModel = ImportDataFromTFDatasourceModalViewModel;

	function ImportDataFromTFDatasourceModalViewModel(selectedDataSource)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);
		self.sizeCss = "modal-dialog-xlg";
		self.contentTemplate('modal/import data/ImportDataFromTFDatasource');
		self.buttonTemplate('modal/positivenegativeextend');
		self.title("Import/Merge Data");
		self.obPositiveButtonLabel("Back");
		self.obSaveAndNewButtonLabel("Next");
		self.obNegativeButtonLabel("Cancel");
		self.obResizable(true);
		self.viewModel = new TF.Control.ImportDataFromTFDatasourceViewModel(self, selectedDataSource);
		self.data(self.viewModel);
		self.viewModel.obHasAnyMatchOn.subscribe(function(value)
		{
			self.obSaveAndNewControl(!value);
		});

	}


	ImportDataFromTFDatasourceModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ImportDataFromTFDatasourceModalViewModel.prototype.constructor = ImportDataFromTFDatasourceModalViewModel;

	ImportDataFromTFDatasourceModalViewModel.prototype.saveAndNewClick = function()
	{
		var self = this, pageIndex = self.viewModel.obStep() + 1;
		self.viewModel.validatePage(pageIndex, TF.ImportAndMergeData.ImportAndMergeOperationType.Next).then(function(result)
		{
			if (result)
			{
				self.viewModel.obStep(pageIndex);
				self.viewModel.pageChange();
			}
		});
	}

	ImportDataFromTFDatasourceModalViewModel.prototype.positiveClick = function()
	{
		var self = this, pageIndex = self.viewModel.obStep() - 1;
		self.viewModel.validatePage(pageIndex, TF.ImportAndMergeData.ImportAndMergeOperationType.Back).then(function(result)
		{
			if (result)
			{
				self.viewModel.obStep(pageIndex);
				self.viewModel.pageChange();
			}
		});
	};

	ImportDataFromTFDatasourceModalViewModel.prototype.negativeClick = function()
	{
		var self = this;
		tf.promiseBootbox.yesNo({
			message: "Are you sure you want to cancel?",
			title: "Confirm"
		}).then(function(result)
		{
			if (result)
			{
				self.negativeClose('close');
			}
		});
	};
})();