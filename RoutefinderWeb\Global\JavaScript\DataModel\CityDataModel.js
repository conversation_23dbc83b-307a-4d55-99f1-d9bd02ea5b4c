﻿(function ()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.CityDataModel = function (cityEntity)
	{
		namespace.BaseDataModel.call(this, cityEntity);
	}

	namespace.CityDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.CityDataModel.prototype.constructor = namespace.CityDataModel;

	namespace.CityDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" }
	];
})();