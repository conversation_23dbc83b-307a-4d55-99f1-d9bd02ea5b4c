(function()
{
	createNamespace('TF.Modal').ListMoverForAdjustSupportedLanguagesModalViewModel = ListMoverForAdjustSupportedLanguagesModalViewModel;

	function ListMoverForAdjustSupportedLanguagesModalViewModel(selectedData, options)
	{
		options.displayCheckbox = false;
		// options.displayAddButton = true;
		// options.showRemoveColumnButton = true;
		//options.mustSelect = false;
		selectedData = selectedData.map(function(item) { return item; });
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverForAdjustSupportedLanguagesViewModel = new TF.Control.ListMoverForAdjustSupportedLanguagesViewModel(selectedData, options);
		this.data(this.ListMoverForAdjustSupportedLanguagesViewModel);
	}

	ListMoverForAdjustSupportedLanguagesModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverForAdjustSupportedLanguagesModalViewModel.prototype.constructor = ListMoverForAdjustSupportedLanguagesModalViewModel;

	ListMoverForAdjustSupportedLanguagesModalViewModel.prototype.positiveClick = function()
	{
		this.ListMoverForAdjustSupportedLanguagesViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ListMoverForAdjustSupportedLanguagesModalViewModel.prototype.negativeClick = function()
	{
		this.ListMoverForAdjustSupportedLanguagesViewModel.cancel().then(function(result)
		{
			if (result)
			{
				this.negativeClose(false);
			}
		}.bind(this));
	};

})();
