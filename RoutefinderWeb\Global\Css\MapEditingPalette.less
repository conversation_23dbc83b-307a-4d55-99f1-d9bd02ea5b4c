﻿.mapeditingpalette {
	.segmented-control {
		height: 30px;
		background-color: #f3d3dc;
		display: flex;
		justify-content: space-between;
		align-items: center;
		>div {
			flex: 1;
			justify-content: center;
			align-items: center;
			display: flex;
			background-repeat: no-repeat;
			background-position: center center;
			height: 100%;
		}
		.zone {
			background-image: url('../img/Routing Map/menuicon/zones.png');
		}
	}
}

.routingmap_panel .list .item-container .item-header.panel-grid-header.mapediting-palette-head {
	.school-boundary.icon {
		background-image: url('../img/Routing Map/menuicon/School-Black.png');
	}
	.population-region.icon {
		background-image: url('../img/Routing Map/menuicon/Parcel-Black.png');
	}
}

.parcelpalette .pannel-item-content .content-wrapper .content-container.mapediting-planning-container {
	width: 100%;
	margin-left: 0px;
	.left-column-icon-container {
		width: 32px;
		margin-right: 0px;
		.left-icon {
			justify-content: center;
			height: 16px;
			width: 16px;
			margin-left: 8px;
			&.lock {
				background-image: url('../img/Routing Map/menuicon/Lock-Black.png');
			}
		}
	}
}

.routingmap_panel .list .item-container .item-header.mapediting {
	border-bottom: none;
}

.routingmap_panel .list .item-container .mapeditingpalette .bottom-info-container {
	padding: 6px 1px;
}

.edittravelregion {
	.help-block {
		margin-top: 25px;
	}
}
