.main-body.customizeddashboard {
	flex-grow: 100;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: white;
	position: relative;

	span.k-numeric-wrap .k-select {
		display: block;
	}

	.right-doc {
		height: 100%;

		.main-body {
			height: 100%;
		}
	}
}

.dashboard-container {
	width: 100%;
	display: flex;

	&.isEditing {

		.data-point-container,
		.map-tool-btn,
		.esri-component.esri-zoom,
		.esri-component.esri-attribution,
		.dashboard-detail-view-header .buttons,
		.timelinecontrol {
			pointer-events: none;
		}

		.dashboard-detail-view-header .buttons {
			opacity: 0.2;
		}

		.grid-title {
			&:hover {
				text-decoration: none !important;
			}
		}
	}

	.ui-resizable-disabled {
		.data-point-container {
			overflow: hidden;
		}
	}
}

.tabstrip-dashboards,
.tabstrip-customdashboard,
.customizeddashboard {
	span.k-numeric-wrap .k-select {
		display: block;
	}

	&.read-mode {
		.detail-view-panel {
			.free-view {
				.dashboard-detail-view-header {
					.template-info {
						.dashboard-input.name {
							background-color: transparent;
							opacity: 1;
							-webkit-text-fill-color: #333333;

							&:hover {
								border-color: transparent;
							}
						}

						input.dashboard-input {
							max-width: 100%;
							width: 100% !important;
						}
					}

					.iconbutton.refresh-big {
						float: right;
					}
				}

				.undo-redo-container {
					display: none;
				}

				.grid-stack {
					.grid-stack-item {
						&.widget-block {
							.toolbar {
								min-width: 36px;
							}
						}
					}
				}
			}
		}
	}

	.grid-wrapper {
		.header-add-button {
			width: 160px;
		}
	}

	.data-points-panel {
		min-width: unset;

		.panel-container .content .data-point-wrapper {
			width: 100%
		}

		.panel-container {

			.page-title.detail {
				height: 70px;
				border-bottom: 2px solid #f2f2f2;

				.pageTitle {
					margin-left: 2px;
				}
			}

			.content {
				.data-point-wrapper {
					width: 100%;

					.data-point-container {
						overflow-y: hidden;
					}
				}
			}
		}
	}

	.customize-panel {
		padding: 0;
		height: 100%;
	}

	.detail-view-panel {
		.detail-view {
			.container-fluid.grid-stack-container {
				overflow-x: visible;
				min-height: auto;
				height: calc(~"100% - 70px");

				&.display-timeline {
					height: calc(~"100% - 130px");
				}
			}
		}

		.right-container {
			position: relative;
		}

		.dashboard-detail-view-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 2px solid #f2f2f2;
			padding: 6px 0;
			background-color: #fff;

			.template-info {
				display: flex;
				flex-direction: column;
				flex: 1 1 auto;
				padding: 0 15px;
				border: 0;

				input.dashboard-input {
					padding: 0;
					margin: 0;
					min-width: 340px;
					width: calc(100% - 100px) !important;
					max-width: unset;

					&.title {
						font-family: "SourceSansPro-SemiBold";
						color: #333333;
						font-size: 21px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&.description {
						height: 22px;
						font-size: 16px;
					}
				}
			}

			.buttons,
			.read-mode-buttons {
				flex: 0 0 180px;
				margin-left: 15px;
			}

			.read-mode-buttons {
				.iconbutton {
					margin-left: 20px;
					float: left;
					width: 24px;
					height: 24px;
					background-size: 24px 24px;

					&.fullscreen {
						background-image: url('../../global/img/dashboard/fullscreen_icon.svg');
						background-size: 40px;
						width: 30px;
						margin-left: 10px
					}

					&.download {
						background-image: url('../../global/img/dashboard/download.svg');
					}

					&.print {
						background-image: url('../../global/img/dashboard/print.svg');
					}
				}
			}

			.undo-redo-container {
				position: absolute;
				right: 180px;

				.undo-redo-button {
					width: 24px;
					height: 24px;
					background-size: 24px 24px;
					background-repeat: no-repeat;
					background-position: center;
					margin-left: 20px;
					float: left;
					cursor: pointer;

					&.disabled {
						opacity: 0.2;
						cursor: default;
					}

					&.undo-button {
						background-image: url(../../global/img/dashboard/undo.svg);
					}

					&.redo-button {
						background-image: url(../../global/img/dashboard/redo.svg);
					}
				}
			}
		}

		.grid-stack {
			.grid-stack-item {
				&.widget-block {
					z-index: auto;
					min-width: auto;

					.grid-stack-item-content {
						padding: 0;
						background-color: transparent;

						.text-box.text-box-container {
							text-align: left;
						}

						.k-grid-display-block {
							border: none;

							.k-grid-header th.k-header {
								border-bottom: none;
							}
						}

						.widget-title {
							font-size: 20px;
							color: #0c0c0c;
							white-space: nowrap;
							text-overflow: ellipsis;
							width: 100%;
							overflow: hidden;
							font-family: "SourceSansPro-SemiBold";
							text-align: left;

							p {
								margin: 0;
							}

							span {
								display: block;
								text-overflow: ellipsis;
								width: 100%;
								white-space: nowrap;
								overflow: hidden;
							}

							.grid-title {
								display: inline;
								cursor: pointer;
							}

							.grid-title {
								&:hover {
									text-decoration: underline;
								}

								&.mobile {
									text-decoration: underline;
								}
							}

							&.chart-title {
								text-align: center;
							}

							&.show {
								padding: 0 0 2px 5px;

								&+.map {
									height: calc(100% - 25px);
								}
							}

							p,
							span {
								cursor: pointer;
							}
						}

						&.custom-word-cloud {
							.word-cloud-container {
								height: calc(100% - 22px);

								.word-cloud-content {
									width: 100%;
									height: 100%;

									span.word-cloud-item {
										opacity: 1;
										cursor: pointer;

										&.selected {
											opacity: 1;
										}

										&.unselected {
											opacity: 0.2;
										}
									}
								}
							}
						}


						&.custom-map {
							.widget-title.show {

								~.on-map-tool,
								~.off-map-tool {
									top: 30px;
								}
							}
						}

						.grid {
							height: calc(100% - 22px);
							padding: 0;
							background-color: #ffffff;
							display: flex;
							justify-content: space-between;

							.grid-map-widget {
								float: left;
								width: 100%;

								.ui-resizable-e {
									background-color: #484848;
									z-index: 101 !important;
									width: 3px;
									right: -3px;
									display: block;
								}

								.k-grid {
									border-color: #BFBFBF;
								}

								.k-pager {
									width: calc(100% - 8px);
								}
							}

							&.empty-grid-widget {
								background-color: #ffffff;

								.kendo-grid {
									&.kendo-grid-container {
										font-size: 14px;
										background-color: #ffffff;
										text-align: center;

										.k-grid-content {
											overflow-y: hidden;
											overflow-x: auto;
										}
									}
								}

								.k-grid-display-block .k-grid-display-block {

									.k-grid-header,
									.k-grid-pager {
										display: none;
									}

									.k-auto-scrollable {
										overflow-y: auto;
									}
								}
							}

							.kendo-grid {
								&.kendo-grid-container {
									text-align: left;
									border: none;
									background: #f5f5f5;
								}

								&.kendo-summarygrid-container {
									margin-top: -36px !important;
									background-color: #EDEDED;
									border: none;

									.k-grid-content.k-auto-scrollable {
										margin-right: 0;
									}

									.k-grid-header {
										padding-right: 0 !important;

										.k-grid-header-locked {
											margin-left: -16px;
										}
									}

									.k-grid-content .k-master-row {
										border-top: 1px #c9c9c9 solid;

										td {
											background-color: #fff;
										}
									}

									tr td {
										text-align: left;
									}

									.k-grid-content-locked {
										width: 30px !important;
										margin-left: -16px;
									}
								}
							}
						}

						.map {
							height: 100%;
							padding: 0;

							&.map-view {
								width: calc(50% - 3px);
								height: 100%;
								display: none;
								z-index: 100;
							}

							.map-container {
								height: 100%;
								position: relative;

								.esri-view-user-storage {
									display: none !important;
								}

								.esri-legend {
									width: auto;

									.esri-legend__service-label {
										width: fit-content;
									}

									.esri-legend__layer-cell {
										min-width: fit-content;
									}

									.esri-legend__ramp-labels {
										width: fit-content;

										.esri-legend__ramp-label {
											width: fit-content;
										}
									}
								}
							}
						}

						.image {
							position: relative;
							width: 100%;
							height: 100%;

							.image-container {
								position: relative;
								width: 100%;
								height: 100%;
								background-repeat: no-repeat;
								background-position: center;
								background-size: contain;
							}
						}

						.website {
							position: relative;
							width: 100%;
							height: 100%;

							.website-container {
								position: relative;
								width: 100%;
								height: 100%;
								pointer-events: none;
								cursor: pointer;

								iframe {
									position: relative;
									border: 0;
									width: 100%;
									height: 100%;
									overflow: hidden;
								}

								iframe::-webkit-scrollbar {
									display: none;
								}
							}
						}

						.gauge-component {
							position: relative;

							.custom-gauge-value {
								position: absolute;
								padding: 5px;
								display: flex;
								border-radius: 50%;
								align-items: center;
								justify-content: center;
								cursor: pointer;

								span {
									cursor: pointer;
								}
							}
						}
					}

					.toolbar {
						display: none;
						position: absolute;
						right: 15px;
						top: -10px;
						background-color: rgba(255, 255, 255, 0.8);
						border: #f0f0f0 solid 2px;
						border-radius: 5px;
						z-index: 12031;
						padding: 5px;
						height: 36px;
						box-sizing: border-box;
						min-width: 130px;

						.toolbar-icon {
							color: #6e6e6e;
							width: 24px;
							height: 24px;
							cursor: pointer;
							outline: none;
							box-shadow: none;
							box-sizing: border-box;

							svg {
								fill: #848484;
								height: 24px;
								width: 24px;
								position: relative;
								top: -8px;
								left: -13px;

								&:hover {
									fill: #515151
								}
							}

							&.delete-icon {
								i {
									background: url(../../global/img/menu/Delete-Black.svg) no-repeat center center;
									position: relative;
									height: 16px;
									width: 16px;
									top: -4px;
									left: -10px;
									display: block;
									opacity: 0.6;

									&:hover {
										opacity: 0.8;
									}
								}
							}
						}
					}

					.ui-resizable-handle {
						display: flex;
					}

					&.ui-resizable-autohide {
						.toolbar {
							display: none;
						}

						.ui-resizable-handle {
							display: none;
						}
					}
				}

				&.tab-strip-stack-item {
					.grid-stack-item-content {
						.widget-block {
							.toolbar {
								top: 0;
							}
						}
					}
				}
			}
		}

		.custom-data-selector {

			.data-selector-container {
				overflow-y: auto;
				position: absolute;
				left: 0;
				right: 0;
				top: 40px;
				bottom: 0;

				.data-selector-content {
					display: flex;
					flex-direction: column;
					width: 100%;

					p {
						font-size: 16px;
						text-align: center;
						width: 100%;
					}

					.multi-select-items {
						.k-multiselect-wrap {
							position: relative;

							&:focus-visible {
								outline: none;
							}

							.k-input {
								min-width: 1px;
								width: 1px !important;

								&.k-readonly {
									min-height: 25px !important;
								}
							}

							.k-clear-value {
								display: none;
								top: calc(50% - 9px);
								right: 26px;
							}

							.k-button {
								&:active {
									color: #2E2E2E;
									background-color: #F7F7F7;
									border-color: #BFBFBF;
								}
							}
						}

						&.k-hover,
						&.k-focus {
							.k-multiselect-wrap {
								border-color: #BFBFBF;
							}
						}
					}

					.dropdown-icon {
						position: absolute;
						width: 25px;
						height: 20px;
						right: 0;
						top: 3px;
					}

					.inline-select-items {
						display: flex;
						flex-direction: row;
						flex-wrap: nowrap;
						overflow-x: initial;
						height: 60px;

						.inline-item {
							border: #BFBFBF 1px solid;
							height: 36px;
							line-height: 36px;
							padding: 0 10px;
							margin: 2px;
							white-space: nowrap;

							&.active {
								color: #FFFFFF;
								background-color: purple;
							}
						}
					}
				}

				.item-select-all,
				.item-select-content {
					display: flex;

					label {
						font-weight: normal;
					}
				}

				.item-select-content {
					flex-wrap: wrap;
				}

				.data-selector-item,
				.item-select-all {
					display: flex;
					margin: 5px;
					position: relative;
					width: fit-content;

					>label {
						margin: 0 10px 0 5px;
					}

					>input {
						&::after {
							content: '';
							position: absolute;
							left: 0;
							right: 5px;
							top: 0;
							bottom: 0;
							cursor: pointer;
						}
					}
				}
			}
		}


		.custom-date {
			overflow: auto;

			.date-container {
				padding: 10px;
				min-width: 260px;

				.is-required,
				.is-error {
					color: red;
				}

				.is-error {
					height: 20px;
					line-height: 20px;
					text-align: left;
				}

				.date-widget-content {
					display: flex;
					flex-direction: column;
				}

				.date-widget-dropdown {
					display: flex;
					flex-direction: column;
					align-items: flex-start;

					label {
						margin-bottom: 5px;
					}

					.date-dropdown {
						width: 100%;
						max-width: 400px;
						text-align: left;
					}
				}

				.date-widget-range {
					display: flex;
					max-width: 400px;
					justify-content: space-between;
					flex-wrap: nowrap;

					>div {
						display: flex;
						justify-content: space-between;
						min-width: 120px;
						flex: 0 0 40%;
						align-items: center;
						margin: 20px 0 10px 0;

						>label {
							margin-right: 5px;
						}

						height: 24px;

						.k-picker-wrap .k-select {
							height: 23px;
						}
					}
				}

				.apply-date {
					display: flex;
					justify-content: space-between;
					min-width: 30px;
					flex: 0 0 15%;
					cursor: pointer;
				}
			}

			.date-widget-slider {
				display: flex;
				padding: 0 7px;

				.k-slider-horizontal {

					.k-slider-track {
						height: 3px;
						background-color: #ccc;
						margin-top: 0;
					}

					.k-slider-selection {
						height: 3px;
						margin-top: -2px;
						background-color: #929292;
					}

					.k-draghandle {
						outline: none;
						top: 2px;
						width: 18px;
						height: 18px;
						border-radius: 50%;
						background-color: #fff;
						border: 2px #929292 solid;
					}
				}
			}
		}
	}

	.free-view {
		.dashboard-designer {
			height: 100%;

			.dashboard-view-no-permission {
				display: table;
				height: 100%;
				width: 100%;

				span {
					display: table-cell;
					text-align: center;
					vertical-align: middle;
				}
			}

			.grid-stack {
				overflow: visible;
				margin: 2px;
				min-width: calc(100% - 4px);
				min-height: calc(100% - 4px) !important;

				/* Width */
				&::-webkit-scrollbar {
					width: 2px;
				}

				/* Track */
				&::-webkit-scrollbar-track {
					background: #f1f1f1;
				}

				/* Handle */
				&::-webkit-scrollbar-thumb {
					background: #888;
				}

				/* Handle on hover */
				&::-webkit-scrollbar-thumb:hover {
					background: #555;
				}

				.horizontal-resize-snap-helper,
				.vertical-resize-snap-helper,
				.north-drag-snap-helper,
				.south-drag-snap-helper,
				.east-drag-snap-helper,
				.west-drag-snap-helper {
					position: absolute;
					border: 0;
					border-style: dotted;
					border-color: black;
					z-index: 999;
				}

				.horizontal-resize-snap-helper,
				.north-drag-snap-helper,
				.south-drag-snap-helper {
					width: 100%;
					border-top-width: 2px;
				}

				.vertical-resize-snap-helper,
				.east-drag-snap-helper,
				.west-drag-snap-helper {
					height: 100%;
					border-left-width: 2px;
				}
			}

			.custom-card {
				padding: 8px !important;
			}

			.alt-key-pressed {
				.grid-stack-item {

					.word-cloud-container {
						span.word-cloud-item:hover {
							text-decoration: underline;
						}
					}

					.card-container {
						.line-value-tag span:hover {
							text-decoration: underline;
						}
					}

					.text-box-container {
						span[data-field]:hover {
							text-decoration: underline;
						}
					}
				}
			}
		}
	}
}

.property-grid {
	position: absolute !important;
	width: 320px;
	top: 100px;
	height: calc(100% - 200px);
	right: 0;
	z-index: 12032;
	background-color: rgba(0, 0, 0, 0.4);
	cursor: move;

	.property-grid-content {
		margin: 20px;
		cursor: default;
		background-color: white;
		text-align: left;
		height: calc(100% - 40px);

		.title-bar {
			border-bottom: 1px solid #C4C4C4;
			background-color: #f2f2f2;
			line-height: 40px;
			height: 40px;
			text-align: center;
			font-weight: bold;
			font-size: 15px;
			position: relative;

			.close {
				color: #000000;
				position: absolute;
				right: 10px;
				top: 10px;
				opacity: 0.5;
				font-size: 20px;
				font-weight: bold;

				&:hover {
					opacity: 1;
				}
			}
		}

		.main-content {
			overflow-y: auto;
			padding: 15px;
			max-height: none;
			min-height: 0;
			height: calc(100% - 80px);

			.text-editor-wrapper {
				.k-combobox .k-input-inner {
					height: 23px;
				}
			}

			.k-dropdown {
				.k-dropdown-wrap {
					border: none;

					&::after {
						content: '';
						position: absolute;
						top: 0;
						right: 0;
						bottom: 0;
						left: 0;
						border: 1px solid #ccc;
						pointer-events: none;
					}

					background-color: #eee;
					border-color: #ccc;

					.k-input {
						background-color: #fff;
						height: 22px;
						cursor: pointer;
					}

					.k-select {
						height: 22px;
						min-height: 22px;
						background-color: #eee;
						line-height: 20px;
						border-left: solid 1px #BFBFBF;
						cursor: pointer;

						.k-icon {
							display: inline-block;
							width: 0;
							height: 0;
							vertical-align: middle;
							border-top: 4px solid;
							border-right: 4px solid transparent;
							border-left: 4px solid transparent;
						}

						.k-icon:before {
							content: '';
						}
					}
				}
			}

			.is-required {
				color: red;
				padding: 2px;
			}

			.error {
				color: red;
				display: none;
				font-size: 12px;
				font-weight: normal !important;
			}

			.section-title {
				margin-top: 0;
				font-weight: bold;
			}

			.section {
				margin-bottom: 10px;

				&.website-section {
					height: 60px;
				}

				>div {
					margin-bottom: 10px;

					&.linkingfilter-line-content,
					&.date-line-content,
					&.timeline-line-content {
						margin-bottom: 0;

						.linking-content,
						.line-content {
							&.minute-range {
								height: 30px;

								label {
									font-weight: bold;
								}

								.k-numerictextbox {
									width: 70px;
									float: right;

									.k-numeric-wrap {
										height: 24px;
									}
								}
							}
						}
					}

					>label {
						font-weight: bold;
					}

					&.checkbox-item,
					.property-line-item .checkbox-item {
						margin-bottom: 10px;

						label {
							position: relative;
							padding-left: 20px;
							cursor: pointer;
						}

						input {
							position: absolute;
							left: 0;
						}

						&.map {
							padding-left: 20px;
							width: 50%;
							float: left;
						}

						&.cluster {
							padding-left: 20px;
						}
					}
				}

				.cluster-settings {
					padding-left: 20px;
					margin-bottom: 10px;

					.title-header {
						width: 100%;
					}

					.k-numerictextbox {
						height: 25px !important;
						width: 125px;

						.k-input-spinner {
							display: none;
						}
					}

					.slider.slider-horizontal {
						width: 100%;

						.slider-tick-label-container .slider-tick-label {
							margin-top: 12px;
							font-size: 12px;
						}
					}

					.slider-bottom-bar {
						font-size: 12px;
						margin-top: 16px;

						.begin {
							margin: 0px -5px 0px;
						}

						.end {
							text-align: right;
							margin: -17px -5px 0px;
						}
					}
				}

				&.data-content,
				&.general-content {

					.series-name-settings,
					.name-settings {
						height: 60px;
						margin-bottom: 0 !important;
					}

					.name-header {
						font-weight: bold;
						margin-bottom: 0;
						text-align: left;
						font-family: "SourceSansPro-Regular";
						color: #333;
						font-size: 14px;
					}

					.widget-name,
					.series-name,
					.child-name {
						width: 100%;
						border: 1px solid #ccc;
						height: 22px;
						padding: 0 8px;

						&:focus-visible {
							outline: none;
						}
					}

					.top-value-container {
						label.width-80 {
							flex: 0 0 80px;
							margin: 0 !important;
						}

						.k-numeric-wrap {
							height: 22px;
							width: 60px;

							.k-select {
								width: 20px;
								display: block;

								.k-link {
									height: 9px;
									line-height: 9px;
								}
							}

							.input {
								height: 22px;
							}
						}
					}

					.series-border {
						display: inline-block;

						&.color-container>span {
							background-image: url("../../global/Img/dashboard/slash.svg") !important;

							.k-picker-wrap {
								width: 20px !important;
								height: 20px !important;
							}

							.k-selected-color {
								width: 20px;
								height: 20px;
							}

							.k-icon.k-i-line::before {
								content: none;
							}
						}
					}

					.manual-serial-chart-line-content {
						.config-item {
							display: flex;
							margin-bottom: 10px;

							&.series-color-setting {
								font-weight: bold;
							}
						}
					}
				}

				&.appearance-content {
					.filled-config .filled-color .filled.color-container>span {
						background-image: url(../../global/Img/dashboard/slash.svg);
					}

					.title-settings {
						margin-top: 20px;
						height: 60px;
						margin-bottom: 0 !important;

						.title-header {
							font-weight: bold;
							margin-bottom: 0;
							text-align: left;
							font-family: "SourceSansPro-Regular";
							color: #333;
							font-size: 14px;
						}

						.widget-title {
							width: 100%;
							border: 1px solid #ccc;
							height: 22px;
							padding: 0 8px;

							&:focus-visible {
								outline: none;
							}
						}
					}

					.legend-title {
						width: 100%;
						border: 1px solid #ccc;
						height: 22px;
						padding: 0 8px;
						margin-left: 5px;

						&:focus-visible {
							outline: none;
						}
					}

					.max-words-container {
						label.max-words-label {
							flex: 0 0 120px;
						}

						.max-words-number {
							width: 110px;
							height: 22px !important;

							.k-input-inner {
								padding-right: 30px;
							}

							.k-input-spinner.k-spin-button {
								position: absolute;
								right: 0;
								top: 0;
								z-index: 1;
								height: 18px;

								button {
									min-height: 10px;
								}
							}

						}

						.k-numeric-wrap {
							height: 22px;
							width: 110px;

							.k-select {
								width: 20px;
								display: block;

								.k-link {
									height: 9px;
									line-height: 9px;
								}
							}

							.input {
								height: 22px;
							}
						}
					}

					.font-color {
						.font-color-container {
							display: inline;
							margin-left: 60px;

							.k-select {
								display: none;
							}
						}
					}

					.font-size-dropdown {
						margin-left: 10px;
						width: 140px;

						&.min-font-size-dropdown {
							margin-left: 12px;
						}
					}

					.min-weight-container {
						.min-weight-dropdown {
							width: 100%;
						}
					}

					.sort-by-container {
						.sort-by-dropdown {
							width: 100%;
						}
					}

					.chart-color-config {
						.k-colorpicker {
							width: 20px;
						}
					}
				}

				&.setting-content {
					display: flex;
					flex-direction: column;
				}

				.border-header {
					font-weight: bold;
				}

				.chart-grid-setting {
					.appearance-item {
						&.left {
							margin-right: 10px;
						}
					}
				}

				.config-item {

					.font-color-text,
					.font-theme-text {
						padding-left: 10px;
					}

					&.font-color .font-color-container {
						.k-colorpicker {
							background-image: url(../../global/Img/dashboard/slash.svg) !important;
							width: 20px;
							height: 20px;

							.k-input-inner,
							.k-picker-wrap {
								width: 20px !important;
								height: 20px !important;
								padding: 0;
								background-color: transparent;
								border: none;

								&::after {
									content: '';
									position: absolute;
									top: 0;
									right: 0;
									bottom: 0;
									left: 0;
									border: 1px solid #888;
									pointer-events: none;
								}

								.k-color-preview-mask,
								.k-selected-color {
									width: 20px;
									height: 20px;
									cursor: pointer;
								}
							}
						}
					}

					&.target-value-config {
						display: flex;
						flex-direction: column;

						.target-parent {
							display: flex;
							margin-bottom: 10px;

							.target-checkbox {
								position: relative;
								display: flex;
								margin-right: 5px;

								.target-value {
									&::after {
										right: 0;
									}
								}
							}

							.appearance-item {
								margin-top: -2px;
							}
						}

						.target-sub {
							padding-left: 30px;

							&.disabled {
								opacity: 0.5;
								pointer-events: none;
							}

							>div {
								display: flex;
							}

							.target-line-color {
								margin-bottom: 10px;
							}

							.appearance-item {
								display: flex;
								align-items: center;

								.border {
									margin-left: 5px;
								}
							}
						}
					}
				}

				.appearance-item {
					float: left;
					font-weight: bold;

					&.left {
						margin-right: 30px;
					}

					.border,
					.background,
					.filled {
						display: inline-block;

						&.color-container>span {
							background-image: url("../../global/Img/dashboard/slash.svg") !important;

							.k-picker-wrap {
								width: 20px !important;
								height: 20px !important;
							}

							.k-selected-color {
								width: 20px;
								height: 20px;
							}

							.k-icon.k-i-line::before {
								content: none;
							}
						}

						&.thickness-container {

							.k-numeric-wrap {
								height: 20px;

								.k-select {
									width: 20px;
									display: block;

									.k-link {
										height: 9px;
										line-height: 9px;
									}
								}
							}
						}
					}
				}

				.color-container {
					right: 0px;
					display: inline-block;
					width: 20px;

					span {
						border: none;
						box-shadow: none;
						cursor: pointer;
					}

					.k-colorpicker {
						background: transparent;
					}

					.k-picker-wrap {
						background-color: transparent;
						padding-right: 0;
						padding-bottom: 0;

						&::after {
							content: '';
							position: absolute;
							top: 0;
							right: 0;
							bottom: 0;
							left: 0;
							border: 1px solid #888;
							pointer-events: none;
						}
					}

					.k-select {
						display: none;
					}
				}
			}

			.attach-image-title {
				padding: 10px 0;
			}

			.image-section {
				height: 60px;
			}

			.attach-image-block {
				border: dashed 1px #999;
				margin: 10px 0 0 0;

				.place-holder {
					padding: 3px 10px;

					.file-name {
						overflow-wrap: break-word;
					}

					.browse-file {
						color: #0000FF;
						cursor: pointer;
						text-decoration: underline;
						font-weight: bold;
					}
				}
			}

			.website-url {
				border: 1px solid #ccc;
				outline: none;
				width: 100%;
			}

			.axis-font-config,
			.card-line-title-editor-wrapper,
			.card-line-value-format-editor-wrapper {

				.k-dropdown-wrap.k-hover,
				.k-dropdown-wrap.k-focus {
					.k-clear-value {
						top: calc(50% - 2px);
					}
				}
			}

			.gauge-config-content {

				.k-dropdown-wrap.k-hover,
				.k-dropdown-wrap.k-focus {
					.k-clear-value {
						top: calc(50% - 3px);
					}
				}
			}

			.auto-refresh-content {

				.property-auto-refresh {
					display: flex;

					label {
						flex: 100px 0 0;
					}
				}
			}
		}

		.appearance-content .labels-config-content ul {
			list-style: none;
			padding: 0;
			margin: 0;

			.k-dropdown.labels-separator {
				width: 100px;
				margin-left: 5px;
			}

			li {
				position: relative;
			}
		}

		.dragover-mask {
			margin: 20px;
		}

		.upload-file-container {
			width: calc(100% - 40px);
			top: 58px;
			bottom: 60px;
			left: 20px;
		}

		.action-bar {
			border: 1px solid #dfdfdf;
			background-color: #f1f1f1;
			height: 40px;
			display: flex;
			flex-direction: row;
			align-items: center;

			.btn {
				height: 30px;
				margin-left: 10px;
				outline: none;
				box-shadow: none;

				&.tf-btn-black {
					color: #ffffff;
					border-radius: 0;
					padding: 0 20px;
					background-color: #333333;
					border-color: #444444;
				}
			}
		}

		.chart-property-window,
		.text-box-data-content,
		.card-line-content,
		.manual-serial-chart-line-content,
		.gauge-config-content {
			.editor-wrapper {
				&.k-content {
					margin: 5px !important;
					border: 0 !important;
					position: relative !important;
					top: 0 !important;
				}

				.text-editor-wrapper {
					padding-top: 2px;

					.k-editable-area {

						iframe.k-content {
							border: 0;
							position: relative;
							top: 0;
						}
					}
				}

				.k-editor-toolbar-wrap,
				.k-editor {
					.k-editor-toolbar {
						[title="Create field"] .k-icon {
							background-image: url(../../global/Img/dashboard/textwidget-add-button.svg);
							width: 18px;
							background-size: contain;
							height: 18px;

							svg {
								display: none;
							}
						}

						.k-tool-group {
							max-width: 230px;

							.k-i-Undo {
								&::before {
									content: "\e100";
								}
							}

							.k-i-Redo {
								&::before {
									content: "\e101";
								}
							}

							.k-i-createfield {
								background-image: url(../../global/Img/dashboard/textwidget-add-button.svg);
								background-size: contain;
								width: 18px;
								height: 18px;
							}
						}
					}
				}

				.html-editor-wrapper {
					min-height: 300px;

					textarea {
						width: calc(100% - 12px);
						height: calc(100% - 14px);
						min-height: 300px;
						margin: 6px 4px 0 6px;
						border: 1px solid #C5C5C5;
						outline: none;
					}
				}
			}
		}

		.appearance-content,
		.chart-property-window,
		.card-line-content,
		.manual-serial-chart-line-content,
		.word-cloud-appearance-panel,
		.gauge-config-content {

			.widget-title-editor-wrapper,
			.editor-wrapper {
				table.k-editor {
					height: auto;
				}

				.k-editor-toolbar {
					padding: 2px 5px;

					&::before {
						height: auto;
					}

					.k-combobox {
						input {
							height: 100%;
						}
					}

					.k-tool-group {
						max-width: 220px !important;
					}

					.k-tool-group>.k-tool {
						width: 27px;
						height: 27px;
					}
				}

				.k-editable-area {
					height: 0;
				}
			}
		}

		.word-cloud-appearance-panel {
			.editor-wrapper {
				.k-editor-toolbar {
					.k-combobox {
						.k-input-inner {
							margin-right: 15px;
						}
					}
				}
			}
		}

		.chart-property-window,
		.card-line-content,
		.manual-serial-chart-line-content {
			.editor-wrapper {
				max-width: 455px;
			}
		}

		.gauge-config-content {
			.editor-wrapper {
				max-width: 295px;
			}
		}

		.chart-property-window.manual-serial-chart {
			.config-item {

				&.legend-config,
				&.legend-location-config,
				&.legend-title-config {
					display: none;
				}
			}
		}

		.k-numerictextbox.k-input {
			height: 20px !important;
			line-height: 20px;

			.k-input-spinner button {
				min-height: 10px;
			}
		}

		.color-container {
			.k-icon-picker {
				width: 20px;
				height: 20px;
				border: 1px #888 solid !important;

				.k-color-preview,
				.k-input-inner,
				.k-color-preview-mask {
					width: 18px;
					height: 18px;
					padding: 0;
				}
			}
		}
	}

	.filled-config {
		display: flex;

		>div {
			display: flex;
			flex: 0 0 50%;

			&.filled-color {
				.color-container {
					margin-right: 5px;
				}

				.k-colorpicker {
					width: 20px;
				}
			}
		}
	}

	.linkingfilter-line-content,
	.date-line-content,
	.timeline-line-content {

		.linking-property-line-item,
		.property-line-item {
			border-top: 1px solid #ccc;
			border-bottom: 0;
		}
	}

	.linking-line-add-line,
	.date-line-add-line,
	.timeline-line-add-line {
		border-top: 1px solid #ccc;
		padding-top: 10px;
	}

	.linking-property-line-item,
	.property-line-item {
		border-bottom: 1px solid #ccc;
		padding-bottom: 10px;

		.section {
			&.card-line-title {
				height: 60px;
				margin-bottom: 0 !important;
			}

			.card-line-title {
				border: 1px solid #ccc;
				outline: none;

				&:hover,
				&:focus {
					border: 1px solid #ccc;
				}
			}
		}

		.linking-header,
		.line-header {
			display: flex;
			align-items: center;
			justify-content: space-between;

			h5 {
				color: #969696;
				font-weight: bold;
			}

			.close {
				color: #000000;
				opacity: 0.5;
				font-size: 20px;
				font-weight: bold;
				cursor: pointer;

				&.disabled {
					color: #888;
					cursor: none;
				}

				&:hover {
					opacity: 1;
				}
			}
		}

		.linking-line-add-line {
			margin-bottom: 10px;
		}
	}

	.add-line {
		font-weight: bold;

		.add {
			color: #969696;
			cursor: pointer;

			&.disabled {
				display: none;
			}
		}
	}

	.docs .doc {
		bottom: 40px;
	}

	.edit-bottombar {
		border-top: 1px solid #C4C4C4;
		background-color: #f2f2f2;
		height: 40px;
		position: absolute;
		bottom: 0px;
		left: 0;
		right: 0;
		display: flex;
		flex-direction: row;
		align-items: center;

		button {
			height: 26px;
			outline: none !important;
		}

		.tf-btn-black {
			color: #ffffff;
			border-radius: 0;
			padding: 0 20px;
			background-color: #333333;
			border-color: #444444;
			margin-left: 30px;
		}
	}

	.data-point-wrapper {
		.filter-bar {
			display: none;
		}
	}

	.apply-filter-to {
		.k-multiselect {

			&.k-hover,
			&.k-focus {
				box-shadow: none;

				.k-multiselect-wrap {
					border-color: #bfbfbf;
				}
			}

			.k-multiselect-wrap {
				cursor: pointer;

				.k-input {
					min-height: unset;
					height: 20px;
				}

				.dropdown-icon {
					position: absolute;
					right: 1px;
					text-align: center;
					background-color: #eee;
					border-left: solid 1px #BFBFBF;
					height: calc(~"100% - 2px");
					width: 20px;
					cursor: pointer;

					.k-icon {
						display: inline-block;
						width: 0;
						height: 0;
						vertical-align: middle;
						border-top: 4px solid;
						border-right: 4px solid transparent;
						border-left: 4px solid transparent;

						&::before {
							content: '';
						}
					}
				}
			}

			.k-clear-value {
				display: none;
			}
		}
	}
}

.card-container {
	display: grid;
	height: 100%;
	overflow: hidden;

	&.row-1 {
		grid-template-rows: 100%;

		.card-widget-line-item.first-line-item {
			.widget-line-value {
				font-size: 60px;
			}
		}
	}

	&.row-2 {
		grid-template-rows: 85% 15%;
	}

	&.row-3 {
		grid-template-rows: 70% 15% 15%;
	}

	&.row-4 {
		grid-template-rows: 55% 15% 15% 15%;
	}

	.card-widget-line-item {
		display: grid;
		grid-template-rows: 100%;
		grid-template-columns: 80% 20%;
		align-items: center;
		font-size: 16px;
		max-width: 100%;
		overflow: hidden;

		.widget-line-title,
		.widget-line-value {
			white-space: pre;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.widget-line-title {
			padding-left: 10px;
			text-align: left;
			font-family: "SourceSansPro-SemiBold";
			color: #0c0c0c;
		}

		.widget-line-value {
			text-align: right;
			padding-right: 10px;
		}

		&.first-line-item {
			grid-template-columns: 100%;
			grid-template-rows: 35% 65%;

			.widget-line-title {
				text-align: center;
				padding-right: 10px;
			}

			.widget-line-value {
				text-align: center;
			}
		}

		p,
		span {
			cursor: pointer;
		}

		.line-title-tag,
		.line-title-tag span {
			overflow: hidden;
			max-width: 100%;
			white-space: pre;
			text-overflow: ellipsis;
			display: block;
		}

		.line-value-tag span {
			cursor: pointer;
		}
	}
}

.custom-text-box {

	p,
	span,
	div,
	td,
	h1,
	h2,
	h3,
	h4,
	h5 {
		cursor: pointer;
	}
}

.empty-card-widget,
.empty-pie-chart,
.empty-serial-chart,
.empty-manual-serial-chart,
.empty-text-box-widget,
.empty-data-selector-widget,
.empty-word-cloud-widget,
.empty-website-widget,
.empty-gauge,
.empty-date-widget {
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center center;
}

.empty-pie-chart {
	background-image: url("../../global/Img/Widgets/Pie Chart unselected.svg");

	svg {
		display: none;
	}
}

.empty-serial-chart {
	background-image: url("../../global/Img/Widgets/Serial Chart unselected.svg");

	svg {
		display: none;
	}
}

.empty-manual-serial-chart {
	background-image: url("../../global/Img/Widgets/Manual Serial Chart unselected.svg");

	svg {
		display: none;
	}
}

.empty-card-widget {
	background-image: url("../../global/Img/Widgets/Card unselected.svg");
}

.empty-text-box-widget {
	background-image: url("../../global/Img/Widgets/text box unselected.svg");
	display: flex;
	width: 100%;
	height: 100%;
}

.empty-data-selector-widget {
	background-image: url("../../global/Img/Widgets/data selector unselected.svg");
	background-position: center -20px;
}

.empty-word-cloud-widget {
	background-image: url("../../global/Img/Widgets/wordcloud unselected.svg");
	background-position: center center;
}

.empty-date-widget {
	background-image: url("../../global/Img/Widgets/date unselected.svg");
	display: flex;
	width: 100%;
	height: 100%;
}

.empty-website-widget {
	background-image: url("../../global/Img/Widgets/website unselected.svg");
	cursor: pointer;
}

.empty-gauge {
	background-image: url("../../global/Img/Widgets/radial gauge unselected.svg");

	svg {
		display: none;
	}
}

.custom-chart-widget {
	position: relative;

	.k-chart,
	.k-stockchart {
		height: auto;
	}

	svg text {
		letter-spacing: 0;
		font-weight: normal;
	}
}

.tabstrip-dashboards,
.tabstrip-customdashboard,
.customizeddashboard {
	.custom-grid {

		.kendo-grid.k-grid-lockedcolumns {

			.k-grid-header-locked,
			.k-grid-content-locked {
				width: 30px !important;
			}
		}

		.kendo-grid.kendo-grid-container.summarybar-showing {
			background: #fff !important;

			.k-grid-content-locked {
				background: #f5f5f5;
			}
		}

		.kendo-summarygrid-container .k-grid-content {
			background-color: #eee;
		}

		.kendo-grid.kendo-summarygrid-container {
			.k-grid-header {
				z-index: 90;
			}
		}
	}
}

.dashboard-widget-loading {
	display: none;

	.spinner {
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: auto;

		.subtitle-text {
			font-size: 16px;
		}
	}

	.spinner-icon-container {
		height: 45px;
	}

	.cover-mouse-event-region {
		top: 0;
		right: 0;
		width: 300px;
		height: 50px;
		position: fixed;
		display: none;
	}
}

.chart-property-window {
	label {
		.bold {
			font-weight: bold !important;
		}

		.normal {
			font-weight: normal !important;
		}
	}

	.legend-config-item {
		&.legend-disabled {
			opacity: 0.5;
		}
	}
}

.property-grid-content {
	.k-editor .k-editor-toolbar .k-input {
		width: 107px;
	}

	.config-item {
		display: flex;
		flex-direction: row;
		position: relative;

		&.indentation {
			padding-left: 30px;
			margin-top: -5px;

			li {
				display: flex;
				flex-direction: row;
				margin-bottom: 5px;
			}

			&.disabled {
				opacity: 0.5;
				pointer-events: none;

				&::after {
					content: '';
					opacity: 0;
					position: absolute;
					left: 0;
					right: 0;
					top: 0;
					bottom: 0;
				}
			}
		}

		&.chart-title-config {
			flex-direction: column;

			input {
				border: 1px #ccc solid;
				outline: none;
			}
		}

		.rotated-text {
			width: 120px;
		}

		input[type="checkbox"]:not(.date-option-input) {
			margin-right: 5px;

			&::after {
				content: '';
				position: absolute;
				cursor: pointer;
				left: 0;
				right: 50%;
				top: 0;
				bottom: 0;
			}
		}

		.dropdown {
			width: 100%;
		}

		.error {
			color: red;
			display: none;
			font-size: 12px;
			font-weight: normal !important;
		}

		&.date-options-setting {
			flex-wrap: wrap;

			.radio-group {
				width: 70px;
			}
		}

		.k-editor-toolbar {
			.k-toolbar-item {
				.k-combobox {
					.k-input-inner {
						border: 0;
					}
				}
			}
		}

		.date-option-input {
			cursor: pointer;
		}
	}


	.k-dropdownlist {
		.k-button {
			background-color: #eee;
			border-left: solid 1px #bfbfbf;
		}

		&.k-hover {
			.k-button {
				background-color: #eee;
			}
		}
	}

	.k-dropdowntree {
		.k-clear-value {
			position: absolute;
			top: 1px;
			right: 25px;
			z-index: 1;
			display: none;
		}

		&.k-hover {
			.k-clear-value {
				display: inline-flex;
			}
		}
	}
}

.gauge-property-window {
	.gauge-config-content {

		.config-item {
			display: flex;
			margin-bottom: 10px;

			label.width-80 {
				flex: 0 0 80px;
				margin: 0 !important;
			}

			input[type="text"],
			input[type="number"] {
				height: 22px;
				outline: none;
				border: 1px #bfbfbf solid;
				flex: 1 1 auto;
				min-width: 0;
			}

			input[type="radio"] {
				&::after {
					content: '';
					cursor: pointer;
					width: 40%;
					position: absolute;
					left: 0;
					right: 0;
					top: 0;
					bottom: 0;
				}
			}

			&.not-grid {
				display: flex;
			}

			&.label-position-config {
				margin-bottom: 15px;
			}

			.radio-group {
				display: flex;
				margin-right: 20px;
				position: relative;

				label {
					margin-left: 5px;
				}

				input[type="radio"] {
					&::after {
						width: 80%;
					}
				}
			}

			&.show-pointer-config,
			&.show-value-config {
				label {
					margin-left: 5px;
				}
			}

			&.editor-wrapper {
				input.k-input {
					border: none;
				}

				.k-editor-toolbar {

					.k-tool-group {
						max-width: 190px !important;

						.k-button[title="Bold"] {
							display: none;
						}
					}
				}
			}

			&.color-wrapper,
			.config-range-list-item .color-wrapper {
				>label {
					margin-right: 10px;
				}

				span {
					&.k-colorpicker {
						width: 22px;

						.k-color-preview-mask {
							cursor: pointer;
						}
					}

					.k-select {
						display: none;
					}

					.k-picker-wrap {
						width: 20px !important;
						height: 20px !important;
						padding-right: 20px;
					}

					.k-selected-color {
						width: 20px;
						height: 18px;
						cursor: pointer;
					}

					.k-icon.k-i-line::before {
						content: none;
					}
				}
			}

			.config-range-list-item {
				.color-wrapper {
					margin-bottom: 0 !important;
				}

				.error {
					height: 16px;
					padding-left: 80px;
					visibility: hidden;
					margin-bottom: 0 !important;
				}
			}

			&.config-range-list {
				flex-direction: column;

				.config-range-list-content {
					display: flex;
					flex-direction: column;

					.config-range-list-item {
						display: flex;
						flex-direction: column;
						margin-bottom: 10px;

						>div {
							display: flex;
							margin-bottom: 10px;
						}

						.range-list-item-title {
							color: #969696;
							font-weight: bold;
							display: flex;
							justify-content: space-between;

							.range-list-close {
								width: 20px;
								text-align: center;
								cursor: pointer;
							}
						}
					}
				}

				.add-range {
					color: #969696;
					font-weight: bold;
					cursor: pointer;
				}
			}
		}

		.error.needle-value-error {
			visibility: hidden;
			margin-top: -10px;
			display: flex;
		}

		.value-font-config,
		.label-font-config {
			.k-editor-toolbar {
				.k-toolbar-item {
					max-width: 88px;
					margin-right: 4px;
				}
			}
		}
	}
}

.k-list-scroller {

	ul li {
		span {
			&.filter-system {
				color: grey;
			}
		}

		&.split-line {
			position: relative;
			padding: 5px 4px !important;

			&::after {
				position: absolute;
				left: 4px;
				right: 4px;
				top: 0;
				height: 1px;
				content: '';
				border-top: 1px grey solid !important;
			}
		}
	}
}

.k-editor-dialog {
	.k-picker-wrap {
		.k-selected-color {
			width: 20px;

			.k-i-line {
				font-size: 16px;
				margin-top: -10px;
				margin-left: 2px;
			}
		}
	}

	.k-icon {

		.k-i-arrow-60-down,
		.k-i-arrow-60-up {
			&::before {
				height: 18px;
			}
		}
	}
}

.k-editor-dialog {

	#k-table-properties,
	#k-cell-properties {
		.k-edit-field .k-picker-wrap {
			.k-select {
				border: 0;
			}

			.k-selected-color {
				width: 22px;
			}
		}
	}
}

.customized-dashboard-settings {
	.section {
		float: left;
		width: 100%;
		margin-bottom: 30px;

		.section-title {
			font-size: 18px;
			font-weight: bold;
			margin-bottom: 8px;
		}

		.item-description {
			margin-bottom: 8px;
			font-family: 'SourceSansPro-SemiBold';
		}

		.section-item {
			float: left;

			.item {
				float: left;

				.k-colorpicker {
					width: 20px !important;
					height: 20px !important;
					margin-right: 10px;

					.k-input-inner {
						width: 20px !important;
						height: 20px !important;
						padding: 0;

						.k-color-preview {
							border-color: #555;
						}

					}
				}

				.k-picker-wrap {
					width: 20px !important;
					height: 20px !important;
					padding: 0;
				}

				input[name='timeline'] {
					position: relative;
					cursor: pointer;
				}

				.k-picker-wrap {
					background-color: transparent;
					padding-right: 0;
					padding-bottom: 0;
					border: none;

					&::after {
						content: '';
						position: absolute;
						top: 0;
						right: 0;
						bottom: 0;
						left: 0;
						border: 1px solid #888;
						pointer-events: none;
					}
				}

				.k-selected-color {
					width: 20px;
					height: 20px;
					cursor: pointer;
				}

				.k-numerictextbox {
					height: 20px !important;
					line-height: 20px;

					.k-input-spinner button {
						min-height: 10px;
					}

					.k-select {
						width: 20px;
						display: block;

						.k-link {
							height: 9px;
							line-height: 9px;
						}
					}
				}
			}

			.item-label {
				float: left;

				&.thickness {
					padding: 3px 0 0 10px;
				}

				&.timeline {
					padding-left: 10px;
				}

				&.timeline {
					font-size: 14px;
					font-family: 'SourceSansPro-Regular';
					font-weight: unset;
					line-height: 20px;
				}
			}

			&.section-item-role {
				.item {
					width: 400px;

					#typeRoles {
						width: 100%;
					}
				}
			}
		}
	}
}

.tabstrip-customdashboard .customdashboard-content {
	.right-doc {
		margin-left: 0;
		flex-grow: 1;
		overflow: visible;

		&.float {
			margin-left: 20px;
		}
	}

	.widgets-panel {
		border-right: 3px solid #4b4b4b;
		background: #fff;
		transition: width 200ms ease;
		height: 100%;
		overflow: unset;

		.main-body {
			height: 100%;
			width: 100%;
		}

		&.disable-transition {
			transition: none;
		}

		&.float {
			position: absolute;
			z-index: 12052;
			min-width: 20px;

			&.collapse {
				display: block;
				visibility: visible;

				.data-points-panel {
					min-width: 17px;

					.non-date-element-container {
						visibility: hidden;
					}
				}
			}

			.handler {
				transform: rotate(0deg);
			}
		}

		.handler {
			width: 30px;
			height: 30px;
			background-image: url(../../global/img/dashboard/right_chevron.svg);
			position: absolute;
			top: 40px;
			right: -16px;
			border-radius: 15px;
			border: 2px solid;
			background-color: #fff;
			background-size: 16px;
			background-position: center;
			background-repeat: no-repeat;
			z-index: 100;
			cursor: pointer;
			transform: rotate(180deg);

			&:hover {
				background-color: #eee;
			}
		}
	}
}

.routefinder.new-window {
	.tabstrip-customdashboard.read-mode {
		.customdashboard-content {
			.right-doc {
				margin-left: 0;
				width: 100%;

				.free-view {
					.buttons {
						display: none;
					}
				}
			}
		}
	}
}

.k-popup-dropdowntree .k-treeview .k-treeview-lines li div>.k-icon+.k-in.k-hover {
	background-color: unset !important;
}

.k-popup-dropdowntree {
	.k-treeview {
		padding: 0 !important;
		overflow-x: hidden;

		.k-treeview-lines {

			li:hover,
			li.k-hover {
				background-color: transparent;
			}

			li {
				span.k-in {

					&.k-hover {
						background-color: #eaeaea;
					}

					&.k-focus {
						border: 1px transparent solid;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: #FFFFCC;
					}
				}

				div>.k-icon+.k-in {
					cursor: pointer;

					&.k-hover {
						background-color: #fff;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: #fff;
					}
				}

				ul li {
					span.k-in {

						&.k-hover {
							background-color: #eaeaea;
						}

						&.k-selected {
							background-color: #FFFFCC;
						}
					}
				}
			}
		}
	}
}

.custom-field-container {
	padding: 10px 50px;

	.is-required {
		color: red;
		padding: 2px;
	}

	.error {
		color: red;
		display: none;
		font-size: 12px;
		font-weight: normal !important;
		height: 0;
	}

	.custom-component {
		margin-bottom: 20px;
	}
}

.custom-field-container,
.date-widget-content {
	.k-dropdown .k-dropdown-wrap .k-input {
		background-color: #fff;
	}

	.k-dropdown .k-dropdown-wrap .k-select {
		height: 20px;
		min-height: 20px;
		background-color: #eee;
		line-height: 20px;
		border-left: solid 1px #BFBFBF;
	}
}

.custom-field-container,
.property-grid {
	.k-dropdowntree {
		cursor: pointer;

		.k-input {
			cursor: pointer;
		}

		.k-select {
			cursor: pointer;
		}
	}

	.k-numeric-wrap {
		input.k-input {
			line-height: 18px;
		}
	}

	.custom-component {
		height: 60px;
		margin-bottom: 0 !important;

		&.disabled {
			opacity: 0.5;
		}

		&.component-recordcount {
			height: 30px;

			.checkbox-recordcount {
				position: static;
			}
		}

		.k-dropdownlist {
			.k-input-button {
				background-color: #eee;
				border-left: solid 1px #BFBFBF;
			}
		}

		.k-dropdowntree {

			>.k-input {
				line-height: 15px;
			}

			.k-clear-value {
				.k-icon {
					margin-top: -3px;
					width: 16px;
					height: 16px;
				}
			}

			>.k-select {
				height: 20px;
				min-height: 20px;
				background-color: #eee;
				line-height: 20px;
				border-left: solid 1px #BFBFBF;

				.k-icon {
					display: inline-block;
					width: 0;
					height: 0;
					vertical-align: middle;
					border-top: 4px solid;
					border-right: 4px solid transparent;
					border-left: 4px solid transparent;
				}
			}
		}
	}

	.advanced.custom-component {
		padding-left: 5px;
		height: 30px !important;
	}

	.component-from-field.custom-component,
	.component-to-field.custom-component {
		padding-left: 20px;
	}

	.name-header {
		font-weight: bold;
		margin-bottom: 0;
		text-align: left;
		font-family: "SourceSansPro-Regular";
		color: #333;
		font-size: 14px;
	}

	.widget-name,
	.series-name,
	.child-name {
		width: 100%;
		border: 1px solid #ccc;
		height: 22px;
		padding: 0 8px;

		&:focus-visible {
			outline: none;
		}
	}
}

.dashboard-to-pdf {
	position: absolute;
	width: 100%;
	z-index: 9999;
	background-repeat: no-repeat;
}

.dashboard-image {
	display: none;
}

.banned-subscribers {
	background-image: url(../../global/img/grid/RedBanned.png);
	background-size: 20px;
	height: 20px;
	width: 20px;
}

.k-popup-dropdowntree {
	.k-treeview {
		.k-treeview-lines>.k-item {
			&.data-type-blank {
				margin-left: 0;
				cursor: pointer;
			}
		}

		.k-in,
		.k-mid,
		.k-top,
		.k-bot {
			&.highlight {
				background-color: #FFFFCC;
			}

			.k-in {
				width: 100%;
			}
		}
	}
}

.k-nodata>div.loading {
	background-image: url('../../global/img/routing map/menuicon/process-loading.gif');
	background-repeat: no-repeat;
	background-size: 40px;
	background-position-y: 30px;
	background-position-x: center;
}

.k-list-container .k-list-scroller {
	.k-list {
		.k-item {

			&.k-selected {
				background-color: #FFFFCC !important;
			}

			span.separation_line {
				display: block;
				border-bottom: 1px #ccc solid;
			}

			span.section_separation_line {
				display: block;
				border-bottom: 1px #959595 solid;
				color: #959595 !important
			}
		}
	}
}

.access-role {
	.access-role-title {
		font-size: 14px;
		font-weight: bold;
	}
}

.no-permission-widget {
	position: relative;
	float: left;
	z-index: 1;
	height: 100%;
	width: 100%;
	background-color: transparent;
	background-image: url("../../global/Img/Widgets/can not view icon.svg");
	background-repeat: no-repeat;
	background-position: center;
}

.radial-gauge-container {
	.no-permission-widget {
		position: absolute;
	}
}

body.page-no-permission {
	min-width: auto;
}

body.page-no-permission .customizeddashboard .detail-view-panel .dashboard-detail-view-header .template-info input.dashboard-input {
	min-width: auto;
}

.k-mobile body.page-no-permission .main-body.customizeddashboard.read-mode {
	min-width: auto;
}

.grid-stack>.grid-stack-item>.grid-stack-item-content.custom-text-box {
	overflow-y: auto;
}

.k-popup-dropdowntree {
	.k-treeview {
		.k-treeview-lines {

			li.data-folder li:hover,
			li.data-folder li.k-hover,
			li.data-type-blank:hover,
			li.data-type-blank.k-hover {
				background-color: #eaeaea;
			}

			li.data-folder:hover,
			li.data-folder.k-hover {
				background-color: transparent;
			}

			li.data-folder li[id],
			li.data-type-blank[id] {
				background-color: #FFFFCC;
			}

			li.data-folder[id] {
				background-color: transparent;
			}

			li.data-folder,
			li.data-folder li,
			li.data-type-blank {
				span.k-in {

					&.k-hover {
						background-color: transparent;
					}

					&.k-focus {
						border: 1px transparent solid;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: transparent;
					}
				}

				div>.k-icon+.k-in {
					cursor: pointer;

					&.k-hover {
						background-color: transparent;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: transparent;
					}
				}

				ul li {
					span.k-in {

						&.k-hover {
							background-color: transparent;
						}

						&.k-selected {
							background-color: transparent;
						}
					}
				}
			}

			li.k-item.data-filter-folder.show-as-group li.k-item.data-filter-child,
			li.k-item.data-forms-folder.show-as-group li.k-item.data-forms-child {
				margin-left: -16px;
				padding-left: 32px !important;

				&:hover,
				&.k-hover {
					background-color: #eaeaea;
				}

				&[id],
				&[aria-selected=true] {
					background-color: #FFFFCC;
				}

				span.k-in.k-focus {
					background-color: #FFFFCC;
				}
			}
		}
	}
}

.k-popup-dropdowntree {
	.k-treeview {
		.k-treeview-lines {
			li.data-folder {
				padding-left: 0;

				>div.k-mid,
				>div.k-bot {
					margin-left: 16px;
				}

				>ul>li {
					padding-left: 32px;
				}
			}

			li.data-folder li:hover,
			li.data-folder li.k-hover,
			li.data-type-blank.k-hover {
				background-color: #eaeaea;
			}

			i.data-folder:hover,
			li.data-folder.k-hover {
				background-color: transparent;
			}

			li.data-folder li[aria-selected=true] {
				background-color: #FFFFCC;
			}

			li.data-folder[aria-selected=false] {
				background-color: transparent;
			}

			li.data-folder,
			li.data-folder li {
				span.k-in {

					&.k-hover {
						background-color: #eaeaea;
					}

					&.k-focus {
						border: 1px transparent solid;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: transparent;
					}
				}

				div>.k-icon+.k-in {
					cursor: pointer;

					&.k-hover {
						background-color: transparent;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: transparent;
					}
				}

				ul li {
					span.k-in {

						&.k-hover {
							background-color: transparent;
						}

						&.k-selected {
							background-color: transparent;
						}
					}
				}
			}
		}
	}
}

.k-tooltip.k-chart-tooltip {
	z-index: 12032;
}

.dashboard-dropdowntree.k-dropdowntree-popup {

	.k-treeview-lines {
		.k-treeview-item {
			&:hover {
				background-color: #eaeaea;
			}

			&.data-folder,
			&.data-filter-folder {
				&:hover {
					background-color: #fff;
				}
			}
		}

		.k-treeview-item[aria-selected="true"],
		.k-treeview-leaf.k-selected {
			background-color: #FFFFCC;
		}

		li.data-folder,
		li.data-filter-folder {
			padding-left: 0;

			.k-treeview-mid,
			.k-treeview-top,
			.k-treeview-bot {
				padding-left: 24px;
			}

			.k-treeview-item {
				padding-left: 40px;

				.k-treeview-mid,
				.k-treeview-top,
				.k-treeview-bot {
					padding-left: 0;
				}
			}
		}
	}
}

.tabstrip-customdashboard,
.customizeddashboard {
	.detail-view-panel .custom-date.dashboard .date-container {
		.k-datepicker .k-input-inner {
			height: 20px;
		}

		.apply-date {
			min-width: 30px;
			flex: 0 0 15%;
		}

		.k-datepicker button.k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base {
			border-right: 0 !important;
			border-top: 1px #bfbfbf solid;
		}
	}
}

.dashboard-date-widget.k-dropdownlist-popup {
	.k-list-content {
		ul li.filter {
			background-image: none !important;
			padding-left: 5px;

			&.custom {
				display: none;
			}
		}
	}
}