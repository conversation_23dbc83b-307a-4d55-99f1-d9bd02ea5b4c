(function()
{
	createNamespace("TF.ImportAndMergeData").PerformArchiveStep = PerformArchiveStep;

	function PerformArchiveStep(data)
	{
		TF.Control.BaseWizardStepViewModel.call(this, data);
		this.template = "modal/import data/performarchive";
		this.name("Perform Database Archive");
		this.description("Select perform archive to create a backup of your current database before import.");
	}

	PerformArchiveStep.prototype = Object.create(TF.Control.BaseWizardStepViewModel.prototype);

	PerformArchiveStep.prototype.constructor = PerformArchiveStep;

	PerformArchiveStep.prototype.execute = function()
	{
		if (!this.data.performArchive())
		{
			return Promise.resolve(true);
		}

		return this.archiveClick({ DBID: tf.datasourceManager.databaseId, Name: tf.datasourceManager.databaseName });
	};

	PerformArchiveStep.prototype.archiveClick = function(data)
	{
		var self = this;
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "datasources", data.DBID), {
			data: {
				IncludeDocument: false,
				IncludeStudentPicture: false
			},
			paramData: {
				archiveName: data.Name,
				skipLockSource: true,
				isAsync: true
			}
		}).then(function(result)
		{
			tf.loadingIndicator.show();
			var operationId = result.Items[0].Id;
			return new Promise((resolve, reject) =>
			{
				var interval = setInterval(() =>
				{
					tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "operations", operationId), null, { overlay: false }).then(function(response)
					{
						var status = response.Items[0].Status;
						if (status.Finished)
						{
							tf.loadingIndicator.tryHide();
							clearInterval(interval);
							if (status.Error)
							{
								tf.promiseBootbox.alert("Archive failed.").then(() => { resolve(false); });
								resolve(false);
							}
							tf.IsDownloading = true;
							window.location = pathCombine(tf.api.apiPrefixWithoutDatabase(), "ArchiveFiles?fileName=" + encodeURIComponent(data.Name));
							setTimeout(function()
							{
								tf.IsDownloading = false;
								tf.promiseBootbox.alert('Archive completed').then(() => { resolve(true); });
							}, 500);
						}
					}).catch();
				}, 6000);
			});
		}).catch(function(exception)
		{
			return TF.showErrorMessageBox(exception);
		});
	}
})();