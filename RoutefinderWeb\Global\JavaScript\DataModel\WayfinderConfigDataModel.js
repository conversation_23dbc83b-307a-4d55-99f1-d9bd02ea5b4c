﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.WayfinderConfigDataModel = function(entity)
	{
		namespace.BaseDataModel.call(this, entity);
	}

	namespace.WayfinderConfigDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.WayfinderConfigDataModel.prototype.constructor = namespace.WayfinderConfigDataModel;

	namespace.WayfinderConfigDataModel.prototype.mapping = [
		{ from: "Tripcolor0", default: "#fffffe" },
		{ from: "Tripcolor1", default: "#fffffe" },
		{ from: "Tripcolor2", default: "#fffffe" },
		{ from: "Tripcolor3", default: "#fffffe" },
		{ from: "Tripcolor4", default: "#fffffe" },
		{ from: "RememberMe", default: 0 },
		{ from: "ScanOnToSchool", default: null },
		{ from: "ScanOffToSchool", default: null },
		{ from: "ScanOnFromSchool", default: null },
		{ from: "ScanOffFromSchool", default: null },
		{ from: "AutoSubstitute", default: null },
		{ from: "AutoSkip", default: true },
		{ from: "AutoSubstituteOption", default: 0 },
		{ from: "OnTimeBufferEarly", default: 5 },
		{ from: "OnTimeBufferLate", default: 5 },
		{ from: "TravelScenarioId", default: 1 }
	];
})();
