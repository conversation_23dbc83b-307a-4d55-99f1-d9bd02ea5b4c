(function()
{
	createNamespace("TF.Map").ExpandMapTool = ExpandMapTool;

	const FullScreenMapZIndex = 99999999;

	var STATUS = {
		expand: 0,
		restore: 1
	};

	/**
	* map expand tool
	* @constructor
	* @param {Object} map - The title of the book.
	* @param {Object} expandContainer - The expand container.
	*/
	function ExpandMapTool(map, expandContainer, options)
	{
		this.map = map;
		this.expandContainer = expandContainer;
		this.options = options || {};
		this.status = STATUS.restore;
		this.onStatusChangedEvent = new TF.Events.Event();
		this.originalContainer = $(map.mapView.container).parent();
		this.containerIsBody = this.expandContainer[0].tagName === "BODY";
		this._init();
	}

	ExpandMapTool.prototype._init = function()
	{
		var expandToolClass = TF.isMobileDevice ? 'map-expand-button is-mobile-device' : 'map-expand-button';
		this.button = $("<div title='expand' class='expand-button " + expandToolClass + "'></div>");
		this.button.on("click", this._toggleClick.bind(this));
		$(this.map.mapView.container).append(this.button);
	};

	ExpandMapTool.prototype._toggleClick = function()
	{
		var self = this;
		if (this.status == STATUS.restore)
		{
			if (TF.isMobileDevice)
			{
				self.map.mapView.navigation.browserTouchPanEnabled = true;
			}
			this._expand();
			this.status = STATUS.expand;
		} else
		{
			if (TF.isMobileDevice)
			{
				self.map.mapView.navigation.browserTouchPanEnabled = false;
			}
			this._restore();
			this.status = STATUS.restore;
		}
		this.onStatusChangedEvent.notify(this.status);
		this.button.toggleClass("restore");
		[".dock-left", ".dock-right"].forEach(function(selector)
		{
			var panel = $(self.map.mapView.container).find(selector);
			if (panel.length > 0)
			{
				var routingMapPanelViewModel = ko.dataFor(panel[0]);
				routingMapPanelViewModel.routingMapDocumentViewModel.routingMapPanelManager.setDockPanelStyle(routingMapPanelViewModel.$panel, routingMapPanelViewModel.$mapPage);
			}
		});

		//Reset sketch modal height when switch expand and restore
		var sketches = $(self.map.mapView.container).find(".sketchEditModal");
		if (sketches.length > 0)
		{
			for (var i = 0; i < sketches.length; i++)
			{
				var sketchEditModal = ko.dataFor(sketches[i]);
				sketchEditModal.refreshKendoEditor && sketchEditModal.refreshKendoEditor();
				sketchEditModal.resetHeight && sketchEditModal.resetHeight();
			}
		}

		const $expendButton = $(arguments[0]?.currentTarget);
		setFocusOnMapView($expendButton); // RW-50985

		function setFocusOnMapView($expendButton)
		{
			$expendButton?.parent()?.find('.esri-view-surface')?.focus();
		}
	};

	ExpandMapTool.prototype._expand = function()
	{
		var mapElement = this._getMapElement();
		/*Formfinder: there's no element with class ".grid-stack-item" on form */
		if (mapElement.closest(".grid-stack-item").length > 0)
		{
			var uniqueClassName = TF.DetailView.DetailViewHelper.prototype.getDomUniqueClassName(mapElement.closest(".grid-stack-item"));
			mapElement.data("uniqueClassName", uniqueClassName);
		}
		if (!this.containerIsBody)
		{
			this.expandContainer.children().css({ "visibility": "hidden" });
			this.expandContainer.css("overflow-y", "hidden");
		}
		this.expandContainer.append(mapElement);
		this.expandContainer.css({ position: 'relative' }).addClass(TF.DetailView.DetailViewHelper.ExpandClassName);
		this.button.attr("title", "restore");
		let zIndex = this.containerIsBody ? FullScreenMapZIndex : 500;
		if (this.options && this.options.zIndex)
		{
			zIndex = this.options.zIndex;
		}
		mapElement.css({
			position: 'absolute',
			top: this.expandContainer.scrollTop(),
			left: 0,
			right: 0,
			bottom: 0,
			'z-index': zIndex,
			'background-color': 'white'
		});

		mapElement.find(".grid-map-expand.is-mobile-device.map-palette").addClass("restore");
	};

	ExpandMapTool.prototype._restore = function()
	{
		var mapElement = this._getMapElement();
		if (!this.containerIsBody)
		{
			this.expandContainer.children().css({ "visibility": "visible" });
			this.expandContainer.css({ "overflow-y": "auto", "position": "" })
		}
		this.expandContainer.removeClass(TF.DetailView.DetailViewHelper.ExpandClassName);
		this.originalContainer.append(mapElement);
		this.button.attr("title", "expand");
		mapElement.css({
			position: 'relative',
			'z-index': 0,
			top: 0
		});

		mapElement.find(".grid-map-expand.is-mobile-device.map-palette").removeClass("restore");
	};

	ExpandMapTool.prototype._getMapElement = function()
	{
		return $(this.map.mapView.container);
	};

	ExpandMapTool.prototype.dispose = function()
	{
		this.button.off("click");
		// ensure expandContainer not disposed in tfdispose.
		// expandContainer will be disposed when detail view closed.
		this.expandContainer = null;
		tfdispose(this);
	};

	ExpandMapTool.moveMobileFullScreenBaseMapAhead = function($offMapTool)
	{
		if (TF.isMobileDevice &&
			$offMapTool.closest('.map-item.map-page').zIndex() === FullScreenMapZIndex * -1)
		{
			$offMapTool.closest('.map-item.map-page').zIndex(FullScreenMapZIndex);
		}
	}

	ExpandMapTool.moveMobileFullScreenBaseMapBehind = function($offMapTool)
	{
		if (TF.isMobileDevice &&
			$offMapTool.closest('.map-item.map-page').zIndex() === FullScreenMapZIndex)
		{
			$offMapTool.closest('.map-item.map-page').zIndex(FullScreenMapZIndex * -1);
		}
	}
})();