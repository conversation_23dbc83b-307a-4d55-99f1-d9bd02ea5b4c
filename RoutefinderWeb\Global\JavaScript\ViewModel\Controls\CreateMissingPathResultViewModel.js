(function()
{
	createNamespace("TF.Control").CreateMissingPathResultViewModel = CreateMissingPathResultViewModel;

	function CreateMissingPathResultViewModel(options)
	{
		this.obAlreadyCount = ko.observable(0);
		this.obNotCalcCount = ko.observable(0);
		this.obUpdatedCount = ko.observable(0);
		const tripText = count => count === 1 ? 'trip' : 'trips';
		this.obAlreadyText = ko.computed(() => `${this.obAlreadyCount()} ${tripText(this.obAlreadyCount())} already with complete paths`);
		this.obUpdatedText = ko.computed(() => `${this.obUpdatedCount()} ${tripText(this.obUpdatedCount())} updated with complete paths`);
		this.obNotCalcText = ko.computed(() => `${this.obNotCalcCount()} ${tripText(this.obNotCalcCount())} could not calculate complete paths`);
		this.obComplete = ko.observable(false);
		this.obCancel = ko.observable(false);
	}

	CreateMissingPathResultViewModel.prototype.cancel = function()
	{
		return Promise.resolve(true);
	};
})();