(function()
{
	createNamespace("TF.Control").RecipientsSelect = RecipientsSelect;

	function RecipientsSelect(element, options)
	{
		options = options || {};
		var self = this;
		self.options = options;
		self.element = $(element);
		self.element.data("RecipientsSelect", self).addClass("recipients-select");
		var inputTemplate = "<input ";
		$.each(options.attributes || {}, function(k, v)
		{
			inputTemplate += k + "='" + v + "'";
		});
		inputTemplate += "/>";
		self.input = $(inputTemplate).addClass("form-control");
		self.button = $("<button tabindex='-1' />").addClass("btn btn-default glyphicon glyphicon-option-horizontal");
		self.element.html("").append(self.input).append(self.button);

		var disable = self.options.disable;
		if (disable != null)
		{
			if (ko.isObservable(disable))
			{
				self.setDisabled(disable());
				disable.subscribe(function()
				{
					self.setDisabled(disable());
				});
			}
			else
			{
				self.setDisabled(disable);
			}
		}

		var enable = self.options.enable;
		if (enable != null)
		{
			if (ko.isObservable(enable))
			{
				self.setDisabled(!enable());
				enable.subscribe(function()
				{
					self.setDisabled(!enable());
				});
			}
			else
			{
				self.setDisabled(!enable);
			}
		}

		var value = self.options.value;
		if (value)
		{
			if (ko.isObservable(value))
			{
				self.input.val(value());
				self.input.on("input change blur", function()
				{
					value(self.input.val());
				});

				value.subscribe(function()
				{
					self.input.val(value());
				});
			}
			else
			{
				self.input.val(value);
			}
		}

		self.button.on("click", function()
		{
			self.openSelectRecipientsModal();
		});
	}

	RecipientsSelect.prototype.setDisabled = function(disabled)
	{
		self.input.pro("disabled", disabled);
		self.button.pro("disabled", disabled);
	};

	RecipientsSelect.prototype.getMailList = function()
	{
		var self = this, mailsStr = (self.input.val() || "").trim();
		var mailList = mailsStr.split(/[,;]/);
		var result = [];
		mailList.forEach(function(item)
		{
			item = (item || "").trim();
			if (item)
			{
				result.push(item);
			}
		});

		return result;
	};

	RecipientsSelect.prototype.getDistinguishedMails = function()
	{
		var self = this, mails = self.getMailList();
		if (!mails.length)
		{
			return Promise.resolve({
				system: [],
				adhoc: [],
				original: []
			});
		}

		return self.getUserMailMap().then(function(userMailMap)
		{
			var system = [], adhoc = [], original = [];
			mails.forEach(function(mail)
			{
				if (!mail) return;
				var key = mail.toLowerCase();
				var user = userMailMap[key];
				if (user)
				{
					system.push(user);
					original.push(user);
				}
				else
				{
					adhoc.push(mail);
					original.push(mail);
				}
			});

			return {
				system: system,
				adhoc: adhoc,
				original: original
			};
		});
	};

	RecipientsSelect.prototype.getUserMailMap = function()
	{
		var self = this;
		if (self._userMailMap)
		{
			return Promise.resolve(self._userMailMap);
		}

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", "users"))
			.then(function(apiResponse)
			{
				self._userMailMap = {};
				var users = apiResponse.Items;
				users.forEach(function(user)
				{
					var mail = (user.Email || "").toLowerCase();
					if (mail)
					{
						self._userMailMap[mail] = user;
					}
				});

				return self._userMailMap;
			});
	};

	RecipientsSelect.prototype.openSelectRecipientsModal = function(viewModel, e)
	{
		var self = this;
		self.getDistinguishedMails().then(function(mails)
		{
			tf.modalManager.showModal(
				new TF.Modal.ListMoverSelectRecipientControlModalViewModel(
					mails.system
				))
				.then(function(result)
				{
					if (!$.isArray(result))
					{
						return;
					}

					var resultMap = {};
					result.forEach(function(item)
					{
						var mail = (item.Email || "").toLowerCase();
						if (mail)
						{
							resultMap[mail] = item;
						}
					});

					var newMails = [];
					mails.original.forEach(function(item)
					{
						if (typeof (item) == "string")
						{
							newMails.push(item);
							return;
						}

						var mail = item.Email.toLowerCase();
						if (resultMap[mail])
						{
							newMails.push(item.Email);
							delete resultMap[mail];
						}
					});

					$.each(resultMap, function(k, v)
					{
						newMails.push(v.Email);
					});

					self.input.val(newMails.join(";"));
					self.input.focus();
				});
		});
	};

	ko.bindingHandlers.recipientsSelect = {
		init: function(element, valueAccessor)
		{
			var options = ko.unwrap(valueAccessor());
			new RecipientsSelect(element, options);
		}
	};
})();