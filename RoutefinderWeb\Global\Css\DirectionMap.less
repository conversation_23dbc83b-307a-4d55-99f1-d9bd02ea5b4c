/***********************************
** Direction style for map popups
************************************/

.direction-popup-menu {
	position: absolute;
	width: 110px;
	height: 50px;
	z-index: 100; // same as map div z-index
	top: 0px;
	left: 0px;
	display: none;

	.left {
		position: absolute;
		width: 30px;
		height: 30px;
		border-radius: 15px;
		top: 20px;
		display: block;
	}

	.right {
		position: absolute;
		width: 30px;
		height: 30px;
		border-radius: 15px;
		// left: 80px;
		display: block;
		// animate
		left: 0;
		top: 20px;
		opacity: 0;
	}

	.dark {
		background: #4B4B4B;
	}

	.light {
		background: #FFFFFF;
	}

	.destination {
		width: 16px;
		height: 16px;
		top: 7px;
		left: 7px;
		position: relative;
		border-radius: 8px;
	}

	.destination.light {
		background-size: contain;
		background-repeat: no-repeat;
		background-image: url('../img/direction-setting-panel/drop-destination-light.png');
		background-color: #4B4B4B;
		opacity: 0.8;
	}

	.destination.dark {
		background-size: contain;
		background-repeat: no-repeat;
		background-image: url('../img/direction-setting-panel/drop-destination-dark.png');
		background-color: #FFFFFF;
	}

	.destination.label {
		color: #333333;
		font: bold normal normal 12px Arial;
		left: -37px;
		top: 15px;
		background-color: #FFFFFF;
		padding: 0;
		border-radius: 0;
		opacity: 0.8;
	}

	.wayPoint {
		width: 9px;
		height: 9px;
		border-radius: 4px;
		border-width: 1px;
		border-style: solid;
		top: 10.5px;
		left: 10.5px;
		position: relative;
	}

	.wayPoint.dark {
		background-color: #666666;
		border-color: #000000;
	}

	.wayPoint.light {
		background-color: #999999;
		border-color: #FFFFFF;
	}

	.wayPoint.label {
		color: #333333;
		font: bold normal normal 12px Arial;
		left: -45px;
		top: 22px;
		background-color: #FFFFFF;
		padding: 0;
		border-radius: 0;
		opacity: 0.8;
		border: none;
	}

	.remove {
		width: 16px;
		height: 16px;
		top: 7px;
		left: 7px;
		position: relative;
		border-radius: 8px;
	}

	.remove.light {
		background-size: contain;
		background-repeat: no-repeat;
		background-color: #4B4B4B;
		opacity: 0.8;
		font: normal normal normal 16px Arial;
		color: #FFFFFF;
		text-align: center;
	}

	.remove.dark {
		background-size: contain;
		background-repeat: no-repeat;
		background-color: #FFFFFF;
		font: normal normal normal 16px Arial;
		color: #000000;
		text-align: center;
	}

	.remove.label {
		color: #333333;
		font: bold normal normal 12px Arial;
		left: -8px;
		top: 15px;
		background-color: #FFFFFF;
		padding: 0;
		border-radius: 0;
		opacity: 0.8;
	}
}
// .direction-popup-menu end

.direction-drop-destination-infowindow {
	position: absolute;
	font: normal normal normal 14px Arial;
	pointer-events: none;
	border-style: solid;
	border-width: 1px;
	padding: 7px;

	p {
		margin: 5px 5px 0;
	}

	.close {
		font: normal normal normal 18px Arial;
		position: absolute;
		top: 5px;
		right: 5px;
		pointer-events: auto;
	}
}

.direction-drop-destination-infowindow.light {
	background-color: #4B4B4B;
	border-color: #999999;
	color: #FFFFFF;
	opacity: 0.8;

	.close {
		color: #FFFFFF;
		opacity: 0.8;
	}
}

.direction-drop-destination-infowindow.dark {
	background-color: #FFFFFF;
	border-color: #666666;
	color: #333333;
	opacity: 1;

	.close {
		color: #333333;
		opacity: 1;
	}
}

.direction-drop-destination-infowindow {
	p {
		margin: 0px 0px 5px;
	}
}

.diretion-drop-destination-tooltip {
	p {
		margin: 0px 0px 0px;
	}
}

#directions_stopSequenceLayer_layer {
	pointer-events: none;
}

.direction-dragging-tooltip{
	position: absolute;
	background-color: #FFFFFF;
	padding: 5px;
	border: 1px solid #8B8B8B;
	color: #444444;
	font-family: Verdana, Geneva, Tahoma, sans-serif;
	font-size: 12px;
	border-radius: 3px;
	margin-left: 20px;
	margin-top: -10px;
	z-index: 100;
	display: none;
}
