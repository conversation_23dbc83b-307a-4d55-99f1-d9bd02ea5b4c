﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.GeoregionTypeDataModel = function(georegionTypeEntity)
	{
		namespace.BaseDataModel.call(this, georegionTypeEntity);
	};

	namespace.GeoregionTypeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.GeoregionTypeDataModel.prototype.constructor = namespace.GeoregionTypeDataModel;

	namespace.GeoregionTypeDataModel.prototype.mapping = [
		{ from: "Id", default: 0 },
		{ from: "Name", default: "" },
		{ from: "Boundary", default: "Street Path" },
		{ from: "Distance", default: 0 },
		{ from: "DistanceUnits", default: () => tf.measurementUnitConverter.isImperial() ? 'feet' : 'meters' },
		{ from: "Buffer", default: 0 },
		{ from: "BufferUnits", default: () => tf.measurementUnitConverter.isImperial() ? 'feet' : 'meters' },
		{ from: "BoundaryThickness", default: 1 },
		{ from: "SymbolSetting", default: '{"symbol":"default","size":"6","color":"#1259d0","borderishow":false,"bordersize":"1","bordercolor":"#000000"}' },
		{ from: "BoundaryColor", default: "#000000", fromMapping: function(v) { return "#" + v; }, toMapping: function(v) { return (TF.Color.toHTMLColorFromLongColor(v) || '').replace("#", ''); } },
		{ from: "BoundaryFill", default: "None" },
		{ from: "SystemDefined", default: "false" }
	];
})();