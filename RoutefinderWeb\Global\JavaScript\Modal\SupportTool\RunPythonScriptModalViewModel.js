(function()
{
	createNamespace("TF.Modal.Support").RunPythonScriptModalViewModel = RunPythonScriptModalViewModel;

	function RunPythonScriptModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "modal-lg";
		this.obIsBusy = ko.observable(false);
		this.title("Run Python Scripts");
		this.contentTemplate("Navigation/PythonScriptData");
		this.buttonTemplate("modal/positivenegative");
		this.obPositiveButtonLabel("Run");
		this.file = ko.observable();
		this.jobMessages = ko.observableArray([]);
		this.isJobSubmit = ko.observable(false);
		this.jobId = ko.observable();
		this.jobStatus = ko.observable();
	};

	RunPythonScriptModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	RunPythonScriptModalViewModel.prototype.constructor = RunPythonScriptModalViewModel;

	RunPythonScriptModalViewModel.prototype.positiveClick = function()
	{
		if (this.isJobSubmit())
		{
			this.hide();
			return;
		}

		if (!this.file() || this.file().length == 0)
		{
			tf.promiseBootbox.alert("Please select a valid python file.");
			return;
		}

		var upload = $("input[name='python_upload']").data("kendoUpload");
		upload.disable();

		this.uploadScript().then(result =>
		{
			let scriptFile = JSON.stringify(result.data.item);
			this.executeScript(scriptFile);
		}).catch(error =>
		{
			upload.enable();
			throw error;
		});
	}

	RunPythonScriptModalViewModel.prototype.uploadScript = function()
	{
		let data = new FormData();
		data.append("f", "json");
		data.append("file", this.file()[0].rawFile);

		return tf.map.ArcGIS.esriRequest(arcgisUrls.getTFUtilitiesGPServiceTaskPath("uploads/upload"), {body: data});
	};

	RunPythonScriptModalViewModel.prototype.executeScript = function(scriptFile)
	{
		let param = { scriptFile: scriptFile, plusToken: tf.entStorageManager.get("token", true), authUrl: pathCombine(tf.api.apiPrefixWithoutDatabase(), "authinfos")};
		this.obDisableControl(true);
		this.obNegativeButtonVisbile(false);
		this.obPositiveButtonLabel("Running");
		this.isJobSubmit(true);
		tf.map.ArcGIS.geoprocessor.submitJob(arcgisUrls.getTFUtilitiesGPServiceTaskPath("ExecuteScript"), param).then(jobTask =>
		{
			this.title("Output");
			this.jobId(jobTask.jobId);
			this.jobStatus(jobTask.jobStatus);
			const options = 
			{
				interval: TF.Map.BaseMap.shortJobInterval,
				statusCallback: (j) => {
					this.jobStatus(j.jobStatus);
					this._appendMessage(j.messages);
				}
			};
			return jobTask.waitForJobCompletion(options)
		}).then(result =>
		{
			this.jobStatus(result.jobStatus);
			this._rebuildMessage(result.messages);
		}).catch(error =>
		{
			if (error.jobStatus && error.messages)
			{
				this.jobStatus(error.jobStatus);
				this._rebuildMessage(error.messages);
			}
			else
			{
				this.jobMessages.push(`Error: ${error?.message || "Unknown error occurred"}`);
			}
		}).finally(() =>
		{
			this.obPositiveButtonLabel("Close");
			this.obDisableControl(false);
		});
	}

	RunPythonScriptModalViewModel.prototype.negativeClose = function()
	{
		let isRunning = this.obDisableControl();
		if (isRunning)
		{
			return tf.promiseBootbox.alert("Please wait for the python script to complete execution.").then(() =>
			{
				return false;
			});
		}
		this.hide();
	};

	RunPythonScriptModalViewModel.prototype._appendMessage = function(messages)
	{
		let currentLength = this.jobMessages().length;
		for (let i = currentLength; i < (messages || []).length; i++)
		{
			const msg = `${messages[i].type}: ${messages[i].description}`;
			this.jobMessages.push(msg);
		}
	}

	RunPythonScriptModalViewModel.prototype._rebuildMessage = function(messages)
	{
		this.jobMessages.removeAll();
		for (let i = 0; i < (messages || []).length; i++)
		{
			const msg = `${messages[i].type}: ${messages[i].description}`;
			this.jobMessages.push(msg);
		}
	}

})();