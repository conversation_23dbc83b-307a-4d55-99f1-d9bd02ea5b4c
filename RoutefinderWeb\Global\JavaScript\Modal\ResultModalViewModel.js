(function()
{
	createNamespace("TF.Modal").ResultModalViewModel = ResultModalViewModel;

	function ResultModalViewModel(options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		options = options || {};
		this.title(options.title);
		this.sizeCss = "modal-dialog-md";
		this.contentTemplate("modal/ResultControl");
		this.buttonTemplate("modal/positive");
		this.obPositiveButtonLabel("Close");
		this.data({ resultText: options.content });
	}

	ResultModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	ResultModalViewModel.prototype.constructor = ResultModalViewModel;

	ResultModalViewModel.prototype.afterRender = function(el)
	{
		$(el).find("textarea").scrollTop(0);
	}

})();


