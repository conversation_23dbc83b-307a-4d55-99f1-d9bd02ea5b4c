﻿(function()
{
	createNamespace("TF.Control").ManageMergeTemplateTypeViewModel = ManageMergeTemplateTypeViewModel;

	function ManageMergeTemplateTypeViewModel()
	{
		this.excludeEmailType = true;
		this.reloadTemplateData();
	}

	ManageMergeTemplateTypeViewModel.prototype.reloadTemplateData = function()
	{
		var mergeTemplateTypeData = TF.MergeTemplateTypeHelper.getMergeTemplateTypes(this.excludeEmailType);
		this.mergeTemplateTypeData = mergeTemplateTypeData.sort(function(a, b)
		{
			return (a.Name.toLowerCase() > b.Name.toLowerCase()) ? 1 : -1;
		});
	}

	ManageMergeTemplateTypeViewModel.prototype.init = function(viewModel, el)
	{
		var self = this;

		self.element = $(el);
		self.dataSourceGridContainer = $(el).find(".manageMergeTemplateType-container");
		self.dataSourceGridContainer.kendoGrid(
			{
				dataSource:
				{
					data: self.mergeTemplateTypeData
				},
				height: 300,
				scrollable: true,
				selectable: true,
				columns: [
					{
						width: '32px',
						type: "image",
						template: function(item)
						{
							return item.SystemDefined ? '<img src=\'../../global/img/detail-screen/settings_gray.svg\' title=\'System Defined\' width=\'16\' height=\'16\' />' : '';
						}
					},
					{
						field: "Name",
						title: "Document Layout Name",
						encoded: true
					},
					{
						title: "Width",
						field: "PageWidth",
						width: "80px"
					},
					{
						title: "Height",
						field: "PageHeight",
						width: "80px"
					},
					{
						title: "Action",
						width: "80px",
						command: [
							{
								name: "edit-command",
								click: function(e)
								{
									e.preventDefault();
									var selectedItem = self.getSelectedItem(e);
									if (!selectedItem)
									{
										tf.promiseBootbox.alert("Record not existed", "Error");
										self.refreshGrid();
									}
									else
									{
										self.editMergeDocumentTemplate(selectedItem);
									}
								}
							},
							{
								name: "copyandnew",
								click: function(e)
								{
									e.preventDefault();
									var selectedItem = self.getSelectedItem(e);
									if (!selectedItem)
									{
										tf.promiseBootbox.alert("Record not existed", "Error");
										self.refreshGrid();
									}
									else
									{
										self.copyMergeDocuemntTemplate(selectedItem, self.mergeTemplateTypeData);
									}
								}
							},
							{
								name: "delete",
								click: function(e)
								{
									e.preventDefault();
									var selectedItem = self.getSelectedItem(e);
									if (!selectedItem)
									{
										tf.promiseBootbox.alert("Record not existed", "Error");
										self.refreshGrid();
									}
									else
									{
										self.deleteMergeDocumentTemplate(selectedItem);
									}
								}
							}]
					}],
				dataBound: function(e)
				{
					var gird = self.dataSourceGridContainer.data("kendoGrid");
					var data = gird.dataSource.data();
					var $gridRows = self.dataSourceGridContainer.find('.k-grid-content table.k-grid-table tr');
					$gridRows.each(function name(i, row)
					{
						var item = data[i];
						if (item.SystemDefined)
						{
							var $row = $(row);
							var $editButton = $row.find('.k-button.k-grid-edit-command');
							$editButton.addClass('k-button-disabled');

							var $deleteButton = $row.find('.k-button.k-grid-delete');
							$deleteButton.addClass('k-button-disabled');

							if (item.Name == "Email")
							{
								var $copyandnew = $row.find('.k-button.k-grid-copyandnew');
								$copyandnew.addClass('k-button-disabled');
							}
						}
					});
					var g = self.dataSourceGridContainer;
					var gridHeight = g.outerHeight();
					var gridHeaderHeight = g.find("table:eq(0)").outerHeight();
					var gridBodyHeight = g.find("table:eq(1)").outerHeight();
					if (gridHeight < gridHeaderHeight + gridBodyHeight)
					{ // show the scrollbar
						g.find(".k-grid-header").css({
							'padding': '',
							'padding-right': '2px',
							'overflow-y': 'hidden',
							'scrollbar-gutter': 'stable',
						});
						g.find(".k-grid-content").css('overflow-y', 'auto');
					}
					else
					{ // hide the scrollbar
						g.find(".k-grid-header").css({
							'padding-right': '2px',
							'overflow-y': '',
							'scrollbar-gutter': '',
						});
						g.find(".k-grid-content").css('overflow-y', 'auto');
					}
				}
			});
	};

	ManageMergeTemplateTypeViewModel.prototype.getSelectedItem = function(e)
	{
		this.reloadTemplateData();
		var data = this.element.find(".manageMergeTemplateType-container").data("kendoGrid").dataItem($(e.target).closest("tr"));
		return this.mergeTemplateTypeData.filter(function(item)
		{
			return item.Id === data.Id;
		})[0];
	};

	ManageMergeTemplateTypeViewModel.prototype.newTemplateButtonClick = function()
	{
		var self = this;
		tf.modalManager.showModal(
			new TF.Modal.EditMergeTemplateTypeModalViewModel(
				{
					hideRolesAccess: true,
					...TF.MergeTemplateTypeModalHelper.NewTemplateModalOptions()
				},
				new TF.DataModel.MergeTemplateTypeModel()
			))
			.then(TF.MergeTemplateTypeModalHelper.NewTemplateModalCallback)
			.then(function()
			{
				self.refreshGrid();
			});
	}

	ManageMergeTemplateTypeViewModel.prototype.editMergeDocumentTemplate = function(selectedItem)
	{
		var self = this;
		var newRecord = $.extend({}, selectedItem);
		var newRecordModel = new TF.DataModel.MergeTemplateTypeModel(newRecord);

		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocuments") + "?templateId=" + newRecordModel.id(),
			{ overlay: false }
		).then(function(apiResponse)
		{
			var isAssociateToOtherDocuments = false;
			if (apiResponse.Items && apiResponse.Items.length > 0)
			{
				isAssociateToOtherDocuments = true;
			}

			tf.modalManager.showModal(
				new TF.Modal.EditMergeTemplateTypeModalViewModel(
					{
						hideRolesAccess: true,
						...TF.MergeTemplateTypeModalHelper.EditTemplateModalOptions(isAssociateToOtherDocuments)
					},
					newRecordModel
				))
				.then(TF.MergeTemplateTypeModalHelper.EditTemplateModalCallback)
				.then(function()
				{
					self.refreshGrid();
				});
		});
	}

	ManageMergeTemplateTypeViewModel.prototype.copyMergeDocuemntTemplate = function(selectedItem, allItems)
	{
		var self = this;
		var existedNames = self.mergeTemplateTypeData.map(function(item) { return item.Name; }),
			newCopyName = TF.Helper.NewCopyNameHelper.generateNewCopyName(selectedItem.Name, existedNames),
			newRecord = $.extend({}, selectedItem, { Name: newCopyName, SystemDefined: false, Id: null, });

		var newRecordModel = new TF.DataModel.MergeTemplateTypeModel(newRecord);

		tf.modalManager.showModal(
			new TF.Modal.CopyMergeTemplateTypeModalViewModel(
				newRecordModel
			)).then(function()
			{
				self.refreshGrid();
			});
	}

	ManageMergeTemplateTypeViewModel.prototype.deleteMergeDocumentTemplate = function(selectedItem)
	{
		var self = this;
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergedocuments"),
			{
				data: { templateId: selectedItem.Id }
			},
			{ overlay: false }).then(function(apiResponse)
			{
				if (apiResponse.Items && apiResponse.Items.length > 0)
				{
					tf.promiseBootbox.alert("This Document Layout cannot be deleted because it is associated to one or more Merge Documents.", "Alert");
					return false;
				}
				else
				{
					tf.promiseBootbox.confirm(
						{
							message: "Are you sure you want to delete the " + selectedItem.Name + " Document Layout?",
							title: "Delete Confirmation",
							containerWidthOffset: $('.navigation-container').width(),
						})
						.then(function(result)
						{
							if (result)
							{
								tf.promiseAjax.delete(
									pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergetemplatetypes"),
									{ data: [selectedItem.Id] },
									{ overlay: false }
								)
									.then(function(apiResponse)
									{
										self.refreshGrid();
									})
									.catch(function(apiResponse)
									{
										if (apiResponse.StatusCode == 412)
										{
											tf.promiseBootbox.alert("The Merge Document Layout cannot be deleted because it is associated to some Documents", "Warning");
											return;
										}
										else
										{
											tf.promiseBootbox.alert("Delete Merge Document Layout Failed!");
											return;
										}
									});
							}
						});
					return true;
				}
			});
	}

	ManageMergeTemplateTypeViewModel.prototype.refreshGrid = function()
	{
		var self = this;
		self.reloadTemplateData();
		var grid = self.dataSourceGridContainer.data("kendoGrid");
		grid.dataSource.data(self.mergeTemplateTypeData);
	}
})();

