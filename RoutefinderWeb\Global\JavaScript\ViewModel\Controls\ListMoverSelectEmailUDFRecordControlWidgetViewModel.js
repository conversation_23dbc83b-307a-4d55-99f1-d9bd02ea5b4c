﻿(function()
{
	createNamespace('TF.Modal').ListMoverSelectEmailUDFRecordControlWidgetViewModel = ListMoverSelectEmailUDFRecordControlWidgetViewModel;

	function ListMoverSelectEmailUDFRecordControlWidgetViewModel(selectedData, options)
	{
		options.displayCheckbox = false;
		options.getSelectableRecords = (data, selectedData) =>
		{
			return data.filter(this.isValidUDFEmail);
		}
		options.singleTypeData = true;
		this.template = "modal/ListMoverSelectRecordControl";
		this.isWidget = ko.observable(true);
		this.inheritChildrenShortCutKey = false;
		options.serverPaging = false;

		TF.Control.ListMoverSelectRecordControlViewModel.call(this, selectedData, options);

		this.useMaxRecordCount = true;
	}

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype = Object.create(TF.Control.ListMoverSelectRecordControlViewModel.prototype);

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.constructor = ListMoverSelectEmailUDFRecordControlWidgetViewModel;

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.onBeforeLeftGridDataBound = function(leftSearchGrid)
	{
		TF.Control.KendoListMoverWithSearchControlViewModel.prototype.onBeforeLeftGridDataBound.call(leftSearchGrid);
		leftSearchGrid.$container.find(".k-grid-content table.k-grid-table tr").map((idx, row) =>
		{
			var $row = $(row);
			var kendoUid = $row.data('kendoUid');
			let item = leftSearchGrid.kendoGrid.dataSource.getByUid(kendoUid);
			if (!this.isValidUDFEmail(item))
			{
				$row.addClass("disable");
				$row.css("color", "grey");
				$row.css("pointer-events", "none");
				$row.bind("select", function(e)
				{
					e.preventDefault();
				});
			}
		});

	};

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.isValidUDFEmail = function(item)
	{
		let UDFEmail = item.UDFEmail_Email;
		if (TF.testEmail(UDFEmail).validList.length === 0)
		{
			return false;
		}
		return true;
	}

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.getFields = function()
	{
		let fields = TF.Control.ListMoverSelectRecordControlViewModel.prototype.getFields.call(this);
		fields = fields.filter(field => field !== 'UDFEmail_EmailField' && field !== 'UDFEmail_Email');

		let emailUDFs = this.options.emailUDFs();
		emailUDFs.forEach(udf =>
		{
			fields.push(udf.OriginalName);
		});
		return fields;
	};

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.getColumnSources = function(dataType, isForRequests)
	{
		let columnSources = TF.Control.ListMoverSelectRecordControlViewModel.prototype.getColumnSources.call(this, dataType, isForRequests);
		if (!isForRequests)
		{
			columnSources = [...columnSources, {
				FieldName: "Id",
				DisplayName: "ID",
				Width: '50px',
				type: "number",
				hidden: true
			}, {
				FieldName: 'UDFEmail_EmailField',
				DisplayName: 'Email Field',
				Width: "120px",
				type: "string"
			}, {
				FieldName: 'UDFEmail_Email',
				DisplayName: 'Email',
				Width: "120px",
				type: "string"
			}];
		}

		return columnSources;
	}

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.reloadListMover = function()
	{
		this.updateAllRecords();
		this.updateSelectedRecords();
		this.setDataSource();
	}

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.processAllRecords = function(records)
	{
		let self = this;
		self.allOriginalRecords = records;
		self.updateAllRecords();
	}

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.updateSelectedRecords = function()
	{
		this.selectedData = this.selectedData.filter(record =>
		{
			return this.allRecords.findIndex(rec => rec.Id === record.Id) > -1;
		});
	}

	ListMoverSelectEmailUDFRecordControlWidgetViewModel.prototype.updateAllRecords = function()
	{
		let self = this;
		let records = this.allOriginalRecords;
		let emailUDFs = this.options.emailUDFs();
		emailUDFs = emailUDFs.filter(udf => udf.obFieldChecked());

		let newRecords = [];
		if (records && records.length > 0)
		{
			records.forEach(record =>
			{
				emailUDFs.forEach(udf =>
				{
					if (!!record[udf.OriginalName])
					{
						let newRecord = $.extend(true, {}, record, {
							'UDFEmail_EmailField': udf.DisplayName,
							'UDFEmail_Email': record[udf.OriginalName]
						});
						newRecord.originalId = record.Id;
						newRecord.Id = record.Id + '_' + udf.UDFId;
						newRecords.push(newRecord);
					}
				});
			});
		}
		self.allRecords = newRecords;
		if (self.options.invalidIds &&
			self.options.invalidIds.length > 0)
		{
			self.allRecords = self.allRecords.filter(function(item)
			{
				return !Enumerable.From(self.options.invalidIds).Any("$=='" + item.Id + "'");
			});
		}
		this.maxReocrdCount = this.allRecords.length;
	}
})();
