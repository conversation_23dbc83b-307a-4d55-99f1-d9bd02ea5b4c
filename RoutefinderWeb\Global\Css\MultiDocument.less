.document-grid .header.setting-page-title,
.main-body .page-title {
	padding-left: 16px;
	font-weight: bold;
	letter-spacing: 3px;
	font-size: 27px;
	color: #262626;
	line-height: 56px;
	white-space: nowrap;
	font-family: "SourceSansPro-SemiBold";
	height: 56px;
}

.docs {
	.resizable-doc {
		height: 100%;
		float: left;
		width: 100%;
		display: flex;

		.left-doc,
		.right-doc {
			height: 100%;
			overflow: unset;

			.grid-doc,
			.other-doc,
			.main-body {
				height: 100%;
				width: 100%;
			}

			.calendar-view
			{
				height: 100%;
				.desktop-header
				{
					border-bottom: 2px solid #f2f2f2;
					padding-left:5px;
				}

				.grid-icons
				{
					padding-left: 21px;
				}

				.scheduler-wrap {
					height: calc(100% - 90px);
					margin-left: 3px;
					float: left;
					.scheduler-container
					{
						height: 100%;
						.kendoscheduler
						{
							height: 100%;
							.k-scheduler-toolbar
							{
								.k-view-day.k-selected, .k-view-week.k-selected
								{
									background-color: #D0503C;
									border-color: #D0503C;
								}
							}
							.k-scheduler-header
							{
								.k-scheduler-table {
									th 
									{
										border-color: #c5c5c5;
										vertical-align: middle;
										&:first-child {
											border-left: none;
										}
										&.k-today::before
										{
											border-bottom-color: #D0503C;
										}
									}
								}
							}

							.k-scheduler-content {
								overflow-x: hidden;
								overflow-y: auto;
								.k-task
								{
									border: none !important;
								}
								
								tr.k-hover .k-scheduler-datecolumn
								{
									background-color: #f2f2f2;
								}

								.k-current-time
								{
									width:unset;
								}

								.k-scheduler-table
								{
									td 
									{
										border-color: #c5c5c5;
										vertical-align: middle;
										&.selected {
											border: 1px solid black;
										}

										&.k-scheduler-timecolumn
										{
											span.k-svg-i-caret-alt-right
											{
												position: absolute;
												right: -4px;
											}
										}

										&.k-scheduler-cell
										{
											vertical-align: top;
										}
									}
								}

								div.k-event.selected
								{
									border-color: black !important;
								}

							}

							.k-scheduler-weekview,
							.k-scheduler-dayview {
								div[role='button'] {
									.k-event-template {
										margin-top: 15px;
										text-align: center;
									}
								}
							}
						}
						
					}
				}
			}
		}

		.right-doc {
			margin-left: 3px;
			overflow: auto;

			&:has(.splitmap:not([style*='display: none'])) {
				overflow: hidden;
			}
		}

		.splitmap {
			width: 100%;
			height: 100%;
			position: relative;

			.map_panel {
				width: 100%;
				height: 100%;
			}
		}

		.resize-handler {
			position: absolute;
			left: 0;
			width: 3px;
			height: 100%;
			background-color: #4b4b4b;
			cursor: ew-resize;
			z-index: 2040;

			.sliderbar-button {
				position: absolute;
				left: 0;
				top: calc(~"50% - 19.5px");
				width: 44px;
				height: 39px;
				background-color: transparent;
				background: linear-gradient(90deg, #484848, #484848) no-repeat left center / 8px 39px;
				border-radius: 0;
				cursor: pointer;

				&.slider-tapped,
				&:hover {
					background: repeating-linear-gradient(90deg, #868686, #868686 1px, #4b4b4b 1px, #4b4b4b 4px) no-repeat 16px center / 9px 18px, #4b4b4b !important;
				}
			}

			.panel-handler {
				position: absolute;
				width: 20px;
				height: 34px;
				top: calc(~"50% - 17px");
				margin-left: -20px;
				background-color: #383838;
				background-size: 12px 12px;
				background-repeat: no-repeat;
				background-position: center;
				cursor: pointer;
				background-image: url('../img/Routing Map/right-arrow-white.png');

				&.close {
					background-image: url('../img/Routing Map/left-arrow-white.png');
					opacity: 1;
				}
			}
		}

		.doc {
			position: relative;
		}
	}
}