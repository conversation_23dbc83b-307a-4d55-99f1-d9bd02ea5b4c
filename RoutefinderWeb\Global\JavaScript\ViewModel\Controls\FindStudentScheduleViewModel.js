(function()
{
	createNamespace("TF.Control").FindStudentScheduleViewModel = FindStudentScheduleViewModel;

	function FindStudentScheduleViewModel(selectedCount, options, obDisableControl)
	{
		var self = this;
		this.obSpecifyRecords = ko.observableArray([
			{ value: 'allInFilter', text: 'All records in filter' },
			{ value: 'selected', disable: selectedCount == 0, text: 'Current ' + selectedCount + ' selected ' + TF.getSingularOrPluralTitle("record", selectedCount) }
		]);
		var selectedSpecifyRecord = self.obSpecifyRecords()[selectedCount > 0 ? 1 : 0];
		self.obSelectedSpecifyRecords = ko.observable(selectedSpecifyRecord.value);
		this.obCreateDoorToDoorStops = ko.observable();
		this.obUseStopsInStopPool = ko.observable();
		this.obInteractive = ko.observable(!!(options && options.interactive));
		this.stopPoolCategories = ko.observableArray();
		this.selectedStopPoolCategory = ko.observable();
		this.showSpecifyRecords = !(options && options.hideRecords);
		this.selectedName = ko.computed(function()
		{
			return self.selectedStopPoolCategory() ? self.selectedStopPoolCategory().Name : '';
		});
		this.disable = ko.computed(function()
		{
			return !self.obUseStopsInStopPool();
		});

		this.initStopPoolCategory().then(function(data)
		{
			self.stopPoolCategories(data);
			self.selectedStopPoolCategory(self.stopPoolCategories()[0]);
		});

		this.obDisableControl = obDisableControl;
		this.obPastDate = ko.observable(true);
		this.obCurrentDate = ko.observable(true);
		this.obFutureDate = ko.observable(true);
		this.obScheduleTo = ko.computed(() =>
		{
			return this.obPastDate() || this.obCurrentDate() || this.obFutureDate();
		});
		this.obScheduleTo.subscribe(value =>
		{
			this.obDisableControl(!value);
		});
	}

	FindStudentScheduleViewModel.prototype.initStopPoolCategory = function()
	{
		return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "stoppoolcategories"), {
			paramData: {
				dbid: tf.datasourceManager.databaseId
			}
		}).then(function(apiResponse)
		{
			return apiResponse.Items;
		});
	};

	FindStudentScheduleViewModel.prototype.apply = function()
	{
		return {
			specifyRecords: this.obSelectedSpecifyRecords(),
			interactive: this.obInteractive(),
			createDoorToDoorStops: this.obCreateDoorToDoorStops(),
			useStopsInStopPool: this.obUseStopsInStopPool(),
			selectedStopPoolCategory: this.selectedStopPoolCategory() ? this.selectedStopPoolCategory().Id : 0,
			scheduleTo: {
				pastDates: this.obPastDate(),
				currentDate: this.obCurrentDate(),
				futureDates: this.obFutureDate()
			}
		};
	};

})();