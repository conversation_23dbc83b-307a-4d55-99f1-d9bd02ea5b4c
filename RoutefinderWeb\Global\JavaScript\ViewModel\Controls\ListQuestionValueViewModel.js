(function()
{
	createNamespace("TF.Control").ListQuestionValueViewModel = ListQuestionValueViewModel;

	function ListQuestionValueViewModel(options)
	{
		var self = this;
		self.options = options;
		self.field = self.options.field;
	}

	ListQuestionValueViewModel.prototype.init = function(viewModel, element)
	{
		let self = this;
		let questionType = self.field.QuestionType.replaceAll(' ', '');
		self.listQuestionValueControl = new TF.Control.Form[questionType + 'Question'](self.field, self.field.DataTypeId);	
		self.$element = $(element);	
		self.$element.find(".previewContainer").append(self.listQuestionValueControl.element);
		self.$element.find(".question-title").css("padding-bottom", "10px");
	};

	ListQuestionValueViewModel.prototype.apply = function()
	{
		return Promise.resolve(true).then(() =>
		{
			return this.listQuestionValueControl.value;
		})	
	};

	ListQuestionValueViewModel.prototype.dispose = function()
	{
		this.obText = null;
		this.listQuestionValueControl.dispose();
	};
})();