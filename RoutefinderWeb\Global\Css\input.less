﻿@import (reference) 'main';

.tf-input {
	height: 100%;
	width: 100%;
}

.tf-input-textbox input {
	height: calc(~"100% - 2px");
	margin: 1px;
	width: calc(~"100% - 2px");
}

/*.tf-input-datebox input
{ 
	height: calc(100% - 2px);
	width: calc(100% - 2px);
}

.tf-datebox-container
{
	display: inline-block;
	height: calc(100% - 2px);
	position: relative;
	width: calc(100% - 35px);
}

.tf-datebox-container > input.tf-input-datebox
{
	height: 100%;
	vertical-align: top;
	width: calc(100% - 20px);
}

.tf-datebox-container > .tf-icon
{
	background-color: silver;
	cursor: pointer;
	display: inline-block;
	height: 16px;
	margin: 2px;
	width: 16px;
}*/

.input-group.tf-datebox>input.form-control {
	border-right: none;
}

.input-group-addon.tf-date-picker {
	background-color: #fff;
	border-left: none;
}

.tf-calendar-icon {
	background-image: url(../img/Icons/calendar.gif);
	cursor: pointer;
	display: inline-block;
	height: 16px;
	width: 16px;
}

.tf-popup {
	position: fixed;
	overflow: auto;
	width: 109px;
	max-height: 111px;
	border: 1px solid #c5c5c5;
	margin-top: 45px;
	z-index: 1;
	background-color: #fff;
	padding: 2px;
	display: none;

	ul {
		height: 100%;

		li {
			display: block;
			padding: 3px 20px;
			clear: both;
			font-weight: normal;
			line-height: 1.42857143;
			color: #333;
			white-space: nowrap;
			font-size: 12px;
			min-height: 23px;
			overflow: hidden;
			text-overflow: ellipsis;
			cursor: pointer;

			&:hover {
				color: darkgrey;
				text-decoration: none;
				outline: 0;
				background-color: #DDEDFB;
			}
		}
	}
}

.grid-color {
	border: 1px solid #ccc;
	padding: 8px 4px 6px 8px;
	height: 68px;
	overflow: auto;
	width: 280px;

	.colorbox {
		float: left;
		margin: 0 2px 2px 0;
		width: auto;
	}

	.k-colorpicker .k-input-inner,
	.k-colorpicker .k-color-preview,
	.k-colorpicker .k-selected-color {
		width: 20px !important;
		height: 20px !important;
	}

	span.k-colorpicker.k-picker,
	span.k-colorpicker {
		width: 20px;
	}

	.k-picker-wrap {
		border: none;
		width: 20px;
		height: 20px;
		line-height: 20px;
		margin: 0;
		padding: 0;
	}

	.k-colorpicker .k-input-button,
	.k-picker-wrap .k-select {
		display: none;
	}

	.k-colorpicker {
		>.k-input-inner {
			padding: unset;

			.k-color-preview {
				cursor: pointer;
				border-width: 0px;
				border-radius: 0;
			}
		}
	}
}

.k-colorpicker {
	.k-input-inner {
		border-radius: 0;
		width: 100%;
		height: 22px;
	}

	.k-input-button {
		display: none;
	}
}

.k-colorpicker .k-color-preview,
.k-colorpicker .k-selected-color {
	border-radius: 0;
	height: 100%;
	width: 100%;
	position: absolute;
}

span.k-colorpicker.k-header {
	width: 100%;
	display: inline-block;
}

.k-dropdown.form-control {
	width: 100%
}

.k-list-container.k-popup.k-autocomplete-popup[aria-hidden="true"] {
	position: absolute;
}

.k-popup.k-list-container {
	padding: 0px;
}

.k-textbox:focus,
.k-dropdown-wrap.k-focus,
.k-dropdown-wrap.k-hover {
	box-shadow: none;
	-webkit-box-shadow: none;
	background-color: transparent;
}

.check-button {
	background-color: transparent;
	color: @systemForeColor;
	padding: 2px 10px;
	border-radius: 18px;
	margin: 0 5px;
	border-style: none;
	cursor: pointer;
	outline: none;
	height: 22px;

	&:hover {
		background-color: @toggleButtonUncheckedColor;
		color: @systemForeColor;
	}

	&.checked {
		background-color: @systemColor;
		color: @systemReversedForeColor;
	}

	&:disabled {
		opacity: 0.5;
		pointer-events: none;
	}
}

.k-list-container .k-list-optionlabel.k-selected.k-focus {
	color: #333;
}

.form-group .k-input.k-numerictextbox {
	display: flex;
	width: 100%;
	border-radius: 0;
	font-size: 12px;

	.k-numeric-wrap.k-focus,
	.k-numeric-wrap.k-hover {
		box-shadow: none;

		.k-input {
			border-radius: 0;
		}
	}
}

.timepicker-picker,
.bootstrap-datetimepicker-widget {
	a {
		color: @systemColor;
	}

	.btn-primary,
	table td.active,
	table td.active:hover,
	table td span.active {
		background-color: @systemColor;
		border-color: @systemColor;
	}
}

input[type="file"]:focus {
	outline-color: #d5d5d5;
	outline-width: 1px;
	outline-style: solid;
	outline-offset: 0;
}

.k-textbox:hover {
	border-color: #d5d5d5;
}

input.k-textbox:hover {
	color: #333;
}

.k-upload.k-header {

	.k-button.k-upload-selected,
	.k-button.k-clear-selected {
		display: none;
	}

	.k-file-size {
		display: none;
	}

	.k-file-extension-wrapper {
		display: none;
	}

	.k-button:active,
	.k-button.k-active {
		color: #2e2e2e;
		border-color: #c5c5c5;
		background-color: #e9e9e9;
	}
}

.restore-plus-archive,
.restore-pro-archive {
	.k-grid-content-expander {
		width: 100% !important;
	}

	.k-button.k-upload-button {
		input {
			left: 0;
			top: 0;
			font-size: 0px !important;
			width: 100%;
		}
	}
}