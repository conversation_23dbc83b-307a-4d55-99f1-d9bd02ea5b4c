(function()
{
	if (!kendo || !kendo.ui || !kendo.ui.Grid || !kendo.ui.Grid.fn)
	{
		return;
	}

	// this method comes from kendo.all.js kendoGrid autoFitColumn method.
	kendo.ui.Grid.fn.autoFitColumnHeaders = function()
	{
		var self = this,
			headerTable = self.thead.parent(),
			notGroupOrHierarchyCol = "col:not(.k-group-col):not(.k-hierarchy-col)";

		var tables = headerTable;

		var allColumnWidth = 0;
		for (var index = 0; index < self.columns.length; index++)
		{
			var col,
				th = headerTable.find("[" + kendo.attr("index") + "='" + index + "']"),
				column = self.columns[index];
			if (self.options.scrollable)
			{
				col = headerTable.find(notGroupOrHierarchyCol).eq(index);
			}
			else
			{
				var contentTable = column.locked ? self.lockedTable : self.table;
				col = contentTable.children("colgroup").find(notGroupOrHierarchyCol).eq(index);
			}

			col.width("");
			tables.css("table-layout", "fixed");
			col.width("auto");
			tables.addClass("k-autofitting");
			tables.css("table-layout", "");

			var newColumnWidth = th.outerWidth() + 1;
			col.width(newColumnWidth);
			column.width = newColumnWidth;
			allColumnWidth += newColumnWidth;
		}

		var headerTableWidth = self.table.outerWidth();
		if (allColumnWidth < headerTableWidth)
		{
			var detal = headerTableWidth - allColumnWidth;
			colDetal = Math.floor(detal / self.columns.length);
			allColumnWidth = 0;
			for (var index = 0; index < self.columns.length; index++)
			{
				var col,
					column = self.columns[index];
				if (self.options.scrollable)
				{
					col = headerTable.find(notGroupOrHierarchyCol).eq(index);
				}
				else
				{
					var contentTable = column.locked ? self.lockedTable : self.table;
					col = contentTable.children("colgroup").find(notGroupOrHierarchyCol).eq(index);
				}

				if (index == self.columns.length - 1)
				{
					column.width = headerTableWidth - allColumnWidth;
				}
				else
				{
					column.width += colDetal;
				}

				col.width(column.width);
				allColumnWidth += column.width;
			}
		}

		if (self.options.scrollable)
		{
			var cols = headerTable.find("col"),
				colWidth,
				totalWidth = 0;
			for (var idx = 0, length = cols.length; idx < length; idx += 1)
			{
				colWidth = cols[idx].style.width;
				if (colWidth && colWidth.indexOf("%") == -1)
				{
					totalWidth += parseInt(colWidth, 10);
				}
				else
				{
					totalWidth = 0;
					break;
				}
			}

			if (totalWidth)
			{
				tables.each(function()
				{
					this.style.width = totalWidth + "px";
				});
			}
		}

		tables.removeClass("k-autofitting");
		self._applyLockedContainersWidth();
		self._syncLockedContentHeight();
		self._syncLockedHeaderHeight();
	};
})();