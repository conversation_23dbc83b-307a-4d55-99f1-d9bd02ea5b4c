(function()
{
	createNamespace("TF.Control").SingleInputWithCounterViewModel = SingleInputWithCounterViewModel;

	function SingleInputWithCounterViewModel(options)
	{
		TF.Control.SingleInputViewModel.call(this, options);
	}
	SingleInputWithCounterViewModel.prototype = Object.create(TF.Control.SingleInputViewModel.prototype);
	SingleInputWithCounterViewModel.prototype.constructor = SingleInputWithCounterViewModel;

	SingleInputWithCounterViewModel.prototype.getInputElement = function()
	{
		return this.$element.find("textarea");
	}

	SingleInputWithCounterViewModel.prototype.init = function(viewModel, element)
	{
		TF.Control.SingleInputViewModel.prototype.init.apply(this, arguments);
		tf.udgHelper.bindCounterBoxEvent(this.$element.find("textarea"), this.maxLength, this.$element.find("small"), true);
	};
})();
