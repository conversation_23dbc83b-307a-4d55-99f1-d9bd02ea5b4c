﻿(function()
{
	createNamespace("TF.Control").CopyMergeTemplateTypeViewModel = CopyMergeTemplateTypeViewModel;

	function CopyMergeTemplateTypeViewModel(model)
	{
		var self = this;
		self.obEntityDataModel = ko.observable(model);
	}

	CopyMergeTemplateTypeViewModel.prototype.init = function(viewModel, el)
	{
		var self = this,
			nameUniqueCheck = function(value)
			{
				if (!value || !value.trim()) return true;

				return tf.promiseAjax.get(
					pathCombine(tf.api.apiPrefixWithoutDatabase(), "mergetemplatetypes"),
					{ async: false, data: { name: value } },
					{ overlay: false }
				).then(function(apiResponse)
				{
					return !(apiResponse.Items[0])
				});
			};

		var validatorFields = {
			name: {
				trigger: "blur change",
				validators: {
					notEmpty: {
						message: "required"
					},
					callback: {
						message: "must be unique",
						callback: nameUnique<PERSON>he<PERSON>
					}
				}
			},
		};

		self.validator = ($(el).bootstrapValidator({
			excluded: [":hidden", ":not(:visible)", ":disabled"],
			live: "enabled",
			fields: validatorFields
		})).data("bootstrapValidator");
	};

	CopyMergeTemplateTypeViewModel.prototype.apply = function(viewModel, e)
	{
		var self = this;
		return self.validate().then(function(valid)
		{
			return valid ? self.save() : Promise.resolve(false);
		});
	};

	CopyMergeTemplateTypeViewModel.prototype.save = function(viewModel, e)
	{
		return Promise.resolve(true);
	};

	CopyMergeTemplateTypeViewModel.prototype.validate = function(viewModel, e)
	{
		return this.validator ? this.validator.validate() : Promise.resolve(true);
	};
})();

