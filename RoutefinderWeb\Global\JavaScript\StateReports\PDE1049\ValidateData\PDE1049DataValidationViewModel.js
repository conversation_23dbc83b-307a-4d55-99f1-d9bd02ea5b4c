(function()
{
	createNamespace('TF.Control').PDE1049DataValidationViewModel = PDE1049DataValidationViewModel;

	const DEFAULT_METHOD_INDEX = 2;
	const METHOD_OPTIONS = [
		{
			name: "1",
			label: "Best Day Overall",
			field: "BestDayOverall",
			revenueValue: ko.observable("")
		},
		{
			name: "2",
			label: "Best Day by Vehicle",
			field: "BestDaybyVehicle",
			revenueValue: ko.observable("")
		},
		{
			name: "0",
			label: "Monthly Average",
			field: "MonthlyAverage",
			revenueValue: ko.observable("")
		}
	];
	const VEHICLE_FIELDS = [
		'BusNum', 'ContractorName', 'Vin', 'YearMade',
		'Capacity', 'PDE_CongestedHours', 'PDE_CongestedMinutes',
		'PDE - One Way', 'PDE - Spare', 'PDE_ContractorTypeOfService',
		'PDE_ContractorAmount', 'PDE_ContractorNotificationNumber',
		'PDE_NumberOfDays', 'PDE_TotalAnnualMiles', 'PDE_DailyMilesWith',
		'PDE_DailyMilesWithout', 'PDE_NumberOfPupilsAssigned', 'PDE_ActivityNumberOfRuns',
		'PDE_ActivityNumberOfDays', 'PDE_ActivityDailyMilesWith', 'PDE_ActivityDailyMilesWithout',
		'PDE_NumberOfRuns', 'PDE - Starting Odometer', 'PDE - Ending Odometer',
	];

	const VEHICLE_FIELDS_EXPORT = [
		'PDE_ContractorNotificationNumber', 'PDE_ContractorTypeOfService', 'ContractorName', 'PDE_ContractorAmount',
		'BusNum', 'Vin', 'YearMade',
		'Capacity', 'PDE_CongestedHours', 'PDE_CongestedMinutes',
		'PDE - One Way', 'PDE - Spare',
		'PDE_NumberOfDays', 'PDE_TotalAnnualMiles', 'PDE_DailyMilesWith',
		'PDE_DailyMilesWithout', 'PDE_NumberOfPupilsAssigned', 'PDE_ActivityNumberOfRuns',
		'PDE_ActivityDailyMilesWith', 'PDE_ActivityDailyMilesWithout', 'PDE_ActivityNumberOfDays',
		'PDE - Starting Odometer', 'PDE - Ending Odometer',
	];

	function PDE1049DataValidationViewModel(options)
	{
		const dataType = "vehicle";
		const defaultMethod = METHOD_OPTIONS[DEFAULT_METHOD_INDEX];
		const { SchoolYear, AdminUnitNumber } = options;

		this.$element = null;
		this.vehicleKendoGrid = null;
		this.errorKendoGrid = null;
		this.options = options;
		this.dataType = dataType;


		this.vehicleUdfs = [];
		this.vehicleUdfDict = {};
		this.vehicleColumns = [];
		this.mapCache = new Map();
		this.tripIds = [];

		this.obSchoolYear = ko.observable(SchoolYear)
		this.obAdminUnitNumber = ko.observable(AdminUnitNumber);
		this.obCostIndex = ko.observable()
		this.obRatio = ko.observable();
		this.obSelectedMethod = ko.observable(defaultMethod.name);
		this.obMethods = ko.observableArray(METHOD_OPTIONS);
		this.obVehicleGridUpdated = ko.observable(false);

		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();

		this.vehicleGridIds = [];
		this.validationErrorList = [];
		this.fieldNames = [];

		this.obSelectedMethod.subscribe((val) =>
		{
			this.initDataGrid();
		});
	}

	/**
	 * Initialization work when modal is ready.
	 *
	 * @param {*} model
	 * @param {*} element
	 */
	PDE1049DataValidationViewModel.prototype.initialize = function(model, element)
	{
		const self = this;

		self.$element = $(element);

		// prepare data columns
		const columns = tf.PDE1049DataValidationVehicleGridDefinition.getColumnsByFieldNames(VEHICLE_FIELDS);
		const udfs = tf.UDFDefinition.get(self.dataType).userDefinedFields;
		const udfDict = {};

		udfs.forEach(field => udfDict[field.OriginalName] = field.FieldName);

		self.vehicleColumns = columns;
		self.vehicleUdfs = udfs;
		self.vehicleUdfDict = udfDict;

		self.initDataGrid();
		self.initValidators();
	};

	PDE1049DataValidationViewModel.prototype.initDataGrid = function()
	{
		// init vehicle grid and errors
		const self = this;
		let { TripIds } = self.options;
		const kendoColumns = self.getKendoGridColumns(self.vehicleColumns);
		self.fieldNames = kendoColumns.map(o => o.field).filter(s => s);

		if (!Array.isArray(TripIds) || TripIds.length === 0) 
		{
			TripIds = null;
		}

		let schoolYear = this.obSchoolYear().split("-");
		const urlParam = {
			startYear: schoolYear[0],
			endYear: schoolYear[1],
			pde1049Method: self.obSelectedMethod(),
		};
		self.tripIds = TripIds;
		const extraCondition = { TripIds };

		self.getMethodRevenue();

		tf.loadingIndicator.show();
		tf.dataTypeHelper.getRecordByIdsAndColumns(
			null,
			self.dataType,
			null,
			self.fieldNames,
			["ContractorId"],
			null,
			urlParam,
			extraCondition,
		).then(vehicleList =>
		{
			// save vehicleIds for export
			self.vehicleGridIds = vehicleList.map(item => item.Id);
			// init vehicle grid data
			self.formatVehicleList(vehicleList);

			// init validation error grid data
			const errorList = self.getValidationErrors(vehicleList, self.vehicleColumns);
			//save error for export 
			self.validationErrorList = errorList;
			const errorColumns = tf.PDE1049DataValidationVehicleGridDefinition.getValidationErrorGridDefinition();
			const errorKendoColumns = self.getKendoGridColumns(errorColumns);

			self.initVehicleGrid(vehicleList, errorList, kendoColumns);
			self.initValidationErrorGrid(errorList, errorKendoColumns);
			tf.loadingIndicator.tryHide();
		});
	}

	PDE1049DataValidationViewModel.prototype.getMethodRevenue = function()
	{
		const self = this;
		let schoolYear = this.obSchoolYear().split("-");
		const urlParam = {
			startYear: schoolYear[0],
			endYear: schoolYear[1],
			costIndex: self.obCostIndex() || 0,
			ratio: self.obRatio() || 0,
			pde1049Method: self.obSelectedMethod(),
		};

		let tripIds = self.tripIds;
		const extraCondition = { tripIds };
		tf.loadingIndicator.show();
		tf.dataTypeHelper.getRecordByIdsAndColumns(
			null,
			"vehiclerevenues",
			null,
			self.fieldNames,
			["ContractorId"],
			null,
			urlParam,
			extraCondition,
		).then(vehiclerevenueList =>
		{
			let methodOptions = METHOD_OPTIONS.map(d =>
			{
				d.revenueValue(` ($${self.moneyFormat(vehiclerevenueList.find(f => f.Name === d.field).Value)})`);
				return d;
			})

			this.obMethods(methodOptions);

			tf.loadingIndicator.tryHide();
		});
	}

	PDE1049DataValidationViewModel.prototype.moneyFormat = function(num)
	{
		return String(parseInt(num)).replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
	}

	/**
	 * Initialize an editable vehicle grid.
	 *
	 * @param {Array<any>} data
	 * @param {Array<any>} columns
	 */
	PDE1049DataValidationViewModel.prototype.initVehicleGrid = function(vehicleList, errorList, columns)
	{
		const self = this;
		const $grid = self.$element.find(".vehicle-grid.kendo-grid");
		if (self.vehicleKendoGrid)
		{
			self.vehicleKendoGrid.destroy();
			$grid.empty();
		}

		// Hide this due to June Release Plan.
		// const errorMap = new Map();

		// errorList.forEach(e =>
		// {
		// 	const identifier = e.Vehicle;
		// 	if (!errorMap.has(identifier))
		// 	{
		// 		errorMap.set(identifier, []);
		// 	}

		// 	errorMap.get(identifier).push(e);
		// });

		// const formatContent = val => (val === undefined || val === null) ? "" : val;
		const source = vehicleList.map(entity =>
		{
			var item = Object.assign({}, entity);
			for (var key in item)
			{
				var column = columns.find(c => c.field == key);
				if (column && column.template)
				{
					let result = column.template(item);
					if (result === "0")
					{
						item[key] = 0;
					}
				}
			}
			return item;
		});

		self.vehicleKendoGrid = $grid.kendoGrid({
			dataSource: {
				data: source,
				aggregate: [
					{ field: "Id", aggregate: "count" } // use this placeholder to make height calculate correctly
				]
			},
			scrollable: true,
			sortable: true,
			editable: false,
			selectable: "single",
			columns: columns,
			dataBound: function() 
			{
				self.updateGridFooter(this, "Vehicle");
			}
			// Hide this due to June Release Plan.
			// rowTemplate: (item) =>
			// {
			// 	const busNum = item["BusNum"];
			// 	const errors = errorMap.get(busNum);
			// 	let rowStatus = "";

			// 	if (Array.isArray(errors) && errors.length > 0)
			// 	{
			// 		rowStatus = errors.some(e => e.Type === "error") ? "error" : "warning";;
			// 	}

			// 	return `<tr class="k-master-row ${rowStatus}" data-kendo-uid="${item.uid}" role="row">
			// 		${columns.map(o =>
			// 	{
			// 		let cellStatus = "";
			// 		if (rowStatus && errors.some(e => e.Field === o.title))
			// 		{
			// 			cellStatus = "error";
			// 		}
			// 		return `<td class="${cellStatus}" field="${o.field}" role="gridcell">${formatContent(item[o.field])}</td>`;
			// 	}).join("")}
			// 	</tr>`;
			// }
		}).data("kendoGrid");

		// need to reset scroll status
		self.vehicleKendoGrid.scrollables.scrollTop(0);
		self.vehicleKendoGrid.scrollables.scrollLeft(0);
	};

	/**
	 * Initialize vehicle data validation error grid.
	 *
	 * @param {Array<any>} data
	 * @param {Array<any>} columns
	 */
	PDE1049DataValidationViewModel.prototype.initValidationErrorGrid = function(errorList, columns)
	{
		const self = this;
		const $grid = self.$element.find(".error-grid.kendo-grid");
		if (self.errorKendoGrid)
		{
			self.errorKendoGrid.destroy();
			$grid.empty();
		}

		self.errorKendoGrid = $grid.kendoGrid({
			dataSource: {
				data: errorList,
				aggregate: [
					{ field: "Id", aggregate: "count" } // use this placeholder to make height calculate correctly
				]
			},
			scrollable: true,
			sortable: true,
			selectable: "single",
			columns: columns,
			dataBound: function()
			{
				self.updateGridFooter(this, "Error");
			},
		}).data("kendoGrid");

		// Hide this due to June Release Plan.
		// $grid.on("click", "tr", (e) =>
		// {
		// 	// prepare
		// 	const uid = e.target.closest("tr").attributes["data-kendo-uid"].value;
		// 	const data = self.errorKendoGrid.dataSource.getByUid(uid);
		// 	const targetVehicleId = data["Vehicle"];
		// 	const targetVehicle = self.vehicleKendoGrid.dataItems().find(o => o["BusNum"] === targetVehicleId);
		// 	const $scroll = self.vehicleKendoGrid.content;

		// 	// vertical scroll
		// 	const $targetRow = $scroll.find(`tr[data-kendo-uid="${targetVehicle.uid}"]`);
		// 	const targetScrollTop = $scroll.scrollTop() + $targetRow.position().top;
		// 	const maxScrollTop = $scroll[0].scrollHeight - $scroll[0].clientHeight;

		// 	$scroll.scrollTop(Math.min(targetScrollTop, maxScrollTop));

		// 	// horizontal scroll
		// 	const $targetCell = $targetRow.find(`td[field="${data["Field"]}"]`);
		// 	const targetScrollLeft = $scroll.scrollLeft() + $targetCell.position().left;
		// 	const maxScrollLeft = $scroll[0].scrollWidth - $scroll[0].clientWidth;

		// 	$scroll.scrollLeft(Math.min(targetScrollLeft, maxScrollLeft));
		// });

		self.errorKendoGrid.scrollables.scrollTop(0);
		self.errorKendoGrid.scrollables.scrollLeft(0);
	};

	/**
	 * Generate and append grid footer to show count.
	 *
	 * @param {*} kendoGrid
	 * @param {*} name
	 * @param {*} pluralName
	 */
	PDE1049DataValidationViewModel.prototype.updateGridFooter = function(kendoGrid, name, pluralName)
	{
		let $footer = kendoGrid.element.find(".k-grid-footer");
		let recordName = name || "Record";

		if ($footer.length === 0)
		{
			$footer = $("<div/>", { class: "k-grid-footer" }).appendTo(kendoGrid.element);
		}

		const count = kendoGrid.dataItems().length;

		if (count !== 1)
		{
			recordName = pluralName || `${recordName}s`;
		}

		const footerLabel = `${kendoGrid.dataItems().length} ${recordName}`;

		$footer.text(footerLabel);
	};

	PDE1049DataValidationViewModel.prototype.initValidators = function()
	{
		this.$element.bootstrapValidator({
			excluded: [':disabled'],
			live: 'enabled',
			fields: tf.PDE1049DataValidationVehicleGridDefinition.getPDE1049Validators()
		});

		this.pageLevelViewModel.load(this.$element.data("bootstrapValidator"));
	};

	/**
	 * Format vehicle data, especially for UDFs.
	 *
	 * @param {*} vehicleList
	 */
	PDE1049DataValidationViewModel.prototype.formatVehicleList = function(vehicleList)
	{
		const udfs = this.vehicleUdfs;
		const columns = this.vehicleColumns;

		vehicleList.forEach(data =>
		{
			udfs.forEach(udf =>
			{
				data[udf.FieldName] = data[udf.OriginalName];
				delete data[udf.OriginalName];
			});

			columns.forEach(col =>
			{
				const fieldName = col.FieldName;
				switch (col.type)
				{
					case "number":
						data[fieldName] = this.formatNumberValue(data[fieldName], col);
						break;
					default:
						break;
				}
			});
		});
	};

	/**
	 * Format number field value.
	 *
	 * @param {*} value
	 * @param {*} column
	 * @return {*} 
	 */
	PDE1049DataValidationViewModel.prototype.formatNumberValue = function(value, column)
	{
		if (!isNullObj(column.precision))
		{
			const temp = Math.pow(10, +column.precision);
			value = Math.round(value * temp) / temp;
		}

		return value;
	};

	/**
	 * Get grid columns in kendo format.
	 *
	 * @return {*} 
	 */
	PDE1049DataValidationViewModel.prototype.getKendoGridColumns = function(gridColumns)
	{
		const udfDict = this.vehicleUdfDict;
		return gridColumns.map(column =>
		({
			field: udfDict[column.FieldName] || column.FieldName,
			title: column.DisplayName,
			// Hide this due to June Release Plan.
			//editable: () => column.IsEditable,
			type: column.type,
			width: column.Width,
			template: column.template,
			hidden: column.hidden,
			// editor: (container, kendoOptions) =>
			// {
			// 	this.bindEditor(container, kendoOptions, column);
			// },
		}));
	};

	PDE1049DataValidationViewModel.prototype.updateVehicleData = function()
	{
		// Hide this due to June Release Plan.
		// const kendoGrid = this.vehicleKendoGrid;
		// const editedItems = kendoGrid.dataSource.data().filter(item => item.dirty);

		// tf.promiseAjax.patch(pathCombine(tf.api.apiPrefixWithoutDatabase(), "dashboards"), {
		// 	data: records.map(item =>
		// 	{
		// 		return {
		// 			Id: item.Id,
		// 			Op: "replace",
		// 			Path: "IsFavorite",
		// 			Value: !removeFavorite
		// 		}
		// 	})
		// }).then(() => { this.searchGrid.refresh(); });

	};

	PDE1049DataValidationViewModel.prototype.refreshVehicleData = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(isValid =>
			{
				if (!isValid)
				{
					return false;
				}

				this.initDataGrid();
				return true;
			});

	};

	PDE1049DataValidationViewModel.prototype.apply = function()
	{
		let self = this;
		return this.pageLevelViewModel.saveValidate()
			.then(isValid =>
			{
				if (!isValid)
				{
					return false;
				}

				//TODO
				self.exportGridData();
				return true;
			});
	};

	PDE1049DataValidationViewModel.prototype.bindEditor = function(container, kendoOptions, column)
	{
		if (column.hasOwnProperty("type") && column.type === "boolean")
		{
			return this.initBooleanDropdown(container, kendoOptions);
		}

		if (column.hasOwnProperty("EditType") && column.EditType.format === "ComboBox")
		{
			return this.initComboBox(container, kendoOptions, column);
		}
	}

	PDE1049DataValidationViewModel.prototype.initBooleanDropdown = function(container, kendoOptions)
	{
		var $dropdown = $(`<input name="${kendoOptions.field}"/>`)
			.appendTo(container)
			.kendoDropDownList({
				dataSource: ["false", "true"],
				change: (e) =>
				{
					kendoOptions.model.Title = e.sender.text();
				}
			});

		$dropdown.data("kendoDropDownList").value(kendoOptions.model);
	};

	PDE1049DataValidationViewModel.prototype.initComboBox = function(container, kendoOptions, column)
	{
		var $dropdown = $(`<input name="${kendoOptions.field}"/>`)
			.appendTo(container)
			.kendoComboBox({
				dataTextField: "Text",
				dataValueField: "Value",
				dataSource: {
					transport: {
						read: (kendoOptions) =>
						{
							if (this.mapCache.has(column.FieldName))
							{
								kendoOptions.success(this.mapCache.get(column.FieldName));
							}
							else
							{
								column.EditType.getSource()
									.then(data =>
									{
										kendoOptions.success(data);
										this.mapCache.set(column.FieldName, data);
									});
							}
						}
					}
				},
				change: (e) =>
				{
					let text = e.sender.text();
					kendoOptions.model.Title = text;
					kendoOptions.model.set(column.FieldName, text);
					let contractor = this.mapCache.get(column.FieldName).find(d => d.Text === text);
					kendoOptions.model.set(column.EditType.entityKey, contractor && contractor.Value);
				}
			});

		$dropdown.data("kendoComboBox").value(kendoOptions.model.get(column.EditType.entityKey));
	};

	/**
	 * Validate vehicle data and get errors if any.
	 *
	 * @param {*} vehicles
	 * @param {*} columns
	 */
	PDE1049DataValidationViewModel.prototype.getValidationErrors = function(vehicles, columns)
	{
		const errors = [];
		const validationColumns = columns.filter(c => Array.isArray(c.Validations) && c.Validations.length > 0);

		vehicles.forEach(vehicle =>
		{
			const busNum = vehicle["BusNum"];
			validationColumns.forEach(column =>
			{
				const { FieldName, DisplayName, Validations } = column;
				Validations.forEach(validation =>
				{
					const result = this.conductValidation(vehicle, validation, FieldName, DisplayName);
					if (result)
					{
						errors.push({
							Vehicle: busNum,
							Field: FieldName,
							Title: DisplayName,
							Message: result.errorMessage,
							Type: result.errorLevel,
						});
					}
				});
			});
		});

		return errors;
	};

	/**
	 * Conduct validation for one field.
	 *
	 * @param {any} data
	 * @param {any} validation
	 * @param {string} fieldName
	 * @param {string} displayName
	 * @return {*} 
	 */
	PDE1049DataValidationViewModel.prototype.conductValidation = function(data, validation, fieldName, displayName)
	{
		let errorMessage = "";
		let errorLevel = "";
		const value = data[fieldName];

		switch (validation.type)
		{
			case "MAX_LIMIT":
				const maxLimit = validation.max;
				if (+value > maxLimit)
				{
					errorMessage = validation.message || `${displayName} exceed ${maxLimit}.`;
					errorLevel = validation.level;
				}
				break;
			case "FIXED_LENGTH":
				const fixedLength = validation.length;
				if (!value || value.length !== fixedLength)
				{
					errorMessage = validation.message || `${displayName} must be ${fixedLength} in length.`;
					errorLevel = validation.level;
				}
				break;
			case "REGEX":
				const regex = validation.regex;
				if (!value || !regex.exec(value))
				{
					errorMessage = validation.message;
					errorLevel = validation.level;
				}
				break;
			case "CUSTOM":
				const { callback } = validation;
				if (typeof callback === "function")
				{
					const result = callback(value, data);
					if (result)
					{
						errorMessage = result.message;
						errorLevel = result.level;
					}
				}
				break;
			default:
				break;
		}

		return errorMessage ? { errorMessage, errorLevel } : null;
	};

	/**
	 * if user click apply first call this function 
	 */
	PDE1049DataValidationViewModel.prototype.exportGridData = function(fileFormat = 'zip', layoutColumns = null)
	{
		let self = this;
		const url = tf.dataTypeHelper.getExportFileEndpoint("pde1049vehicle");
		const getDataUrl = url + '/';
		if (!layoutColumns)
		{
			layoutColumns = tf.PDE1049DataValidationVehicleGridDefinition.getColumnsByFieldNames(VEHICLE_FIELDS_EXPORT);
		}

		let gridLayoutExtendedEntity = {
			Description: "",
			Name: "",
			FilterId: null,
			LayoutColumns: layoutColumns,
			APIIsDirty: false
		}

		// set sort items
		let sortItems = (this.vehicleKendoGrid.dataSource._sort || []).map(x =>
		{
			return { Name: x.field, Direction: x.dir == "asc" ? "Ascending" : "Descending" }
		})

		if (sortItems.length == 0)
		{
			sortItems = [{ Name: "ContractorId", Direction: "Ascending", isAscending: true }];
		}

		let schoolYear = this.obSchoolYear().split("-");
		let getDataOption = {
			data: {
				parameters: {
					StartYear: schoolYear[0],
					EndYear: schoolYear[1],
					Aun: self.obAdminUnitNumber(),
					Pde1049Method: self.obSelectedMethod(),
					CostIndex: self.obCostIndex() || 0,
					Ratio: self.obRatio() || 0
				},
				gridLayoutExtendedEntity: gridLayoutExtendedEntity,
				selectedIds: self.vehicleGridIds,
				sortItems: sortItems,
				tripIds: self.tripIds
			}
		};
		if (fileFormat != "zip")
		{
			getDataOption.paramData = { fileFormat: fileFormat };
		}

		tf.promiseAjax.post(getDataUrl, getDataOption).then(function(keyApiResponse)
		{
			var fileUrl = `${url}?key=${keyApiResponse.Items[0]}&fileFormat=${fileFormat}&fileName=${encodeURIComponent('PDE1049Data')}`;
			window.open(fileUrl);
		})

	}

	/**
	 * when print Errors button click
	 * @param {any} data 
	 * @param {any} e 
	 */
	PDE1049DataValidationViewModel.prototype.printErrorInfo = function(data, e)
	{
		let printSettingsModal = new TF.Modal.Grid.PrintSettingsModalViewModel();
		tf.modalManager.showModal(printSettingsModal).then(function(result)
		{
			if (!result) return;
			//after user choose print options
			var printHelper = new TF.DetailView.PrintHelper();
			var $errorGridDom = $(e.target).siblings(".error-grid").eq(0).clone();

			//When another model is opening ,url in location.href is just like 'en-US/html/#/xxxx/xxxx'
			//And the url on print and print preview at left bottom will same as location.href
			//So that we should change location.href before print and restore it after print.
			const nowHref = window.location.href;
			//set restore event after print
			printHelper.setAfterPrintEvents(() =>
			{
				window.location.href = nowHref;
			});
			// change href to base for print
			window.location.href = `${window.location.href.split("#")[0]}#/`;
			printHelper.print($errorGridDom, printSettingsModal.model.obSelectedOrientation());
		});
	}

	PDE1049DataValidationViewModel.prototype.viewErrorInfo = function()
	{
		tf.exportcsv.exportAndDownload({
			columns: tf.PDE1049DataValidationVehicleGridDefinition.getValidationErrorGridDefinition().map(x => x.FieldName),
			data: this.validationErrorList,
			fileName: "ErrorView"
		});
	}

	PDE1049DataValidationViewModel.prototype.saveVehiclesGrid = function()
	{
		return this.exportGridData("xls", tf.PDE1049DataValidationVehicleGridDefinition.getColumnsByFieldNames(VEHICLE_FIELDS).filter(x => x.hidden != true));
	}

})();