(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.SendMergeDocTestEmailDataModel = function(settingsConfigurationEntity)
	{
		namespace.BaseDataModel.call(this, settingsConfigurationEntity);
	};

	namespace.SendMergeDocTestEmailDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.SendMergeDocTestEmailDataModel.prototype.constructor = namespace.SendMergeDocTestEmailDataModel;

	namespace.SendMergeDocTestEmailDataModel.prototype.mapping = [
		{ from: "TimeZone", default: "" },
		{ from: "ClientId", default: "" },
		{ from: "Smtphost", default: "" },
		{ from: "Smtpport", default: 0 },
		{ from: "SmtpuserName", default: "" },
		{ from: "Smtppassword", default: "" },
		{ from: "Smtpssl", default: false },
		{ from: "EmailAddress", default: "" },
		{ from: "EmailName", default: "" },
		{ from: "MailToList", default: [] },
		{ from: "MailCcList", default: [] },
		{ from: "MailBccList", default: [] },
		{ from: "EmailSubject", default: "" },
		{ from: "EmailMessage", default: "" },
		{ from: "TransfinderDataFolder", default: "" },
		{ from: "InstallationLocation", default: "" },
		{ from: "DatabaseServer", default: "" },
		{ from: "DatabaseName", default: "" },
		{ from: "DatabaseLoginId", default: "" },
		{ from: "DatabasePassword", default: "" },
		{ from: "IsRespectDaylight", default: true },
		{ from: "GroupPoints", default: true },
		{ from: "ClusterMinMapPoints", default: 0 },
		{ from: "ClusterMinZoomLevel", default: 14 },
		{ from: "ClusterTolerance", default: 50 },
		{ from: "Record", default: "" },
	];

})();