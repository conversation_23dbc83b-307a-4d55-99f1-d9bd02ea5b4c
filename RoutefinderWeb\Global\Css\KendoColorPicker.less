@import "z-index.less";

.close-color-picker {
	top: 10px;
	right: 10px;
	position: absolute;
	cursor: pointer;
	z-index: 10000;
	opacity: 0.7;
	font-size: 21px;
	font-weight: bold;
	line-height: 1;
	color: #000;
	text-shadow: 0 1px 0 #000;
}

.color-picker {
	height: 320px;
	width: 500px;
	padding: 0 15px;

	.col-xs-12 {
		box-sizing: border-box !important;

		.form-group {
			margin-top: 20px;

			&.color-display {
				margin-top: 0;
				margin-bottom: 35px;
			}

			&.color-selector {
				margin-top: 40px;
			}

			// .color-header {
			// 	font-weight: bold;
			// 	font-size: 14px;
			// }
		}
	}

	.palette {
		margin-top: 10px;
	}

	.k-hsv-gradient {
		margin-top: 10px;
		height: 132px;
	}

	.k-selected-color {
		width: 50%;
		margin-top: 10px;

		.k-selected-color-display {
			float: left;
			border: solid 1px #ccc !important;
			width: 100%;
		}

		.k-color-value.form-control {
			border-left: solid 1px #ccc !important;
			float: right;
			padding: 0 8px !important;
			width: calc(~"100% - 39px");
			border-radius: 0;
		}
	}

	.recentPalette {
		margin-top: 5px;
		max-width: 220px;
		overflow: hidden;
		float: left;

		.k-item {
			border: solid 1px #999999;
			box-sizing: border-box;

			&.k-selected {
				box-shadow: 3px 3px 4px 0px #999999;
			}
		}

		table {
			border-spacing: 5px;
			border-collapse: separate;
			margin-left: -4px;
		}
	}

	.k-slider {
		height: 30px;
	}

	.k-slider-wrap {
		.k-slider-track {
			height: 2px;
			-webkit-box-shadow: none;
			box-shadow: none;

			.k-slider-selection {
				height: 2px;
				margin-top: -1px;
			}

			.k-draghandle {
				background: 0 0;
				background-color: #3f51b5;
				-webkit-box-shadow: none;
				box-shadow: none;
				margin-top: -1px;
				width: 6px;
				height: 6px;
				border: 3px solid #3f51b5;
				border-radius: 13px;
				outline: none;

				&:hover {
					border-width: 2px;
					border-color: #3f51b5;
					background-color: #3f51b5;
					-webkit-box-shadow: 0 0 0 8px rgba(63, 81, 181, .3);
					box-shadow: 0 0 0 8px rgba(63, 81, 181, .3);
				}

				&.k-selected {
					padding: 1px;
					border-width: 2px;
					border-color: #3f51b5;
					background-color: #3f51b5;
					-webkit-box-shadow: 0 0 0 8px rgba(63, 81, 181, .3);
					margin-left: -1px;
					margin-top: -2px;
					width: 10px;
					height: 10px;
					box-shadow: none;
					top: -6px;
				}
			}
		}
	}

	.image-button {
		height: 34px;
		width: 16px;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: right center;
		margin-top: 5px;
		cursor: pointer;
		position: absolute;
		z-index: @COLOR_PICKER_BUTTON_Z_INDEX;

		&.image-previous {
			background-image: url('../../Global/Img/ColorPicker/Previous-Image.png');
			left: 0px;
			opacity: 0.3;
		}

		&.image-next {
			background-image: url('../../Global/Img/ColorPicker/Next-Image.png');
			right: 0;
			opacity: 0.7;
		}
	}
}

.k-flatcolorpicker:not(.more-color-picker) {
	width: auto;
}