(function()
{
	ko.components.register('filter-drop-down-list', {

		template: '<div class="input-group" >' +
			'    <div name="distanceUnit" data-bind="typeahead:{source:obSource,format:format,afterRender:afterRender,drowDownShow:true,notSort:true,selectedValue:obSelectedValue,disable:disable}">' +
			'       <!-- ko customInput:{type:"Select",value:selectedFilterName,attributes:{class:"form-control"},disable:disable} -->' +
			'       <!-- /ko -->' +
			'    </div>' +
			'    <div class="input-group-btn">' +
			'        <button type="button" class="btn btn-default btn-sharp" >' +
			'            <span class="caret"></span>' +
			'        </button>' +
			'    </div>' +
			'</div>',
		viewModel: function(params)
		{
			var self = this;
			this.obFilters = ko.observableArray();
			this.disable = ko.observable(params.disable || false);
			this.onlyApply = params.onlyApply;
			this.gridDefinition = {
				Columns: tf.studentGridDefinition.gridDefinition().Columns.map(function(definition)
				{
					return TF.Grid.GridHelper.convertToOldGridDefinition(definition);
				})
			};
			this.obOriginalValue = params.filter || ko.observable();
			this.obSelectedValue = ko.observable(null);
			this.selectedFilterName = ko.computed(function()
			{
				if (self.obSelectedValue())
				{
					return self.obSelectedValue().name();
				}
				return "";
			});
			var newFilterText = "New Filter";
			var manageFilterText = "Manage Filters";
			var clearFilterText = "Clear";
			this.obSource = ko.computed(function()
			{
				if (self.onlyApply)
				{
					return [{
						name: ko.observable(clearFilterText)
					}, {
						name: ko.observable("[divider]")
					}].concat(self.obFilters());
				}
				else
				{
					return [{
						name: ko.observable(newFilterText)
					}, {
						name: ko.observable(manageFilterText)
					}, {
						name: ko.observable("[divider]")
					}, {
						name: ko.observable(clearFilterText)
					}, {
						name: ko.observable("[divider]")
					}].concat(self.obFilters());
				}
			});

			this.afterRender = function(input)
			{
				var typeahead = input.data("typeahead");
				var $menu = input.data("typeahead").$menu;
				setTimeout(function()
				{
					$menu.find("a").each(function(index, a)
					{
						var text = $(a).text();
						if (index < (!self.onlyApply ? 4 : 2))
						{
							$(a).click(function(e)
							{
								e.stopPropagation();
								e.preventDefault();
								typeahead.hide();
								if (text == newFilterText)
								{
									createNewFilterClick();
								}
								if (text == manageFilterText)
								{
									manageFilterClick();
								}
								if (text == clearFilterText)
								{
									clearGridFilterClick();
								}
							});
						}
					});
				}, 10);
			};

			this.obSelectedValue.subscribe(function(value)
			{
				if (!self.initValue)
				{
					params.filter(value);
				}
				else
				{
					self.initValue = false;
				}
			});

			this.format = function(value)
			{
				return value.name();
			};

			loadFilter().then(() =>
			{
				if (this.obOriginalValue())
				{
					const sameValue = this.obFilters().find(x => x.name() === this.obOriginalValue().name());
					this.initValue = true;
					this.obSelectedValue(sameValue || this.obOriginalValue());
				}
			});

			function createNewFilterClick()
			{
				tf.modalManager.showModal(
					new TF.Modal.Grid.ModifyFilterModalViewModel(
						params.type,
						"new",
						null,
						null,
						self.gridDefinition,
						null
					)
				).then(function(result)
				{
					if (!result)
					{
						return;
					}

					isFilterDataModelValid(result.savedGridFilterDataModel).then(isValid =>
					{
						if (isValid)
						{
							updateAndApply(result.savedGridFilterDataModel, result.applyOnSave);
						}
					});
				});
			}

			function manageFilterClick()
			{
				getAllFilters().then(items =>
				{
					var filterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, items || []);
					const allFiltersData = ko.observableArray(filterDataModels);
					const manageFilterModal = new TF.Modal.Grid.ManageFilterModalViewModel({
						obAllFilters: allFiltersData,
						editFilter: saveAndEditGridFilter,
						applyFilter: filter => isFilterDataModelValid(filter).then(isValid =>
						{
							if (isValid)
							{
								updateAndApply(filter, true);
							}
							else
							{
								tf.promiseBootbox.alert("It cannot be applied because of unsupported fields.");
								return {
									preventClose: true
								};
							}
						}),
						filterName: self.obSelectedGridFilterName
					});

					const manageFilterModalViewModel = manageFilterModal.data();
					manageFilterModalViewModel.onFilterDeleted.subscribe((evt, filterId) =>
					{
						const filter = self.obFilters().find(f => f.id() === filterId);
						if (filter)
						{
							removeFilter(filter);
						}
					});

					tf.modalManager.showModal(manageFilterModal).then(function()
					{
					});
				});
			}

			function clearGridFilterClick()
			{
				applyGridFilter(null);
			}

			function getAllFilters()
			{
				let dateTypeName = tf.dataTypeHelper.getNameByType(params.type);
				let dataTypeId = tf.dataTypeHelper.getIdByName(dateTypeName);
				let paramData = {
					"@filter": String.format("(eq(dbid, {0})|isnull(dbid,))&eq(datatypeId,{1})&eq(IsValid,true)&isnotnull(Name,)", tf.datasourceManager.databaseId, dataTypeId),
					"@sort": "Name"
				};
				if (params.withRelationship)
				{
					paramData["@relationships"] = "Reminder,AutoExport";
				}
				return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"), {
					paramData: paramData
				}).then(result => result.Items);
			}

			function loadFilter()
			{
				return getAllFilters().then(function(filters)
				{
					return getAvailableFilters(filters).then(availableFilters =>
					{
						var gridFilterDataModels = TF.DataModel.BaseDataModel.create(TF.DataModel.GridFilterDataModel, availableFilters || []);
						self.obFilters(gridFilterDataModels);
					});
				});
			}

			function saveAndEditGridFilter(isNew, gridFilterDataModel, getCurrentHeaderFilters, getCurrentOmittedRecords, options)
			{
				return tf.modalManager.showModal(
					new TF.Modal.Grid.ModifyFilterModalViewModel(
						params.type,
						isNew,
						gridFilterDataModel,
						null,
						self.gridDefinition
					)
				).then(function(result)
				{
					if (!result)
					{
						return true;
					}

					isFilterDataModelValid(result.savedGridFilterDataModel).then(isValid =>
					{
						if (isValid)
						{
							updateAndApply(result.savedGridFilterDataModel, result.applyOnSave);
						}
						else if (isNew !== "new")
						{
							const changedDataModel = self.obFilters().find(x => x.id() === result.savedGridFilterDataModel.id());
							changedDataModel && removeFilter(changedDataModel);
						}
					});

					// return created/updated filter to let ManageFilterViewModel refresh the grid
					return result.savedGridFilterDataModel;
				});
			}

			function updateAndApply(filter, apply)
			{
				let changedDataModel = self.obFilters().find(x => x.id() === filter.id());
				if (changedDataModel)
				{
					changedDataModel.update(filter.toData());
				}
				else
				{
					self.obFilters.push(filter);
					changedDataModel = filter;
				}

				sortAllFilters();

				if (apply)
				{
					changedDataModel && applyGridFilter(changedDataModel);
				}
			}

			function applyGridFilter(filter)
			{
				self.obSelectedValue(filter);
				params.filter(filter);
			}

			function sortAllFilters()
			{
				self.obFilters(self.obFilters().sort((a, b) => a.name().toLowerCase() > b.name().toLowerCase() ? 1 : -1));
			}

			function removeFilter(filter)
			{
				if (filter)
				{
					self.obFilters.remove(filter);
					if (filter === self.obSelectedValue())
					{
						self.obSelectedValue(null);
					}
				}
			}

			function isFilterDataModelValid(filterDataModel)
			{
				return filterDataModel ? getAvailableFilters([filterDataModel], true).then(items => items.length > 0) : Promise.resolve(false);
			}

			function getAvailableFilters(items, isDataModel)
			{
				if (params.type.toLowerCase() === "student" && params.for === "nez")
				{
					const calculatedFields = ["ActivateSubscribers", "InvitedSubscribers", "ActivatedSubscribers", "TagId", "PrimaryContactName",
						"PrimaryContactPhone", "PrimaryContactMobile", "PrimaryContactEmail", "LastUpdatedName", "FromSchoolPUDefaultRequirement", "FromSchoolDODefaultRequirement",
						"ToSchoolPUDefaultRequirement", "ToSchoolDODefaultRequirement", "WalkToStopPolicy",
						"WalkToSchoolPolicy", "RideTimePolicy", "ActualLoadTime", "CalculatedLoadTime", "StopfinderContacts", "DocumentCount"]
						.concat(getTripCalculatedFields());
					//Allow these since they are not changed often.
					//const these = ["District", "grade", "Mail_City", "Mail_State", "Mail_Zip", "Sex", "Gender", "SCHOOLNAME", "ResSchName", "EthnicCode", "DisabilityCodes", "PopulationRegionName"];

					return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "userDefinedFields"), {
						paramData: {
							"datatypeid": tf.dataTypeHelper.getId(params.type),
							"@Relationships": "UDFType",
							"@fields": "DisplayName,Type"
						}
					}).then(function(result)
					{
						const udfs = (result.Items || []).filter(x => x.DisplayName && ["Roll-up", "Case"].includes(x.Type));
						udfs.forEach(x =>
						{
							if (!/^[a-zA-Z0-9]+$/.test(x.DisplayName))
							{
								x.DisplayName = `[${x.DisplayName}]`;
								x.needCheck = true;
							}
							else
							{
								calculatedFields.push(x.DisplayName);
							}
						});
						const getWhereClause = filter => isDataModel ? filter.whereClause() : filter.WhereClause;
						return items.filter(x => getWhereClause(x) != null
							&& !calculatedFields.some(field => new RegExp(`([^a-zA-Z0-9]|^)${field}([^a-zA-Z0-9]|$)`, "i").test(getWhereClause(x))
								&& !new RegExp(`(\\[[^\\[\\]]+?${field}[^\\[]*?\\])|(\\[[^\\[\\]]*?${field}[^\\[]+?\\])`, "i").test(getWhereClause(x)))
							&& !udfs.some(udf => udf.needCheck && getWhereClause(x).toLowerCase().includes(udf.DisplayName.toLowerCase())));
					});
				}

				return Promise.resolve(items);
			}

			function getTripCalculatedFields()
			{
				const fields = [];
				for (let i = 1; i <= 4; i++)
				{
					["ToSchoolTrip", "FromSchoolTrip"].forEach(prefix =>
					{
						fields.push(`${prefix}${i}TripName`);
						fields.push(`${prefix}${i}TripAlias`);
						fields.push(`${prefix}${i}Type`);
						fields.push(`${prefix}${i}PuStopName`);
						fields.push(`${prefix}${i}PuStopTime`);
						fields.push(`${prefix}${i}DoStopName`);
						fields.push(`${prefix}${i}DoStopTime`);
						fields.push(`${prefix}${i}WalkToStopDistance`);
						fields.push(`${prefix}${i}CrossStatus`);
						fields.push(`${prefix}${i}StopCrosser`);
						fields.push(`${prefix}${i}Bus`);
						fields.push(`${prefix}${i}RideTime`);
						fields.push(`${prefix}${i}Days`);
						fields.push(`${prefix}${i}DateRange`);
						fields.push(`${prefix}${i}IsException`);
					});
				}
				return fields;
			}
		},
	});
})();

