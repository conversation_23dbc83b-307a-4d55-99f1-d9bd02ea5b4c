﻿(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.RouteDeletion = RouteDeletion;

	function RouteDeletion()
	{
		namespace.BaseDeletion.apply(this, arguments);
		this.type = 'route';
	}

	RouteDeletion.prototype = Object.create(namespace.BaseDeletion.prototype);

	RouteDeletion.prototype.constructor = RouteDeletion;

	RouteDeletion.prototype.getEntityPermissions = function(ids)
	{
		let associatedDatas = [];
		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			associatedDatas.push(this.type);
		}

		return Promise.resolve(associatedDatas);
	};

	RouteDeletion.prototype.deleteSingleVerify = function()
	{
		let associatedDatas = [];
		if (!tf.authManager.isAuthorizedFor(this.type, 'delete'))
		{
			associatedDatas.push(this.type);
		}

		return Promise.resolve(associatedDatas);
	};
})();