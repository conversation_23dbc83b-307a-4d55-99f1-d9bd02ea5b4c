(function()
{
	createNamespace("TF.Modal").NewMergeDocumentModalViewModel = NewMergeDocumentModalViewModel;

	function NewMergeDocumentModalViewModel(successCallback, failCallback, originModel, dataType)
	{
		TF.Modal.BaseModalViewModel.call(this, successCallback, failCallback);
		this.dataType = dataType;
		this.displayName = tf.dataTypeHelper.getDisplayNameByDataType(this.dataType);
		this.singleName = tf.applicationTerm.getApplicationTermSingularByName(this.displayName);
		this.title((originModel ? "Copy " : "New ") + this.singleName);
		this.sizeCss = originModel ? "modal-dialog-sm" : "modal-dialog-md";
		this.contentTemplate("workspace/controlpanel/modal/newmergedocument");
		this.buttonTemplate("modal/positiveNegative");
		this.obPositiveButtonLabel(originModel ? "Copy" : "Create");
		this.data(new TF.Control.NewMergeDocumentViewModel(originModel, dataType));
	}

	NewMergeDocumentModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	NewMergeDocumentModalViewModel.prototype.constructor = NewMergeDocumentModalViewModel;

	NewMergeDocumentModalViewModel.prototype.negativeClick = function(viewModel, e)
	{
		var self = this;
		if (!viewModel.data().obEntityDataModel().apiIsDirty())
		{
			self.negativeClose();
			return;
		}

		tf.promiseBootbox.confirm(
			{
				message: "You have unsaved changes. Would you like to save your changes prior to canceling?",
				title: "Unsaved Changes"
			})
			.then(function(result)
			{
				if (result)
				{
					self.positiveClick(viewModel, e);
				}
				else
				{
					self.negativeClose();
				}
				return result;
			});
	};

	NewMergeDocumentModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		var self = this;
		self.data().apply().then(function(success)
		{
			if (!success) return;
			self.successCallback(viewModel, e);
			self.positiveClose();
		});
	};

	NewMergeDocumentModalViewModel.prototype.dispose = function()
	{
		var data = this.data();
		if (data != null)
		{
			data.dispose();
		}
	};
})();


