(function()
{
	createNamespace('TF.Modal').ListMoverSelectDashboardControlModalViewModel = ListMoverSelectDashboardControlModalViewModel;

	function ListMoverSelectDashboardControlModalViewModel(selectedData, options)
	{
		var defaults = {
			title: "Select Dashboards ",
			description: "You may select one or more of the Routefinder Plus dashboards.",
			availableTitle: "Available",
			selectedTitle: "Selected",
			type: "scheduledDashboard",
			getUrl: function()
			{
				return pathCombine(tf.api.apiPrefixWithoutDatabase(), "search", "dashboards");
			},
		};
		this.options = options = $.extend(true, defaults, options);

		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, selectedData, options);
		this.ListMoverSelectDashboardControlViewModel = new TF.Control.ListMoverSelectDashboardControlViewModel(selectedData, options);
		this.ListMoverSelectDashboardControlViewModel.obDisplayCheckbox(false);
		this.data(this.ListMoverSelectDashboardControlViewModel);
	}

	ListMoverSelectDashboardControlModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	ListMoverSelectDashboardControlModalViewModel.prototype.constructor = ListMoverSelectDashboardControlModalViewModel;

	ListMoverSelectDashboardControlModalViewModel.prototype.positiveClick = function()
	{
		this.data().apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	ListMoverSelectDashboardControlModalViewModel.prototype.negativeClick = function()
	{
		this.data().cancel().then(function(result)
		{
			if (result)
			{
				this.negativeClose();
			}
		}.bind(this));
	};

})();
