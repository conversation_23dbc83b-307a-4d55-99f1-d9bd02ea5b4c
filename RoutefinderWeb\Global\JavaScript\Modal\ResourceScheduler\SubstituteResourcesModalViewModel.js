(function()
{
	createNamespace("TF.Modal.ResourceScheduler").SubstituteResourcesModalViewModel = SubstituteResourcesModalViewModel;

	function SubstituteResourcesModalViewModel(tripIds, options)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.sizeCss = "substitute-resources-modal modal-dialog";
		this.contentTemplate('modal/resourcescheduler/SubstituteResourcesControl');
		this.buttonTemplate('modal/PositiveNegativeOther');
		this.title("Substitute Resources");
		this.obPositiveButtonLabel("Save");
		this.obNegativeButtonLabel("Cancel");
		this.obOtherButtonLabel(TF.isPhoneDevice ? "" : "Reset Resources");
		this.substituteResourcesViewModel = new TF.Control.ResourceScheduler.SubstituteResourcesViewModel(tripIds, options);
		this.data(this.substituteResourcesViewModel);
		this.options = options;
		this.obDisableControl = ko.computed(() => !this.substituteResourcesViewModel.isResourceChanged());
	}

	SubstituteResourcesModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	SubstituteResourcesModalViewModel.prototype.constructor = SubstituteResourcesModalViewModel;

	SubstituteResourcesModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		return this.substituteResourcesViewModel.apply()
			.then((result) =>
			{
				if (result !== false)
				{
					this.positiveClose(result);
				}
			});
	};

	SubstituteResourcesModalViewModel.prototype.negativeClick = function()
	{
		return this.substituteResourcesViewModel.cancel().then(function(result)
		{
			if (result === false) return;

			this.substituteResourcesViewModel.dispose();
			this.negativeClose(result);
		}.bind(this));
	};

	SubstituteResourcesModalViewModel.prototype.otherClick = function()
	{
		return this.substituteResourcesViewModel.resetResources().then(function(result)
		{
			if (result !== false)
			{
				this.positiveClose({ isReset:result });
			}
		}.bind(this));
	};
})();