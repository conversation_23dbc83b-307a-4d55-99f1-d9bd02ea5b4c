(function()
{
	createNamespace('TF.Control').EditFieldTripNoNewRequestsDateRecordViewModel = EditFieldTripNoNewRequestsDateRecordViewModel;
	function EditFieldTripNoNewRequestsDateRecordViewModel(configType, recordEntity)
	{
		TF.Control.EditFieldTripConfigRecordViewModelBase.call(this, configType, recordEntity);
		this._init(recordEntity);
	}

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype = Object.create(TF.Control.EditFieldTripConfigRecordViewModelBase.prototype);
	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.constructor = EditFieldTripNoNewRequestsDateRecordViewModel;

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.init = function(viewModel, el)
	{
		TF.Control.EditFieldTripConfigRecordViewModelBase.prototype.init.call(this, viewModel, el);
		setTimeout(() =>
		{
			/* trigger validator after element and data load completed */
			this.$element.find("input[name=validateMonth]").change();
			this.$element.find("input[name=validateWeek]").change();
		}, 500);
	}


	EditFieldTripNoNewRequestsDateRecordViewModel.prototype._init = function(recordEntity)
	{
		const self = this;
		if (!recordEntity)
		{
			recordEntity = {};
		}

		self.obStartDate = ko.observable(recordEntity.NoNewRequestsDate || "");
		self.obEndDate = ko.observable(recordEntity.EndDate || "");
		self.obRecurrenceEnabled = ko.observable(recordEntity.RecurrenceEnabled || false);
		self.obRecurrenceDisabled = ko.computed(function()
		{
			return !self.obRecurrenceEnabled();
		});
		self.obApplySchoolCalendar = ko.observable(recordEntity.ApplySchoolCalendar || false);
		self.obRecurEvery = ko.observable(recordEntity.RecurEvery || 1);
		self.obRecurringTypes = ko.observableArray(TF.Enums.Recurring.RecurringTypeEnum);
		self.obCurrentRecurringType = ko.observable(recordEntity.RecurBy || 0);
		self.obSelectedRecurringType = ko.observable(null);
		if (recordEntity.RecurBy)
		{
			self.obSelectedRecurringType(self.obRecurringTypes().find(o => o.value === recordEntity.RecurBy));
		}
		self.obSelectedRecurringType.subscribe(function(data)
		{
			self.obCurrentRecurringType(data.value);
		});
		self.obSelectedRecurringTypeText = ko.pureComputed(function()
		{
			return !!self.obSelectedRecurringType() ? self.obSelectedRecurringType().text : "Day";
		});

		//#region Recurring Type Day

		self.obByDayWeekdayOnly = ko.observable(recordEntity.ByDayWeekdayOnly || false);

		//#endregion

		//#region Recurring Type Week

		self.obByWeekMonToFri = ko.observable(recordEntity.ByWeekMon && recordEntity.ByWeekThurs && recordEntity.ByWeekWeds && recordEntity.ByWeekTues && recordEntity.ByWeekFri);
		self.obByWeekMonToFri.subscribe(function(v)
		{
			if (!self.lockWeekDayChange)
			{
				self.lockWeekDayChange = true;
				self.obByWeekMon(v);
				self.obByWeekTues(v);
				self.obByWeekWeds(v);
				self.obByWeekThurs(v);
				self.obByWeekFri(v);
				self.lockWeekDayChange = false;
			}
		});
		self.obByWeekMon = ko.observable(recordEntity.ByWeekMon || false);
		self.obByWeekMon.subscribe(self.dayOfWeekChanged.bind(self));
		self.obByWeekTues = ko.observable(recordEntity.ByWeekTues || false);
		self.obByWeekTues.subscribe(self.dayOfWeekChanged.bind(self));
		self.obByWeekWeds = ko.observable(recordEntity.ByWeekWeds || false);
		self.obByWeekWeds.subscribe(self.dayOfWeekChanged.bind(self));
		self.obByWeekThurs = ko.observable(recordEntity.ByWeekThurs || false);
		self.obByWeekThurs.subscribe(self.dayOfWeekChanged.bind(self));
		self.obByWeekFri = ko.observable(recordEntity.ByWeekFri || false);
		self.obByWeekFri.subscribe(self.dayOfWeekChanged.bind(self));
		self.obByWeekSat = ko.observable(recordEntity.ByWeekSat || false);
		self.obByWeekSun = ko.observable(recordEntity.ByWeekSun || false);

		self.obValidateWeek = ko.computed(function()
		{
			if (self.$element)
			{
				setTimeout(function()
				{
					self.$element.find("input[name=validateWeek]").change();
				});
			}

			// if not Week, always okay
			if (self.obCurrentRecurringType() != 1) return "1";

			return (self.obByWeekMon()
				|| self.obByWeekTues()
				|| self.obByWeekWeds()
				|| self.obByWeekThurs()
				|| self.obByWeekFri()
				|| self.obByWeekSat()
				|| self.obByWeekSun()) ? "1" : "";
		});

		self.lockWeekDayChange = false;

		//#endregion

		//#region Recurring Type Month

		self.obMonthPatternOptions = ko.observableArray(TF.Enums.Recurring.MonthPatternSelectionEnum);
		self.obByMonthPatternSelection = ko.observable(); // selected item value
		self.obSelectedMonthPatternSelection = ko.observable(null); // selecte item
		self.obSelectedMonthPatternSelection.subscribe(function(data)
		{
			self.obByMonthPatternSelection(data ? data.value : "");
		});
		self.obSelectedMonthPatternSelectionText = ko.pureComputed(function()
		{
			return !!self.obSelectedMonthPatternSelection() ? self.obSelectedMonthPatternSelection().text : "First";
		});
		if (recordEntity.ByMonthPatternSelection)
		{
			self.obSelectedMonthPatternSelection(self.obMonthPatternOptions().find(o => o.value === recordEntity.ByMonthPatternSelection));
		}

		self.obMonthPatternWeekDayOptions = ko.observableArray(TF.Enums.Recurring.MonthPatternWeekDayEnum);
		self.obByMonthPatternWeekDay = ko.observable();
		self.obSelectedMonthPatternWeekDay = ko.observable(null);
		self.obSelectedMonthPatternWeekDay.subscribe(function(data)
		{
			self.obByMonthPatternWeekDay(data && data.value || "");
		});
		self.obSelectedMonthPatternWeekDayText = ko.pureComputed(function()
		{
			return !!self.obSelectedMonthPatternWeekDay() ? self.obSelectedMonthPatternWeekDay().text : "Monday";
		});
		if (recordEntity.ByMonthPatternWeekDay)
		{
			self.obSelectedMonthPatternWeekDay(self.obMonthPatternWeekDayOptions().find(o => o.value === recordEntity.ByMonthPatternWeekDay));
		}

		self.obByMonthMode = ko.observable(0);
		self.obIsNotMonthModePattern = ko.observable(true);
		self.obByMonthMode.subscribe(function()
		{
			self.obIsNotMonthModePattern(self.obByMonthMode() !== 1);
		});
		self.obIsDisabledSelectedMonthPattern = ko.pureComputed(function()
		{
			return self.obIsNotMonthModePattern() || self.obRecurrenceDisabled();
		});
		if (recordEntity.ByMonthMode)
		{
			self.obByMonthMode(recordEntity.ByMonthMode);
		}

		self.obByMonthSpecificDay = ko.observable(parseInt(moment().currentTimeZoneTime().format('D')));
		if (recordEntity.ByMonthSpecificDay)
		{
			self.obByMonthSpecificDay(recordEntity.ByMonthSpecificDay);
		}

		self.obValidateMonth = ko.computed(function()
		{
			if (self.$element)
			{
				setTimeout(function()
				{
					self.$element.find("input[name=validateMonth]").change();
				});
			}

			if (self.obCurrentRecurringType() == 2 && self.obByMonthMode() == 0 && !self.obByMonthSpecificDay()) return "";
			return true;
		});
		self.validateMonthSpecificDay = self.validateMonthSpecificDay.bind(self);
	}

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.updateValidatorFields = function(validatorFields)
	{
		const self = this;
		validatorFields["startDate"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				date: {
					message: 'invalid date',
					format: 'MM/DD/YYYY'
				}
			}
		};

		validatorFields["endDate"] = {
			trigger: "blur change",
			validators: {
				date: {
					message: 'invalid date',
					format: 'MM/DD/YYYY'
				},
				notEmpty: {
					message: "required"
				},
				callback: {
					message: 'End date cannot be before start date.',
					callback: function(value, validator)
					{
						if (value === "")
						{
							return true;
						}
						var m = new moment(value);
						if (!m.isValid() || !self.obStartDate())
						{
							return true;
						}
						return m >= moment(self.obStartDate());
					}
				}
			}
		};

		validatorFields["validateWeek"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				}
			}
		};

		validatorFields["validateMonth"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				}
			}
		};

		validatorFields["recurEvery"] = {
			trigger: "blur change",
			validators: {
				notEmpty: {
					message: "required"
				},
				regexp: {
					regexp: "^[0-9]*[1-9][0-9]*$",
					message: "The value must be >= 1."
				}
			}
		};
	}

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.afterValidation = function(e, data)
	{
		data.element.closest('.form-group').removeClass('has-success');
		if (data.field === 'startDate' && !data.bv.isValidField('endDate'))
		{
			// We need to revalidate the end date
			data.bv.revalidateField('endDate');
		}

		if (data.field === 'endDate' && !data.bv.isValidField('startDate'))
		{
			// We need to revalidate the start date
			data.bv.revalidateField('startDate');
		}
	}

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.save = async function()
	{
		if (!await this.validateByMonthSpecificDay())
		{
			return null;
		}

		return await TF.Control.EditFieldTripConfigRecordViewModelBase.prototype.save.call(this);
	}

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.getRecordEntity = function()
	{
		return {
			ID: this.obRecordId(),
			...this.getRecurrenceReportEntity(),
		};
	}

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.getRecurrenceReportEntity = function()
	{
		const self = this;
		const selectedRecurringType = Number(self.obCurrentRecurringType()),
			entity = {
				NoNewRequestsDate: self.obStartDate(),
				EndDate: self.obEndDate() || undefined,
				RecurEvery: self.obRecurEvery(),
				RecurBy: selectedRecurringType,
				ApplySchoolCalendar: self.obApplySchoolCalendar(),
				RecurrenceEnabled: self.obRecurrenceEnabled(),
			};

		switch (selectedRecurringType)
		{
			case 0:// Daily
				entity.ByDayWeekdayOnly = !!self.obByDayWeekdayOnly();
				break;
			case 1:// Weekly
				entity.ByWeekMon = self.obByWeekMon();
				entity.ByWeekTues = self.obByWeekTues();
				entity.ByWeekWeds = self.obByWeekWeds();
				entity.ByWeekThurs = self.obByWeekThurs();
				entity.ByWeekFri = self.obByWeekFri();
				entity.ByWeekSat = self.obByWeekSat();
				entity.ByWeekSun = self.obByWeekSun();
				break;
			case 2:// Monthly
				entity.ByMonthMode = self.obByMonthMode();

				if (self.obByMonthMode() == 0)
				{
					entity.ByMonthSpecificDay = self.obByMonthSpecificDay();
				}
				else if (self.obByMonthMode() == 1)
				{
					entity.ByMonthPatternSelection = $.isNumeric(self.obByMonthPatternSelection()) ? Number(self.obByMonthPatternSelection()) : 1;
					entity.ByMonthPatternWeekDay = Number(self.obByMonthPatternWeekDay()) || 1;
				}
				break;
			default:
				break;
		}

		return entity;
	};

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.onToDateChange = function(e)
	{
		var self = this,
			toDate = e.target.value,
			mToDate = moment(toDate);

		if (mToDate.isValid())
		{
			self.obEndDate(mToDate.format("YYYY-MM-DDT00:00:00.000"));
		}
		else
		{
			self.obEndDate("");
		}

		if (self.obStartDate()
			&& self.obStartDate() !== "Invalid date"
			&& toDate
			&& new Date(self.obStartDate()) > new Date(toDate))
		{
			self.obStartDate(self.obEndDate());
		}
	};

	//#region Recurring Type Week

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.dayOfWeekChanged = function()
	{
		var self = this;

		if (!self.lockWeekDayChange)
		{
			self.lockWeekDayChange = true;
			self.obByWeekMonToFri(self.obByWeekMon() && self.obByWeekThurs() && self.obByWeekWeds() && self.obByWeekTues() && self.obByWeekFri());
			self.lockWeekDayChange = false;
		}
	};

	//#endregion

	//#region Recurring Type Month

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.validateMonthSpecificDay = function(model, event)
	{
		var testInteger = /^[1-9]+[0-9]*]*$/,
			testValue = event.target.value;
		if (!testInteger.test(testValue) || Number(testValue) > 31)
		{
			this.obByMonthSpecificDay(Number(moment().format('D')));
		}
	};

	EditFieldTripNoNewRequestsDateRecordViewModel.prototype.validateByMonthSpecificDay = function()
	{
		if (this.obCurrentRecurringType() == 2 && this.obByMonthMode() == 0)
		{
			var intDay = Number(this.obByMonthSpecificDay());
			if (intDay === 29 || intDay === 30 || intDay === 31)
			{
				var otherInfo = `Not all months have ${intDay} days. Are you sure you want to continue?`;
				const months = moment(this.obEndDate()).diff(moment(this.obStartDate()), 'months');
				if (months >= 12)
				{
					return tf.promiseBootbox.yesNo(otherInfo, "Confirmation Message");
				}

				const startDateDays = moment(this.obStartDate()).dates();
				const startDateMonth = moment(this.obStartDate()).months() + 1;
				const startDateYear = moment(this.obStartDate()).years();
				const endDateDays = moment(this.obEndDate()).dates();
				const endDateMonth = moment(this.obEndDate()).months() + 1;
				const endDateYear = moment(this.obEndDate()).years();


				let current = moment(`${startDateMonth}/${intDay}/${startDateYear}`, 'MM/DD/YYYY')
				let currentMonth = startDateMonth;
				let currentYear = startDateYear;
				let isValid = startDateDays >= intDay || current.isValid();

				while (isValid)
				{
					currentMonth += 1;
					if (currentMonth > 12)
					{
						currentMonth = 1;
						currentYear += 1;
					}

					if (currentYear > endDateYear || (currentMonth > endDateMonth && currentYear === endDateYear))
					{
						break;
					}

					current = moment(`${currentMonth}/${intDay}/${currentYear}`, 'MM/DD/YYYY')
					if (!current.isValid() && !(currentMonth === endDateMonth && currentYear == endDateYear))
					{
						isValid = false;
						break;
					}
				}

				if (!isValid)
				{
					return tf.promiseBootbox.yesNo(otherInfo, "Confirmation Message");
				}
			}
		}

		return Promise.resolve(true);
	};

	/**
	 * Trying to convert row to Recurrence string to display in Date Restrictions grid
	 * Rules: The Recurrence field should contain a recurrence statement 
	 * similar to how recurrence appears for student schedules: ex: 
	 * “Every day, Mon-Fri”; “Every 3 weeks, Fri”; “Every 2 months, every third Tuesday”. 
	 * For Month recurrence based on specific Day settings, then the statement should end with “on the X”, where “X” is “First”, “Second”, “Third”, and so on.
	 */
	EditFieldTripNoNewRequestsDateRecordViewModel.toRecurrenceString = function(row)
	{
		if (!row.RecurrenceEnabled)
		{
			return "";
		}

		const getRecurByLabels = (recurByLabel) => `Every ${(row.RecurEvery > 1 ? row.RecurEvery + " " : "")}${recurByLabel}${(row.RecurEvery > 1 ? "s" : "")}`;
		// Day
		if (row.RecurBy === 0)
		{
			return `${getRecurByLabels('day')}${(row.ByDayWeekdayOnly ? ", Mon-Fri" : "")}`;
		}

		// Week
		if (row.RecurBy === 1)
		{
			const dayOfWeeks = [!!row.ByWeekSun, !!row.ByWeekMon, !!row.ByWeekTues, !!row.ByWeekWeds, !!row.ByWeekThurs, !!row.ByWeekFri, !!row.ByWeekSat];
			const monToFriDayWeeks = [false, true, true, true, true, true, false];
			const onlyMonToFri = dayOfWeeks.every((n, i) => n === monToFriDayWeeks[i]);
			if (onlyMonToFri)
			{
				return `${getRecurByLabels('week')}, Mon-Fri`;
			}

			const dayOfWeeekLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
			const avaialbelDayOfWeekLabels = dayOfWeeks.map((n, i) => n ? dayOfWeeekLabels[i] : '').filter(n => !!n).join(' ');
			return `${getRecurByLabels('week')}, ${avaialbelDayOfWeekLabels}`;
		}

		// Month
		if (row.RecurBy === 2)
		{
			const firstThreeSpecificDayLabelMap = { "1": "1st", "2": "2nd", "3": "3rd" };
			const getSpecificDateLabel = specificDay =>
			{
				if ([1, 2, 3].includes(specificDay))
				{
					return firstThreeSpecificDayLabelMap[specificDay];
				}

				return `${specificDay}th`;
			};

			if (!row.ByMonthMode)
			{
				// Specific Date
				return `${getRecurByLabels('month')} on the ${getSpecificDateLabel(row.ByMonthSpecificDay)}`;
			}
			const getEveryPeriod = () =>
			{
				const foundPatternSelection = TF.Enums.Recurring.MonthPatternSelectionEnum.find(o => o.value === row.ByMonthPatternSelection);
				const foundPatternWeekDay = TF.Enums.Recurring.MonthPatternWeekDayEnum.find(o => o.value === row.ByMonthPatternWeekDay);

				return `every ${foundPatternSelection?.text?.toLowerCase()} ${foundPatternWeekDay?.text?.toLowerCase()}`;
			};

			// Pattern
			return `${getRecurByLabels('month')}, ${getEveryPeriod()}`;
		}

		return '';
	}

	//#endregion
})();

