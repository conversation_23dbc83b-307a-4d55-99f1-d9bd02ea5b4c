(function()
{
	if (!$ || !$.fn || !$.fn.bootstrapValidator)
	{
		return;
	}

	var bv = $.fn.bootstrapValidator, bvClass = bv.Constructor, bvPrototype = bvClass.prototype;
	bvPrototype.clearExcludeErrors = function()
	{
		for (var field in this.options.fields)
		{
			var el = this.getFieldElements(field);
			var type = el.attr('type'),
				total = ('radio' === type || 'checkbox' === type) ? 1 : el.length;
			for (var i = 0; i < total; i++)
			{
				var $field = el.eq(i);
				if (this._isExcluded($field))
				{
					var error = this.$form.find("[data-bv-for='" + field + "']");
					error.each(function()
					{
						$(this).hide();
					});
					break;
				}
			}
		}
	};

	if (bv.helpers && bv.helpers.call)
	{
		var bootstrapValidatorHelpersCall = bv.helpers.call;
		bv.helpers.call = function(functionName, args)
		{
			var element = args[2], validator = args[1];
			if (!element || !validator)
			{
				return bootstrapValidatorHelpersCall(functionName, args);
			}

			var name = element.attr("name"), validatorField = validator.options.fields[name];
			if (validatorField.async == null || validatorField.async === false)
			{
				return bootstrapValidatorHelpersCall(functionName, args);
			}

			return new Promise(function(resolve)
			{
				if (validatorField.timer != null)
				{
					clearTimeout(validatorField.timer);
				}

				validatorField.timer = setTimeout(function()
				{
					resolve(bootstrapValidatorHelpersCall(functionName, args));
					validatorField.timer = null;
				}, validatorField.async === true ? 0 : validatorField.async);
			});
		};
	}

	TF.smartOverride(bvPrototype, "_initField", function(base, field)
	{
		var fieldOptions = this.options.fields[field];
		if (fieldOptions && fieldOptions.useGroupContainer)
		{
			var el = this.getFieldElements(field),
				group = this.options.fields[field].group || this.options.group || '.form-group',
				errorCss = '.group-error',
				container = el.closest(group).find(errorCss);
			fieldOptions.container = container.length ? container : fieldOptions.container;
		}

		base.call(this, field);
	});

	TF.smartOverride(bvPrototype, "destroy", function(base)
	{
		for (var field in this.options.fields)
		{
			var fieldOption = this.options.fields[field];
			if (fieldOption.timer != null)
			{
				clearTimeout(fieldOption.timer);
				fieldOption.timer = null;
			}
		}

		base.call(this);
	});
})();