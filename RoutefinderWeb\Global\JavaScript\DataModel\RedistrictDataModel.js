(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.RedistrictDataModel = function(redistrictEntity)
	{
		namespace.BaseDataModel.call(this, redistrictEntity);
	};

	namespace.RedistrictDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.RedistrictDataModel.prototype.constructor = namespace.RedistrictDataModel;

	namespace.RedistrictDataModel.prototype.mapping = [
		{ from: "Id", default: 0, required: true },
		{ from: "Description", default: "" },
		{ from: "FilterName", default: "" },
		{ from: "FilterSpec", default: "" },
		{ from: "LastUpdated", default: "1970-01-01T00:00:00" },
		{ from: "LastUpdatedId", default: 0 },
		{ from: "LastUpdatedName", default: "" },
		{ from: "LastUpdatedType", default: 0 },
		{ from: "Name", default: "" },
		{ from: "RedistId", default: 0 },
		{ from: "RsysLockId", default: 0 },
		{ from: "Schools", default: "" },
		{ from: "SchoolIds", default: "" },
		{ from: "System1", default: "" },
		{ from: "System2", default: "" },
		{ from: "System3", default: "" },
		{ from: "System4", default: "" },
		{ from: "IShow", default: false }
	];
})();
