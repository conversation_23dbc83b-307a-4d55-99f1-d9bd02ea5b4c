.k-mobile {
	.modal-content .btn.btn-mobile-confirm {
		background-color: #8E52A1;
		border-color: #8E52A1;
		color: #fff;
	}

	.plus {
		.modal-content .btn.btn-mobile-confirm {
			background-color: #D74B3C;
			border-color: #D74B3C;
			color: #fff;
		}

		.customizeddashboard {
			.esri-popup__main-container .popupPage .head {
				background-color: #D74B3C !important;
			}

			.detail-view-panel .grid-stack .grid-stack-item.widget-block {

				.grid-stack-item-content .grid,
				.esri-view {
					.esri-map-toolbar-bottom .apply-btn {
						color: #D74B3C;
					}
				}
			}
		}

		.unsave-mobile .modal-footer .btn.btn-yes-mobile {
			background-color: #D74B3C !important;
		}

		#FullPageCalloutContainer .popupPage,
		.esri-popup__main-container .popupPage {

			.head {
				color: #D74B3C !important;

				.head-pager .page-group .left-caret:before,
				.head-pager .page-group .right-caret:before {
					border-left-color: #D74B3C;
					border-right-color: #D74B3C;
				}
			}

			.content .content-extend {

				.tab-header ul li,
				.tab-content .sub-content .module .section-head,
				.tab-content .sub-content .module .section-head a.drill-down-links,
				.tab-content .sub-content .module .section-subhead,
				.tab-content .sub-content .notes-tab .center-container .notes-control button {
					color: #D74B3C;
				}

				.tab-header ul li,
				.tab-content .sub-content .notes-tab .center-container .notes-control button {
					border-color: #D74B3C;
				}

				.tab-header ul li.select {
					background-color: #D74B3C;
				}
			}
		}
	}

	.customizeddashboard {
		.detail-view-panel .grid-stack .grid-stack-item.widget-block .grid-stack-item-content .grid .kendo-grid.kendo-summarygrid-container {
			margin-top: -53px !important;

			.k-grid-header .k-grid-header-locked {
				margin-left: 0;
			}

			.k-grid-content-locked {
				margin-left: 0;
			}
		}

		.esri-popup__main-container .popupPage .head {
			background-color: #8E52A1 !important;
		}

		.detail-view-panel .grid-stack .grid-stack-item.widget-block {
			.grid-stack-item-content .grid {
				.grid-map-widget .ui-resizable-handle .sliderbar-button {
					position: absolute;
					left: 0;
					top: calc(50% - 19.5px);
					width: 44px;
					height: 39px;
					background-color: transparent;
					background: linear-gradient(90deg, #484848, #484848) no-repeat left center / 8px 39px;
					border-radius: 0;
					cursor: pointer;

					&.slider-tapped {
						background: repeating-linear-gradient(90deg, #868686, #868686 1px, #4b4b4b 1px, #4b4b4b 4px) no-repeat 16px center / 9px 18px, #4b4b4b !important;
					}
				}
			}

			&.selected .toolbar {
				display: block;
			}

			.grid-stack-item-content .grid,
			.esri-view {
				.esri-map-toolbar-bottom {
					background-color: white;
					border: 1px solid #d1d1d1;
					padding: 0px;
					bottom: -1px;
					z-index: 111;

					.apply-btn {
						box-shadow: none;
						float: right;
						height: 100%;
						padding: 18px;
						width: 50%;
						color: #8E52A1;
						font-size: 16px;
						border-left: 1px solid #d1d1d1;
					}

					.close-btn {
						color: black;
						box-shadow: none;
						float: right;
						height: 100%;
						padding: 18px;
						width: 50%;
						font-size: 16px;
					}

					.exit-btn {
						display: none;
					}
				}
			}
		}
	}

	#FullPageCalloutContainer .popupPage,
	.esri-popup__main-container .popupPage {

		.head {
			color: #8E52A1 !important;

			.title,
			.plus-button {
				color: #ffffff;
			}

			.head-pager .page-group .left-caret:before,
			.head-pager .page-group .right-caret:before {
				border-left-color: #8E52A1;
				border-right-color: #8E52A1;
			}

			.photo.no-image {
				background-color: #8E52A1;
			}
		}

		.content .content-extend {

			.tab-header ul li,
			.tab-content .sub-content .module .section-head,
			.tab-content .sub-content .module .section-head a.drill-down-links,
			.tab-content .sub-content .module .section-subhead,
			.tab-content .sub-content .notes-tab .center-container .notes-control button {
				color: #8E52A1;
			}

			.tab-header ul li,
			.tab-content .sub-content .notes-tab .center-container .notes-control button {
				border-color: #8E52A1;
			}

			.tab-header ul li.select {
				background-color: #8E52A1;
				color: #fff;
			}
		}
	}

	.mobile-modal-content-body .page-title {
		padding-left: 16px;
		font-weight: bold;
		letter-spacing: 3px;
		font-size: 27px;
		color: #262626;
		line-height: 56px;
		white-space: nowrap;
		font-family: "SourceSansPro-SemiBold";
		height: 56px;
		border-bottom: 2px solid #f2f2f2;
	}
}

.customizeddashboard.desktop-mode .detail-view-panel .grid-stack .grid-stack-item.widget-block .grid-stack-item-content .grid .kendo-grid.kendo-summarygrid-container .k-auto-scrollable {
	width: 100% !important;
}

.customizeddashboard.desktop-mode .detail-view-panel .grid-stack .grid-stack-item.widget-block:hover .toolbar {
	display: block;
}

.tabstrip-customdashboard .detail-view-panel .grid-stack .grid-stack-item.widget-block:hover .toolbar {
	display: block;
}

.cover-mouse-event-region {
	pointer-events: none;
}