(function()
{
	createNamespace('TF.Control.ResourceScheduler').EditTripOnCalendarLevelViewModel = EditTripOnCalendarLevelViewModel;

	const DATE_FORMAT = "MM/DD/YYYY";
	var MIN_DATE = "1/1/1753";
	var MAX_DATE = "12/31/9999";
	var NONE_LABLE = "(None)";
	var NONE_DRIVER = { DriverId: null, DriverName: NONE_LABLE };
	var NONE_AIDE = { AideId: null, AideName: NONE_LABLE };
	var NONE_VEHICLE = { VehicleId: null, VehicleName: NONE_LABLE };
	var BATCH_EDIT_PLACEHOLDER = "(Multiple Values Exist)";

	//the id is -1 means, made no changes to the select when batch edit
	var DRIVER_MULTIPLE_VALUES_OPTION = { DriverId: -1, DriverName: BATCH_EDIT_PLACEHOLDER },
		AIDE_MULTIPLE_VALUES_OPTION = { AideId: -1, AideName: BATCH_EDIT_PLACEHOLDER },
		VEHICLE_MULTIPLE_VALUES_OPTION = { VehicleId: -1, VehicleName: BATCH_EDIT_PLACEHOLDER };

	var WEEK_DAY_OPTIONS = [
		{ name: "work", label: "Mon-Fri", line: 1 },
		{ name: "tue", label: "Tuesday", line: 1 },
		{ name: "thu", label: "Thursday", line: 1 },
		{ name: "sat", label: "Saturday", line: 1 },
		{ name: "mon", label: "Monday", line: 2 },
		{ name: "wed", label: "Wednesday", line: 2 },
		{ name: "fri", label: "Friday", line: 2 },
		{ name: "sun", label: "Sunday", line: 2 },
	];

	var LEVEL_OPTIONS = [
		{ name: "allfuture", label: "All Future Days" },
		{ name: "daterange", label: "Date Range" }
	];

	var WEEK_DAY_ENUM = {
		SUN: 0,
		MON: 1,
		TUE: 2,
		WED: 3,
		THU: 4,
		FRI: 5,
		SAT: 6
	};

	var LEVEL_OPTION_ENUM = {
		ALL_FUTURE: "allfuture",
		DATE_RANGE: "daterange"
	}

	EditTripOnCalendarLevelViewModel.LEVEL_OPTION_ENUM = LEVEL_OPTION_ENUM;

	function EditTripOnCalendarLevelViewModel(options)
	{
		var self = this,
			today = moment(moment.utc().format(DATE_FORMAT));

		self._initialized = false;
		self._conflictsUpdating = false;

		self.currentDate = today;
		self.updating = false;
		self.momentHelper = new TF.Document.MomentHelper();
		self.timeRangeHelper = new TF.Document.ResourceScheduler.TimeRangeHelper();
		self.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel();
		self.tripList = options.trips;
		self.options = options;

		if (options.disableEditingAssignment)
		{
			options.DisableTimeEditing = true;
			options.DisableResourceEditing = true;
		}

		self.initDateRangeEditing(options);
		self.initTripTimeEditing(options);
		self.initResourceEditing(options);

		self.DisableDateEditing = ko.observable(!!options.DisableDateEditing);
		self.DisableTimeEditing = ko.observable(!!options.DisableTimeEditing);
		self.DisableResourceEditing = ko.observable(!!options.DisableResourceEditing);
		self.NeedProcessTrip = ko.observable(!!options.trips);
		this.obRecurringTypes = ko.observable(TF.Helper.TripDateRangeHelper.recurringTypes);
		this.obRecurEvery = ko.observable(1)
		this.obRecurBy = ko.observable(TF.Helper.TripDateRangeHelper.recurringTypes[0]);
		this.obRecurByText = ko.pureComputed(() =>
			this.obRecurBy().text
		);

		self.initTripData(options);
		self.editLevelChanged = new TF.Events.Event();
		self.dateRangeChanged = new TF.Events.Event();
	}

	EditTripOnCalendarLevelViewModel.prototype.initTripData = function(options)
	{
		if (options.disableEditingAssignment) return;

		var self = this,
			drivers = Array.isArray(options.drivers) ? options.drivers.slice() : [],
			aides = Array.isArray(options.aides) ? options.aides.slice() : [],
			vehicles = Array.isArray(options.vehicles) ? options.vehicles.slice() : [];

		self.drivers = drivers;
		self.aides = aides;
		self.vehicles = vehicles;

		self.updateResourceConflicts();
	};

	EditTripOnCalendarLevelViewModel.prototype.initDateRangeEditing = function(options)
	{
		const self = this;
		let { DisableDateEditing, maxDate, weekdayFlags } = options;

		if (!DisableDateEditing)
		{
			let firstLineOptions = [];
			let secondLineOptions = [];
			const dayOptionList = WEEK_DAY_OPTIONS.map((item) =>
			{
				const option = {
					name: item.name,
					label: item.label,
					checked: ko.observable(true),
					disable: ko.observable(false)
				};

				(option.line === 1 ? firstLineOptions : secondLineOptions).push(option);

				return option;
			});

			// to / from date pickers should both obey range restriction.
			self.minDate = moment(self.currentDate)//.format(DATE_FORMAT);
			self.maxDate = maxDate ? moment.utc(maxDate) : null;//.format(DATE_FORMAT) : null;
			self.minDatePickerValue = self.minDate.format(DATE_FORMAT);

			const momentMaxDate = moment(maxDate);
			self.maxDatePickerValue = maxDate && momentMaxDate.isValid() && momentMaxDate.year() !== 9999
				? moment(maxDate).add("day", 1).format(DATE_FORMAT)
				: MAX_DATE;

			self.weekdayFlags = weekdayFlags || new Array(7).fill(true);
			self.weekdayRange = [WEEK_DAY_ENUM.MON, WEEK_DAY_ENUM.TUE, WEEK_DAY_ENUM.WED, WEEK_DAY_ENUM.THU, WEEK_DAY_ENUM.FRI];
			self.weekendRange = [WEEK_DAY_ENUM.SUN, WEEK_DAY_ENUM.SAT];
			self.alldaysRange = Object.values(WEEK_DAY_ENUM);

			self.obEditLevelOptions = ko.observableArray(LEVEL_OPTIONS);
			self.obSelectedLevel = ko.observable();
			self.obFromDate = ko.observable();
			self.obToDate = ko.observable();

			self.obDayOptionList = ko.observableArray(dayOptionList);
			self.obDayOptionFirstLine = ko.observableArray(firstLineOptions);
			self.obDayOptionSecondLine = ko.observableArray(secondLineOptions);

			// if "all future" option is selected, date range should be disabled.
			self.shouldDateRangeDisabled = ko.computed(() => self.obSelectedLevel() === LEVEL_OPTION_ENUM.ALL_FUTURE);

			// bind function context
			self.obSelectedLevel.subscribe(self.onEditLevelChanged.bind(self));
			self.obFromDate.subscribe(() => self.onDateRangeUpdated(true));
			self.obToDate.subscribe(() => self.onDateRangeUpdated(false));

			self.onDateRangeUpdated = self.onDateRangeUpdated.bind(self);
			self.onWeekdayOptionUpdated = self.onWeekdayOptionUpdated.bind(self);
		}
	};

	EditTripOnCalendarLevelViewModel.prototype.initTripTimeEditing = function(options)
	{
		const self = this;
		const { DisableTimeEditing, trips } = options;

		if (!DisableTimeEditing)
		{
			self.obStartTime = ko.observable();
			self.obEndTime = ko.observable();
			self.timeOffsetByMinutes = ko.observable();

			let startTime, endTime;
			var starttimes = distinctArray(trips, "StartTime", function(item) { return self.momentHelper.toString(item, 'HH:mm'); });
			var endtimes = distinctArray(trips, "EndTime", function(item) { return self.momentHelper.toString(item, 'HH:mm'); });

			if (starttimes.length == 1 && endtimes.length == 1)
			{
				startTime = starttimes[0];
				endTime = endtimes[0];
			}

			if (trips.length > 1)
			{
				self.initTimeEditForBatch();
				self.obDurationValid = ko.observable(false);
			}
			else
			{
				self.initTimeEdit();
				self.timeOffsetByMinutes(self.momentHelper.minutesDiff(startTime, endTime));
				self.obStartTime(startTime);
				self.obEndTime(endTime);

				self.obDurationText = ko.pureComputed(function()
				{
					var timeoff = self.timeOffsetByMinutes();
					return String.format("Duration: {0}hr {1}min", parseInt(timeoff / 60), timeoff % 60);
				}, self);
				self.obDurationValid = ko.pureComputed(function()
				{
					var startMoment = self.momentHelper.toString(self.obStartTime(), 'HH:mm');
					var endMoment = self.momentHelper.toString(self.obEndTime(), 'HH:mm');
					return startMoment !== 'Invalid date' && endMoment !== 'Invalid date';
				});
			}
		}
	};

	EditTripOnCalendarLevelViewModel.prototype.initResourceEditing = function(options)
	{
		const self = this;
		let { DisableResourceEditing, drivers, aides, vehicles, trips } = options;

		if (!DisableResourceEditing)
		{
			drivers = Array.isArray(drivers) ? drivers.slice() : [];
			aides = Array.isArray(aides) ? aides.slice() : [];
			vehicles = Array.isArray(vehicles) ? vehicles.slice() : [];

			// Init driver, aide, and vehicle 
			drivers.unshift(NONE_DRIVER);
			aides.unshift(NONE_AIDE);
			vehicles.unshift(NONE_VEHICLE);
			self.tripList = trips;

			var selectedDriver = getSelectedItem(distinctArray(trips, "DriverId"), drivers, "DriverId", DRIVER_MULTIPLE_VALUES_OPTION);
			var selectedAide = getSelectedItem(distinctArray(trips, "AideId"), aides, "AideId", AIDE_MULTIPLE_VALUES_OPTION);
			var selectedVehicle = getSelectedItem(distinctArray(trips, "VehicleId"), vehicles, "VehicleId", VEHICLE_MULTIPLE_VALUES_OPTION);

			self.obAvailableDrivers = ko.observableArray(drivers);
			self.obSelectedDriver = ko.observable(selectedDriver);
			self.obSelectedDriverText = ko.observable(!selectedDriver ? "" : selectedDriver.DriverName);

			self.obAvailableAides = ko.observableArray(aides);
			self.obSelectedAide = ko.observable(selectedAide);
			self.obSelectedAideText = ko.observable(!selectedAide ? "" : selectedAide.AideName);

			self.obAvailableVehicles = ko.observableArray(vehicles);
			self.obSelectedVehicle = ko.observable(selectedVehicle);
			self.obSelectedVehicleText = ko.observable(!selectedVehicle ? "" : selectedVehicle.VehicleName);
		}
	}

	EditTripOnCalendarLevelViewModel.prototype.ProduceDatasourceArr = function(sourceArr, keyField, displayField, excludeIds)
	{
		var res = this.SeperateDatasourceArr(sourceArr, keyField, excludeIds, conflictArray, conflictFreeArray, keyField),
			conflictArray = res[0], conflictFreeArray = res[1];

		var tempItem = {};
		tempItem[keyField] = 0;
		tempItem[displayField] = "(none)";
		var newArr = [];
		newArr.push(tempItem);
		if (conflictFreeArray.length > 0)
		{
			let tempItem = {};
			tempItem[keyField] = null;
			tempItem[displayField] = "[disable]-------No Conflicts-------";
			newArr.push(tempItem);
			Array.sortBy(conflictFreeArray, displayField);
			Array.prototype.push.apply(newArr, conflictFreeArray);
		}
		if (conflictArray.length > 0)
		{
			let tempItem = {};
			tempItem[keyField] = null;
			tempItem[displayField] = "[disable]-------Conflicts-------";
			newArr.push(tempItem);
			Array.sortBy(conflictArray, displayField);
			Array.prototype.push.apply(newArr, conflictArray);
		}
		return newArr;
	};

	EditTripOnCalendarLevelViewModel.prototype.SeperateDatasourceArr = function(sourceArr, keyField, excludeIds)
	{
		var conflictArray = [];
		var conflictFreeArray = [];
		if (!Array.isArray(sourceArr) || !Array.isArray(excludeIds))
		{
			return [];
		}
		sourceArr.sort(function(a, b)
		{
			var aField = a[keyField], bField = b[keyField];
			if (aField == null && bField == null) return 0;

			if (aField == null) return -1;
			if (bField == null) return 1;
			if (aField == bField)
			{
				return 0;
			}
			return aField > bField ? 1 : -1;
		});
		excludeIds.sort((x, y) => x > y ? 1 : -1);
		for (var i = 0, j = 0; i < sourceArr.length;)
		{
			if (j == excludeIds.length)
			{
				conflictFreeArray.push(sourceArr[i]);
				i++;
				continue;
			}
			if (sourceArr[i][keyField] == excludeIds[j])
			{
				conflictArray.push(sourceArr[i]);
				i++;
				j++;
			} else if (sourceArr[i][keyField] < excludeIds[j])
			{
				conflictFreeArray.push(sourceArr[i]);
				i++;
			} else
			{
				j++;
			}
		}
		return [conflictArray, conflictFreeArray];
	};

	EditTripOnCalendarLevelViewModel.prototype.init = function(data, element)
	{
		var self = this,
			fromDate = self.currentDate;

		self.$element = $(element);
		self.obFromDate(fromDate.toLocaleString("en-US"));
		// self.obToDate(toDate.toLocaleString("en-US"));
		self.initValidation();

		// need to disable date range after customInputs are initialized.
		setTimeout(function()
		{
			self.obSelectedLevel(LEVEL_OPTIONS[0].name);
			self._initialized = true;
			$(':focus').blur();
		});
	};

	/**
	 * Validation initialization.
	 * @returns {void}
	 */
	EditTripOnCalendarLevelViewModel.prototype.initValidation = function()
	{
		var self = this,
			validatorFields = {},
			isValidating = false,
			callback = function(obDate)
			{
				return (value, validator, $el) =>
				{
					if (!value) return !tf.authManager.hasTripDates(); // raise form warning if Recurs feature enabled since it relay on Start Date and End Date.

					var todayDate = moment().startOf("day"),
						thisMoment = moment(value);
					if (!thisMoment.isValid() || thisMoment.isBefore(todayDate))
					{
						$el.val(null);
						obDate(null);
					}
					return true;
				};
			};

		validatorFields.From = {
			trigger: "blur change",
			validators:
			{
				callback: {
					message: "From Date is invalid.",
					callback: callback(this.obFromDate)
				}
			}
		};

		validatorFields.To = {
			trigger: "blur change",
			validators:
			{
				callback: {
					message: "To Date is invalid.",
					callback: callback(this.obToDate)
				}
			}
		};

		validatorFields.recurEveryBy = {
			trigger: "blur change",
			validators:
			{
				notEmpty: {
					message: "required"
				}
			}
		};

		setTimeout(function()
		{
			self.$element.bootstrapValidator(
				{
					excluded: [':hidden', ':not(:visible)'],
					live: 'enabled',
					message: 'This value is not valid',
					fields: validatorFields
				}).on('success.field.bv', function(e, data)
				{
					if (!isValidating)
					{
						isValidating = true;
						self.pageLevelViewModel.saveValidate(data.element).then(function()
						{
							isValidating = false;
						});
					}
				});

			self.pageLevelViewModel.load(self.$element.data("bootstrapValidator"));
		}, 0);
	};

	EditTripOnCalendarLevelViewModel.prototype.keepToDateGreaterThanFrom = function(isChangingFromDate)
	{
		var self = this,
			currentDate = moment().startOf("day"),
			startDate = new moment(self.obFromDate()),
			endDate = new moment(self.obToDate()),
			isEndDateGreaterThanOrEqualToStart = !startDate.isAfter(endDate),
			isStartDateValid = startDate.isValid() && !startDate.isBefore(currentDate),
			isEndDateValid = endDate.isValid() && !endDate.isBefore(currentDate);

		if (!isStartDateValid || !isEndDateValid || isEndDateGreaterThanOrEqualToStart) return;

		if (isChangingFromDate)
		{
			self.obToDate(self.obFromDate());
		}
		else
		{
			self.obFromDate(self.obToDate());
		}
	};

	EditTripOnCalendarLevelViewModel.prototype.getWorkDayOptions = function()
	{
		return this.obDayOptionList().filter(dayOption => { return this.weekdayRange.includes(WEEK_DAY_ENUM[dayOption.name.toUpperCase()]); });
	};

	EditTripOnCalendarLevelViewModel.prototype.getWeekDayOptions = function()
	{
		return this.obDayOptionList().filter(dayOption => { return WEEK_DAY_ENUM[dayOption.name.toUpperCase()] >= 0 })
			.sort((a, b) => { return WEEK_DAY_ENUM[a.name.toUpperCase()] - WEEK_DAY_ENUM[b.name.toUpperCase()] });
	};

	EditTripOnCalendarLevelViewModel.prototype.getApiDayOptions = function()
	{
		return this.getWeekDayOptions().map(function(day) { return day.checked() });
	};

	EditTripOnCalendarLevelViewModel.prototype.updateResourceConflicts = function()
	{
		if (this.options.disableEditingAssignment) return;

		var self = this,
			promiseArray,
			paramData = {
				databaseId: tf.api.datasourceManager.databaseId,
				startDate: moment(self.obFromDate()).format("YYYY-MM-DD")
			};

		if (self._conflictsUpdating) return;

		self._conflictsUpdating = true;

		// date range
		if (self.obSelectedLevel() == LEVEL_OPTIONS[1].name)
		{
			paramData.endDate = moment(self.obToDate()).format("YYYY-MM-DD");
		}

		promiseArray = self.tripList.map(function(trip)
		{
			var times = {};
			if (!self.options.DisableTimeEditing)
			{
				times['startTime'] = moment().format("YYYY-MM-DD") + "T" + self.momentHelper.toString(trip.StartTime, 'HH:mm:ss');
				times['endTime'] = moment().format("YYYY-MM-DD") + "T" + self.momentHelper.toString(trip.EndTime, 'HH:mm:ss');
			}
			var daysOptionsKey = window.UsingNET8API ? "daysOptions[]" : "daysOptions";
			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), 'conflictresschls'), {
				paramData: $.extend({
					id: trip.Id,
					[daysOptionsKey]: self.getApiDayOptions(),
					type: trip.Type.toLowerCase() == 'trip' ? 'Trip' : 'FieldTrip'
				}, paramData, times)
			});
		});

		Promise.all(promiseArray).then(function(conflictResArray)
		{
			var conflictDriverIds = [];
			var conflictAideIds = [];
			var conflictVehicleIds = [];
			conflictResArray.forEach(function(conflictRes)
			{
				conflictRes.Items.forEach(function(item)
				{
					conflictDriverIds.push(item.DriverId);
					conflictDriverIds.push(item.AideId);
					conflictAideIds.push(item.AideId);
					conflictAideIds.push(item.DriverId);
					conflictVehicleIds.push(item.VehicleId);
				});
			});
			self.tripList.forEach(trip =>
			{
				conflictDriverIds.push(trip.AideId);
				conflictAideIds.push(trip.DriverId);
			});
			self.obAvailableDrivers(self.ProduceDatasourceArr(self.drivers, "DriverId", "DriverName", conflictDriverIds));
			self.obAvailableAides(self.ProduceDatasourceArr(self.aides, "AideId", "AideName", conflictAideIds));
			self.obAvailableVehicles(self.ProduceDatasourceArr(self.vehicles, "VehicleId", "VehicleName", conflictVehicleIds));
			self._conflictsUpdating = false;
		});
	};

	EditTripOnCalendarLevelViewModel.prototype.onTimeUpdated = function()
	{
		if (!this._initialized) return;

		this.updateResourceConflicts();
	};

	/**
	 * On date range is updated. 
	 * @param {Object} data
	 * @param {Event} evt
	 */
	EditTripOnCalendarLevelViewModel.prototype.onDateRangeUpdated = function(isChangingFromDate)
	{
		var self = this;
		clearTimeout(self.onDateRangeUpdatedTimeout);
		self.onDateRangeUpdatedTimeout = setTimeout(() =>
		{
			if (self.updating) return;

			if (!self.obFromDate() || !self.obToDate())
			{
				self.updateWeekdayOptions(self.alldaysRange);
			}
			else
			{
				self.updating = true;
				self.keepToDateGreaterThanFrom(isChangingFromDate);
				self.updateWeekdayOptions(self.calculateWeekdayInclude(new moment(self.obFromDate()), new moment(self.obToDate())));
				setTimeout(function()
				{
					self.updating = false;
					self.dateRangeChanged.notify();

					if (self._initialized)
					{
						self.updateResourceConflicts();
					}
				}, 0);
			}
		}, 20);
	};

	EditTripOnCalendarLevelViewModel.prototype.initTimeEdit = function()
	{
		var self = this, tripEntity = self.tripList[0];

		self.obStartTime.subscribe(function() { self.onTimeRangeUpdated(true); });
		self.obEndTime.subscribe(function() { self.onTimeRangeUpdated(false); });

		self.obNumOfMinutes = ko.pureComputed({
			read: function()
			{
				return self.momentHelper.minutesDiff(tripEntity.StartTime, self.obStartTime());
			},
			write: function(val)
			{
				self.obStartTime(self.momentHelper.toString(self.momentHelper.add(tripEntity.StartTime, parseInt(val)), 'hh:mm A'));
			}
		});
		self.onPlusClick = function()
		{
			var self = this,
				starttime = self.momentHelper.toString(self.obStartTime(), 'HH:mm');
			self.obStartTime(self.momentHelper.toString(self.momentHelper.add(starttime, 1), 'hh:mm A'));
		}
		self.onMinusClick = function()
		{
			var self = this,
				starttime = self.momentHelper.toString(self.obStartTime(), 'HH:mm');
			self.obStartTime(self.momentHelper.toString(self.momentHelper.add(starttime, -1), 'hh:mm A'));
		}
	}

	EditTripOnCalendarLevelViewModel.prototype.initTimeEditForBatch = function()
	{
		var self = this;
		self.obStartTime.subscribe(function(val) { self.onTimeRangeUpdatedForBatch(true, val); });
		self.obEndTime.subscribe(function(val) { self.onTimeRangeUpdatedForBatch(false, val); });

		self.obNumOfMinutes = ko.observable(null);
		self.obNumOfMinutes.subscribe(function(val)
		{
			if (val == null)
			{
				return;
			}
			var integerNum = parseInt(val);
			if (integerNum !== val)
			{
				self.obNumOfMinutes(integerNum);
			} else
			{
				self.obStartTime(null);
				self.obEndTime(null);
			}
		});
		self.onPlusClick = function()
		{
			self.obNumOfMinutes(self.obNumOfMinutes() + 1);
		}
		self.onMinusClick = function()
		{
			self.obNumOfMinutes(self.obNumOfMinutes() - 1);
		}
	}

	EditTripOnCalendarLevelViewModel.prototype.onTimeRangeUpdated = function(isChangingFromTime)
	{
		var self = this;

		if (self.updating) return;

		if (self.obStartTime() == null || self.obEndTime() == null) return;

		self.updating = true;
		self.keepEndTimeGraterThanStart(isChangingFromTime);
		setTimeout(function()
		{
			self.updating = false;
			if (!self._initialized) return;

			self.updateResourceConflicts();
		}, 0);
	};

	EditTripOnCalendarLevelViewModel.prototype.onTimeRangeUpdatedForBatch = function(isChangingFromTime, val)
	{
		var self = this;
		if (self.obEndTime() && self.obStartTime())
		{
			isChangingFromTime ? self.obEndTime(null) : self.obStartTime(null);

		}
		if (val)
		{
			self.obNumOfMinutes(null);
		}
	}

	EditTripOnCalendarLevelViewModel.prototype.keepEndTimeGraterThanStart = function(isChangingFromTime)
	{
		var self = this,
			res = self.adjustTimeRange(self.timeOffsetByMinutes(), self.obStartTime(), self.obEndTime(), isChangingFromTime);
		self.obStartTime(self.momentHelper.toString(res.StartTime, 'hh:mm A'));
		self.obEndTime(self.momentHelper.toString(res.EndTime, 'hh:mm A'));
	};

	EditTripOnCalendarLevelViewModel.prototype.adjustTimeRange = function(timeoffset, start, end, isChangingFromStart)
	{
		var self = this,
			start = self.momentHelper.toString(start, 'HH:mm:ss'),
			end = self.momentHelper.toString(end, 'HH:mm:ss'),
			startTime = start,
			endTime = end;

		if (isChangingFromStart)
		{
			var updateTime = self.momentHelper.toString(self.momentHelper.add(start, timeoffset), 'HH:mm:ss');
			if (updateTime < start)
			{
				var result = self.timeRangeHelper.adjustSchedulerStartTime(start, self.timeRangeHelper.maxTime, timeoffset);
				startTime = result.startTime;
				endTime = result.endTime;
			}
			else
			{
				endTime = updateTime;
			}
		}
		else
		{
			var updateTime = self.momentHelper.toString(self.momentHelper.add(end, timeoffset * (-1)), 'HH:mm:ss');
			if (updateTime > end)
			{
				var result = self.timeRangeHelper.adjustSchedulerEndTime(self.timeRangeHelper.minTime, end, timeoffset);
				startTime = result.startTime;
				endTime = result.endTime;
			}
			else
			{
				startTime = updateTime;
			}
		}
		return {
			StartTime: startTime,
			EndTime: endTime
		}
	}


	/**
	 * On week day option is updated.
	 * @param {Object} data 
	 * @param {Event} evt 
	 */
	EditTripOnCalendarLevelViewModel.prototype.onWeekdayOptionUpdated = function(data, evt)
	{
		var self = this,
			checked = data.checked();

		if (data.name == "work")
		{
			self.getWorkDayOptions().forEach(dayOption => { dayOption.checked(checked) });
			return;
		}

		let allWeekdaysChecked = self.getWorkDayOptions().every(dayOption => dayOption.checked());
		self.obDayOptionList()[0].checked(allWeekdaysChecked);
	};

	/**
	 * Calculate the weekdays included within start date and end date.
	 * @param {Date} startDate 
	 * @param {Date} endDate
	 */
	EditTripOnCalendarLevelViewModel.prototype.calculateWeekdayInclude = function(startDate, endDate)
	{
		var self = this,
			includeWeekdays = [];
		if (startDate.isValid() && endDate.isValid())
		{
			var numberOfDaysPerWeek = 7,
				startWeekday = startDate.weekday(),
				range = Math.ceil(moment.duration(endDate - startDate).asDays());

			if (range >= 0)
			{
				if (range >= numberOfDaysPerWeek)
				{
					includeWeekdays = self.alldaysRange;
				}
				else
				{
					for (; range >= 0; range--)
					{
						includeWeekdays.push((startWeekday + range) % numberOfDaysPerWeek);
					}
				}
			}
		}

		return includeWeekdays;
	};

	/**
	 * Update the weekday option disable status according to included weekdays.
	 * @param {Array} includeWeekdays 
	 */
	EditTripOnCalendarLevelViewModel.prototype.updateWeekdayOptions = function(includeWeekdays)
	{
		const self = this;

		includeWeekdays = includeWeekdays.filter(i => self.weekdayFlags[i]);

		let checkedDays = [],
			// weekend would not be automatically selected unless only weekend are included.
			onlyWeekendIncluded = includeWeekdays.every((i) => self.weekendRange.indexOf(i) > -1);

		for (let i = self.obDayOptionList().length - 1; i >= 0; i--)
		{
			var shouldEnable, shouldCheck, item = self.obDayOptionList()[i];
			switch (item.name)
			{
				// case "all":
				// 	shouldEnable = includeWeekdays.length === 7;
				// 	shouldCheck = checkedDays.length === 7;
				// 	break;
				case "work":
					shouldEnable = includeWeekdays.length >= 5 && self.weekdayRange.every((idx) => includeWeekdays.indexOf(idx) > -1);
					shouldCheck = checkedDays.length >= 5 && self.weekdayRange.every((idx) => checkedDays.indexOf(idx) > -1);
					break;
				default:
					var dayIndex = WEEK_DAY_ENUM[item.name.toUpperCase()];
					shouldEnable = includeWeekdays.indexOf(dayIndex) > -1;
					shouldCheck = shouldEnable && (item.checked() || self.weekendRange.indexOf(dayIndex) === -1 || onlyWeekendIncluded);
					if (shouldCheck)
					{
						checkedDays.push(dayIndex);
					}
					break;
			}

			item.disable(!shouldEnable);
			item.checked(shouldCheck);
		}
	};

	/**
	 * Called when edit level is changed.
	 *
	 * @param {Event} evt
	 */
	EditTripOnCalendarLevelViewModel.prototype.onEditLevelChanged = function(evt)
	{
		var self = this,
			$dateInputs = self.$element.find(".form-group.to-date input, .form-group.from-date input"),
			currentDateStr = self.currentDate.clone()//.toLocaleString("en-US");

		switch (self.obSelectedLevel())
		{
			case LEVEL_OPTION_ENUM.ALL_FUTURE:
				$dateInputs.attr("disabled", "disabled");
				self.obFromDate(self.minDate);
				self.obToDate(self.maxDate);
				self.onDateRangeUpdated(false);
				// self.obDayOptionList().forEach((item) =>
				// {
				// 	const dayIdx = WEEK_DAY_ENUM[item.name().toUpperCase()];
				// 	const isAvailable = self.weekdayFlags[dayIdx];
				// 	item.disable(false);
				// 	item.checked(isAvailable);
				// });
				break;
			case LEVEL_OPTION_ENUM.DATE_RANGE:
				$dateInputs.attr("disabled", null);
				self.obFromDate(currentDateStr);
				self.obToDate(currentDateStr);
				break;
			default:
				break;
		}

		self.editLevelChanged.notify(self.obSelectedLevel());

		if (self._initialized)
		{
			self.updateResourceConflicts();
		}
	};

	EditTripOnCalendarLevelViewModel.prototype.setDateWithIn = function(trips, otherTrips, dayOfWeek, dateRange)
	{
		let notAllDateWithin = false, noDateWithin = true;
		for (const tripResource of trips)
		{
			const trip = { ...this.tripList.find(t => t.Id === tripResource.TripId) };

			// with override the day week to filter out the date range which is not fit.
			const tripWithChangeDayOfWeek = { ...trip, ...dayOfWeek };
			const dateRanges = TF.Helper.TripDateRangeHelper.getSetDates([dateRange], true);
			let combinedTripDateRanges = TF.Helper.TripDateRangeHelper.getMergedTripDateRanges(tripWithChangeDayOfWeek, tripWithChangeDayOfWeek.Id, dateRanges, false);
			otherTrips.forEach(x =>
			{
				if (x.Name == trip.Name)
				{
					trip.TripDateRanges = (trip.TripDateRanges || []).concat(x.TripDateRanges || []);
				}
			});
			const verifyResult = this.verifyAppliedDateRangeAndClip(trip, combinedTripDateRanges, dayOfWeek);
			if (verifyResult.notAllDateWithin)
			{
				notAllDateWithin = true;
			}

			if (!verifyResult.noDateWithin)
			{
				noDateWithin = false;
			}
		}
		return { notAllDateWithin, noDateWithin };
	}

	EditTripOnCalendarLevelViewModel.prototype.attachSameNameTripForFutureMode = function(postProcessData, otherTrips)
	{
		const trips = postProcessData.filter(t => t._type === "trip");
		for (const tripResource of trips)
		{
			otherTrips.forEach(tripItem =>
			{
				if (tripItem.Name === tripResource.Name)
				{
					const startTime = this.adjustTime(tripItem.StartTime);
					const endTime = this.adjustTime(tripItem.FinishTime);
					postProcessData.push({ ...tripResource, StartTime: startTime, EndTime: endTime, TripId: tripItem.Id });
				}
			})
		}
	}

	EditTripOnCalendarLevelViewModel.prototype.apply = async function()
	{
		var self = this, basicData = {};
		if (!this.options.mode && this.obSelectedLevel() !== "allfuture" && !await this.pageLevelViewModel.saveValidate())
		{
			return false;
		}

		if (!self.obDayOptionList().some(o => o.checked()))
		{
			tf.promiseBootbox.alert("At least one day should be enabled to make this change.");
			return false;
		}

		if (!self.DisableDateEditing())
		{
			var timeFormat = "HH:mm:ss",
				dateFormat = "YYYY-MM-DDTHH:mm:ss",
				formatTime = function(val, format) { return self.momentHelper.toString(val, format || timeFormat); },
				startDate = formatTime(self.obFromDate() || MIN_DATE, dateFormat),
				endDate = formatTime(self.obToDate() || MAX_DATE, dateFormat),
				weekdayData = self.exportDateList();
			$.extend(basicData, {
				DBID: tf.datasourceManager.databaseId,
				StartDate: startDate,
				EndDate: endDate
			}, weekdayData);

			if (self.obSelectedLevel() == LEVEL_OPTION_ENUM.ALL_FUTURE)
			{
				basicData.IsTripLevel = true;
				basicData.shouldNotUpdateTrip = true;
				basicData.IsAllFuture = true;
			}
		}

		if (!self.DisableTimeEditing())
		{
			$.extend(basicData, {
				StartTime: self.obStartTime(),
				EndTime: self.obEndTime(),
				MinutesDiff: self.obNumOfMinutes()
			});
		}

		if (!self.DisableResourceEditing())
		{
			$.extend(basicData, {
				DriverId: self.obSelectedDriver().DriverId,
				VehicleId: self.obSelectedVehicle().VehicleId,
				AideId: self.obSelectedAide().AideId,
			});
		}
		let postProcessData;
		if (self.NeedProcessTrip())
		{
			postProcessData = self.tripList.map(function(trip)
			{
				var obj = {};
				$.extend(obj, basicData, {
					TripId: trip.Id,
					Name: trip.Name,
				});
				if (!self.DisableTimeEditing())
				{
					var timeRange = self.adjustTimeRangeForBatchEntities(trip, formatTime);
					return $.extend(obj, {
						StartTime: self.momentHelper.toString(timeRange.StartTime, timeFormat),
						EndTime: self.momentHelper.toString(timeRange.EndTime, timeFormat),
						_type: trip.Type
					});
				}
				return obj;
			});
		}

		let otherTrips = await this.getSameNameTrips(this.tripList, moment(basicData.StartDate), basicData.IsAllFuture ? moment("9999-12-31") : moment(basicData.EndDate));

		if (otherTrips.length > 0)
		{
			const updateSameNameTrip = await tf.promiseBootbox.yesNo({ message: "The affected dates would occur during the date intervals of at least one other trip with the same name. Do you want to apply changes to the other trip(s)?", title: "Confirm Message" });
			if (!updateSameNameTrip)
			{
				otherTrips = [];
			}
		}

		if (!this.options.mode && otherTrips.length > 0 && basicData.IsAllFuture)
		{
			this.attachSameNameTripForFutureMode(postProcessData, otherTrips)
		}
		else if (!this.options.mode && !basicData.IsAllFuture && tf.authManager.hasTripDates())
		{
			const dayOfWeek = this.obDayOptionList().reduce((obj, curr) =>
			{
				obj[curr.label] = curr.checked();
				return obj;
			}, {});

			const dateRange = new TF.Helper.TripDateRangeHelper.TripDateRangeDataModel({
				StartDate: this.obFromDate(),
				EndDate: this.obToDate(),
				...dayOfWeek,
				RecurBy: this.obRecurBy()?.value,
				RecurEvery: this.obRecurEvery(),
			});

			const tripResourcesExtract = [];
			const trips = postProcessData.filter(t => t._type === "trip");
			const fieldTrips = postProcessData.filter(t => t._type === "fieldtrip");

			let { notAllDateWithin, noDateWithin } = this.setDateWithIn(trips, otherTrips, dayOfWeek, dateRange);

			for (const tripResource of trips)
			{
				const trip = this.tripList.find(t => t.Id === tripResource.TripId),
					sameNameTrips = otherTrips.filter(x => x.Name == trip.Name);
				[trip].concat(sameNameTrips).forEach(tripItem =>
				{
					// with override the day week to filter out the date range which is not fit.
					const tripWithChangeDayOfWeek = { ...tripItem, ...dayOfWeek };
					const combinedTripDateRanges = TF.Helper.TripDateRangeHelper.getMergedTripDateRanges(tripWithChangeDayOfWeek, tripWithChangeDayOfWeek.Id, TF.Helper.TripDateRangeHelper.getSetDates([dateRange], true), false);
					const verifyResult = this.verifyAppliedDateRangeAndClip(tripItem, combinedTripDateRanges, dayOfWeek);

					const startTime = tripItem.Id != trip.Id ? this.adjustTime(tripItem.StartTime) : tripResource.StartTime;
					const endTime = tripItem.Id != trip.Id ? this.adjustTime(tripItem.FinishTime) : tripResource.EndTime;
					if (verifyResult.clippedDateRanges.length > 0)
					{
						// postProcessData should only has one record to split data range
						Array.prototype.push.apply(tripResourcesExtract, verifyResult.clippedDateRanges.map(r => ({ ...tripResource, StartTime: startTime, EndTime: endTime, TripId: tripItem.Id, EndDate: r.EndDate, StartDate: r.StartDate })));
					}
				})
			}

			if (noDateWithin)
			{
				await tf.promiseBootbox.alert("None of the days for resource assignment are within the trip's date intervals. No change in resource assignment will take place.");
				return false; // terminate current process if no available date range.
			}

			if (notAllDateWithin)
			{
				await tf.promiseBootbox.alert("Not all dates for resource assignment are within the trip's date intervals. Resource assignment will apply only to days included in the trip's date intervals.");
			}

			postProcessData = tripResourcesExtract.concat(fieldTrips);
		}

		return {
			pageConfigData: basicData,
			postProcessData: postProcessData
		};
	};

	EditTripOnCalendarLevelViewModel.prototype.adjustTime = function(time)
	{
		return moment("2000-01-01 " + time).add(this.obNumOfMinutes(), "minutes").format("HH:mm:ss");
	}

	EditTripOnCalendarLevelViewModel.prototype.getSameNameTrips = async function(trips, startDate, endDate)
	{
		if (!tf.authManager.hasTripDates() || trips.length === 0)
		{
			return [];
		}

		const response = await tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "trips","samenames"), {
			paramData: {
				"tripIds": trips.map(t => t.Id),
				startDate:startDate.format("YYYY-MM-DD"),
				endDate:endDate.format("YYYY-MM-DD")
			},
		});
		return  response.Items;
	}

	EditTripOnCalendarLevelViewModel.prototype.verifyAppliedDateRangeAndClip = function(trip, appliedDateRanges, dayOfWeek)
	{
		const tripDateRanges = trip.TripDateRanges || [];
		const clippedDateRanges = [];

		/*
		* 1. All day of week within: The day of week in Trip Resource is fully part of the day of week from Trip
		- Trip day of week: Mo-Fr, Trip resource day of week: Mo, Tu
		* 0. Not all day of week within:
		- Trip day of week: Mo, Tu, Trip resource day of week: Tu, We
		* -1. No day of week within:
		- Trip day of week: Mo, Tu, Trip resource day of week: We,Th
		*/
		const compareDayPatterns = (tripDays, resourceDays) =>
		{
			let allWithin = true;
			let someWithin = false;

			for (let i = 0; i < 7; i++)
			{
				if (resourceDays[i] && !tripDays[i])
				{
					allWithin = false;
				}
				if (resourceDays[i] && tripDays[i])
				{
					someWithin = true;
				}
			}

			if (allWithin) return 1;
			if (someWithin) return 0;
			return -1;
		}
		const dayOfWeekMatchResult = compareDayPatterns(
			[trip.Monday, trip.Tuesday, trip.Wednesday, trip.Thursday, trip.Friday, trip.Saturday, trip.Sunday],
			[dayOfWeek.Monday, dayOfWeek.Tuesday, dayOfWeek.Wednesday, dayOfWeek.Thursday, dayOfWeek.Friday, dayOfWeek.Saturday, dayOfWeek.Sunday])

		// edit single trip's resource: currently it won't happen since Edit Trip Resource model does not allow to choose day of week which not available on trip.
		// edit multiple trip's resource: it may happen with all day of week does not match trip's day of week setting. 
		if (dayOfWeekMatchResult === -1)
		{
			return { clippedDateRanges: [], notAllDateWithin: true, noDateWithin: true };
		}

		// if trip date range is always, return appliedDateRanges directly
		if (tripDateRanges.length === 0)
		{
			return { clippedDateRanges: appliedDateRanges, notAllDateWithin: dayOfWeekMatchResult !== 1, noDateWithin: false };
		}

		let totalAppliedDateRangesDayCount = 0;
		appliedDateRanges.forEach(appliedDateRange =>
		{
			const startDate = moment(appliedDateRange.StartDate);
			const endDate = moment(appliedDateRange.EndDate);
			const rangesMatched = [];
			totalAppliedDateRangesDayCount += (endDate.diff(startDate, 'days') + 1);
			tripDateRanges.forEach(tripDateRange =>
			{
				const tripStartDate = moment(tripDateRange.StartDate);
				const tripEndDate = moment(tripDateRange.EndDate);

				const newStartDate = moment.max(startDate, tripStartDate);
				const newEndDate = moment.min(endDate, tripEndDate);

				if (!newStartDate.isAfter(newEndDate))
				{
					rangesMatched.push({
						StartDate: newStartDate.format("L"),
						EndDate: newEndDate.format("L"),
					});
					totalAppliedDateRangesDayCount -= (newEndDate.diff(newStartDate, 'days') + 1);
				}
			});
			/* if a single range of splitted trip resource date ranges cannot match any range of trip's date range, 
			 that means this single one is not within any range of trip */
			Array.prototype.push.apply(clippedDateRanges, rangesMatched);
		});

		return {
			clippedDateRanges: clippedDateRanges,
			// a. clipped date range not all in trip's date intervals b. clipped date range all in trip's date intervals, but the day of week not all all in trip's day of week
			notAllDateWithin: totalAppliedDateRangesDayCount > 0 || (clippedDateRanges.length !== 0 && dayOfWeekMatchResult === 0),
			// no matched date range found
			noDateWithin: clippedDateRanges.length === 0
		};
	}

	EditTripOnCalendarLevelViewModel.prototype.adjustTimeRangeForBatchEntities = function(trip, formatTime)
	{
		var self = this,
			timeDiff = self.obNumOfMinutes(),
			duration = self.momentHelper.minutesDiff(trip.StartTime, trip.EndTime);

		if (timeDiff)
		{
			return self.adjustTimeRange(duration, formatTime(self.momentHelper.add(trip.StartTime, timeDiff)), trip.EndTime, true);
		}
		else if (self.obStartTime())
		{
			return self.adjustTimeRange(duration, formatTime(self.obStartTime()), trip.EndTime, true);
		}
		else if (self.obEndTime())
		{
			return self.adjustTimeRange(duration, trip.StartTime, formatTime(self.obEndTime()), false);
		}

		return trip;
	};

	/**
	 * Export date range as an array of date.
	 */
	EditTripOnCalendarLevelViewModel.prototype.exportDateList = function()
	{
		var self = this,
			dayCheckList = self.obDayOptionList();

		return {
			Sunday: dayCheckList.filter(option => option.name.toLowerCase() == "sun")[0].checked(),
			Monday: dayCheckList.filter(option => option.name.toLowerCase() == "mon")[0].checked(),
			Tuesday: dayCheckList.filter(option => option.name.toLowerCase() == "tue")[0].checked(),
			Wednesday: dayCheckList.filter(option => option.name.toLowerCase() == "wed")[0].checked(),
			Thursday: dayCheckList.filter(option => option.name.toLowerCase() == "thu")[0].checked(),
			Friday: dayCheckList.filter(option => option.name.toLowerCase() == "fri")[0].checked(),
			Saturday: dayCheckList.filter(option => option.name.toLowerCase() == "sat")[0].checked(),
		};
	};

	EditTripOnCalendarLevelViewModel.prototype.dispose = function()
	{
		var self = this;
		self.momentHelper.dispose();
		self.timeRangeHelper.dispose();
		self.pageLevelViewModel.dispose();
	};

	function distinctArray(array, key, valueFnc)
	{
		if (!Array.isArray(array))
		{
			return array;
		}

		if (key)
		{
			array = array.map(function(item)
			{
				return item[key];
			});
		}
		if (valueFnc && typeof valueFnc === 'function')
		{
			array = array.map(function(item)
			{
				return valueFnc.call(this, item);
			});
		}

		return array.filter(function(value, index, self)
		{
			return self.indexOf(value) === index;
		});
	}

	function getSelectedItem(selectedkeys, dataSource, key, batchItem)
	{
		var selected;
		if (selectedkeys.length === 1)
		{
			selected = dataSource.find(function(item) { return item[key] == selectedkeys[0] }) || dataSource[0];
		} else if (selectedkeys.length > 1)
		{
			// if multi value exist, a new batchitem will be add to the datasource
			dataSource.unshift(batchItem);
			selected = batchItem;
		} else
		{
			selected = dataSource[0];
		}
		return selected;
	}
})();