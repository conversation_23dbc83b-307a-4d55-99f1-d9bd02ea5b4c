(function()
{
	createNamespace('TF.Modal').FormRuleCopyMappingLogModalViewModel = FormRuleCopyMappingLogModalViewModel;

	function FormRuleCopyMappingLogModalViewModel(options)
	{
		var self = this;
		TF.Modal.BaseModalViewModel.call(self);

		self.title("Copy Mapping");
		self.sizeCss = "modal-dialog-md";
		self.viewModel = new TF.Control.FormRuleCopyMappingLogViewModel(options, self);
		self.contentTemplate('modal/FormRuleCopyMappingLogControl');
		self.buttonTemplate('modal/positivenegative');
		self.obNegativeButtonLabel("Close");
		self.obNegativeButtonClass("btn btn-link btn-sm negative dialog-btn-right");
		self.obCustomizeCss("hide");
		self.data(this.viewModel);
	}

	FormRuleCopyMappingLogModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);

	FormRuleCopyMappingLogModalViewModel.prototype.constructor = FormRuleCopyMappingLogModalViewModel;

	FormRuleCopyMappingLogModalViewModel.prototype.negativeClick = function()
	{
		this.positiveClose();
	}
})()