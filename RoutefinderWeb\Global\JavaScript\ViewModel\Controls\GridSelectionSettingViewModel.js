(function()
{
	createNamespace("TF.Control").GridSelectionSettingViewModel = GridSelectionSettingViewModel;

	function GridSelectionSettingViewModel(options)
	{
		this.obSpecifyRecords = ko.observableArray([
			{ value: 'allInFilter', text: 'All records in filter' },
			{ value: 'selected', disable: options.selectedCount == 0, text: 'Current ' + options.selectedCount + ' selected ' + TF.getSingularOrPluralTitle("record", options.selectedCount) }
		]);
		var selectedSpecifyRecord = this.obSpecifyRecords()[options.selectedCount > 0 ? 1 : 0];
		this.obSelectedSpecifyRecords = ko.observable(selectedSpecifyRecord.value);
	}

	GridSelectionSettingViewModel.prototype.apply = function()
	{
		return {
			specifyRecords: this.obSelectedSpecifyRecords()
		};
	};

})();