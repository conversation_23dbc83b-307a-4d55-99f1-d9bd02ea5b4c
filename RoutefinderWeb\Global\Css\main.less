@import 'z-index';

@font-face {
	font-family: "SourceSansPro-Regular";
	src: local("SourceSansPro-Regular"),
		url("../../Global/Css/fonts/SourceSansPro_Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "SourceSansPro-SemiBold";
	src: local("SourceSansPro-SemiBold"),
		url("../../Global/Css/fonts/SourceSansPro_SemiBold.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "SourceSansPro-Italic";
	src: local("SourceSansPro-Italic"),
		url("../../Global/Css/fonts/SourceSansPro_Italic.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "SourceSansPro-Bold";
	src: local("SourceSansPro-Bold"),
		url("../../Global/Css/fonts/SourceSansPro_Bold.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Alegreya";
	src: local("Alegreya"),
		url("../../Global/Css/fonts/Alegreya-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Avenir-Next-LT-Pro";
	src: local("Avenir-Next-LT-Pro"),
		url("../../Global/Css/fonts/AvenirNextLTPro-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "BellTopo-Sans";
	src: local("BellTopo-Sans"),
		url("../../Global/Css/fonts/BellTopoSans-Regular.otf") format("opentype");
	font-display: auto;
}

@font-face {
	font-family: "Josefin-Slab";
	src: local("Josefin-Slab"),
		url("../../Global/Css/fonts/JosefinSlab-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Merriweather";
	src: local("Merriweather"),
		url("../../Global/Css/fonts/Merriweather-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Noto-Sans";
	src: local("Noto-Sans"),
		url("../../Global/Css/fonts/NotoSans-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Noto-Serif";
	src: local("Noto-Serif"),
		url("../../Global/Css/fonts/NotoSerif-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Playfair-Display";
	src: local("Playfair-Display"),
		url("../../Global/Css/fonts/PlayfairDisplay-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Ubuntu";
	src: local("Ubuntu"),
		url("../../Global/Css/fonts/Ubuntu-Regular.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Ubuntu-Light";
	src: local("Ubuntu-Light"),
		url("../../Global/Css/fonts/Ubuntu-Light.ttf") format("truetype");
	font-display: auto;
}

@font-face {
	font-family: "Ubuntu-Mono";
	src: local("Ubuntu-Mono"),
		url("../../Global/Css/fonts/UbuntuMono-Regular.ttf") format("truetype");
	font-display: auto;
}

html {
	height: 100%;
	width: 100%;
}

body {
	height: 100%;
	width: 100%;
	min-height: 640px;
	min-width: 1024px;
	position: relative;
	margin: 0;
	padding: 0;
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	background: white;
	font-family: "SourceSansPro-Regular";
	font-size: 12px;

	&.nav-icons-9 {
		min-height: 694px;
	}

	&.nav-icons-10 {
		min-height: 748px;
	}
}

@readOnlyBackgroundColor: rgba(239, 239, 239, 0.8);
@systemColor: #D0503C;
@systemReversedForeColor: white;
@systemForeColor: black;
@toggleButtonUncheckedColor: rgb(221, 221, 221);

.color-system {
	color: @systemColor;
	text-decoration: none;

	&:hover {
		color: @systemColor;
	}
}

.background-color-readonly {
	background-color: @readOnlyBackgroundColor;
}

.border-color-system {
	border-color: @systemColor;
}

a.color-system:hover {
	text-decoration: underline;
}

input::-ms-clear {
	display: none;
}

td,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p {
	cursor: default;
}

input[type=text].text-right {
	padding-right: 0;
}

body {
	display: flex;
}

.right-page {
	flex-grow: 100;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: white;
	position: relative;

	.popup-container {
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 100;
		pointer-events: none;
	}
}

#main {
	height: 100%;
	width: 100%;

	.tfmodal-container .tfmodal {
		z-index: 12031;
	}
}

calcite-button {
	--calcite-color-foreground-2: --calcite-color-foreground-3;
}

#main .docs {
	height: calc(~"100% - 28px");
	position: relative;
}

.docs .doc {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	overflow: hidden;
}

#main .doc-selector {
	height: 28px;
	bottom: 0;
	width: 100%;
	background: white;
	border-top: 1px solid #C4C4C4;
	position: relative;
}

#main .doc-selector .doc-item {
	height: 100%;
	border: 1px solid #C4C4C4;
	border-left: none;
	border-top: none;
	background-color: #ddd;
}

#main .doc-selector .doc-separator:first-child {
	display: none;
}

.list-mover-drag-hint .doc-selector .tab-dashboard-detail {
	background-image: url(../../global/img/menu/Edit-Black.png) !important;
	background-repeat: no-repeat;
	background-position: 10px, 0;
	padding: 0 10px 0 35px;
	background-size: 16px;
}

#main {
	.doc-selector {
		.doc-separator {
			width: 5px;
		}

		.doc-select {
			cursor: pointer;
			text-align: center;
			white-space: nowrap;
			line-height: 25px;
			background-color: #E0E0E0;
			color: #666666;

			&.document-hover,
			&:hover {
				background-color: white;
			}

			&.active {
				border-bottom: 3px solid #db4d37;
				color: #db4d37;
				position: relative;
				height: auto;
				margin-top: -1px;
				background-color: white;
				padding-top: 1px;
				line-height: 24px;
			}
		}

		.tab_dataentry {
			&.active {
				background-color: #F2F2F2;
			}
		}

		.tab_grid {
			&.active {
				background-color: #eae8e8;
			}
		}

		.tab_dataview_with_button {
			&.active {
				background-color: #F2F2F2;
			}
		}
	}
}

#mappage {
	display: block;
	width: auto;
	height: 100%;
}

.document-map .map {
	width: calc(~"100% - 250px");
	height: 100%;
	position: relative;
	left: 250px;
	z-index: 0;
	background: #f0ede5;
}

.toast-messages {
	a {
		cursor: pointer;
	}
}

.toast-messages-fade {
	animation-name: fadeOut;
	animation-duration: 2s;
	animation-fill-mode: both;
}

@-webkit-keyframes fadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

@keyframes fadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

#searchbar {
	width: 100%;
	margin-bottom: 5px;
}

#searchbar>input[type="text"] {
	width: 76%;
}

.verticalResize {
	resize: vertical;
}

.leftpanel {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	width: 250px;
	background: white;
	overflow-y: auto;
	overflow-x: hidden;
}

.leftpanel>* {
	pointer-events: auto;
}

.leftpanel .titleBar {
	height: 28px;
	line-height: 28px;
	background: #999;
	color: white;
	font-weight: bold;
	text-align: center;
}

.leftpanel .subTitleBar {
	height: 28px;
	line-height: 28px;
	background: #ccc;
	color: white;
	font-weight: bold;
	font-size: 11px;
	padding-left: 15px;
}

.floatleft {
	float: left;
}

.floatright {
	float: right;
}

.full-height {
	height: 100%;
}

.full-width {
	width: 100%;
}

.floatbutton {
	display: none;
	width: 80%;
}

.leftpanel .leftPanelTrip {
	cursor: pointer;
	font-size: 95%;
	font-weight: bold;
}

.leftpanel .IconBar {
	padding: 6px;
}

.leftpanel .IconBar>.iconbutton {
	margin-right: 10px;
}

.leftPanelTrip .tripTitleWrap,
.leftPanelTrip .stopListItemWrap {
	height: 25px;
	border-bottom: 1px solid #EEE;
	background-color: #FFFFFF;

	&:hover {
		background-color: #DADADA;
	}
}

.leftPanelTrip .tripTitleWrap .tripColorBar {
	height: 100%;
	width: 7px;
}

.leftPanelTrip .tripTitleWrap .tripTitle {
	line-height: 26px;
}

.leftPanelTrip .tripTitleWrap .tripTitle .tripName {
	margin-left: 2px;
}

.leftPanelTrip .tripTitleWrap .tripTitle .tripState {
	margin-right: 10px;
	font-size: 80%;
}

.leftPanelTrip .stopListItem {
	margin: 0px 10px;
	line-height: 23px;
}

.leftPanelTrip .stopListItem .stopSequence {
	color: #333;
	height: 12px;
	padding: 0 1px;
}

.leftPanelTrip .stopListItem .stopTime {
	font-size: 90%;
}

.leftPanelTrip .stopListItem .stopTime:hover {
	text-decoration: underline;
}

.leftPanelTrip .stopListItem .stopName {
	margin-left: 5px;
	width: 65%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.z-index-highlight {
	z-index: 12000;
}

.z-index-change {
	z-index: 1039;
}

.layerpanelwrapper {
	position: absolute;
	top: 55px;
	left: 0px;
	right: 0px;
	bottom: 0px;
	overflow-y: scroll;
}

#layerpanel {
	height: 100%;
}

#layerpanel #layerselector {
	border-bottom: 1px solid #666;
	/*border-radius: 6px;*/
}

#layerpanel #layerselector>ul {
	padding-top: 5px;
	height: 25px;
}

#layerpanel #layerselector>ul>li {
	float: left;
	padding: 0 10px 0 10px;
	list-style: none;
	line-height: 25px;
	/*border: 1px solid #666;*/
	border-top: none;
	border-left: none;
	border-bottom: none;
}

#layerpanel #layerselector>ul>li:last-child {
	border-right: none;
}

#layerpanel #layerselector>ul>li:hover {
	background: #FAA561;
	cursor: pointer;
}

#layerpanel #layers {
	height: 100%;
}

#layerpanel #layers>div {
	height: 100%;
}

#layerpanel #layers>div>div {
	height: 100%;
}

#layerpanel #layers .layertitle {}

.listCardWrapper {
	height: 100%;
}

.listCardWrapper .listCardTitleBar>* {
	display: inline-block;
}

.listCard {
	width: 80%;
	margin: 0 auto;
}

.listCard .tfSmallPopup {
	float: left;
}

.smallicon {
	cursor: pointer;
}

.smallicon img {
	height: 22px;
	width: 22px;
}

/*Overwrites*/

.olControlDragFeatureOver {
	cursor: default;
}

.olControlLayerSwitcher {
	top: 55px;
}

.panel-title {
	font-size: 14px;
	font-weight: bold;
}

.small_bootbox {
	overflow: hidden;
}

.small_bootbox .modal-dialog {
	width: 300px;
}

.small_bootbox .modal-header {
	padding: 10px;
}

.small_bootbox .modal-body {
	padding: 10px;
}

.small_bootbox .modal-footer {
	margin-top: 5px;
	padding: 10px;
}

.small_bootbox .btn {
	padding: 3px 6px;
}

.newStop .map {
	width: 100%;
	height: 210px;
}

.EditStop .map {
	width: 100%;
	height: 136px;
}

.olPopup {
	padding: 0;
}

.olPopup .popover-title {
	background: #141414;
	color: white;
	padding: 4px 14px;
	text-align: center;
}

.olPopup .popover-content {
	padding: 0;
}

.olPopup .tile {
	width: 70px;
	height: 45px;
	border-right: 1px #ddd solid;
	cursor: pointer;
}

.olPopup .tile:last-child {
	border-right: 0px #ddd solid;
}

.olPopup .tile .icon {
	width: 32px;
	height: 32px;
	margin: 0 auto;
}

.olPopup .tile .text {
	font-size: 9px;
	text-align: center;
}

.olPopup .stopPopup {
	width: calc(~"70px * 6");
}

.olPopup .studentPopup {
	width: calc(~"70px * 3");
}

.olControlDragFeatureOver {
	cursor: pointer;
}

.tf-documentpicker ul li {
	padding: 5px 10px;
	line-height: 22px;
	margin: 0 10px;
}

.tf-documentpicker ul li:hover {
	background: rgb(226, 238, 255);
}

label.requirestar::after {
	content: "*";
	color: red;
	margin-left: 3px;
}

label.font-size-setting::after {
	font-size: 20px;
}

.margin-top-5 {
	margin-top: 5px;
}

.margin-top-15 {
	margin-top: 15px;
}

.margin-top-25 {
	margin-top: 25px;
}

.margin-right-15 {
	margin-right: 15px;
}

.margin-left-15 {
	margin-left: 15px;
}

.margin-left-30 {
	margin-left: 30px;
}

.margin-bottom-15 {
	margin-bottom: 15px;
}

.margin-bottom-5 {
	margin-bottom: 5px;
}

.margin-bottom-0 {
	margin-bottom: 0;
}

.margin-bottom-minus-25 {
	margin-bottom: -25px;
}

.btn.btn-link {
	color: #333;
}

.btn-link {
	&:hover {
		text-decoration: none;
	}

	&:focus {
		text-decoration: none;
	}
}

.tf-list-menu {
	border-right: 2px solid #c8c8c8;
	height: 400px;
}

.tf-list-menu .glyphicon {
	color: #E7C000;
	margin-right: 5px;
	font-size: 12px;
}

.tf-list-menu ul {
	list-style: none;
	padding-left: 0;
}

.tf-list-menu hr {
	margin: 5px 0;
	border-color: #cacaca;
}

.tf-list-menu ul li {
	line-height: 2;
	padding-left: 10px;
	position: relative;
	cursor: pointer;
}

.tf-list-menu ul li.on,
.tf-list-menu ul li:hover {
	background-color: #DB4D37;
	color: #ffffff;
}

.tf-list-menu ul li.on:after,
.tf-list-menu ul li:hover:after {
	content: "";
	display: block;
	position: absolute;
	background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' height='24' width='30'><polyline points='0,0 0,24 20,24 30,12 20,0 0,0' style='fill:#DB4D37' /></svg>") no-repeat;
	right: -30px;
	top: 0;
	width: 30px;
	height: 24px;
}

.about-top {
	width: 598px;
	height: 140px;
	background-repeat: no-repeat;
	background-image: url('../../global/img/Routefinder-enterprise-orange-logo-and-icon.png');
	position: relative;
	background-position: 0 0;
	margin: -15px -30px -15px -15px;
}

.about-top .about-top-icon {
	position: absolute;
	top: 20px;
	left: 10px;
	width: 160px;
	height: 90px;
	border: 1px solid #d3d3d3;
	background-repeat: no-repeat;
	background-position: center;
	background-color: #141414;
	background-image: url('../../global/img/icons/transfinder.png');
}

[class^=list-style] {
	padding-left: 25px;
	position: relative;
}

[class^=list-style]:before {
	content: "";
	position: absolute;
	display: block;
	background-size: 12px 12px;
	background-repeat: no-repeat;
	background-position: left center;
	width: 14px;
	height: 100%;
	left: 0;
	opacity: 0.6;
}

.list-style-pencil:before {
	background-image: url(../img/grid/editor_pencil.png);
}

.list-style-gridview:before {
	background-image: url(../img/grid/grid.png);
}

.list-style-menu-control-panel:before {
	background-image: url(../img/menu/control-panel.png);
}

.list-style-mapview:before {
	background-image: url('../../global/img/grid/Map.png');
}

.list-style-menu-document:before {
	background-image: url(../img/menu/document.png);
}

.list-style-menu-report:before {
	background-image: url(../img/menu/report.png);
}

.text-eclipse {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	display: inline-block;
	width: 100%;
}

.reminder-data .text-eclipse {
	width: auto;
}

.reminder-data {
	padding: 2px 10px 2px 0;
}

.reminder-manage-grid-container {
	width: 680px;
	height: 354px;
}

.tab-left {
	background-repeat: no-repeat;
	background-position: 10px, 0;
	padding: 0 10px 0 35px;
}

#main {
	.path {
		padding-left: 0;
		padding-right: 0;

		span {
			padding: 0;
			font-size: 12px;
			color: #2e2e2e;
		}

		span:first-child {
			text-decoration: underline;
		}
	}

	.doc-selector .tab-flat,
	.doc-selector .tab-collapse {
		height: 27px;
	}

	.tab-flat .doc-select:hover,
	.tab-collapse .doc-select.tab-not-collapse:hover,
	.tab-flat .doc-select.document-hover {
		background-color: #DB4D37;
		color: white;

		.close {
			display: block;
			color: white;
			opacity: .8;
			margin: 0 -5px;
		}
	}

	.doc-selector .tab-collapse .contextmenu-open,
	.doc-selector .tab-collapse .document-hover {
		background-color: white;
		margin-top: -1px;
		padding-top: 1px;
		height: 28px;
	}

	.tab-flat .doc-select .close,
	.tab-collapse .doc-select .close {
		display: none;
	}

	.tab-scheduler {
		.tab-left;
		background-image: url('../../Global/img/menu/ResourceSched-Black.png');
	}

	.tab-grid {
		.tab-left;
		background-image: url('../../global/img/grid/Grid.png');
	}

	.doc-item:hover,
	.doc-item.document-hover {
		.tab-grid {
			background-image: url('../../global/img/grid/grid_White.png');
		}

		.tab-scheduler {
			background-image: url('../../Global/img/menu/ResourceSched-White.png');
		}
	}

	.tab-map {
		.tab-left;
		background-image: url('../../global/img/grid/Map.png');
	}

	.doc-item:hover .tab-map {
		background-image: url('../../global/img/grid/Map_White.png');
	}

	.tab-customized-dashboard {
		.tab-left;
		background-image: url('../../global/img/menu/dashboards-black.svg');
		background-size: 16px 16px;
	}

	.doc-item:hover .tab-customized-dashboard {
		background-image: url('../../global/img/menu/dashboards-white.svg');
		background-size: 16px 16px;
	}

	.tab-dataentry {
		.tab-left;
		background-image: url('../../global/img/menu/Edit-Black.png');
	}

	.doc-item:hover .tab-dataentry {
		background-image: url('../../global/img/menu/Edit-White.png');
	}

	.tab-dataview {
		.tab-left;
		background-image: url('../img/grid/view.png');
	}

	.doc-item:hover .tab-dataview,
	.doc-item.document-hover .tab-dataview {
		background-image: url('../img/grid/view-white.png');
	}

	.tab-controlpanelgrid {
		.tab-left;
		background-image: url('../../global/img/menu/control-panel.png');
	}

	.doc-item:hover .tab-controlpanelgrid {
		background-image: url('../../global/img/menu/control-panel_White.png');
	}

	.tab-dashboards {
		.tab-left;
		background-image: url('../../global/img/menu/dashboards-black.svg');
		background-size: 16px;
	}

	.tab-dashboard,
	.tab-dashboard-detail {
		.tab-left;
		background-image: url('../../global/img/menu/dashboards-black.svg');
		background-size: 16px;

		&.doc-item {
			padding-right: 10px;
		}
	}

	.tab-dashboard-detail {
		background-image: url('../../global/img/menu/Edit-Black.png') !important;
	}

	.tab-reportlayout {
		.tab-left;
		background-image: url('../../global/img/menu/report.png');

		&.doc-item {
			padding-right: 0;
		}
	}

	.tab-reportEditor {
		.tab-left;
		background-image: url('../../global/img/grid/editor_pencil_2.png');

		.event-template {
			text-overflow: ellipsis;
			overflow: hidden;
		}

		&.doc-item:not(.tab-reportEditor-consolidate) {
			padding-right: 0;
		}
	}

	.tab-dataGrids {
		.tab-left;
		background-image: url('../../global/img/grid/grid.svg');
		background-size: 16px;
	}

	.tab-forms {
		.tab-left;
		background-image: url('../../global/img/menu/forms.svg');
		background-size: 16px;
	}

	.tab-reminder {
		.tab-left;
		background-image: url('../../global/img/menu/reminders.svg');
		background-size: 16px;
	}

	.doc-selector .doc-item.tab-dashboard:hover,
	.doc-selector .doc-item.tab-dashboard-detail:hover {
		background-color: #DB4D37;
		color: white;
		background-image: url('../../global/img/menu/dashboards-white.svg');
		background-size: 16px;
	}

	.doc-item:hover .tab-dashboard,
	.doc-item:hover .tab-dashboard-detail {
		background-image: url('../../global/img/menu/dashboards-white.svg');
		background-size: 16px;
	}

	.doc-selector .doc-item.tab-dashboard-detail:hover,
	.doc-item:hover .tab-dashboard-detail {
		background-image: url('../../global/img/menu/Edit-White.png') !important;
	}

	.tab-documentcenter {
		.tab-left;
		background-image: url('../../global/img/menu/document.png');
	}

	.doc-item:hover .tab-documentcenter {
		background-image: url('../../global/img/menu/document_White.png');
	}

	.tab-report {
		.tab-left;
		background-image: url('../../global/img/menu/ViewReport-Black.png');
	}

	.doc-item:hover .tab-report {
		background-image: url('../../global/img/menu/ViewReport-White.png');
	}

	.tab-flat .tab-profile {
		.tab-left;
		background-image: url('../../global/img/menu/profile.png');
		padding-right: 0;
	}

	.doc-item:hover .tab-profile {
		background-image: url('../../global/img/menu/profile_White.png');

		.close,
		.close:hover {
			margin-right: 5px;
			margin-left: -20px;
		}
	}

	.tab-collapse .tab-profile {
		.tab-left;
		background-image: url('../../global/img/menu/profile.png');
		padding-right: 14px;

		&:hover {
			background-image: url('../../global/img/menu/profile_White.png');
			background-color: #DB4D37;
			color: white;

			.close,
			.close:hover {
				margin-right: -9px;
				display: block;
				color: white;
				opacity: .8;
			}
		}
	}

	.tab-resource {
		.tab-left;
		background-image: url('../../global/img/menu/ResourceSched-Black.png');
		padding-right: 0;
		width: 0;

		&:hover {
			background-image: url('../../global/img/menu/ResourceSched-White.png');
		}
	}

	.tab-cpstab {
		padding: 0 15px;
	}

	.tab-collapse .doc-select.tab-not-collapse {
		padding-right: 20px;

		&,
		&:hover {
			.close {
				margin-right: -15px;
			}
		}
	}

	.doc-selector .tab-closeall {
		padding: 0 15px;

		&:hover {
			background-color: #DB4D37;
			color: white;
		}
	}

	.tab-collapse .close,
	.tab-flat .close {
		font-size: 15px;
		font-weight: normal;
		line-height: 24px;
	}

	.tab-collapse .close {
		line-height: 27px;
	}

	.doc-print {
		text-align: center;
		width: 100%;
		height: calc(~"100% - 28px");
		display: none;

		.print-cell {
			display: table-cell;
			vertical-align: middle;
		}
	}

	.draggable-container {
		position: absolute;
		bottom: 1px;
		width: calc(100% - 1px);
		height: 27px;
	}

	.document-dataentry.view .container {
		width: auto;
		padding: 0 15px;
	}

	.edit-bottombar .container .row {
		margin: 0;
	}
}

.tf-contextmenu.tab-menu {
	.menu-item .active {
		color: #DB4D37;
	}

	.menu-title:first-child {
		margin-top: 10px;
	}

	.menu-title .close,
	.menu-item .close {
		display: none;
		font-size: 15px;
		font-weight: normal;
		line-height: 20px;
	}

	.menu-title:hover .close,
	.menu-item:hover .close,
	.menu-item.document-hover .close {
		display: block;
		opacity: 0.8;
	}

	.circle {
		width: 5px;
		height: 5px;
		background: #DB4D37;
		-moz-border-radius: 50px;
		-webkit-border-radius: 50px;
		border-radius: 50px;
		margin-top: 7px;
		visibility: hidden;
		margin-right: 4px;
	}

	.menu-item .circle.active {
		visibility: visible;
	}

	.menu-item .consolidate-tab-list {
		max-width: 210px;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}

	.tab-items-ellipsis {
		.event-template {
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
	}
}

.modal-dialog {
	.bv-form .help-block {
		height: auto;
		-ms-word-wrap: normal;
		word-wrap: normal;
		white-space: normal;
	}

	.dataexport {
		background: url(../img/grid/dataexport.png) no-repeat center center;
		height: 16px;
	}

	.about h5 {
		margin-bottom: 3px;
	}

	.property {
		margin: 0;
	}

	.property .form-group .checkbox {
		margin: 0;
	}

	.modal-content label.inactive {
		color: #999999;
		cursor: pointer;
	}

	.group-row {
		margin-bottom: 8px;

		.group-element-row {
			margin-top: 0;
			margin-bottom: 4px;
			padding: 0 0 0 0;

			.check-option-interval {
				padding: 0 0 0 0;
				margin: 0 0 0 0;
			}
		}

		.select-interval {
			margin-top: 0;
			margin-bottom: 6px;
		}

		.date {
			padding: 2px 0;

			&.left {
				padding-right: 8px;
			}

			&.right {
				padding-left: 8px;
			}
		}
	}
}

.header1 {
	font-size: 24px;
	font-weight: bold;
}

.step-header {
	font-size: 12px;
	font-weight: bold;
}

.message-selectable {
	user-select: text;
}

.titleNotify {
	background: none;
	color: #666666;
	font-size: 13px;
	border: 0;
	padding: 0;
	font-family: "SourceSansPro-Regular", Arial;
	word-break: normal;
	white-space: pre-wrap;
}

.draggable-item .doc-selector .doc-select {
	cursor: pointer;
	text-align: center;
	white-space: nowrap;
	line-height: 25px;
}

.draggable-item {
	.doc-select {
		color: white;
		background-color: #DB4D37;
		height: 26px;

		.close {
			display: none;
		}
	}

	.tab-cpstab {
		padding: 0 15px;
	}

	.tab-grid {
		.tab-left;
		background-image: url('../../global/img/grid/grid_White.png');
	}

	.tab-map {
		.tab-left;
		background-image: url('../../global/img/grid/Map_White.png');
	}

	.tab-customized-dashboard {
		.tab-left;
		background-image: url('../../global/img/menu/dashboards-white.svg');
		background-size: 16px 16px;
	}

	.tab-dataentry {
		.tab-left;
		background-image: url('../../global/img/menu/Edit-White.png');
	}

	.tab-controlpanelgrid {
		.tab-left;
		background-image: url('../../global/img/menu/control-panel_White.png');
	}

	.tab-documentcenter {
		.tab-left;
		background-image: url('../../global/img/menu/document_White.png');
	}

	.tab-report {
		.tab-left;
		background-image: url('../../global/img/menu/ViewReport-White.png');
	}

	.tab-profile {
		.tab-left;
		padding-right: 0;
		background-image: url('../../global/img/menu/profile_White.png');
	}

	.tab-resource {
		.tab-left;
		background-image: url('../../global/img/menu/ResourceSched-White.png');
		padding-right: 0;
		width: 0;
	}

	.draggable-container {
		position: absolute;
		bottom: 1px;
		width: calc(100% - 1px);
		height: 27px;
	}
}

.k-reorder-cue {
	z-index: @ent-typeahead-z-index !important;
	pointer-events: none;
}

.k-drag-clue {
	z-index: @ent-typeahead-z-index !important;
}

.modal-open .modal1 {
	overflow-x: hidden;
	overflow-y: auto;
}

.modal1 {
	display: none;
	overflow: hidden;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: @ent-modal1-z-index;
	-webkit-overflow-scrolling: touch;
	outline: 0;
}

.fieldtrip-status {
	button {
		&.other.btn-link {
			float: left !important;
			margin-left: 0;
		}

		&.negative {
			float: right;
			margin-right: 30px;
			margin-top: 4px;
			margin-left: 0;
		}

		&.positive {
			float: right;
		}
	}

	textarea {
		margin: 10px 0;
	}

	small.help-block {
		margin-bottom: 0;
		position: absolute;
		bottom: 20px;
		top: auto;
	}
}

.fieldtripstatus .dropdown-menu {
	max-height: 200px;
	overflow-y: auto;
}

.list-mover-grid-right-label {
	position: absolute;
	right: 0;
	top: -14px;

	label {
		font-size: 12px;
		line-height: 1.8;
	}
}

.sliderbar-button-wrap {
	position: absolute;
	left: calc(~"50%");
	z-index: 1001;
	height: 100%;
	border-left: 2pt solid #4b4b4b;

	&:hover {
		cursor: ew-resize;
	}

	.sliderbar-button {
		position: absolute;
		left: 0;
		top: calc(~"50% - 19.5px");
		z-index: 999;
		width: 44px;
		height: 39px;
		background: linear-gradient(90deg, #484848, #484848) no-repeat left center/8px 39px;
		border-radius: 0;
		cursor: pointer;

		&.slider-tapped,
		&:hover {
			cursor: pointer;
			background: repeating-linear-gradient(90deg, #868686, #868686 1px, #4b4b4b 1px, #4b4b4b 4px) no-repeat 16px center/9px 18px, #4b4b4b !important;
		}

		.white-dot {
			width: 2px;
			height: 2px;
			border-radius: 2px;
		}
	}
}

.form-control:focus {
	border-color: #ccc;
	outline: 0;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.document-dataentry .form-control:focus {
	background: #ffffff;
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
	background: #eee;

	&:focus {
		background: #eee;
	}
}

.tf-btn-gray {
	color: #333333;
	padding: 0px;
	height: 26px;
	width: 88px;
	background-color: #bbbbbb;
	border-color: #cccccc;
	font-size: 10px;
}

.tf-btn-gray:hover,
.tf-btn-gray:focus,
.tf-btn-gray:active,
.tf-btn-gray.active,
.open>.dropdown-toggle.tf-btn-gray {
	color: #333333;
	background-color: #cccccc;
	border-color: #dddddd;
}

.message-view {
	.title {
		font-size: 14px;
		margin-bottom: 8px;
		padding: 0 15px;

		small {
			font-size: 12px;
			color: #999999;
			margin-left: 5px;
		}
	}

	@media only screen and (min-width: 1000px) {
		.content-container {
			height: 564px;
		}
	}

	@media only screen and (max-width: 999px) {
		.content-container {
			height: 464px;
		}
	}

	.content-container {
		position: relative;
		padding: 0 40px;

		.content-wrap {
			height: 100%;
			width: 100%;
			overflow: hidden;
			position: relative;

			.detail {
				height: 100%;
				width: 100%;
				right: -100%;
				position: absolute;

				&.active {
					right: 0;
				}

				&.slideInLeft {
					animation: slideInToLeft 0.3s ease forwards;
				}

				&.slideOutLeft {
					animation: slideOutToLeft 0.3s ease forwards;
				}

				&.slideInRight {
					animation: slideInToRight 0.3s ease forwards;
				}

				&.slideOutRight {
					animation: slideOutToRight 0.3s ease forwards;
				}

				.content {
					overflow: auto;
					height: calc(~"100% - 28px");
					width: 100%;
				}

				.video-container {
					position: relative;
					width: 100%;
					height: 100%;

					.msg-video {
						width: 100%;
						height: 100%;
					}

					.msg-video-play {
						display: inline-block;
						width: 32px;
						height: 32px;
						position: absolute;
						margin: auto;
						bottom: 0;
						top: 0;
						left: 0;
						right: 0;
						box-shadow: none;
						border-radius: 0;
						background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0MCA0MCI+PHRpdGxlPlBsYXlfNDB4NDA8L3RpdGxlPjxwYXRoIGQ9Ik0yMCAuODMzYTE5LjE2NyAxOS4xNjcgMCAxIDAgMTkuMTY3IDE5LjE2NyAxOS4xNjcgMTkuMTY3IDAgMCAwLTE5LjE2Ny0xOS4xNjd6IiBvcGFjaXR5PSIuNiIvPjxwYXRoIGQ9Ik0yMCAyYTE4IDE4IDAgMSAxLTE4IDE4IDE4LjAyIDE4LjAyIDAgMCAxIDE4LTE4bTAtMmEyMCAyMCAwIDEgMCAyMCAyMCAyMCAyMCAwIDAgMC0yMC0yMHptOC4wNzIgMjAuOWExLjY3MSAxLjY3MSAwIDAgMS0uNTEzLjUxM2wtOS45OSA2LjM1N2ExLjY3MSAxLjY3MSAwIDAgMS0yLjU2OS0xLjQxM3YtMTIuNzE1YTEuNjcxIDEuNjcxIDAgMCAxIDIuNTY5LTEuNDFsOS45OSA2LjM1OGExLjY3MSAxLjY3MSAwIDAgMSAuNTEzIDIuMzF6IiBmaWxsPSIjZmZmIi8+PC9zdmc+);
					}
				}
			}
		}

		.btn-left,
		.btn-right {
			position: absolute;
			top: 50%;
			cursor: pointer;

			&.disabled {
				cursor: not-allowed;
				opacity: 0.1;
			}
		}

		.btn-left {
			border-right: 14px solid #231f20;
			border-top: 13px solid transparent;
			border-bottom: 13px solid transparent;
			left: 0;
		}

		.btn-right {
			border-left: 14px solid #231f20;
			border-top: 13px solid transparent;
			border-bottom: 13px solid transparent;
			right: 0;
		}
	}
}

.message-footer {
	display: flex;
	position: relative;
	justify-content: center;
	min-height: 26px;

	.btn-msg-close {
		position: absolute;
		left: 0;
	}

	.btn-msg-action {
		width: 400px;
		max-width: 400px;
		overflow: hidden;
		border: none;
	}
}

.left-fix-layout(@width) {
	>.left {
		float: left;
		position: relative;
		width: @width;
		margin-right: -@width;
	}

	>.right {
		margin-left: @width;
	}
}

.report-list-view {
	.left-fix-layout(300px);

	.list-view {
		height: 100%;
		background-color: #FAFAFA;

		.list-search {
			background-color: #FAFAFA;
			height: 50px;

			.search-box {
				float: right;
				position: relative;
				margin: 10px 10px 0 0;

				input {
					width: 240px;
					height: 30px;
					border: 1px solid #bfbfbf;
					padding-right: 30px;
					padding-left: 12px;
				}

				a.glass {
					width: 30px;
					height: 30px;
					cursor: pointer;
					background-image: url(../img/06-magnify.png);
					background-repeat: no-repeat;
					background-size: 20px 20px;
					background-position: center center;
					position: absolute;
					top: 0;
					right: 0;

					&:hover {
						transform: scale(0.8);
					}
				}
			}
		}

		.list-container {
			height: calc(~"100% - 80px");
			overflow: auto;

			.item {
				padding: 10px 10px 20px 100px;

				&:hover {
					.head {
						a {
							display: inline;
						}
					}
				}

				.head {
					margin: 0;
					font-weight: bold;
					font-size: 14px;
					position: relative;

					.favorite {
						color: #E7C000;
						margin: 0 2px 0 10px;
					}

					.favorite-on {
						color: #E7C000;
					}

					a {
						color: #333333;
						cursor: pointer;
						margin: 0 2px;
						display: none;

						&:hover {
							text-decoration: none;
						}
					}

					.buttons {
						a {
							margin: 0 2px;
						}
					}

					.right {
						position: absolute;
						right: 0;
						top: 0;
					}
				}

				.info {}

				.description {
					margin-top: 10px;
				}

				&:nth-child(odd) {
					background-color: #ffffff;
				}

				&:nth-child(even) {
					background-color: #ecf2f9;
				}
			}
		}

		.pagination {
			text-align: center;
			display: block;
			margin: 5px 0 0 0;
			padding: 0;

			span {
				margin: 0 2px;
			}
		}
	}
}

.nav-red {
	width: 300px;
	border: 1px solid #d5d5d5;
	height: 100%;
	font-size: 14px;
	padding: 10px 0;
	box-shadow: 5px 5px 10px 0 #bcbcbc;

	>ul {
		padding: 0;
		list-style: none;

		>li {
			padding: 6px 14px;
			margin-bottom: 1px;
			position: relative;
			cursor: pointer;

			>span,
			>i {
				cursor: pointer;
			}

			i {
				font-style: normal;
				margin-left: 2px;
			}

			&.active,
			&:hover {
				background-color: #DB4D37;
				color: #ffffff;
				margin-left: -1px;
				margin-right: -1px;
			}

			&.active {
				margin-right: -5px;

				&:after {
					border-top: 16px solid transparent;
					border-bottom: 16px solid transparent;
					border-left: 8px solid #db4d37;
					position: absolute;
					right: -8px;
					top: 0;
					content: "";
				}
			}
		}
	}
}

.bold {
	font-weight: bold;
}

.no-bold {
	font-weight: normal;
}

.mobile-padding {
	padding: 15px;
}

.mobile-list-view {
	border-top: 1px solid #d9d9d9;

	.item {
		border: 1px solid #d9d9d9;
		border-right: none;
		border-left: none;
		margin-top: -1px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 14px;
	}
}

.display-flex {
	display: flex;
}

.flex-1 {
	flex: 1;
}

.switch-buttons {
	display: flex;

	a {
		flex: 1;
		font-size: 14px;
		text-align: center;
		padding: 8px 15px;
		border-width: 1px;
		border-style: solid;

		&.active {
			background-color: @systemColor;
			color: #ffffff;
		}

		&:hover,
		&.color-system:hover {
			text-decoration: none;
		}
	}
}

.btn-add {
	background-image: url('../../global/img/icons/add-16x16-b.png');
	background-repeat: no-repeat;
	width: 16px;
	height: 16px;
	background-position: center center;
	display: inline-block;
}

.btn-help {
	background-color: black;
	display: inline-block;
	color: white;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	text-align: center;
}

.btn-delete {
	background-image: url('../../global/img/icons/close-16x16-black.png');
	background-repeat: no-repeat;
	width: 16px;
	height: 24px;
	background-position: center center;
	display: inline-block;
}

.checkmark {
	background-image: url('../../global/img/icons/checkmark.png');
	background-repeat: no-repeat;
	width: 22px;
	height: 24px;
	background-position: center right;
	display: inline-block;
}

.k-link span {
	cursor: pointer;
}

.tooltip {
	opacity: 1;
	background-color: white;
	border-radius: 5px;
	padding: 10px 15px;
	z-index: 40000;
	border: 1px solid #bcbcbc;
	font-family: "SourceSansPro-Regular", Arial;

	.arrow {
		border-top: 11px solid #bcbcbc;
		border-left: 11px solid transparent;
		border-right: 11px solid transparent;
		display: block;
		position: fixed;

		&:after {
			border-top: 10px solid white;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			content: '';
			display: block;
			position: absolute;
			top: -11px;
			left: -10px;
		}
	}

	.content {
		max-width: 250px;
		max-height: 100px;
		overflow: auto;
	}

	.scroll-content {
		overflow: auto;
	}

	a {
		cursor: pointer;
	}
}

@media (max-width: 736px) {
	.checkmark {
		background-image: url('../../global/img/icons/checkmark-12x12-black.png');
	}

	.btn-delete {
		background-image: url('../../global/img/icons/close-12x12-black.png');
		width: 12px;
	}
}

@keyframes slideInToLeft {
	from {
		right: -100%;
	}

	to {
		right: 0;
	}
}

@keyframes slideOutToLeft {
	from {
		right: 0;
	}

	to {
		right: 100%;
	}
}

@keyframes slideInToRight {
	from {
		right: 100%;
	}

	to {
		right: 0;
	}
}

@keyframes slideOutToRight {
	from {
		right: 0;
	}

	to {
		right: -100%;
	}
}

.k-calendar-container {
	.highlight-month {
		background: yellow;
		margin-right: -5px;
		padding-right: 5px;
	}

	&.enhanced-datetimepicker {
		border-width: 0px;
		box-shadow: 0 0 black;

		&.timepicker-selected {
			background: transparent;
			box-shadow: none !important;
			border: none !important;

			[data-kendo-role="calendar"] {
				height: 0 !important;
			}

			.time-icon-container {
				display: none;
			}
		}
	}
}

#main.new-window {
	.docs {
		height: 100%;

		.doc {
			overflow: auto;
		}
	}

	.doc-selector {
		display: none;
	}
}

.welcome-page {
	background-repeat: no-repeat;
	background-position: center;
	background-image: url('../../global/img/login_logo.png');
	background-size: 383px 287px;
	width: 100%;
	height: 100%;
}

@keyframes uil-default-anim {
	0% {
		opacity: 1
	}

	100% {
		opacity: 0
	}
}

.uil-default-css {
	position: relative;
	left: -78px;
	top: -78px;
	background: none;
	width: 200px;
	height: 200px;

	>div {
		background: rgb(255, 255, 255);
		border-radius: 8px;
		left: 93px;
		top: 80px;
		width: 16px;
		height: 40px;
		position: absolute;
		animation: uil-default-anim .7s linear infinite;
	}

	.generate-subs(12);

	.generate-subs(@n, @i: 1) when (@i =< @n) {
		>div:nth-of-type(@{i}) {
			@index: @i - 1;
			transform: rotate(30deg * @index) translate(0px, -58px);
			animation-delay: -0.35s + @index * 0.0583333333333;
		}

		.generate-subs(@n, (@i + 1));
	}
}

.no-left-padding {
	padding-left: 0;
}

.col-xs-24 .checkbox+.checkbox {
	margin-top: 10px;
	;
}

.col-xs-24.form-group.row>label {
	display: block;
}

.row>.row {
	padding-left: 0;
	margin-left: 0;
	padding-right: 0;
}

.row.col-xs-24.form-group>.col-xs-12 {
	padding-left: 0;
}

button:disabled {
	opacity: 0.5;
}

.k-dirty {
	display: none;
}

.disabled-element {
	opacity: 0.4 !important;
	pointer-events: none;

	&.checkbox {
		opacity: 1 !important;
	}
}

.checkbox {

	label span,
	input[type='checkbox'] {
		cursor: pointer;
	}
}

.residence-selector {
	height: 80px;

	&>div.list {
		border: 1px solid #C4C4C4;
		padding-left: 5px;
		padding-right: 0;
		height: 80px;
	}

	&>div.button {
		vertical-align: top;
	}
}

.warning {
	color: red;
}

.custom-list {
	padding: 0;
	overflow-y: auto;
	overflow-x: hidden;

	ul {
		list-style-type: none;
		padding-left: 0;
		margin-bottom: 0;

		li {
			&:focus {
				background-color: #ddedfb;
			}

			outline: none;
			padding: 4px 10px;
			display: flex;
			align-items: center;
			cursor: pointer;

			&:hover {
				background-color: #ddedfb;

				&.disabled {
					background-color: transparent;
				}
			}

			&.disabled {
				opacity: 0.7;
				cursor: default;
			}
		}
	}
}

.k-list .group-header {
	font-weight: bold;
	pointer-events: none;
	font-style: italic;
}

.k-list .group-header-hover.k-hover {
	background-color: transparent !important;
}

.ui-icon.ui-icon-gripsmall-diagonal-se {
	background-image: url('../../global/img/corner.svg');
	background-size: 12px;
}

.user-profile {
	.description {
		color: #666666;
	}

	.redistrict {
		word-break: normal;
		overflow-wrap: anywhere;
	}

	.map-settings {
		height: 322px;
		width: 552px;
	}

	.reset-map-link {
		padding-left: 520px;
	}

	.phone-ext-container {
		.phone {
			width: calc(100% - 50px);
			float: left;
		}

		.middle {
			width: 10px;
			height: 22px;
			float: left;
		}

		.ext {
			width: 40px;
			padding: 0;
			text-align: center;
		}
	}
}

@media (max-width: 1200px) {
	.user-profile {
		.image-default {
			width: 95px;
			height: 95px;

			img {
				width: 100%;
			}
		}
	}
}

@import 'geoCoding.less';


@circle-width: 40px;
@circle-border-width: 3px;

.spinner-circle-wrapper {

	visibility: hidden;

	.border {
		border: @circle-border-width solid black;
		width: @circle-width;
		height: @circle-width;
		box-sizing: content-box;
		position: absolute;
		z-index: 4;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;

		span {
			font-weight: bolder;
			font-size: 16px;
			text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
		}
	}

	.wrapper {
		position: relative;
		background: white;
		width: 42px;
		height: 42px;
		border-radius: 50%;
		top: 2px;
		left: 2px;
	}

	.pie {
		width: 50%;
		height: 100%;
		position: absolute;
		background: black;
		transform-origin: 100% 50%;
	}

	.spinner {
		border-radius: 20px 0 0 20px;
		z-index: 2;
		border-right: none;
	}

	.filler {
		border-radius: 0 20px 20px 0;
		z-index: 1;
		border-left: none;
		left: 50%;
		display: none;
	}

	.mask {
		width: 50%;
		height: 100%;
		position: absolute;
		z-index: 3;
		background: inherit;
	}
}

.top-align {
	vertical-align: top;
}

.indent-1 {
	margin-left: 15px;
}



.hasStudentCardConnectButton {
	#student-card-connect-button {
		position: absolute;
		bottom: 1px;
		right: 1px;
		z-index: 10000;

		button {
			height: 27px;
			width: 150px;
		}
	}

	#main .doc-selector {
		width: calc(100% - 152px);
	}
}

.routing-trip-kendo-dropdown.k-dropdownlist .k-input-inner{
	padding-left: 5px;
}

.k-list-item.k-disabled.routing-trip-dropdown-line {
	height: 1px !important;
	font-size: 1px !important;
	border-bottom: solid 1px gray !important;
	margin-bottom: 2px !important;
	pointer-events: none;
	cursor: default;

	.k-list-item-text {
		display: none !important;
	}
}

.boot-box-dialog-copy-link{
	text-align: right;
	padding-right: 2px;

	span{
		cursor: pointer;
		display: inline-block;
		padding-left: 20px;
		background: url('../../global/img/icons/copy.svg') no-repeat left center;
		background-size: 16px !important;
		&:hover {
			text-decoration: underline;
		}
	}
}

.tf-pie-chart-tooltip-content{
	max-width: 500px;
	white-space: normal;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}