(function()
{
	createNamespace("TF.Modal").AddExceptionModalViewModel = AddExceptionModalViewModel;

	function AddExceptionModalViewModel(data)
	{
		var options = {
			title: "Add Student Schedule Exception",
			availableTitle: "Available",
			selectedTitle: "Selected",
			displayCheckbox: false,
			showRemoveColumnButton: true,
			type: 'student',
			contentTemplate: "modal/AddException",
			serverPaging: true
		};
		TF.Modal.KendoListMoverWithSearchControlModalViewModel.call(this, [], options);
		this.AddExceptionViewModel = new TF.Fragment.AddExceptionViewModel(options, data, this.obDisableControl);
		this.data(this.AddExceptionViewModel);
		this.obPositiveButtonLabel("Apply");
		this.obResizable(false);
	}

	AddExceptionModalViewModel.prototype = Object.create(TF.Modal.KendoListMoverWithSearchControlModalViewModel.prototype);
	AddExceptionModalViewModel.prototype.constructor = AddExceptionModalViewModel;

	AddExceptionModalViewModel.prototype.positiveClick = function(viewModel, e)
	{
		this.AddExceptionViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	AddExceptionModalViewModel.prototype.dispose = function()
	{
		this.AddExceptionViewModel.dispose();
	};

})();

