(function()
{
	var namespace = window.createNamespace("TF.DataModel");
	namespace.ParcelAddressPointDataModel = function(recordEntity)
	{
		namespace.BaseDataModel.call(this, recordEntity);
	}

	namespace.ParcelAddressPointDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.ParcelAddressPointDataModel.prototype.constructor = namespace.ParcelAddressPointDataModel;

	namespace.ParcelAddressPointDataModel.prototype.mapping = [
		{ "from": "OBJECTID", "default": 0 },
		{ "from": "Style", "default": "" },
		{ "from": "Street", "default": "" },
		{ "from": "AddressNumber", "default": "" },
		{ "from": "Lock", "default": "" },
		{ "from": "LocalId", "default": "" },
		{ "from": "City", "default": "" },
		{ "from": "State", "default": "" },
		{ "from": "Type", "default": "Address Point" },
		{ "from": "RelateId", "default": null },
		{ "from": "IsCentroid", "default": null },
		{ "from": "PostalCode", "default": "" },
		{ "from": "Address", "default": "" },
		{ "from": "XCoord", "default": null },
		{ "from": "YCoord", "default": null },
		{ "from": "LastUpdatedBy", "default": 0 },
		{ "from": "LastUpdated", "default": null },
		{ "from": "CreatedBy", "default": 0 },
		{ "from": "CreatedOn", "default": null },
		{ "from": "Shape", "default": null },
		{ "from": "Boundary", "default": null },
	];
})();