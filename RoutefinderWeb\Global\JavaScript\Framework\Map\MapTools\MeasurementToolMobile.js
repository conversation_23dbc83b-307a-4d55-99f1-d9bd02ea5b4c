(function()
{
	createNamespace("TF.Map").MeasurementToolMobile = MeasurementToolMobile;

	function MeasurementToolMobile(mapView, $container)
	{
		var self = this;
		self.mapView = mapView;
		self.$container = $container;
		self.map = mapView._map;
		self.arcgis = mapView.ArcGIS;

		self.$infoPanel = null;
		self.$mode = null;
		self.$closeBtn = null;
		self.$content = null;
		self.$dropdown = null;
		self.$unit = null;
		self.$value = null;

		self.unitMenuHeightMapping = {
			"Distance": 259,
			"Area": 299,
			"Location": 99
		}
		self.tabNameList = ["Distance", "Area", "Location"];

		self.supportedUnitList = {
			"Distance": ["Miles", "Kilometers", "Feet", "Meters", "Yards", "Nautical Miles"],
			"Area": ["Acres", "Sq. Miles", "Sq. Km", "Hectares", "Sq. Yards", "Sq. Feet", "Sq. Meters"],
			"Location": ["Degrees", "Degrees, Minutes, Seconds"]
		};
		self.abbreviationsMapping = {
			"Kilometers": "km", "Miles": "mi", "Meters": "m", "Yards": "yd", "Feet": "ft", "Nautical Miles": "NM",
			"Sq. Miles": "sq mi", "Sq. Km": "sq. km", "Sq. Yards": "sq. yd", "Sq. Feet": "sq. ft", "Sq. Meters": "sq. m", "Hectares": "ha", "	Acre": "ac",
			"Degrees": "Deg.", "Degrees, Minutes, Seconds": "DMS"
		};
		self.unitTextMapping = {
			"Kilometers": "kilometers", "Miles": "miles", "Meters": "meters", "Yards": "yards", "Feet": "feet", "Nautical Miles": "nautical-miles",
			"Sq. Miles": "square-miles", "Sq. Km": "square-kilometers", "Sq. Yards": "square-yards", "Sq. Feet": "square-feet", "Sq. Meters": "square-meters", "Hectares": "hectares", "	Acre": "acres",
			"Degrees": "degrees", "Degrees, Minutes, Seconds": "dms"
		}
		self.defaultDistanceUnit = "Kilometers";
		self.defaultAreaUnit = "Sq. Km";
		self.defaultLocationUnit = "Degrees";
		self.defaultMeasurementType = "Distance";

		self.obDistanceValue = ko.observable(0);
		self.obAreaValue = ko.observable(0);
		self.obPinLocation = ko.observable(null);
		self.obTrackLocation = ko.observable(["---", "---"]);
		self.obSelectedUnit = ko.observable(null);

		self.isActive = false;
		self.currentMeasurementType = self.defaultMeasurementType;
		self.currentUnit = "Kilometers";

		self.measureHelper = null;
	};

	MeasurementToolMobile.prototype = Object.create(TF.Map.MeasurementToolMobile.prototype);
	MeasurementToolMobile.prototype.constructor = TF.Map.MeasurementToolMobile;

	/**
	 * Initialize Measurement Tool.
	 * @return {void}
	 */
	MeasurementToolMobile.prototype.init = function()
	{
		var self = this;
		self.initMeasureHelper();
		self.initMobileInfoPanel();
	};

	MeasurementToolMobile.prototype.initMeasureHelper = function()
	{
		var self = this,
			measureHelper = new TF.Map.MapMeasureHelper(self.map, self.arcgis, {
				isMobile: true
			});

		measureHelper.onMeasureEnd.subscribe(self.onHelperMeasureEnd.bind(self));
		self.measureHelper = measureHelper;
	};

	MeasurementToolMobile.prototype.onHelperMeasureEnd = function(evt, data)
	{
		var self = this, type = data.type, value = data.value;

		switch (type)
		{
			case "distance":
			case "area":
				value = (Math.floor(value * 100) / 100).toFixed(3);
				self.$value.html(value);
				break;
			case "location":
				value = self.lonLatFormatter(value, self.unitTextMapping[self.currentUnit]);
				self.$value.find(".location-value").eq(0).html(value[1]);
				self.$value.find(".location-value").eq(1).html(value[0]);
				break;
			default:
				break;
		}
	};

	/**
	 * Initialize the measurement information panel.
	 * @return {void}
	 */
	MeasurementToolMobile.prototype.initMobileInfoPanel = function()
	{
		var self = this;
		self.$infoPanel = $("<div></div>", { "class": "measurement-info-panel-mobile", "id": "measurementInfoPanel" }),
			self.$mode = $("<div></div>", { "class": "measurement-mode" }),
			self.$closeBtn = $("<div></div>", { "class": "measurement-close-button" }),
			self.$content = $("<div></div>", { "class": "measurement-content" }),
			self.$dropdown = $("<div></div>", { "class": "measurement-dropdown" }),
			self.$unit = $("<div></div>", { "class": "measurement-unit" }),
			self.$value = $("<div></div>", { "class": "measurement-value" });

		self.$mode.on("touchend", self.openModeMenu.bind(self));
		self.$dropdown.on("touchend", self.openUnitMenu.bind(self));
		self.$unit.on("touchend", self.openUnitMenu.bind(self));

		self.$unit.html(self.abbreviationsMapping[self.currentUnit]);

		$(document).on("touchend", function(e)
		{
			var $target = $(e.target);
			if ($(".unit-menu").length > 0 && $target.closest(".unit-menu").length <= 0 &&
				$target.closest(".measurement-dropdown").length <= 0 && $target.closest(".measurement-unit").length <= 0)
			{
				$(".unit-menu").animate({
					height: "55px"
				}, 300, function()
					{
						$(".unit-menu").remove();
						self.$content.show();
						self.$unit.html(self.abbreviationsMapping[self.currentUnit]);
					});
			}

			if ($(".menu-container").length > 0 && $target.closest(".menu-container").length <= 0 && $target.closest(".measurement-mode").length <= 0)
			{
				self.changeMeasureMode(self.currentMeasurementType);
			}
			e.stopPropagation();
		});

		self.$content.append(self.$dropdown).append(self.$unit).append(self.$value);
		self.$infoPanel.append(self.$mode).append(self.$closeBtn).append(self.$content);
		self.$container.append(self.$infoPanel);
	}

	MeasurementToolMobile.prototype.openUnitMenu = function()
	{
		var self = this, left = self.$content.offset().left + "px", bottom = self.$infoPanel.css("bottom"), width = self.$content.width() + "px", $menu,
			units = self.supportedUnitList[self.currentMeasurementType];
		self.$content.hide();
		$menu = $("<div class='unit-menu'></div>");
		$menu.css({ "left": left, "bottom": bottom, "width": width });

		$menu.animate({
			height: self.unitMenuHeightMapping[self.currentMeasurementType]
		}, 300);

		for (var i = 0; i < units.length; i++)
		{
			if (self.currentUnit !== units[i])
			{
				$menu.append($("<div class='unit-menu-item'>" + units[i] + "</div>"));
			}
		}
		$menu.append($("<div class='unit-menu-item'>" + self.currentUnit + "</div>"));
		$menu.find("div").on("touchend", function(e)
		{
			self.currentUnit = $(this).html();
			self.measureHelper.setUnit(self.unitTextMapping[self.currentUnit]);
			$menu.animate({
				height: "55px"
			}, 300, function()
				{
					$menu.remove();
					self.$content.show();
					self.$unit.html(self.abbreviationsMapping[self.currentUnit]);
				});
			e.stopPropagation();
		});
		self.$container.append($menu);
	}

	MeasurementToolMobile.prototype.openModeMenu = function()
	{
		var self = this, $menu;
		if (self.$mode.find(".menu-container").length > 0)
		{
			return;
		}
		self.$mode.empty();
		self.$mode.animate({
			marginTop: "-80px",
			height: "135px"
		}, 300);
		$menu = $("<div class='menu-container'></div>");
		for (var i = 0; i < self.tabNameList.length; i++)
		{
			if (self.currentMeasurementType !== self.tabNameList[i])
			{
				$menu.append($("<div style='padding-bottom: 14px;'>" + self.tabNameList[i] + "</div>"));
			}
		}
		$menu.append($("<div>" + self.currentMeasurementType + "</div>"));

		self.$mode.append($menu);
		$menu.find("div").on("touchend", function(e)
		{
			self.changeMeasureMode($(this).html());
			e.stopPropagation();
		});
	}

	/**
	 * Toggle the Measurement Tool activeness status.
	 * @return {void}
	 */
	MeasurementToolMobile.prototype.activate = function()
	{
		var self = this;
		if (!self.$infoPanel || !self.measureHelper)
		{
			self.init();
		}

		self.measureHelper.activate();
		self.changeMeasureMode(self.defaultMeasurementType);
		self.$infoPanel.addClass("active");
		self.isActive = true;
	};

	/**
	 * 
	 */
	MeasurementToolMobile.prototype.changeMeasureMode = function(name)
	{
		var self = this;
		self.currentMeasurementType = name;
		self.currentUnit = self["default" + self.currentMeasurementType + "Unit"];
		self.$unit.html(self.abbreviationsMapping[self.currentUnit]);

		if (self.currentMeasurementType === "Location")
		{
			self.$value.empty();
			self.$value.append($("<div class='location-container'><div class='location-unit'>Lat.</div><span class='location-value'>---</span></div>"));
			self.$value.append($("<div class='location-container'><div class='location-unit'>Lon.</div><span class='location-value'>---</span></div>"));
		}
		else
		{
			self.$value.html(0);
		}

		self.$mode.animate({
			marginTop: "0px",
			height: "100%"
		}, 300, function()
			{
				self.$mode.html(name);
			});
		self.measureHelper.setTool(name.toLowerCase());
		self.measureHelper.setUnit(self.unitTextMapping[self.currentUnit]);
	};

	/**
	 * Format the longitude and latitude data.
	 * @param {Array} lonLat The array that contains longitude and latitude.
	 * @param {string} unitType The type of the unit.
	 * @return {Array} The formatted coordinate data. 
	 */
	MeasurementToolMobile.prototype.lonLatFormatter = function(lonLat, unitType)
	{
		if (!lonLat) { return; }

		var self = this,
			fillDigits = function(value)
			{
				var gap = 2 - String(value).length;
				while (gap-- > 0) { value = "0" + value; }
				return value;
			},
			formatter = function(value)
			{
				if (unitType === "degrees")
				{
					value = value.toFixed(6) + "°";
				}
				else if (unitType === "dms")
				{
					var degree = Math.floor(value),
						decimal = (value - degree) * 60,
						minute = Math.floor(decimal),
						second = Math.floor((decimal - minute) * 60);
					value = degree + "°" + fillDigits(minute) + "'" + fillDigits(second) + "\"";
				}
				return value;
			};

		return lonLat.map(function(item)
		{
			return formatter(item);
		});
	};

	/**
	 * 
	 */
	MeasurementToolMobile.prototype.deactivate = function()
	{
		var self = this;
		if (self.$infoPanel && self.measureHelper)
		{
			self.$infoPanel.removeClass("active");
			self.measureHelper.deactivate();
		}
		self.isActive = false;
	};

	/**
	 * Dispose 
	 * @return {void}
	 */
	MeasurementToolMobile.prototype.dispose = function()
	{
		var self = this, key;
		if (self.$infoPanel)
		{
			self.$infoPanel.remove();
		}
	};
})();