(function()
{
	var MapLineStyleDefaults = {
		templateImageUrl: '',
		templateSvg: 'M0 0L 200 0',
		pattern: 0,
		style: 'solid',
		dashArray: 'none',
		svg: '',
	};
	var _allLineStyles = [];

	var colors = [
		"#ffffff", "#000000", "#d6ecff", "#4e5b6f", "#7fd13b", "#ea157a", "#feb80a", "#00addc", "#738ac8", "#1ab39f",
		"#f2f2f2", "#7f7f7f", "#a7d6ff", "#d9dde4", "#e5f5d7", "#fad0e4", "#fef0cd", "#c5f2ff", "#e2e7f4", "#c9f7f1",
		"#d8d8d8", "#595959", "#60b5ff", "#b3bcca", "#cbecb0", "#f6a1c9", "#fee29c", "#8be6ff", "#c7d0e9", "#94efe3",
		"#bfbfbf", "#3f3f3f", "#0090c0", "#8d9baf", "#b2e389", "#f272af", "#fed46b", "#51d9ff", "#aab8de", "#5fe7d5",
		"#a5a5a5", "#262626", "#003e75", "#3a4453", "#5ea226", "#af0f5b", "#c58c00", "#0081a5", "#425ea9", "#138677",
		"#7f7f7f", "#0c0c0c", "#00192e", "#272d37", "#3f6c19", "#750a3d", "#835d00", "#00566e", "#2c3f71", "#0c594f"
	];

	function MapLineStyle()
	{
		this.colors = colors;
		this.defaultSymbol = {
			type: "simple-line",
			color: TF.StreetHelper.color,
			width: 1,
			cap: "butt"
		}
	}

	createNamespace("TF.Map").MapLineStyle = new MapLineStyle();

	MapLineStyle.prototype.getAll = function()
	{
		return _allLineStyles.slice(0);
	};

	MapLineStyle.prototype.getByPattern = function(pattern)
	{
		var lineStyle = _allLineStyles.filter(function(c)
		{
			return c.pattern == pattern || c.style === pattern;
		});
		if (lineStyle.length > 0)
		{
			return lineStyle[0];
		}
		return null;
	};

	MapLineStyle.prototype.getStyle = function(pattern)
	{
		var lineStyle = this.getByPattern(pattern);
		if (lineStyle)
		{
			return lineStyle.style;
		}
		return MapLineStyleDefaults.style;
	};

	MapLineStyle.prototype.getTemplateSvg = function(pattern)
	{
		var lineStyle = this.getByPattern(pattern);
		if (lineStyle && lineStyle.svg)
		{
			var template = lineStyle.svg;
			return MapLineStyleDefaults.templateSvg +
				this.transform(0, [30, 0], template) +
				this.transform(0, [75, 0], template) +
				this.transform(0, [120, 0], template) +
				this.transform(0, [165, 0], template);
		}

		return MapLineStyleDefaults.templateSvg;
	};

	MapLineStyle.prototype.getDashArray = function(pattern, width)
	{
		var lineStyle = this.getByPattern(pattern);
		if (!lineStyle)
		{
			return MapLineStyleDefaults.dashArray;
		}
		var dashArray = lineStyle.dashArray;
		var valueList = dashArray.split(',');
		if (valueList.length < 2)
		{
			return dashArray;
		}
		var result = '';
		valueList.map(function(item)
		{
			result += `${item * width},`;
		});
		return result.substring(0, result.length - 1);
	};


	MapLineStyle.prototype.getRenderer = function(colors)
	{
		var uniqueValueInfos = [];
		this.getAll().forEach(item =>
		{
			colors.forEach(color =>
			{
				uniqueValueInfos.push({
					value: `${item.pattern}:${color}`,
					symbol: {
						type: "simple-line",
						width: 1,
						style: item.style,
						color: color,
						cap: this.defaultSymbol.cap
					}
				});
			});
		});

		var sizeVisualVariable = {
			type: "size",
			field: "width",
			minDataValue: 1,
			maxDataValue: 18,
			minSize: 1,
			maxSize: 18
		};

		var opacityVisualVariable = {
			type: "opacity",
			field: "opacity",
			stops: [
				{ value: 1, opacity: 1 },
				{ value: 0, opacity: 0 }
			]
		};
		return {
			type: "unique-value",
			field: "styleValue",
			visualVariables: [sizeVisualVariable, opacityVisualVariable],
			defaultSymbol: this.defaultSymbol,
			uniqueValueInfos: uniqueValueInfos
		};
	};

	function _createLineStyle(options)
	{
		_allLineStyles.push($.extend({}, MapLineStyleDefaults, options));
	}

	_createLineStyle({
		templateImageUrl: 'linestyle/STYLE_SOLID.png',
		pattern: 2,
		style: 'solid',
		dashArray: 'none'
	});

	_createLineStyle({
		templateImageUrl: 'linestyle/STYLE_DOT.png',
		pattern: 5,
		style: 'dot',
		dashArray: '1,3'
	});

	_createLineStyle({
		templateImageUrl: 'linestyle/STYLE_DASH.png',
		pattern: 9,
		style: 'dash',
		dashArray: '4,3'
	});

	_createLineStyle({
		templateImageUrl: 'linestyle/STYLE_DASHDOT.png',
		pattern: 20,
		style: 'dash-dot',
		dashArray: '4,3,1,3'
	});

	_createLineStyle({
		templateImageUrl: 'linestyle/STYLE_DASHDOTDOT.png',
		pattern: 22,
		style: 'long-dash-dot-dot',
		dashArray: '8,3,1,3,1,3'
	});
})();