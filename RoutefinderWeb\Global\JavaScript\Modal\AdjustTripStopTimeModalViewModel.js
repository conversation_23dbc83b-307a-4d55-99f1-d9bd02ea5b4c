﻿(function()
{
	createNamespace("TF.Modal").AdjustTripStopTimeModalViewModel = AdjustTripStopTimeModalViewModel;

	function AdjustTripStopTimeModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.title('Adjust ' + tf.applicationTerm.getApplicationTermSingularByName("Trip Stop") + ' Time');
		this.sizeCss = "modal-sm";
		this.contentTemplate('modal/AdjustTripStopTimeControl');
		this.buttonTemplate('modal/positivenegative');
		this.obPositiveButtonLabel("Apply");
		this.adjustTripStopTimeViewModel = new TF.Control.AdjustTripStopTimeViewModel();
		this.data(this.adjustTripStopTimeViewModel);

		this.containerLoaded = ko.observable(false);
	};

	AdjustTripStopTimeModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	AdjustTripStopTimeModalViewModel.prototype.constructor = AdjustTripStopTimeModalViewModel;

	AdjustTripStopTimeModalViewModel.prototype.positiveClick = function()
	{
		this.adjustTripStopTimeViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.positiveClose(result);
			}
		}.bind(this));
	};

	AdjustTripStopTimeModalViewModel.prototype.dispose = function()
	{
		this.adjustTripStopTimeViewModel.dispose();
	};
})();

