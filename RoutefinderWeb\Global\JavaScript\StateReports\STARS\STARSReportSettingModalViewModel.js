(function()
{
	createNamespace('TF.Modal').STARSReportSettingModalViewModel = STARSReportSettingModalViewModel;

	function STARSReportSettingModalViewModel()
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.modalRootContainerCss = "stars-report-dialog";
		this.sizeCss = "modal-dialog-md";
		this.contentTemplate('modal/StateReport/STARSReportSetting');
		this.buttonTemplate('modal/positivenegative');
		this.title("Transfinder STARS Export Module");
		this.data(new TF.Control.STARSReportSettingViewModel());
		this.obPositiveButtonLabel('Export Files');
	}

	STARSReportSettingModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	STARSReportSettingModalViewModel.prototype.constructor = STARSReportSettingModalViewModel;
	STARSReportSettingModalViewModel.prototype.positiveClick = function()
	{
		this.data().apply().then((res) =>
		{
			if (res)
			{
				this.positiveClose(res);
			}
		});
	}
})()