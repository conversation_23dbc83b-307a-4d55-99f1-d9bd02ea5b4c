(function()
{
	createNamespace("TF.Control").TripChatUserConfirmViewModel = TripChatUserConfirmViewModel;

	function TripChatUserConfirmViewModel(selectRecordIds, modalViewModel)
	{
		const self = this;
		self.obTripUsers = ko.observableArray([]);

		self.staffTypeOptions = ko.observableArray([
			{ TypeName: 'Driver', TypeId: 'DriverId', Checked: ko.observable(true), idExpression: (x) => x.DriverId },
			{ TypeName: 'Current Driver', TypeId: 'CurrentDriverID', Checked: ko.observable(true), idExpression: (x) => x.CurrentDriverID },
			{ TypeName: 'Bus Aide', TypeId: 'AideId', Checked: ko.observable(true), idExpression: (x) => x.AideId },
			{ TypeName: 'Current Bus Aide', TypeId: 'CurrentBusAideID', Checked: ko.observable(true), idExpression: (x) => x.CurrentBusAideID },
		]);

		self.selectRecordIds = selectRecordIds;

		self.modalViewModel = modalViewModel;
	}

	TripChatUserConfirmViewModel.prototype.staffTypeChanged = function(viewModel, e)
	{
		const self = this;
		const checkedOptions = this.staffTypeOptions().filter(x => x.Checked());
		if (checkedOptions.length == 0)
		{
			self.modalViewModel.obDisableControl(true);
		} else
		{
			self.modalViewModel.obDisableControl(false);
		}
	}

	TripChatUserConfirmViewModel.prototype.GetStaffsByTripIds = function()
	{
		const self = this;
		const optionOperates = this.staffTypeOptions().filter(x => x.Checked());
		const TypeIds = optionOperates.map(o => o.TypeId)
		const postData = {
			sortItems: [
				{
					Name: "Name",
					isAscending: "asc",
					Direction: "Ascending"
				}
			],
			idFilter: {
				IncludeOnly: self.selectRecordIds
			},
			fields: ["Id", "Name", ...TypeIds]
		}

		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", "trips"), {
			data: postData
		}).then(response =>
		{
			return response.Items;
		});
	}

	TripChatUserConfirmViewModel.prototype.apply = function()
	{
		const optionOperates = this.staffTypeOptions().filter(x => x.Checked());
		const tripStaffIdList = [];

		return this.GetStaffsByTripIds().then((staffIds) =>
		{
			staffIds.forEach(record =>
			{
				optionOperates.forEach(operate =>
				{
					const staffId = operate.idExpression(record);
					if (staffId && staffId != 0)
					{
						if (!tripStaffIdList.includes(staffId))
						{
							tripStaffIdList.push(staffId);
						}
					}
				})
			});

			if (tripStaffIdList.length === 0)
			{
				return Promise.resolve(false);
			}

			return tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), 'staff'), {
				paramData: {
					"@filter": `in(id,${tripStaffIdList.join(",")})`
				}
			}).then(function(apiResponse)
			{
				return apiResponse.Items;
			});
		})
	};
})(); 