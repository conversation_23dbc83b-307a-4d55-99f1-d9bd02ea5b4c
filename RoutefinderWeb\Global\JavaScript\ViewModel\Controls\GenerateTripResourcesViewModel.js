(function()
{
	createNamespace('TF.Control').GenerateTripResourcesViewModel = GenerateTripResourcesViewModel;

	function GenerateTripResourcesViewModel(tripIds)
	{
		var now = new Date();
		this.tripIds = tripIds;
		this.pageLevelViewModel = new TF.PageLevel.BasePageLevelViewModel(this);
		this.entity = {
			monday: ko.observable(true),
			tuesday: ko.observable(true),
			wednesday: ko.observable(true),
			thursday: ko.observable(true),
			friday: ko.observable(true),
			saturday: ko.observable(false),
			sunday: ko.observable(false),
			startDate: ko.observable(moment(now).format("L")),
			endDate: ko.observable(moment(now).format("L")),
			applySchoolCalendar: ko.observable(true),
			filter: ko.observable(),
			specifyRecordOption: ko.observable(tripIds && tripIds.length > 0 ? specifyRecordOptions.SpecificRecords : specifyRecordOptions.AllRecords),
			selectedDataSource: ko.observable(),
			selectedSpecificRecord: ko.observableArray()
		};
		this.onWeekDaysComponentCreated = this.onWeekDaysComponentCreated.bind(this);
		this.initUIBinding();
	}

	const specifyRecordOptions = {
		AllRecords: {
			id: 1,
			name: "All Records"
		},
		Filter: {
			id: 2,
			name: "Filter"
		},
		SpecificRecords: {
			id: 3,
			name: "Specific Records"
		}
	};

	const specifyRecordsSource = Object.values(specifyRecordOptions);

	GenerateTripResourcesViewModel.prototype.init = function(viewModel, el)
	{
		this.$element = $(el);
	};

	GenerateTripResourcesViewModel.prototype.onWeekDaysComponentCreated = function()
	{
		this.initValidation();
	};

	GenerateTripResourcesViewModel.prototype.initUIBinding = function()
	{
		// data source
		let dataSources = tf.datasourceManager.datasources.map(function(item)
		{
			var obj = {
				name: item.Name,
				id: item.DBID,
				version: item.DBVersion
			};

			return obj;
		}).sort((a, b) => a.name.toLowerCase() > b.name.toLowerCase() ? 1 : -1);

		this.obDataSourceOptions = ko.observableArray(dataSources);
		this.obSelectedDataSourceText = ko.pureComputed(() =>
		{
			return this.entity.selectedDataSource() ? this.entity.selectedDataSource().name : "";
		});
		this.entity.selectedDataSource(dataSources.find(x => x.id == tf.datasourceManager.databaseId));
		this.selectedDataSourceId = this.entity.selectedDataSource().id;
		// clear filter setting when data source changed
		this.entity.selectedDataSource.subscribe((newValue) =>
		{
			if (newValue.id != this.selectedDataSourceId)
			{
				this.selectedDataSourceId = newValue.id;
				this.entity.filter(null);
				this.getAndSetFilterDataSource();
				this.entity.selectedSpecificRecord([]);
			}
		});

		// specify record
		this.obSpecifyRecords = ko.observableArray(specifyRecordsSource);
		this.obSpecifyRecordOptionText = ko.pureComputed(() =>
		{
			return this.entity.specifyRecordOption() ? this.entity.specifyRecordOption().name : "";
		});

		this.specifyRecordOptionId = this.entity.specifyRecordOption().id;
		// clear filter and specific records when specify record option changed
		this.entity.specifyRecordOption.subscribe((newValue) =>
		{
			if (newValue.id != this.specifyRecordOptionId)
			{
				this.specifyRecordOptionId = newValue.id;
				this.entity.filter(null);
				this.entity.selectedSpecificRecord([]);
			}
		});

		// filter name
		this.obFilterDataSource = ko.observableArray();
		this.obFilterText = ko.pureComputed(() =>
		{
			return this.entity.filter() ? this.entity.filter().Name : "";
		});
		this.obFilterSpec = ko.pureComputed(() =>
		{
			return this.entity.filter() ? this.entity.filter().WhereClause : "";
		});
		this.obDisabledFilterName = ko.pureComputed(() =>
		{
			return this.entity.specifyRecordOption().id != 2;
		});
		this.obFilterForValidation = ko.pureComputed(() =>
		{
			setTimeout(() =>
			{
				this.validator && this.validator.revalidateField('filterName');
			}, 20);
			return this.obDisabledFilterName() || this.obFilterText() ? "1" : "";
		});
		this.getAndSetFilterDataSource();

		// specific records
		this.obDisabledSpecificRecord = ko.pureComputed(() =>
		{
			return this.entity.specifyRecordOption().id != specifyRecordOptions.SpecificRecords.id;
		});
		this.obSpecificRecordStringForValidation = ko.pureComputed(() =>
		{
			return this.obDisabledSpecificRecord() || this.entity.selectedSpecificRecord().length > 0 ? "1" : "";
		});
		if (this.entity.specifyRecordOption().id == specifyRecordOptions.SpecificRecords.id && this.tripIds.length > 0)
		{
			this.getTripByIds(this.tripIds).then(trips =>
			{
				this.entity.selectedSpecificRecord(trips);
			});
		}
	};

	GenerateTripResourcesViewModel.prototype.initValidation = function()
	{
		var self = this;
		function validateDates()
		{
			var endDate = self.entity.endDate();
			if (!endDate) return true;

			var startDate = self.entity.startDate();
			if (!startDate) return true;

			var start = moment(startDate), end = moment(endDate);
			return start.isSame(end) || start.isBefore(end);
		}

		var validatorFields = {
			weekdays: {
				container: this.$element.find(".days"),
				validators: {
					choice: {
						min: 1,
						message: 'Please choose one week day at least.'
					}
				}
			},
			startDate: {
				trigger: "blur change",
				validators: {
					notEmpty:
					{
						message: "is required"
					},
					callback: {
						message: "must <= End Date",
						callback: validateDates
					}
				}
			},
			endDate: {
				trigger: "blur change",
				validators: {
					notEmpty:
					{
						message: "is required"
					},
					callback: {
						message: "must >= Start Date",
						callback: validateDates
					}
				}
			},
			filterName: {
				trigger: "blur change",
				validators:
				{
					notEmpty:
					{
						message: "is required"
					}
				}
			},
			specificRecords: {
				trigger: "blur change",
				validators:
				{
					notEmpty:
					{
						message: " At least one record must be selected"
					}
				}
			}
		};
		this.validator = this.$element.bootstrapValidator({
			excluded: [".data-bv-excluded"],
			fields: validatorFields,
			live: 'enabled'
		}).data("bootstrapValidator");

		this.pageLevelViewModel.load(this.validator);
	};

	/**
	 * click on select specific records
	 */
	GenerateTripResourcesViewModel.prototype.selectRecordClick = function()
	{
		tf.modalManager.showModal(
			new TF.Modal.ListMoverSelectRecordControlModalViewModel(
				this.entity.selectedSpecificRecord(),
				$.extend(
					{},
					{
						title: "Select Records",
						description: "You may select one or more specific records to run the report against. At least one record must be selected.",
						availableTitle: 'Available',
						selectedTitle: 'Selected',
						mustSelect: true,
						gridOptions:
						{
							forceFitColumns: true,
							enableColumnReorder: true
						}
					},
					{
						type: 'trip',
						dataSource: this.selectedDataSourceId,
						showRemoveColumnButton: true,
						allowApplyZeroRecord: true
					})
			)
		).then(result =>
		{
			if (Array.isArray(result))
			{
				this.entity.selectedSpecificRecord(result);
				this.validator && this.validator.revalidateField('specificRecords');
			}
		});
	};

	GenerateTripResourcesViewModel.prototype.getAndSetFilterDataSource = function()
	{
		tf.promiseAjax.get(pathCombine(tf.api.apiPrefixWithoutDatabase(), "gridfilters"),
			{
				paramData: {
					"@filter": String.format("(eq(dbid, {0})|isnull(dbid,))&eq(datatypeId,{1})&eq(IsValid,true)&isnotnull(Name,)", tf.datasourceManager.databaseId, tf.dataTypeHelper.getId("trip")),
					"@sort": "Name"
				}
			})
			.then((data) =>
			{
				var filterList = data.Items;
				filterList = sortFilter(filterList);
				filterList.unshift(
					{
						Name: "",
						Id: undefined,
						WhereClause: ""
					});
				this.obFilterDataSource(filterList);
			});
	};

	GenerateTripResourcesViewModel.prototype.getTripByIds = function(tripIds)
	{
		return tf.promiseAjax.post(pathCombine(tf.api.apiPrefix(), "search", "trips"),
			{
				paramData:
				{
					getCount: false
				},
				data:
				{
					fields: ["Id", "Name"],
					idFilter:
					{
						IncludeOnly: tripIds,
						ExcludeAny: null
					}
				}
			}).then(response =>
			{
				return response.Items;
			});
	};

	GenerateTripResourcesViewModel.prototype.createRequestOptions = function()
	{
		var options = {
			DataSourceId: this.entity.selectedDataSource().id,
			CreateRecordsFor: 3, // 3 means date range
			OverwriteExisting: false,
			ApplySchoolCalendar: this.entity.applySchoolCalendar(),
			StartDate: toISOStringWithoutTimeZone(moment(this.entity.startDate())),
			EndDate: toISOStringWithoutTimeZone(moment(this.entity.endDate())),
			DayOfWeeks: [],
			FromAttendanceIntegration: false,
			AttendanceRecordSource: 1 // 1 means plus
		};
		[this.entity.sunday(),
		this.entity.monday(),
		this.entity.tuesday(),
		this.entity.wednesday(),
		this.entity.thursday(),
		this.entity.friday(),
		this.entity.saturday()].forEach((dayOfWeek, index) =>
		{
			if (dayOfWeek)
			{
				options.DayOfWeeks.push(index);
			}
		});

		if (this.entity.specifyRecordOption().id == specifyRecordOptions.Filter.id)
		{
			options.FilterId = this.entity.filter().Id;
		}
		if (this.entity.specifyRecordOption().id == specifyRecordOptions.SpecificRecords.id)
		{
			options.SpecifiedIds = this.entity.selectedSpecificRecord().map(x => x.Id);
		}
		return options;
	};

	GenerateTripResourcesViewModel.prototype.getOverlapExisting = async function(options)
	{
		var overlapTripHistories = await tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postTripHistoriesGenerateTripLogOverlapTripHistoriesUrl(), {
			paramData: {
				databaseId: this.entity.selectedDataSource().id,
			},
			data: options
		});
		if (overlapTripHistories.Items.length > 0)
		{
			var overlapMessage = "Trip Resources already exist within the selected date range for the following trips. Do you want to overwrite the existing records?<br/>"
				+ Enumerable.From(overlapTripHistories.Items).Select(function(c) { return c.TripName; }).Distinct().OrderBy().ToArray().join("<br/>");

			return tf.promiseBootbox.confirm(
				{
					buttons: {
						OK: {
							label: "Yes",
							className: "btn-primary btn-sm btn-primary-black"
						},
						Cancel: {
							label: "No",
							className: "btn-default btn-sm btn-default-link"
						}
					},
					title: "Confirm",
					message: "<div style='max-height:200px;'>" + overlapMessage + "</div>"
				});
		}

		return false;
	};

	GenerateTripResourcesViewModel.prototype.apply = function()
	{
		return this.pageLevelViewModel.saveValidate()
			.then(async (valid) =>
			{
				if (!valid) return false;

				var options = this.createRequestOptions();
				options.OverwriteExisting = await this.getOverlapExisting(options);
				return tf.promiseAjax.post(TF.Helper.ApiUrlHelper.postTripHistoriesGenerateTripLogUrl(), {
					paramData: {
						databaseId: this.entity.selectedDataSource().id,
					},
					data: options
				}).then((response) =>
				{
					tf.promiseBootbox.alert("<div style='max-height:200px;'>" + response.Items[0] + "</div>", "Result");
				});
			});
	};
})();