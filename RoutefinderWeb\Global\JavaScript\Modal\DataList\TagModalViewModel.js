(function()
{
	createNamespace('TF.Modal').TagModalViewModel = TagModalViewModel;
	function TagModalViewModel(entity, onSave, config)
	{
		TF.Modal.BaseModalViewModel.call(this);
		this.contentTemplate('modal/datalist/TagControl');
		this.buttonTemplate('modal/positivenegative');
		this.tagViewModel = new TF.Control.TagViewModel(entity);
		this.data(this.tagViewModel);
		this.sizeCss = "modal-dialog-sm";
		this.onSave = onSave;

		if (entity)
		{
			this.title(tf.applicationTerm.getApplicationTermSingularByName("Edit Tag"));
		}
		else
		{
			this.title(tf.applicationTerm.getApplicationTermSingularByName("Add Tag"));
			this.obSaveAndNewVisible(config?.saveAndNewVisible == false ? false : true); // default to true
			this.buttonTemplate('modal/positivenegativeextend');
		}
	}

	TagModalViewModel.prototype = Object.create(TF.Modal.BaseModalViewModel.prototype);
	TagModalViewModel.prototype.constructor = TagModalViewModel;

	TagModalViewModel.prototype.positiveClick = function()
	{
		tf.loadingIndicator.show();
		this.tagViewModel.apply().then(result =>
		{
			tf.loadingIndicator.tryHide();
			if (result)
			{
				this.positiveClose(result);
				this.onSave && this.onSave();
			}
		});
	};

	TagModalViewModel.prototype.negativeClose = function(returnData)
	{
		this.tagViewModel?.$form?.find("input").blur(); // to trigger the change event to update data model state
		if (this.tagViewModel.obEntityDataModel().apiIsDirty())
		{
			return tf.promiseBootbox.yesNo({ message: "You have unsaved changes.  Would you like to save your changes prior to closing?", backdrop: true, title: "Unsaved Changes", closeButton: true })
				.then(function(result)
				{
					if (result === true)
					{
						return this.positiveClick();
					}
					if (result === false)
					{
						return TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
					}
				}.bind(this));
		}
		else
		{
			TF.Modal.BaseModalViewModel.prototype.negativeClose.call(this, returnData);
		}
	};

	TagModalViewModel.prototype.saveAndNewClick = function()
	{
		this.tagViewModel.apply().then(function(result)
		{
			if (result)
			{
				this.tagViewModel.obEntityDataModel(new TF.DataModel.TagDataModel());
				if ((this.tagViewModel?.$form?.find("input[name=name]")?.length || 0) > 0)
				{
					this.tagViewModel.$form.find("input[name=name]").focus();
				}
				this.onSave && this.onSave();
			}
		}.bind(this));
	};

	TagModalViewModel.prototype.dispose = function()
	{
		this.tagViewModel.dispose();
	};
})();
