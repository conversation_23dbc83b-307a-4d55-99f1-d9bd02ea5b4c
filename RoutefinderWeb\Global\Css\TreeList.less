@import 'z-index';

.kendo-treelist {
	font-size: 14px;

	&.no-loading-mask {
		.k-loading-mask {
			display: none;
		}
	}

	tr {
		height: 33px;

		td {
			&.has-link {
				cursor: pointer;
				padding: 0;
			}

			>div {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			background-color: transparent;
		}

		td.k-detail-cell {
			>div {
				overflow: visible;
			}
		}
	}

	.k-grid-header {
		background: linear-gradient(#4b4b4b 0, #4b4b4b 33px, #d6d6d6 33px, #d6d6d6 100%);

		&.no-padding {
			padding: 0 !important;
		}

		th {
			.k-numeric-wrap {
				padding: 0 24px 0 0;

				.k-select {
					width: 24px;

					.k-link {
						.k-icon {
							float: none;
						}
					}
				}
			}

			&.k-header {
				vertical-align: middle;
				padding: 6px 7px 5px 7px;
				z-index: @grid-column-header-z-index;

				>.k-link {
					height: 20px;
					margin: -6px -7px -5px -7px;
					padding: 6px 7px 5px 7px;
					display: flex;
					justify-content: space-between;
					text-decoration: none;
				}
			}
		}

		.sort-icon-template() {
			table tr th {
				.k-filtercell {
					&>span {
						padding-right: 1px;

						.k-autocomplete,
						.k-numerictextbox,
						.tf-filter,
						.k-header {
							float: right;
							width: calc(~"100% - 1.4em");
							z-index: 0;
							border-bottom-width: 1px;

							&::-ms-clear {
								display: none;
							}
						}

						.k-dropdown-operator {
							width: 2em;
							right: inherit;
						}

						.k-button {
							margin-right: 0;
							margin-left: 0;
						}

						.k-datepicker,
						.k-numerictextbox {
							&+span+.k-button {
								right: 2em;
								border-radius: 0;
							}
						}

						.tf-filter {
							&+span+.k-button {
								right: 2em; //2.3em;
								border-radius: 0;
								border-right: 0;
							}
						}
					}

					.k-operator-hidden {

						.k-header,
						.k-autocomplete,
						.k-numerictextbox,
						.tf-filter,
						.k-header {
							width: 100%;
						}

						.k-button {
							right: 2em;
							border-radius: 0;
						}

						.icon-select-item {
							height: 20px;
							background-repeat: no-repeat;
							width: 100%;
							display: inline-block;
							background-size: 16px;
							background-position-y: center;
						}

						.k-dropdown {
							.k-dropdown-wrap {
								.k-select {
									border-left: 1px solid #c5c5c5;
								}
							}
						}
					}
				}

				.hide-cross-button {
					.k-button {
						display: none;

						&.clear-custom-filter-menu-btn {
							display: inline;
							vertical-align: top;
						}
					}
				}

				.k-header {
					a.k-link {
						.k-icon {

							&.k-i-arrow-n,
							&.k-i-arrow-60-up {
								border-bottom: 9px solid #FFFFFF;
								border-right: 5px solid transparent;
								border-left: 5px solid transparent;
								background-image: none;
								width: 0;
								height: 0;
								margin: 3.5px;
							}

							&.k-i-arrow-s,
							&.k-i-sort-desc-sm,
							&.k-i-sort-asc-sm {
								border-top: 9px solid #FFFFFF;
								border-right: 5px solid transparent;
								border-left: 5px solid transparent;
								background-image: none;
								width: 0;
								height: 0;
								margin: 3.5px;
							}
						}
					}
				}
			}
		}

		.k-grid-header-wrap {
			border-right-width: 0;
			.sort-icon-template;
		}

		.k-grid-header-locked {
			.sort-icon-template;
		}
	}

	.k-header {
		background: #4B4B4B;
		color: #cccccc;

		>.k-link {
			color: #cccccc;
		}
	}

	.k-pager-numbers {
		.k--selected {
			background-color: transparent;
			border-color: transparent;
			color: #222222;
			font-weight: bold;
		}
	}

	tbody>tr>td>.k-button,
	.on-demand-container .k-button {
		min-width: 16px;
		text-indent: -99999px;
		border: none;
		width: 16px;
		height: 16px;

		&:hover {
			opacity: 0.8;
		}
	}

	.k-button {
		@k-button-border-width: 0px;

		&:active:hover,
		&.k-active:hover {
			background-color: #ecf2f9;
		}

		&:focus:not(.k-disabled):not([disabled]) {
			/* move border and fix offset*/
			border: none;
			top: @k-button-border-width;
			right: @k-button-border-width;
			box-shadow: none;
		}
	}

	.k-button.k-grid-edit,
	.k-button.k-grid-copyandnew,
	.k-button.k-grid-delete {
		&:active:hover {
			background-color: transparent;
		}
	}

	.k-grid-edit,
	.k-grid-copyandnew,
	.k-grid-delete,
	.k-grid-setting,
	.k-grid-run,
	.unassign-student-icon,
	.open-trip-icon,
	.k-grid-approve {
		height: 16px !important;
		opacity: 0.6;
	}

	.k-button.k-button-disabled {
		pointer-events: none;
		opacity: .3;
	}

	.k-grid-export {
		background: url('../../global/img/grid/export.svg') no-repeat center center;
	}

	.k-grid-view {
		background: url('../../global/img/grid/view.png') no-repeat center center;
		cursor: pointer;
		opacity: 0.6;

		&.disable {
			opacity: 0.4;
			cursor: default;

			&:hover {
				opacity: 0.4;
			}
		}
	}

	.k-grid-download {
		background: url('../../global/img/grid/download.png') no-repeat center center;
		cursor: pointer;
		opacity: 0.6;

		&.disable {
			opacity: 0.4;
			cursor: default;

			&:hover {
				opacity: 0.4;
			}
		}
	}

	.k-grid-edit {
		background: url('../../global/img/grid/editor_pencil.png') no-repeat center center;
	}

	.k-grid-copyandnew {
		background: url('../../global/img/grid/Copy-and-New-5.png') no-repeat center center;
	}

	.k-grid-copyandnew2 {
		background: url('../../global/img/Routing Map/menuicon/Copy-black.png') no-repeat center center;
	}

	.k-grid-delete {
		background: url('../../global/img/menu/Delete-Black.svg') no-repeat center center;

		&.delete-relationship {
			background: url('../../Global/img/detail-screen/remove-association.png') no-repeat center center;
			cursor: pointer;
		}

		&.reportlib-remove {
			background-image: url('../../global/img/grid/reportlib-delete-black.png');
			cursor: pointer;
		}
	}

	.k-grid-run {
		background: url(../../global/img/grid/runnow.png) no-repeat center center;
	}

	.k-grid-setting {
		background: url('../../global/img/navigation-menu/data_source_black.svg') no-repeat center center;
		margin: 0 8px;
	}

	.k-grid-approve {
		background: url('../../global/img/menu/Approve-Black.png') no-repeat center center;
		background-size: 16px 16px;
	}

	.k-button.disable,
	.k-grid-edit.disable,
	.k-grid-copyandnew.disable,
	.k-grid-delete.disable,
	.k-grid-approve.disable {
		opacity: 0.3;

		&:hover {
			cursor: not-allowed;
			opacity: 0.3;
			background-color: transparent;
		}
	}

	&.kendo-grid-container {
		font-size: 0px;
		background-color: rgba(204, 204, 204, 0.19);
	}

	&.kendo-grid-container>div {
		font-size: 14px;
	}

	&.kendo-grid-container {
		table tr td span.hot-link {
			&:hover {
				text-decoration: underline;
				text-underline-offset: 3px;
				cursor: pointer;
			}
		}
	}

	&.kendo-grid-container .k-grid-header-wrap.k-auto-scrollable {
		background: linear-gradient(#4B4B4B 0, #4B4B4B 33, #D6D6D6 33, #D6D6D6 100%);
	}

	.k-grid-header-wrap th.k-header:last-child {
		border-right-width: 0;
	}

	.k-grid-pager {
		text-align: center;
		height: 24px;
		overflow: hidden;
		padding-top: 4px;
		padding-bottom: 4px;

		.k-pager-numbers {
			float: none;
		}

		>.k-pager-nav {
			float: none;
			width: 2em;
			display: inline-block;
		}

		.k-pager-sizes {
			float: left;
		}

		.k-pager-refresh {
			display: none;
		}
	}

	.k-pager>.k-link {
		border-radius: 0;
	}

	.k-pager-numbers .k-link {
		border-radius: 0;
	}

	.k-dropdown .k-input {
		background-color: #fff;

		&::before {
			content: '';
		}
	}

	.k-pager-info {
		margin-top: 1px;
		margin-bottom: 1px;
	}

	.input-group-addon {
		line-height: 12px;
	}

	.form-control {
		height: 26px;
	}
}