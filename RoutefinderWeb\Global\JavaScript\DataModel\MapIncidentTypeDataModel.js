(function()
{
	createNamespace("TF.DataModel").MapIncidentTypeDataModel = MapIncidentTypeDataModel;
	var arr = [];

	const _INCIDENT_TYPE_DEFINITION = {
		Columns: [
			{
				field: "Id",
				defaultValue: null,
				type: "number",
				unique: true,
				hidden: true
			},
			{
				field: "Name",
				defaultValue: "",
				type: "string",
				unique: true
			},
			{
				field: "SymbolType",
				defaultValue: 1,
				type: "number",
				hidden: true
			},
			{
				field: "Symbol",
				defaultValue: "<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8.15 8.16' ><title>Asset 35</title><g id='Layer_2' data-name='Layer 2'><g id='Layer_1-2' data-name='Layer 1'><path d='M8.15,4.08a4,4,0,0,1-.32,1.59A4.1,4.1,0,0,1,7,7a4,4,0,0,1-1.3.87,4.09,4.09,0,0,1-3.17,0A4.08,4.08,0,0,1,1.19,7a4.24,4.24,0,0,1-.87-1.3,4.09,4.09,0,0,1,0-3.17,4.17,4.17,0,0,1,.87-1.31A4.08,4.08,0,0,1,2.48.32a4.09,4.09,0,0,1,3.17,0A4.06,4.06,0,0,1,7.83,2.5,4,4,0,0,1,8.15,4.08Z'/></g></g></svg>",
				type: "string",
				hidden: true
			},
			{
				field: "SymbolColor",
				defaultValue: "#0000ff",
				type: "string",
				hidden: true
			},
			{
				field: "BackgroundColor",
				defaultValue: "#FFF",
				type: "string",
				hidden: true
			},
			{
				field: "BorderColor",
				defaultValue: "#333",
				type: "string",
				hidden: true
			},
			{
				field: "BorderSize",
				defaultValue: 2,
				type: "number",
				hidden: true
			},
			{
				field: "DisplayIcon",
				title: "Display Icon",
				defaultValue: "data:image/png;base64,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",
				width: "160px",
				type: "string",
				template: function(dataItem)
				{
					return "<img src='" + dataItem.DisplayIcon + "' style='width: 20px; height: 20px;' />";
				}
			},
			{
				field: "WayfinderReports",
				defaultValue: false,
				type: "boolean",
				hidden: true
			},
			{
				field: "NotificationType",
				defaultValue: 1,
				type: "number",
				hidden: true
			},
			{
				field: "NotificationDistance",
				defaultValue: null,
				type: "number",
				hidden: true
			},
			{
				field: "DisplayTime",
				defaultValue: null,
				type: "number",
				hidden: true
			},
			{
				field: "ExtendTime",
				defaultValue: null,
				type: "number",
				hidden: true
			},
			{
				field: "RemoveCount",
				defaultValue: null,
				type: "number",
				hidden: true
			},
			{
				field: "SnapToStreet",
				defaultValue: true,
				type: "boolean",
				hidden: true
			}
		]
	};

	const _INCIDENT_TYPE_CONFIG = {
		name: "IncidentType",
		singular: "Map Incident Type",
		plural: "Map Incident Types",
		value: "incidenttype",
		apiEndpoint: "mapincidenttypes",
		hasDBID: true,
		relationshipStr: "all",
		type: "",
		definition: _INCIDENT_TYPE_DEFINITION,
		editorContentTemplate: "Modal/EditMapIncidentType",
		editorViewModelType: "EditMapIncidentTypeViewModel"
	};

	function MapIncidentTypeDataModel(recordEntity)
	{
		var self = this,
			gridDefinition = _INCIDENT_TYPE_CONFIG.definition.Columns;

		arr.length = 0;
		gridDefinition.forEach(function(def)
		{
			if (def.unique)
			{
				self.uniqueField = def;
			}
			arr.push({
				from: def.mappingField || def.field,
				default: def.defaultValue
			});
		});
		TF.DataModel.BaseDataModel.call(this, recordEntity);
	}

	MapIncidentTypeDataModel.prototype = Object.create(TF.DataModel.BaseDataModel.prototype);
	MapIncidentTypeDataModel.prototype.mapping = arr;

	MapIncidentTypeDataModel.prototype.getIncidentTypeConfig = function()
	{
		return _INCIDENT_TYPE_CONFIG;
	};

})();