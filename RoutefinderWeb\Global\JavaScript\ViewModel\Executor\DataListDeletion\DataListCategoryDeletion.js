(function()
{
	var namespace = createNamespace("TF.Executor");

	namespace.DataListCategoryDeletion = DataListCategoryDeletion;

	function DataListCategoryDeletion()
	{
		this.type = 'category';
		this.deleteType = 'ID';
		this.deleteRecordName = 'Category';
		namespace.DataListBaseDeletion.apply(this, [true]);
	}

	DataListCategoryDeletion.prototype = Object.create(namespace.DataListBaseDeletion.prototype);
	DataListCategoryDeletion.prototype.constructor = DataListCategoryDeletion;

	DataListCategoryDeletion.prototype.getAssociatedData = function(ids)
	{
		//need a special deal with
		var associatedDatas = [];
		var p0 = tf.promiseAjax.get(pathCombine(tf.api.apiPrefix(), "vehiclecategories?categoryId=" + ids[0]))
			.then(function(response)
			{
				associatedDatas.push({
					type: 'vehicle',
					items: response.Items
				});
			});

		return Promise.all([p0]).then(function()
		{
			return associatedDatas;
		});
	};

	DataListCategoryDeletion.prototype.getEntityStatus = function()
	{
		return Promise.resolve({ Items: [{ Status: "" }] });
	};

	DataListCategoryDeletion.prototype.publishData = function(ids)
	{
		PubSub.publish(topicCombine(pb.DATA_CHANGE, "category", pb.DELETE), ids);
	};

})();