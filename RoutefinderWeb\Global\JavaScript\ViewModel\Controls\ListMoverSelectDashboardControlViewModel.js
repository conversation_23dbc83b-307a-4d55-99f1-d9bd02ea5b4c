(function()
{
	createNamespace('TF.Control').ListMoverSelectDashboardControlViewModel = ListMoverSelectDashboardControlViewModel;

	function ListMoverSelectDashboardControlViewModel(selectedData, options)
	{
		if (options.columnSources)
		{
			this.columnSources = options.columnSources;
		}
		TF.Control.KendoListMoverWithSearchControlViewModel.call(this, selectedData, options);
	}

	ListMoverSelectDashboardControlViewModel.prototype = Object.create(TF.Control.KendoListMoverWithSearchControlViewModel.prototype);
	ListMoverSelectDashboardControlViewModel.prototype.constructor = ListMoverSelectDashboardControlViewModel;

	ListMoverSelectDashboardControlViewModel.prototype.columnSources = {
		scheduledDashboard: [
			{
				FieldName: "Name",
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName("Name"),
				Width: "150px",
				type: "string",
				isSortItem: true
			},
			{
				FieldName: "Description",
				DisplayName: tf.applicationTerm.getApplicationTermSingularByName("Description"),
				Width: "150px",
				type: "string"
			}]
	};

	ListMoverSelectDashboardControlViewModel.prototype.initGridScrollBar = function(container)
	{
		var $gridContent = container.find(".k-grid-content");
		$gridContent.css(
			{
				"overflow-y": "auto"
			});


		if ($gridContent[0].clientHeight == $gridContent[0].scrollHeight)
		{
			$gridContent.find("colgroup col:last").css(
				{
					width: 137
				});
		}
		else
		{
			$gridContent.find("colgroup col:last").css(
				{
					width: 120
				});
		}
	};

	ListMoverSelectDashboardControlViewModel.prototype.onLeftGridChange = function(e, rowsData)
	{
		var isDisableRow = false;
		var $selectRows = this.leftSearchGrid.kendoGrid.select();
		$selectRows.map(function(idx, row)
		{
			var $row = $(row);
			if ($row.hasClass("disable"))
			{
				$row.removeClass(TF.KendoClasses.STATE.SELECTED);
				isDisableRow = true;
			}
		}.bind(this));

		if (isDisableRow)
			this._clearLeftSelection();
		else
			this._obLeftSelData(rowsData);

		if (this._obLeftSelData().length > 0)
			this._clearRightSelection();
	};

	ListMoverSelectDashboardControlViewModel.prototype.apply = function()
	{
		return TF.Control.KendoListMoverWithSearchControlViewModel.prototype.apply.call(this).then(function(selectedData)
		{
			return selectedData;
		});
	};

	ListMoverSelectDashboardControlViewModel.prototype.cancel = function()
	{
		return new Promise(function(resolve, reject)
		{
			if (!isArraySame(this.oldData, this.selectedData))
			{
				return tf.promiseBootbox.yesNo("You have made changes to the " + (this.options.typeDisplayName || "dashboards") + ".  Are you sure you want to discard them?", "Confirmation Message").then(function(result)
				{
					if (result)
					{
						resolve(true);
					}
				});
			}
			else
			{
				resolve(true);
			}
		}.bind(this));
	};

	function isArraySame(oldData, newData)
	{
		if (newData.length != oldData.length)
		{
			return false;
		}
		var oldIds = oldData.map(function(item)
		{
			return item.Id;
		});
		var newIds = newData.map(function(item)
		{
			return item.Id;
		});
		var diffData1 = Enumerable.From(newIds).Where(function(x)
		{
			return !Array.contain(oldIds, x);
		}).ToArray();
		var diffData2 = Enumerable.From(oldIds).Where(function(x)
		{
			return !Array.contain(newIds, x);
		}).ToArray();
		return diffData1.length == 0 && diffData2.length == 0;
	}
})();
