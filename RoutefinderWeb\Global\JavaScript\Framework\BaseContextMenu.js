﻿(function()
{
	var namespace = createNamespace("TF.ContextMenu");
	namespace.BaseContextMenu = BaseContextMenu;

	function BaseContextMenu()
	{
		this.disposed = false;
		this._timer = null;
		this._$container = null;
		this._target = null;
	}

	BaseContextMenu.prototype.constructor = BaseContextMenu;

	// this method needs to be called in subclass
	BaseContextMenu.prototype.createContainer = function($wrapper, target)
	{
		var $target = $(target);
		this._target = $target;

		if ($target.is(":hidden") && tf.isViewfinder)
		{
			$target.parents(".grid-icons").find(".iconrow").show().css("width", "auto");
			$target.parents(".grid-icons").find(".grid-staterow-wrap").hide();
		}

		var $container = $('<div></div>');
		this._$container = $container;
		var self = this;

		$container.on("contextMenuClose", function()
		{
			// $container.hide();
			self.dispose();
			if (tf.pageManager) // view-852,this is only used in ViewfinderWeb
			{
				tf.pageManager.obContextMenuVisible(false);
			}
		});

		if (this.isElementTarget($target[0]))
		{
			var $handlePlaceholder = $('<div></div>');
			this.handleWidth = $target.outerWidth();
			this.handleHeight = $target.outerHeight();
			$handlePlaceholder.css(
				{
					width: this.handleWidth,
					height: this.handleHeight,
					position: "absolute"
				});
			$handlePlaceholder.appendTo($container);
			$handlePlaceholder.addClass("contextmenu-overlay");
			if ($handlePlaceholder.width() === 0)
			{
				// VIEW-1343 Right-Click menu does not close when navigating away from it
				$handlePlaceholder.css(
					{
						width: 40,
						height: 20,
						marginLeft: -20
					});
			}
			var offset = $target.offset();
			$container.css(
				{
					position: "absolute",
					left: offset.left - 1,
					top: offset.top
				});
			$target.addClass("contextmenu-open");

			$(window).on("resize.contextmenu", function()
			{
				if (TF.isPhoneDevice)
				{
					return;
				}
				setTimeout(function()
				{
					var offset = $target.offset();
					$container.css(
						{
							position: "absolute",
							left: offset.left - 1,
							top: offset.top
						});
				}, 10);
			}.bind(this));
		}
		else
		{
			$container.css(
				{
					position: "absolute",
					left: $target[0].left - 1,
					top: $target[0].top
				});
		}
		$container.appendTo($wrapper);

		$("body").on("mousemove.contextmenu", function(e)
		{
			var target = $(e.target);
			clearTimeout(self._timer);
			if (target.closest($container).length == 0 && target.closest($target).length == 0 && target.closest('.mobile-alert').length === 0 && target.closest("#loadingindicator").length === 0)
			{
				self._timer = setTimeout(function()
				{
					self.dispose();
					if (tf.pageManager) // view-852,this is only used in ViewfinderWeb
					{
						tf.pageManager.obContextMenuVisible(false);
					}
				}, 100); // original 300ms might cause menu stuck if user moves very fast. Decrease to 100ms to prevent this issue.
			}
		});

		return $container;
	};

	// this method needs to be called in subclass to set position properly
	BaseContextMenu.prototype.setMenuPosition = function($menuContainer, $target)
	{
		$menuContainer = $($menuContainer);
		$target = $($target);
		var leftDiff, topSpace, bottomSpace, isBottom, offset, handleAddition;
		var screenHeight = $(window).height();
		var screenWidth = $(window).width();
		if (this.isElementTarget($target[0]))
		{
			handleAddition = this.handleHeight;
			offset = $target.offset();
		}
		else
		{
			handleAddition = 0;
			offset = $target[0];
		}
		leftDiff = offset.left + $menuContainer.outerWidth() - screenWidth;
		bottomSpace = screenHeight - offset.top - $menuContainer.outerHeight() - handleAddition;
		topSpace = offset.top - $menuContainer.outerHeight();
		isBottom = bottomSpace > topSpace;
		var topx;
		if (isBottom)
		{
			topx = 0 + this.handleHeight;
			if (bottomSpace < 0)
			{
				$menuContainer.find(".grid-menu").css('max-height', screenHeight - offset.top);
			}
		}
		else
		{
			topx = $menuContainer.outerHeight() > offset.top ? offset.top : $menuContainer.outerHeight();
			$menuContainer.find(".grid-menu").css('max-height', topx);
			topx = -topx + 1;
		}
		//for viewfinder mobile	
		if (tf && tf.isViewfinder && TF.isPhoneDevice)
		{
			var left;
			if (leftDiff < 0)
			{
				left = 0;
			}
			else
			{
				if (offset.left < Math.abs(-$menuContainer.outerWidth() + $target.outerWidth()))
				{
					left = -offset.left;
					if (screenWidth < $menuContainer.outerWidth())
					{
						$menuContainer.width(screenWidth);
					}
				}
				else
				{
					left = -$menuContainer.outerWidth() + $target.outerWidth();
				}
			}
			$menuContainer.css(
				{
					position: "absolute",
					left: left,
					top: topx
				});
		} else
		{
			$menuContainer.css(
				{
					// this messes with submenu, didn't find a solution yet
					// overflowY: "auto",
					// overflowX: "visible",
					// maxHeight: isBottom ? screenHeight - this.handleHeight - offset.top : offset.top,
					position: "absolute",
					left: leftDiff < 0 ? 0 : -$menuContainer.outerWidth() + $target.outerWidth(),
					top: topx
				});
		}
		$menuContainer.show();
	};

	BaseContextMenu.prototype.isElementTarget = function($target)
	{
		return !($target.hasOwnProperty("top") && $target.hasOwnProperty("left"));
	};

	BaseContextMenu.prototype.dispose = function()
	{
		this.disposed = true;
		clearTimeout(this._timer);
		this._$container.remove();
		this._target.removeClass("contextmenu-open");
		$(window).off("resize.contextmenu");
		$("body").off("mousemove.contextmenu");
		if (tf.isViewfinder && $(".main-body .grid-icons").hasClass("short-length"))
		{
			$(".main-body .grid-icons .iconrow").hide();
			$(".main-body .grid-icons .grid-staterow-wrap").show().css("width", "auto");
		}
	};

	BaseContextMenu.prototype.render = function()
	{
		throw "render needs to be implemented in subclass of BaseContextMenu";
	};
})();
