﻿.navcontainer
{
	min-width: 1024px;
}

.navbar .navbar-brand
{
	padding-top: 4px;
	cursor:pointer;
}

.navbar .navbar-btn
{
	margin-top: 11px;
}

.navbar-nav > li > .dropdown-menu
{
	border-top: 4px solid #db4d37;
	padding-bottom: 0px;
}

.navbar .dropdown-menu > .dropdown-header
{
	color: inherit;
	font-weight: 900;
	padding-top: 15px;
	margin-bottom: 5px;
}

.navbar .dropdown-menu > .dropdown-header > .subheader
{
	color: #ccc;
	font-weight: normal;
	font-size: 11px;
}

.navbar .dropdown-menu > li > a 
{
	font-size: 11px;
}

.navbar .dropdown-menu > li.<PERSON>uFooter
{
	margin-top: 20px;
}

.navbar .dropdown-menu > li.MenuFooter.Section
{
	background-color: #6b6b6b;
	color: #fff;
	padding-top: 10px;
	margin-bottom: 0px;
	padding-bottom: 10px;
}

.navbar .dropdown-menu
{
	padding-bottom: 0px;
}

.navbar .SearchBox
{
	margin-top: 10px;
	margin-bottom: 0px;
}

.navbar .SearchBox .input-group-btn:first-child>.btn
{
	margin-left: 0;
	margin-right: 0;
}

.navbar .SearchBox .btn.ToggleButton
{
	background-color: #4C4C4C;
	border-color: #4C4C4C;
	border-radius: 0;
	border: 0;
	padding: 6px;
}

.navbar .SearchBox .btn.ToggleButton.Active
{
	background-color: #FFF;
	border-color: #FFF;
}

.navbar .SearchBox button.btn.SearchButton
{
	background-color: #DB4D37;
	border: 1px solid #DB4D37;
	border-radius: 0;
	color: #FFF;
	height: 28px;
	line-height:10px;
}

.navbar .SearchBox input.form-control
{
	background-color: #D7D7D7;
	border-color: #D7D7D7;
	height: 28px;
}

.navbar .divider-vertical
{
	border-right: 1px solid #ccc;
	height: 22px;
	margin-right: 6px;
	margin-top: 14px;
}

.navbar .dropdown > .dropdown-toggle.UserProfileImage
{
	padding: 2px 2px 6px 2px;
}

.navbar .UserProfileImage > img
{
	border-radius: 5px;
	display: block;
    height: auto;
	margin: 6px 8px 0px 8px;
	max-width: 36px;
    max-height: 36px;
    width: auto;
}
