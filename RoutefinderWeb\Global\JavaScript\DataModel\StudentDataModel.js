﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.StudentDataModel = function(studentEntity)
	{
		namespace.BaseDataModel.call(this, studentEntity);
	}

	namespace.StudentDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.StudentDataModel.prototype.constructor = namespace.StudentDataModel;

	namespace.StudentDataModel.prototype.mapping = [
		{ "from": "AidEligible", "default": false },
		{ "from": "AideReq", "default": false },
		{ "from": "AttendanceSchoolName", "default": null },
		{ "from": "Cohort", "default": "" },
		{ "from": "Comments", "default": "" },
		{ "from": "DBID", "default": function() { return tf.datasourceManager.databaseId; } },
		{ "from": "DBINFO", "default": null },
		{ "from": "DisabilityCodeIds", "default": [] },
		{ "from": "DisabilityCodes", "default": null },
		{ "from": "Disabled", "default": false },
		{ "from": "DisplayGender", "default": null },
		{ "from": "District", "default": "" },
		{ "from": "DistrictId", "default": null },
		{ "from": "Dob", "default": null },
		{ "from": "EntryDate", "default": null },
		{ "from": "StudentRequirements", "default": [] },
		{ "from": "EthnicCodeIds", "default": [] },
		{ "from": "EthnicCodes", "default": null },
		{ "from": "FirstName", "default": "" },
		{ "from": "GeoCity", "default": "" },
		{ "from": "GeoConfidence", "default": 1 },
		{ "from": "GeoCounty", "default": "" },
		{ "from": "GeoStreet", "default": "" },
		{ "from": "GeoZip", "default": "" },
		{ "from": "Grade", "default": "" },
		{ "from": "GradeId", "default": null },
		{ "from": "Guid", "default": "" },
		{ "from": "Id", "default": 0 },
		{ "from": "ImageBase64", "default": null },
		{ "from": "InActive", "default": false },
		{ "from": "IntGratChar1", "default": "" },
		{ "from": "IntGratChar2", "default": "" },
		{ "from": "IntGratDate1", "default": null },
		{ "from": "IntGratDate2", "default": null },
		{ "from": "IntGratNum1", "default": 0 },
		{ "from": "IntGratNum2", "default": 0 },
		{ "from": "LastName", "default": "" },
		{ "from": "LastUpdated", "default": "1970-01-01T00:00:00" },
		{ "from": "LastUpdatedId", "default": 0 },
		{ "from": "LastUpdatedType", "default": 0 },
		{ "from": "LoadTime", "default": 0 },
		{ "from": "LoadTimeManuallyChanged", "default": false },
		{ "from": "LocalId", "default": "" },
		{ "from": "MailCity", "default": function() { return tf.setting.userProfile.MailCityName; } },
		{ "from": "MailCityId", "default": function() { return tf.setting.userProfile.MailCity; } },
		{ "from": "MailState", "default": function() { return tf.setting.userProfile.MailStateName; } },
		{ "from": "MailStateId", "default": function() { return tf.setting.userProfile.MailState; } },
		{ "from": "MailStreet1", "default": "" },
		{ "from": "MailStreet2", "default": "" },
		{ "from": "MailZip", "default": function() { return tf.setting.userProfile.MailZipName; } },
		{ "from": "MailZipId", "default": function() { return tf.setting.userProfile.MailPostalCode; } },
		{ "from": "Mi", "default": "" },
		{ "from": "DistanceFromResidSch", "default": null },
		{ "from": "DistanceFromSchl", "default": 0 },
		{ "from": "Name", "default": null },
		{ "from": "PreRedistSchool", "default": "" },
		{ "from": "Priorschool", "default": "" },
		{ "from": "ProhibitCross", "default": false },
		{ "from": "ResidSchool", "default": "" },
		{ "from": "ResidSchoolName", "default": null },
		{ "from": "ResidenceSchoolName", "default": null },
		{ "from": "SchoolCode", "default": "" },
		{ "from": "SchoolName", "default": null },
		{ "from": "Gender", "default": "" },
		{ "from": "Transported", "default": false },
		{ "from": "TripStopId", "default": null },
		{ "from": "UserDefinedFields", "default": null },
		{ "from": "DocumentRelationships", "default": null },
		{ "from": "Xcoord", "default": 0 },
		{ "from": "Ycoord", "default": 0 },
		{ "from": "RecordPicture", "default": null },
		{ "from": "PopulationRegionName", "default": null },
		{ "from": "PopulationRegionID", "default": null }
	];

})()

