﻿(function()
{
	var namespace = window.createNamespace("TF.DataModel");

	namespace.DisabilityCodeDataModel = function(disabilityCodeEntity)
	{
		namespace.BaseDataModel.call(this, disabilityCodeEntity);
	}

	namespace.DisabilityCodeDataModel.prototype = Object.create(namespace.BaseDataModel.prototype);

	namespace.DisabilityCodeDataModel.prototype.constructor = namespace.DisabilityCodeDataModel;

	namespace.DisabilityCodeDataModel.prototype.mapping = [
		{ from: "ObjectId", default: 0 },
		{ from: "Id", default: 0 },
		{ from: "DBID", default: 0 },
		{ from: "Code", default: "" },
		{ from: "Comments", default: "" },
		{ from: "Description", default: "" },
		{ from: "LoadTimePerStudent", default: 0 }
	];
})();