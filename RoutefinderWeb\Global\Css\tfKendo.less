.k-input {
	margin: 0;
	padding: 0;
	width: 100%;
	min-width: 0;
	box-sizing: border-box;
	border-width: 1px;
	border-style: solid;
	outline: 0;
	font-weight: normal;
	text-align: start;
	box-shadow: none;
	display: inline-flex;
	flex-flow: row nowrap;
	align-items: stretch;
	vertical-align: middle;
	position: relative;
	overflow: hidden;
	text-overflow: ellipsis;

	-webkit-appearance: none;

	.k-input-inner[type=text] {
		border: 0;
		padding: .177em 0;
		height: 1.65em;
		text-indent: .33em;
	}

	.k-input-spinner {
		width: calc(var(--kendo-line-height, normal)* 1em + var(--kendo-spacing-1, .25rem)* 2);
	}

	&.k-numerictextbox {
		padding: 0;

		&.k-hover,
		&.k-focus {
			box-shadow: none;
			border-color: #d5d5d5;
		}

		input.borderThickness {
			width: 100%;
		}

		.k-icon {
			padding: 0;
			height: auto;
		}

		.k-input-inner {
			background-color: #fff;
			width: calc(100% - var(--kendo-line-height, normal)* 1em - var(--kendo-spacing-1, .25rem)* 2);
		}

		&.no-arrows {
			.k-input-inner {
				width: 100%;
			}
		}

		.k-input-spinner {
			position: absolute;
			right: 0;
			top: 0;
			z-index: 5;

			button {
				min-height: 11px;
			}
		}
	}
}

.colorPickerContainer {
	.k-colorpicker {
		height: 22px;
	}
}

.k-colorpicker,
.k-picker-md.k-colorpicker,
.form-group .k-colorpicker {
	height: 22px;

	.k-input-inner {
		padding: unset;
		width: 100%;
		height: 100%;

		.k-color-preview {
			cursor: pointer;
			border-radius: 0;
		}
	}

	.k-input-button {
		opacity: 0;
	}
}

.grid-color {
	.k-colorpicker {
		height: 20px;
	}
}

.k-dropdownlist {
	border-radius: 0;
	background-color: #fff;
	margin: 0;
	padding: 0;

	&.k-picker-solid {

		&:hover,
		&.k-hover {
			background-color: #fff;
		}
	}

	&.k-hover {

		.k-input-inner,
		.k-input-button {
			background-color: #fff;
		}
	}

	.k-input-inner {
		margin-right: 20px;
		height: 20px;
		line-height: 20px;
		font-family: "SourceSansPro-Regular", Arial;
		font-size: 14px;
		background-color: #fff;
		width: calc(100% - 20px);
	}

	.k-input-button {
		z-index: 1;

		&:hover {
			border-width: 0;
		}

		height: 24px;
		line-height: 24px;
		min-height: 24px;
		box-sizing: border-box;
		width: 22px;
		position: absolute;
		top: 0;
		right: 0;

		.k-svg-icon {
			height: 16px;
			width: 16px;
			cursor: pointer;
			display: table;
			min-height: 16px;
		}
	}
}

.k-add-new-option {
	color: #007bff;
	cursor: pointer;
}

.user-profile {
	.k-dropdownlist {
		.k-input-inner {
			height: 20px;
			line-height: 20px;
			font-size: 12px;
		}
	}
}

.Edit-UDF-Modal {
	.form-group .k-dropdowntree {
		height: 100%;
	}

	.form-group .k-dropdownlist .k-input-inner,
	.form-group .k-dropdowntree .k-input-inner {
		height: 20px;
		line-height: 20px;
	}

	.form-group .k-dropdownlist .k-input-button,
	.form-group .k-dropdowntree .k-input-button {
		height: 20px;
		min-height: 20px;
		background-color: #eee;
		line-height: 20px;
		border-width: 0;
		border-left: solid 1px #bfbfbf;

		.k-svg-icon {
			display: inline-block;
			width: 0;
			height: 0;
			min-height: 0;
			vertical-align: middle;
			border-top: 4px solid;
			border-right: 4px solid transparent;
			border-left: 4px solid transparent;
		}
	}

	.tabstrip-group-udf .form-group {

		.k-dropdownlist .k-input-button,
		.k-dropdownlist .k-input-inner,
		.k-dropdowntree .k-input-inner,
		.k-dropdowntree .k-input-button {
			height: 24px;
			line-height: 24px;
		}
	}
}

.k-dropdowntree {
	background-image: none;
	background-position: 50% 50%;
	background-color: #fff;
	border-color: #bfbfbf;
	height: 22px;
	padding-right: 20px;
	box-sizing: border-box;
	border-width: 1px;
	border-radius: 0;
	color: #333;

	&.k-hover,
	&.k-focus {
		box-shadow: none;
		border-color: #d5d5d5;
	}

	&.k-picker-solid {

		&:hover,
		&.k-hover {
			background-color: #fff;
		}
	}

	.k-clear-value {
		position: absolute;
		top: 3px;
		right: calc(3em - 8px);
		z-index: 1;
	}

	.k-input-inner {
		margin-right: 20px;
		height: 20px;
		line-height: 20px;
		font-family: "SourceSansPro-Regular", Arial;
		font-size: 14px;
		padding: 2px 8px;
		background-color: #fff;
		width: calc(100% - 20px);
	}

	.k-input-button {
		z-index: 1;

		&:hover,
		&:focus {
			box-shadow: none;
			border-color: #bfbfbf !important;
		}

		&:active {
			color: #333;
		}

		box-sizing: border-box;
		width: 22px;
		position: absolute;
		top: 0;
		right: 0;
		height: 20px;
		min-height: 20px;
		background-color: #eee;
		line-height: 20px;
		border-width: 0;
		border-left: solid 1px #bfbfbf;

		.k-svg-icon.k-button-icon {
			display: inline-block;
			width: 0;
			height: 0;
			min-height: 0;
			vertical-align: middle;
			border-top: 4px solid;
			border-right: 4px solid transparent;
			border-left: 4px solid transparent;
		}
	}
}

.k-animation-container {
	.k-child-animation-container {
		width: auto;
		height: auto;

		.k-popup {
			ul[data-kendo-role="contextmenu"] {
				.k-menu-item {
					.k-menu-link {
						.k-menu-link-text {
							display: inline-flex;
						}
					}
				}
			}
		}

		form.k-filter-menu.k-popup {
			margin-left: 2px;
			background-color: #EEEEEE;

			.k-filter-menu-container {
				& .k-dropdownlist {
					width: calc(100% - 2px);

					&.k-hover {
						.k-input-inner {
							background-color: #bdb4af;
						}
					}

					& .k-input-inner {
						background-color: #ececec;
						height: 20px;
						padding-right: 20px;
						box-sizing: border-box;

						.k-input-value-text {
							height: 20px;
							line-height: 20px;
							padding: 0px;
							border-radius: 0;
							color: #333;
						}
					}

					&.k-hover {
						border-color: #B6B6B6;
					}

					&.k-hover .k-input-button,
					&.k-focus .k-input-button {
						border-color: #b6b6b6;
						background-color: #b6b6b6;
						background-image: none;
					}

					& .k-input-button:hover {
						border-width: 1px;
						border-top-width: 0;
						border-right-width: 0;
					}

					& .k-input-button {
						width: 22px;
						border-left-style: solid;
						border-left-width: 1px;
						//border-right-color: #cccccc;
						border-left-color: #cccccc;
						min-height: 21px;
						height: 21px;
						border-bottom-width: 1px;
						border-bottom-style: solid;
						background-color: #EEEEEE;
						cursor: pointer;
						border-top-width: 0;
						border-right-width: 0;
					}
				}

				& .k-textbox {
					margin: 0
				}

				& span.k-filter-and {
					margin: 0;
					width: 6em;
				}

				& .k-numerictextbox {
					width: 100%;
				}

				& .k-actions-stretched .k-button.k-button-solid-primary {
					background-color: #333333;
					border-color: #444444;
					outline: none !important;
					color: #fff;
				}
			}

		}
	}

	.k-multiselect-popup,
	.k-combobox-popup {

		.k-list-item {
			font-size: 14px;

			&.k-selected {
				color: #2e2e2e;
			}
		}

		.k-list-md {
			font-size: 14px;

			.k-list-item {
				&.k-focus {
					box-shadow: none;
				}

				&.k-selected {
					&.k-hover {
						background-color: #FFFFCC;
					}

					border-color: #007cc0;
					padding: 0 4px;
					border-width: 1px;
					border-style: solid;
				}
			}
		}

		.k-virtual-list>.k-virtual-content {
			position: relative;
		}
	}

	.k-list-container.k-multiselect-popup {
		height: auto !important;

		.k-list {
			height: auto !important;
			max-height: 200px;
		}
	}

	.k-dropdownlist-popup {
		font-size: 14px !important;

		.k-virtual-list>.k-virtual-content {
			position: relative;
		}

		.k-list-md {
			font-size: unset;
		}

		.k-list-ul {
			>li {
				&.k-hover {
					background-color: #eee;
				}

				line-height: 1.8em;
				min-height: 1.8em;
				border-width: 1px;
				border-style: solid;
				border-color: transparent;
				padding: 0 4px;
				border-radius: 0;
				font-family: "SourceSansPro-Regular",
				Arial;
				font-size: 100%;
				font-stretch: 100%;
				font-style: normal;
				font-weight: 400;

				.k-list-item-text {
					text-overflow: ellipsis;
					overflow: hidden;
					text-wrap: nowrap;
				}
			}

			>li.k-focus {
				background-color: #ffc;
				border-color: #007cc0;
				background: #ffc;
				color: #222;
				box-shadow: none;
			}
		}
	}

	.k-calendar-container {
		border-radius: 0;
		box-shadow: none;

		.k-calendar {
			font-size: 14px;
		}

		.k-header {

			.k-calendar-nav-prev,
			.k-calendar-nav-next {
				width: 1.42857143em;
				height: 1.42857143em;
				box-sizing: content-box;
			}

			.k-calendar-nav-fast {
				font-weight: bold;
				border-radius: 4px;

				&::before {
					background: #333;
				}
			}
		}

		.k-calendar-footer .k-calendar-nav-today,
		.k-calendar-nav-fast {
			opacity: 1;
			color: #D0503C;
		}

		.k-calendar-view {
			padding: 0;
			font-size: 14px;

			.k-calendar-table {
				width: 100%;
				height: auto;

				th {
					color: #333;
					font-size: 14px;
					height: 2.42857143em;
					padding: .25em 2px;
					border-bottom-width: 1px;
					box-sizing: border-box;
					background-color: #fff;
					border-color: #bfbfbf;
				}

				.k-today {
					>.k-link {
						background-color: #ccc;
						font-weight: 400;
					}
				}

				.k-selected {
					>.k-link {
						background-color: #007cc0;
						color: #fff;
					}
				}

				.k-calendar-td.k-focus .k-link {
					box-shadow: inset 0 0 0 1px #007cc0
				}
			}
		}

		.k-calendar-footer {
			padding: 0;

			.k-calendar-nav-today {
				&:hover {
					text-decoration: underline;
				}

				text-transform: uppercase;

				&::before {
					background: none;
				}
			}
		}
	}

	.k-dropdowntree-popup {
		font-size: 14px !important;

		.k-treeview-md {
			font-size: 14px;
			line-height: 1.3333em;
			overflow-x: hidden;
			padding: 0;

			.k-treeview-item {
				&.form-item {
					cursor: pointer;

					>.k-treeview-mid,
					>.k-treeview-top {
						.k-treeview-toggle .k-icon {
							cursor: pointer;
						}

						.k-treeview-leaf {
							cursor: pointer;

							.k-treeview-leaf-text {
								cursor: pointer;
								text-overflow: ellipsis;
								overflow: hidden;
								text-wrap: nowrap;
							}
						}
					}
				}

				.k-treeview-leaf {
					padding-left: 0px;
					width: 100%;

					&.k-focus,
					&.k-hover {
						box-shadow: none;
					}

					&.k-focus.k-hover {
						background-color: #eaeaea;
					}

					&.k-focus {
						background-color: unset;
					}

					&.k-selected {
						color: #2e2e2e;
						background-color: #FFFFCC;
					}

					.k-treeview-leaf-text {
						text-overflow: ellipsis;
						overflow: hidden;
						text-wrap: nowrap;
					}
				}

				&.show-as-dropdown {
					.k-treeview-leaf {
						padding-left: 7px;
					}
				}
			}
		}
	}

	.k-colorpicker-popup {
		.k-colorgradient-inputs {

			.k-input.k-textbox {
				display: block;
				border: solid 1px #ccc !important;
				width: 100px;

				.k-input-inner {
					border-left: solid 1px #ccc !important;
					width: 75px !important;
					float: right;
					height: 20px;
				}
			}

			.k-colorgradient-input-label {
				text-transform: none;
			}
		}
	}

	.k-ct-popup {
		box-sizing: content-box;

		.k-ct-cell {
			box-sizing: content-box;
		}
	}

	.k-tooltip {
		font-size: 14px;
	}
}

.k-calendar-container {
	min-width: 238px;

	.k-calendar .k-calendar-monthview {
		padding: 0;
	}
}

.k-svg-icon {
	width: 16px;
	height: 16px;
	outline: 0;
	line-height: 1;
	display: inline-flex;
	flex-flow: row nowrap;
	align-items: center;
	justify-content: center;
	vertical-align: middle;
	position: relative;
	padding: 0;
}

html body .k-grid tbody .k-svg-icon {
	width: 16px;
}

.k-grid-edit-command.k-button {
	padding: 0;
	background: transparent;

	.k-svg-i-pencil svg {
		fill: #666666;
	}

	.k-button-text {
		display: none;
	}
}

.list-mover-mover .kendo-grid .k-grid-header-wrap.k-auto-scrollable,
.list-mover-mover .k-grid-header {
	background: linear-gradient(#4b4b4b 0, #4b4b4b 33px, #d6d6d6 33px, #d6d6d6 100%);
}

.eta-tree-view,
.routing-tree-view {
	li.k-treeview-item {
		padding: 0 0 0 15px;

		.icon.close-current-item {
			display: none;
		}

		.k-treeview-top,
		.k-treeview-mid,
		.k-treeview-bot {
			display: block;

			.k-treeview-leaf:hover {
				background-color: inherit;
				color: inherit;
				border-color: inherit;
			}
		}
	}
}

.k-drag-clue {
	display: block;

	.k-drag-status {
		text-indent: -99999px;
		overflow: hidden;
		position: relative;
		display: inline-block;
		overflow: hidden;
		width: 1em;
		height: 1em;
		text-align: center;
		vertical-align: middle;
		background-image: none;
		font: 16px / 1 WebComponentsIcons;
		speak: none;
		font-variant: normal;
		text-transform: none;
		text-indent: 0;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		color: inherit;
	}
}

.k-datepicker,
.form-control.k-datepicker {
	overflow: visible;
	display: flex;
	box-shadow: none;
	border-color: #bfbfbf;

	&.k-hover {
		border-color: #d5d5d5;
	}

	.k-input-inner {
		font-size: 14px;
		box-shadow: none;
		height: 20px;
		min-height: 20px;
		padding: 2px 8px;
		box-sizing: border-box;
		color: #333;
		text-indent: 0
	}

	.k-input-button {
		cursor: pointer;
		width: 22px;
		height: 21px;
		line-height: 21px;
		min-height: 21px;
		box-sizing: border-box;
		padding: unset;
		border-style: solid;
		border-width: 0 0 0 1px;
		border-color: inherit;
		background-color: #eee;
		border-bottom-width: 1px;

		&:active {
			color: #333;
			border-color: #b6b6b6;
		}

		&:hover {
			border-color: #b6b6b6;
		}

		.k-svg-i-calendar {
			padding: unset;
		}

		&:focus {
			box-shadow: none;
		}
	}
}

.form-container {
	.form-layer {
		.form {
			.form-body {
				.form-base-container {
					.k-datepicker {
						.k-input-button {
							height: 29px;
							line-height: 29px;
							width: 2em;
							background-color: #fff;
						}
					}

					.k-multiselect.k-focus,
					.k-multiselect.k-hover,
					.k-combobox.k-focus,
					.k-combobox.k-hover,
					.k-datepicker.k-focus,
					.k-datepicker.k-hover,
					.k-autocomplete.k-focus,
					.k-dropdownlist.k-hover,
					.k-dropdownlist.k-focus,
					.k-numerictextbox.k-hover,
					.k-numerictextbox.k-focus {
						border-color: #333333;
					}

					.k-numerictextbox .number-question {
						font-size: 14px;
						padding-left: 8px;
					}

					.date-range-question,
					.form-question,
					.form-question-container {
						.k-datepicker {
							.k-input-button {
								height: 29px !important;
								line-height: 29px !important;
								width: 2em !important;
								border-right: 0 !important;
								margin-left: 0 !important;
								left: 0px !important;
								top: 0px !important;
							}
						}
					}
				}
			}
		}
	}
}

.k-editor-toolbar.k-toolbar {
	row-gap: 0;
	column-gap: 7px;
	padding-left: 4px;

	&>* {
		margin-right: 0;
	}

	.k-toolbar-overflow-button.k-button {
		background-color: #fff !important;
		height: 26px;
		border: 1px solid #bfbfbf !important;
		margin-inline-start: unset;
	}

	.k-combobox {
		width: 7.5em;

		.k-input-inner {
			margin-right: 0;
			height: 23px;
		}
	}

	.k-combobox .k-button {
		margin-right: -1px;
	}

	.k-colorpicker.k-toolbar-color-picker {
		background-image: none;
		background-position: 50% 50%;
		background-color: #fff;
		border: 1px solid #bfbfbf;
		height: 26px;
		display: flex;

		span {
			cursor: pointer;
		}

		.k-input-inner {
			position: relative;
			padding: 2px 2px 2px 2px;
			margin: 0px 3px;
			background-repeat: no-repeat;
			vertical-align: middle;
			width: 16px;
			height: 20px;
			background-color: #fff;

			.k-color-preview {

				.k-icon {
					top: 3px;
				}

				.k-color-preview-mask {
					height: 3px;
				}
			}
		}

		.k-input-button {
			opacity: 1;
			display: block;
			border-left: 1px solid #bfbfbf;
			width: 22px;
			height: 24px;
			padding: unset;
			padding-right: 1px;
			background-color: #fff;

			&:hover,
			&:active {
				border: 0;
				border-left: 1px solid #bfbfbf;
				background-color: #fff;

				.k-svg-icon {
					svg {
						fill: #333;
					}
				}
			}
		}
	}

	.k-button-group {
		.k-button {
			&.k-selected {
				color: #fff;
				background-color: #007cc0;
				border-color: #007cc0;

				&:hover {
					color: #fff;
					background-color: #0089d4;
					border-color: #0089d4;
				}
			}
		}
	}
}

.k-editor-toolbar.k-toolbar,
.form-rule-modal {
	.k-dropdownlist {

		.k-input-button,
		.k-input-inner {
			height: 24px;
			line-height: 24px;
		}
	}
}

.k-button-solid-base:active,
.k-button-solid-base.k-active {
	border-color: var(--kendo-color-border-alt, #b6b6b6);
	background-color: var(--kendo-color-base-active, #d6d6d6);
}

.k-multiselect {
	&.k-input {
		height: auto;
	}

	&.k-focus {
		border-color: #bfbfbf;
		box-shadow: none;
	}

	.k-input-inner {
		font-size: 14px;
	}

	.k-button {
		border: 0;
		border-left: 1px #bfbfbf solid;
		background-color: #f7f7f7;
	}

	.k-selection-multiple,
	.k-chip-list {
		.k-chip {
			margin: 1px 0 1px 1px;
			padding: .1em 0.1em .1em .4em;
			line-height: 1.5em;
			min-height: calc(1.7em + 2px);
			float: left;
			position: relative;
			border-radius: 0;
		}

		.k-chip-solid-base {
			border-color: var(--kendo-color-border, #bfbfbf);
			color: var(--kendo-color-on-base, #2e2e2e);
			background-color: var(--kendo-color-base, #f7f7f7);
		}
	}

	.k-input-values {
		.k-selection-multiple.k-chip-list {
			.k-disabled {
				.k-chip-content {
					padding-right: 5px;
				}

				.k-chip-actions {
					display: none;
				}
			}
		}
	}
}

.k-upload {

	.k-dropzone,
	.k-upload-dropzone {
		padding: 0.8em;
		font-size: 14px;

		.k-upload-button-wrap {
			&:focus {
				background-color: #007cc0;
				color: #fff;
			}

			.k-upload-button {
				&:active {
					color: #fff;
					background-color: #007cc0;
					border-color: #005483
				}
			}

			.k-dropzone-hint {
				color: #7d7d7d;
				display: block !important;
				font-style: italic;
			}
		}
	}
}

.kendo-grid .k-grid-header {

	.k-grid-header-wrap,
	.k-grid-header-locked {
		table thead tr td {
			.k-filtercell {
				.k-numerictextbox.k-input.date-number {
					display: inline-flex !important;
					float: right;
					width: calc(100% - 1.4em);
					z-index: 0;
					border-bottom-width: 1px;

					.k-input-spinner.k-spin-button {
						line-height: 21px;
						min-height: 21px;
						box-sizing: border-box;
						width: 22px;
						height: 21px;
						margin-left: -3px;
					}
				}

				.k-datepicker+span+.k-button[title="Clear"] {
					right: 22px;

					&.clear-custom-filter-menu-btn {
						display: inline;
						vertical-align: top;
						pointer-events: auto;
						cursor: pointer;
						opacity: 1;

						>span {
							opacity: 1;
						}
					}
				}

				.k-filtercell-wrapper {
					.k-numerictextbox .k-input-spinner.k-spin-button {
						line-height: 21px;
						min-height: 21px;
						box-sizing: border-box;
						width: 22px;
						height: 21px;
					}

					display: block;

					.k-dropdownlist.k-picker-md:not(.k-dropdown-operator) {
						top: -1px;
					}

					.k-autocomplete {
						display: block;
						text-indent: 0;
					}

					.k-button[title="Clear"] {
						top: 0px;
						right: 0px;
						border-radius: 0;
						position: absolute;
						padding: 4px;
						z-index: 2;
					}

					.k-numerictextbox+span+.k-button[title="Clear"] {
						right: 22px;

						&.clear-custom-filter-menu-btn {
							display: inline;
							vertical-align: top;
							pointer-events: auto;
							cursor: pointer;
							opacity: 1;

							>span {
								opacity: 1;
							}
						}
					}
				}

				.k-input-button {
					background-color: #ececec;
					width: 21px;
				}

				.k-filter,
				.k-svg-i-filter {
					display: block;
					width: 22px;
					height: 22px;

					svg {
						display: none;
					}
				}
			}
		}
	}
}

.k-popup ul.k-list-ul {
	li.filter {
		padding: 0 4px;
		padding-left: 30px;
		background-position: left center;
		border-width: 1px 0;
		border-style: solid;
		line-height: 1.9em;
		min-height: 1.9em;

		.k-list-item-text {
			font-size: 14px;
		}
	}
}


.k-filter-custom-btn {
	.k-icon {
		&.k-svg-i-filter {
			background-position: -32px -80px;

			&::before {
				content: '';
			}
		}
	}
}

.kendo-grid .k-filtercell .k-svg-i-filter:before {
	content: '';
}

.k-popup ul.k-list-ul li.filter,
.kendo-grid .k-filtercell .k-filter,
.kendo-grid .k-filtercell .k-svg-i-filter {
	&.custom {
		background-image: url('../../global/img/Filter/Custom.png');
	}

	&.all {
		background-image: url('../../global/img/Filter/all.svg');
	}

	&.lastxdays {
		background-image: url('../../global/img/Filter/lastxdays.svg');
	}

	&.lastxhours {
		background-image: url('../../global/img/Filter/lastxhours.svg');
	}

	&.lastxmonths {
		background-image: url('../../global/img/Filter/lastxmonths.svg');
	}

	&.lastxweeks {
		background-image: url('../../global/img/Filter/lastxweeks.svg');
	}

	&.lastxyears {
		background-image: url('../../global/img/Filter/lastxyears.svg');
	}

	&.nextxdays {
		background-image: url('../../global/img/Filter/nextxdays.svg');
	}

	&.nextxhours {
		background-image: url('../../global/img/Filter/nextxhours.svg');
	}

	&.nextxmonths {
		background-image: url('../../global/img/Filter/nextxmonths.svg');
	}

	&.nextxweeks {
		background-image: url('../../global/img/Filter/nextxweeks.svg');
	}

	&.nextxyears {
		background-image: url('../../global/img/Filter/nextxyears.svg');
	}

	&.olderthanxdays {
		background-image: url('../../global/img/Filter/lastxyears.svg');
	}

	&.olderthanxmonths {
		background-image: url('../../global/img/Filter/olderthanxmonths.svg');
	}

	&.olderthanxyears {
		background-image: url('../../global/img/Filter/olderthanxyears.svg');
	}

	&.onyearx {
		background-image: url('../../global/img/Filter/onyearx.svg');
	}

	&.lastmonth {
		background-image: url('../../global/img/Filter/lastmonth.svg');
	}

	&.lastweek {
		background-image: url('../../global/img/Filter/lastweek.svg');
	}

	&.lastyear {
		background-image: url('../../global/img/Filter/lastyear.svg');
	}

	&.nextmonth {
		background-image: url('../../global/img/Filter/nextmonth.svg');
	}

	&.nextweek {
		background-image: url('../../global/img/Filter/nextweek.svg');
	}

	&.nextyear {
		background-image: url('../../global/img/Filter/nextyear.svg');
	}

	&.onorafterx {
		background-image: url('../../global/img/Filter/onorafterx.svg');
	}

	&.onorbeforex {
		background-image: url('../../global/img/Filter/onorbeforex.svg');
	}

	&.onx {
		background-image: url('../../global/img/Filter/onx.svg');
	}

	&.thismonth {
		background-image: url('../../global/img/Filter/thismonth.svg');
	}

	&.thisweek {
		background-image: url('../../global/img/Filter/thisweek.svg');
	}

	&.thisyear {
		background-image: url('../../global/img/Filter/thisyear.svg');
	}

	&.today {
		background-image: url('../../global/img/Filter/today.svg');
	}

	&.tomorrow {
		background-image: url('../../global/img/Filter/tomorrow.svg');
	}

	&.yesterday {
		background-image: url('../../global/img/Filter/yesterday.svg');
	}

	&.nextbusinessday {
		background-image: url('../../global/img/Filter/nextbusinessday.svg');
	}

	&.list {
		background-image: url('../../global/img/Filter/List.png');
	}

	&.contains {
		background-image: url('../../global/img/Filter/Contains.png');
	}

	&.isequalto {
		background-image: url('../../global/img/Filter/Equals.png');
	}

	&.isnotequalto {
		background-image: url('../../global/img/Filter/DoesNotEqual.png');
	}

	&.startswith {
		background-image: url('../../global/img/Filter/StartsWith.png');
	}

	&.doesnotcontain {
		background-image: url('../../global/img/Filter/DoesNotContain.png');
	}

	&.endswith {
		background-image: url('../../global/img/Filter/EndsWith.png');
	}

	&.islessthanorequalto {
		background-image: url('../../global/img/Filter/LessThanEqual.png');
	}

	&.isgreaterthanorequalto {
		background-image: url('../../global/img/Filter/GreaterThanEqual.png');
	}

	&.isgreaterthan {
		background-image: url('../../global/img/Filter/GreaterThan.png');
	}

	&.isafter {
		background-image: url('../../global/img/Filter/GreaterThan.png');
	}

	&.islessthan {
		background-image: url('../../global/img/Filter/LessThan.png');
	}

	&.isbefore {
		background-image: url('../../global/img/Filter/LessThan.png');
	}

	&.isbeforeorequalto {
		background-image: url('../../global/img/Filter/LessThanEqual.png');
	}

	&.isafterorequalto {
		background-image: url('../../global/img/Filter/GreaterThanEqual.png');
	}

	&.isnull {
		background-image: none;
	}

	&.isnotnull {
		background-image: none;
	}

	&.isempty {
		background-image: url('../../global/img/Filter/Empty.png');
	}

	&.isnotempty {
		background-image: url('../../global/img/Filter/NotEmpty.png');
	}

	&.k-hover,
	&.k-focus {
		border-width: 1px 0;
	}

	&.k-selected {
		background-repeat: no-repeat;
		background-size: 16px;
		background-position: 2px;
		cursor: pointer;
	}
}

.k-popup ul.k-list-ul li.filter,
.kendo-grid .k-filtercell .k-filter,
.kendo-grid .k-filtercell .k-svg-i-filter {
	background-repeat: no-repeat;
	background-size: 16px;
	cursor: pointer;
	background-position: 2px;
}

.k-popup ul.k-list-ul li.filter {
	@item-horizon: 1px #eee solid;

	&.custom,
	&.list {
		border-top: @item-horizon;
	}
}

.k-animation-container {
	.k-list-container .k-list-ul>li {
		text-overflow: ellipsis;
		white-space: normal;
		font-size: 14px;
	}

	.k-list-container:not(.filter-updated) .k-list-ul>li {
		overflow: hidden;
	}
}

.kendo-grid {

	.k-grid-header-wrap,
	.k-grid-header-locked {
		.k-input .k-input-inner[type=text] {
			height: 20px;
			padding: 0 0 0 4px;
			color: #333;
			font-size: 14px;
		}
	}
}

.k-grid-header th.k-header:first-child {
	border-left-width: 1px;
	border-inline-start-width: 1px !important
}

.k-list-container.k-popup.k-autocomplete-popup.filter-updated[aria-hidden="true"] {
	position: absolute;
}

.k-animation-container {
	.k-list-container {
		font-size: 14px;
		width: 152px;
	}

	.k-filter-menu-container {
		.input-group.tf-filter {
			background-color: #fff;
		}
	}
}

.k-tabstrip {

	&.k-header,
	.k-tabstrip-items .k-item {
		background-image: none, linear-gradient(to bottom, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0) 100%);
		background-position: 50% 50%;
		background-color: #eae8e8;
		border-color: #c9c9c9;
	}

	.k-tabstrip-items {
		background-color: inherit;
		padding-left: 0px;
	}

	span.k-link.add-item.scroll-add-icon {
		display: none;
	}
}

.k-tabstrip-items-wrapper .k-item:focus,
.k-tabstrip-items-wrapper .k-item.k-focus {
	box-shadow: none;
}

.k-tabstrip-top>.k-tabstrip-items-wrapper .k-item:active,
.k-tabstrip-top>.k-tabstrip-items-wrapper .k-item.k-active {
	background-color: #fff;
	border-bottom-color: #fff;
}

.k-header {

	& .k-filter-list-btn,
	& .k-grid-filter-menu.k-filter-custom-btn {
		height: 21px;
		z-index: 1;
		background-color: #eee;
		border-width: 0 0 1px 1px;
		border-style: solid;
		border-color: #c5c5c5;
		position: absolute;
		padding: 0;
		margin: 0;
		top: 42px;
		width: 22px;
		box-sizing: border-box;
		display: flex;
		justify-content: center;
		align-items: center;
		right: 8px;
		@parent-control-right-padding: .6em;

		&.z-left-btn {
			right: @parent-control-right-padding;
		}

		&.o-left-btn {
			right: calc(~"22px + "@parent-control-right-padding);
		}

		&.t-left-btn {
			right: calc(~"44px + "@parent-control-right-padding);
		}

		&:hover {
			cursor: default;
		}
	}

	& .k-grid-filter-menu.k-filter-custom-btn,
	& .k-filter-list-btn {

		.k-icon.k-filter,
		.k-icon.k-svg-icon.k-svg-i-filter {
			background-image: url('../../global/thirdparty/kendo/styles/default/sprite.png');
		}

		border-width: 0.01em;
	}
}

.k-header {
	& .k-grid-filter-menu.k-filter-custom-btn {
		top: 42px;
		right: 8px;
		border-left: 1px #B6B6B6 solid;
		height: 22px;
		color: var(--kendo-color-on-app-surface, #272727);
	}

	& .k-filter-list-btn {
		height: 22px;
		top: 42px;
		right: 9px;
		width: 22px;
	}

	&:not(.k-datepicker) {
		.clearButtonPosition {
			top: -1px;
			right: 1.7em;
		}
	}
}

.k-grid-header th.k-header {
	overflow: visible;
}

.kendo-grid .k-grid-header {

	.k-grid-header-wrap,
	.k-grid-header-locked {
		table tr td .hide-cross-button .k-button.clear-custom-filter-menu-btn {
			.k-icon {
				height: 16px;
			}

			position: absolute;
			right: 20px;
			margin-bottom: 0;
			z-index: 100;
			display: inline;
			vertical-align: top;
			pointer-events: auto;
			cursor: pointer;
			opacity: 1;
			width: 22px;
		}
	}
}

.kendo-grid.kendo-grid-container .tf-filter {
	.form-control.datepickerinput[disabled] {
		color: #858585;
		background-color: white;
		opacity: .6;
	}

	.form-control.datepickerinput {
		background-color: white;
	}

	.k-disabled.datepickerbutton {
		background-color: white;
		color: #7d7d7d;
		border-color: #ccc;
	}

	.k-disabled>.k-input[data-kendo-role="datepicker"] {
		background-color: #EFEFEF;
	}
}

.kendo-grid.selectedcolumngrid-container {
	.tf-filter {
		.form-control.datepickerinput {
			background-color: white;
		}
	}
}

.kendo-grid-container .k-filtercell .k-button.k-button-icon .k-icon.k-i-close {
	left: -2px;
	top: -4px;
}

.k-progressbar-horizontal>.k-selected,
.k-rtl .k-progressbar-horizontal.k-progressbar-reverse>.k-selected {
	left: -1px;
	right: auto;
	top: -1px;
	height: 100%;
	border-radius: 4px 0 0 4px;
}

.k-progressbar-horizontal {
	font-size: 12px;
	border-radius: 0;
	box-sizing: content-box;
}

.k-combobox {
	border-radius: 0;
	background-color: #fff;
	margin: 0;
	padding: 0;
	height: 100%;

	&.k-picker-solid {

		&:hover,
		&.k-hover {
			background-color: #fff;
		}
	}

	&.k-focus {
		box-shadow: none;
		border-color: #bfbfbf;
	}

	&.k-hover {
		border-color: #bfbfbf;

		.k-input-inner,
		.k-input-button {
			background-color: #fff;
		}
	}

	.k-input-inner {
		margin-right: 20px;
		height: 20px;
		line-height: 20px;
		font-family: "SourceSansPro-Regular", Arial;
		font-size: 14px;
		background-color: #fff;

		&[type=text] {
			height: 20px;
		}
	}

	.k-clear-value {
		margin-right: 20px;
	}

	.k-input-button {
		z-index: 1;
		border-width: 0;
		background-color: #fff;

		&:hover,
		&:active {
			color: #333;
			background-color: #fff;
			border-width: 0;
		}

		height: 22px;
		line-height: 22px;
		min-height: 22px;
		box-sizing: border-box;
		width: 22px;
		position: absolute;
		top: 0;
		right: 0;

		.k-svg-icon {
			height: 16px;
			width: 16px;
			cursor: pointer;
			display: table;
		}
	}
}

.kendo-treelist {
	.k-table-md {
		font-size: 14px;
	}
}

.k-numerictextbox.k-input.merge-template-filed-changed {
	animation: 3s highlightField;
}

.kendo-grid {
	.k-grid-edit-command {
		background: url('../../global/img/grid/editor_pencil.png') no-repeat center center;
		opacity: .6;

		.k-svg-icon {
			width: auto;
		}
	}

	a {
		color: #337ab7;

		&:hover {
			text-decoration: underline;
		}
	}
}

.property-grid .property-grid-content .k-editor span.k-picker.k-dropdownlist.k-picker-solid.k-picker-md.k-rounded-md {
	width: 130px !important;
}

.tabstrip-requiredfields {
	.k-grid {
		display: block;
	}
}

.kendo-grid {

	.k-grid-header-wrap,
	.k-grid-header-locked {
		.k-datepicker.k-input.k-input-solid {
			width: calc(100% - 16px);
			float: right;

			.k-input-inner[type=text] {
				width: calc(100% - 22px) !important;
			}

			&.k-focus {
				border-color: #d5d5d5;
			}

			.k-filter-custom-input.text-ellipsis:not(.datepickerinput) {
				width: calc(100% - 42px) !important;
			}
		}
	}
}

.kendo-grid .k-table-tfoot {
	background-color: transparent;
}

.k-color-preview-mask::before {
	background: none;
}

.k-button-md.k-icon-button .k-button-icon {
	min-height: 17px;
	min-width: 17px;
}

.k-colorpicker-popup,
.k-flatcolorpicker {

	.k-hsv-gradient {
		margin-bottom: 0;
	}

	.k-hue-slider,
	.k-transparency-slider {
		display: flex;
	}

	.k-hue-slider .k-draghandle,
	.k-hsv-rectangle .k-draghandle {
		width: 14px;
		height: 14px;
	}

	.recent-colors {
		display: flex;

		.recentPalette {
			margin-top: 5px;
			max-width: 220px;
			overflow: hidden;
			float: left;

			.k-colorpalette-tile {
				border: solid 1px #999999;
				box-sizing: border-box;

				&.k-selected {
					box-shadow: 3px 3px 4px 0px #999999;
				}
			}

			table {
				border-spacing: 5px;
				border-collapse: separate;
			}
		}

		.image-button {
			height: 34px;
			width: 16px;
			background-size: contain;
			background-repeat: no-repeat;
			background-position: right center;
			margin-top: 5px;
			cursor: pointer;
			z-index: 102;

			&.image-previous {
				background-image: url('../../Global/Img/ColorPicker/Previous-Image.png');
				left: 0px;
				opacity: 0.3;
			}

			&.image-next {
				background-image: url('../../Global/Img/ColorPicker/Next-Image.png');
				right: 0;
				opacity: 0.7;
			}
		}
	}

	&.only-palette {
		padding: 0;
		min-width: unset;

		.k-coloreditor-header {
			padding: 0;
		}

		.k-coloreditor-views {
			padding: 0;
			min-width: 252px;

			.k-colorpalette-table {
				.k-colorpalette-tile {
					width: 14px !important;
					height: 14px !important;
				}
			}
		}
	}
}

.student-requirement-panel,
.student-exception-modal,
.calendar-events,
.parameter-item-editor,
.list-mover-iconrow-wrapper,
.kendo-grid {
	.k-datepicker button.k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base {
		height: 21px;
		width: 21px;
		top: 0;
		border-right: unset !important;
	}
}

.k-datepicker button.k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base {
	height: 22px;
	width: 22px;
	border-right: 1px #bfbfbf solid !important;
	margin-left: -3px;
	left: 0px;
	top: -1px;
}

.k-popup .k-datepicker button.k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base {
	top: 0px;
	height: 21px;
	left: 1px;
}

.k-grid .k-hierarchy-cell {
	cursor: pointer;
}

.k-grid tbody .k-detail-row {
	.k-cell-inner .k-grid-filter-menu.k-grid-header-menu.k-filter-custom-btn {
		width: 24px;
		height: 22px;
	}

	.k-svg-i-filter svg {
		display: none;
	}

	.k-button {
		min-width: auto;
	}
}

.k-tabstrip {
	font-size: 14px;
}

span.k-chip-label {
	font-size: 14px;
}

.k-grid .k-detail-cell {
	overflow: visible !important;
}

.modal-body .adjust-display {

	#border-thumbnail,
	#symbol-color {
		div.disabled {
			border: none;
		}

		#border-color-selector,
		#symbol-color-selector {
			border: 1px solid #000;
			padding: 0;
			width: 32px;
			height: 22px;

			span {
				border: 0;
			}

			.k-colorpicker {
				.k-input-inner {
					width: 100%;
					height: 20px;
					padding: 0;
				}
			}
		}
	}
}

.k-loading-mask>.k-loading-image {

	&::after,
	&::before {
		content: none;
	}
}

.k-tooltip.k-slider-tooltip {
	display: none !important;
}

.k-window {
	border: 0;
	font-size: 14px;

	&.k-editor-window {
		width: auto !important;

		.k-input.k-textbox {
			border: 0;
		}
	}

	.k-window-titlebar {
		background-color: #000000;
		color: #fff;
		background-image: none;

		.k-window-title {
			padding: 10px;
			font-size: 15px;
		}

		.k-window-titlebar-actions {
			svg {
				fill: #fff;
			}
		}
	}

	.k-window-buttons {
		.k-button-solid-primary {
			color: #fff;
			background-color: #000000;
			background-image: none;

			svg {
				fill: #fff;
			}
		}
	}

	.k-checkbox:focus,
	.k-checkbox:checked,
	.k-checkbox.k-checked {
		outline: none;
	}

	.k-input.k-textbox input {
		padding: 2.5px 0;
	}

	.k-editor-dialog {
		width: 100%;

		.k-root-tabs {
			margin: 0;
		}

		.k-form {
			.k-form-fieldset {
				margin: 15px 0;
			}
		}

		.k-form-field {
			margin-top: 0;
			margin-bottom: 15px;

			.k-numerictextbox {
				width: 100%;
				height: 26px;

				.k-button {
					display: inline-flex;
					min-height: 12px;
				}
			}

			.k-dropdownlist {
				height: 24px;
			}

			span.k-input-inner,
			input.k-input-inner {
				height: 22px;
				line-height: 22px;
			}

			.k-input.k-textbox {
				height: 26px;
				border: 0;

				.k-input-inner {
					height: 100%;
				}
			}

			.k-input.k-textarea {
				border-width: 0;
				height: 100%;
				width: 100%;

				textarea {
					width: calc(100% - 20px);
				}
			}

			.k-colorpicker {
				width: 22px;
				border: solid 1px #555;
			}
		}

		.k-window-buttons {
			padding-left: 15px;
		}
	}
}

.k-scheduler {
	.k-scheduler-toolbar {
		.k-button-group {
			.k-button {
				.k-icon {
					width: 19px;
					height: 19px;
				}

				&.k-state-hover,
				&:hover,
				&:focus {
					box-shadow: none;
					border-color: #bfbfbf;
				}
			}

			.k-view-month,
			.k-view-list {

				&.k-focus.k-selected,
				&.k-selected,
				&.k-selected:hover,
				&.k-active,
				&:active {
					background-color: #D0503C;
					border-color: #D0503C;
					color: #fff;
				}
			}
		}
	}

	.k-scheduler-header {
		.k-heading-cell {
			text-align: left;
			vertical-align: middle;
		}
	}

	.k-scheduler-content {
		.k-event {
			.calendar-event {
				margin-right: -6px;
			}
		}
	}

	.k-scheduler-agendaview {
		.k-scheduler-content {
			tr.k-hover {
				background-color: #fff;

				.k-scheduler-datecolumn {
					border-color: #fff;
					background-color: #e5e5e5;

				}
			}

			.k-task {
				border: 1px #ACACAC solid !important;
			}
		}
	}
}

.k-list-item-text::before,
.k-list-header-text::before,
.k-list-optionlabel::before {
	content: '';
}

.kendo-summarygrid-container .k-grid-content.k-auto-scrollable {
	margin-right: 17px;
}

.k-input-md,
.k-picker-md {
	font-size: 14px;
}

.rollover-school.k-grid tbody .k-button {
	min-height: 22px;
	min-width: 0;
	height: 22px;
	line-height: 22px;
	margin: 0;
}

.select-datasource .k-datepicker {
	.k-input-inner {
		background-color: rgba(0, 0, 0, 0);
	}

	button.k-input-button.k-button.k-icon-button.k-button-md.k-button-solid.k-button-solid-base {
		height: 21px;
		left: 1px;
		top: 0px;
	}
}

.grid-stack .calendar-item .k-calendar {

	.k-header {
		display: block;

		.k-button {
			&.k-calendar-nav-fast {
				position: absolute;
				pointer-events: none;
				float: left;
				text-align: left;
				padding-left: 0;
				height: auto;
				line-height: normal;
				margin: 0;
			}

			&.k-calendar-nav-prev,
			&.k-calendar-nav-next {
				position: absolute;
				height: 18px;
				width: 14.3%;
				top: 5px;
				padding-bottom: 3px;
				box-sizing: border-box;

				.k-icon {
					background: none;
					height: 0;
					width: 0;
				}
			}

			&.k-calendar-nav-prev {
				left: auto;
				right: 28.6%;
				margin-right: 0;

				&:before {
					content: '';
					position: absolute;
					top: 1px;
					left: calc(~"50% - 5px");
				}
			}

			&.k-calendar-nav-next {
				right: 0;

				&:before {
					content: '';
					position: absolute;
					top: 1px;
					left: calc(~"50% - 2.5px");
				}
			}
		}
	}

	.k-calendar-footer {
		top: 18px;
		right: 21%;
	}
}

.k-grid-content.k-auto-scrollable[data-kendo-role=virtualscrollable] {
	min-height: 2px !important;
}

.k-dropdownlist-popup .no-transportation-dropdown .k-list-content li .k-list-item-text
{
	text-overflow: initial !important;
	text-wrap: initial !important;
}

[data-block-field-name="AuditLogGrid"] td[data-kendo-field="OldValue"],
[data-block-field-name="AuditLogGrid"] td[data-kendo-field="NewValue"] {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: unset !important;
  word-break: break-word !important;
  height: auto !important;
  max-width: 100% !important;
}